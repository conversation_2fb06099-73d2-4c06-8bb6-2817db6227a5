Code Style and Structure
- Follow existing project package structure: business, configs, dto, enums, etc.
- Use Lombok annotations to simplify code (@Data, @Builder, etc.)
- Use hutool utility library uniformly
- Keep consistency with westcatr-boot parent project
- Naming conventions
  - Package names are all lowercase: com.westcatr.rd.testbusiness
  - Class names use PascalCase: Xxx<PERSON><PERSON>roller, XxxServiceImpl
  - Method names use camelCase: getXxxById, updateXxx
  - Constants use uppercase with underscores: MAX_SIZE

Technical specifications
  - Spring Boot related
    - Continue to use westcatr-boot (2.1.7.1) as the parent project
    - Use Spring annotations reasonably (@Service, @Autowired, etc.)
    - Use Mybatis-Plus for data access uniformly
    - Enable async processing with @EnableAsync
    - Enable scheduling with @EnableScheduling
    - Enable caching with @EnableCaching

  - Database operations
    - Use Mybatis-Plus's BaseMapper interface
    - Use batch processing for large data operations
    - Use database transactions reasonably
    - Define proper BaseResultMap and Base_Column_List

  - Asynchronous processing
    - Use @Async annotation for asynchronous tasks
    - Put timed tasks in the job package uniformly
    - Use Quartz for complex timed task scheduling
    - Implement proper exception handling for async tasks

  - Document processing
    - Use appropriate document processing utilities (BishengOffice, POI)
    - Implement template-based document generation
    - Support multiple document formats (docx, xlsx, pdf)
    - Handle document conversion and merging properly

  - Workflow processing
    - Use Flowable for workflow management
    - Define workflow processes in .bpmn20.xml format
    - Implement proper workflow listeners and handlers
    - Maintain clear workflow documentation

  - Utility library usage
    - Use hutool utility library first
    - Use commons-lang3 secondarily
    - Use the project's own utility classes for file operations
    - Utilize project-specific utility classes for specialized operations

  - Logging specifications
    - Use Slf4j+Logback uniformly
    - Log INFO level for key business
    - Log ERROR level for exceptions
    - Include necessary context in log messages

  - Data synchronization
    - Put synchronization-related code in the syncdata package uniformly
    - Implement idempotence processing
    - Add necessary log records
    - Handle synchronization failures gracefully

  - Test specifications
    - Write unit tests to cover core business logic
    - Use Junit5 for testing
    - Use a fixed seed for mock test data
    - Test workflow processes thoroughly

  - Security specifications
    - Follow the project's existing permission system
    - Use SM2 and RSA encryption appropriately
    - Encrypt sensitive data storage
    - Prevent SQL injection and XSS attacks
    - Implement proper authentication and authorization
    - Secure file upload/download operations

  - Documentation specifications
    - Add comments to core business code
    - Use Swagger annotations to generate API documentation
    - Update README documentation in a timely manner
    - Document workflow processes and business rules
    - Maintain template documentation