/*!
 * Tiny PowerPaste plugin
 *
 * Copyright 2010-2021 Tiny Technologies, Inc. All rights reserved.
 *
 * Version: 5.6.2-4
 */
!function(){"use strict";function e(r){return function(e){return n=typeof(t=e),(null===t?"null":"object"==n&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":n)===r;var t,n}}function t(t){return function(e){return typeof e===t}}function c(e){return null==e}function v(e){return!c(e)}function S(){}function s(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}}function b(e){return function(){return e}}function o(e){return e}function r(e,t){return e===t}var n,O=e("string"),d=e("object"),l=e("array"),i=t("boolean"),f=function(e){return n===e},g=t("function"),a=t("number");function I(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}function A(e){return function(){throw new Error(e)}}function C(e){return e()}function u(e){return parseInt(e,10)}function m(e,t){var n=e-t;return 0==n?0:0<n?1:-1}function p(e,t,n){return{major:e,minor:t,patch:n}}function h(e){var t=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(e);return t?p(u(t[1]),u(t[2]),u(t[3])):p(0,0,0)}function y(e){return h([e.majorVersion,e.minorVersion].join(".").split(".").slice(0,3).join("."))}function D(e,t){return e&&-1===function(e,t){var n=m(e.major,t.major);if(0!==n)return n;var r=m(e.minor,t.minor);if(0!==r)return r;var o=m(e.patch,t.patch);return 0!==o?o:0}(y(e),h(t))}function x(){return k}var w=b(!1),T=b(!(n=void 0)),k={fold:function(e,t){return e()},isSome:w,isNone:T,getOr:o,getOrThunk:L,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(void 0),or:o,orThunk:L,map:x,each:S,bind:x,exists:w,forall:T,filter:function(){return k},toArray:function(){return[]},toString:b("none()")};function L(e){return e()}function E(e,t){return-1<ie.call(e,t)}function N(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n))return!0;return!1}function _(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r}function P(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n)}function R(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o)?n:r).push(a)}return{pass:n,fail:r}}function M(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r)&&n.push(i)}return n}function F(e,n,r){return P(e,function(e,t){r=n(r,e,t)}),r}function j(e,t){return function(e,t,n){for(var r=0,o=e.length;r<o;r++){var i=e[r];if(t(i,r))return re.some(i);if(n(i,r))break}return re.none()}(e,t,w)}function U(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n))return re.some(n);return re.none()}function H(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!l(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);ae.apply(t,e[n])}return t}function W(e,t){return H(_(e,t))}function B(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n))return!1;return!0}function z(e,t){for(var n={},r=0,o=e.length;r<o;r++){var i=e[r];n[String(i)]=t(i,r)}return n}function $(e){return 0<e.length?re.some(e[0]):re.none()}function q(e,t){for(var n=0;n<e.length;n++){var r=t(e[n],n);if(r.isSome())return r}return re.none()}function V(e){return O(e)&&E(["clean","merge","prompt"],e)}function G(e,t,n){var r=e.getParam(t,n);return g(r)||V(r)?r:n}function K(e){return e.getParam("images_upload_url")}function X(e){return e.getParam("automatic_uploads",!0,"boolean")}function J(e){return e.getParam("paste_preprocess")}function Y(e){return G(e,"powerpaste_word_import","prompt")}function Z(e){return G(e,"powerpaste_html_import","clean")}function Q(e){return!1!==e.getParam("paste_merge_formats")}function ee(e){return tinymce.explode(e.getParam("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string"))}function te(e,t){e.dom.bind(t,"drop dragstart dragend dragover dragenter dragleave dragdrop draggesture",function(e){e.preventDefault(),e.stopImmediatePropagation()})}var ne=function(n){function e(){return o}function t(e){return e(n)}var r=b(n),o={fold:function(e,t){return t(n)},isSome:T,isNone:w,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return ne(e(n))},each:function(e){e(n)},bind:t,exists:t,forall:t,filter:function(e){return e(n)?o:k},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},re={some:ne,none:x,from:function(e){return null==e?k:ne(e)}},oe=Array.prototype.slice,ie=Array.prototype.indexOf,ae=Array.prototype.push,ue=function(){return(ue=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function ce(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||((r=r||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function se(e,t){for(var n=be(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i)}}function le(e,n){return we(e,function(e,t){return{k:t,v:n(e,t)}})}function fe(e,t){var n={},r=t,o=n,i=function(e,t){o[t]=e},a=S;return se(e,function(e,t){(r(e,t)?i:a)(e,t)}),n}function de(e,n){var r=[];return se(e,function(e,t){r.push(n(e,t))}),r}function me(e){return be(e).length}function pe(e,t){return Te(e,t)?re.from(e[t]):re.none()}function ge(a){if(!l(a))throw new Error("cases must be an array");if(0===a.length)throw new Error("there must be at least one case");var u=[],n={};return P(a,function(e,r){var t=be(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(void 0!==n[o])throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");u.push(o),n[o]=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=n.length;if(t!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+t);return{fold:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(e.length!==a.length)throw new Error("Wrong number of arguments to fold. Expected "+a.length+", got "+e.length);return e[r].apply(null,n)},match:function(e){var t=be(e);if(u.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+u.join(",")+"\nActual: "+t.join(","));if(!B(u,function(e){return E(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+u.join(", "));return e[o].apply(null,n)},log:function(e){console.log(e,{constructors:u,constructor:o,params:n})}}}}),n}var ve,he,ye,be=Object.keys,xe=Object.hasOwnProperty,we=function(e,r){var o={};return se(e,function(e,t){var n=r(e,t);o[n.k]=n.v}),o},Te=function(e,t){return xe.call(e,t)},Ie=ge([{blob:["id","imageresult","objurl"]},{url:["id","url","raw"]}]),Se=ue({cata:function(e,t,n){return e.fold(t,n)}},Ie),Oe={},Ae={exports:Oe};function Ce(e){setTimeout(function(){throw e},0)}function De(e){return a=e,yt(function(r){var o=[],i=0;0===a.length?r([]):P(a,function(e,t){var n;e.get((n=t,function(e){o[n]=e,++i>=a.length&&r(o)}))})});var a}function ke(e){var t=(new Date).getTime();return e+"_"+Math.floor(1e9*Math.random())+ ++xt+String(t)}function Le(e,t){return Tt(document.createElement("canvas"),e,t)}function Ee(e){var t=Le(e.width,e.height);return wt(t).drawImage(e,0,0),t}function Ne(e){return e.naturalWidth||e.width}function _e(e){return e.naturalHeight||e.height}function Pe(e){var t=e.split(","),n=/data:([^;]+)/.exec(t[0]);if(!n)return re.none();for(var r=n[1],o=t[1],i=atob(o),a=i.length,u=Math.ceil(a/1024),c=new Array(u),s=0;s<u;++s){for(var l=1024*s,f=Math.min(1024+l,a),d=new Array(f-l),m=l,p=0;m<f;++p,++m)d[p]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return re.some(new Blob(c,{type:r}))}function Re(e,r,o){return r=r||"image/png",g(HTMLCanvasElement.prototype.toBlob)?new pt(function(t,n){e.toBlob(function(e){e?t(e):n()},r,o)}):St(e.toDataURL(r,o))}function Me(e){return u=e,new pt(function(e,t){function n(){o.removeEventListener("load",i),o.removeEventListener("error",a)}var r=URL.createObjectURL(u),o=new Image,i=function(){n(),e(o)},a=function(){n(),t("Unable to load data of type "+u.type+": "+r)};o.addEventListener("load",i),o.addEventListener("error",a),o.src=r,o.complete&&setTimeout(i,0)}).then(function(e){Ot(e);var t=Le(Ne(e),_e(e));return wt(t).drawImage(e,0,0),t});var u}function Fe(n){return new pt(function(e){var t=new FileReader;t.onloadend=function(){e(t.result)},t.readAsDataURL(n)})}function je(e,t,n){function r(t,n){return e.then(function(e){return e.toDataURL(t||"image/png",n)})}return{getType:b(t.type),toBlob:function(){return pt.resolve(t)},toDataURL:b(n),toBase64:function(){return n.split(",")[1]},toAdjustedBlob:function(t,n){return e.then(function(e){return Re(e,t,n)})},toAdjustedDataURL:r,toAdjustedBase64:function(e,t){return r(e,t).then(function(e){return e.split(",")[1]})},toCanvas:function(){return e.then(Ee)}}}function Ue(t,e){return Re(t,e).then(function(e){return je(pt.resolve(t),e,t.toDataURL())})}function He(e,t){return r=t,je(Me(n=e),n,r);var n,r}function We(e){return(0===(t=e.src).indexOf("data:")?St:It)(t).then(function(e){return Fe(t=e).then(function(e){return je(Me(t),t,e)});var t});var t}function Be(e,t,n){return void 0===t&&void 0===n?Dt(e):e.toAdjustedBlob(t,n)}function ze(e){return e.toDataURL()}function $e(e){var t=URL.createObjectURL(e);return kt(e,t)}function qe(e){return De(_(e,$e))}function Ve(e,t,n){return""===t||e.length>=t.length&&e.substr(n,n+t.length)===t}function Ge(e,t){return Lt(e,t)?(n=t.length,e.substring(n)):e;var n}function Ke(e,t){return Et(e,t)?(n=t.length,e.substring(0,e.length-n)):e;var n}function Xe(e,t){return-1!==e.indexOf(t)}function Je(e,i){return function(e){for(var t,n,r=void 0!==i?i:_t,o=0;o<e.length;++o)void 0!==(t=r)[n=e[o]]&&null!==t[n]||(t[n]={}),r=t[n];return r}(e.split("."))}function Ye(e){return e.dom.nodeName.toLowerCase()}function Ze(e){return e.dom.nodeType}function Qe(t){return function(e){return Ze(e)===t}}function et(e){return 8===Ze(e)||"#comment"===Ye(e)}function tt(t){return function(e){return Pt(e)&&Ye(e)===t}}function nt(e,t,n){if(!(O(n)||i(n)||a(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")}function rt(e,t,n){nt(e.dom,t,n)}function ot(e,t){var n=e.dom;se(t,function(e,t){nt(n,t,e)})}function it(e,t){var n=e.dom.getAttribute(t);return null===n?void 0:n}function at(e,t){return re.from(it(e,t))}function ut(e,t){var n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)}function ct(e,t){e.dom.removeAttribute(t)}function st(){return'Safari does not support direct paste of images. <a href="https://support.ephox.com/entries/88543243-Safari-Direct-paste-of-images-does-not-work" style="text-decoration: underline">More information on image pasting for Safari</a>'}function lt(e){return tinymce.translate(Ut[e])}ve=Oe,he=Ae,ye=void 0,function(e){"object"==typeof ve&&void 0!==he?he.exports=e():("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=e()}(function(){return function i(a,u,c){function s(t,e){if(!u[t]){if(!a[t]){var n="function"==typeof ye;if(!e&&n)return n(t,!0);if(l)return l(t,!0);var r=new Error("Cannot find module '"+t+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[t]={exports:{}};a[t][0].call(o.exports,function(e){return s(a[t][1][e]||e)},o,o.exports,i,a,u,c)}return u[t].exports}for(var l="function"==typeof ye,e=0;e<c.length;e++)s(c[e]);return s}({1:[function(e,t,n){var r,o,i=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function c(t){if(r===setTimeout)return setTimeout(t,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:a}catch(e){r=a}try{o="function"==typeof clearTimeout?clearTimeout:u}catch(e){o=u}}();var s,l=[],f=!1,d=-1;function m(){f&&s&&(f=!1,s.length?l=s.concat(l):d=-1,l.length&&p())}function p(){if(!f){var e=c(m);f=!0;for(var t=l.length;t;){for(s=l,l=[];++d<t;)s&&s[d].run();d=-1,t=l.length}s=null,f=!1,function(t){if(o===clearTimeout)return clearTimeout(t);if((o===u||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(t);try{o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(e)}}function g(e,t){this.fun=e,this.array=t}function v(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new g(e,t)),1!==l.length||f||c(p)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(e,f,t){!function(t){function r(){}function i(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],l(e,this)}function o(n,r){for(;3===n._state;)n=n._value;0!==n._state?(n._handled=!0,i._immediateFn(function(){var e,t=1===n._state?r.onFulfilled:r.onRejected;if(null!==t){try{e=t(n._value)}catch(e){return void u(r.promise,e)}a(r.promise,e)}else(1===n._state?a:u)(r.promise,n._value)})):n._deferreds.push(r)}function a(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof i)return t._state=3,t._value=e,void c(t);if("function"==typeof n)return void l((r=n,o=e,function(){r.apply(o,arguments)}),t)}t._state=1,t._value=e,c(t)}catch(e){u(t,e)}var r,o}function u(e,t){e._state=2,e._value=t,c(e)}function c(e){2===e._state&&0===e._deferreds.length&&i._immediateFn(function(){e._handled||i._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)o(e,e._deferreds[t]);e._deferreds=null}function s(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function l(e,t){var n=!1;try{e(function(e){n||(n=!0,a(t,e))},function(e){n||(n=!0,u(t,e))})}catch(e){if(n)return;n=!0,u(t,e)}}var n=setTimeout;i.prototype.catch=function(e){return this.then(null,e)},i.prototype.then=function(e,t){var n=new this.constructor(r);return o(this,new s(e,t,n)),n},i.all=function(e){var u=Array.prototype.slice.call(e);return new i(function(o,i){if(0===u.length)return o([]);for(var a=u.length,e=0;e<u.length;e++)!function t(n,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var r=e.then;if("function"==typeof r)return r.call(e,function(e){t(n,e)},i),0}u[n]=e,0==--a&&o(u)}catch(e){i(e)}}(e,u[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(o){return new i(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},i._immediateFn="function"==typeof t?function(e){t(e)}:function(e){n(e,0)},i._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},i._setImmediateFn=function(e){i._immediateFn=e},i._setUnhandledRejectionFn=function(e){i._unhandledRejectionFn=e},void 0!==f&&f.exports?f.exports=i:this.Promise||(this.Promise=i)}.call(this,e("timers").setImmediate)},{timers:3}],3:[function(c,e,s){!function(e,t){var r=c("process/browser.js").nextTick,n=Function.prototype.apply,o=Array.prototype.slice,i={},a=0;function u(e,t){this._id=e,this._clearFn=t}s.setTimeout=function(){return new u(n.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new u(n.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(e){e.close()},u.prototype.unref=u.prototype.ref=function(){},u.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},s.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},s._unrefActive=s.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;0<=t&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},s.setImmediate="function"==typeof e?e:function(e){var t=a++,n=!(arguments.length<2)&&o.call(arguments,1);return i[t]=!0,r(function(){i[t]&&(n?e.apply(null,n):e.call(null),s.clearImmediate(t))}),t},s.clearImmediate="function"==typeof t?t:function(e){delete i[e]}}.call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(e,t,n){var r=e("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function ft(e){if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}}var dt,mt,pt=Ae.exports.boltExport,gt=function(e){function r(e){o()?i(e):t.push(e)}var n=re.none(),t=[],o=function(){return n.isSome()},i=function(t){n.each(function(e){setTimeout(function(){t(e)},0)})};return e(function(e){o()||(n=re.some(e),P(t,i),t=[])}),{get:r,map:function(n){return gt(function(t){r(function(e){t(n(e))})})},isReady:o}},vt={nu:gt,pure:function(t){return gt(function(e){e(t)})}},ht=function(n){function e(e){n().then(e,Ce)}return{map:function(e){return ht(function(){return n().then(e)})},bind:function(t){return ht(function(){return n().then(function(e){return t(e).toPromise()})})},anonBind:function(e){return ht(function(){return n().then(function(){return e.toPromise()})})},toLazy:function(){return vt.nu(e)},toCached:function(){var e=null;return ht(function(){return e=null===e?n():e})},toPromise:n,get:e}},yt=function(e){return ht(function(){return new pt(e)})},bt=function(e){return ht(function(){return pt.resolve(e)})},xt=0,wt=function(e){return e.getContext("2d")},Tt=function(e,t,n){return e.width=t,e.height=n,e},It=function(r){return new pt(function(e,t){var n=new XMLHttpRequest;n.open("GET",r,!0),n.responseType="blob",n.onload=function(){200===this.status&&e(this.response)},n.onerror=function(){var e;t(0===this.status?((e=new Error("No access to download image")).code=18,e.name="SecurityError",e):new Error("Error "+this.status+" downloading image"))},n.send()})},St=function(n){return new pt(function(e,t){Pe(n).fold(function(){t("uri is not base64: "+n)},e)})},Ot=function(e){URL.revokeObjectURL(e.src)},At=Fe,Ct=Pe,Dt=function(e){return e.toBlob()},kt=function(i,a){return yt(function(o){At(i).then(function(e){var t=He(i,e),n=ke("image"),r=Se.blob(n,t,a);o(r)})})},Lt=function(e,t){return Ve(e,t,0)},Et=function(e,t){return Ve(e,t,e.length-t.length)},Nt=(dt=/^\s+|\s+$/g,function(e){return e.replace(dt,"")}),_t="undefined"!=typeof window?window:Function("return this;")(),Pt=Qe(1),Rt=Qe(3),Mt=Qe(9),Ft=Qe(11),jt={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return ft(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return ft(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return ft(n)},fromDom:ft,fromPoint:function(e,t,n){return re.from(e.dom.elementFromPoint(t,n)).map(ft)}},Ut={"cement.dialog.paste.title":"Paste Formatting Options","cement.dialog.paste.instructions":"Choose to keep or remove formatting in the pasted content.","cement.dialog.paste.merge":"Keep formatting","cement.dialog.paste.clean":"Remove formatting","safari.imagepaste":st(),"webview.imagepaste":st(),"error.code.images.not.found":"The images service was not found: (","error.imageupload":"Image failed to upload: (","error.full.stop":").","errors.local.images.disallowed":"Local image paste has been disabled. Local images have been removed from pasted content.","errors.imageimport.failed":"Some images failed to import.","errors.imageimport.unsupported":"Unsupported image type.","errors.imageimport.invalid":"Image is invalid."},Ht=Object.hasOwnProperty,Wt=Object.setPrototypeOf,Bt=Object.isFrozen,zt=Object.getPrototypeOf,$t=Object.getOwnPropertyDescriptor,qt=Object.freeze,Vt=Object.seal,Gt=Object.create,Kt="undefined"!=typeof Reflect&&Reflect,Xt=Kt.apply||function(e,t,n){return e.apply(t,n)},qt=qt||function(e){return e},Vt=Vt||function(e){return e},Jt=Kt.construct||function(e,t){return new(Function.prototype.bind.apply(e,[null].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(t))))},Yt=cn(Array.prototype.forEach),Zt=cn(Array.prototype.pop),Qt=cn(Array.prototype.push),en=cn(String.prototype.toLowerCase),tn=cn(String.prototype.match),nn=cn(String.prototype.replace),rn=cn(String.prototype.indexOf),on=cn(String.prototype.trim),an=cn(RegExp.prototype.test),un=(mt=TypeError,function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Jt(mt,t)});function cn(o){return function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Xt(o,e,n)}}function sn(e,t){Wt&&Wt(e,null);for(var n=t.length;n--;){var r,o=t[n];"string"!=typeof o||(r=en(o))!==o&&(Bt(t)||(t[n]=r),o=r),e[o]=!0}return e}function ln(e){var t=Gt(null),n=void 0;for(n in e)Xt(Ht,e,[n])&&(t[n]=e[n]);return t}function fn(e,t){for(;null!==e;){var n=$t(e,t);if(n){if(n.get)return cn(n.get);if("function"==typeof n.value)return cn(n.value)}e=zt(e)}return function(e){return console.warn("fallback value for",e),null}}var dn=qt(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),mn=qt(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),pn=qt(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),gn=qt(["animate","color-profile","cursor","discard","fedropshadow","feimage","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),vn=qt(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),hn=qt(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),yn=qt(["#text"]),bn=qt(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),xn=qt(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),wn=qt(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Tn=qt(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),In=Vt(/\{\{[\s\S]*|[\s\S]*\}\}/gm),Sn=Vt(/<%[\s\S]*|[\s\S]*%>/gm),On=Vt(/^data-[\-\w.\u00B7-\uFFFF]/),An=Vt(/^aria-[\-\w]+$/),Cn=Vt(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Dn=Vt(/^(?:\w+script|data):/i),kn=Vt(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Ln="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function En(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function Nn(n){return{sanitizeHtml:function(e,t){return n(e)?e:function(e){lr.addHook("uponSanitizeElement",function(e,t){E(fr,t.tagName)||Te(t.allowedTags,t.tagName)||(t.allowedTags[t.tagName]=!0)}),lr.addHook("uponSanitizeAttribute",function(e,t){0===t.attrName.indexOf("on")||Te(t.allowedAttributes,t.attrName)||(t.allowedAttributes[t.attrName]=!0),t.attrValue&&-1!==t.attrValue.indexOf("\n")&&(t.attrValue=t.attrValue.replace(/\r?\n/g,""))});var t=Lt(Nt(e),"<!"),n=t?"<body>"+e+"</body>":e.replace(/^[\S\s]*?(<!DOCTYPE|<html)/i,"$1"),r=lr.sanitize(n,{ALLOW_UNKNOWN_PROTOCOLS:!0,FORBID_TAGS:fr,WHOLE_DOCUMENT:-1!==e.lastIndexOf("</html>")});return lr.removeHook("uponSanitizeElement"),lr.removeHook("uponSanitizeAttribute"),t?Ke(Ge(r,"<body>"),"</body>"):r}(e)},sanitizeText:o}}function _n(e){return-1!==e.indexOf(gr)}function Pn(e,t){var n=new tinymce.html.DomParser({},e.schema).parse(t,{forced_root_block:!1,isRootContent:!0});return new tinymce.html.Serializer({validate:!0},e.schema).serialize(n)}function Rn(e){var n=Nn(_n),r={sanitizeHtml:I(Pn,e),sanitizeText:o};return{sanitizeText:n.sanitizeText,sanitizeHtml:function(e,t){return(_n(e)?r:n).sanitizeHtml(e,t)}}}function Mn(e){(n=document.createElement("div")).appendChild(e.cloneNode(!0));var t=n.innerHTML,n=e=null;return t}function Fn(e,t){return function(){return e.apply(t,arguments)}}function jn(e){"undefined"!=typeof console&&console.log&&console.log(e)}function Un(t,e,n){return function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return tinymce.each(Array.prototype.slice.call(arguments,1),function(e){for(var t in e)n[t]=e[t]}),n}(function(e){if(t&&"string"!=typeof t)return t;switch(t){case"clean":return yr;case"merge":return br;default:return e}}(e),{base_64_images:n})}function Hn(e){return e.replace(/-(.)/g,function(e,t){return t.toUpperCase()})}function Wn(i,e,t){var n,r,o,a,u,c,s,l,f,d;switch(i.nodeType){case 1:e?n=wr:(n=xr,a=Sr(i),u={},c=i,s=function(e,t){u[e]=t},null!=(d=t||c.getAttribute("style"))&&d.split||(d=c.style.cssText),vr(d.split(";"),function(e){var t=e.indexOf(":");0<t&&(l=(l=(l=hr(e.substring(0,t))).toUpperCase()===l?l.toLowerCase():l).replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}),f=hr(e.substring(t+1)),Or=Or||0===l.indexOf("mso-"),s(l,f))}),Or||(f=c.style["mso-list"])&&s("mso-list",f)),r="HTML"!==i.scopeName&&i.scopeName&&i.tagName&&i.tagName.indexOf(":")<=0?(i.scopeName+":"+i.tagName).toUpperCase():i.tagName;break;case 3:n=Tr,o=i.nodeValue;break;case 8:n=Ir,o=i.nodeValue;break;default:jn("WARNING: Unsupported node type encountered: "+i.nodeType)}function m(e){n===xr&&a.filter(e)}return{getNode:function(){return a&&a.getAttributes(),i},tag:function(){return r},type:function(){return n},text:function(){return o},toString:function(){return"Type: "+n+", Tag: "+r+" Text: "+o},getAttribute:function(e){return a.get(e.toLowerCase())},filterAttributes:m,filterStyles:function(r){var o;n===xr&&(o="",vr(u,function(e,t){var n=r(t,e);null===n?(i.style.removeProperty?i.style.removeProperty(Hn(t)):i.style.removeAttribute(Hn(t)),delete u[t]):(o+=t+": "+n+"; ",u[t]=n)}),o=o||null,m(function(e,t){return"style"===e?o:t}),i.style.cssText=o)},getAttributeCount:function(){return a.getAttributeCount()},attributes:function(e){a.each(e)},getStyle:function(e){return u[e]},styles:function(n){vr(u,function(e,t){n(t,e)})},getComputedStyle:function(){return(e=i).ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,null):e.currentStyle||{};var e},isWhitespace:function(){return n===Tr&&/^[\s\u00A0]*$/.test(o)}}}function Bn(e,t,n,r){var o=r.createElement(e),i="";return vr(t,function(e,t){o.setAttribute(t,e)}),vr(n,function(e,t){i+=t+":"+e+";",o.style[Hn(t)]=e}),Wn(o,!1,""!==i?i:null)}function zn(e,t){return Wn(t.createElement(e),!0)}function $n(o){function i(e){a.appendChild(e)}var a=o.createDocumentFragment();return{dom:a,receive:function(e){var t,n,r;switch(e.type()){case xr:r=e.getNode().cloneNode(!1),i(n=r),a=n;break;case Tr:t=o.createTextNode(e.text()),i(t);break;case wr:a=a.parentNode;break;case Ir:break;default:throw{message:"Unsupported token type: "+e.type()}}}}}function qn(p,g){return function(t,e,n){function r(){g&&g(m),l=!1,c=[],s=[]}function o(e){vr(e,function(e){t.receive(e)})}function i(e){l?s.push(e):t.receive(e)}function a(){f(),o(s),r()}var u,c,s,l=!1,f=function(){vr(u,function(e){i(e)}),d()},d=function(){u=[]},m={document:n||window.document,settings:e||{},emit:i,receive:function(e){g&&c.push(e),p(m,e),e===Ar&&a()},startTransaction:function(){l=!0},rollback:function(){o(c),r()},commit:a,defer:function(e){(u=u||[]).push(e)},hasDeferred:function(){return u&&0<u.length},emitDeferred:f,dropDeferred:d};return r(),m}}function Vn(n){return qn(function(e,t){t.filterAttributes(Fn(n,e)),e.emit(t)})}function Gn(e){return"A"===e.tag()||"SPAN"===e.tag()}function Kn(e){var t=e.getStyle("mso-list");return t&&"skip"!==t}function Xn(e,t){return e.type()===xr?0===e.getAttributeCount()||t&&1===e.getAttributeCount()&&null!==e.getAttribute("style")&&void 0!==e.getAttribute("style"):e.type()===wr}function Jn(e){if(jr)for(var t=void 0,n=Mr.length,o=void 0,o=0;o<n;o++)(t=Mr[o])&&(t.type()===xr&&"SPAN"===t.tag()&&Xn(t)?function(e){for(var t,n=1,r=o+1;r<e;r++)if((t=Mr[r])&&"SPAN"===t.tag())if(t.type()===xr)n++;else if(t.type()===wr&&0==--n)return Mr[r]=null}(n):e.emit(t));Mr=[],jr=!(Fr=[])}function Yn(e,t){Mr.push(t),Fr=Fr||[],t.type()===xr?Fr.push(t):t.type()===wr&&(Fr.pop(),0===Fr.length&&Jn(e))}function Zn(e){return!Xn(e)&&!/^OLE_LINK/.test(e.getAttribute("name"))}function Qn(e,t){var n={tag:e.tag,type:e.type,variant:t};return e.start&&(n.start=e.start),e.type||delete n.type,n}function er(e,t,n){var r,o,i,a=null;return e&&(r=e.text,o=e.symbolFont),r=hr(r),(a=to[r])?a=Qn(a,r):o?a=(a=eo[r])?Qn(a,r):{tag:"UL",variant:r}:(vr(Qr,function(e){if(e.regex.test(r)){if(t&&no(e.type,t,!0))return(a=e.type).start=parseInt(r,10),!1;(a=a||e.type).start=parseInt(r,10)}}),a&&!a.variant&&(i="("===r.charAt(0)?"()":")"===r.charAt(r.length-1)?")":".",a=Qn(a,i))),a=a&&"OL"===a.tag&&n&&("P"!==n.tag()||/^MsoHeading/.test(n.getAttribute("class")))?null:a}function tr(e,t){function n(e){var t=e.style.fontFamily;t&&(a="Wingdings"===t||"Symbol"===t)}var r,o,i,a=!1;if(e.type()===xr&&t.openedTag&&"SPAN"===e.tag()){for(n(r=t.openedTag.getNode()),1<r.childNodes.length&&"A"===r.firstChild.tagName&&""===r.firstChild.textContent&&(r=r.childNodes[1]);r.firstChild&&("SPAN"===r.firstChild.tagName||"A"===r.firstChild.tagName);)n(r=r.firstChild);return(r=r.firstChild)&&3===r.nodeType?(o=r.value,hr(o)||(o=(r=r.parentNode.nextSibling)?r.value:""),r&&hr(r.parentNode.textContent)==o&&((i=er({text:o,symbolFont:a},null,t.originalToken))&&(r.nextSibling&&"SPAN"===r.nextSibling.tagName&&/^[\u00A0\s]/.test(r.nextSibling.firstChild.value)&&("P"===t.openedTag.tag()||"UL"===i.tag)))):r&&"IMG"===r.tagName}}function nr(i,a){function u(e,t){var n={},r={};m++,t&&e.type&&(n={"list-style-type":e.type}),e.start&&1<e.start&&(r={start:e.start}),o.push(e),i.emit(Bn(e.tag,r,n,a)),f=e}function c(){i.emit(zn(o.pop().tag,a)),m--,f=o[o.length-1]}function s(e,t,n){var r,o={};e?void 0!==(r=e.getStyle("margin-left"))&&(o["margin-left"]=r):o["list-style-type"]="none",f&&!no(f,t)&&(c(),n&&(i.emit(Bn("P",{},{},a)),i.emit(Wn(a.createTextNode("\xa0"))),i.emit(zn("P",a))),u(t,!0)),i.emit(Bn("LI",{},o,a)),e&&"P"!==e.tag()?(d.push(e.tag()),e.filterStyles(function(){return null}),i.emit(e)):d.push("P")}function l(){var e=d?d.pop():"P";"P"!==e&&i.emit(zn(e,a)),i.emit(zn("LI",a))}var f,o=[],d=[],m=0;return{openList:u,closelist:c,closeAllLists:function(){for(;0<m;)l(),c();i.commit()},closeItem:l,openLI:s,openItem:function(e,t,n,r){if(n){for(m=m||0;e<m;)l(),c();var o;if(n=o="UL"===(o=n).tag&&oo[e-1]===o.type?{tag:"UL"}:o,m===e)l(),s(t,n,r);else for(1<e&&0<d.length&&"P"!==d[d.length-1]&&(i.emit(zn(d[d.length-1],a)),d[d.length-1]="P");m<e;)u(n,m===e-1),s(m===e?t:void 0,n)}},getCurrentListType:function(){return f},getCurrentLevel:function(){return m}}}function rr(e,t){jn("Unexpected token in list conversion: "+t.toString()),e.rollback()}function or(e,t,n){n.type()===Tr&&""===hr(n.text())?e.defer(n):t.skippedPara||n.type()!==xr||"P"!==n.tag()||Kn(n)?ao(e,t,n):(t.openedTag=n,e.defer(n),t.nextFilter=io)}function ir(e,t,n){n.type()===wr&&t.originalToken.tag()===n.tag()&&(t.nextFilter=or,t.styleLevelAdjust=-1),e.emit(n)}function ar(e){var a,u,t;go.nextFilter=po,go.itemLevel=0,go.originalToken=null,go.commentMode=!1,go.openedTag=null,go.symbolFont=!1,go.listType=null,go.indentGuesser={guessIndentLevel:function(e,t,n,r){var o,i;return r&&/^([0-9]+\.)+[0-9]+\.?$/.test(r.text)?r.text.replace(/([0-9]+|\.$)/g,"").length+1:(o=u||parseInt(ro(n,t.getAttribute("class"))),i=function(e,t){for(var n=0,r=e.parentNode;null!=r&&r!==t.parentNode;)n+=r.offsetLeft,r=r.offsetParent;return n}(e.getNode(),t.getNode()),o?a?i+=a:0===i&&(i+=a=o):o=48,u=o=Math.min(i,o),Math.max(1,Math.floor(i/o))||1)}},go.emitter=nr(e,e.document),go.styles=(t=!1,{check:function(e){return t&&e.type()===Tr?(e.text(),!0):e.type()===xr&&"STYLE"===e.tag()?t=!0:e.type()===wr&&"STYLE"===e.tag()&&!(t=!1)}}),go.spanCount=[],go.skippedPara=!1,go.styleLevelAdjust=0,go.bulletInfo=void 0}var ur,cr,sr,lr=function t(e){function s(e){return t(e)}var l=0<arguments.length&&void 0!==e?e:"undefined"==typeof window?null:window;if(s.version="2.2.8",s.removed=[],!l||!l.document||9!==l.document.nodeType)return s.isSupported=!1,s;var n,f=l.document,a=l.document,d=l.DocumentFragment,r=l.HTMLTemplateElement,m=l.Node,u=l.Element,o=l.NodeFilter,i=l.NamedNodeMap,c=void 0===i?l.NamedNodeMap||l.MozNamedAttrMap:i,p=l.Text,g=l.Comment,v=l.DOMParser,h=l.trustedTypes,y=u.prototype,b=fn(y,"cloneNode"),x=fn(y,"nextSibling"),w=fn(y,"childNodes"),T=fn(y,"parentNode");"function"!=typeof r||(n=a.createElement("template")).content&&n.content.ownerDocument&&(a=n.content.ownerDocument);var I=function(e,t){if("object"!==(void 0===e?"undefined":Ln(e))||"function"!=typeof e.createPolicy)return null;var n=null,r="data-tt-policy-suffix",o="dompurify"+((n=t.currentScript&&t.currentScript.hasAttribute(r)?t.currentScript.getAttribute(r):n)?"#"+n:"");try{return e.createPolicy(o,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(h,f),S=I&&re?I.createHTML(""):"",O=a,A=O.implementation,C=O.createNodeIterator,D=O.createDocumentFragment,k=f.importNode,L={};try{L=ln(a).documentMode?a.documentMode:{}}catch(e){}var E={};function N(e){ve&&ve===e||(e=ln(e=e&&"object"===(void 0===e?"undefined":Ln(e))?e:{}),W="ALLOWED_TAGS"in e?sn({},e.ALLOWED_TAGS):B,z="ALLOWED_ATTR"in e?sn({},e.ALLOWED_ATTR):$,le="ADD_URI_SAFE_ATTR"in e?sn(ln(fe),e.ADD_URI_SAFE_ATTR):fe,ce="ADD_DATA_URI_TAGS"in e?sn(ln(se),e.ADD_DATA_URI_TAGS):se,q="FORBID_TAGS"in e?sn({},e.FORBID_TAGS):{},V="FORBID_ATTR"in e?sn({},e.FORBID_ATTR):{},_="USE_PROFILES"in e&&e.USE_PROFILES,G=!1!==e.ALLOW_ARIA_ATTR,K=!1!==e.ALLOW_DATA_ATTR,X=e.ALLOW_UNKNOWN_PROTOCOLS||!1,J=e.SAFE_FOR_TEMPLATES||!1,Y=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,ne=!1!==e.RETURN_DOM_IMPORT,re=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,oe=!1!==e.SANITIZE_DOM,ie=!1!==e.KEEP_CONTENT,ae=e.IN_PLACE||!1,H=e.ALLOWED_URI_REGEXP||H,ge=e.NAMESPACE||ge,J&&(K=!1),te&&(ee=!0),_&&(W=sn({},[].concat(En(yn))),z=[],!0===_.html&&(sn(W,dn),sn(z,bn)),!0===_.svg&&(sn(W,mn),sn(z,xn),sn(z,Tn)),!0===_.svgFilters&&(sn(W,pn),sn(z,xn),sn(z,Tn)),!0===_.mathMl&&(sn(W,vn),sn(z,wn),sn(z,Tn))),e.ADD_TAGS&&sn(W=W===B?ln(W):W,e.ADD_TAGS),e.ADD_ATTR&&sn(z=z===$?ln(z):z,e.ADD_ATTR),e.ADD_URI_SAFE_ATTR&&sn(le,e.ADD_URI_SAFE_ATTR),ie&&(W["#text"]=!0),Y&&sn(W,["html","head","body"]),W.table&&(sn(W,["tbody"]),delete q.tbody),qt&&qt(e),ve=e)}s.isSupported="function"==typeof T&&A&&void 0!==A.createHTMLDocument&&9!==L;var _,P=In,R=Sn,M=On,F=An,j=Dn,U=kn,H=Cn,W=null,B=sn({},[].concat(En(dn),En(mn),En(pn),En(vn),En(yn))),z=null,$=sn({},[].concat(En(bn),En(xn),En(wn),En(Tn))),q=null,V=null,G=!0,K=!0,X=!1,J=!1,Y=!1,Z=!1,Q=!1,ee=!1,te=!1,ne=!0,re=!1,oe=!0,ie=!0,ae=!1,ue=sn({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ce=null,se=sn({},["audio","video","img","source","image","track"]),le=null,fe=sn({},["alt","class","for","id","label","name","pattern","placeholder","summary","title","value","style","xmlns"]),de="http://www.w3.org/1998/Math/MathML",me="http://www.w3.org/2000/svg",pe="http://www.w3.org/1999/xhtml",ge=pe,ve=null,he=a.createElement("form"),ye=sn({},["mi","mo","mn","ms","mtext"]),be=sn({},["foreignobject","desc","title","annotation-xml"]),xe=sn({},mn);sn(xe,pn),sn(xe,gn);var we=sn({},vn);function Te(t){Qt(s.removed,{element:t});try{t.parentNode.removeChild(t)}catch(e){try{t.outerHTML=S}catch(e){t.remove()}}}function Ie(e,t){try{Qt(s.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){Qt(s.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!z[e])if(ee||te)try{Te(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}}function Se(e){var t,n=void 0,r=void 0;Q?e="<remove></remove>"+e:r=(t=tn(e,/^[\r\n\t ]+/))&&t[0];var o=I?I.createHTML(e):e;if(ge===pe)try{n=(new v).parseFromString(o,"text/html")}catch(e){}n&&n.documentElement||((n=A.createDocument(ge,"template",null)).documentElement.innerHTML=o);var i=n.body||n.documentElement;return e&&r&&i.insertBefore(a.createTextNode(r),i.childNodes[0]||null),Y?n.documentElement:i}function Oe(e){return C.call(e.ownerDocument||e,e,o.SHOW_ELEMENT|o.SHOW_COMMENT|o.SHOW_TEXT,function(){return o.FILTER_ACCEPT},!1)}function Ae(e){return"object"===(void 0===m?"undefined":Ln(m))?e instanceof m:e&&"object"===(void 0===e?"undefined":Ln(e))&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName}function Ce(e,t,n){E[e]&&Yt(E[e],function(e){e.call(s,t,n,ve)})}function De(e){var t;if(Ce("beforeSanitizeElements",e,null),!(e instanceof p||e instanceof g||"string"==typeof e.nodeName&&"string"==typeof e.textContent&&"function"==typeof e.removeChild&&e.attributes instanceof c&&"function"==typeof e.removeAttribute&&"function"==typeof e.setAttribute&&"string"==typeof e.namespaceURI&&"function"==typeof e.insertBefore))return Te(e),1;if(tn(e.nodeName,/[\u0080-\uFFFF]/))return Te(e),1;var n=en(e.nodeName);if(Ce("uponSanitizeElement",e,{tagName:n,allowedTags:W}),!Ae(e.firstElementChild)&&(!Ae(e.content)||!Ae(e.content.firstElementChild))&&an(/<[/\w]/g,e.innerHTML)&&an(/<[/\w]/g,e.textContent))return Te(e),1;if(W[n]&&!q[n])return e instanceof u&&!function(e){var t=T(e);t&&t.tagName||(t={namespaceURI:pe,tagName:"template"});var n=en(e.tagName),r=en(t.tagName);if(e.namespaceURI===me)return t.namespaceURI===pe?"svg"===n:t.namespaceURI===de?"svg"===n&&("annotation-xml"===r||ye[r]):Boolean(xe[n]);if(e.namespaceURI===de)return t.namespaceURI===pe?"math"===n:t.namespaceURI===me?"math"===n&&be[r]:Boolean(we[n]);if(e.namespaceURI===pe&&(t.namespaceURI!==me||be[r])&&(t.namespaceURI!==de||ye[r])){var o=sn({},["title","style","font","a","script"]);return!we[n]&&(o[n]||!xe[n])}}(e)||("noscript"===n||"noembed"===n)&&an(/<\/no(script|embed)/i,e.innerHTML)?(Te(e),1):(J&&3===e.nodeType&&(t=e.textContent,t=nn(t,P," "),t=nn(t,R," "),e.textContent!==t&&(Qt(s.removed,{element:e.cloneNode()}),e.textContent=t)),Ce("afterSanitizeElements",e,null),0);if(ie&&!ue[n]){var r=T(e)||e.parentNode,o=w(e)||e.childNodes;if(o&&r)for(var i=o.length-1;0<=i;--i)r.insertBefore(b(o[i],!0),x(e))}return Te(e),1}function ke(e,t,n){if(oe&&("id"===t||"name"===t)&&(n in a||n in he))return!1;if(!(K&&an(M,t)||G&&an(F,t))){if(!z[t]||V[t])return!1;if(!le[t]&&!an(H,nn(n,U,""))&&("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==rn(n,"data:")||!ce[e])&&(!X||an(j,nn(n,U,"")))&&n)return!1}return!0}function Le(e){var t,n=void 0,r=void 0;Ce("beforeSanitizeAttributes",e,null);var o=e.attributes;if(o){for(var i={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z},r=o.length;r--;){var a=(t=o[r]).name,u=t.namespaceURI,n=on(t.value),c=en(a);if(i.attrName=c,i.attrValue=n,i.keepAttr=!0,i.forceKeepAttr=void 0,Ce("uponSanitizeAttribute",e,i),n=i.attrValue,!i.forceKeepAttr&&(Ie(a,e),i.keepAttr))if(an(/\/>/i,n))Ie(a,e);else if(J&&(n=nn(n,P," "),n=nn(n,R," ")),ke(e.nodeName.toLowerCase(),c,n))try{u?e.setAttributeNS(u,a,n):e.setAttribute(a,n),Zt(s.removed)}catch(e){}}Ce("afterSanitizeAttributes",e,null)}}return sn(we,hn),s.sanitize=function(e,t){var n,r,o=void 0,i=void 0,a=void 0;if("string"!=typeof(e=e||"\x3c!--\x3e")&&!Ae(e)){if("function"!=typeof e.toString)throw un("toString is not a function");if("string"!=typeof(e=e.toString()))throw un("dirty is not a string, aborting")}if(!s.isSupported){if("object"===Ln(l.toStaticHTML)||"function"==typeof l.toStaticHTML){if("string"==typeof e)return l.toStaticHTML(e);if(Ae(e))return l.toStaticHTML(e.outerHTML)}return e}if(Z||N(t),s.removed=[],!(ae="string"!=typeof e&&ae))if(e instanceof m)1===(n=(o=Se("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===n.nodeName||"HTML"===n.nodeName?o=n:o.appendChild(n);else{if(!ee&&!J&&!Y&&-1===e.indexOf("<"))return I&&re?I.createHTML(e):e;if(!(o=Se(e)))return ee?null:S}o&&Q&&Te(o.firstChild);for(var u=Oe(ae?e:o);r=u.nextNode();)3===r.nodeType&&r===i||De(r)||(r.content instanceof d&&function e(t){var n,r=Oe(t);for(Ce("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)Ce("uponSanitizeShadowNode",n,null),De(n)||(n.content instanceof d&&e(n.content),Le(n));Ce("afterSanitizeShadowDOM",t,null)}(r.content),Le(r),i=r);if(i=null,ae)return e;if(ee){if(te)for(a=D.call(o.ownerDocument);o.firstChild;)a.appendChild(o.firstChild);else a=o;return ne?k.call(f,a,!0):a}var c=Y?o.outerHTML:o.innerHTML;return J&&(c=nn(c,P," "),c=nn(c,R," ")),I&&re?I.createHTML(c):c},s.setConfig=function(e){N(e),Z=!0},s.clearConfig=function(){ve=null,Z=!1},s.isValidAttribute=function(e,t,n){return ve||N({}),ke(en(e),en(t),n)},s.addHook=function(e,t){"function"==typeof t&&(E[e]=E[e]||[],Qt(E[e],t))},s.removeHook=function(e){E[e]&&Zt(E[e])},s.removeHooks=function(e){E[e]&&(E[e]=[])},s.removeAllHooks=function(){E={}},s}(),fr=["script","svg"],dr={sanitizeHtml:o,sanitizeText:o},mr="x-tinymce/html",pr=b(mr),gr="\x3c!-- "+mr+" --\x3e",vr=tinymce.each,hr=tinymce.trim,yr={strip_class_attributes:"all",retain_style_properties:"none"},br={strip_class_attributes:"none",retain_style_properties:"valid"},xr="startElement",wr="endElement",Tr="text",Ir="comment",Sr=function(o){function t(){return a}function i(n){vr(l(),function(e,t){n(t,e)})}var a,u,c,s=0,l=function(){return a={},s=0,vr(o.attributes,function(e){var t=e.nodeName,n=e.value;!1===e.specified&&("name"!==e.nodeName||""===e.value)||null!=n&&(a[t]=n,s++)}),void 0===a.style&&o.style.cssText&&(a.style=o.style.cssText,s++),l=t,a};return{get:function(e){return l()[e]},each:i,filter:function(e){var n,r;u||(c=l),r=e,u=(n=u)&&r?function(e,t){return r(e,n(e,t))}:n||r,l=function(){return l=c,i(function(e,t){var n=u(e,t);null===n?(o.removeAttribute(e),delete a[e],s--):n!==t&&("class"===e?o.className=n:o.setAttribute(e,n),a[e]=n)}),l=t,a}},getAttributes:function(){return l()},getAttributeCount:function(){return l(),s}}},Or=!1,Ar=zn("HTML",window.document),Cr=[function(e){var t=e;return 65279===t.charCodeAt(t.length-1)?t.substring(0,t.length-1):e}],Dr=tinymce.isIE&&9<=document.documentMode?[function(e){return e.replace(/<BR><BR>/g,"<br>")},function(e){return e.replace(/<br>/g," ")},function(e){return e.replace(/<br><br>/g,"<BR><BR>")},function(e){return/<(h[1-6r]|p|div|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|blockquote|center|dl|dt|dd|dir|fieldset)/.test(e)?e.replace(/(?:<br>&nbsp;[\s\r\n]+|<br>)*(<\/?(h[1-6r]|p|div|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|blockquote|center|dl|dt|dd|dir|fieldset)[^>]*>)(?:<br>&nbsp;[\s\r\n]+|<br>)*/g,"$1"):e}].concat(Cr):Cr,kr=(ur=Array.prototype.slice.call(Dr).reverse(),function(e){for(var t=e,n=0;n<ur.length;n++)t=(0,ur[n])(t);return t}),Lr=/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/,Er=qn(function(e,t){var r,n=e.settings.get("retain_style_properties");t.filterStyles((r=n,function(e,t){var n=!1;switch(r){case"all":case"*":n=!0;break;case"valid":n=!Lr.test(e);break;case void 0:case"none":n="list-style-type"===e;break;default:n=0<=(","+r+",").indexOf(","+e+",")}return n?t:null})),e.emit(t)}),Nr=qn(function(e,t){e.seenList||(e.inferring?"LI"===t.tag()&&(t.type()===xr?e.inferring++:(e.inferring--,e.inferring||(e.needsClosing=!0))):("OL"===t.tag()||"UL"===t.tag()?e.seenList=!0:"LI"===t.tag()&&(e.inferring=1,e.needsClosing||e.emit(Bn("UL",{},{},e.document))),!e.needsClosing||e.inferring||t.isWhitespace()||(e.needsClosing=!1,e.emit(zn("UL",e.document))))),e.emit(t)}),_r=Vn(function(e,t){return"name"===e||"id"===e?null:t}),Pr=Vn(function(e,t){if("class"===e)switch(this.settings.get("strip_class_attributes")){case"mso":return 0===t.indexOf("Mso")?null:t;case"none":return t;default:return null}return t}),Rr=function(){if(0<navigator.userAgent.indexOf("Gecko")&&navigator.userAgent.indexOf("WebKit")<0)return!1;var e=document.createElement("div");try{e.innerHTML='<p style="mso-list: Ignore;">&nbsp;</p>'}catch(e){return!1}return"Ignore"===Wn(e.firstChild).getStyle("mso-list")}(),Mr=[],Fr=[],jr=!1,Ur=qn(function(e,t){function n(e){return!(0<=",FONT,EM,STRONG,SAMP,ACRONYM,CITE,CODE,DFN,KBD,TT,B,I,U,S,SUB,SUP,INS,DEL,VAR,SPAN,".indexOf(","+e.tag()+",")&&Xn(e,!0))}0===(Mr=Mr||[]).length?t.type()!==xr||n(t)?e.emit(t):Yn(e,t):(jr=jr||n(t),Yn(e,t))}),Hr=Vn(function(e,t){return"style"===e&&""===t?null:t}),Wr=Vn(function(e,t){return"lang"===e?null:t}),Br=qn(function(e,t){if("IMG"===t.tag()){if(t.type()===wr&&e.skipEnd)return e.skipEnd=!1;if(t.type()===xr){if(/^file:/.test(t.getAttribute("src")))return e.skipEnd=!0,0;if(e.settings.get("base_64_images")&&/^data:image\/.*;base64/.test(t.getAttribute("src")))return e.skipEnd=!0,0}}e.emit(t)}),zr=qn(function(e,t){"META"!==t.tag()&&"LINK"!==t.tag()&&e.emit(t)}),$r=[],qr=qn(function(e,t){t.type()===xr&&"A"===t.tag()?($r.push(t),Zn(t)&&e.defer(t)):t.type()===wr&&"A"===t.tag()?(Zn($r.pop())&&e.defer(t),0===$r.length&&e.emitDeferred()):e.hasDeferred()?e.defer(t):e.emit(t)}),Vr=!1,Gr=[qn(function(e,t){"SCRIPT"===t.tag()?Vr=t.type()===xr:Vr||(t.filterAttributes(function(e,t){return/^on/.test(e)||"language"===e?null:t}),e.emit(t))}),_r,Br,Er,Wr,Hr,Pr,qr,Ur,zr,Nr],Kr=qn(function(e,n){n.filterAttributes(function(e,t){return"align"!==e&&("UL"!==n.tag()&&"OL"!==n.tag()||"type"!==e)?t:null}),e.emit(n)}),Xr=Vn(function(e,t){return/^xmlns(:|$)/.test(e)?null:t}),Jr=qn(function(e,t){t.tag&&/^([OVWXP]|U[0-9]+|ST[0-9]+):/.test(t.tag())||e.emit(t)}),Yr=Vn(function(e,t){return"href"===e&&(0<=t.indexOf("#_Toc")||0<=t.indexOf("#_mso"))?null:t}),Zr=Vn(function(e,t){return/^v:/.test(e)?null:t}),Qr=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"OL",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"OL",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"OL",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"OL"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"OL",variant:"outline"}},{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"OL",type:"upper-alpha"}}],eo={"\u2022":{tag:"UL",type:"disc"},"\xb7":{tag:"UL",type:"disc"},"\xa7":{tag:"UL",type:"square"}},to={o:{tag:"UL",type:"circle"},"-":{tag:"UL",type:"disc"},"\u25cf":{tag:"UL",type:"disc"}},no=function(e,t,n){return e===t||e&&t&&e.tag===t.tag&&e.type===t.type&&(n||e.variant===t.variant)},ro=(cr=function(e,t){var n,r=/([^{]+){([^}]+)}/g;for(r.lastIndex=0;null!==(n=r.exec(e));)vr(n[1].split(","),function(){var e=(void 0).indexOf(".");if(0<=e&&void 0===hr((void 0).substring(e+1)))return!1}());return!1},sr={},function(e,t){var n,r=e+","+t;return sr.hasOwnProperty(r)?sr[r]:(n=cr.call(null,e,t),sr[r]=n)}),oo=["disc","circle","square"],io=function(e,t,n){n.type()!==xr||"SPAN"!==n.tag()||0!==t.spanCount.length||!Rr&&tr(n,t)||Kn(n)?n.type()===wr?"SPAN"===n.tag()?(e.defer(n),t.spanCount.pop()):"P"===n.tag()?(e.defer(n),t.skippedPara=!0,t.openedTag=null,t.nextFilter=or):(t.nextFilter=ao,t.nextFilter(e,t,n)):n.isWhitespace()?e.defer(n):(t.nextFilter=ao,t.nextFilter(e,t,n)):(e.defer(n),t.spanCount.push(n))},ao=function(e,t,n){function r(){t.emitter.closeAllLists(),e.emitDeferred(),t.openedTag=null,e.emit(n),t.nextFilter=ao}var o;n.type()===xr&&Kn(n)&&"LI"!==n.tag()?(o=/ level([0-9]+)/.exec(n.getStyle("mso-list")))&&o[1]?(t.itemLevel=parseInt(o[1],10)+t.styleLevelAdjust,t.nextFilter===ao?e.emitDeferred():e.dropDeferred(),t.nextFilter=uo,e.startTransaction(),t.originalToken=n,t.commentMode=!1):r():!Rr&&(n.type()===Ir&&"[if !supportLists]"===n.text()||tr(n,e))?(n.type()===xr&&"SPAN"===n.tag()&&t.spanCount.push(n),t.nextFilter=uo,e.startTransaction(),t.originalToken=t.openedTag,t.commentMode=!0,t.openedTag=null,e.dropDeferred()):n.type()===wr&&Gn(n)?(e.defer(n),t.spanCount.pop()):n.type()===xr?Gn(n)?(e.defer(n),t.spanCount.push(n)):(t.openedTag&&(t.emitter.closeAllLists(),e.emitDeferred()),t.openedTag=n,e.defer(n)):r()},uo=function(e,t,n){var r,o;n.type()===xr&&"Ignore"===n.getStyle("mso-list")&&(t.nextFilter=co),n.type()===xr&&"SPAN"===n.tag()?(t.spanCount.push(n),(t.commentMode&&""===n.getAttribute("style")||null===n.getAttribute("style"))&&(t.nextFilter=co)):"A"===n.tag()?n.type()===xr?t.spanCount.push(n):t.spanCount.pop():n.type()===Tr?t.commentMode?(t.nextFilter=co,t.nextFilter(e,t,n)):(r=t.originalToken,o=t.spanCount,t.emitter.closeAllLists(),e.emit(r),vr(o,Fn(e.emit,e)),e.emit(n),e.commit(),t.originalToken=r,t.nextFilter=ir):!t.commentMode&&n.type()===Ir||rr(e,n)},co=function(e,t,n){n.type()===Tr?n.isWhitespace()||(t.nextFilter=so,t.bulletInfo={text:n.text(),symbolFont:t.symbolFont}):Gn(n)?n.type()===xr?t.spanCount.push(n):t.spanCount.pop():n.type()===xr&&"IMG"===n.tag()?(t.nextFilter=so,t.bulletInfo={text:"\u2202",symbolFont:!0}):rr(e,n)},so=function(e,t,n){n.type()===xr&&Gn(n)?(t.spanCount.push(n),t.nextFilter=lo):n.type()===wr&&Gn(n)?(t.spanCount.pop(),t.nextFilter=fo):n.type()===wr&&"IMG"===n.tag()||rr(e,n)},lo=function(e,t,n){n.type()===wr&&(Gn(n)&&t.spanCount.pop(),t.nextFilter=fo)},fo=function(n,r,o){function e(e){var t;if(r.nextFilter=mo,r.commentMode&&(r.itemLevel=r.indentGuesser.guessIndentLevel(o,r.originalToken,r.styles.styles,r.bulletInfo)),r.listType=er(r.bulletInfo,(t=r.emitter.getCurrentListType(),r.emitter.getCurrentLevel()===r.itemLevel?t:null),r.originalToken),r.listType){for(r.emitter.openItem(r.itemLevel,r.originalToken,r.listType,r.skippedPara),n.emitDeferred();0<r.spanCount.length;)n.emit(r.spanCount.shift());e&&n.emit(o)}else jn("Unknown list type: "+r.bulletInfo.text+" Symbol font? "+r.bulletInfo.symbolFont),n.rollback()}o.type()===Tr||o.type()===xr?e(!0):o.type()===Ir?e("[endif]"!==o.text()):o.type()===wr?Gn(o)&&r.spanCount.pop():rr(n,o)},mo=function(e,t,n){n.type()===wr&&n.tag()===t.originalToken.tag()?(t.nextFilter=or,t.skippedPara=!1):e.emit(n)},po=ao,go={};function vo(e,t,n){var r=kr(e),o=Qo(r);t.setWordContent(o);var i=Gr;return function(e,t,n,r){for(var i=$n(n),o=function(e,n){var r;n=n||window.document,r=n.createElement("div"),n.body.appendChild(r),r.style.position="absolute",r.style.left="-10000px",r.innerHTML=e;var o=r.firstChild||Ar,i=[],a=!1;return{hasNext:function(){return void 0!==o},next:function(){var e=o,t=a;return!a&&o.firstChild?(i.push(o),o=o.firstChild):a=!a&&1===o.nodeType||(o.nextSibling?(o=o.nextSibling,!1):(o=i.pop(),!0)),e===Ar||o||(n.body.removeChild(r),o=Ar),e===Ar?e:e?Wn(e,t):void 0}}}(e,n),a=function(e,t,n){for(var r=i,o=e.length-1;0<=o;o--)r=e[o](r,t,n);return r}(r,t,n);o.hasNext();)a.receive(o.next());return i.dom}(r,t,n,i=o?Zo.concat(Gr):i)}function ho(c,i,a){return{showDialog:function(u){function n(e){var t,n,r,o,i,a=Rn(c).sanitizeHtml(u);0<a.length&&(c.fire("PastePreProcess",{content:{content:a},internal:!1}),n=Un(t=e,yr,!0),r=Un(t,br,!0),o=r,i=vo(a,{setWordContent:function(e){o=e?n:r},get:function(e){return o[e]}},c.getDoc()),c.fire("PastePostProcess",{node:i,internal:!1}),c.undoManager.transact(function(){var e=Mn(i);c.insertContent(e)}))}var e,t=Y(c),r=Z(c),o=Qo(u)?t:r;(g(e=o)?e():pt.resolve(e)).then(function(e){var t=V(e)?e:"clean";"prompt"===t?(a?ti:ei).openDialog(c,i,n):n(t)},function(e){console.error(e),n("clean")})}}}function yo(e,t){return e.hasEventListeners(t)}function bo(e,t,n,r,o){var i,a,u,c,s,l,f,d,m,p=t.replace(gr,""),g=n,v=r,h=o,y=yo(m=i=e,"PastePreProcess")?m.fire("PastePreProcess",{internal:g,content:p,source:v,mode:h}).content:p,b=n,x=r,w=o;return yo(i,"PastePostProcess")?(u=y,c=b,s=x,l=w,f=(a=i).dom.add(a.getBody(),"div",{style:"display:none"},u),d=a.fire("PastePostProcess",{internal:c,node:f,source:s,mode:l}).node.innerHTML,a.dom.remove(f),d):y}function xo(e){return e.plugins.powerpaste}function wo(c,e,s,l){var f;function d(e,t){return t in e&&0<e[t].length}c.on("dragstart dragend",function(e){f="dragstart"===e.type}),c.on("dragover dragend dragleave",function(e){f||e.preventDefault()});c.on("drop",function(e){if(!f){var t,n=tinymce.dom.RangeUtils;n&&n.getCaretRangeFromPoint&&(v(t=n.getCaretRangeFromPoint(e.clientX||0,e.clientY||0,c.getDoc()))&&c.selection.setRng(t));var r=M(e.target.files||e.dataTransfer.files,(a=ee(c),function(r){return Lt(r.type,"image/")&&N(a,function(e){return t=e.toLowerCase(),(Te(n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"},t)?"image/"+n[t]:"image/"+t)===r.type;var t,n})}));if(0<r.length)return qe(r).get(function(e){var t=_(e,function(e){var t=jt.fromTag("img");return rt(t,"src",Se.cata(e,s.getLocalURL,function(e,t,n){return t})),t.dom.outerHTML}).join("");c.insertContent(bo(c,t,!1,"imagedrop","auto"),{merge:Q(c)}),X(c)&&s.uploadImages(e)}),void e.preventDefault();var o=function(e){var t,n={};if(e&&(!e.getData||(t=e.getData("Text"))&&0<t.length&&(n["text/plain"]=t),e.types))for(var r=0;r<e.types.length;r++){var o=e.types[r];n[o]=e.getData(o)}return n}(e.dataTransfer);u=o,!!(i=u["text/plain"])&&0===i.indexOf("file://")||!d(u,"text/html")&&!d(u,"text/plain")||(ho(c,lt,l).showDialog(o["text/html"]||o["text/plain"]),e.preventDefault())}var i,a,u})}function To(){function n(e){URL.revokeObjectURL(e.objurl())}var o={};return{add:function(e,t,n){var r={id:b(e),imageresult:b(t),objurl:b(n)};return o[e]=r},get:function(e){return pe(o,e)},remove:function(e){var t=o[e];delete o[e],void 0!==t&&n(t)},lookupByData:function(a){return function(e){for(var t=be(e),n=0,r=t.length;n<r;n++){var o=t[n],i=e[o];if(ze(i.imageresult())===a)return re.some(i)}return re.none()}(o)},destroy:function(){se(o,n),o={}}}}function Io(t){var o=[];return{bind:function(e){if(void 0===e)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(t){o=M(o,function(e){return e!==t})},trigger:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var r={};P(t,function(e,t){r[e]=n[t]}),P(o,function(e){e(r)})}}}function So(e){return{registry:le(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:le(e,function(e){return e.trigger})}}function Oo(n){var r,o=!1;return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o||(o=!0,r=n.apply(null,e)),r}}function Ao(e,t){var n=String(t).toLowerCase();return j(e,function(e){return e.search(n)})}function Co(t){return function(e){return Xe(e,t)}}function Do(e){return window.matchMedia(e).matches}function ko(e,t){var n=e.dom;if(1!==n.nodeType)return!1;var r=n;if(void 0!==r.matches)return r.matches(t);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(t);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(t);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function Lo(e){return 1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount}function Eo(e,t){var n=void 0===t?document:t.dom;return Lo(n)?[]:_(n.querySelectorAll(e),jt.fromDom)}function No(e,t){return e.dom===t.dom}function _o(e){return jt.fromDom(e.dom.ownerDocument)}function Po(e){return Mt(e)?e:_o(e)}function Ro(e){return jt.fromDom(Po(e).dom.defaultView)}function Mo(e){return re.from(e.dom.parentNode).map(jt.fromDom)}function Fo(e){return re.from(e.dom.previousSibling).map(jt.fromDom)}function jo(e){return re.from(e.dom.nextSibling).map(jt.fromDom)}function Uo(o){return e=function(t){for(var n=[],e=function(e){return n.push(e),t(e)},r=t(o);(r=r.bind(e)).isSome(););return n}(Fo),(t=oe.call(e,0)).reverse(),t;var e,t}function Ho(e){return _(e.dom.childNodes,jt.fromDom)}function Wo(e){return jt.fromDom(e.dom.host)}function Bo(e,t){return Eo(t,e)}function zo(e){var t=e.replace(/\./g,"-");return{resolve:function(e){return _(e.split(" "),function(e){return t+"-"+e}).join(" ")}}}function $o(e,t){return Bo(e,"img["+Ai+'="'+t+'"]')}function qo(e){return Bo(e,"img:not(["+Ai+"])["+Oi.blobId()+"]")}function Vo(){function o(){return 0<i.length}var i=[],a=[],u=So({complete:Io(["response"])});return{findById:$o,findAll:qo,register:function(e,t){rt(e,Ai,t),i.push(t)},report:function(e,t,n){var r;P(t,function(e){ct(e,Ai),a.push({success:n,element:e.dom})}),r=e,i=M(i,function(e){return e!==r}),o()||(u.trigger.complete(a),a=[])},inProgress:o,isActive:function(e){return E(i,e)},events:u.registry}}ar({});function Go(){return ni(0,0)}function Ko(e){function t(e){return function(){return n===e}}var n=e.current,r=e.version;return{current:n,version:r,isEdge:t("Edge"),isChrome:t("Chrome"),isIE:t("IE"),isOpera:t("Opera"),isFirefox:t(ai),isSafari:t("Safari")}}function Xo(e){function t(e){return function(){return n===e}}var n=e.current,r=e.version;return{current:n,version:r,isWindows:t(si),isiOS:t("iOS"),isAndroid:t(li),isOSX:t("OSX"),isLinux:t("Linux"),isSolaris:t(fi),isFreeBSD:t(di),isChromeOS:t(mi)}}var Jo,Yo,Zo=[Jr,qn(function(e,t){var n,r,o;go.styles.check(t)||(go.symbolFont=(r=go.symbolFont,(n=t).type()===xr&&((o=n.getStyle("font-family"))?r="Wingdings"===o||"Symbol"===o:/^(P|H[1-6]|DIV)$/.test(n.tag())&&(r=!1)),r),go.nextFilter(e,go,t))},function(e){ar(e)}),Yr,Zr,Xr,Kr],Qo=function(e){return 0<=e.indexOf("<o:p>")||0<=e.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")||0<=e.indexOf("MsoListParagraphCxSpFirst")||0<=e.indexOf("<w:WordDocument>")},ei=Object.freeze({__proto__:null,openDialog:function(e,t,n){var r=t("cement.dialog.paste.clean"),o=t("cement.dialog.paste.merge"),i=[{text:r,ariaLabel:r,onclick:function(){u.close(),n("clean")}},{text:o,ariaLabel:o,onclick:function(){u.close(),n("merge")}}],a={title:t("cement.dialog.paste.title"),spacing:10,padding:10,items:[{type:"container",html:t("cement.dialog.paste.instructions")}],buttons:i},u=e.windowManager.open(a);setTimeout(function(){u&&u.getEl().focus()},1)}}),ti=Object.freeze({__proto__:null,openDialog:function(e,t,n){var r=t("cement.dialog.paste.clean"),o=t("cement.dialog.paste.merge"),i={title:t("cement.dialog.paste.title"),body:{type:"panel",items:[{type:"htmlpanel",name:"instructions",html:t("cement.dialog.paste.instructions")}]},buttons:[{text:r,type:"custom",name:"clean"},{text:o,type:"custom",name:"merge"}],onAction:function(e,t){switch(t.name){case"clean":e.close(),n("clean");break;case"merge":e.close(),n("merge")}}};e.windowManager.open(i)}}),ni=function(e,t){return{major:e,minor:t}},ri={nu:ni,detect:function(e,t){var n,r,o=String(t).toLowerCase();return 0===e.length?Go():(r=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}}(e,n=o))?ni(i(1),i(2)):{major:0,minor:0};function i(e){return Number(n.replace(r,"$"+e))}},unknown:Go},oi=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ii={browsers:b([{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return Xe(e,"edge/")&&Xe(e,"chrome")&&Xe(e,"safari")&&Xe(e,"applewebkit")}},{name:"Chrome",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,oi],search:function(e){return Xe(e,"chrome")&&!Xe(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return Xe(e,"msie")||Xe(e,"trident")}},{name:"Opera",versionRegexes:[oi,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Co("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Co("firefox")},{name:"Safari",versionRegexes:[oi,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(Xe(e,"safari")||Xe(e,"mobile/"))&&Xe(e,"applewebkit")}}]),oses:b([{name:"Windows",search:Co("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return Xe(e,"iphone")||Xe(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Co("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Co("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Co("linux"),versionRegexes:[]},{name:"Solaris",search:Co("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Co("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Co("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}])},ai="Firefox",ui=function(){return Ko({current:void 0,version:ri.unknown()})},ci=Ko,si=(b("Edge"),b("Chrome"),b("IE"),b("Opera"),b(ai),b("Safari"),"Windows"),li="Android",fi="Solaris",di="FreeBSD",mi="ChromeOS",pi=function(){return Xo({current:void 0,version:ri.unknown()})},gi=Xo,vi=(b(si),b("iOS"),b(li),b("Linux"),b("OSX"),b(fi),b(di),b(mi),Oo(function(){return e=navigator.userAgent,t=re.from(navigator.userAgentData),n=Do,g=ii.browsers(),v=ii.oses(),h=t.bind(function(e){return r=g,q(e.brands,function(t){var n=t.brand.toLowerCase();return j(r,function(e){var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())}).map(function(e){return{current:e.name,version:ri.nu(parseInt(t.version,10),0)}})});var r}).orThunk(function(){return Ao(g,n=e).map(function(e){var t=ri.detect(e.versionRegexes,n);return{current:e.name,version:t}});var n}).fold(ui,ci),y=Ao(v,r=e).map(function(e){var t=ri.detect(e.versionRegexes,r);return{current:e.name,version:t}}).fold(pi,gi),{browser:h,os:y,deviceType:(i=h,a=e,u=n,c=(o=y).isiOS()&&!0===/ipad/i.test(a),s=o.isiOS()&&!c,f=(l=o.isiOS()||o.isAndroid())||u("(pointer:coarse)"),d=c||!s&&l&&u("(min-device-width:768px)"),m=s||l&&!d,p=i.isSafari()&&o.isiOS()&&!1===/safari/i.test(a),{isiPad:b(c),isiPhone:b(s),isTablet:b(d),isPhone:b(m),isTouch:b(f),isAndroid:o.isAndroid,isiOS:o.isiOS,isWebView:b(p),isDesktop:b(!m&&!d&&!p)})};var e,t,n,r,o,i,a,u,c,s,l,f,d,m,p,g,v,h,y})),hi=ko,yi=g(Element.prototype.attachShadow)&&g(Node.prototype.getRootNode),bi=b(yi),xi=yi?function(e){return jt.fromDom(e.dom.getRootNode())}:Po,wi=function(e){return v(e.dom.shadowRoot)},Ti=function(e){var t=Rt(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;var n,r,o,i,a,u=t.ownerDocument;return o=jt.fromDom(t),a=xi(o),(Ft(i=a)&&v(i.dom.host)?re.some(a):re.none()).fold(function(){return u.body.contains(t)},(n=Ti,r=Wo,function(e){return n(r(e))}))},Ii=function(e,t){var n=[];return P(Ho(e),function(e){n=(n=t(e)?n.concat([e]):n).concat(Ii(e,t))}),n},Si=zo("ephox-salmon").resolve,Oi={uploadInProgress:b(Si("upload-image-in-progress")),blobId:b("data-"+Si("image-blob")),trackedImage:b("data-"+Si("image-upload"))},Ai=Oi.trackedImage();function Ci(t){return yt(function(n){var e=new FileReader;e.onload=function(e){var t=e.target?e.target.result:"";n(t)},e.readAsText(t)})}function Di(e){try{var t=JSON.parse(e);return ji.value(t)}catch(e){return ji.error("Response was not JSON.")}}function ki(e){return bt(e.response)}function Li(e,t){function n(e){return Bi({message:e,status:t.status,responseText:t.responseText})}switch(e){case Jo.JSON:return Di(t.response).fold(n,Wi);case Jo.Blob:case Jo.Text:return Wi(t.response);default:return n("unknown data type")}}function Ei(p){return Hi(function(r){var o,i=new XMLHttpRequest;i.open(p.method,(o=p.url,re.from(p.query).map(function(e){var t=de(e,function(e,t){return encodeURIComponent(t)+"="+encodeURIComponent(e)}),n=Xe(o,"?")?"&":"?";return 0<t.length?o+n+t.join("&"):o}).getOr(o)),!0);var n,e,t,a,u,c,s,l,f,d=(a=(t=p).body,u=re.from(a).bind(function(e){switch(e.type){case Jo.JSON:return re.some("application/json");case Jo.FormData:return re.some("application/x-www-form-urlencoded; charset=UTF-8");case Jo.MultipartFormData:return re.none();default:return Jo.Text,re.some("text/plain")}}),c=!0===t.credentials?re.some(!0):re.none(),s=function(){switch(t.responseType){case Jo.Blob:return"application/octet-stream";case Jo.JSON:return"application/json, text/javascript";case Jo.Text:return"text/plain";default:return""}}()+", */*; q=0.01",l=void 0!==t.headers?t.headers:{},{contentType:u,responseType:function(){switch(t.responseType){case Jo.JSON:return re.none();case Jo.Blob:return re.some("blob");case Jo.Text:return re.some("text");default:return re.none()}}(),credentials:c,accept:s,headers:l,progress:g(t.progress)?re.some(t.progress):re.none()});function m(){var t,n=p.url;(function(e,t){switch(e){case Jo.JSON:return Di(t.response).fold(function(){return ki(t)},bt);case Jo.Blob:return re.from(t.response).map(Ci).getOr(bt("no response content"));default:return Jo.Text,ki(t)}})(p.responseType,t=i).map(function(e){return{message:0===t.status?"Unknown HTTP error (possible cross-domain request)":"Could not load url "+n+": "+t.statusText,status:t.status,responseText:e}}).get(function(e){return r(ji.error(e))})}n=i,(e=d).contentType.each(function(e){return n.setRequestHeader("Content-Type",e)}),n.setRequestHeader("Accept",e.accept),e.credentials.each(function(e){return n.withCredentials=e}),e.responseType.each(function(e){return n.responseType=e}),e.progress.each(function(t){return n.upload.addEventListener("progress",function(e){return t(e.loaded,e.total)})}),se(e.headers,function(e,t){return n.setRequestHeader(t,e)}),i.onerror=m,i.onload=function(){0===i.status&&!Lt(p.url,"file:")||i.status<100||400<=i.status?m():Li(p.responseType,i).get(r)},f=p.body,re.from(f).map(function(e){return e.type===Jo.JSON?JSON.stringify(e.data):e.type===Jo.FormData||e.type===Jo.MultipartFormData?(t=e.data,n=new FormData,se(t,function(e,t){n.append(t,e)}),n):e.data;var t,n}).fold(function(){return i.send()},function(e){i.send(e)})})}function Ni(e,t){var n,r,o,i;return r=null!==(n=(t=void 0===t?{}:t).strictMode)&&void 0!==n&&n,o=(r?zi:$i).exec(e),i=z(["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],function(e,t){var n;return null!==(n=o[t])&&void 0!==n?n:""}),ue(ue({},i),{queryKey:function(e){for(var t={};;){var n=qi.exec(e);if(null===n)return t;t[n[1]]=n[2]}}(i.query)})}function _i(e){return Ke(e,Vi(e))}function Pi(e){for(var t,n=e,r="";""!==n;)Lt(n,"../")?n=Ge(n,"../"):Lt(n,"./")?n=Ge(n,"./"):Lt(n,"/./")?n="/"+Ge(n,"/./"):"/."===n?n="/":Lt(n,"/../")?(n="/"+Ge(n,"/../"),r=_i(r)):"/.."===n?(n="/",r=_i(r)):"."===n||".."===n?n="":(n=Ge(n,t=/(^\/?.*?)(\/|$)/.exec(n)[1]),r+=t);return r}function Ri(e,t,n){return{message:b(e),status:b(t),contents:b(n)}}(Yo=Jo=Jo||{}).JSON="json",Yo.Blob="blob",Yo.Text="text",Yo.FormData="formdata",Yo.MultipartFormData="multipart/form-data";var Mi=function(n){return{isValue:T,isError:w,getOr:b(n),getOrThunk:b(n),getOrDie:b(n),or:function(e){return Mi(n)},orThunk:function(e){return Mi(n)},fold:function(e,t){return t(n)},map:function(e){return Mi(e(n))},mapError:function(e){return Mi(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOptional:function(){return re.some(n)}}},Fi=function(n){return{isValue:w,isError:T,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return A(String(n))()},or:o,orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return Fi(n)},mapError:function(e){return Fi(e(n))},each:S,bind:function(e){return Fi(n)},exists:w,forall:T,toOptional:re.none}},ji={value:Mi,error:Fi,fromOption:function(e,t){return e.fold(function(){return Fi(t)},Mi)}},Ui=function(i){return ue(ue({},i),{toCached:function(){return Ui(i.toCached())},bindFuture:function(t){return Ui(i.bind(function(e){return e.fold(function(e){return bt(ji.error(e))},function(e){return t(e)})}))},bindResult:function(t){return Ui(i.map(function(e){return e.bind(t)}))},mapResult:function(t){return Ui(i.map(function(e){return e.map(t)}))},mapError:function(t){return Ui(i.map(function(e){return e.mapError(t)}))},foldResult:function(t,n){return i.map(function(e){return e.fold(t,n)})},withTimeout:function(e,o){return Ui(yt(function(t){var n=!1,r=setTimeout(function(){n=!0,t(ji.error(o()))},e);i.get(function(e){n||(clearTimeout(r),t(e))})}))}})},Hi=function(e){return Ui(yt(e))},Wi=function(e){return Ui(bt(ji.value(e)))},Bi=function(e){return Ui(bt(ji.error(e)))},zi=/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@\/]*)(?::([^:@\/]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,$i=/^(?:(?![^:@\/]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*)(?::([^:@\/]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,qi=/(?:^|&)([^&=]+)=?([^&]*)/g,Vi=function(e){return e.substring(e.lastIndexOf("/"))},Gi=["jpg","png","gif","jpeg"];function Ki(e,t){var n=it(e,t);return void 0===n||""===n?[]:n.split(" ")}function Xi(e){return void 0!==e.dom.classList}function Ji(e){return Ki(e,"class")}function Yi(e,t){var n,r,o;Xi(e)?e.dom.classList.add(t):(r=t,o=Ki(n=e,"class").concat([r]),rt(n,"class",o.join(" ")))}function Zi(e,t){var n,r,o,i;Xi(e)?e.dom.classList.remove(t):(o=t,0<(i=M(Ki(r=e,"class"),function(e){return e!==o})).length?rt(r,"class",i.join(" ")):ct(r,"class")),0===(Xi(n=e)?n.dom.classList:Ji(n)).length&&ct(n,"class")}function Qi(e,t){return Xi(e)&&e.dom.classList.contains(t)}function ea(t){return function(e){return Qi(e,t)}}var ta=function(m){function p(e,t){var n,r,o,i,a,u,c=e.split(/\s+/),s=1===c.length&&""!==c[0]?c[0]:t;return r=s,i=Ni(l,o={strictMode:!0}),a=Ni(r,o),u={},""!==a.protocol?(u.protocol=a.protocol,u.authority=a.authority,u.path=Pi(a.path),u.query=a.query):(""!==a.authority?(u.authority=a.authority,u.path=Pi(a.path),u.query=a.query):(""===a.path?(u.path=i.path,""!==a.query?u.query=a.query:u.query=i.query):(Lt(a.path,"/")?u.path=Pi(a.path):(u.path=function(e,t){if(""!==i.authority&&""===e)return"/"+t;var n=e.substring(e.lastIndexOf("/")+1);return Ke(e,n)+t}(i.path,a.path),u.path=Pi(u.path)),u.query=a.query),u.authority=i.authority),u.protocol=i.protocol),u.anchor=a.anchor,(n="")!==u.protocol&&(n+=u.protocol,n+=":"),""!==u.authority&&(n+="//",n+=u.authority),n+=u.path,""!==u.query&&(n+="?",n+=u.query),""!==u.anchor&&(n+="#",n+=u.anchor),n}function r(e,t,o){var n,r,i,a,u,c,s,l=(c=t,s=(u=e).name,O(s)&&!Et(s,".tmp")?s:function(e,t){if(O(e.type)&&Lt(e.type,"image/")){var n=e.type.substr("image/".length);return E(Gi,n)?t+"."+n:t}return t}(u,c)),f=(r=e,i=l,(a=new FormData).append("image",r,i),n=a.get("image"),{type:Jo.Blob,data:n}),d={url:m.url,body:f,responseType:Jo.Text,credentials:!0===m.credentials};Ei(ue(ue({},d),{method:"post"})).get(function(e){e.fold(function(e){o(ji.error(Ri(e.message,e.status,e.responseText)))},function(t){var n;try{var e=JSON.parse(t);if(!O(e.location))return void o(ji.error(Ri("JSON response did not contain a string location",500,t)));n=e.location}catch(e){n=t}var r=p(n,l);o(ji.value({location:r}))})})}var e,t,n,o,l=(n=0<(t=(e=m.url).lastIndexOf("/"))?e.substr(0,t):e,o=void 0===m.basePath?n:m.basePath,Et(o,"/")?o:o+"/");return{upload:function(e,t,n){Be(e.imageresult()).then(function(e){r(e,t,n)})}}};function na(e,t,n){for(var r=e.dom,o=g(n)?n:w;r.parentNode;){var r=r.parentNode,i=jt.fromDom(r);if(t(i))return re.some(i);if(o(i))break}return re.none()}function ra(e,t){return j(e.dom.childNodes,function(e){return t(jt.fromDom(e))}).map(jt.fromDom)}function oa(e,o){var i=function(e){for(var t=0;t<e.childNodes.length;t++){var n=jt.fromDom(e.childNodes[t]);if(o(n))return re.some(n);var r=i(e.childNodes[t]);if(r.isSome())return r}return re.none()};return i(e.dom)}function ia(e,t,n){return na(e,function(e){return ko(e,t)},n)}function aa(e,t){return n=t,Lo(r=void 0===e?document:e.dom)?re.none():re.from(r.querySelector(n)).map(jt.fromDom);var n,r}function ua(e,t,n){var r,o,i=ia,a=n;return ko(r=e,o=t)?re.some(r):g(a)&&a(r)?re.none():i(r,o,a)}function ca(e,t){return{image:b(e),blobInfo:b(t)}}function sa(e,t){var n=(t||document).createElement("div");return n.innerHTML=e,Ho(jt.fromDom(n))}function la(t,n){Mo(t).each(function(e){e.dom.insertBefore(n.dom,t.dom)})}function fa(e,t){jo(e).fold(function(){Mo(e).each(function(e){Ea(e,t)})},function(e){la(e,t)})}function da(t,n){var e;e=t.dom.childNodes,re.from(e[0]).map(jt.fromDom).fold(function(){Ea(t,n)},function(e){t.dom.insertBefore(n.dom,e.dom)})}function ma(e,t){la(e,t),Ea(t,e)}function pa(t,e){P(e,function(e){Ea(t,e)})}function ga(e){Zi(e,Oi.uploadInProgress())}function va(l){return{importImages:function(e){return De(W(e,function(e){return Se.cata(e,function(e,t,n){var u,r,c,s,o=ze(t);return[(u=e,r=t,c=o,s=n,yt(function(a){Dt(r).then(function(e){var t,n,r,o,i;l.editorUpload.blobCache.add({id:b(u),name:b(u),filename:(t=u,n=e.type,b(4===(i=y(tinymce)).major&&i.minor<6?t:t+"."+(r=n.toLowerCase(),Te(o={"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"},r)?o[r]:"dat"))),blob:b(e),base64:b(c.split(",")[1]),blobUri:b(s),uri:b(null)}),a(e)})}))]},b([]))}))},uploadImages:function(){l.uploadImages()},prepareImages:S,getLocalURL:function(e,t,n){return ze(t)}}}function ha(r,e){function m(){return jt.fromDom(r.getBody())}function p(t,e,n){P(e,function(e){rt(e,"data-mce-src",t.location)}),function(e,t,n){for(var r=0;r<e.undoManager.data.length;r++)e.undoManager.data[r].content=e.undoManager.data[r].content.split(t.objurl()).join(n.location)}(r,n,t)}var o,t,i,g=To(),v=Vo(),s=(o=r,t=e.url,i=Na,{instance:function(){return Oo(n)}}),h=ta(e);function a(){return lt("error.code.images.not.found")+t+lt("error.full.stop")}function u(){return lt("error.imageupload")+t+lt("error.full.stop")}function n(e){var t=e.status(),n=0===t||400<=t||t<500?a:u;i.showDialog(o,n())}function l(f,d){var e=v,t=f.blobInfo().id(),n=f.image(),r=e.isActive(t);e.register(n,t),Yi(n,Oi.uploadInProgress()),(r?re.none():re.some(t)).each(function(e){var t,n,r,o,i,a,u,c,s;function l(){var e="Internal error with blob cache";console.error(e,c),s(ka.failure(Ri(e,666,c)))}t=e,n=f.blobInfo(),r=d,o=h,i=v,a=g,u=m(),c=t,s=function(e){e.fold(r,p)},o.upload(n,c,function(e){var t,o=i.findById(u,c);P(o,(t=Oi.uploadInProgress(),function(e){Zi(e,t)})),e.fold(function(e){s(ka.failure(e))},function(t){var e=a,n=c,r=t;P(o,function(e){rt(e,"src",r.location),ct(e,Oi.blobId())}),La(e,n).fold(l,function(e){s(ka.success(t,o,e))})}),i.report(c,o,e.isValue())})})}return v.events.complete.bind(function(e){!function(e){for(var t=0;t<e.undoManager.data.length;t++){var n=e.undoManager.data[t].content,r=jt.fromTag("div");pa(r,sa(n)),P(Bo(r,"."+Oi.uploadInProgress()),ga),e.undoManager.data[t].content=r.dom.innerHTML}}(r)}),{importImages:function(){return bt([])},uploadImages:function(e){var t,n,f,r,o,i,a,u,c=s.instance();o=v,i=g,a=m(),u=o.findAll(a),P(o.inProgress()?[]:_(u,function(e){return t=i,r=it(n=e,Oi.blobId()),t.get(r).fold(function(){return ji.error(r)},function(e){return ji.value(ca(n,e))});var t,n,r}),function(e){e.fold(function(e){v.report(e,[],!1)},function(e){l(e,c)})}),t=e,n=s.instance(),f=g,r=m(),P(W(t,function(e){return Se.cata(e,function(c,s,l){return aa(r,'img[src="'+l+'"]').fold(function(){return[ji.error("Image that was just inserted could not be found: "+l)]},function(e){return[ji.value((t=f,n=c,o=l,i=e,a=ze(r=s),u=t.lookupByData(a).getOrThunk(function(){return t.add(n,r,o)}),rt(i,Oi.blobId(),u.id()),ca(i,u)))];var t,n,r,o,i,a,u})},b([]))}),function(e){e.fold(function(e){console.error(e)},function(e){l(e,n)})})},prepareImages:S,getLocalURL:function(e,t,n){return n}}}function ya(t,r,e,n){var o,i,a,u=t.selection,c=t.dom,s=t.getBody(),l=e.isText();if(e.reset(),n.clipboardData&&n.clipboardData.getData("text/html")){n.preventDefault();var f=n.clipboardData.getData("text/html"),d=f.match(/<html[\s\S]+<\/html>/i),m=null===d?f:d[0];return r(m)}if(!c.get("_mcePaste")){if(o=c.add(s,"div",{id:"_mcePaste",class:"mcePaste"},'\ufeff<br _mce_bogus="1">'),a=s!==t.getDoc().body?c.getPos(t.selection.getStart(),s).y:s.scrollTop,c.setStyles(o,{position:"absolute",left:-1e4,top:a,width:1,height:1,overflow:"hidden"}),tinymce.isIE)return(g=c.doc.body.createTextRange()).moveToElementText(o),g.execCommand("Paste"),c.remove(o),"\ufeff"===o.innerHTML?(t.execCommand("mcePasteWord"),void n.preventDefault()):(r(l?o.innerText:o.innerHTML),tinymce.dom.Event.cancel(n));var p,g,v=function(e){e.preventDefault()};c.bind(t.getDoc(),"mousedown",v),c.bind(t.getDoc(),"keydown",v),tinymce.isGecko&&((g=t.selection.getRng(!0)).startContainer!==g.endContainer||3!==g.startContainer.nodeType||1===(p=c.select("p,h1,h2,h3,h4,h5,h6,pre",o)).length&&c.remove(p.reverse(),!0)),i=t.selection.getRng(),o=o.firstChild,(g=t.getDoc().createRange()).setStart(o,0),g.setEnd(o,1),u.setRng(g),window.setTimeout(function(){var n="",e=c.select("div.mcePaste");vr(e,function(e){var t=e.firstChild;t&&"DIV"===t.nodeName&&t.style.marginTop&&t.style.backgroundColor&&c.remove(t,1),vr(c.select("div.mcePaste",e),function(e){c.remove(e,1)}),vr(c.select("span.Apple-style-span",e),function(e){c.remove(e,1)}),vr(c.select("br[_mce_bogus]",e),function(e){c.remove(e)}),n+=e.innerHTML}),vr(e,function(e){c.remove(e)}),i&&u.setRng(i),r(n),c.unbind(t.getDoc(),"mousedown",v),c.unbind(t.getDoc(),"keydown",v)},0)}}function ba(e){var t=e;return{get:function(){return t},set:function(e){t=e}}}function xa(e){function t(){return n.get().each(e)}var n=ba(re.none());return{clear:function(){t(),n.set(re.none())},isSet:function(){return n.get().isSome()},get:function(){return n.get()},set:function(e){t(),n.set(re.some(e))}}}function wa(){var t=xa(S);return ue(ue({},t),{on:function(e){return t.get().each(e)}})}function Ta(e,t,n,r,o){var i,a,u=(i=n,a=r,function(e){function t(){return r.stopPropagation()}function n(){return r.preventDefault()}var r,o;i(e)&&a((r=e,o=s(n,t),{target:jt.fromDom(function(e){if(bi()&&v(e.target)){var t=jt.fromDom(e.target);if(Pt(t)&&wi(t)&&e.composed&&e.composedPath){var n=e.composedPath();if(n)return $(n)}}return re.from(e.target)}(r).getOr(r.target)),x:r.clientX,y:r.clientY,stop:t,prevent:n,kill:o,raw:r}))});return e.dom.addEventListener(t,u,o),{unbind:I(_a,e,t,u,o)}}function Ia(e,t,n){return Ta(e,t,Pa,n,!1)}function Sa(n,r){function t(e){return n(e)?re.from(e.dom.nodeValue):re.none()}return{get:function(e){if(!n(e))throw new Error("Can only get "+r+" value of a "+r+" node");return t(e).getOr("")},getOption:t,set:function(e,t){if(!n(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom.nodeValue=t}}}function Oa(e){return Ra.get(e)}function Aa(e,t,n){var r,o=e.document.createRange(),i=o;return t.fold(function(e){i.setStartBefore(e.dom)},function(e,t){i.setStart(e.dom,t)},function(e){i.setStartAfter(e.dom)}),r=o,n.fold(function(e){r.setEndBefore(e.dom)},function(e,t){r.setEnd(e.dom,t)},function(e){r.setEndAfter(e.dom)}),o}function Ca(e,t,n,r,o){var i=e.document.createRange();return i.setStart(t.dom,n),i.setEnd(r.dom,o),i}function Da(e,t,n){return t(jt.fromDom(n.startContainer),n.startOffset,jt.fromDom(n.endContainer),n.endOffset)}var ka=ge([{failure:["error"]},{success:["result","images","blob"]}]),La=function(t,n){return t.get(n).fold(function(){return ji.error("Internal error with blob cache")},function(e){return t.remove(n),ji.value(e)})},Ea=function(e,t){e.dom.appendChild(t.dom)},Na=Object.freeze({__proto__:null,showDialog:function(e,t){var n=e.windowManager.open({title:"Error",spacing:10,padding:10,items:[{type:"container",html:t}],buttons:[{text:"Ok",onclick:function(){n.close()}}]})}}),_a=function(e,t,n,r){e.dom.removeEventListener(t,n,r)},Pa=T,Ra=Sa(et,"comment"),Ma=vi(),Fa=b({isSupported:!1,cleanDocument:function(){return pt.reject("not supported")}}),ja=Ma.deviceType.isiOS()||Ma.deviceType.isAndroid()?Fa:function(e,t,n){var r=t+"/wordimport.js"+re.from(n||"v=8.0.2").filter(function(e){return 0!==e.length}).map(function(e){return(-1===e.indexOf("?")?"?":"")+e}).getOr(""),o=e.loadScript("ephox.wimp",r);return o.catch(function(e){console.error("Unable to load word import: ",e)}),{isSupported:!0,cleanDocument:function(t,n,r){return o.then(function(e){return e.cleanDocument(t,n,r.cleanFilteredInlineElements)})}}},Ua=ge([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function Ha(e){return gu.get(e)}function Wa(e,t){var n=Ye(e);return"input"===n?hu.after(e):E(["br","img"],n)?0===t?hu.before(e):hu.after(e):hu.on(e,t)}function Ba(e,t,n,r){var o=_o(e).dom.createRange();return o.setStart(e.dom,t),o.setEnd(n.dom,r),o}function za(e){return re.from(e.getSelection())}function $a(e,t,n,r,o){var i=Ca(e,t,n,r,o);za(e).each(function(e){e.removeAllRanges(),e.addRange(i)})}function qa(c,e){return o=c,r=e.match({domRange:function(e){return{ltr:b(e),rtl:re.none}},relative:function(e,t){return{ltr:Oo(function(){return Aa(o,e,t)}),rtl:Oo(function(){return re.some(Aa(o,t,e))})}},exact:function(e,t,n,r){return{ltr:Oo(function(){return Ca(o,e,t,n,r)}),rtl:Oo(function(){return re.some(Ca(o,n,r,e,t))})}}}),((n=(t=r).ltr()).collapsed?t.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return Ua.rtl(jt.fromDom(e.endContainer),e.endOffset,jt.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Da(0,Ua.ltr,n)}):Da(0,Ua.ltr,n)).match({ltr:function(e,t,n,r){$a(c,e,t,n,r)},rtl:function(o,i,a,u){za(c).each(function(e){if(e.setBaseAndExtent)e.setBaseAndExtent(o.dom,i,a.dom,u);else if(e.extend)try{n=a,r=u,(t=e).collapse(o.dom,i),t.extend(n.dom,r)}catch(e){$a(c,a,u,o,i)}else $a(c,a,u,o,i);var t,n,r})}});var o,t,n,r}function Va(e){if(0<e.rangeCount){var t=e.getRangeAt(0),n=e.getRangeAt(e.rangeCount-1);return re.some(pu(jt.fromDom(t.startContainer),t.startOffset,jt.fromDom(n.endContainer),n.endOffset))}return re.none()}function Ga(e){if(null===e.anchorNode||null===e.focusNode)return Va(e);var t=jt.fromDom(e.anchorNode),n=jt.fromDom(e.focusNode),r=t,o=e.anchorOffset,i=n,a=e.focusOffset,u=Ba(r,o,i,a),c=No(r,i)&&o===a;return u.collapsed&&!c?re.some(pu(t,e.anchorOffset,n,e.focusOffset)):Va(e)}function Ka(e){return za(e).filter(function(e){return 0<e.rangeCount}).bind(Ga)}function Xa(e){e.dom.textContent="",P(Ho(e),function(e){xu(e)})}function Ja(e){var t,n=Ho(e);0<n.length&&(t=e,P(n,function(e){la(t,e)})),xu(e)}function Ya(e,t,n,r){return{startContainer:b(e),startOffset:b(t),endContainer:b(n),endOffset:b(r),collapsed:b(No(e,n)&&t===r)}}function Za(n){var e,r=(e=!1,{isBlocked:function(){return e},block:function(){e=!0},unblock:function(){e=!1}});return{control:r,instance:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r.isBlocked()||n.apply(void 0,e)}}}function Qa(e,t,n){return void 0===n&&(n=r),e.exists(function(e){return n(e,t)})}function eu(e,t){return e?re.some(t):re.none()}function tu(e){return void 0!==e.style&&g(e.style.getPropertyValue)}function nu(e,t,n){if(!O(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);tu(e)&&e.style.setProperty(t,n)}function ru(e,t){tu(e)&&e.style.removeProperty(t)}function ou(e,t,n){nu(e.dom,t,n)}function iu(e,t){var n=e.dom;se(t,function(e,t){nu(n,t,e)})}function au(e,t){var n=e.dom,r=window.getComputedStyle(n).getPropertyValue(t);return""!==r||Ti(e)?r:wu(n,t)}function uu(e,t){var n=e.dom,r=wu(n,t);return re.from(r).filter(function(e){return 0<e.length})}function cu(e){var t={},n=e.dom;if(tu(n))for(var r=0;r<n.style.length;r++){var o=n.style.item(r);t[o]=n.style[o]}return t}function su(e,t){ru(e.dom,t),Qa(at(e,"style").map(Nt),"")&&ct(e,"style")}function lu(e){return e.dom.innerHTML}function fu(t,e){var n,r=jt.fromTag("div");function o(e){return Qi(e,Au)}return ot(r,e),ot(r,{contenteditable:"true","aria-hidden":"true"}),iu(r,{position:"fixed",top:"0px",width:"100px",height:"100px",overflow:"hidden",opacity:"0"}),n=r,P([Ou,Au],function(e){Yi(n,e)}),{attach:function(e){Xa(r),ou(r,"left",Cu(e)),Ea(e,r)},focus:function(){ia(r,"body").each(function(e){t.toOff(e,r)})},contents:function(){var n,e=o;return jo(n=r).filter(e).each(function(e){var t=Ho(e);pa(n,t),xu(e)}),Su(n,e),P(Ho(n),function(e){var t;Pt(t=e)&&!t.dom.hasChildNodes()&&E(Iu,Ye(t))&&xu(e)}),{elements:Ho(r),html:lu(r),offscreen:r}},container:b(r),detach:function(){xu(r)}}}function du(e){var t=No(e.start,e.finish)&&e.soffset===e.foffset;return{startContainer:b(e.start),startOffset:b(e.soffset),endContainer:b(e.finish),endOffset:b(e.foffset),collapsed:b(t)}}Ua.ltr,Ua.rtl;function mu(e){return"rtl"===au(e,"direction")?"rtl":"ltr"}var pu=function(e,t,n,r){return{start:e,soffset:t,finish:n,foffset:r}},gu=Sa(Rt,"text"),vu=ge([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),hu={before:vu.before,on:vu.on,after:vu.after,cata:function(e,t,n,r){return e.fold(t,n,r)},getStart:function(e){return e.fold(o,o,o)}},yu=ge([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),bu={domRange:yu.domRange,relative:yu.relative,exact:yu.exact,exactFromRange:function(e){return yu.exact(e.start,e.soffset,e.finish,e.foffset)},getWin:function(e){return Ro(e.match({domRange:function(e){return jt.fromDom(e.startContainer)},relative:function(e,t){return hu.getStart(e)},exact:function(e,t,n,r){return e}}))},range:pu},xu=function(e){var t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},wu=function(e,t){return tu(e)?e.style.getPropertyValue(t):""},Tu=b((0,zo("ephox-sloth").resolve)("bin")),Iu=["b","i","u","sub","sup","strike"],Su=function(e,o){P(Ho(e),function(e){var t,n,r;o(e)&&(n=Ho(t=e),pa(r=jt.fromTag("div",_o(t).dom),n),la(t,r),xu(t))})},Ou=Tu(),Au=Ou+ke(""),Cu=function(e){return"rtl"===mu(e)?"100000px":"-100000px"},Du=vi(),ku=b(Du.browser.isIE()&&Du.browser.version.major<=10),Lu=ku()?function(e,t,n){t.control.block(),e.dom.execCommand("paste"),n(),t.control.unblock()}:function(e,t,n){setTimeout(n,1)},Eu={set:function(e,t){var n,r,o,i,a,u,c,s,l;n=e,r=t.startContainer(),o=t.startOffset(),i=t.endContainer(),a=t.endOffset(),qa(n,(u=i,c=a,s=Wa(r,o),l=Wa(u,c),bu.relative(s,l)))},get:function(e){return Ka(e).map(du)}};function Nu(p){return function(t){var u,r,o,c,n,i,a,s,l,f=So({after:Io(["container"])}),d=(u=Eu,r=jt.fromTag("br"),o=re.none(),c=function(e){return Ro(e).dom},{cleanup:function(){xu(r)},toOn:function(i,e){var a=c(e);o.each(function(e){var t=i.dom.childNodes.length,n=No(i,e.startContainer())&&t<e.startOffset()?t:e.startOffset(),r=No(i,e.endContainer())&&t<e.endOffset()?t:e.endOffset(),o=Ya(e.startContainer(),n,e.endContainer(),r);u.set(a,o)})},toOff:function(e,t){var n=c(t);Ea(t,r),o=u.get(n),u.set(n,Ya(r,0,r,0))}}),e=(i=t,a=fu(n=d,p),s=Za(function(){var e;l.trigger.before(),a.attach(i),a.focus(),e=_o(i),Lu(e,s,m)}),{instance:b(function(){s.instance()}),destroy:S,events:(l=So({before:Io([]),after:Io(["elements","html","container"])})).registry});function m(){n.cleanup();var e=a.contents();a.detach(),l.trigger.after(e.elements,e.html,a.container())}return e.events.after.bind(function(e){d.toOn(t,e.container),f.trigger.after(e.container)}),{run:function(){e.instance()()},events:f.registry}}}function _u(l,f){return vt.nu(function(t){function n(e){P(s,function(e){e.unbind()}),t(e.fold(function(e){return ji.error(e+'Unable to download editor stylesheets from "'+l+'"')},ji.value))}var e,r,o,i,a,u,c=(e=l,r=jt.fromDom(document),ot(o=jt.fromTag("link",r.dom),{rel:"stylesheet",type:"text/css",href:e}),i=r,a=o,u=function(){var e=i.dom.head;if(null==e)throw new Error("Head is not available yet");return jt.fromDom(e)}(),Ea(u,a),o),s=[Ia(c,"load",function(e){!function(e){try{var t=e.target.dom.sheet.cssRules;return d(t)&&0===t.length}catch(e){}}(e)?f(n):n(ji.error(""))}),Ia(c,"error",I(n,ji.error("")))]})}function Pu(e,t,n,r,o){return e.fold(t,n,r,o)}function Ru(e,n){var r={};return P(yc,function(t){n[t].or(e[t]).each(function(e){r[t]=e})}),bc(r)}function Mu(e){return{response:pc(e),bundle:bc({})}}function Fu(e){return pt.resolve(Mu(e))}function ju(e){return v(e.then)}function Uu(e,t,n,r){return{steps:e,input:t,label:n,capture:r}}function Hu(e,t){var n,r,o=Ru(e.bundle,t.bundle);return{response:(n=e.response,r=t.response,Pu(n,re.none,re.none,re.none,function(e,t,n){return Pu(r,re.none,function(e,t){return re.some(mc.incomplete(e,t,n))},re.none,re.none)}).getOr(r)),bundle:o}}function Wu(t,n,r){function e(){return t}function o(){var e=r(n,t);return ju(e)?e.then(function(e){return Hu(t,e)}):Hu(t,e)}return Pu(t.response,e,o,e,o)}function Bu(e,t){var n=Ye(e),r=t.name,o=void 0!==t.condition?t.condition:T;return Sc.matches(r,n)&&o(e)}function zu(e,t,n){var r,o,i=e.dom.getAttribute("style"),a=(o={},P(v(r=i)?r.split(";"):[],function(e){var t=e.split(":");2===t.length&&(o[Nt(t[0])]=Nt(t[1]))}),o),u={};return P(t,function(e){var t=a[e];void 0===t||n(t,e)||(u[e]=t)}),u}function $u(t){return _(be(t),function(e){return e+": "+t[e]}).join("; ")}function qu(e,t){var n,r,o,i,a,u=zu(e,Oc,t);!function(n,e,t){rt(n,"style","");var r,o,i=me(e),a=me(t);0===i&&0===a?ct(n,"style"):0===i?rt(n,"style",$u(t)):(se(e,function(e,t){ou(n,t,e)}),r=it(n,"style"),o=0<a?$u(t)+"; ":"",rt(n,"style",o+r))}(e,(r=t,i=c(o=(n=e).dom.style)?[]:o,a={},P(i,function(t){uu(n,t).each(function(e){r(e,t)||(a[t]=e)})}),a),u)}function Vu(e,t){var n,r,o,i,a,u,c,s=(n=t,r={},P(e.dom.attributes,function(e){n(e.value,e.name)||(r[e.name]=e.value)}),r);i=s,c=_((o=e).dom.attributes,function(e){return e.name}),me(i)!==c.length&&(a=o,u=i,P(c,function(e){ct(a,e)}),se(u,function(e,t){rt(a,t,e)}))}function Gu(e,t,s){e(s,function(u,c){return N(t,function(e){return t=s,n=u,r=c,o=e.name,i=void 0!==e.condition?e.condition:T,a=void 0!==e.value?e.value:Sc.all(),Sc.matches(o,r)&&Sc.matches(a,n)&&i(t);var t,n,r,o,i,a})})}function Ku(e,t,n){var r,o,i=jt.fromDom(e);switch(e.nodeType){case 1:t?r=Cc:(r=Ac,iu(i,n||{}));var a=e,u=("HTML"!==a.scopeName&&a.scopeName&&a.tagName&&a.tagName.indexOf(":")<=0?a.scopeName+":"+a.tagName:a.tagName).toLowerCase();break;case 3:r=Dc,o=e.nodeValue;break;case 8:r=kc,o=e.nodeValue;break;default:console.log("WARNING: Unsupported node type encountered: "+e.nodeType)}return{getNode:b(e),tag:function(){return u},type:function(){return r},text:function(){return o}}}function Xu(e,t,n,r){var o=r.createElement(e);return se(t,function(e,t){o.setAttribute(t,e+"")}),Ku(o,!1,n)}function Ju(e,t){return Ku(t.createElement(e),!0)}function Yu(i){function a(e){c.appendChild(e)}var u=i.createDocumentFragment(),c=u;return{dom:u,receive:function(e){var t,n,r,o;switch(e.type()){case Ac:o=e.getNode().cloneNode(!1),a(r=o),c=r;break;case Dc:n=i.createTextNode(e.text()),a(n);break;case Cc:t=c.parentNode,c=null===t?u:t;break;case kc:break;default:throw new Error("Unsupported token type: "+e.type())}},label:"SERIALISER"}}function Zu(n){return function(e){var t,r;t=e,r=wc({styles:[],attributes:[],classes:[],tags:[]},n),P(Bo(t,"*"),function(n){Gu(qu,r.styles,n),Gu(Vu,r.attributes,n),P(r.classes,function(t){P(ut(n,"class")?(Xi(n)?function(e){for(var t=e.dom.classList,n=new Array(t.length),r=0;r<t.length;r++){var o=t.item(r);null!==o&&(n[r]=o)}return n}:Ji)(n):[],function(e){Sc.matches(t.name,e)&&Zi(n,e)})})}),P(Bo(t,"*"),function(e){N(r.tags,I(Bu,e))&&xu(e)})}}function Qu(r){return function(e){var t=e,n=wc({tags:[]},r);P(Bo(t,"*"),function(e){N(n.tags,I(Bu,e))&&Ja(e)})}}function ec(r){return function(e){var t=e,n=wc({tags:[]},r);P(Bo(t,"*"),function(t){j(n.tags,I(Bu,t)).each(function(e){e.mutate(t)})})}}function tc(r){return function(e){var t=lu(e),n=function(e,t,n){for(var o=Yu(e),r=function(e,r){var o=(r=void 0===r?window.document:r).createElement("div");r.body.appendChild(o),o.style.position="absolute",o.style.left="-10000px",o.innerHTML=e;var i=o.firstChild||Lc,a=[],u=!1;return{hasNext:function(){return void 0!==i},next:function(){var e=i,t=i,n=u;return!u&&e.firstChild?(a.push(e),i=e.firstChild):u=!u&&1===e.nodeType||(e.nextSibling?(i=e.nextSibling,!1):(i=a.pop(),!0)),t===Lc||i||(r.body.removeChild(o),i=Lc),t===Lc?t:t?Ku(t,n):void 0}}}(t,e),i=function(e,t){for(var n=o,r=t.length-1;0<=r;r--)n=t[r](n,{},e);return n}(e,n);r.hasNext();){var a=r.next();i.receive(a)}return o.dom}(_o(e).dom,t,r);Xa(e),e.dom.appendChild(n)}}function nc(t,e,n){var r=jt.fromTag("div",t.dom);iu(r,{position:"fixed",left:"-100000px",top:"0px"}),Ea(function(){var e=t.dom.body;if(null==e)throw new Error("Body is not available yet");return jt.fromDom(e)}(),r),r.dom.innerHTML=e,P(n,function(e){e(r)});var o=lu(r);return xu(r),o}function rc(i,e){return function(t){function n(e){t.receive(e)}function r(e,t,n){return Ku(t,n=void 0!==n?n:e.type()===Cc,{})}var o={emit:n,emitTokens:function(e){P(e,n)},receive:function(e){i(o,e,r)},document:window.document};return e(o),o}}function oc(e,t){if(void 0===e||void 0===t)throw console.trace(),new Error("brick");e.nextFilter.set(t)}function ic(e,t,n,r){var o=n.getCurrentListType(),i=n.getCurrentLevel()==r.level()?o:null,a=r.emblems(),u=i;return j(a,function(e){return"ul"===e.tag||v(u)&&Ec(e,u,!0)}).orThunk(function(){return $(a)}).filter(function(e){return!("ol"===e.tag&&function(e){if(E(["p"],e.tag())){var t=it(jt.fromDom(e.getNode()),"class");return v(t)&&/^MsoHeading/.test(t)}return 1}(t))})}function ac(e,t,n){return{pred:e,action:t,label:b(n)}}var uc,cc,sc,lc=function(a){var u=I(Je,a);function t(n,r){var e,t,o=u(),i=(t="callback_"+(e=void 0===o.count?0:o.count),o.count=e+1,t);return o.callbacks[i]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];r||c(i),n.apply(void 0,e)},a+".callbacks."+i}Je("callbacks",u());function c(e){var t=e.substring(e.lastIndexOf(".")+1),n=u();void 0!==n.callbacks[t]&&delete n.callbacks[t]}return{ephemeral:function(e){return t(e,!1)},permanent:function(e){return t(e,!0)},unregister:c}}("ephox.henchman.features"),fc=(sc={},uc={getOrSetIndexed:function(e,t){return void 0!==sc[e]?sc[e]:(n=e,r=t(),sc[n]=r);var n,r},waitForLoad:function(){return De(de(sc,o))}},{preload:function(){cc().get(o)},addStylesheet:function(e,t){return uc.getOrSetIndexed(e,function(){return _u(e,t)})},addScript:function(e,t){return uc.getOrSetIndexed(e,function(){return i=e,vt.nu(function(t){function e(){r.unbind(),o.unbind()}var n=jt.fromTag("script");rt(n,"src",i),rt(n,"type","text/javascript"),rt(n,"async","async"),rt(n,"data-main",lc.ephemeral(function(e){t(ji.value(e))}));var r=Ia(n,"error",function(){e(),t(ji.error("Error loading external script tag "+i))}),o=Ia(n,"load",e);Ea(jt.fromDom(document.head),n)}).map(t);var i})},waitForLoad:cc=function(){return uc.waitForLoad()}}),dc={loadScript:function(e,r){return new pt(function(t,n){fc.addScript(r,o).get(function(e){e.fold(n,t)})})}},mc=ge([{error:["message"]},{paste:["elements","correlated"]},{cancel:[]},{incomplete:["elements","correlated","message"]}]),pc=mc.error,gc=mc.paste,vc=mc.cancel,hc=mc.incomplete,yc=["officeStyles","htmlStyles","gdocsStyles","isWord","isGoogleDocs","proxyBin","isInternal","backgroundAssets"],bc=function(t){return z(yc,function(e){return re.from(t[e])})},xc={response:vc(),bundle:bc({})},wc=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o,i=e[r];for(o in i)Te(i,o)&&(n[o]=(n[o],i[o]))}return n},Tc=ge([{starts:["value","f"]},{pattern:["regex","f"]},{contains:["value","f"]},{exact:["value","f"]},{all:[]},{not:["stringMatch"]}]),Ic=function(e,n){return e.fold(function(e,t){return 0===t(n).indexOf(t(e))},function(e,t){return e.test(t(n))},function(e,t){return 0<=t(n).indexOf(t(e))},function(e,t){return t(n)===t(e)},T,function(e){return!Ic(e,n)})},Sc={starts:Tc.starts,pattern:Tc.pattern,contains:Tc.contains,exact:Tc.exact,all:Tc.all,not:Tc.not,cata:function(e,t,n,r,o,i,a){return e.fold(t,n,r,o,i,a)},matches:Ic,caseSensitive:o,caseInsensitive:function(e){return e.toLowerCase()}},Oc=["mso-list"],Ac="startElement",Cc="endElement",Dc="text",kc="comment",Lc=Ju("html",window.document),Ec=function(e,t,n){return void 0===n&&(n=!1),e===t||v(e)&&v(t)&&e.tag===t.tag&&e.type===t.type&&(n||e.variant===t.variant)};function Nc(e,r,o){function t(e,t,n){j(r,function(e){return e.pred(t,n)}).fold(b(o),function(e){return e.action})(e,t,n)}return t.toString=function(){return"Handlers for "+e},t}function _c(e,t){return{state:b(e),result:b(t)}}function Pc(e,t){return{state:b(e),value:b(t)}}function Rc(e,t,n,r){return{level:b(e),type:b(t),types:b(n),items:b(r)}}function Mc(e){var t=e.items().slice(0);if(0<t.length&&"p"!==t[t.length-1]){var n=t[t.length-1];return t[t.length-1]="p",Pc(Rc(e.level(),e.type(),e.types(),t),re.some(n))}return Pc(e,re.none())}function Fc(e,t,n){for(var r=[],o=e;t(o);)var i=n(o),o=i.state(),r=r.concat(i.result());return _c(o,r)}function jc(e,t,n){var r=t.start&&1<t.start?{start:t.start}:{},o=e.level()+1,i=t,a=e.types().concat([t]),u=[I(Xu,t.tag,r,n)];return _c(Rc(o,i,a,e.items()),u)}function Uc(e){var t=e.types().slice(0),n=[I(Ju,t.pop().tag)];return _c(Rc(e.level()-1,t[t.length-1],t,e.items()),n)}function Hc(e,o,t){var n,r,i,a,u,c,s,l,f=o?v(l=au(jt.fromDom(o.getNode()),"margin-left"))&&"0px"!==l?{"margin-left":l}:{}:{"list-style-type":"none"},d=e.type()&&!Ec(e.type(),t)?(n=t,_c((i=jc((r=Uc(e)).state(),n,n.type?{"list-style-type":n.type}:{})).state(),r.result().concat(i.result()))):_c(e,[]),m=[I(Xu,"li",{},f)],p=(a=d.state(),u=o&&o.tag(),c=a.items().slice(0),(s=void 0!==u&&"p"!==u?re.some(u):re.none()).fold(function(){c.push("p")},function(e){c.push(e)}),Pc(Rc(a.level(),a.type(),a.types(),c),s)),g=p.value().map(function(e){var t=o,n=t.getNode(),r=T;return qu(jt.fromDom(n),r),[b(t)]}).getOr([]);return _c(p.state(),d.result().concat(m).concat(g))}function Wc(e){var t=I(Ju,"li"),n=Mc(e),r=n.value().fold(function(){return[t]},function(e){return[I(Ju,e),t]});return _c(n.state(),r)}function Bc(e){if(0===e.length)throw new Error("Compose must have at least one element in the list");var t=e[e.length-1],n=W(e,function(e){return e.result()});return _c(t.state(),n)}function zc(e){var t=Wc(e),n=Uc(t.state());return Bc([t,n])}function $c(e,t){return n=t,Fc(e,function(e){return e.level()>n},zc);var n}function qc(e,t,n,r){var o,i,a,u,c,s,l,f,d,m,p,g,v,h,y,b,x,w=e.level()>t?$c(e,t):_c(e,[]),T=w.state().level()===t?(d=r,m=n,g=Hc((p=0<(f=w.state()).level()?Wc(f):_c(f,[])).state(),m,d),Bc([p,g])):(o=w.state(),i=r,u=n,s=(c=1<(a=t)?Mc(o):Pc(o,re.none())).value().map(function(e){return[I(Ju,e)]}).getOr([]),v=c.state(),h=i,b=u,x=y=a,_c((l=Fc(v,function(e){return e.level()<x},function(e){return n=h,r=y,o=b,i=(t=e).level()===r-1&&n.type?{"list-style-type":n.type}:{},u=Hc((a=jc(t,n,i)).state(),a.state().level()==r?o:void 0,n),Bc([a,u]);var t,n,r,o,i,a,u})).state(),s.concat(l.result())));return Bc([w,T])}function Vc(e){for(var t=[];null!==e.nextNode();)t.push(jt.fromDom(e.currentNode));return t}function Gc(e){return e.dom.textContent}function Kc(e){var t=zu(e,["mso-list"],w)["mso-list"],n=v(t)&&/ level([0-9]+)/.exec(t);return n&&n[1]?re.some(parseInt(n[1],10)):re.none()}function Xc(e,t){var u,n,r,o,i,a=Gc(e).trim(),c=(r=As[u=a]?[As[u]]:[],o=(n=t)&&Os[u]?[Os[u]]:n?[{tag:"ul",variant:u}]:[],i=W(Ss,function(e){return e.regex.test(u)?[wc(e.type,(o=(r=u).split("."),i=function(){if(0===o.length)return r;var e=o[o.length-1];return 0===e.length&&1<o.length?o[o.length-2]:e}(),a=parseInt(i,10),isNaN(a)?{}:{start:a}),{variant:(t=e.type,n=u,f(t.variant)?"("===n.charAt(0)?"()":")"===n.charAt(n.length-1)?")":".":t.variant)})]:[];var t,n,r,o,i,a}),_(r.concat(o).concat(i),function(e){return void 0!==e.variant?e:wc(e,{variant:u})}));return 0<c.length?re.some(c):re.none()}function Jc(e){return ra(e,et).bind(jo).filter(tt("span"))}function Yc(e){return oa(e,function(e){return(Pt(e)?zu(e,["mso-list"],w):{})["mso-list"]})}function Zc(e){P(function(e){var t=re.none().fold(Is,function(t){return function(e){return t(e.nodeValue)}});t.acceptNode=t;var n,r,o=document.createTreeWalker(e.dom,NodeFilter.SHOW_COMMENT,t,!1);return n=o,((r=vi().browser).isIE()||r.isEdge()?function(e){try{return Vc(e)}catch(e){return[]}}:Vc)(n)}(e),xu)}function Qc(e,t,n,r){var o,i;i=n,rt(o=e,"data-list-level",t),rt(o,"data-list-emblems",JSON.stringify(i)),Zc(e),P(r,xu),ct(e,"style"),ct(e,"class")}function es(e){return Kc(r=e).bind(function(n){return ra(r,Cs).bind(function(t){return Xc(t,!0).map(function(e){return{mutate:function(){Qc(r,n,e,[t])}}})})}).orThunk(function(){return Kc(r=e).bind(function(n){return Jc(r).bind(function(t){return Xc(t,Cs(t)).map(function(e){return{mutate:function(){Qc(r,n,e,[t])}}})})});var r}).orThunk(function(){return Kc(r=e).bind(function(n){return Jc(r).bind(function(t){return Xc(t,Cs(t)).map(function(e){return{mutate:function(){Qc(r,n,e,[t])}}})})});var r}).orThunk(function(){return"p"!==Ye(r=e)?re.none():Kc(r).bind(function(n){return Yc(r).bind(function(t){return Xc(t,!1).map(function(e){return{mutate:function(){Qc(r,n,e,[Mo(t).getOr(t)])}}})})});var r}).orThunk(function(){return"p"!==Ye(r=e)?re.none():Yc(r).bind(function(e){var n=Mo(e).getOr(e);return Xc(e,Cs(n)).bind(function(t){return uu(r,"margin-left").bind(function(e){var t=parseInt(e,10);return isNaN(t)?re.none():re.some(Math.max(1,Math.ceil(t/18)))}).map(function(e){return{mutate:function(){Qc(r,e,t,[n])}}})})});var r});var r}function ts(e){return(et(t=e)?(r="v:shape",re.from(t.dom.nodeValue).bind(function(e){var t=e.indexOf("]>"),n=function(t){try{return(new DOMParser).parseFromString(t,"text/html").body}catch(e){var n=document.implementation.createHTMLDocument("").body;return n.innerHTML=t,n}}("<div>"+e.slice(t+"]>".length,e.lastIndexOf("<!["))+"</div>");return oa(jt.fromDom(n),function(e){return Ye(e)===r})})):re.none()).map(function(e){var t,n,r,o=it(e,"o:spid"),i=void 0===o?at(e,"id").getOr(""):o,a=jt.fromTag("img");return Yi(a,"rtf-data-image"),rt(a,"data-image-id",i.substr("_x0000_".length)),rt(a,"data-image-type","code"),t=a,n={width:uu(e,"width"),height:uu(e,"height")},r=t.dom,se(n,function(e,t){e.fold(function(){ru(r,t)},function(e){nu(r,t,e)})}),a});var r,t}function ns(e){if(tt("img")(e)){var t=it(e,"src");if(null!=t&&Lt(t,"file://")){var n=jt.fromDom(e.dom.cloneNode(!1)),r=t.split(/[\/\\]/);return rt(n,"data-image-id",r[r.length-1]),ct(n,"src"),rt(n,"data-image-type","local"),Yi(n,"rtf-data-image"),re.some(n)}return re.none()}return re.none()}var rs,os,is,as,us,cs,ss,ls,fs,ds,ms=$c,ps=["disc","circle","square"],gs={getCurrentListType:function(){return vs().getCurrentListType()},getCurrentLevel:function(){return vs().getCurrentLevel()},closeAllLists:function(){return vs().closeAllLists()},openItem:function(e,t,n){return vs().openItem(e,t,n)}},vs=function(){return{getCurrentListType:b({}),getCurrentLevel:b(1),closeAllLists:S,openItem:o}},hs={inside:function(){return bs},outside:function(){return xs}},ys=(rs=!1,{check:function(e){return!(!rs||e.type()!==Dc)||(e.type()===Ac&&"style"===e.tag()?rs=!0:e.type()===Cc&&"style"===e.tag()&&!(rs=!1))}}),bs=(os=hs,Nc("Inside.List.Item",[ac(function(e,t){var n=e.originalToken.get();return t.type()===Cc&&null!==n&&t.tag()===n.tag()},function(e,t){oc(t,os.outside())},"Closing open tag")],function(e,t,n){e.emit(n)})),xs=Nc("Outside.List.Item",[ac(function(e,t){return ut(jt.fromDom(t.getNode()),"data-list-level")},(ds=is=hs,function(e,t,n){var r,o,i,a,u=n,c=(r=jt.fromDom(u.getNode()),o=parseInt(it(r,"data-list-level"),10),a=v(i=it(r,"data-list-emblems"))?JSON.parse(i):[],ct(r,"data-list-level"),ct(r,"data-list-emblems"),{level:b(o),emblems:b(a)});t.originalToken.set(u);var s,l,f,d=(s=u,l=c,ic((f=t).listType.get(),s,f.emitter,l).each(f.listType.set),{level:b(l.level()),token:b(f.originalToken.get()),type:b(f.listType.get())});t.emitter.openItem(d.level(),d.token(),d.type()),oc(t,ds.inside())}),"Data List ****"),ac(function(e,t){return t.type()===Dc&&(n=t).type()===Dc&&/^[\s\u00A0]*$/.test(n.text());var n},function(e,t,n){e.emit(n)},"Whitespace")],function(e,t,n){t.emitter.closeAllLists(),e.emit(n),oc(t,is.outside())}),ws=(us=ba(as=xs),cs=ba(null),ss=ba(null),{reset:function(e){function i(e){P(e.result(),function(e){var t=e(r);n.emit(t)})}var n,r,a;us.set(as),cs.set(null),ss.set(null),vs=b((r=(n=e).document,a=Rc(0,void 0,[],[]),{closeAllLists:function(){var e=ms(a,0);a=e.state(),i(e)},openItem:function(e,t,n){var r,o;n&&(r="ul"===n.tag&&ps[e-1]===n.type?{tag:"ul"}:n,o=qc(a,e,t,r),a=o.state(),i(o))},getCurrentListType:function(){return a.type()},getCurrentLevel:function(){return a.level()}}))},nextFilter:us,originalToken:cs,listType:ss,emitter:gs}),Ts=rc(function(e,t,n){var r,o,i;ys.check(t)||(r=e,i=t,(o=ws).nextFilter.get()(r,o,i))},ws.reset),Is=b(T),Ss=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"ol",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"ol",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"ol",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"ol",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"ol"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"ol",variant:"outline"}},{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"ol",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"ol",type:"upper-alpha"}}],Os={"\u2022":{tag:"ul",type:"disc"},"\xb7":{tag:"ul",type:"disc"},"\xa7":{tag:"ul",type:"square"}},As={o:{tag:"ul",type:"circle"},"-":{tag:"ul",type:"disc"},"\u25cf":{tag:"ul",type:"disc"},"\ufffd":{tag:"ul",type:"circle"}},Cs=function(e){return Pt(e)&&uu(e,"font-family").exists(function(e){return E(["wingdings","symbol"],e.toLowerCase())})},Ds=ec({tags:[{name:Sc.pattern(/^(p|h\d+)$/,Sc.caseInsensitive),mutate:function(e){es(e).each(function(e){e.mutate()})}}]}),ks=Ts;function Ls(o){return function(r){at(r,o.attrName).each(function(e){var t,n=v(o.styleName)?o.styleName:o.attrName;uu(r,n).isNone()&&(t=o.mapValue(e),ou(r,n,t)),ct(r,o.attrName)})}}function Es(e){var t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()}function Ns(e){return{value:Es(e.red)+Es(e.green)+Es(e.blue)}}function _s(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}}function Ps(e,t,n,r){return _s(parseInt(e,10),parseInt(t,10),parseInt(n,10),parseFloat(r))}function Rs(r){return"currentcolor"===r||"transparent"===r?r:"#"+(e=Ge(r,"#").toUpperCase(),(Gs.test(t=e)||Ks.test(t)?re.some({value:Ge(e,"#").toUpperCase()}):re.none()).orThunk(function(){return function(e){if("transparent"===e)return re.some(_s(0,0,0,0));var t=Xs.exec(e);if(null!==t)return re.some(Ps(t[1],t[2],t[3],"1"));var n=Js.exec(e);return null!==n?re.some(Ps(n[1],n[2],n[3],n[4])):re.none()}(r).map(Ns)}).getOrThunk(function(){var e=document.createElement("canvas");e.height=1,e.width=1;var t=e.getContext("2d");t.clearRect(0,0,e.width,e.height),t.fillStyle="#FFFFFF",t.fillStyle=r,t.fillRect(0,0,1,1);var n=t.getImageData(0,0,1,1).data;return Ns(_s(n[0],n[1],n[2],n[3]))}).value);var e,t}function Ms(e,t){if(f(e))return"";switch(t){case"color":return Rs(e);case"font-family":return e.replace(/['"]/g,"");case"font-weight":return function(e){switch(e){case"bold":return"700";case"normal":return"400";default:return e}}(e);default:return Et(t,"-color")?Rs(e):e.replace(/^0(pt|px|pc|in|cm|mm|Q|cap|ch|ic|em|ex|lh|rlh|rem|vw|vh|vb|vi|vmax|vmin|%)$/,"0")}}function Fs(n){return ec({tags:[{name:Sc.exact(n.matchTag,Sc.caseInsensitive),mutate:function(t){pe(cu(t),n.key).exists(function(e){return E(n.values,e)})&&(ma(t,jt.fromTag(n.newTag)),su(t,n.key),l(n.removeExtra)&&P(n.removeExtra,function(e){return su(t,e),0}))}}]})}function js(e,t){return oa(e,t).isSome()}function Us(e,t){var n=jt.fromTag(e);la(t,n),P(t.dom.attributes,function(e){n.dom.setAttribute(e.name,e.value)});var r=Ho(t);return pa(n,r),xu(t),n}function Hs(e){var i=Us("span",e),a={"font-size":{1:"8pt",2:"10pt",3:"12pt",4:"14pt",5:"18pt",6:"24pt",7:"36pt"}};se({face:"font-family",size:"font-size",color:"color"},function(r,o){at(i,o).each(function(e){var t=a[r],n=void 0!==t&&void 0!==t[e]?t[e]:e;ou(i,r,n),ct(i,o)})})}function Ws(e){var t=Us("span",e);Yi(t,"ephox-limbo-transform"),ou(t,"text-decoration","underline")}(fs=ls=ls||{})[fs.Word=0]="Word",fs[fs.GoogleDocs=1]="GoogleDocs",fs[fs.Html=2]="Html";function Bs(e){var t=Ye(e);return"td"===t||"tr"===t||"col"===t||"th"===t}function zs(e){var t=e.dom.attributes;return null==t||0===t.length||1===t.length&&"style"===t[0].name}var $s,qs,Vs,Gs=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Ks=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Xs=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,Js=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,Ys=["background-repeat-x","background-repeat-y"],Zs=ec({tags:[{name:Sc.pattern(/^(p|div)$/,Sc.caseInsensitive),mutate:function(t){var e="ltr"===mu(t),n=e?"margin-left":"margin-right",r=e?"padding-left":"padding-right";uu(t,n).each(function(){var e=au(t,n);ou(t,r,e),su(t,n)})}}]}),Qs=Qu({tags:[{name:Sc.exact("b",Sc.caseInsensitive),condition:function(e){return at(e,"id").exists(function(e){return Lt(e,"docs-internal-guid")})}}]}),el=Zu({attributes:[{name:Sc.exact("id",Sc.caseInsensitive),value:Sc.starts("docs-internal-guid",Sc.caseInsensitive)}]}),tl=[ec({tags:[{name:Sc.exact("col",Sc.caseInsensitive),mutate:Ls({attrName:"width",mapValue:function(e){return e.replace(/^(\d+)$/,"$1px")}})}]})],nl=[Fs({matchTag:"span",key:"font-weight",values:["700","bold"],newTag:"strong"}),Fs({matchTag:"span",key:"font-style",values:["italic"],newTag:"em"}),Fs({matchTag:"span",key:"vertical-align",values:["sub"],newTag:"sub",removeExtra:["font-size"]}),Fs({matchTag:"span",key:"vertical-align",values:["super"],newTag:"sup",removeExtra:["font-size"]})],rl=["p","div","article","aside","details","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"],ol=Zu({styles:[{name:Sc.exact("background-color",Sc.caseInsensitive),value:Sc.exact("transparent",Sc.caseInsensitive)},{name:Sc.exact("white-space",Sc.caseInsensitive),value:Sc.starts("pre",Sc.caseInsensitive)},{name:Sc.pattern(/^overflow(-[xy])?$/,Sc.caseInsensitive),condition:function(e){return Bs(e)&&Qa(uu(e,"overflow"),"hidden")}},{name:Sc.exact("overflow-wrap",Sc.caseInsensitive),condition:Bs},{name:Sc.exact("table-layout",Sc.caseInsensitive),value:Sc.exact("fixed",Sc.caseInsensitive),condition:tt("table")},{name:Sc.exact("line-height",Sc.caseInsensitive),value:Sc.exact("1.38",Sc.caseInsensitive)},{name:Sc.exact("vertical-align",Sc.caseInsensitive),value:Sc.exact("baseline",Sc.caseInsensitive)},{name:Sc.exact("font-style",Sc.caseInsensitive),value:Sc.exact("normal",Sc.caseInsensitive)},{name:Sc.exact("font-variant",Sc.caseInsensitive),value:Sc.exact("normal",Sc.caseInsensitive)},{name:Sc.exact("background-color",Sc.caseInsensitive),value:Sc.exact("transparent",Sc.caseInsensitive)},{name:Sc.starts("padding",Sc.caseInsensitive),condition:Bs},{name:Sc.pattern(/^text-decoration(-(line|thickness|style|color))?$/,Sc.caseInsensitive),condition:function(e){return!tt("a")(e)&&Qa(uu(e,"text-decoration"),"none")}}],attributes:[{name:Sc.exact("aria-level",Sc.caseInsensitive),condition:tt("li")},{name:Sc.exact("dir",Sc.caseInsensitive),value:Sc.exact("ltr",Sc.caseInsensitive),condition:function(e){return E(rl,Ye(e))}},{name:Sc.exact("role",Sc.caseInsensitive),value:Sc.exact("presentation",Sc.caseInsensitive),condition:function(e){return tt("p")(e)&&Mo(e).exists(tt("li"))}}]}),il=Zu({styles:[{name:Sc.exact("text-align",Sc.caseInsensitive),value:Sc.exact("right",Sc.caseInsensitive),condition:function(e){return"rtl"===mu(e)}}]}),al=ec({tags:[{name:Sc.exact("p",Sc.caseInsensitive),condition:function(t){function e(e){return uu(t,e).map(function(e){return parseInt(e,10)}).filter(function(e){return!isNaN(e)}).getOr(0)}var n=mu(t);return e("text-indent")+e("rtl"===n?"padding-right":"padding-left")===0},mutate:function(e){var t=mu(e);su(e,"text-indent"),su(e,"rtl"===t?"padding-right":"padding-left")}}]}),ul=tt("li"),cl=function(e){return Fo(e).bind(function(e){return Rt(e)&&0===Ha(e).trim().length?cl(e):ul(e)?re.some(e):re.none()})},sl=Zu({tags:[{name:Sc.exact("script",Sc.caseInsensitive)},{name:Sc.exact("link",Sc.caseInsensitive)},{name:Sc.exact("style",Sc.caseInsensitive),condition:function(e){return 0===lu(e).length}}],attributes:[{name:Sc.starts("on",Sc.caseInsensitive)},{name:Sc.exact('"',Sc.caseInsensitive)},{name:Sc.exact("lang",Sc.caseInsensitive)},{name:Sc.exact("language",Sc.caseInsensitive)}],styles:[{name:Sc.all(),value:Sc.pattern(/OLE_LINK/i,Sc.caseInsensitive)}]}),ll=Zu({tags:[{name:Sc.exact("meta",Sc.caseInsensitive)}]}),fl=Zu({tags:[{name:Sc.exact("style",Sc.caseInsensitive)}]}),dl=Zu({styles:[{name:Sc.not(Sc.pattern(/^(width|height|list-style-type)$/,Sc.caseInsensitive)),condition:function(e){return!Qi(e,"ephox-limbo-transform")}},{name:Sc.pattern(/^(width|height)$/,Sc.caseInsensitive),condition:function(e){return"img"!==Ye(e)&&!("table"===Ye(t=e)||Bs(t));var t}}]}),ml=Zu({classes:[{name:Sc.not(Sc.exact("rtf-data-image",Sc.caseInsensitive))}]}),pl=Zu({styles:[{name:Sc.pattern(/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/,Sc.caseInsensitive)}]}),gl=Zu({classes:[{name:Sc.pattern(/mso/i,Sc.caseInsensitive)}]}),vl=Qu({tags:[{name:Sc.exact("img",Sc.caseInsensitive),condition:function(e){var t=it(e,"src");return O(t)&&/^file:/.test(t)}}]}),hl=Qu({tags:[{name:Sc.exact("a",Sc.caseInsensitive),condition:zs}]}),yl=Zu({attributes:[{name:Sc.exact("style",Sc.caseInsensitive),value:Sc.exact("",Sc.caseInsensitive)}]}),bl=Zu({attributes:[{name:Sc.exact("class",Sc.caseInsensitive),value:Sc.exact("",Sc.caseInsensitive)}]}),xl=Qu({tags:[{name:Sc.pattern(/^(font|em|strong|samp|acronym|cite|code|dfn|kbd|tt|b|i|u|s|sub|sup|ins|del|var|span)$/,Sc.caseInsensitive),condition:($s=function(e){return!zs(e)||(n=null!=(t=e.dom.attributes)&&0<t.length,("span"!==Ye(e)||n)&&js(e,function(e){var t=!zs(e),n=!E(["font","em","strong","samp","acronym","cite","code","dfn","kbd","tt","b","i","u","s","sub","sup","ins","del","var","span"],Ye(e));return Rt(e)||t||n}));var t,n},function(e){return!$s(e)})}]}),wl=ec({tags:[{name:Sc.exact("p",Sc.caseInsensitive),mutate:function(e){0===lu(e).length&&Ea(e,jt.fromTag("br"))}}]}),Tl=ec({tags:[{name:Sc.pattern(/ol|ul/,Sc.caseInsensitive),mutate:function(t){Mo(t).each(function(e){E(["ol","ul"],Ye(e))&&cl(t).fold(function(){var e=jt.fromTag("li");ou(e,"list-style-type","none"),ma(t,e)},function(e){Ea(e,t)})})}}]}),Il=Zu({classes:[{name:Sc.exact("ephox-limbo-transform",Sc.caseInsensitive)}]}),Sl=Zu({tags:[{name:Sc.exact("br",Sc.caseInsensitive),condition:ea("Apple-interchange-newline")}]}),Ol=Zu({styles:[{name:Sc.pattern(/^-/,Sc.caseInsensitive)},{name:Sc.all(),value:Sc.exact("initial",Sc.caseInsensitive)},{name:Sc.exact("background-color",Sc.caseInsensitive),value:Sc.exact("transparent",Sc.caseInsensitive)},{name:Sc.exact("font-style",Sc.caseInsensitive),value:Sc.exact("normal",Sc.caseInsensitive)},{name:Sc.pattern(/font-variant.*/,Sc.caseInsensitive)},{name:Sc.exact("letter-spacing",Sc.caseInsensitive)},{name:Sc.exact("font-weight",Sc.caseInsensitive),value:Sc.pattern(/400|normal/,Sc.caseInsensitive)},{name:Sc.exact("orphans",Sc.caseInsensitive)},{name:Sc.exact("text-decoration",Sc.caseInsensitive),value:Sc.exact("none",Sc.caseInsensitive)},{name:Sc.exact("text-size-adjust",Sc.caseInsensitive)},{name:Sc.exact("text-indent",Sc.caseInsensitive),value:Sc.exact("0px",Sc.caseInsensitive)},{name:Sc.exact("text-transform",Sc.caseInsensitive),value:Sc.exact("none",Sc.caseInsensitive)},{name:Sc.exact("white-space",Sc.caseInsensitive),value:Sc.exact("normal",Sc.caseInsensitive)},{name:Sc.exact("widows",Sc.caseInsensitive)},{name:Sc.exact("word-spacing",Sc.caseInsensitive),value:Sc.exact("0px",Sc.caseInsensitive)},{name:Sc.exact("text-align",Sc.caseInsensitive),value:Sc.pattern(/start|end/,Sc.caseInsensitive)},{name:Sc.exact("font-weight",Sc.caseInsensitive),value:Sc.pattern(/700|bold/,Sc.caseInsensitive),condition:function(e){return/^h\d$/.test(Ye(e))}}]}),Al=(qs=Cl(Fo,Et),Vs=Cl(jo,Lt),ec({tags:[{name:Sc.exact("span",Sc.caseInsensitive),condition:ea("Apple-converted-space"),mutate:function(e){"\xa0"===Gc(e)&&(qs(e)||Vs(e)?Ja(e):(la(e,jt.fromText(" ")),xu(e)))}}]}));function Cl(e,n){return function(t){return e(t).filter(function(e){return Rt(t)&&n(Gc(e)||""," ")}).isSome()}}var Dl,kl=(Dl=/^file:\/\/\/[^#]+(#[^#]+)$/,ec({tags:[{name:Sc.exact("a",Sc.caseInsensitive),condition:function(e){var t=it(e,"href");return!!t&&Dl.test(t)},mutate:function(t){at(t,"href").each(function(e){rt(t,"href",e.replace(Dl,"$1"))})}}]})),Ll=Zu({attributes:[{name:Sc.exact("href",Sc.caseInsensitive),value:Sc.starts("file:///",Sc.caseInsensitive)}]}),El=ec({tags:[Nl("a","data-ephox-href","href"),Nl("img","data-ephox-src","src")]});function Nl(e,n,r){return{name:Sc.exact(e,Sc.caseInsensitive),condition:function(e){return ut(e,n)},mutate:function(t){at(t,n).each(function(e){rt(t,r,e),ct(t,n)})}}}function _l(a){var u=["table","thead","tbody","tfoot","th","tr","td","ul","ol","li"],e=Ii(a,et),t=j(e,function(e){return Xe(Oa(e),"StartFragment")}),n=j(e,function(e){return Xe(Oa(e),"EndFragment")});t.each(function(i){n.each(function(e){for(var t,n=i,r=[],o=(t=Ba(i,0,e,0),jt.fromDom(t.commonAncestorContainer));void 0!==o&&!No(o,a);)E(u,Ye(o))?n=o:r.push(o),o=Mo(o).getOrUndefined();P(r,Ja),P(Uo(n),xu)}),xu(i)}),n.each(xu)}function Pl(e,t){return gu.getOption(e).exists(function(e){return 0===t(e).length})}function Rl(e){return P(Ho(e),function(e){Pl(e,Nt)&&xu(e)})}var Ml=ec({tags:[{name:Sc.pattern(/^(img|table)$/,Sc.caseInsensitive),mutate:function(e){uu(e,"margin-left").exists(function(e){return Lt(e,"-")})&&su(e,"margin-left"),Lt(au(e,"margin-left"),"-")&&(ou(e,"margin-top",au(e,"margin-top")),ou(e,"margin-bottom",au(e,"margin-bottom")),ou(e,"margin-right",au(e,"margin-right")),su(e,"margin"))}}]}),Fl=ec({tags:[{name:Sc.exact("p",Sc.caseInsensitive),mutate:Ls({attrName:"align",styleName:"text-align",mapValue:o})}]}),jl=Zu({tags:[{name:Sc.exact("font",Sc.caseInsensitive),condition:function(e){function t(e){return e.replace(/[ \r\n\uFEFF]+/g,"")}var n=Ho(e);return 0===n.length||B(n,function(e){return Pl(e,t)})}}]}),Ul=ec({tags:[{name:Sc.exact("ol",Sc.caseInsensitive),mutate:Rl},{name:Sc.exact("ul",Sc.caseInsensitive),mutate:Rl}]}),Hl=Qu({tags:[{name:Sc.pattern(/^([OVWXP]|U[0-9]+|ST[0-9]+):/i,Sc.caseInsensitive)}]}),Wl=[tc([ks])],Bl=Zu({attributes:[{name:Sc.exact("height",Sc.caseInsensitive),condition:tt("table")}]}),zl=Zu({attributes:[{name:Sc.pattern(/^(width|height)$/,Sc.caseInsensitive),condition:Bs}]}),$l=ec({tags:[{name:Sc.exact("table",Sc.caseInsensitive),mutate:Ls({attrName:"width",mapValue:function(e){return e.replace(/^(\d+)$/,"$1px")}})}]}),ql=Zu({styles:[{name:Sc.exact("height",Sc.caseInsensitive),condition:tt("td")},{name:Sc.exact("width",Sc.caseInsensitive),condition:tt("tr")},{name:Sc.exact("height",Sc.caseInsensitive),condition:tt("col")}]}),Vl=Zu({attributes:[{name:Sc.pattern(/^v:/,Sc.caseInsensitive)},{name:Sc.exact("href",Sc.caseInsensitive),value:Sc.contains("#_toc",Sc.caseInsensitive)},{name:Sc.exact("href",Sc.caseInsensitive),value:Sc.contains("#_mso",Sc.caseInsensitive)},{name:Sc.pattern(/^xmlns(:|$)/,Sc.caseInsensitive)},{name:Sc.exact("type",Sc.caseInsensitive),condition:function(e){return"ol"===Ye(e)||"ul"===Ye(e)}}]}),Gl=Qu({tags:[{name:Sc.exact("p",Sc.caseInsensitive),condition:function(e){return Mo(e).exists(function(e){return"li"===Ye(e)&&1===Ho(e).length})}}]});function Kl(e){return e.browser.isIE()&&11<=e.browser.version.major}function Xl(e,t){return e.type===ls.GoogleDocs?ce(ce(ce(ce([ol],nl),tl),[(n=t,function(e){var r=[],o={border:n.browser.isFirefox()?"medium none":"none","text-decoration":"none"},i=function(t,e){var n;f(e)||(n=jt.fromTag(Ye(t)),Ea(e,n),r.push({me:t,fake:n})),P(M(Ho(t),Pt),function(e){return i(e,t)})};i(e),P(_(r,function(e){var u=e.fake,c=e.me,t=fe(cu(c),function(e,t){var n,r,o,i=(n=c,(E(Ys,t)?uu(n,"background-repeat"):re.none()).getOr(e)),a=(r=u,E(Ys,o=t)?au(r,"background-repeat"):au(r,o));return Ms(i,t)===Ms(a,t)}),n=fe(o,function(e,t){return Qa(uu(c,t),e)});return{fake:u,me:c,toRemove:t,toPreserve:n}}),function(e){var n=e.me,t=e.toRemove,r=e.toPreserve,o=e.fake;se(t,function(e,t){su(n,t)}),se(r,function(e,t){ou(n,t,e)}),xu(o)})}),il,al]),e.type!==ls.GoogleDocs||e.indentUseMargin?[]:[Zs]):[];var n}function Jl(n,r){var e,t,o,i,a,u,c,s,l,f,d,m,p,g,v,h,y=r.merge,b=(e=r,i=(o=(t=n).browser.isFirefox()||t.browser.isEdge())?ns:ts,a=!o,u=Kl(t)?S:tc([(l=i,f=a,rc(function(e,t){var r,o,n=(r=t,o=f,l(jt.fromDom(r.getNode())).fold(function(){return[r]},function(e){var t=r.type()===Cc,n=[Ku(e.dom,t)];return!t&&o&&n.push(Ku(e.dom,!0)),n}));e.emitTokens(n)},S))]),{annotate:[e.type===ls.Word?u:S],local:[o?S:vl]});return H([b.local,(s=r,Kl(n)||s.type!==ls.Word?[]:[Ds]),r.type===ls.GoogleDocs?[Qs]:[],b.annotate,(v=y?[]:r.cleanFilteredInlineElements,[jl,Ul,(h=v,ec({tags:M([{name:"b",transform:{mutate:I(Us,"strong")}},{name:"i",transform:{mutate:I(Us,"em")}},{name:"u",transform:{mutate:Ws}},{name:"s",transform:{mutate:I(Us,"strike")}},{name:"font",transform:{mutate:Hs,debug:!0}}],function(e){return!E(h,e.name)}).map(function(e){return ue({name:Sc.exact(e.name,Sc.caseInsensitive)},e.transform)})}))]),function(){if(r.type!==ls.Word)return[];var e=[Hl],t=Kl(n)?[]:Wl;return e.concat(t).concat([Vl])}(),Xl(r,n),[el],[Tl],[sl],[ll],(p=[Fl,pl,gl],g=[Fl,dl,Qu({tags:_(m=void 0===(m=(d=r).cleanFilteredInlineElements)?[]:m,function(e){return{name:Sc.exact(e,Sc.caseInsensitive)}})}),ml],d.merge?p:g),[kl,Ll,hl,El],[yl],[bl],[xl],[Sl],r.type===ls.Html&&r.merge?[Ol]:[],[Al],[wl],(c=r,Kl(n)&&c.type===ls.Word?[Gl]:[]),[Ml],r.type===ls.Word?[ql,$l,zl,Bl]:[],[Il],[fl]])}function Yl(e,t){void 0===t&&(t=2);var n=Math.pow(10,t),r=Math.round(e*n);return Math.ceil(r/n)}function Zl(e,t,n,r,o){return a=t,u=n,c=r,s=o,(i=e).toCanvas().then(function(e){return cf(e,i.getType(),a,u,c,s)});var i,a,u,c,s}function Ql(e){return parseInt(e,10)}function ef(e){return e.isPx&&(e.cropWidth!==e.width||e.cropHeight!==e.height)}function tf(o,i){return function(r){return e=au(i,"transform"),re.from(sf.exec(e)).map(function(e){return Math.round(parseFloat(e[1])*(180/Math.PI))}).fold(function(){return pt.resolve(r)},function(e){return n=e,(t=r).toCanvas().then(function(e){return uf(e,t.getType(),n)}).then(function(e){return su(i,"transform"),ct(o,"width"),ct(o,"height"),e});var t,n});var e}}function nf(n,e,t){return We(n.dom).then((a=e,function(e){return t=a.width,n=a.height,o=t,i=n,(r=e).toCanvas().then(function(e){return of(e,o,i).then(function(e){return Ue(e,r.getType())})});var t,n,r,o,i})).then((r=n,o=e,function(e){if(ef(o)){var t=-1*Ql(au(r,"margin-top"));return Zl(e,-1*Ql(au(r,"margin-left")),t,o.cropWidth,o.cropHeight).then(function(e){return ot(r,{width:o.cropWidth,height:o.cropHeight}),e})}return pt.resolve(e)})).then(tf(n,t)).then(function(e){var t=ze(e);return rt(n,"src",t),pt.resolve()});var r,o,a}function rf(f){return Mo(f).filter(tt("span")).map(function(i){function e(){var r,o,e,t,n=i;su(t=f,"margin-top"),su(t,"margin-left"),su(n,"width"),su(n,"height"),su(n,"overflow"),su(n,"display"),o=t,e=["transform"],Pt(r=n)&&Pt(o)&&P(e,function(e){var t,n=o;uu(r,t=e).each(function(e){uu(n,t).isNone()&&ou(n,t,e)})}),su(n,"transform")}var t,n,r,o,a,u,c,s=(t=f,r=au(n=i,"width"),o=au(n,"height"),a=l(t,"width"),u=l(t,"height"),{isPx:(c=/^\d+px$/).test(r)&&c.test(o),cropWidth:Ql(r),cropHeight:Ql(o),width:a,height:u});function l(e,t){return at(e,t).map(Ql).filter(function(e){return!isNaN(e)}).getOr(0)}return(ef(s)||sf.test(au(i,"transform"))?nf(f,s,i):pt.resolve()).then(e,e)}).getOrThunk(function(){return pt.resolve()})}ec({tags:[{name:Sc.pattern(/^(img|table)$/,Sc.caseInsensitive),mutate:function(e){uu(e,"margin-left").exists(function(e){return Lt(e,"-")})&&su(e,"margin-left"),Lt(au(e,"margin-left"),"-")&&(ou(e,"margin-top",au(e,"margin-top")),ou(e,"margin-bottom",au(e,"margin-bottom")),ou(e,"margin-right",au(e,"margin-right")),su(e,"margin"))}}]}),ge([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);var of=function(e,t,n){var r=Ne(e),o=_e(e),i=t/r,a=n/o,u=!1;(i<.5||2<i)&&(i=i<.5?.5:2,u=!0),(a<.5||2<a)&&(a=a<.5?.5:2,u=!0);var c=af(e,i,a);return u?c.then(function(e){return of(e,t,n)}):c},af=function(a,u,c){return new pt(function(e){var t=Ne(a),n=_e(a),r=Math.floor(t*u),o=Math.floor(n*c),i=Le(r,o);wt(i).drawImage(a,0,0,t,n,0,0,r,o),e(i)})},uf=function(e,t,n){var r=(n<0?360+n:n)*Math.PI/180,o=e.width,i=e.height,a=Math.sin(r),u=Math.cos(r),c=Yl(Math.abs(o*u)+Math.abs(i*a)),s=Yl(Math.abs(o*a)+Math.abs(i*u)),l=Le(c,s),f=wt(l);return f.translate(c/2,s/2),f.rotate(r),f.drawImage(e,-o/2,-i/2),Ue(l,t)},cf=function(e,t,n,r,o,i){var a=Le(o,i);return wt(a).drawImage(e,-n,-r),Ue(a,t)},sf=/rotate\((\d\.\d+)rad\)/,lf=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function ff(e,t){return U(e,function(e){return e.start===t})}function df(e,t,n){return{element:e,start:t,finish:n}}function mf(e){return ue(ue({},e),{isBoundary:function(){return e.fold(Yf,Jf,Jf,Jf)},toText:function(){return e.fold(re.none,re.none,function(e){return re.some(e)},re.none)},is:function(n){return e.fold(Jf,Jf,function(e,t){return t.eq(e,n)},Jf)},len:function(){return e.fold(Zf,Qf,function(e,t){return t.property().getText(e).length},Qf)}})}function pf(c,e,t){var n,i,a,r=W(t,function(e){return[e.start,e.finish]}),s=(n=e,a=function(e,t){return function(r,e,t){var n=r.property().getText(e),o=M(function(r,e){if(0===e.length)return[r];var t=F(e,function(e,t){if(0===t)return e;var n=r.substring(e.prev,t);return{prev:t,values:e.values.concat([n])}},{prev:0,values:[]}),n=e[e.length-1];return n<r.length?t.values.concat(r.substring(n)):t.values}(n,t),function(e){return 0<e.length});if(o.length<=1)return[df(e,0,n.length)];r.property().setText(e,o[0]);var i=$f(o.slice(1),function(e,t){var n=df(r.create().text(e),t,t+e.length);return re.some(n)},o[0].length),a=_(i,function(e){return e.element});return r.insert().afterAll(e,a),[df(e,0,o[0].length)].concat(i)}(c,e.element,t)},0===(i=r).length?n:W(n,function(t){var e,n,r,o=W(i,function(e){return e>=t.start&&e<=t.finish?[e-t.start]:[]});return 0<o.length?(n=a(e=t,o),r=e.start,_(n,function(e){return ue(ue({},e),{start:e.start+r,finish:e.finish+r})})):[t]}));return _(t,function(e){var n,t,r,o,i,a=_((n=s,t=e.start,r=e.finish,o=ff(n,t),i=ff(n,r),o.bind(function(e){var t=i.getOr(n[n.length-1]&&n[n.length-1].finish===r?n.length+1:-1);return-1<t?re.some(n.slice(e,t)):re.none()}).getOr([])),function(e){return e.element}),u=_(a,c.property().getText).join("");return{elements:a,word:e.word,exact:u}})}function gf(e){return!ua(e,"a",void 0).isSome()}function vf(e){var t,n;P((t=e,n={word:"__INTERNAL__",pattern:Vf(ad)},od(id,t,[n],void 0)),function(e){var n,t=e.exact;(t.indexOf("@")<0||ud(t))&&(n=e.elements,re.from(n[0]).filter(gf).map(function(e){var t=jt.fromTag("a");return la(e,t),pa(t,n),rt(t,"href",Gc(t)),t}))})}function hf(e){P(e,function(e){Pt(e)&&uu(e,"position").isSome()&&su(e,"position")})}function yf(e){var t,n,r,o=M(e,tt("li"));0<o.length&&(t=Uo(o[0]),n=jt.fromTag("ul"),la(e[0],n),0<t.length&&(r=jt.fromTag("li"),Ea(n,r),pa(r,t)),pa(n,o))}function bf(e){var t=Ho(e);P([vf,hf,yf],function(e){e(t)})}function xf(e){return void 0!==e&&void 0!==e.types&&null!==e.types}function wf(t){return function(e){return{discriminator:t,data:e}}}function Tf(t){return function(e){return e.discriminator===t?re.some(e.data):re.none()}}function If(e){return Xe(t=e,"<html")&&(Xe(t,'xmlns:o="urn:schemas-microsoft-com:office:office"')||Xe(t,'xmlns:x="urn:schemas-microsoft-com:office:excel"'))||Xe(e,'meta name="ProgId" content="Word.Document"');var t}function Sf(e){return Xe(e,'id="docs-internal-guid-')}function Of(e){return 0<e.length}function Af(t,e){return md(t.types,e).map(function(e){return t.getData(e.type)}).filter(Of)}function Cf(e){return Af(e,"html")}function Df(e){return Cf(e).filter(Sf)}function kf(e){return Ad?re.from(e.clipboardData).filter(xf):re.none()}function Lf(e){var t,n=jt.fromTag("div"),r=nc(_o(n),e,[_l]),o=_o(t=n).dom,i=jt.fromDom(o.createDocumentFragment());return pa(i,sa(r,o)),Xa(t),Ea(t,i),gd({container:n})}function Ef(e){return _(e,function(e){return e.asset})}function Nf(d,m,u){function c(e,t,n){p.trigger.block(!0);var r,o,i,a=(r=m,i=o=e,q(d,function(t){return t.getAvailable(i).map(function(e){return Uu(t.steps,e,t.label,t.capture())})}).getOrThunk(function(){var e=r.getAvailable(o);return Uu(r.steps,e,r.label,r.capture())}));a.capture&&n();var u,c,s=a.steps,l=(u=a.input,c=F(s,function(e,t){return ju(e)?e.then(function(e){return Wu(e,u,t)}):Wu(e,u,t)},{response:gc([],[]),bundle:bc({})}),ju(c)?c:pt.resolve(c)),f=kd.getLabelForApi(a.label);l.then(function(e){var r=e.bundle.isInternal.getOr(!1),o=e.bundle.officeStyles.fold(b("auto"),function(e){return e?"merge":"clean"});p.trigger.block(!1),Pu(e.response,function(e){p.trigger.error(e)},function(e,t){p.trigger.insert(e,Ef(t),t,r,f,o)},function(){p.trigger.cancel()},function(e,t,n){p.trigger.insert(e,Ef(t),t,r,f,o),p.trigger.error(n)})})}var p=So({cancel:Io([]),error:Io(["message"]),insert:Io(["elements","assets","correlated","isInternal","source","mode"]),block:Io(["state"])}),s=Za(function(a){Ka(Ro(jt.fromDom(a.target)).dom).each(function(e){var t,n,r;function o(r){return void 0===r.items?re.none():(t=r.types,q(Cd,function(e){return md(t,e)}).map(function(e){for(var t=[],n=0;n<r.items.length;n++)t.push(r.items[n]);return vd({images:t})}));var t}function i(t){return q(t.types,function(e){return"text/plain"===e?re.some(t.getData(e)).map(function(e){return yd({text:r.sanitizeText(e)})}):re.none()})}Qi(e.start,Tu())||(n=a,void 0===(r=u)&&(r=dr),t={getWordData:function(){return kf(n).bind(function(n){return Cf(n).filter(If).map(function(e){var t=Af(n,"rtf");return hd({html:e,rtf:t.fold(function(){return fd()},function(e){return dd(e)})})})})},getGoogleDocsData:function(){return kf(n).bind(Df).map(function(e){return r.sanitizeHtml(e,"googledocs")}).map(Lf)},getImage:function(){return kf(n).bind(o)},getText:function(){return kf(n).fold(function(){var e=window.clipboardData;return void 0!==e?re.some(yd({text:r.sanitizeText(e.getData("text"))})):re.none()},i)},getHtml:function(){return kf(n).bind(Cf).map(r.sanitizeHtml).map(Lf)},getOnlyText:function(){return kf(n).bind(function(e){return 1===(t=e.types).length&&"text/plain"===t[0]?i(e):re.none();var t})},getNative:function(){return pd({nativeEvent:n})},getVoid:function(){return bd({})}},ku()&&(s.control.block(),a.preventDefault()),c(t,s.control,function(){a.preventDefault()}))})});return{paste:s.instance,pasteCustom:function(e,t){c(e,Za(S).control,t=void 0===t?S:t)},isBlocked:s.control.isBlocked,destroy:S,events:p.registry}}function _f(){var t=re.none();return{convert:function(e){t=Ld(e)},listen:function(e){t.getOrThunk(function(){return pt.resolve([])}).then(e)},clear:function(){t=re.none()}}}function Pf(e,t){return{asset:e,image:t}}function Rf(e,r){return Se.cata(e,function(e,t,n){return rt(r,"src",n),!0},w)}function Mf(e,r){var o=[];return P(e,function(e,t){var n=r[t];Rf(e,n)&&o.push(Pf(e,n))}),o}function Ff(e){return $e(e).toPromise()}function jf(e,t){return{asyncAsset:e.then(ji.value,ji.error),image:t}}function Uf(e){var t=jt.fromTag("div");return pa(t,e),Bo(t,"img[src]")}function Hf(e){return 0===e.indexOf("data:")&&-1<e.indexOf("base64")}function Wf(e){return 0===e.indexOf("blob:")}function Bf(e){return at(e,"src").exists(function(e){return Hf(e)||Wf(e)})}function zf(e){return W(Uf(e),function(e){var t,n,r,o=at(e,"src").getOr("");return Hf(o)?(n=e,Ct(o).map(function(e){return jf(Ff(e),n)}).toArray()):Wf(o)?(t=e,re.from(0===(r=o).indexOf("blob:")?It(r):0===r.indexOf("data:")?St(r):null).map(function(e){return jf(e.then(Ff),t)}).toArray()):[]})}function $f(e,n,t){return F(e,function(t,e){return n(e,t.len).fold(b(t),function(e){return{len:e.finish,list:t.list.concat([e])}})},{len:t=void 0===t?0:t,list:[]}).list}function qf(t,e,n){var r,o,i,a=W(e,function(e){return rd(t,e,n)});return M((r=function(e){return e.match({boundary:function(){return Kf.excludeWithout(e)},empty:function(){return Kf.excludeWith(e)},text:function(){return Kf.include(e)},nonEditable:function(){return Kf.excludeWithout(e)}})},o=[],i=[],P(a,function(e){var t=r(e);Kf.cata(t,function(){i.push(e)},function(){0<i.length&&o.push(i),o.push([e]),i=[]},function(){0<i.length&&o.push(i),i=[]})}),0<i.length&&o.push(i),o),function(e){return 0<e.length})}var Vf=function(e){return t=e,n=b(0),r=b(0),o=re.none(),{term:function(){return new RegExp(t,o.getOr("g"))},prefix:n,suffix:r};var t,n,r,o},Gf=ge([{include:["item"]},{excludeWith:["item"]},{excludeWithout:["item"]}]),Kf={include:Gf.include,excludeWith:Gf.excludeWith,excludeWithout:Gf.excludeWithout,cata:function(e,t,n,r){return e.fold(t,n,r)}},Xf=ge([{boundary:["item","universe"]},{empty:["item","universe"]},{text:["item","universe"]},{nonEditable:["item","universe"]}]),Jf=w,Yf=T,Zf=b(0),Qf=b(1),ed={text:s(mf,Xf.text),boundary:s(mf,Xf.boundary),empty:s(mf,Xf.empty),nonEditable:s(mf,Xf.empty),cata:function(e,t,n,r,o){return e.fold(t,n,r,o)}},td=b([]),nd=function(t,e,n){if(t.property().isText(e))return[ed.text(e,t)];if(t.property().isEmptyTag(e))return[ed.empty(e,t)];if(t.property().isNonEditable(e))return[];if(t.property().isElement(e)){var r=t.property().children(e),o=t.property().isBoundary(e)?[ed.boundary(e,t)]:[],i=void 0!==n&&n(e)?[]:W(r,function(e){return nd(t,e,n)});return o.concat(i).concat(o)}return[]},rd=nd,od=function(s,e,l,t){return W(qf(s,e,t),function(e){var r,n,t,o,i=W(e,function(e){return e.fold(td,td,function(e){return[e]},td)}),a=_(i,s.property().getText).join(""),u=(n=a,o=W(l,function(t){return _(function(e,t){for(var n=t.term(),r=[],o=n.exec(e);o;){var i=o.index+t.prefix(o),a=o[0].length-t.prefix(o)-t.suffix(o);r.push({start:i,finish:i+a}),n.lastIndex=i+a,o=n.exec(e)}return r}(n,t.pattern),function(e){return ue(ue({},t),e)})}),(t=Array.prototype.slice.call(o,0)).sort(function(e,t){return e.start<t.start?-1:t.start<e.start?1:0}),t),c=(r=s,$f(i,function(e,t){var n=t+r.property().getText(e).length;return re.from(df(e,t,n))}));return pf(s,c,u)})},id={up:b({selector:ia,closest:ua,predicate:na,all:function(e,t){for(var n=g(t)?t:w,r=e.dom,o=[];null!==r.parentNode&&void 0!==r.parentNode;){var i=r.parentNode,a=jt.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o}}),down:b({selector:Bo,predicate:Ii}),styles:b({get:au,getRaw:uu,set:ou,remove:su}),attrs:b({get:it,set:rt,remove:ct,copyTo:function(e,t){ot(t,F(e.dom.attributes,function(e,t){return e[t.name]=t.value,e},{}))}}),insert:b({before:la,after:fa,afterAll:function(n,r){P(r,function(e,t){fa(0===t?n:r[t-1],e)})},append:Ea,appendAll:pa,prepend:da,wrap:ma}),remove:b({unwrap:Ja,remove:xu}),create:b({nu:jt.fromTag,clone:function(e){return jt.fromDom(e.dom.cloneNode(!1))},text:jt.fromText}),query:b({comparePosition:function(e,t){return e.dom.compareDocumentPosition(t.dom)},prevSibling:Fo,nextSibling:jo}),property:b({children:Ho,name:Ye,parent:Mo,document:function(e){return Po(e).dom},isText:Rt,isComment:et,isElement:Pt,isSpecial:function(e){return E(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],Ye(e))},getLanguage:function(e){return Pt(e)?at(e,"lang"):re.none()},getText:Ha,setText:function(e,t){return gu.set(e,t)},isBoundary:function(e){return!!Pt(e)&&("body"===Ye(e)||E(lf,Ye(e)))},isEmptyTag:function(e){return!!Pt(e)&&E(["br","img","hr","input"],Ye(e))},isNonEditable:function(e){return Pt(e)&&"false"===it(e,"contenteditable")}}),eq:No,is:hi},ad=/(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)[A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*(?::\d+)?(?:\/(?:[-.~*+=!;:'%@$(),\/\w]*[-~*+=%@$()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?/g.source,ud=function(e){var t=e.indexOf("://");return 3<=t&&t<=9},cd=function(e,t){return nc(e,t,[ll,El])},sd={disabled:function(){return{discriminator:"disabled",data:{}}},fromClipboard:function(e){return{discriminator:"fromClipboard",data:{rtf:e}}}},ld=be(sd),fd=sd.disabled,dd=sd.fromClipboard,md=function(e,t){var n=new RegExp(t,"i");return q(e,function(e){return eu(null!==n.exec(e),{type:e,flavor:t})})},pd=wf("event"),gd=wf("html"),vd=wf("images"),hd=wf("word"),yd=wf("text"),bd=wf("void"),xd=Tf("event"),wd=Tf("html"),Td=Tf("images"),Id=Tf("word"),Sd=Tf("text"),Od=vi().browser,Ad=!(Od.isIE()||Od.isEdge()&&Od.version.major<16),Cd=["^image/","file"],Dd={native:"Outside of Textbox.io pasting HTML5 API (could be internal)",fallback:"Outside of Textbox.io pasting offscreen (could be internal)",msoffice:"Word Import pasting",googledocs:"Google Docs pasting",image:"Image pasting",plaintext:"Only plain text is available to paste",text:"Plain text pasting",none:"There is no valid way to paste",discard:"There is no valid way to paste, discarding content"},kd=ue({getLabelForApi:function(t){return j(be(Dd),function(e){return Dd[e]===t}).fold(b("unknown"),function(e){switch(e){case"native":case"fallback":return"html";case"none":case"discard":return"invalid";default:return e}})}},Dd),Ld=function(n){var e,r,t=function(e){for(var t=null!=r?r:_t,n=0;n<e.length&&null!=t;++n)t=t[e[n]];return t}("window.clipboardData.files".split(".")),o=v((e=n).convertURL)?e.convertURL:v(e.msConvertURL)?e.msConvertURL:void 0;if(void 0!==t&&void 0!==Ld&&0<t.length){var i=pt.all(_(t,function(e){var t=URL.createObjectURL(e);return o.apply(n,[e,"specified",t]),kt(e,t).toPromise()}));return re.some(i)}return re.none()};function Ed(f){return function(e,s){function l(){return pt.resolve(s)}function t(e,t){return!1===f.allowLocalImages?(P(c=M(Uf(o=e),Bf),xu),pt.resolve({response:0<c.length?(u=M(o,function(e){return!tt("img")(e)||!Bf(e)}),hc(u,[],"errors.local.images.disallowed")):s.response,bundle:s.bundle})):0===t.length?(n=zf(i=e),r=pt.all(_(n,function(e){return e.asyncAsset})),a=_(n,function(e){return e.image}),r.then(function(e){var t,n,r=(t=[],n=[],P(e,function(e){e.fold(function(e){t.push(e)},function(e){n.push(e)})}),{errors:t,values:n}),o=Mf(r.values,a);return{response:0<r.errors.length?hc(i,o,"errors.imageimport.failed"):gc(i,o),bundle:s.bundle}})):l();var i,n,r,a,o,u,c}return Pu(s.response,Fu,t,l,t)}}function Nd(t,r,o){return void 0===o&&(o=!1),new pt(function(e){var n=new XMLHttpRequest;n.onreadystatechange=function(){4===n.readyState&&e({status:n.status,blob:n.response})},n.open("GET",t,!0),n.withCredentials=o,se(r,function(e,t){n.setRequestHeader(t,e)}),n.responseType="blob",n.send()})}function _d(e){var t,n=(t=e,"ImageProxy HTTP error: "+j(Kd,function(e){return t===e.code}).fold(b("Unknown ImageProxy error"),function(e){return e.message}));return pt.reject(n)}function Pd(t){return j(Xd,function(e){return e.type===t}).fold(b("Unknown service error"),function(e){return e.message})}function Rd(e){return r=e,new pt(function(e,t){var n=new FileReader;n.onload=function(){e(n.result)},n.onerror=function(e){t(e)},n.readAsText(r)}).then(function(e){var t="ImageProxy Service error: "+function(e){try{return re.some(JSON.parse(e))}catch(e){return re.none()}}(e).bind(function(e){return t=F(["error","type"],function(e,t){return v(e)?e[t]:void 0},e),re.from(t).map(Pd);var t}).getOr("Invalid JSON in service error message");return pt.reject(t)});var r}function Md(e){return e<200||300<=e}function Fd(e,t,n){return void 0===n&&(n=!1),t?(a={"Content-Type":"application/json;charset=UTF-8","tiny-api-key":t},Nd((o=t,i=-1===(r=e).indexOf("?")?"?":"&",/[?&]apiKey=/.test(r)?r:r+i+"apiKey="+encodeURIComponent(o)),a).then(function(e){return Md(e.status)?(t=e.status,"application/json"!==(null==(n=e.blob)?void 0:n.type)||400!==t&&403!==t&&404!==t&&500!==t?_d(t):Rd(n)):pt.resolve(e.blob);var t,n})):Nd(e,{},n).then(function(e){return Md(e.status)?_d(e.status):pt.resolve(e.blob)});var r,o,i,a}function jd(e,a){function t(){return pt.resolve({response:a.response,bundle:a.bundle})}function u(o){return At(o).then(function(e){var t=ke("image"),n=He(o,e),r=URL.createObjectURL(o);return Se.blob(t,n,r)})}function c(e,t){return Se.url(ke("image"),t,e)}function n(n,r){var o=!1,i=W(n,function(e){return Bo(e,"img")});return pt.all(_(i,function(e){var t=e.dom.src,n=Ni(t);return Xe(n.host,"google")&&!Lt(n.path,"/drawings/")?Fd(e.dom.src).then(u,function(){return o=!0,c(e,t)}):c(e,t)})).then(function(e){var t=r.concat(Mf(e,i));return{response:o?hc(n,t,"errors.imageimport.failed"):gc(n,t),bundle:a.bundle}},function(){return{response:pc("errors.imageimport.invalid"),bundle:a.bundle}})}return Pu(a.response,t,n,t,n)}function Ud(e){var t=Zd(e);return t&&Jd(e)||!t&&Yd(e)}function Hd(e){return e.isInternal.getOr(!1)}function Wd(e){return Zd(e)?ls.Word:e.isGoogleDocs.getOr(!1)?ls.GoogleDocs:ls.Html}function Bd(e,t){var u,r,c,o=(u=e,c=(r=t).translations,{get:function(e){var t=function(){switch(e){case ls.Word:return"officeStyles";case ls.GoogleDocs:return"gdocsStyles";default:return"htmlStyles"}}(),n=r[t];return g(n)?n().then(function(e){return s("merge"===e)},function(e){return console.error(e),s(!1)}):"clean"===n?pt.resolve(s(!1)):"merge"===n?pt.resolve(s(!0)):new pt(function(e){var t=jt.fromTag("div");Yi(t,Qd("styles-dialog-content"));var n=jt.fromTag("p");pa(n,sa(c("cement.dialog.paste.instructions"))),Ea(t,n);var r={text:c("cement.dialog.paste.clean"),tabindex:0,className:Qd("clean-styles"),click:function(){i(),e(s(!1))}},o={text:c("cement.dialog.paste.merge"),tabindex:1,className:Qd("merge-styles"),click:function(){i(),e(s(!0))}},i=function(){a.destroy()},a=u();a.setTitle(c("cement.dialog.paste.title")),a.setContent(t),a.setButtons([r,o]),a.events.close.bind(function(){i(),e(re.none())}),a.show()})},destroy:S});function s(e){return re.some(wc(r,{officeStyles:e,gdocsStyles:e,htmlStyles:e}))}return function(e,t){var n=t.bundle,r=t.response;return o.get(Wd(n)).then(function(e){return e.fold(function(){return{response:vc(),bundle:t.bundle}},function(e){return{response:r,bundle:bc({officeStyles:e.officeStyles,gdocsStyles:e.gdocsStyles,htmlStyles:e.htmlStyles})}})})}}function zd(n,r){return function(e,t){return Hd(t.bundle)?pt.resolve({response:t.response,bundle:bc({officeStyles:!0,gdocsStyles:!0,htmlStyles:!0})}):Bd(n,r)(e,t)}}function $d(e,t){if(!Ti(e))throw new Error("Internal error: attempted to write to an iframe that is not in the DOM");var n,r=function(e){var t=e.dom;try{var n=t.contentWindow?t.contentWindow.document:t.contentDocument,r=jt.fromDom;return null!=n?re.some(r(n)):re.none()}catch(e){return console.log("Error reading iframe: ",t),console.log("Error was: "+e),re.none()}}(n=e).getOrThunk(function(){return _o(n)}).dom;r.open("text/html","replace"),r.writeln(t),r.close()}var qd,Vd,Gd,Kd=[{code:404,message:"Could not find Image Proxy"},{code:403,message:"Rejected request"},{code:0,message:"Incorrect Image Proxy URL"}],Xd=[{type:"not_found",message:"Failed to load image."},{type:"key_missing",message:"The request did not include an api key."},{type:"key_not_found",message:"The provided api key could not be found."},{type:"domain_not_trusted",message:"The api key is not valid for the request origins."}],Jd=function(e){return e.officeStyles.getOr(!0)},Yd=function(e){return e.htmlStyles.getOr(!1)},Zd=function(e){return e.isWord.getOr(!1)},Qd=zo("ephox-cement").resolve,em={},tm={exports:em};function nm(e){return W(e,Cm)}function rm(e,t,n){function r(e){return-1!==e.selector.indexOf(",")}var i,o,a,u=W(M(e,r),function(t){return _(t.selector.split(","),function(e){return{selector:e.trim(),raw:t.raw}})}),c=M(e,function(e){return!r(e)}).concat(u);c.sort(function(e,t){return Am.compare(e.selector,t.selector)}).reverse(),i=t,o=n,a=W(c,function(o){var e=Bo(i,o.selector);return P(e,function(e){var t,n,r;iu(e,(t=o.raw,n=e,r={},P(t,function(e){void 0!==t[e]&&(E(n.dom.style,e)||(r[e]=t[e]))}),r))}),e}),o&&P(a,function(e){ct(e,"class")})}function om(e,t,n,r){rm(nm(Om(e)).map(function(e){var t=e.selector;return{selector:n.hasOwnProperty(t)?n[t]:t,raw:e.raw}}),t,r)}function im(e,t,n,r){rm(M(nm(Om(e)),function(e){return Lt(e.selector,n)}),t,r)}function am(e,t,n,r){rm(M(nm(Om(e)),function(e){return E(n,e.selector)}),t,r)}function um(a){var e,n=(e=jt.fromDom(document.body),{play:function(a,u,c){var s=jt.fromTag("div"),l=jt.fromTag("iframe");iu(s,{display:"none"});var f=Ia(l,"load",function(){var e;f.unbind(),$d(l,a);var t=null===(e=l.dom.contentWindow)||void 0===e?void 0:e.document;if(void 0===t)throw new Error("sandbox iframe load event did not fire correctly");var n=jt.fromDom(t),r=n.dom.body;if(void 0===r)throw new Error("sandbox iframe does not have a body");var o=jt.fromDom(r),i=u(n,o);xu(s),setTimeout(I(c,i),0)});Ea(s,l),Ea(e,s)}});return function(t){return new pt(function(e){n.play(t,function(e,t){return n=e,r=t,o={mergeInline:b(a)},(i=o.mergeInline())&&(km.inlineStyles(n,r,Lm),Ml(r)),Em(r,i),lu(t);var n,r,o,i},e)})}}function cm(e,t,u,n){function c(e){return{response:e,bundle:bc({})}}var r=n.sanitizeHtml(t,"word");return um(e)(r).then(function(e){function t(e){return c(gc(n,e))}var n=sa(e),r=jt.fromTag("div");pa(r,n);var o=M(Eo("img[src]",r),function(e){return at(e,"src").exists(function(e){return Lt(e,"blob:")||Lt(e,"data:")})}),i=Eo("img[data-image-src]",r);if(0===o.length&&0===i.length)return t([]);if(u)return P(o,function(e){return ct(e,"id"),0}),pt.all(_(o,function(a){var u=a.dom;return We(u).then(function(i){return i.toBlob().then(function(e){return t=i,n=e,r=Lt(u.src,"blob:")?u.src:URL.createObjectURL(n),o=ke("image"),Pf(Se.blob(o,t,r),a);var t,n,r,o})})})).then(t);P(o,xu),P(i,xu);var a=Ho(r);return c(hc(a,[],"errors.local.images.disallowed"))})}function sm(e){try{var t=e(),n=null!=t&&0<t.length?sa(t):[];return ji.value(n)}catch(e){return console.error("PowerPaste error code: PT01. Message: ",e),ji.error("errors.paste.process.failure")}}function lm(e){return e.fold(Mu,function(e){return{response:gc(e,[]),bundle:bc({})}})}function fm(u,c,s,l,f){return sm(function(){var e,t,n,r,o,i,a={type:l,merge:s,cleanFilteredInlineElements:null!==(e=f.cleanFilteredInlineElements)&&void 0!==e?e:[],indentUseMargin:null!==(t=f.indentUseMargin)&&void 0!==t&&t};return n=u,r=_m,i=a,bf(o=c),nc(n,lu(o),Jl(r,i))})}function dm(e){function t(e){return{response:gc([e],[]),bundle:bc({})}}return r=Bo(n=e,"img"),pt.all(_(r,rf)).then(function(){return n}).then(t).catch(function(){return t(e)});var n,r}function mm(e,t,n,r,o,i){return fm(e,t,r,n,i).fold(Fu,function(r){return o.then(function(e){var t,o,i,n=(t=e,o=[],i=W(r,function(e){return tt("img")(e)?[e]:Bo(e,"img")}),P(t,function(r){Se.cata(r,function(e,t,n){P(i,function(e){it(e,"src")===n&&o.push(Pf(r,e))})},S)}),o);return{response:gc(r,n),bundle:bc({})}})})}function pm(e){return"\n"===e||"\r"===e}function gm(e,t){var n,r,o,i,a,u=(n=e,(r=jt.fromTag("div")).dom.textContent=n,lu(r)),c=sa((i=t,1===(o=_(F(a=u.replace(/\t/g,i<=0?"":new Array(i+1).join(" ")),function(e,t){return-1!==" \f\t\v".indexOf(t)||"\xa0"===t?e.pcIsSpace||""===e.str||e.str.length===a.length-1||(n=e.str.length+1)<a.length&&0<=n&&pm(a[n])?{pcIsSpace:!1,str:e.str+"\xa0"}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:pm(t),str:e.str+t};var n},{pcIsSpace:!1,str:""}).str.replace(/^[\r\n]*|[\r\n]*$/g,"").split(/(?:\r?\n){2}/),function(e){return e.split(/\n|\r\n/).join("<br />")})).length?o[0]:_(o,function(e){return"<p>"+e+"</p>"}).join("")));return gc(c,[])}function vm(o){return function(e,t){return n=o,{response:0<(r=Sd(e).getOrDie("Required text input for Text Handler")).text.length?gm(r.text,n):vc(),bundle:bc({})};var n,r}}function hm(e,o){function t(e,t){var n=jt.fromTag("div");pa(n,e),bf(n);var r=Ho(n);return{response:gc(r,t),bundle:o.bundle}}var n=b(o);return Pu(o.response,n,t,n,t)}function ym(u,c,s){return function(e,t){var n,r,o=wd(e).getOrDie("Wrong input type for HTML handler").container,i=_o(c),a=t.bundle;return Hd(a)?(n=i,r=o,lm(sm(function(){return cd(n,lu(r))}))):(u(o),lm(fm(i,o,Ud(a),Wd(a),s)))}}function bm(c,s,l){return function(e,t){var u=t.bundle;return u.proxyBin.fold(function(){return console.error("There was no proxy bin setup. Ensure you have run proxyStep first."),pt.resolve({response:vc(),bundle:bc({})})},function(e){var n,t=Ud(u),r=Wd(u),o=Hd(u),i=(n=u,new pt(function(t){n.backgroundAssets.fold(function(){t([])},function(e){e.listen(t)})})),a=_o(c);return o?function(e,t,n,r){var o=ls.Html,i=t.findClipboardTags(Ho(n)).getOr([]);P(i,xu);var a=pt.resolve([]);return mm(e,n,o,!0,a,r)}(a,s,e,l):mm(a,e,r,t,i,l)})}}function xm(e,t){return xc}function wm(r){return function(e,t){var n=Ru(t.bundle,bc(r));return{response:t.response,bundle:n}}}function Tm(e,t){return dm(wd(e).getOrDie("Wrong input type for HTML handler").container)}function Im(e,t){return js(e,function(e){return Pt(e)&&at(e,"style").exists(function(e){return-1<e.indexOf("mso-")})})}function Sm(e,t){var n,r=t;return 0<=(n=lu(e)).indexOf("<o:p>")||r.browser.isEdge()&&0<=n.indexOf('v:shapes="')||r.browser.isEdge()&&0<=n.indexOf("mso-")||0<=n.indexOf("mso-list")||0<=n.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")||0<=n.indexOf("MsoListParagraphCxSpFirst")||0<=n.indexOf("<w:WordDocument>")}qd=em,Vd=tm,Gd=void 0,function(e){"object"==typeof qd&&void 0!==Vd?Vd.exports=e():("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=e()}(function(){return function i(a,u,c){function s(t,e){if(!u[t]){if(!a[t]){var n="function"==typeof Gd;if(!e&&n)return n(t,!0);if(l)return l(t,!0);var r=new Error("Cannot find module '"+t+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[t]={exports:{}};a[t][0].call(o.exports,function(e){return s(a[t][1][e]||e)},o,o.exports,i,a,u,c)}return u[t].exports}for(var l="function"==typeof Gd,e=0;e<c.length;e++)s(c[e]);return s}({1:[function(e,t,n){var r,a,o=(r=function(e){for(var t,n=[],r=e.split(","),o=0,i=r.length;o<i;o+=1)0<(t=r[o]).length&&n.push(a(t));return n},a=function(c){var e,t,s=c,l={a:0,b:0,c:0},f=[];function n(e){var t,n,r,o;if(e.test(s))for(n=0,r=(t=s.match(e)).length;n<r;n+=1)o=t[n],s=s.replace(o,Array(o.length+1).join("A"))}return e=function(e,t){var n,r,o,i,a,u;if(e.test(s))for(r=0,o=(n=s.match(e)).length;r<o;r+=1)l[t]+=1,i=n[r],a=s.indexOf(i),u=i.length,f.push({selector:c.substr(a,u),type:t,index:a,length:u}),s=s.replace(i,Array(u+1).join(" "))},n(/\\[0-9A-Fa-f]{6}\s?/g),n(/\\[0-9A-Fa-f]{1,5}\s/g),n(/\\./g),(t=/:not\(([^\)]*)\)/g).test(s)&&(s=s.replace(t,"     $1 ")),function(){var e,t,n,r,o=/{[^]*/gm;if(o.test(s))for(t=0,n=(e=s.match(o)).length;t<n;t+=1)r=e[t],s=s.replace(r,Array(r.length+1).join(" "))}(),e(/(\[[^\]]+\])/g,"b"),e(/(#[^\#\s\+>~\.\[:]+)/g,"a"),e(/(\.[^\s\+>~\.\[:]+)/g,"b"),e(/(::[^\s\+>~\.\[:]+|:first-line|:first-letter|:before|:after)/gi,"c"),e(/(:[\w-]+\([^\)]*\))/gi,"b"),e(/(:[^\s\+>~\.\[:]+)/g,"b"),s=(s=s.replace(/[\*\s\+>~]/g," ")).replace(/[#\.]/g," "),e(/([^\s\+>~\.\[:]+)/g,"c"),f.sort(function(e,t){return e.index-t.index}),{selector:c,specificity:"0,"+l.a.toString()+","+l.b.toString()+","+l.c.toString(),specificityArray:[0,l.a,l.b,l.c],parts:f}},{calculate:r,compare:function(e,t){var n,r,o;if("string"==typeof e){if(-1!==e.indexOf(","))throw"Invalid CSS selector";n=a(e).specificityArray}else{if(!Array.isArray(e))throw"Invalid CSS selector or specificity array";if(4!==e.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";n=e}if("string"==typeof t){if(-1!==t.indexOf(","))throw"Invalid CSS selector";r=a(t).specificityArray}else{if(!Array.isArray(t))throw"Invalid CSS selector or specificity array";if(4!==t.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";r=t}for(o=0;o<4;o+=1){if(n[o]<r[o])return-1;if(n[o]>r[o])return 1}return 0}});void 0!==n&&(n.calculate=o.calculate,n.compare=o.compare)},{}],2:[function(e,t,n){var r=e("specificity");t.exports={boltExport:r}},{specificity:1}]},{},[2])(2)});function Om(e){var t=e.dom.styleSheets;return Array.prototype.slice.call(t)}var Am=tm.exports.boltExport,Cm=function(e){return W(e.cssRules,function(e){return e.type===window.CSSRule.IMPORT_RULE?Cm(e.styleSheet):e.type===window.CSSRule.STYLE_RULE?[function(e){var t=e.selectorText,n=e.style.cssText;if(void 0===n)throw new Error("WARNING: Browser does not support cssText property");return{selector:t,style:n,raw:e.style}}(e)]:[]})},Dm={inlineStyles:function(e,t,n){om(e,t,n,!0)},inlineStylesKeepClasses:function(e,t,n){om(e,t,n,!1)},inlinePrefixedStyles:function(e,t,n){im(e,t,n,!0)},inlinePrefixedStylesKeepClasses:function(e,t,n){im(e,t,n,!1)},inlineSelectorsStyles:function(e,t,n){am(e,t,n,!0)},inlineSelectorsStylesKeepClasses:function(e,t,n){am(e,t,n,!1)}},km={inlineStyles:Dm.inlineStyles,inlineStylesKeepClasses:Dm.inlineStylesKeepClasses,inlinePrefixedStyles:Dm.inlinePrefixedStyles,inlinePrefixedStylesKeepClasses:Dm.inlinePrefixedStylesKeepClasses,inlineSelectorsStyles:Dm.inlineSelectorsStyles,inlineSelectorsStylesKeepClasses:Dm.inlineSelectorsStylesKeepClasses},Lm={p:"p, li[data-converted-paragraph]"},Em=function(s,e){P(Bo(s,"li[data-converted-paragraph]"),function(e){ct(e,"data-converted-paragraph")}),e&&(P(Bo(s,"li"),function(e){var t,n,r,o,i,a=(da(s,o=jt.fromTag("span")),i=o,{convertToPx:function(e){var t;return ou(i,"margin-left",e),t=au(i,"margin-left"),parseFloat(/-?\d+\.?\d*/.exec(t)[0])},destroy:function(){return xu(i)}}),u=(t=a,n=at(s,"data-tab-interval").getOr("36pt"),t.convertToPx(n)),c=Nm(e,u,a).getOr({});ct(r=e,"data-list-level"),ct(r,"data-text-indent-alt"),ct(r,"data-border-margin"),su(r,"margin-left"),su(r,"text-indent"),se(cu(r),function(e,t){!Lt(t,"border")||"border-image"!==t&&"none"!==e.trim()&&"initial"!==e.trim()||su(r,t)}),a.destroy(),iu(e,c)}),P(Bo(s,"ol,ul"),function(t){var e=Bo(t,"li");uu(t,"margin-top").isNone()&&re.from(e[0]).each(function(e){ou(t,"margin-top",au(e,"margin-top"))}),uu(t,"margin-bottom").isNone()&&re.from(e[e.length-1]).each(function(e){ou(t,"margin-bottom",au(e,"margin-bottom"))})})),ct(s,"data-tab-interval")},Nm=function(d,m,p){function g(e){return at(e,"data-list-level").map(function(e){return parseInt(e,10)}).getOr(1)}return uu(d,"text-indent").bind(function(f){return uu(d,"margin-left").map(function(e){var t=uu(d,"list-style").exists(function(e){return Xe(e,"none")}),n=at(d,"data-border-margin").getOr("0px"),r=t?g(d)+1:g(d),o=p.convertToPx(e)+p.convertToPx(n),i=m*r,a=at(d,"data-text-indent-alt").getOr(f),u=p.convertToPx(a),c={},s=m/2*-1-u;0<s&&(c["text-indent"]=s+"px");var l=o-i-s;return c["margin-left"]=0<l?l+"px":"0px",c})})},_m=vi(),Pm=b(Qd("smartpaste-eph-bin")),Rm=vi();function Mm(e,t,n){return t.indexOf(e,n)}function Fm(e,t,n,r,o,i,a){return-1===e||-1===t?re.none():re.some({start:e,end:t,bower:n,regex:r,idRef:o,isEquation:i,attrs:a})}function jm(e,t,n){return e.substring(t,n)}function Um(e,t){if(-1===t)return t;var n=0,r=e.length;do{var o=e.indexOf("{",t),i=e.indexOf("}",t);if(o<i&&-1!==o?(t=o+1,++n):(i<o||o<0)&&-1!==i&&(t=i+1,--n),r<t||-1===i)return-1}while(0<n);return t}function Hm(e,t,n,r,o){var i,a,u;return Fm(n,r,jm(e,n,r),/[^a-fA-F0-9]([a-fA-F0-9]+)\}$/,"i",o,(a=Mm("\\picscalex",i=e,n),u=Mm("\\bliptag",i,a),-1<a&&a<u?re.from(i.substring(a,u)):re.none()))}function Wm(e,t,n,r,o){return Fm(n,r,jm(e,n,r),/([a-fA-F0-9]{64,})(?:\}.*)/,"s",o,re.none())}function Bm(e){for(var c=[],t=function(){return e.length},n=function(e){var r,o,i,a,u,t,n=(o=(r=e).bower,i=r.regex,a=r.isEquation,u=r.attrs,(null!==(t=/\\shplid(\d+)/.exec(o))?re.some(t[1]):re.none()).map(function(e){var t,n=r.idRef+e;return(0<=(t=o).indexOf("\\pngblip")?ji.value("image/png"):0<=t.indexOf("\\jpegblip")?ji.value("image/jpeg"):ji.error("errors.imageimport.unsupported")).fold(function(e){return Ym.unsupported(n,e,a,u)},function(t){return((e=o.match(i))&&e[1]&&e[1].length%2==0?ji.value(e[1]):ji.error("errors.imageimport.invalid")).fold(function(e){return Ym.unsupported(n,e,a,u)},function(e){return Ym.supported(n,t,function(e,t){if(0===e.length)throw new Error("Zero length content passed to Hex conversion");var n=function(e){for(var t=new Array(e.length/2),n=0;n<e.length;n+=2){var r=e.substr(n,2);t[Math.floor(n/2)]=parseInt(r,16)}return t}(e),r=new Uint8Array(n);return new Blob([r],{type:t})}(e,t),a,u)});var e})}));return c=c.concat(n.toArray()),e.end},r=0;r<e.length;)r=Zm(e,r).fold(t,n);return c}function zm(e){return Ym.cata(e,function(e,t,n){return e},function(e,t,n,r,o){return e})}function $m(e){return Ym.cata(e,function(e,t,n){return n},function(e,t,n,r,o){return r})}function qm(e){return Ym.cata(e,function(e,t,n){return ji.error(t)},function(e,t,n,r,o){return ji.value(n)})}function Vm(e,t){var n=new RegExp("\\\\pic"+t+"(\\-?\\d+)\\\\").exec(e)[1];return parseInt(n,10)}function Gm(e,t,i,a,u){var c=[],s=[],l=!1;return{blobs:W(e,function(n,r){var o=it(n,"data-image-id");return ct(n,"rtf-data-image"),ct(n,"data-image-id"),ct(n,"data-ms-equation"),u||ct(n,"data-image-src"),"unsupported"===o?(l=!0,rt(n,"alt",i("errors.imageimport.unsupported")),[]):j(t,function(e,t){return a(e,t,o,r)}).fold(function(){return console.log("WARNING: unable to find data for image ",n.dom),l=!0,rt(n,"alt",i("errors.imageimport.unsupported")),[]},function(t){return qm(t).fold(function(e){return l=!0,console.error("PowerPaste error code: RTF04"),rt(n,"alt",i(e)),[]},function(e){return c.push(n),s.push(Ym.cata(t,function(e,t,n){return re.none()},function(e,t,n,r,o){return o})),u&&ct(n,"data-image-src"),[e]})})}),filteredImages:c,imageAttrs:s,failedImage:l}}function Km(e,t,r,n,o){var i=F(t,function(t,n){var r=zm(n),o=$m(n);return U(t,function(e){return!o&&!$m(e)&&zm(e)===r}).fold(function(){return t.concat([n])},function(e){return qm(t[e]).isValue()?t:t.slice(0,e).concat(t.slice(e+1)).concat([n])})},[]),a=o.keepSrc||!1,u=R(i,function(e){return!$m(e)}),c=u.pass,s=u.fail,l=R(e,function(e){return"true"!==it(e,"data-ms-equation")}),f=l.pass,d=l.fail,m=Gm(f,c,n,function(e,t,n,r){return zm(e)===n},a),p=Gm(d,s,n,function(e,t,n,r){return t===r},a),g=m.filteredImages.concat(p.filteredImages),v=m.imageAttrs.concat(p.imageAttrs),h=m.blobs.concat(p.blobs),y=m.failedImage||p.failedImage;qe(h).get(function(e){var t,n=v;((t=e).length===n.length?pt.all(_(t,function(e,t){return y=e,n[t].fold(function(){return pt.resolve(y)},function(h){return Se.cata(y,function(p,g,v){return g.toCanvas().then(function(e){var t,n,r,o,i,a,u,c,s,l=jt.fromDom(e),f=at(l,"width").map(function(e){return parseInt(e,10)}).getOr(1),d=at(l,"height").map(function(e){return parseInt(e,10)}).getOr(1),m=(t=f,n=d,a=(o=(r=I(Vm,h))("wgoal"))/t,u=(i=r("hgoal"))/n,{cropl:(c=r("cropl"))/a,cropt:(s=r("cropt"))/u,cropw:(o-c-r("cropr"))/a,croph:(i-s-r("cropb"))/u});return f===m.cropw&&d===m.croph&&0===m.cropl&&0===m.cropt?pt.resolve(y):Zl(g,m.cropl,m.cropt,m.cropw,m.croph).then(function(n){return n.toBlob().then(function(e){URL.revokeObjectURL(v);var t=URL.createObjectURL(e);return Se.blob(p,n,t)})})})},function(e,t,n){return pt.resolve(y)})});var y})):pt.resolve(t)).then(function(e){var t=Mf(e,g);r(t,y)})})}function Xm(e){return Bo(e,"[rtf-data-image]")}var Jm=ge([{unsupported:["id","message","isEquation","attrs"]},{supported:["id","contentType","blob","isEquation","attrs"]}]),Ym={unsupported:Jm.unsupported,supported:Jm.supported,cata:function(e,t,n){return e.fold(t,n)}},Zm=function(e,t){var n=Mm("{\\pict{",e,t),r=Um(e,n),o=Mm("{\\shp{",e,t),i=Um(e,o),a=Mm("{\\mmathPict{",e,t),u=Um(e,a),c=-1!==a&&(a<n&&r<u||a<o&&i<u),s=I(Wm,e,t,o,i,c),l=I(Hm,e,t,n,r,c);return-1===n&&-1===o?re.none():-1===n?s():-1===o||o<n&&r<i?l():n<o&&i<r?s():n<o?l():o<n?s():re.none()},Qm=function(e){return Bm(e.replace(/\r/g,"").replace(/\n/g,""))};function ep(s){return function(u,c){return new pt(function(t,e){function o(e){return t({response:e,bundle:bc({})})}var r,a,i=(r=s.translations,{events:(a=So({insert:Io(["elements","correlated"]),incomplete:Io(["elements","correlated","message"])})).registry,processRtf:function(o,i,e,t){var n=Qm(e);Km(Xm(o),n,function(e,t){var n=Ho(o),r=e.concat(i);t?(console.error("PowerPaste error code: RTF01"),a.trigger.incomplete(n,r,"errors.imageimport.failed")):a.trigger.insert(n,r)},r,t)}});function n(t){function e(){return pt.resolve(c)}function n(e,n){var r=jt.fromTag("div");return pa(r,e),t.fold(function(){var e,t=Xm(r);return 0<t.length?function(){P(t,xu);var e=Ho(r);return console.error("PowerPaste error code: RTF03"),o(hc(e,n,"errors.imageimport.failed"))}():(e=Ho(r),o(gc(e,n)))},function(e){i.processRtf(r,n,e,s)})}return Pu(c.response,e,n,e,n)}i.events.insert.bind(function(e){o(gc(e.elements,e.correlated))}),i.events.incomplete.bind(function(e){console.error("PowerPaste error code: RTF02"),o(hc(e.elements,e.correlated,e.message))}),function(t,n){var e=be(n);if(e.length!==ld.length)throw new Error("Partial match");q(e,function(e){return eu(t.discriminator===e,n[e])}).getOrDie("Must find branch for constructor: "+t.discriminator)(t.data)}(Id(u).getOrDie("Word input required for rtf data").rtf,{disabled:function(){n(re.none())},fromClipboard:function(e){n(!0===s.allowLocalImages?re.some(e.rtf):re.none())}})})}}function tp(r){function o(){return pt.resolve(r)}return Se.cata(r.asset,function(e,t,n){return/tiff$/.test(t.getType())?Be(t,"image/png").then(function(e){return $e(e).toPromise()}).then(re.some).catch(function(e){return console.warn(e),re.none()}).then(function(e){return e.map(function(e){var t=r.image;return URL.revokeObjectURL(n),Rf(e,t),Pf(e,t)}).getOr(r)}):o()},o)}function np(e,n){function r(e,t){return pt.all(_(e,tp)).then(function(e){return{response:t(e),bundle:n.bundle}})}return Pu(n.response,Fu,function(t,e){return r(e,function(e){return gc(t,e)})},function(){return pt.resolve(n)},function(t,e,n){return r(e,function(e){return console.error("PowerPaste error code:  IMG01"),hc(t,e,n)})})}function rp(e,t){return e.isSupported?t.getWordData():re.none()}function op(e){return e.getNative()}function ip(e){return e.getImage()}function ap(e){return e.getHtml()}function up(e){return e.getText()}function cp(e){return e.getOnlyText()}function sp(e){return e.getGoogleDocsData()}function lp(e){return e.getVoid()}function fp(e,t,n,r){return{label:e,getAvailable:t,steps:n,capture:b(r)}}function dp(e,t,n,r){return{label:e,getAvailable:t,steps:n,capture:b(r)}}function mp(e,t,n){return fp(kd.msoffice,I(rp,e),[wm({isWord:!0}),Bd(t,n),(u=e,function(e,t){var n,r,o,i=Id(e).getOrDie("Wrong input type for Word Import handler"),a=Jd(t.bundle);return n=a,r=c,o=i.html,u.cleanDocument(o,n,r).then(function(e){if(null==e||0===e.length)return{response:gc([],[]),bundle:bc({})};var t=void 0===r.sanitizer?Nn(r.intraFlag.isMarked):r.sanitizer;return cm(n,e,r.allowLocalImages,t)},function(e){return console.error("PowerPaste error code: WIM01"),{response:pc("errors.paste.process.failure"),bundle:bc({})}})}),ep(c=n),np],!0);var u,c}function pp(e){return fp(kd.image,ip,[!1===e.allowLocalImages?function(e,t){return Fu("errors.local.images.disallowed")}:function(e,t){var n=Td(e).getOrDie("Must have image data for images handler");return qe(F(M(n.images,function(e){return"file"===e.kind&&/image/.test(e.type)}),function(e,t){var n=t.getAsFile();return null!==n?e.concat(n):e},[])).toPromise().then(function(e){var i,a;return{response:(i=[],a=[],P(e,function(o){return Se.cata(o,function(e,t,n){var r=jt.fromTag("img");rt(r,"src",n),i.push(r),a.push(Pf(o,r))},function(e,t,n){console.error("Internal error: Paste operation produced an image URL instead of a Data URI: ",t)})}),gc(i,a)),bundle:bc({})}})},np],!0)}function gp(e){return fp(kd.text,up,[vm(e=void 0===e?4:e),hm],!0)}function vp(e,t,n,r,o){var i,a,m,u,p,c,s,l=ja(o=void 0===o?dc:o,r.baseUrl,r.cacheSuffix),f=Nu(void 0!==r.pasteBinAttrs?r.pasteBinAttrs:{}),d=void 0===r.sanitizer?Nn(r.intraFlag.isMarked):r.sanitizer,g=vi().browser.isIE();return Nf(ce([(i=re.from(r.tabSpaces).getOr(4),fp(kd.plaintext,cp,[vm(i),hm],!0)),mp(l,t,r),fp(kd.googledocs,sp,[wm({isGoogleDocs:!0}),Bd(t,r),Tm,ym(n,e,r),jd,Ed(r),np],!0),fp(kd.native,ap,[(s=r.intraFlag,function(e,t){var n=wd(e).getOrDie("Wrong input type for HTML handler"),r=s.findClipboardTags(Ho(n.container));r.each(function(e){P(e,xu)});var o=r.isSome();return{response:t.response,bundle:bc({isInternal:o})}}),zd(t,r),ym(n,e,r),Ed(r),np],!0),pp(r)],g?[]:[gp(r.tabSpaces)]),dp(kd.fallback,op,[(a=f,m=n,u=e,p=r.intraFlag,c=_f,function(e,t){var n=xd(e).getOrDie("Must pass through event type").nativeEvent,f=c(),d=t.response;return new pt(function(l,e){var t=a(u);t.events.after.bind(function(e){var t,n,r,o,i,a,u,c,s=e.container;Rm.browser.isSafari()&&aa(s,'img[src^="webkit-fake-url"]').isSome()?(t=Rm.deviceType.isWebView()?"webview.imagepaste":"safari.imagepaste",l({response:pc(t),bundle:bc({})})):(m(s),Yi(s,Pm()),u=s,n=((c=(a=Rm).browser).isIE()&&11<=c.version.major?Im:Sm)(u,a),r=N(Bo(s,"*[id]"),function(e){return at(e,"id").exists(function(e){return Lt(e,"docs-internal-guid-")})}),o=Ho(s),i=p.findClipboardTags(o).isSome(),l({response:d,bundle:bc({isWord:n,isGoogleDocs:r,isInternal:i,proxyBin:s,backgroundAssets:f})}))}),f.convert(n),t.run()})}),zd(t,r),bm(e,r.intraFlag,r),Ed(r),np],!1),d)}function hp(){var n={},u={};tinymce.Resource={add:function(e,t){u[e]&&(u[e](t),delete u[e]),n[e]=pt.resolve(t)},load:function(r,o){var i='Script at URL "'+o+'" failed to load',a='Script at URL "'+o+"\" did not call `tinymce.Resource.add('"+r+"', data)` within 1 second";if(void 0!==n[r])return n[r];var e=new pt(function(e,t){var n=function(e,t,n){function r(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o||(o=!0,null!==i&&(clearTimeout(i),i=null),n.apply(void 0,e))}}void 0===n&&(n=1e3);var o=!1,i=null,a=r(e),u=r(t);return{reject:u,resolve:a,start:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o||(i=setTimeout(function(){return u.apply(void 0,e)},n))}}}(e,t);u[r]=n.resolve,tinymce.ScriptLoader.loadScripts([o],function(){return n.start(a)},function(){return n.reject(i)})});return n[r]=e}}}function yp(e,t,n,r){!function(e,t,n){if(!1===tinymce.Env.iOS&&"function"==typeof(null==e?void 0:e.setData))try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(pr(),t),1}catch(e){return}}(e.clipboardData,t.html,t.text)?n(t.html,r):(e.preventDefault(),r())}function bp(a){return function(e,t){var n=a.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),r=a.dom.create("div",{contenteditable:"true","data-mce-bogus":"all"},e);a.dom.setStyles(n,{position:"fixed",top:"50%",left:"-3000px",width:"1000px",overflow:"hidden"}),n.appendChild(r),a.dom.add(a.getBody(),n);var o=a.selection.getRng();r.focus();var i=a.dom.createRng();i.selectNodeContents(r),a.selection.setRng(i),setTimeout(function(){var e;a.selection.setRng(o),null!==(e=n.parentNode)&&void 0!==e&&e.removeChild(n),t()},0)}}function xp(e){var t=e.selection.getContent({contextual:!0}).replace(/ contenteditable="([^"]+)"/g,' data-mce-contenteditable="$1"');return{html:gr+t,text:e.selection.getContent({format:"text"})}}function wp(e){return!e.selection.isCollapsed()||null!==(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t}function Tp(e){return/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e)}function Ip(n){var e=/^<a href="([^"]+)">([^<]+)<\/a>$/.exec(n);return re.from(e).bind(function(e){var t={url:e[1],html:n};return eu(e[1]===e[2],t)})}function Sp(e,t,n){return"extra"in e.undoManager?(e.undoManager.extra(function(){Np(e,t)},n),re.some(!0)):re.none()}function Op(r,e){return Ip(e).bind(function(e){var t,n;return!1===r.selection.isCollapsed()&&Tp(e.url)?Sp(t=r,(n=e).html,function(){t.execCommand("mceInsertLink",!1,n.url)}):re.none()})}function Ap(a,e){return Ip(e).bind(function(e){return r=e.url,o=ee(a),Tp(i=r.toLowerCase())&&N(o,function(e){return Et(i,"."+e.toLowerCase())})?Sp(t=a,(n=e).html,function(){t.insertContent('<img src="'+n.url+'">')}):re.none();var t,n,r,o,i})}function Cp(m,p,e,t,g,v){function h(e,t,n){var r,o=e.getParam("smart_paste",!0,"boolean")?[Op,Ap]:[],i=e,a=t;q(o.concat([(r=n,function(e,t){return e.undoManager.transact(function(){Np(e,t),P(_(e.getBody().getElementsByTagName("*"),jt.fromDom),function(t){ut(t,"data-mce-style")&&!ut(t,"style")&&at(t,"data-mce-style").each(function(e){return rt(t,"style",e)})}),g.prepareImages(r)}),re.some(!0)})]),function(e){return e(i,a)})}function y(){x.on(function(e){return m.selection.moveToBookmark(e)}),x.clear()}var b=xa(function(e){return e.unbind()}),x=wa(),w=(t?t.jsUrl:e).replace(/\/$/,"")+"/"+"/js".replace(/^\//,""),T=Rn(m),I=m.getParam("paste_tab_spaces",4,"number");m.on("init",function(){var e,c,t,s,n={baseUrl:w,cacheSuffix:m.getParam("cache_suffix"),officeStyles:Y(m),htmlStyles:Z(m),gdocsStyles:G(m,"powerpaste_googledocs_import","prompt"),translations:lt,allowLocalImages:m.getParam("powerpaste_allow_local_images",!0,"boolean"),pasteBinAttrs:{"data-mce-bogus":"all",class:"mce-pastebin"},intraFlag:{isMarked:_n,findClipboardTags:function(e){var t=M(e,function(e){return et(e)&&Xe(Oa(e),pr())});return t.length?re.some(t):re.none()}},keepSrc:m.getParam("powerpaste_keep_unsupported_src",!1,"boolean"),cleanFilteredInlineElements:(e=m.getParam("powerpaste_clean_filtered_inline_elements"),O(e)?_(e.split(","),function(e){return e.trim()}):[]),indentUseMargin:m.getParam("indent_use_margin",!1),sanitizer:T,tabSpaces:I},r=v?(s=m,{createDialog:function(){function r(){e.trigger.close()}var o,i,a="",t=wa(),u=(o=ba([{text:"Close",name:"close",type:"custom",primary:!0}]),i=ba({}),{setButtons:function(e){var n={},t=_(e,function(e){var t=e.text;return n[t.toLowerCase()]=e.click,{text:t,name:t.toLowerCase(),type:"custom"}});i.set(n),o.set(t)},getButtons:o.get,getAction:function(e){var t=i.get();return Te(t,e)?re.some(t[e]):re.none()}}),e=So({close:Io([])});return{events:e.registry,setTitle:function(e){return a=e},setContent:function(e){return t.set(e)},setButtons:function(e){u.setButtons(e)},show:function(){t.on(function(e){var t=Mn(e.dom),n={title:a,body:{type:"panel",items:[{type:"htmlpanel",html:t}]},initialData:{},buttons:u.getButtons(),onCancel:r,onAction:function(e,t){u.getAction(t.name).each(C),e.close()}};s.windowManager.open(n)})},hide:S,destroy:function(){t.clear()},reflow:S}}}):(c=m,{createDialog:function(){function t(){n.trigger.close()}function e(){r.off("close",t),r.close("close")}var r,o="",i=[],a=[],u=wa(),n=So({close:Io([])});return{events:n.registry,setTitle:function(e){o=e},setContent:function(e){var t=Mn(e.dom);i=[{type:"container",html:t}],u.set(e)},setButtons:function(e){var t=[];e.forEach(function(e){t.push({text:e.text,ariaLabel:e.text,onclick:e.click})}),a=t},show:function(){0===a.length&&(a=[{text:"Close",onclick:function(){r.close()}}]);var e={title:o,spacing:10,padding:10,minWidth:300,minHeight:100,layout:"flex",items:i,buttons:a};r=c.windowManager.open(e);var n=jt.fromDom(r.getEl());u.on(function(e){var t=aa(n,"."+it(e,"class")).getOrDie("We must find this element or we cannot continue");la(t,e),xu(t)}),r.on("close",t)},hide:function(){e()},destroy:function(){e()},reflow:S}}}),o=jt.fromDom(m.getBody()),a=vp(o,r.createDialog,S,n,kp),u=(t=T,Nf([gp(I)],dp(kd.discard,lp,[xm],!0),t=void 0===T?dr:t));P([a,u],function(e){e.events.cancel.bind(function(){y()}),e.events.error.bind(function(e){y(),m.notificationManager?m.notificationManager.open({text:lt(e.message),type:"error"}):(v?_p:Na).showDialog(m,lt(e.message))}),e.events.insert.bind(function(e){var t=_(e.elements,function(e){return Mn(e.dom)}).join("").replace(/ data-mce-contenteditable="([^"]+)"/g,' contenteditable="$1"');m.focus(),g.importImages(e.assets).get(function(){y(),h(m,bo(m,t,e.isInternal,e.source,e.mode),e.assets),X(m)&&g.uploadImages(e.assets)})}),D(tinymce,"5.7.0")||e.events.block.bind(function(e){m.setProgressState(e.state)})}),m.addCommand("mceInsertClipboardContent",function(e,t){var n,r,o,i;void 0!==t.content?a.pasteCustom((o=t.content,i=void 0===T?dr:T,{getWordData:function(){return re.some(hd({html:i.sanitizeHtml(o),rtf:fd()}))},getGoogleDocsData:re.none,getImage:re.none,getHtml:re.none,getText:re.none,getNative:A("There is no native event"),getOnlyText:re.none,getVoid:A("There is no paste event")})):void 0!==t.text&&u.pasteCustom((n=t.text,r=void 0===T?dr:T,{getWordData:re.none,getGoogleDocsData:re.none,getImage:re.none,getHtml:re.none,getText:function(){return re.some(yd({text:r.sanitizeText(n)}))},getNative:A("There is no native event"),getOnlyText:re.none,getVoid:A("There is no paste event")}))});var i,l,f,d=Ia(o,"paste",function(e){var t;m.readonly||(t=e.raw,(Ep?re.from(t.clipboardData).bind(function(e){return re.from(e.getData("text/html"))}):re.none()).bind(function(e){return Xe(e,"<google-sheets-html-origin")?re.some("googlesheets"):Xe(n=e," data-ccp-props=")&&Xe(n," paraid=")&&/font-family:.+?_MSFontService(&quot;)?[,;]/.test(n)?re.some("mswordonline"):Xe(t=e,"<meta name=ProgId content=Excel.Sheet>")&&!Xe(t,'="urn:schemas-microsoft-com:office:')?re.some("msexcelonline"):re.none();var t,n}).each(function(e){m.fire("PowerPasteTempStats",{source:e})}),x.isSet()||x.set(m.selection.getBookmark(1)),(p.isText()?u:a).paste(e.raw),p.reset())});b.set(d),(i=m).on("cut",(l=i,function(e){wp(l)&&yp(e,xp(l),bp(l),function(){var e,t=vi().browser;t.isChrome()||t.isFirefox()?(e=l.selection.getRng(),tinymce.util.Delay.setEditorTimeout(l,function(){l.selection.setRng(e),l.execCommand("Delete")},0)):l.execCommand("Delete")})})),i.on("copy",(f=i,function(e){wp(f)&&yp(e,xp(f),bp(f),S)}))}),m.on("remove",function(){b.clear()})}function Dp(n,r){var o=ba(n.getParam("paste_as_text",!1,"boolean")),i=ba(!1);n.on("keydown",function(e){var t=e;tinymce.util.VK.metaKeyPressed(t)&&86===t.keyCode&&t.shiftKey&&(i.set(!0),tinymce.Env.ie&&tinymce.Env.ie<10&&(e.preventDefault(),n.fire("paste",{})))});function a(){var e=!o.get();o.set(e),n.fire("PastePlainTextToggle",{state:e}),n.focus()}return{buttonToggle:function(e){var t=!o.get();r?e.setActive(t):this.active(t),a()},toggle:a,reset:function(){i.set(!1)},isText:function(){return i.get()||o.get()}}}var kp=Object.freeze({__proto__:null,loadScript:function(e,t){return tinymce.Resource||hp(),tinymce.Resource.load(e,t)}}),Lp=vi().browser,Ep=!(Lp.isIE()||Lp.isEdge()&&Lp.version.major<16),Np=function(e,t){return e.insertContent(t,{merge:Q(e),paste:!0}),re.some(!0)},_p=Object.freeze({__proto__:null,showDialog:function(e,t){e.windowManager.open({title:"Error",body:{type:"panel",items:[{type:"htmlpanel",html:t}]},initialData:{},buttons:[{text:"OK",type:"cancel",name:"ok",primary:!0}]})}});tinymce.PluginManager.requireLangPack("powerpaste","ar,bg_BG,ca,cs,da,de,el,es,eu,fa,fi,fr_FR,he_IL,hr,hu_HU,id,it,ja,kk,ko_KR,nb_NO,nl,pl,pt_BR,pt_PT,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,zh_CN,zh_TW"),tinymce.PluginManager.add("powerpaste",D(tinymce,"4.0.28")?(console.error('The "powerpaste" plugin requires at least 4.0.28 version of TinyMCE.'),S):function(n,e){function t(t){function e(e){t.setActive(e.state)}return t.setActive(f.isText()),n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}function r(){var t=this;t.active(f.isText()),n.on("PastePlainTextToggle",function(e){t.active(e.state)})}var o,i,a,u,c,s,l=!D(tinymce,"5.0.0"),f=Dp(n,l);tinymce.Env.ie&&tinymce.Env.ie<10?function(t,e){function n(t){return function(e){t(e)}}var r,o,i,a=this,u=ho(t,lt,!1),c=(o=u.showDialog,i=e,function(e){ya(r,o,i,e)});(r=t).on("paste",n(c));var s,l,f,d=(l=u.showDialog,f=e,function(e){(tinymce.isOpera||0<navigator.userAgent.indexOf("Firefox/2"))&&((tinymce.isMac?e.metaKey:e.ctrlKey)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)&&ya(s,l,f,e)});(s=t).on("keydown",n(d)),t.addCommand("mceInsertClipboardContent",function(e,t){u.showDialog(t.content||t)}),J(t)&&t.on("PastePreProcess",function(e){J(t).call(a,a,e)})}(n,f):(s=(void 0!==n.uploadImages?va:function(e){if(O(K(e))){var t={url:K(e),basePath:e.getParam("images_upload_base_path"),credentials:e.getParam("images_upload_credentials",!1)};return ha(e,t)}var o;return{importImages:function(){return bt([])},uploadImages:S,prepareImages:function(e){P(e,function(e){Se.cata(e,function(e,t,n){var r=ze(t);P(o.dom.select('img[src="'+n+'"]'),function(e){o.dom.setAttrib(e,"src",r)})},S)})},getLocalURL:va(o=e).getLocalURL}})(n),Cp(n,f,e,void 0,s,l),n.getParam("powerpaste_block_drop",!1,"boolean")?(c=n).on("init",function(){te(c,c.getBody()),c.inline||te(c,c.getDoc())}):wo(n,0,s,l)),i=J(o=n),a=o.getParam("paste_postprocess"),i&&o.on("PastePreProcess",function(e){return i.call(o,xo(o),e)}),a&&o.on("PastePostProcess",function(e){return a.call(o,xo(o),e)}),l?(n.ui.registry.addToggleButton("pastetext",{icon:"paste-text",tooltip:"Paste as text",onAction:f.buttonToggle,onSetup:t}),n.ui.registry.addToggleMenuItem("pastetext",{icon:"paste-text",text:"Paste as text",onAction:f.buttonToggle,onSetup:t})):(n.addButton("pastetext",{icon:"pastetext",tooltip:"Paste as text",onclick:f.buttonToggle,onPostRender:r}),n.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,onclick:f.buttonToggle,onPostRender:r})),u=f,n.addCommand("mceTogglePlainTextPaste",u.toggle)})}();
