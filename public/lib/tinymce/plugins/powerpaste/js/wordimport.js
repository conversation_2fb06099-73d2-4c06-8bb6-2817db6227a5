// Generated by js_of_ocaml 3.5.2
(function(a){var
  b=undefined}(function(){return this}()));(function(n){"use strict";var
  gO="q",eh="i",aj="img",gn="Invalid_argument",cT="Map.bal",d9='"',cY=",",dV=1255,d8="<",bK=255,gA=0x800,dU="jsError",cL=0x8000,gN="data-text-indent-alt",b4="th",aw=256,W="style",ak="!",dT="\n}",ga="End_of_file",gm="align",gz="text-align",cK="center",f$="Failure",bc="label",aL="col",Q=0xff,f_="data-converted-paragraph",r=50834029,bk="title",dS="code",d1="del",k=-579472809,b3="font",gl="strike",E="contents",dR="dfn",bO="height",gk="abbr",eg="samp",b=-841728391,d7=0xf,C=698996132,b2=128,f9=0xdc00,gj="Sys_blocked_io",aK="p",ef="start",gM="fd ",ay="form",z=248,ee="DeltaViewInsertion",ed="var",f8="data-list-level",gy="Division_by_zero",ec=" {\n",b1=">",gL=1027,gx="<\/",eb="em",f7="Sys_error",dQ="cite",bf="noframes",c3="caption",X="ol",N=246,a2="td",aJ="object",d0="[endif]",b0="noscript",cX="optgroup",dP="kbd",gw="buffer.ml",bV="h5",cJ="int_of_string",ax="table",cI="dt",bM="tbody",bN=1024,P="script",f6="display",gi=" : flags Open_rdonly and Open_wronly are not compatible",cW="colgroup",gK="mso-list",c2="button",bZ="h1",bJ="h6",av="head",bU="h3",gJ="([^/]*)",f5=512,gh="-",gv="br",f4=0x7ff0,gg=" : file already exists",ea="b",au="body",dO="u",gf="startfragment",dZ="strong",gI="Out_of_memory",bI="\n",c1="big",bH="h2",Y="ul",cV=254,gH="index out of bounds",f3="bdo",gu=100,bT="pre",cS="select",V="tr",gt=" : flags Open_text and Open_binary are not compatible",cH="area",d$="@",bS="tfoot",bi="isindex",bj=0xffff,cR="basefont",cG="input",cQ="none",a9="span",dN=3257036,at=-804212868,bG="thead",d6=17731,f2="lexing: empty token",cP="small",aU="--",O="li",cO="menu",bL=1000,g="",gs="Stack_overflow",bY="sub",dM="v:shape",bF="address",dY="^",bh=749117977,bb=0x3f,gq="tab-interval",gr="Not_found",cU=65599,aX="link",ge="src",c0="frame",bR="dd",gd="Match_failure",f0=", ",dL="ins",f1=1252,bX="html",fZ="static/",h=781665294,cZ="iframe",gG=252,d_="tt",gF=0xf0,gE="<![endif]",aZ="dir",dK="data-list-type",ba="param",a1="width",bE="class",gp=0x3FFFFFFF,go=1026,gD="map",a0="fieldset",t="src/main/re/css_parser.ml",aW="a",bQ="sup",d5="?",cF="list-style",a8=" ",Z=0x80,fY="Undefined_recursive_module",be="base",cN="legend",aV=-810056052,aY=":",gC="list-style-type",bd="option",cM="applet",gc=0xe0,bP="hr",fX="cleanDocument",bD="h4",l=-936778451,cE="Set.bal",gb=0xdfff,a$="div",as="/",d4="compare: functional value",gB="Assert_failure",dX="s",aI="meta",dW=1073741823,d3="true",j=870530776,cD="textarea",a_="dl",aH=250,d2="acronym",bg="frameset",bW="blockquote";function
  sl(d,b,e,c,f){if(c<=b)for(var
  a=1;a<=f;a++)e[c+a]=d[b+a];else
  for(var
  a=f;a>=1;a--)e[c+a]=d[b+a];return 0}function
  ew(d,e,c){var
  b=new
  Array(c);for(var
  a=0;a<c;a++)b[a]=d[e+a];return b}function
  eu(b,c,a){var
  d=String.fromCharCode;if(c==0&&a<=4096&&a==b.length)return d.apply(null,b);var
  e=g;for(;0<a;c+=bN,a-=bN)e+=d.apply(null,ew(b,c,Math.min(a,bN)));return e}function
  c7(b){if(n.Uint8Array)var
  c=new(n.Uint8Array)(b.l);else
  var
  c=new
  Array(b.l);var
  e=b.c,d=e.length,a=0;for(;a<d;a++)c[a]=e.charCodeAt(a);for(d=b.l;a<d;a++)c[a]=0;b.c=c;b.t=4;return c}function
  az(d,e,b,f,c){if(c==0)return 0;if(f==0&&(c>=b.l||b.t==2&&c>=b.c.length)){b.c=d.t==4?eu(d.c,e,c):e==0&&d.c.length==c?d.c:d.c.substr(e,c);b.t=b.c.length==b.l?0:2}else
  if(b.t==2&&f==b.c.length){b.c+=d.t==4?eu(d.c,e,c):e==0&&d.c.length==c?d.c:d.c.substr(e,c);b.t=b.c.length==b.l?0:2}else{if(b.t!=4)c7(b);var
  g=d.c,h=b.c;if(d.t==4)if(f<=e)for(var
  a=0;a<c;a++)h[f+a]=g[e+a];else
  for(var
  a=c-1;a>=0;a--)h[f+a]=g[e+a];else{var
  i=Math.min(c,g.length-e);for(var
  a=0;a<i;a++)h[f+a]=g.charCodeAt(e+a);for(;a<c;a++)h[f+a]=0}}return 0}function
  bm(d,a,e,b,c){return az(d,a,e,b,c)}function
  b6(a){return a}function
  c5(a,b){switch(a.t&6){default:if(b>=a.c.length)return 0;case
  0:return a.c.charCodeAt(b);case
  4:return a.c[b]}}function
  gS(a,c,b){b&=Q;if(a.t!=4){if(c==a.c.length){a.c+=String.fromCharCode(b);if(c+1==a.l)a.t=0;return 0}c7(a)}a.c[c]=b;return 0}function
  tc(c,e){var
  d=c.length,b=new
  Array(d+1),a=0;for(;a<d;a++)b[a]=c[a];b[a]=e;return b}function
  ab(b,a){if(b.fun)return ab(b.fun,a);var
  c=b.length,d=a.length,e=c-d;if(e==0)return b.apply(null,a);else
  if(e<0)return ab(b.apply(null,ew(a,0,c)),ew(a,c,d-c));else
  return function(c){return ab(b,tc(a,c))}}function
  s0(b,a){throw[0,b,a]}function
  hb(a,b){if(a==0)return g;if(b.repeat)return b.repeat(a);var
  c=g,d=0;if(a==0)return c;for(;;){if(a&1)c+=b;a>>=1;if(a==0)return c;b+=b;d++;if(d==9)b.slice(0,1)}}function
  aA(a){if(a.t==2)a.c+=hb(a.l-a.c.length,"\0");else
  a.c=eu(a.c,0,a.c.length);a.t=0}function
  g0(a){if(a.length<24){for(var
  b=0;b<a.length;b++)if(a.charCodeAt(b)>127)return false;return true}else
  return!/[^\x00-\x7f]/.test(a)}function
  ev(e){for(var
  k=g,c=g,h,f,i,a,b=0,j=e.length;b<j;b++){f=e.charCodeAt(b);if(f<Z){for(var
  d=b+1;d<j&&(f=e.charCodeAt(d))<Z;d++);if(d-b>f5){c.substr(0,1);k+=c;c=g;k+=e.slice(b,d)}else
  c+=e.slice(b,d);if(d==j)break;b=d}a=1;if(++b<j&&((i=e.charCodeAt(b))&-64)==b2){h=i+(f<<6);if(f<gc){a=h-0x3080;if(a<Z)a=1}else{a=2;if(++b<j&&((i=e.charCodeAt(b))&-64)==b2){h=i+(h<<6);if(f<gF){a=h-0xe2080;if(a<gA||a>=0xd7ff&&a<0xe000)a=2}else{a=3;if(++b<j&&((i=e.charCodeAt(b))&-64)==b2&&f<0xf5){a=i-0x3c82080+(h<<6);if(a<0x10000||a>0x10ffff)a=3}}}}}if(a<4){b-=a;c+="\ufffd"}else
  if(a>bj)c+=String.fromCharCode(0xd7c0+(a>>10),f9+(a&0x3FF));else
  c+=String.fromCharCode(a);if(c.length>bN){c.substr(0,1);k+=c;c=g}}return k+c}function
  s7(a){switch(a.t){case
  9:return a.c;default:aA(a);case
  0:if(g0(a.c)){a.t=9;return a.c}a.t=8;case
  8:return ev(a.c)}}function
  R(c,a,b){this.t=c;this.c=a;this.l=b}R.prototype.toString=function(){return s7(this)};R.prototype.slice=function(){var
  a=this.t==4?this.c.slice():this.c;return new
  R(this.t,a,this.l)};function
  a(a){return new
  R(0,a,a.length)}function
  es(c,b){s0(c,a(b))}var
  v=[0];function
  H(a){es(v.Invalid_argument,a)}function
  b5(){H(gH)}function
  u(a,b){if(b>>>0>=a.length-1)b5();return a}var
  tb=Math.log2&&Math.log2(1.1235582092889474E+307)==1020;function
  ta(a){if(tb)return Math.floor(Math.log2(a));var
  b=0;if(a==0)return-Infinity;if(a>=1)while(a>=2){a/=2;b++}else
  while(a<1){a*=2;b--}return b}function
  em(c){var
  a=new(n.Float32Array)(1);a[0]=c;var
  b=new(n.Int32Array)(a.buffer);return b[0]|0}function
  c8(b,c,a){return[bK,b,c,a]}function
  b8(a){if(!isFinite(a)){if(isNaN(a))return c8(1,0,f4);return a>0?c8(0,0,f4):c8(0,0,0xfff0)}var
  f=a==0&&1/a==-Infinity?cL:a>=0?0:cL;if(f)a=-a;var
  b=ta(a)+1023;if(b<=0){b=0;a/=Math.pow(2,-go)}else{a/=Math.pow(2,b-gL);if(a<16){a*=2;b-=1}if(b==0)a/=2}var
  d=Math.pow(2,24),c=a|0;a=(a-c)*d;var
  e=a|0;a=(a-e)*d;var
  g=a|0;c=c&d7|f|b<<4;return c8(g,e,c)}function
  bp(a){return[a[3]>>8,a[3]&Q,a[2]>>16,a[2]>>8&Q,a[2]&Q,a[1]>>16,a[1]>>8&Q,a[1]&Q]}function
  sq(d,b,g){d.write(32,b.dims.length);d.write(32,b.kind|b.layout<<8);for(var
  a=0;a<b.dims.length;a++)d.write(32,b.dims[a]);switch(b.kind){case
  2:case
  3:case
  12:for(var
  a=0;a<b.data.length;a++)d.write(8,b.data[a]);break;case
  4:case
  5:for(var
  a=0;a<b.data.length;a++)d.write(16,b.data[a]);break;case
  6:for(var
  a=0;a<b.data.length;a++)d.write(32,b.data[a]);break;case
  8:case
  9:d.write(8,0);for(var
  a=0;a<b.data.length;a++)d.write(32,b.data[a]);break;case
  7:for(var
  a=0;a<b.data.length/2;a++){var
  e=bp(b.get(a));for(var
  c=0;c<8;c++)d.write(8,e[c])}break;case
  1:for(var
  a=0;a<b.data.length;a++){var
  e=bp(b8(b.get(a)));for(var
  c=0;c<8;c++)d.write(8,e[c])}break;case
  0:for(var
  a=0;a<b.data.length;a++){var
  e=em(b.get(a));d.write(32,e)}break;case
  10:for(var
  a=0;a<b.data.length/2;a++){var
  c=b.get(a);d.write(32,em(c[1]));d.write(32,em(c[2]))}break;case
  11:for(var
  a=0;a<b.data.length/2;a++){var
  f=b.get(a),e=bp(b8(f[1]));for(var
  c=0;c<8;c++)d.write(8,e[c]);var
  e=bp(b8(f[2]));for(var
  c=0;c<8;c++)d.write(8,e[c])}break}g[0]=(4+b.dims.length)*4;g[1]=(4+b.dims.length)*8}function
  gR(a){switch(a){case
  7:case
  10:case
  11:return 2;default:return 1}}function
  sn(c,e){var
  b=n,a;switch(c){case
  0:a=b.Float32Array;break;case
  1:a=b.Float64Array;break;case
  2:a=b.Int8Array;break;case
  3:a=b.Uint8Array;break;case
  4:a=b.Int16Array;break;case
  5:a=b.Uint16Array;break;case
  6:a=b.Int32Array;break;case
  7:a=b.Int32Array;break;case
  8:a=b.Int32Array;break;case
  9:a=b.Int32Array;break;case
  10:a=b.Float32Array;break;case
  11:a=b.Float64Array;break;case
  12:a=b.Uint8Array;break}if(!a)H("Bigarray.create: unsupported kind");var
  d=new
  a(e*gR(c));return d}function
  en(c){var
  a=new(n.Int32Array)(1);a[0]=c;var
  b=new(n.Float32Array)(a.buffer);return b[0]}function
  b9(a){return[bK,a[7]|a[6]<<8|a[5]<<16,a[4]|a[3]<<8|a[2]<<16,a[1]|a[0]<<8]}function
  eo(d){var
  f=d[1],g=d[2],b=d[3],c=(b&0x7fff)>>4;if(c==2047)return(f|g|b&d7)==0?b&cL?-Infinity:Infinity:NaN;var
  e=Math.pow(2,-24),a=(f*e+g)*e+(b&d7);if(c>0){a+=16;a*=Math.pow(2,c-gL)}else
  a*=Math.pow(2,-go);if(b&cL)a=-a;return a}function
  ej(b){var
  d=b.length,c=1;for(var
  a=0;a<d;a++){if(b[a]<0)H("Bigarray.create: negative dimension");c=c*b[a]}return c}function
  sD(b,a){return[bK,b&0xffffff,b>>>24&Q|(a&bj)<<8,a>>>16&bj]}function
  ep(a){return a[2]>>>8&bj|a[3]<<16}function
  eq(a){return a[1]|(a[2]&Q)<<24}function
  aM(c,d,b,a){this.kind=c;this.layout=d;this.dims=b;this.data=a}aM.prototype.caml_custom="_bigarray";aM.prototype.offset=function(b){var
  c=0;if(typeof
  b==="number")b=[b];if(!(b
  instanceof
  Array))H("bigarray.js: invalid offset");if(this.dims.length!=b.length)H("Bigarray.get/set: bad number of dimensions");if(this.layout==0)for(var
  a=0;a<this.dims.length;a++){if(b[a]<0||b[a]>=this.dims[a])b5();c=c*this.dims[a]+b[a]}else
  for(var
  a=this.dims.length-1;a>=0;a--){if(b[a]<1||b[a]>this.dims[a])b5();c=c*this.dims[a]+(b[a]-1)}return c};aM.prototype.get=function(a){switch(this.kind){case
  7:var
  d=this.data[a*2+0],b=this.data[a*2+1];return sD(d,b);case
  10:case
  11:var
  e=this.data[a*2+0],c=this.data[a*2+1];return[cV,e,c];default:return this.data[a]}};aM.prototype.set=function(a,b){switch(this.kind){case
  7:this.data[a*2+0]=eq(b);this.data[a*2+1]=ep(b);break;case
  10:case
  11:this.data[a*2+0]=b[1];this.data[a*2+1]=b[2];break;default:this.data[a]=b;break}return 0};aM.prototype.fill=function(b){switch(this.kind){case
  7:var
  c=eq(b),e=ep(b);if(c==e)this.data.fill(c);else
  for(var
  a=0;a<this.data.length;a++)this.data[a]=a%2==0?c:e;break;case
  10:case
  11:var
  d=b[1],f=b[2];if(d==f)this.data.fill(d);else
  for(var
  a=0;a<this.data.length;a++)this.data[a]=a%2==0?d:f;break;default:this.data.fill(b);break}};aM.prototype.compare=function(b,g){if(this.layout!=b.layout||this.kind!=b.kind){var
  e=this.kind|this.layout<<8,f=b.kind|b.layout<<8;return f-e}if(this.dims.length!=b.dims.length)return b.dims.length-this.dims.length;for(var
  a=0;a<this.dims.length;a++)if(this.dims[a]!=b.dims[a])return this.dims[a]<b.dims[a]?-1:1;switch(this.kind){case
  0:case
  1:case
  10:case
  11:var
  c,d;for(var
  a=0;a<this.data.length;a++){c=this.data[a];d=b.data[a];if(c<d)return-1;if(c>d)return 1;if(c!=d){if(!g)return NaN;if(c==c)return 1;if(d==d)return-1}}break;case
  7:for(var
  a=0;a<this.data.length;a+=2){if(this.data[a+1]<b.data[a+1])return-1;if(this.data[a+1]>b.data[a+1])return 1;if(this.data[a]>>>0<b.data[a]>>>0)return-1;if(this.data[a]>>>0>b.data[a]>>>0)return 1}break;case
  2:case
  3:case
  4:case
  5:case
  6:case
  8:case
  9:case
  12:for(var
  a=0;a<this.data.length;a++){if(this.data[a]<b.data[a])return-1;if(this.data[a]>b.data[a])return 1}break}return 0};function
  bl(c,d,b,a){this.kind=c;this.layout=d;this.dims=b;this.data=a}bl.prototype=new
  aM();bl.prototype.offset=function(a){if(typeof
  a!=="number")if(a
  instanceof
  Array&&a.length==1)a=a[0];else
  H("Ml_Bigarray_c_1_1.offset");if(a<0||a>=this.dims[0])b5();return a};bl.prototype.get=function(a){return this.data[a]};bl.prototype.set=function(a,b){this.data[a]=b;return 0};bl.prototype.fill=function(a){this.data.fill(a);return 0};function
  gQ(c,d,a,b){var
  e=gR(c);if(ej(a)*e!=b.length)H("length doesn't match dims");if(d==0&&a.length==1&&e==1)return new
  bl(c,d,a,b);return new
  aM(c,d,a,b)}function
  ac(a){es(v.Failure,a)}function
  so(b,r){var
  j=b.read32s();if(j<0||j>16)ac("input_value: wrong number of bigarray dimensions");var
  o=b.read32s(),i=o&Q,n=o>>8&1,h=[];for(var
  a=0;a<j;a++)h.push(b.read32u());var
  d=ej(h),f=sn(i,d),g=gQ(i,n,h,f);switch(i){case
  2:for(var
  a=0;a<d;a++)f[a]=b.read8s();break;case
  3:case
  12:for(var
  a=0;a<d;a++)f[a]=b.read8u();break;case
  4:for(var
  a=0;a<d;a++)f[a]=b.read16s();break;case
  5:for(var
  a=0;a<d;a++)f[a]=b.read16u();break;case
  6:for(var
  a=0;a<d;a++)f[a]=b.read32s();break;case
  8:case
  9:var
  q=b.read8u();if(q)ac("input_value: cannot read bigarray with 64-bit OCaml ints");for(var
  a=0;a<d;a++)f[a]=b.read32s();break;case
  7:var
  e=new
  Array(8);for(var
  a=0;a<d;a++){for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  p=b9(e);g.set(a,p)}break;case
  1:var
  e=new
  Array(8);for(var
  a=0;a<d;a++){for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  k=eo(b9(e));g.set(a,k)}break;case
  0:for(var
  a=0;a<d;a++){var
  k=en(b.read32s());g.set(a,k)}break;case
  10:for(var
  a=0;a<d;a++){var
  m=en(b.read32s()),l=en(b.read32s());g.set(a,[cV,m,l])}break;case
  11:var
  e=new
  Array(8);for(var
  a=0;a<d;a++){for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  m=eo(b9(e));for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  l=eo(b9(e));g.set(a,[cV,m,l])}break}r[0]=(4+j)*4;return gQ(i,n,h,f)}function
  sm(a,b,c){return a.compare(b,c)}if(!Math.imul)Math.imul=function(b,a){a|=0;return((b>>16)*a<<16)+(b&bj)*a|0};var
  c_=Math.imul;function
  G(b,a){a=c_(a,0xcc9e2d51|0);a=a<<15|a>>>32-15;a=c_(a,0x1b873593);b^=a;b=b<<13|b>>>32-13;return(b+(b<<2)|0)+(0xe6546b64|0)|0}function
  gW(a,b){a=G(a,eq(b));a=G(a,ep(b));return a}function
  el(a,b){return gW(a,b8(b))}function
  sp(c){var
  b=ej(c.dims),d=0;switch(c.kind){case
  2:case
  3:case
  12:if(b>aw)b=aw;var
  e=0,a=0;for(a=0;a+4<=c.data.length;a+=4){e=c.data[a+0]|c.data[a+1]<<8|c.data[a+2]<<16|c.data[a+3]<<24;d=G(d,e)}e=0;switch(b&3){case
  3:e=c.data[a+2]<<16;case
  2:e|=c.data[a+1]<<8;case
  1:e|=c.data[a+0];d=G(d,e)}break;case
  4:case
  5:if(b>b2)b=b2;var
  e=0,a=0;for(a=0;a+2<=c.data.length;a+=2){e=c.data[a+0]|c.data[a+1]<<16;d=G(d,e)}if((b&1)!=0)d=G(d,c.data[a]);break;case
  6:if(b>64)b=64;for(var
  a=0;a<b;a++)d=G(d,c.data[a]);break;case
  8:case
  9:if(b>64)b=64;for(var
  a=0;a<b;a++)d=G(d,c.data[a]);break;case
  7:if(b>32)b=32;b*=2;for(var
  a=0;a<b;a++)d=G(d,c.data[a]);break;case
  10:b*=2;case
  0:if(b>64)b=64;for(var
  a=0;a<b;a++)d=el(d,c.data[a]);break;case
  11:b*=2;case
  1:if(b>32)b=32;for(var
  a=0;a<b;a++)d=el(d,c.data[a]);break}return d}function
  sC(a,b){b[0]=4;return a.read32s()}function
  sT(a,b){switch(a.read8u()){case
  1:b[0]=4;return a.read32s();case
  2:ac("input_value: native integer value too large");default:ac("input_value: ill-formed native integer")}}function
  sG(c,d){var
  b=new
  Array(8);for(var
  a=0;a<8;a++)b[a]=c.read8u();d[0]=8;return b9(b)}function
  sF(e,d,b){var
  c=bp(d);for(var
  a=0;a<8;a++)e.write(8,c[a]);b[0]=8;b[1]=8}function
  gX(a,b){var
  c=a[3]<<16,d=b[3]<<16;if(c>d)return 1;if(c<d)return-1;if(a[2]>b[2])return 1;if(a[2]<b[2])return-1;if(a[1]>b[1])return 1;if(a[1]<b[1])return-1;return 0}function
  sE(a){var
  c=a[1]|(a[2]&Q)<<24,b=a[2]>>>8&bj|a[3]<<16;return c^b}var
  aN={"_j":{deserialize:sG,serialize:sF,fixed_length:8,compare:gX,hash:sE},"_i":{deserialize:sC,fixed_length:4},"_n":{deserialize:sT,fixed_length:4},"_bigarray":{deserialize:so,serialize:sq,compare:sm,hash:sp}};function
  ek(a){return aN[a.caml_custom]&&aN[a.caml_custom].compare}function
  gT(f,c,d,e){var
  b=ek(c);if(b){var
  a=d>0?b(c,f,e):b(f,c,e);if(e&&a!=a)return d;if(+a!=+a)return+a;if((a|0)!=0)return a|0}return d}function
  gU(a){if(typeof
  a==="number")return bL;else
  if(a
  instanceof
  R)return gG;else
  if(a
  instanceof
  Array&&a[0]===a[0]>>>0&&a[0]<=bK){var
  b=a[0]|0;return b==cV?0:b}else
  if(a
  instanceof
  String)return f1;else
  if(typeof
  a=="string")return f1;else
  if(a
  instanceof
  Number)return bL;else
  if(a&&a.caml_custom)return dV;else
  if(a&&a.compare)return 1256;else
  if(typeof
  a=="function")return 1247;else
  if(typeof
  a=="symbol")return 1251;return 1001}function
  gY(a,b){if(a<b)return-1;if(a==b)return 0;return 1}function
  b_(a,b){a.t&6&&aA(a);b.t&6&&aA(b);return a.c<b.c?-1:a.c>b.c?1:0}function
  c6(a,b,d){var
  e=[];for(;;){if(!(d&&a===b)){var
  f=gU(a);if(f==aH){a=a[1];continue}var
  g=gU(b);if(g==aH){b=b[1];continue}if(f!==g){if(f==bL){if(g==dV)return gT(a,b,-1,d);return-1}if(g==bL){if(f==dV)return gT(b,a,1,d);return 1}return f<g?-1:1}switch(f){case
  247:H(d4);break;case
  248:var
  c=gY(a[2],b[2]);if(c!=0)return c|0;break;case
  249:H(d4);break;case
  250:H("equal: got Forward_tag, should not happen");break;case
  251:H("equal: abstract value");break;case
  252:if(a!==b){var
  c=b_(a,b);if(c!=0)return c|0}break;case
  253:H("equal: got Double_tag, should not happen");break;case
  254:H("equal: got Double_array_tag, should not happen");break;case
  255:var
  c=gX(a,b);if(c!=0)return c|0;break;case
  1247:H(d4);break;case
  1255:var
  i=ek(a);if(i!=ek(b))return a.caml_custom<b.caml_custom?-1:1;if(!i)H("compare: abstract value");var
  c=i(a,b,d);if(c!=c)return d?-1:c;if(c!==(c|0))return-1;if(c!=0)return c|0;break;case
  1256:var
  c=a.compare(b,d);if(c!=c)return d?-1:c;if(c!==(c|0))return-1;if(c!=0)return c|0;break;case
  1000:a=+a;b=+b;if(a<b)return-1;if(a>b)return 1;if(a!=b){if(!d)return NaN;if(a==a)return 1;if(b==b)return-1}break;case
  1001:if(a<b)return-1;if(a>b)return 1;if(a!=b){if(!d)return NaN;if(a==a)return 1;if(b==b)return-1}break;case
  1251:if(a!==b){if(!d)return NaN;return 1}break;case
  1252:var
  a=a.toString(),b=b.toString();if(a!==b){if(a<b)return-1;if(a>b)return 1}break;case
  246:case
  254:default:if(a.length!=b.length)return a.length<b.length?-1:1;if(a.length>1)e.push(a,b,1);break}}if(e.length==0)return 0;var
  h=e.pop();b=e.pop();a=e.pop();if(h+1<a.length)e.push(a,b,h+1);a=a[h];b=b[h]}}function
  bn(a,b){return c6(a,b,true)}function
  J(a){if(a<0)H("Bytes.create");return new
  R(a?2:9,g,a)}function
  bo(a,b){return+(c6(a,b,false)==0)}function
  st(a,c,b,d){if(b>0)if(c==0&&(b>=a.l||a.t==2&&b>=a.c.length))if(d==0){a.c=g;a.t=2}else{a.c=hb(b,String.fromCharCode(d));a.t=b==a.l?0:2}else{if(a.t!=4)c7(a);for(b+=c;c<b;c++)a.c[c]=d}return 0}var
  g8=0;function
  S(){return g8++}if(n.process&&n.process.cwd)var
  b7=n.process.cwd().replace(/\\/g,as);else
  var
  b7="/static";if(b7.slice(-1)!==as)b7+=as;function
  sN(a){a=a
  instanceof
  R?a.toString():a;if(a.charCodeAt(0)!=47)a=b7+a;var
  d=a.split(as),b=[];for(var
  c=0;c<d.length;c++)switch(d[c]){case"..":if(b.length>1)b.pop();break;case".":break;case"":if(b.length==0)b.push(g);break;default:b.push(d[c]);break}b.orig=a;return b}function
  $(a){es(v.Sys_error,a)}function
  sY(a){a=a
  instanceof
  R?a.toString():a;$(a+": No such file or directory")}function
  hc(a){return new
  R(4,a,a.length)}function
  et(){H(gH)}function
  sr(b,a){if(a>>>0>=b.l)et();return c5(b,a)}function
  aQ(a){return a.l}function
  gP(){}function
  F(a){this.data=a}F.prototype=new
  gP();F.prototype.truncate=function(a){var
  b=this.data;this.data=J(a|0);az(b,0,this.data,0,a)};F.prototype.length=function(){return aQ(this.data)};F.prototype.write=function(b,d,g,a){var
  c=this.length();if(b+a>=c){var
  e=J(b+a),f=this.data;this.data=e;az(f,0,this.data,0,c)}az(d,g,this.data,b,a);return 0};F.prototype.read=function(c,a,d,b){var
  e=this.length();az(this.data,c,a,d,b);return 0};F.prototype.read_one=function(a){return sr(this.data,a)};F.prototype.close=function(){};F.prototype.constructor=F;function
  _(b,a){this.content={};this.root=b;this.lookupFun=a}_.prototype.nm=function(a){return this.root+a};_.prototype.lookup=function(b){if(!this.content[b]&&this.lookupFun){var
  c=this.lookupFun(a(this.root),a(b));if(c!==0)this.content[b]=new
  F(c[1])}};_.prototype.exists=function(a){if(a==g)return 1;var
  c=a+as,d=new
  RegExp(dY+c);for(var
  b
  in
  this.content)if(b.match(d))return 1;this.lookup(a);return this.content[a]?1:0};_.prototype.readdir=function(c){var
  f=c==g?g:c+as,h=new
  RegExp(dY+f+gJ),d={},b=[];for(var
  e
  in
  this.content){var
  a=e.match(h);if(a&&!d[a[1]]){d[a[1]]=true;b.push(a[1])}}return b};_.prototype.is_dir=function(a){var
  d=a==g?g:a+as,e=new
  RegExp(dY+d+gJ),f=[];for(var
  c
  in
  this.content){var
  b=c.match(e);if(b)return 1}return 0};_.prototype.unlink=function(a){var
  b=this.content[a]?true:false;delete
  this.content[a];return b};_.prototype.open=function(a,b){if(b.rdonly&&b.wronly)$(this.nm(a)+gi);if(b.text&&b.binary)$(this.nm(a)+gt);this.lookup(a);if(this.content[a]){if(this.is_dir(a))$(this.nm(a)+" : is a directory");if(b.create&&b.excl)$(this.nm(a)+gg);var
  c=this.content[a];if(b.truncate)c.truncate();return c}else
  if(b.create){this.content[a]=new
  F(J(0));return this.content[a]}else
  sY(this.nm(a))};_.prototype.register=function(c,b){if(this.content[c])$(this.nm(c)+gg);if(b
  instanceof
  R)this.content[c]=new
  F(b);else
  if(b
  instanceof
  Array)this.content[c]=new
  F(hc(b));else
  if(b.toString){var
  d=a(b.toString());this.content[c]=new
  F(d)}};_.prototype.constructor=_;function
  c4(a){if(a.t!=4)c7(a);return a.c}function
  ss(b,a,c){if(a>>>0>=b.l)et();return gS(b,a,c)}var
  ei=n.Buffer;function
  am(a){this.fs=require("fs");this.fd=a}am.prototype=new
  gP();am.prototype.truncate=function(a){this.fs.ftruncateSync(this.fd,a|0)};am.prototype.length=function(){return this.fs.fstatSync(this.fd).size};am.prototype.write=function(f,b,c,e){var
  a=c4(b);if(!(a
  instanceof
  n.Uint8Array))a=new(n.Uint8Array)(a);var
  d=ei.from(a);this.fs.writeSync(this.fd,d,c,e,f);return 0};am.prototype.read=function(g,d,c,f){var
  a=c4(d);if(!(a
  instanceof
  n.Uint8Array))a=new(n.Uint8Array)(a);var
  e=ei.from(a);this.fs.readSync(this.fd,e,c,f,g);for(var
  b=0;b<f;b++)ss(d,c+b,e[c+b]);return 0};am.prototype.read_one=function(c){var
  b=new(n.Uint8Array)(1),a=ei.from(b);this.fs.readSync(this.fd,a,0,1,c);return a[0]};am.prototype.close=function(){this.fs.closeSync(this.fd)};am.prototype.constructor=am;function
  al(a){this.fs=require("fs");this.root=a}al.prototype.nm=function(a){return this.root+a};al.prototype.exists=function(a){return this.fs.existsSync(this.nm(a))?1:0};al.prototype.readdir=function(a){return this.fs.readdirSync(this.nm(a))};al.prototype.is_dir=function(a){return this.fs.statSync(this.nm(a)).isDirectory()?1:0};al.prototype.unlink=function(a){var
  b=this.fs.existsSync(this.nm(a))?1:0;this.fs.unlinkSync(this.nm(a));return b};al.prototype.open=function(f,c){var
  a=require("constants"),b=0;for(var
  e
  in
  c)switch(e){case"rdonly":b|=a.O_RDONLY;break;case"wronly":b|=a.O_WRONLY;break;case"append":b|=a.O_WRONLY|a.O_APPEND;break;case"create":b|=a.O_CREAT;break;case"truncate":b|=a.O_TRUNC;break;case"excl":b|=a.O_EXCL;break;case"binary":b|=a.O_BINARY;break;case"text":b|=a.O_TEXT;break;case"nonblock":b|=a.O_NONBLOCK;break}var
  d=this.fs.openSync(this.nm(f),b);return new
  am(d)};al.prototype.rename=function(b,a){this.fs.renameSync(this.nm(b),this.nm(a))};al.prototype.constructor=al;var
  br=b7.match(/[^\/]*\//)[0];function
  s9(){return typeof
  n.process!=="undefined"&&typeof
  n.process.versions!=="undefined"&&typeof
  n.process.versions.node!=="undefined"&&n.process.platform!=="browser"}var
  ca=[];if(s9())ca.push({path:br,device:new
  al(br)});else
  ca.push({path:br,device:new
  _(br)});ca.push({path:br+fZ,device:new
  _(br+fZ)});function
  hf(b){var
  f=sN(b),b=f.join(as),e=b+as,c;for(var
  d=0;d<ca.length;d++){var
  a=ca[d];if(e.search(a.path)==0&&(!c||c.path.length<a.path.length))c={path:a.path,device:a.device,rest:b.substring(a.path.length,b.length)}}return c}function
  gV(c,b){var
  a=hf(c);if(!a.device.register)ac("cannot register file");a.device.register(a.rest,b);return 0}function
  su(){var
  b=n.caml_fs_tmp;if(b)for(var
  a=0;a<b.length;a++)gV(b[a].name,b[a].content);n.caml_create_file=gV;n.caml_fs_tmp=[];return 0}function
  sv(a,b){return+(c6(a,b,false)>=0)}function
  sz(d,b){var
  e=b.length,a,c;for(a=0;a+4<=e;a+=4){c=b[a]|b[a+1]<<8|b[a+2]<<16|b[a+3]<<24;d=G(d,c)}c=0;switch(e&3){case
  3:c=b[a+2]<<16;case
  2:c|=b[a+1]<<8;case
  1:c|=b[a];d=G(d,c)}d^=e;return d}function
  sA(d,b){var
  e=b.length,a,c;for(a=0;a+4<=e;a+=4){c=b.charCodeAt(a)|b.charCodeAt(a+1)<<8|b.charCodeAt(a+2)<<16|b.charCodeAt(a+3)<<24;d=G(d,c)}c=0;switch(e&3){case
  3:c=b.charCodeAt(a+2)<<16;case
  2:c|=b.charCodeAt(a+1)<<8;case
  1:c|=b.charCodeAt(a);d=G(d,c)}d^=e;return d}function
  sy(a,b){switch(b.t&6){default:aA(b);case
  0:a=sA(a,b.c);break;case
  2:a=sz(a,b.c)}return a}function
  sx(a){a^=a>>>16;a=c_(a,0x85ebca6b|0);a^=a>>>13;a=c_(a,0xc2b2ae35|0);a^=a>>>16;return a}function
  sw(j,l,n,m){var
  f,g,h,d,c,b,a,e,i;d=l;if(d<0||d>aw)d=aw;c=j;b=n;f=[m];g=0;h=1;while(g<h&&c>0){a=f[g++];if(a&&a.caml_custom){if(aN[a.caml_custom]&&aN[a.caml_custom].hash){var
  k=aN[a.caml_custom].hash(a);b=G(b,k);c--}}else
  if(a
  instanceof
  Array&&a[0]===(a[0]|0))switch(a[0]){case
  248:b=G(b,a[2]);c--;break;case
  250:f[--g]=a[1];break;case
  255:b=gW(b,a);c--;break;default:var
  o=a.length-1<<10|a[0];b=G(b,o);for(e=1,i=a.length;e<i;e++){if(h>=d)break;f[h++]=a[e]}break}else
  if(a
  instanceof
  R){b=sy(b,a);c--}else
  if(a===(a|0)){b=G(b,a+a+1);c--}else
  if(a===+a){b=el(b,a);c--}}b=sx(b);return b&gp}function
  sB(d,g,a){var
  b=0;function
  f(a){g--;if(d<0||g<0)return;if(a
  instanceof
  Array&&a[0]===(a[0]|0))switch(a[0]){case
  248:d--;b=b*cU+a[2]|0;break;case
  250:g++;f(a);break;case
  255:d--;b=b*cU+a[1]+(a[2]<<24)|0;break;default:d--;b=b*19+a[0]|0;for(var
  c=a.length-1;c>0;c--)f(a[c])}else
  if(a
  instanceof
  R){d--;switch(a.t&6){default:aA(a);case
  0:for(var
  i=a.c,e=a.l,c=0;c<e;c++)b=b*19+i.charCodeAt(c)|0;break;case
  2:for(var
  h=a.c,e=a.l,c=0;c<e;c++)b=b*19+h[c]|0}}else
  if(a===(a|0)){d--;b=b*cU+a|0}else
  if(a===+a){d--;var
  k=bp(b8(a));for(var
  c=7;c>=0;c--)b=b*19+k[c]|0}else
  if(a&&a.caml_custom)if(aN[a.caml_custom]&&aN[a.caml_custom].hash){var
  j=aN[a.caml_custom].hash(a)|0;b=b*cU+j|0}}f(a);return b&gp}function
  aC(a,b){switch(a.t&6){default:if(b>=a.c.length)return 0;case
  0:return a.c.charCodeAt(b);case
  4:return a.c[b]}}function
  o(a){return a.l}function
  sX(c){var
  a=0,e=o(c),b=10,d=1;if(e>0)switch(aC(c,a)){case
  45:a++;d=-1;break;case
  43:a++;d=1;break}if(a+1<e&&aC(c,a)==48)switch(aC(c,a+1)){case
  120:case
  88:b=16;a+=2;break;case
  111:case
  79:b=8;a+=2;break;case
  98:case
  66:b=2;a+=2;break;case
  117:case
  85:d=0;a+=2;break}return[a,d,b]}function
  g9(a){if(a>=48&&a<=57)return a-48;if(a>=65&&a<=90)return a-55;if(a>=97&&a<=122)return a-87;return-1}function
  gZ(f){var
  h=sX(f),c=h[0],i=h[1],d=h[2],g=o(f),j=-1>>>0,e=c<g?aC(f,c):0,b=g9(e);if(b<0||b>=d)ac(cJ);var
  a=b;for(c++;c<g;c++){e=aC(f,c);if(e==95)continue;b=g9(e);if(b<0||b>=d)break;a=d*a+b;if(a>j)ac(cJ)}if(c!=g)ac(cJ);a=i*a;if(d==10&&(a|0)!=a)ac(cJ);return a|0}function
  sH(){var
  b=n.console?n.console:{},c=["log","debug","info","warn","error","assert",aZ,"dirxml","trace","group","groupCollapsed","groupEnd","time","timeEnd"];function
  d(){}for(var
  a=0;a<c.length;a++)if(!b[c[a]])b[c[a]]=d;return b}function
  he(c,e){var
  d=c.length,b=new
  Array(d+1);b[0]=e;for(var
  a=1;a<=d;a++)b[a]=c[a-1];return b}function
  sI(a){return he(a,0)}function
  s8(e){for(var
  f=g,b=f,a,i,c=0,h=e.length;c<h;c++){a=e.charCodeAt(c);if(a<Z){for(var
  d=c+1;d<h&&(a=e.charCodeAt(d))<Z;d++);if(d-c>f5){b.substr(0,1);f+=b;b=g;f+=e.slice(c,d)}else
  b+=e.slice(c,d);if(d==h)break;c=d}if(a<gA){b+=String.fromCharCode(0xc0|a>>6);b+=String.fromCharCode(Z|a&bb)}else
  if(a<0xd800||a>=gb)b+=String.fromCharCode(gc|a>>12,Z|a>>6&bb,Z|a&bb);else
  if(a>=0xdbff||c+1==h||(i=e.charCodeAt(c+1))<f9||i>gb)b+="\xef\xbf\xbd";else{c++;a=(a<<10)+i-0x35fdc00;b+=String.fromCharCode(gF|a>>18,Z|a>>12&bb,Z|a>>6&bb,Z|a&bb)}if(b.length>bN){b.substr(0,1);f+=b;b=g}}return f+b}function
  aO(a){var
  b=9;if(!g0(a))b=8,a=s8(a);return new
  R(b,a,a.length)}function
  sJ(a){return function(){return arguments.length>0?ab(a,arguments):ab(a,[undefined])}}function
  sK(a){return function(){return ab(a,he(arguments,this))}}function
  c9(a){if((a.t&6)!=0)aA(a);return a.c}function
  M(b){b=c9(b);var
  d=b.length/2,c=new
  Array(d);for(var
  a=0;a<d;a++)c[a]=(b.charCodeAt(2*a)|b.charCodeAt(2*a+1)<<8)<<16>>16;return c}function
  sL(b,t,a){var
  n=2,o=3,r=5,d=6,h=7,g=8,j=9,m=1,l=2,q=3,s=4,p=5;if(!b.lex_default){b.lex_base=M(b[m]);b.lex_backtrk=M(b[l]);b.lex_check=M(b[p]);b.lex_trans=M(b[s]);b.lex_default=M(b[q])}var
  e,c=t,k=c4(a[n]);if(c>=0){a[h]=a[r]=a[d];a[g]=-1}else
  c=-c-1;for(;;){var
  f=b.lex_base[c];if(f<0)return-f-1;var
  i=b.lex_backtrk[c];if(i>=0){a[h]=a[d];a[g]=i}if(a[d]>=a[o])if(a[j]==0)return-c-1;else
  e=aw;else{e=k[a[d]];a[d]++}if(b.lex_check[f+e]==c)c=b.lex_trans[f+e];else
  c=b.lex_default[c];if(c<0){a[d]=a[h];if(a[g]==-1)ac(f2);else
  return a[g]}else
  if(e==aw)a[j]=0}}function
  a3(c){var
  b=0;for(var
  a=c.length-1;a>=0;a--){var
  d=c[a];b=[0,d,b]}return b}function
  aP(a,d){if(a<0)b5();var
  a=a+1|0,b=new
  Array(a);b[0]=0;for(var
  c=1;c<a;c++)b[c]=d;return b}var
  sO=function(){function
  l(a,b){return a+b|0}function
  a(d,a,c,f,b,e){a=l(l(a,d),l(f,e));return l(a<<b|a>>>32-b,c)}function
  g(c,b,d,e,h,f,g){return a(b&d|~b&e,c,b,h,f,g)}function
  h(d,b,e,c,h,f,g){return a(b&c|e&~c,d,b,h,f,g)}function
  i(c,b,d,e,h,f,g){return a(b^d^e,c,b,h,f,g)}function
  j(c,b,d,e,h,f,g){return a(d^(b|~e),c,b,h,f,g)}function
  k(f,n){var
  e=n;f[e>>2]|=Z<<8*(e&3);for(e=(e&~0x3)+8;(e&0x3F)<60;e+=4)f[(e>>2)-1]=0;f[(e>>2)-1]=n<<3;f[e>>2]=n>>29&0x1FFFFFFF;var
  k=[0x67452301,0xEFCDAB89,0x98BADCFE,0x10325476];for(e=0;e<f.length;e+=16){var
  a=k[0],b=k[1],c=k[2],d=k[3];a=g(a,b,c,d,f[e+0],7,0xD76AA478);d=g(d,a,b,c,f[e+1],12,0xE8C7B756);c=g(c,d,a,b,f[e+2],17,0x242070DB);b=g(b,c,d,a,f[e+3],22,0xC1BDCEEE);a=g(a,b,c,d,f[e+4],7,0xF57C0FAF);d=g(d,a,b,c,f[e+5],12,0x4787C62A);c=g(c,d,a,b,f[e+6],17,0xA8304613);b=g(b,c,d,a,f[e+7],22,0xFD469501);a=g(a,b,c,d,f[e+8],7,0x698098D8);d=g(d,a,b,c,f[e+9],12,0x8B44F7AF);c=g(c,d,a,b,f[e+10],17,0xFFFF5BB1);b=g(b,c,d,a,f[e+11],22,0x895CD7BE);a=g(a,b,c,d,f[e+12],7,0x6B901122);d=g(d,a,b,c,f[e+13],12,0xFD987193);c=g(c,d,a,b,f[e+14],17,0xA679438E);b=g(b,c,d,a,f[e+15],22,0x49B40821);a=h(a,b,c,d,f[e+1],5,0xF61E2562);d=h(d,a,b,c,f[e+6],9,0xC040B340);c=h(c,d,a,b,f[e+11],14,0x265E5A51);b=h(b,c,d,a,f[e+0],20,0xE9B6C7AA);a=h(a,b,c,d,f[e+5],5,0xD62F105D);d=h(d,a,b,c,f[e+10],9,0x02441453);c=h(c,d,a,b,f[e+15],14,0xD8A1E681);b=h(b,c,d,a,f[e+4],20,0xE7D3FBC8);a=h(a,b,c,d,f[e+9],5,0x21E1CDE6);d=h(d,a,b,c,f[e+14],9,0xC33707D6);c=h(c,d,a,b,f[e+3],14,0xF4D50D87);b=h(b,c,d,a,f[e+8],20,0x455A14ED);a=h(a,b,c,d,f[e+13],5,0xA9E3E905);d=h(d,a,b,c,f[e+2],9,0xFCEFA3F8);c=h(c,d,a,b,f[e+7],14,0x676F02D9);b=h(b,c,d,a,f[e+12],20,0x8D2A4C8A);a=i(a,b,c,d,f[e+5],4,0xFFFA3942);d=i(d,a,b,c,f[e+8],11,0x8771F681);c=i(c,d,a,b,f[e+11],16,0x6D9D6122);b=i(b,c,d,a,f[e+14],23,0xFDE5380C);a=i(a,b,c,d,f[e+1],4,0xA4BEEA44);d=i(d,a,b,c,f[e+4],11,0x4BDECFA9);c=i(c,d,a,b,f[e+7],16,0xF6BB4B60);b=i(b,c,d,a,f[e+10],23,0xBEBFBC70);a=i(a,b,c,d,f[e+13],4,0x289B7EC6);d=i(d,a,b,c,f[e+0],11,0xEAA127FA);c=i(c,d,a,b,f[e+3],16,0xD4EF3085);b=i(b,c,d,a,f[e+6],23,0x04881D05);a=i(a,b,c,d,f[e+9],4,0xD9D4D039);d=i(d,a,b,c,f[e+12],11,0xE6DB99E5);c=i(c,d,a,b,f[e+15],16,0x1FA27CF8);b=i(b,c,d,a,f[e+2],23,0xC4AC5665);a=j(a,b,c,d,f[e+0],6,0xF4292244);d=j(d,a,b,c,f[e+7],10,0x432AFF97);c=j(c,d,a,b,f[e+14],15,0xAB9423A7);b=j(b,c,d,a,f[e+5],21,0xFC93A039);a=j(a,b,c,d,f[e+12],6,0x655B59C3);d=j(d,a,b,c,f[e+3],10,0x8F0CCC92);c=j(c,d,a,b,f[e+10],15,0xFFEFF47D);b=j(b,c,d,a,f[e+1],21,0x85845DD1);a=j(a,b,c,d,f[e+8],6,0x6FA87E4F);d=j(d,a,b,c,f[e+15],10,0xFE2CE6E0);c=j(c,d,a,b,f[e+6],15,0xA3014314);b=j(b,c,d,a,f[e+13],21,0x4E0811A1);a=j(a,b,c,d,f[e+4],6,0xF7537E82);d=j(d,a,b,c,f[e+11],10,0xBD3AF235);c=j(c,d,a,b,f[e+2],15,0x2AD7D2BB);b=j(b,c,d,a,f[e+9],21,0xEB86D391);k[0]=l(a,k[0]);k[1]=l(b,k[1]);k[2]=l(c,k[2]);k[3]=l(d,k[3])}var
  o=new
  Array(16);for(var
  e=0;e<4;e++)for(var
  m=0;m<4;m++)o[e*4+m]=k[e]>>8*m&0xFF;return o}return function(h,g,f){var
  e=[];switch(h.t&6){default:aA(h);case
  0:var
  d=h.c;for(var
  a=0;a<f;a+=4){var
  b=a+g;e[a>>2]=d.charCodeAt(b)|d.charCodeAt(b+1)<<8|d.charCodeAt(b+2)<<16|d.charCodeAt(b+3)<<24}for(;a<f;a++)e[a>>2]|=d.charCodeAt(a+g)<<8*(a&3);break;case
  4:var
  c=h.c;for(var
  a=0;a<f;a+=4){var
  b=a+g;e[a>>2]=c[b]|c[b+1]<<8|c[b+2]<<16|c[b+3]<<24}for(;a<f;a++)e[a>>2]|=c[a+g]<<8*(a&3)}return hc(k(e,f))}}(),an=new
  Array();function
  er(c){var
  a=an[c];if(!a.opened)$("Cannot flush a closed channel");if(!a.buffer||a.buffer==g)return 0;if(a.fd&&v.fds[a.fd]&&v.fds[a.fd].output){var
  b=v.fds[a.fd].output;switch(b.length){case
  2:b(c,a.buffer);break;default:b(a.buffer)}}a.buffer=g;return 0}function
  ha(e,f){var
  b=an[e],d=a(f),c=o(d);b.file.write(b.offset,d,0,c);b.offset+=c;return 0}function
  s_(a){var
  a=ev(a),b=n;if(b.process&&b.process.stdout&&b.process.stdout.write)b.process.stderr.write(a);else{if(a.charCodeAt(a.length-1)==10)a=a.substr(0,a.length-1);var
  c=b.console;c&&c.error&&c.error(a)}}function
  s$(a){var
  a=ev(a),b=n;if(b.process&&b.process.stdout&&b.process.stdout.write)b.process.stdout.write(a);else{if(a.charCodeAt(a.length-1)==10)a=a.substr(0,a.length-1);var
  c=b.console;c&&c.log&&c.log(a)}}function
  c$(c,e,d,a){if(v.fds===undefined)v.fds=new
  Array();a=a?a:{};var
  b={};b.file=d;b.offset=a.append?d.length():0;b.flags=a;b.output=e;v.fds[c]=b;if(!v.fd_last_idx||c>v.fd_last_idx)v.fd_last_idx=c;return c}function
  td(c,b,g){var
  a={};while(b){switch(b[1]){case
  0:a.rdonly=1;break;case
  1:a.wronly=1;break;case
  2:a.append=1;break;case
  3:a.create=1;break;case
  4:a.truncate=1;break;case
  5:a.excl=1;break;case
  6:a.binary=1;break;case
  7:a.text=1;break;case
  8:a.nonblock=1;break}b=b[2]}if(a.rdonly&&a.wronly)$(c.toString()+gi);if(a.text&&a.binary)$(c.toString()+gt);var
  d=hf(c),e=d.device.open(d.rest,a),f=v.fd_last_idx?v.fd_last_idx:0;return c$(f+1,ha,e,a)}c$(0,ha,new
  F(J(0)));c$(1,s$,new
  F(J(0)));c$(2,s_,new
  F(J(0)));function
  sP(c){var
  b=v.fds[c];if(b.flags.wronly)$(gM+c+" is writeonly");var
  a={file:b.file,offset:b.offset,fd:c,opened:true,out:false,refill:null};an[a.fd]=a;return a.fd}function
  g2(c){var
  b=v.fds[c];if(b.flags.rdonly)$(gM+c+" is readonly");var
  a={file:b.file,offset:b.offset,fd:c,opened:true,out:true,buffer:g};an[a.fd]=a;return a.fd}function
  sQ(){var
  b=0;for(var
  a=0;a<an.length;a++)if(an[a]&&an[a].opened&&an[a].out)b=[0,an[a].fd,b];return b}function
  sR(g,d,h,f){var
  a=an[g];if(!a.opened)$("Cannot output to a closed channel");var
  c;if(h==0&&aQ(d)==f)c=d;else{c=J(f);az(d,h,c,0,f)}var
  b=c9(c),e=b.lastIndexOf("\n");if(e<0)a.buffer+=b;else{a.buffer+=b.substr(0,e+1);er(g);a.buffer+=b.substr(e+1)}return 0}function
  g3(b,a,d,c){return sR(b,a,d,c)}function
  sS(c,b){var
  d=a(String.fromCharCode(b));g3(c,d,0,1);return 0}function
  g_(a){throw a}function
  s1(){g_(v.Division_by_zero)}function
  g4(b,a){if(a==0)s1();return b%a}function
  sM(d,a,c,f){for(;;){var
  b=d.charCodeAt(a);a++;if(b==Q)return;var
  e=d.charCodeAt(a);a++;if(e==Q)c[b+1]=f;else
  c[b+1]=c[e+1]}}function
  g1(d,a,c){for(;;){var
  b=d.charCodeAt(a);a++;if(b==Q)return;var
  e=d.charCodeAt(a);a++;if(e==Q)c[b+1]=-1;else
  c[b+1]=c[e+1]}}function
  sU(a,D,b){var
  t=2,u=3,A=5,f=6,i=7,h=8,n=9,j=10,r=1,p=2,y=3,B=4,v=5,s=6,q=7,z=8,C=9,w=10,x=11;if(!a.lex_default){a.lex_base=M(a[r]);a.lex_backtrk=M(a[p]);a.lex_check=M(a[v]);a.lex_trans=M(a[B]);a.lex_default=M(a[y])}if(!a.lex_default_code){a.lex_base_code=M(a[s]);a.lex_backtrk_code=M(a[q]);a.lex_check_code=M(a[w]);a.lex_trans_code=M(a[C]);a.lex_default_code=M(a[z])}if(a.lex_code==null)a.lex_code=c9(a[x]);var
  e,c=D,o=c4(b[t]);if(c>=0){b[i]=b[A]=b[f];b[h]=-1}else
  c=-c-1;for(;;){var
  g=a.lex_base[c];if(g<0){var
  d=a.lex_base_code[c];g1(a.lex_code,d,b[j]);return-g-1}var
  l=a.lex_backtrk[c];if(l>=0){var
  d=a.lex_backtrk_code[c];g1(a.lex_code,d,b[j]);b[i]=b[f];b[h]=l}if(b[f]>=b[u])if(b[n]==0)return-c-1;else
  e=aw;else{e=o[b[f]];b[f]++}var
  k=c;if(a.lex_check[g+e]==c)c=a.lex_trans[g+e];else
  c=a.lex_default[c];if(c<0){b[f]=b[i];if(b[h]==-1)ac(f2);else
  return b[h]}else{var
  m=a.lex_base_code[k],d;if(a.lex_check_code[m+e]==k)d=a.lex_trans_code[m+e];else
  d=a.lex_default_code[k];if(d>0)sM(a.lex_code,d,b[j],b[f]);if(e==aw)b[n]=0}}}function
  sV(a,b){return+(c6(a,b,false)!=0)}function
  g7(d,c){var
  b=new
  Array(c+1);b[0]=d;for(var
  a=1;a<=c;a++)b[a]=0;return b}function
  sW(a,b){a[0]=aH;a[1]=b;return 0}function
  bq(a){if(a
  instanceof
  Array&&a[0]==a[0]>>>0)return a[0];else
  if(a
  instanceof
  R)return gG;else
  if(a
  instanceof
  Function||typeof
  a=="function")return 247;else
  if(a&&a.caml_custom)return bK;else
  return bL}function
  aa(b,c,a){if(a&&n.toplevelReloc)b=n.toplevelReloc(a);v[b+1]=c;if(a)v[a]=c}var
  g6={};function
  s2(a,b){g6[c9(a)]=b;return 0}function
  s3(a){a[2]=g8++;return a}function
  e(a,b){if(a===b)return 1;a.t&6&&aA(a);b.t&6&&aA(b);return a.c==b.c?1:0}function
  aB(b,a){if(a>>>0>=b.l)et();return aC(b,a)}function
  f(a,b){return 1-e(a,b)}function
  aR(a){return a}function
  s4(){return[0,a("js_of_ocaml")]}function
  s5(){return 0x7FFFFFFF/4|0}function
  sZ(){g_(v.Not_found)}function
  hd(c){var
  a=n,b=c.toString();if(a.process&&a.process.env&&a.process.env[b]!=undefined)return aO(a.process.env[b]);if(n.jsoo_static_env&&n.jsoo_static_env[b])return aO(n.jsoo_static_env[b]);sZ()}function
  s6(){var
  a=new
  Date().getTime(),b=a^0xffffffff*Math.random();return[0,b]}function
  b$(a){var
  b=1;while(a&&a.joo_tramp){a=a.joo_tramp.apply(null,a.joo_args);b++}return a}function
  m(b,a){return{joo_tramp:b,joo_args:a}}function
  g$(a){return a}function
  g5(a){return g6[a]}function
  B(a){if(a
  instanceof
  Array)return a;if(n.RangeError&&a
  instanceof
  n.RangeError&&a.message&&a.message.match(/maximum call stack/i))return g$(v.Stack_overflow);if(n.InternalError&&a
  instanceof
  n.InternalError&&a.message&&a.message.match(/too much recursion/i))return g$(v.Stack_overflow);if(a
  instanceof
  n.Error&&g5(dU))return[0,g5(dU),a];return[0,v.Failure,aO(String(a))]}function
  c(a,b){return a.length==1?a(b):ab(a,[b])}function
  d(a,b,c){return a.length==2?a(b,c):ab(a,[b,c])}function
  ai(a,b,c,d){return a.length==3?a(b,c,d):ab(a,[b,c,d])}function
  dJ(a,b,c,d,e){return a.length==4?a(b,c,d,e):ab(a,[b,c,d,e])}function
  fW(a,b,c,d,e,f){return a.length==5?a(b,c,d,e,f):ab(a,[b,c,d,e,f])}su();var
  ey=[z,a(f7),-2],cb=[z,a(f$),-3],da=[z,a(gn),-4],q=[z,a(gr),-7],p=[z,a(gB),-11],cc=[0,a(g),0,0,-1],eH=[0,a(g),1,0,0],aq=[0,a("\0\0\x01\0\xf9\xff\0\0A\0\xa3\0\xfd\xff\0\0\x01\0\xff\xff\xf1\0 \0\x80\0\xfd\xff\x01\0@\x01\x8e\x01.\x000\0\xfd\xff\x04\0\0\0\xff\xff\x7f\0\xa0\0\xfe\xff\xff\xff\xee\0\x0e\x01\xfd\xff\xfe\xff\x02\0\xff\xff\0\x02\xf7\xffr\x02\xf9\xff\xfa\xff\xfb\xff\x84\x02\0\0\xf8\x02\xff\xff\xfe\xff\xfe\x02\xf9\xff\x04\x03\xfb\xff\xfc\xff\x02\0\n\x03\xff\xff\xfe\xff\x03\0\xff\xffu\0\xff\xff"),a("\xff\xff\x07\0\xff\xff\x05\0\xff\xff\x03\0\xff\xff\x01\0\xff\xff\xff\xff\x04\0\xff\xff\x03\0\xff\xff\x01\0\xff\xff\0\0\xff\xff\x03\0\xff\xff\x01\0\xff\xff\xff\xff\xff\xff\x02\0\xff\xff\xff\xff\xff\xff\x04\0\xff\xff\xff\xff\x03\0\xff\xff\xff\xff\xff\xff\x07\0\xff\xff\xff\xff\xff\xff\x03\0\x02\0\x07\0\xff\xff\xff\xff\xff\xff\xff\xff\x05\0\xff\xff\xff\xff\x02\0\x05\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff"),a("\x01\0\x01\0\0\0\xff\xff\xff\xff\xff\xff\0\0\xff\xff\xff\xff\0\0\xff\xff\f\0\f\0\0\0\xff\xff\xff\xff\xff\xff\x12\0\x12\0\0\0\xff\xff\xff\xff\0\0\x18\0\x18\0\0\0\0\0\x1c\0\x1c\0\0\0\0\0\xff\xff\0\0#\0\0\0#\0\0\0\0\0\0\0#\0\xff\xff#\0\0\0\0\0.\0\0\0.\0\0\0\0\0\xff\xff.\0\0\0\0\x005\0\0\x007\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0(\0(\x001\x001\0(\0\0\x001\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0(\0\x07\x001\0\0\0\0\x006\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\b\0\t\0\x04\0\x0f\0\x15\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x03\0\xff\xff\x16\0\x06\0 \0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x14\0\x0e\0\xff\xff\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\n\0\0\0\0\0\0\0\0\0\0\0\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\x008\0\0\0\0\0\0\0\n\0\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\xff\xff\x1a\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\0\0\0\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\x02\0\xff\xff\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\n\0\n\0\r\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x1e\0\x1f\0\x13\0\0\0\xff\xff\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\xff\xff\xff\xff\0\0\0\0\n\0\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\x10\0\0\0\0\0\0\0\0\0\x19\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\0\0\0\0\0\0\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\0\0\0\0\0\0\0\0\0\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\0\0\0\0\0\0\x10\0\x1d\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0(\0(\0\0\0\0\0(\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0(\0\0\0%\0\0\0\0\0\0\0\0\0$\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0)\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0'\0\0\0\0\0&\0*\0\0\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\0\0\0\0'\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff\xff\xff\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\xff\xff\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff'\0'\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\0\0\0\0'\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\"\0\xff\xff\xff\xff\0\0\0\0\xff\xff\0\x001\x001\0\0\0\0\x001\0\0\0\xff\xff\xff\xff\0\0\0\0\xff\xff\0\0\xff\xff\xff\xff\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\0\0\0\0\0\x001\0\xff\xff0\0\0\0\0\0\0\0\xff\xff/\0\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff\xff\xff2\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\xff\xff+\0\0\0\0\0\0\0\0\0\0\x003\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\x004\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0-\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\xff\xff"),a("\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff(\0(\x001\x001\0(\0\xff\xff1\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff(\0\x03\x001\0\xff\xff\xff\xff5\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\b\0\x03\0\x0e\0\x14\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x03\0\xff\xff\0\0\x01\0\x15\0\x03\0\x1f\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x11\0\x0b\0\x12\0\xff\xff\x03\0\xff\xff\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x04\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\x007\0\xff\xff\xff\xff\xff\xff\x04\0\xff\xff\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\f\0\x17\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x05\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x18\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\xff\xff\0\0\x01\0\x05\x005\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\n\0\n\0\x0b\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x1b\0\x1b\0\x11\0\xff\xff\x12\0\xff\xff\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x1c\0\x1c\0\xff\xff\xff\xff\n\0\xff\xff\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff7\0\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\x17\0\f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\x18\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x10\0\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\x1b\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0!\0!\0\xff\xff\xff\xff!\0\x1c\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff!\0!\0\xff\xff\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0#\0#\0\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'\0'\0\xff\xff\xff\xff'\0#\0\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff'\0\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff\xff\xff\xff\xff#\0#\0'\0'\0\xff\xff'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff\xff\xff'\0'\0\xff\xff\xff\xff'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff!\0)\0)\0\xff\xff\xff\xff)\0\xff\xff,\0,\0\xff\xff\xff\xff,\0\xff\xff.\0.\0\xff\xff\xff\xff.\0\xff\xff2\x002\0\xff\xff\xff\xff2\0)\0\xff\xff)\0\xff\xff\xff\xff\xff\xff,\0)\0,\0\xff\xff\xff\xff\xff\xff.\0,\0.\0\xff\xff\xff\xff\xff\xff2\0.\x002\0,\0\xff\xff\xff\xff\xff\xff2\0\xff\xff\xff\xff\xff\xff)\0)\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff,\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff.\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff2\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff)\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff,\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff.\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff2\0"),a(g),a(g),a(g),a(g),a(g),a(g)],eW=a3([a(aK),a(a_),a(a$),a(cK),a(b0),a(bf),a(bW),a(ay),a(bi),a(bP),a(ax),a(a0),a(bF),a(bZ),a(bH),a(bU),a(bD),a(bV),a(bJ),a(bT),a(Y),a(X),a(aZ),a(cO)]),sh=[0,a(b3),[0,a(cR),0]],si=[0,a(a0),[0,a(bi),[0,a(cZ),0]]],fc=a(cY),fd=a(aY),fe=a(cY),ff=a(aY),fg=a(d$),fo=a3([a(aK),a(a_),a(a$),a(cK),a(b0),a(bf),a(bW),a(ay),a(bi),a(bP),a(ax),a(a0),a(bF),a(bZ),a(bH),a(bU),a(bD),a(bV),a(bJ),a(bT),a(Y),a(X),a(aZ),a(cO)]),sj=[0,a(cR),0],sk=[0,a(a0),[0,a(bi),[0,a(cZ),0]]],fs=a("o:spid"),ft=a("id"),cr=[0,a(fX)];aa(11,[z,a(fY),-12],fY);aa(10,p,gB);aa(9,[z,a(gj),-10],gj);aa(8,[z,a(gs),-9],gs);aa(7,[z,a(gd),-8],gd);aa(6,q,gr);aa(5,[z,a(gy),-6],gy);aa(4,[z,a(ga),-5],ga);aa(3,da,gn);aa(2,cb,f$);aa(1,ey,f7);aa(0,[z,a(gI),-1],gI);var
  hn=a("Bytes.blit"),hm=a("String.sub / Bytes.sub"),ht=a("String.contains_from / Bytes.contains_from"),hq=a(g),hp=a(g),ho=a("String.concat"),hw=a("Array.blit"),hB=a("Set.remove_min_elt"),hC=[0,0,0,0],hD=[0,0,0],hE=[0,a("set.ml"),547,18],hx=a(cE),hy=a(cE),hz=a(cE),hA=a(cE),hJ=a("Map.remove_min_elt"),hK=[0,0,0,0],hL=[0,a("map.ml"),398,10],hM=[0,0,0],hF=a(cT),hG=a(cT),hH=a(cT),hI=a(cT),hN=a("Stdlib.Stack.Empty"),hP=a("CamlinternalLazy.Undefined"),hU=a("Buffer.add: cannot grow buffer"),hT=[0,a(gw),93,2],hS=[0,a(gw),94,2],hV=a("x"),sf=a("OCAMLRUNPARAM"),sd=a("CAMLRUNPARAM"),hW=a(g),h7=a(g),ia=a(g),h$=a(g),h9=a(g),ig=[2,a(d8)],ih=[2,a(d8)],j6=a(g),j7=a(g),j8=a(g),ky=a(a8),kz=a('="'),kA=a(d9),ku=a(ak),kv=a(aU),kw=a(d5),kG=a("<?"),kH=a(E),kI=a(b1),kJ=a("<!--"),kK=a(E),kL=a("-->"),kM=a("<!"),kN=a(E),kO=a(b1),kx=a(d8),kB=a("/>"),kC=a(b1),kD=a(b1),kE=a(gx),kF=a(b1),kt=a("&quot;"),kP=a("write"),kj=a(g),kk=a(gx),kl=a(E),km=a(aU),kn=a(E),ko=a(ak),kp=a(E),kq=a(d5),kr=a(g),kf=[0,0,0],kg=[0,0,1],kh=[0,0,0],ki=[0,0,1],ke=[0,a("netstring/code/src/netstring/nethtml.ml"),353,27],kd=a(g),ka=a(g),kb=[0,aV,dN],kc=[0,aV,dN],j$=a(g),ii=a("Nethtml.End_of_scan"),ij=a("Nethtml.Found"),ik=[0,[0,a(bg),[0,j,[0,k,[0,a(bg),[0,a(c0),[0,a(bf),0]]]]]],[0,[0,a(c0),[0,j,r]],0]],il=a3([k,a(av),a(bk),a(be),a(P),a(W),a(aI),a(aX),a(aJ),a(au),a(bg)]),im=a(bX),io=[0,a(W),[0,j,bh]],ip=[0,a(aI),[0,j,r]],iq=[0,a(be),[0,j,r]],ir=[0,a(bk),[0,j,[0,k,0]]],is=[0,a(av),[0,j,[0,k,[0,a(bk),[0,a(be),[0,a(P),[0,a(W),[0,a(aI),[0,a(aX),[0,a(aJ),0]]]]]]]]]],it=[0,a(a2),[0,j,h]],iu=[0,a(b4),[0,j,h]],iv=[0,a(V),[0,j,[0,k,[0,a(b4),[0,a(a2),0]]]]],iw=[0,a(aL),[0,j,r]],ix=[0,a(cW),[0,j,[0,k,[0,a(aL),0]]]],iy=[0,a(bS),[0,j,[0,k,[0,a(V),0]]]],iz=[0,a(bM),[0,j,[0,k,[0,a(V),0]]]],iA=[0,a(bG),[0,j,[0,k,[0,a(V),0]]]],iB=[0,a(c3),[0,j,b]],iC=[0,a(cN),[0,j,b]],iD=[0,a(bd),[0,j,[0,k,0]]],iE=[0,a(cX),[0,j,[0,k,[0,a(bd),0]]]],iF=[0,a(O),[0,j,h]],iG=[0,a(bR),[0,j,h]],iH=[0,a(cI),[0,j,b]],iI=[0,a(d1),[0,aV,h]],iJ=[0,a(dL),[0,aV,h]],iK=[0,a(ba),[0,j,r]],iL=[0,a(aX),[0,j,r]],iM=[0,a(cH),[0,j,r]],iO=[0,k,[0,a(P),0]],iP=a(au),iQ=[0,a(bi),[0,l,r]],iR=[0,a(bf),[0,l,h]],iS=[0,a(cK),[0,l,h]],iT=[0,a(bF),[0,l,b]],iV=[0,k,[0,a(cN),0]],iW=a(a0),iX=[0,a(ax),[0,l,[0,k,[0,a(c3),[0,a(aL),[0,a(cW),[0,a(bG),[0,a(bS),[0,a(bM),[0,a(V),0]]]]]]]]]],iY=[0,a(bP),[0,l,r]],i0=[0,k,[0,a(P),0]],i1=[0,a(ay),0],i2=a(ay),i4=[0,k,[0,a(P),0]],i5=a(bW),i6=[0,a(b0),[0,l,h]],i7=[0,a(a$),[0,l,h]],i8=[0,a(a_),[0,l,[0,k,[0,a(cI),[0,a(bR),0]]]]],i9=[0,a(bT),[0,l,[0,C,[0,[0,a(aj),[0,a(aJ),[0,a(cM),[0,a(c1),[0,a(cP),[0,a(bY),[0,a(bQ),sh]]]]]]],b]]]],i_=[0,k,[0,a(O),0]],i$=a(cO),ja=[0,k,[0,a(O),0]],jb=a(aZ),jc=[0,a(X),[0,l,[0,k,[0,a(O),0]]]],jd=[0,a(Y),[0,l,[0,k,[0,a(O),0]]]],je=[0,a(bJ),[0,l,b]],jf=[0,a(bV),[0,l,b]],jg=[0,a(bD),[0,l,b]],jh=[0,a(bU),[0,l,b]],ji=[0,a(bH),[0,l,b]],jj=[0,a(bZ),[0,l,b]],jk=[0,a(aK),[0,l,b]],jl=[0,a(c2),[0,b,[0,C,[0,[0,a(aW),[0,a(cG),[0,a(cS),[0,a(cD),[0,a(bc),[0,a(c2),[0,a(ay),si]]]]]]],h]]]],jm=[0,a(bc),[0,b,[0,C,[0,[0,a(bc),0],b]]]],jn=[0,a(cD),[0,b,[0,k,0]]],jo=[0,a(cS),[0,b,[0,k,[0,a(cX),[0,a(bd),0]]]]],jp=[0,a(cG),[0,b,r]],jq=[0,a(cZ),[0,b,h]],jr=[0,a(cR),[0,b,r]],js=[0,a(b3),[0,b,b]],ju=[0,k,[0,a(ba),0]],jv=a(cM),jw=[0,a(gO),[0,b,b]],jy=[0,k,[0,a(cH),0]],jz=a(gD),jA=[0,a(P),[0,b,bh]],jC=[0,k,[0,a(ba),0]],jD=a(aJ),jE=[0,a(aj),[0,b,r]],jF=[0,a(aW),[0,b,[0,C,[0,[0,a(aW),0],b]]]],jG=[0,a(gv),[0,b,r]],jH=[0,a(f3),[0,b,b]],jI=[0,a(a9),[0,b,b]],jJ=[0,a(bY),[0,b,b]],jK=[0,a(bQ),[0,b,b]],jL=[0,a(d2),[0,b,b]],jM=[0,a(gk),[0,b,b]],jN=[0,a(dQ),[0,b,b]],jO=[0,a(ed),[0,b,b]],jP=[0,a(dP),[0,b,b]],jQ=[0,a(eg),[0,b,b]],jR=[0,a(dS),[0,b,b]],jS=[0,a(dR),[0,b,b]],jT=[0,a(dZ),[0,b,b]],jU=[0,a(eb),[0,b,b]],jV=[0,a(gl),[0,b,b]],jW=[0,a(dX),[0,b,b]],jX=[0,a(dO),[0,b,b]],jY=[0,a(cP),[0,b,b]],jZ=[0,a(c1),[0,b,b]],j0=[0,a(ea),[0,b,b]],j1=[0,a(eh),[0,b,b]],j2=[0,a(d_),[0,b,b]],j5=[0,a(au),[0,a(ax),[0,a(X),[0,a(Y),[0,a(a_),0]]]]],kR=[0,0,0],kT=a(g),kS=a(g),kZ=a(a8),kX=a("' is invalid"),kY=a("document list level '"),kU=a("\xef\x82\xb7"),kV=a("\xef\x82\xa7"),kW=a("o"),k7=a(bX),k_=[0,a("src/main/re/html/htmlStd.re"),138,9],k0=a(E),k1=a(d0),k2=a(ak),k3=a3([a(bF),a("article"),a("aside"),a("audio"),a(bW),a("canvas"),a(bR),a(a$),a(a_),a(a0),a("figcaption"),a("figure"),a("footer"),a(ay),a(bZ),a(bH),a(bU),a(bD),a(bV),a(bJ),a("header"),a("hgroup"),a(bP),a(O),a(b0),a(X),a("output"),a(aK),a(bT),a("section"),a(ax),a(bM),a(a2),a(bS),a(b4),a(bG),a(V),a(Y),a("video")]),k4=[0,a(au),[0,a(av),[0,a(bX),[0,a(aX),[0,a(aI),[0,a(W),[0,a(dM),0]]]]]]],k8=[0,a(g)],ll=a(aK),lj=a(gz),li=a(gm),lk=[0,a(gz)],lg=a(gm),le=a(aj),la=a(ge),lb=a(ge),lc=a("file:"),ld=a("data-image-src"),lX=a(a8),lW=a(bI),lT=a(":level"),lR=a(a8),lK=a(dT),lL=a(ec),lM=a(dT),lN=a(ec),lO=a(d$),lP=a(dT),lQ=a(ec),lS=a(g),lU=a(g),lV=a("@list "),lI=a(f0),lG=[0,a("\n  ")],lH=a("  "),lF=a(g),lD=a("color"),lE=a(a8),lA=a('""'),lB=a(";"),lC=a(aY),lq=a(cY),lr=a(g),ls=a(ak),lt=a(a8),lu=a(g),lp=[0,a(g),0],lv=a(g),lw=a(f0),ln=a(d9),lo=a(d9),lx=[0,a("font-family"),0],mb=[0,a(t),156,8],mc=[0,a(t),173,12],md=[0,a(t),185,4],me=[0,a(t),205,12],mf=[0,a(t),217,8],mg=[0,a(t),297,4],mh=[0,a(t),311,8],mi=[0,a(t),319,4],mj=[0,a(t),348,8],mk=a("Internal failure -- please contact the parser generator's developers.\n%!"),ml=[0,a(t),355,4],mm=[0,a(t),373,8],mn=[0,a(t),397,16],mo=[0,a(t),401,12],mp=[0,a(t),501,8],mq=[0,a(t),607,8],mr=[0,a(t),625,12],ms=[0,a(t),637,4],mt=[0,a(t),658,8],mu=[0,a(t),666,4],mv=[0,a(t),684,8],mw=[0,a(t),710,4],mx=[0,a(t),735,12],my=[0,a(t),739,8],mz=[0,a(t),879,8],mA=[0,a(t),906,8],l5=a(d$),l2=a("list"),l4=a(aY),l3=a(g),l0=a(cY),l1=a(g),lZ=[0,a(g),0],lY=a("Css_parser.MenhirBasics.Error"),mE=a(")"),mF=a("' ("),mG=a("Unexpected char: '"),mB=a("Css_lexer.SyntaxError"),mC=[0,a('\0\0\xeb\xff\x14\0\x03\0\x01\0\xd7\0\x0e\0H\x01\x9d\0\xf1\xff\x03\0\x06\0\x11\0u\x01\xe8\x01B\x02\x9c\x02\xf6\x02\x02\0\x1e\0\xf7\xff\xf8\xff\xf9\xff\xfa\xff\xfb\xff\xfc\xff\b\0\x01\0\xfe\xff\xff\xff\xf6\xff\xf5\xff\x12\0P\x03\xaa\x03|\0\xf4\xff\x04\x04^\x04\xc8\x01\x90\x04\x13\0\xbd\x04\xed\xff0\x05\x15\0\x16\0\x17\0\xf0\xff2\x052\x001\x054\0\x0b\0\x12\0\x0e\0"\0\xa1\x05\xa2\x055\0\xa3\x05\xa7\x05\xa8\x05\xa9\x05\xad\x051\x003\0\xee\xffY\0Z\0\xec\xff\\\0]\0'),a("\xff\xff\xff\xff\x14\0\x14\0\x14\0\x10\0\x14\0\x14\0\x14\0\xff\xff\x14\0\x14\0\x14\0\x10\0\r\0\x10\0\x10\0\x10\0\x14\0\x14\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x02\0\x01\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\x10\0\xff\xff\xff\xff\x10\0\x10\0\xff\xff\f\0\xff\xff\x10\0\xff\xff\r\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff"),a("\x01\0\0\0\xff\xff\xff\xff\x05\0\xff\xff\xff\xff3\x001\0\0\0.\0-\0\t\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\x05\0\xff\xff\xff\xff\xff\xff\0\0\xff\xff\xff\xff'\0'\0'\0\xff\xff\0\0\xff\xff\t\0/\0\t\0\0\x001\x001\x003\x003\0\xff\xff\xff\xff\xff\xff\xff\xff9\x009\x009\x009\x009\x009\x009\x009\0\xff\xff\xff\xff\0\0D\0G\0\0\0G\0G\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x1a\0\x1c\0\x1c\0\0\0\x1b\0\0\0\0\0\0\0\x1a\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x1a\0\x05\0\b\0\x05\0A\0\x05\0\x06\0\x07\0\x1a\0\0\0\t\0\x05\0\x19\0\x0f\0\r\0\x02\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x15\0\x16\0\x03\0\x05\0D\0\x1f\0\x14\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x1e\0\x04\x009\0B\0\x05\0C\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x11\0\x05\0\x05\0\x10\0\x05\0\x05\0\x05\0\x05\0\x05\0\x18\0\x12\0\x17\0\x13\x005\x006\x007\x008\0E\0E\0#\0H\0H\0\0\0F\0\0\0\0\0F\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0#\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0$\0\0\0\xff\xff#\0\0\0\xff\xff\0\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x000\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x05\x002\0\x05\0\0\0\x05\0\0\0\0\0\0\0\x1d\0\xff\xff\x05\0\xff\xff\x05\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\xff\xff\xff\xff\x05\0\xff\xff\xff\xff\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff \0\xff\xff\xff\xff\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\0\0\0\0\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x000\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\xff\xff\0\0\0\0\x05\0\0\0\x05\0\x05\x004\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\xff\xff\0\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0(\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\r\0\0\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\0\0\0\0)\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0*\0\r\0\0\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\xff\xff\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0%\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0!\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\"\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0#\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0&\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0'\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\xff\xff\x05\0\0\0\xff\xff\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0(\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0)\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0+\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\x000\0\x05\0\0\0\0\x000\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \x004\x002\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0:\0:\0:\0\0\0\0\0\0\0:\0:\0:\0\0\0\0\0\0\0:\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0@\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0;\0;\0;\0\0\0\0\0\0\0;\0;\0;\0\0\0\0\0\0\0;\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0<\0\0\0\0\0>\0\0\0=\0\0\0\0\0\0\0?\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\xff\xff"),a('\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\x1b\0\xff\xff\0\0\xff\xff\xff\xff\xff\xff\x1a\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\0\0\x03\0\0\0\0\0\0\0\x1a\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x02\0\x12\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x13\0\0\x008\0A\0\0\0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x06\x005\x006\x007\0D\0E\0#\0G\0H\0\xff\xffE\0\xff\xff\xff\xffH\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\b\0#\0\xff\xff\b\0\xff\xff#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\b\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\b\0\x05\0\xff\xff\x05\0\xff\xff\xff\xff\xff\xff\0\0\x04\0\x05\0\n\0\x05\0\x05\0\x0b\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\f\0 \0)\0\x05\0-\0.\0/\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\x002\0\x05\x004\0;\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x07\0\xff\xff\xff\xff\x07\0\xff\xff\xff\xff\xff\xffD\0E\0\xff\xffG\0H\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\r\0\xff\xff\r\0\xff\xff\r\0\xff\xff\xff\xff\b\0\xff\xff\xff\xff\r\0\xff\xff\r\0\r\0\x07\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\xff\xff\xff\xff\xff\xff\r\0\xff\xff\xff\xff\xff\xff\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\xff\xff\r\0\'\0\xff\xff\r\0\'\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\xff\xff\'\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0e\0\xff\xff\x0e\0\xff\xff\x0e\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0e\0\xff\xff\x0e\0\x0e\0\xff\xff\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\xff\xff\xff\xff\'\0\x0e\0\xff\xff\xff\xff\xff\xff\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\xff\xff\x0e\0\xff\xff\xff\xff\x0e\0\x07\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0f\0\xff\xff\x0f\0\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\x0f\0\x0f\0\xff\xff\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\xff\xff\x0f\0\xff\xff\xff\xff\x0f\0\xff\xff\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x10\0\xff\xff\x10\0\xff\xff\x10\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\'\0\x10\0\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\xff\xff\xff\xff\x10\0\xff\xff\xff\xff\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\x10\0\xff\xff\xff\xff\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x11\0\xff\xff\x11\0\xff\xff\x11\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x11\0\xff\xff\x11\0\x11\0\xff\xff\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\xff\xff\xff\xff\xff\xff\x11\0\xff\xff\xff\xff\xff\xff\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\xff\xff\x11\0\xff\xff\xff\xff\x11\0\xff\xff\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0!\0\xff\xff!\0\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff!\0!\0\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0\xff\xff!\0\xff\xff\xff\xff!\0\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0"\0\xff\xff"\0\xff\xff"\0\xff\xff\xff\xff"\0\xff\xff\xff\xff"\0\xff\xff"\0"\0\xff\xff"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0\xff\xff\xff\xff\xff\xff"\0\xff\xff\xff\xff\xff\xff"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0\xff\xff"\0\xff\xff\xff\xff"\0\xff\xff"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0%\0\xff\xff%\0\xff\xff%\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff%\0\xff\xff%\0%\0\xff\xff%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0\xff\xff\xff\xff\xff\xff%\0\xff\xff\xff\xff\xff\xff%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0\xff\xff%\0\xff\xff\xff\xff%\0\xff\xff%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0&\0\xff\xff&\0\xff\xff&\0\xff\xff\xff\xff&\0\xff\xff\xff\xff&\0\xff\xff&\0&\0\xff\xff&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0\xff\xff\xff\xff(\0&\0\xff\xff(\0\xff\xff&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0(\0&\0\xff\xff\xff\xff&\0\xff\xff&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff*\0\xff\xff*\0\xff\xff*\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff*\0\xff\xff*\0*\0(\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0\xff\xff\xff\xff\xff\xff*\0*\0\xff\xff\xff\xff*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0\xff\xff*\0\xff\xff\xff\xff*\0\xff\xff*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0\xff\xff\xff\xff\xff\xff3\x001\0\xff\xff3\x001\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff,\0\xff\xff,\x001\0,\0\xff\xff\xff\xff3\0\xff\xff\xff\xff,\0\xff\xff,\0,\0\xff\xff,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\xff\xff\xff\xff\xff\xff,\0\xff\xff\xff\xff\xff\xff,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\xff\xff,\x003\x001\0,\0(\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\x009\0:\0<\x009\0:\0<\0=\0>\0?\0=\0>\0?\0@\0\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff9\0:\0<\0\xff\xff\xff\xff\xff\xff=\0>\0?\0\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff9\0:\0<\0\xff\xff\xff\xff\xff\xff=\0>\0?\0\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff:\0\xff\xff\xff\xff=\0\xff\xff<\0\xff\xff\xff\xff\xff\xff>\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff3\x001\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff9\0:\0<\0\xff\xff\xff\xff\xff\xff=\0>\0?\0\xff\xff\xff\xff\xff\xff@\0'),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\x02\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\t\0\x03\0\0\0\x07\0\x04\0\0\0\0\0\0\0\0\0\t\0\x0b\0\x05\0\r\0\x0f\0\x11\0\x13\0\x19\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\t\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x06\0\x06\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x06\0\x06\0\x06\0\x06\0\0\0\0\0\0\0\0\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\0\0\0\0\0\0\0\0\x01\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x0e\0\0\0\x11\0\0\0\x11\0\0\0\x11\0\0\0\x11\0\0\0\x11\0\0\0\x01\0\0\0\0\0\0\0\x11\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x0e\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x0e\0\0\0\x0e\0\0\0\0\0\0\0\x0e\0\0\0\0\0\x0e\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\b\x001\0\x07\0\b\x001\x003\0\xff\xff9\x003\0:\x009\0<\0:\0=\0<\0>\0=\0?\0>\0\xff\xff?\0\xff\xff\0\0@\0\b\x001\0@\0\0\0\x07\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff3\x009\0\xff\xff:\0\xff\xff<\0\xff\xff=\0\xff\xff>\0\xff\xff?\0\xff\xff8\0\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\b\x001\0\xff\xff\xff\xff\xff\xff3\0\xff\xff9\0\xff\xff:\0\xff\xff<\0\xff\xff=\0\xff\xff>\0\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff:\0\xff\xff=\0\xff\xff\xff\xff\xff\xff<\0\xff\xff\xff\xff>\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff2\0\x07\0\b\x001\x004\0;\0\xff\xff3\0\xff\xff9\0\xff\xff:\0\xff\xff<\0\xff\xff=\0\xff\xff>\0\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff@\0"),a("\xff\x03\xff\x02\xff\xff\x03\xff\xff\x01\x02\0\x03\xff\x04\xff\xff\x03\x04\x04\xff\xff")],mL=a(W),mJ=a(": "),mK=a(": syntax error parsing:"),mH=a(gh),mI=a(aY),mM=a(au),mS=a(bI),mP=a(W),mQ=[1,a(bI)],mR=a(bI),mO=a(bI),mN=a(W),mT=[0,[0,a(bg),[0,j,[0,k,[0,a(bg),[0,a(c0),[0,a(bf),0]]]]]],[0,[0,a(c0),[0,j,r]],0]],mU=a3([k,a(av),a(bk),a(be),a(P),a(W),a(aI),a(aX),a(aJ),a(au),a(bg)]),mV=a(bX),mW=[0,a(W),[0,j,bh]],mX=[0,a(aI),[0,j,r]],mY=[0,a(be),[0,j,r]],mZ=[0,a(bk),[0,j,[0,k,0]]],m0=[0,a(av),[0,j,[0,k,[0,a(bk),[0,a(be),[0,a(P),[0,a(W),[0,a(aI),[0,a(aX),[0,a(aJ),0]]]]]]]]]],m1=[0,a(a2),[0,j,h]],m2=[0,a(b4),[0,j,h]],m3=[0,a(V),[0,j,[0,k,[0,a(b4),[0,a(a2),0]]]]],m4=[0,a(aL),[0,j,r]],m5=[0,a(cW),[0,j,[0,k,[0,a(aL),0]]]],m6=[0,a(bS),[0,j,[0,k,[0,a(V),0]]]],m7=[0,a(bM),[0,j,[0,k,[0,a(V),0]]]],m8=[0,a(bG),[0,j,[0,k,[0,a(V),0]]]],m9=[0,a(c3),[0,j,b]],m_=[0,a(cN),[0,j,b]],m$=[0,a(bd),[0,j,[0,k,0]]],na=[0,a(cX),[0,j,[0,k,[0,a(bd),0]]]],nb=[0,a(O),[0,j,h]],nc=[0,a(bR),[0,j,h]],nd=[0,a(cI),[0,j,b]],ne=[0,a(d1),[0,aV,h]],nf=[0,a(dL),[0,aV,h]],ng=[0,a(ba),[0,j,r]],nh=[0,a(aX),[0,j,r]],ni=[0,a(cH),[0,j,r]],nk=[0,k,[0,a(P),0]],nl=a(au),nm=[0,a(bi),[0,l,r]],nn=[0,a(bf),[0,l,h]],no=[0,a(cK),[0,l,h]],np=[0,a(bF),[0,l,b]],nr=[0,k,[0,a(cN),0]],ns=a(a0),nt=[0,a(ax),[0,at,[0,k,[0,a(c3),[0,a(aL),[0,a(cW),[0,a(bG),[0,a(bS),[0,a(bM),[0,a(V),0]]]]]]]]]],nu=[0,a(bP),[0,l,r]],nw=[0,k,[0,a(P),0]],nx=[0,a(ay),0],ny=a(ay),nA=[0,k,[0,a(P),0]],nB=a(bW),nC=[0,a(b0),[0,l,h]],nD=[0,a(a$),[0,l,h]],nE=[0,a(a_),[0,at,[0,k,[0,a(cI),[0,a(bR),0]]]]],nF=[0,a(bT),[0,l,[0,C,[0,[0,a(aJ),[0,a(cM),[0,a(c1),[0,a(cP),[0,a(bY),[0,a(bQ),[0,a(b3),sj]]]]]]],b]]]],nG=[0,k,[0,a(O),0]],nH=a(cO),nI=[0,k,[0,a(O),0]],nJ=a(aZ),nK=[0,a(X),[0,at,[0,k,[0,a(O),0]]]],nL=[0,a(Y),[0,at,[0,k,[0,a(O),0]]]],nM=[0,a(bJ),[0,l,b]],nN=[0,a(bV),[0,l,b]],nO=[0,a(bD),[0,l,b]],nP=[0,a(bU),[0,l,b]],nQ=[0,a(bH),[0,l,b]],nR=[0,a(bZ),[0,l,b]],nS=[0,a(aK),[0,l,b]],nT=[0,a(c2),[0,b,[0,C,[0,[0,a(aW),[0,a(cG),[0,a(cS),[0,a(cD),[0,a(bc),[0,a(c2),[0,a(ay),sk]]]]]]],h]]]],nU=[0,a(bc),[0,b,[0,C,[0,[0,a(bc),0],b]]]],nV=[0,a(cD),[0,b,[0,k,0]]],nW=[0,a(cS),[0,b,[0,k,[0,a(cX),[0,a(bd),0]]]]],nX=[0,a(cG),[0,b,r]],nY=[0,a(cZ),[0,b,h]],nZ=[0,a(cR),[0,b,r]],n0=[0,a(b3),[0,b,h]],n2=[0,k,[0,a(ba),0]],n3=a(cM),n4=[0,a(gO),[0,b,h]],n6=[0,k,[0,a(cH),0]],n7=a(gD),n8=[0,a(P),[0,b,bh]],n_=[0,k,[0,a(ba),0]],n$=a(aJ),oa=[0,a(aj),[0,b,r]],ob=[0,a(aW),[0,b,[0,C,[0,[0,a(aW),0],b]]]],oc=[0,a(gv),[0,b,r]],od=[0,a(f3),[0,b,h]],oe=[0,a(a9),[0,b,h]],of=[0,a(bY),[0,b,h]],og=[0,a(bQ),[0,b,h]],oh=[0,a(d2),[0,b,h]],oi=[0,a(gk),[0,b,h]],oj=[0,a(dQ),[0,b,h]],ok=[0,a(ed),[0,b,h]],ol=[0,a(dP),[0,b,h]],om=[0,a(eg),[0,b,h]],on=[0,a(dS),[0,b,h]],oo=[0,a(dR),[0,b,h]],op=[0,a(dZ),[0,b,h]],oq=[0,a(eb),[0,b,h]],or=[0,a(gl),[0,b,h]],os=[0,a(dX),[0,b,h]],ot=[0,a(dO),[0,b,h]],ou=[0,a(cP),[0,b,h]],ov=[0,a(c1),[0,b,h]],ow=[0,a(ea),[0,b,h]],ox=[0,a(eh),[0,b,h]],oy=[0,a(d_),[0,b,h]],oP=a(W),oO=a(av),oM=a(au),oN=a(bX),oJ=a(av),oK=a(W),oI=a(W),oF=a(E),oG=a(aU),oH=a(gf),oE=a(av),oD=a(au),oA=[0,1],oB=[0,0],oC=[0,1],oL=[1,a(aI),[0,[0,a("http-equiv"),a("Content-Type")],[0,[0,a("content"),a("text/html; charset=utf-8")],0]],0,0],pr=a(E),ps=a(ak),pt=a(E),pu=a("[if !msEquation]"),pv=a(ak),pw=a(E),px=a(d0),pp=[0,a("data-ms-equation"),a(d3)],pe=a(E),pf=a(ak),pg=a(E),ph=a("[if !vml]"),pi=a(ak),pj=a(E),pk=a(d0),pc=a(dM),o9=a(aj),o_=a(aj),o8=a(aj),o$=a(aj),o4=a(dM),o2=a("v:imagedata"),o3=a(aj),o0=a("vshapedata"),oX=[0,[0,a("rtf-data-image"),a(d3)],0],oY=a("data-image-id"),oU=a("rotation"),oQ=a("-90"),oR=a("90"),oS=a(bO),oT=a(a1),oV=a(bO),oW=a(a1),oZ=a("unsupported"),o7=a(aj),pa=a("[if gte vml 1]>"),pb=a(gE),pl=a(aU),pn=a("[if gte msEquation"),po=a(gE),py=a(aU),pA=[0,0],pD=a(E),pE=a("[if !supportLists]"),pF=a(ak),pG=a(aU),qn=a(f8),qo=a(O),qp=a(bE),qr=[0,a(f_),a(d3)],qq=a(O),qk=a(aY),ql=a(aY),qm=a(dK),qj=a(gh),qs=a(Y),qt=a(aK),qh=[0,0,0],qg=[0,0,0,0],qe=a(cF),qf=[0,a(cQ),0],qi=[0,0,0],qc=a("mso-text-indent-alt"),qd=a(gN),p$=a("mso-level-legacy"),qa=a("yes"),qb=[0,a(Y),0,0],p8=a(f6),p9=a(cQ),p_=[0,a(cF)],p5=a("margin-top"),p6=a("margin-bottom"),pX=[0,[0,a(Y),0]],pR=a("alpha-lower"),pS=a("alpha-upper"),pT=a("bullet"),pU=a("image"),pV=a("roman-lower"),pW=a("roman-upper"),pY=[0,[0,a(X),[0,a("upper-roman")]]],pZ=[0,[0,a(X),[0,a("lower-roman")]]],p0=[0,[0,a(X),[0,a("upper-alpha")]]],p1=[0,[0,a(X),[0,a("lower-alpha")]]],p2=a("mso-level-number-format"),p3=[0,a(X),0],pO=[0,[0,a(Y),[0,a("square")]]],pP=[0,[0,a(Y),[0,a("circle")]]],pQ=a("mso-level-text"),pN=a("0"),pM=a(ef),pJ=[0,a(gC)],pK=a(Y),pL=a("mso-level-start-at"),pH=[0,[0,a(cF)],[0,[0,a(cQ),0]]],pI=a(O),pC=a(gK),qA=[0,1],qz=a(X),qw=a(ef),qx=a(dK),qy=a(ef),qu=a3([a(b3),a(a9),a(ea),a(eh),a(dO),a(bY),a(bQ),a(eb),a(dZ),a(eg),a(d2),a(dQ),a(dS),a(dR),a(dP),a(d_),a(dX),a(dL),a(d1),a(ed),a(av)]),q7=a(a9),q4=a(bE),q5=a(ee),q3=a(ee),q1=a(bE),q2=a(ee),qZ=a(bE),q0=a("Apple-converted-space"),qS=a(E),qO=a(aU),qP=a("---"),qN=a(E),qI=a(gf),qJ=a("endfragment"),qK=a("[if "),qL=a("[endif"),qG=a(aW),qE=a("name"),qF=a("OLE_LINK"),qT=a(ak),qV=[0,a("lang"),[0,a("onmouseover"),[0,a("onmouseout"),[0,a(dK),0]]]],qW=[0,a(bE),[0,a(f_),[0,a(f8),[0,a(gN),0]]]],rj=a(a$),rh=a("mso-element"),ri=[0,a("para-border-div"),0],rf=a("border"),rd=a("margin-left"),re=a("data-border-margin"),rb=a(aK),q9=a(a9),q_=a(aZ),q$=a(a9),ra=a(aZ),rB=a("mso-"),rC=a(gK),rp=a("font-stretch"),rq=a("font-variant-caps"),rr=a("text-decoration"),rs=a("text-indent"),rt=a("text-transform"),ru=a("vertical-align"),rv=a("white-space"),rw=a("word-spacing"),rz=a("baseline"),ry=a("normal"),rA=a(cQ),rx=a("0px"),rl=[0,a(gC),[0,a(cF),[0,a(f6),0]]],rn=a3([a("layout-grid-mode"),a("tab-stops"),a(gq),a("text-underline"),a("text-effect"),a("text-line-through"),a("page"),a("font-color"),a("horiz-align"),a("language"),a("separator-image"),a("table-border-color-dark"),a("table-border-color-light"),a("vert-align"),a("widows"),a("letter-spacing"),a("caret-color"),a("orphans")]),rE=[0,a(ax),[0,a(V),[0,a(a2),[0,a(aL),0]]]],rT=a(bO),rU=a(a1),rS=[0,a(a2),[0,a(aL),0]],rR=a(V),rP=a(a1),rQ=a(bO),rO=a(ax),rM=a(ax),rL=a(a1),rJ=a(a1),rI=a("px"),rH=[0,a(a1),[0,a(bO),0]],rY=a(gq),rZ=a("data-tab-interval"),rX=[0,1],rW=[0,0],r1=a("Js_of_ocaml__Js.Error"),r2=a(dU),r9=a(g),r_=a(fX),r5=a("function"),sc=a("ephox.wimp");function
  ex(a){throw[0,cb,a]}function
  K(a){throw[0,da,a]}S(0);function
  i(d,c){var
  a=o(d),e=o(c),b=J(a+e|0);bm(d,0,b,0,a);bm(c,0,b,a,e);return aR(b)}function
  hg(a){try{var
  b=[0,gZ(a)];return b}catch(a){a=B(a);if(a[1]===cb)return 0;throw a}}function
  w(a,b){if(a){var
  c=a[1];return[0,c,w(a[2],b)]}return b}sP(0);var
  db=g2(1);g2(2);function
  dc(a){g3(db,a,0,o(a));sS(db,10);return er(db)}function
  hh(b){function
  a(b){var
  a=b;for(;;){if(a){var
  c=a[2],d=a[1];try{er(d)}catch(a){a=B(a);if(a[1]!==ey)throw a;var
  e=a}var
  a=c;continue}return 0}}return a(sQ(0))}function
  ez(h,g,f){var
  a=g,e=f;for(;;){var
  b=c(e,0);if(b){var
  i=b[2],a=d(h,a,b[1]),e=i;continue}return a}}function
  hi(a){var
  b=65<=a?90<a?0:1:0;if(!b){var
  c=192<=a?214<a?0:1:0;if(!c){var
  d=216<=a?222<a?1:0:1;if(d)return a}}return a+32|0}var
  dd=s5(0),bs=(4*dd|0)-1|0;function
  hj(a){if(65<=a)if(!(90<a))return a+32|0;return a}S(0);var
  hk=s4(0);function
  aS(c){var
  b=0,a=c;for(;;){if(a){var
  b=b+1|0,a=a[2];continue}return b}}function
  bt(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[1],b],a=a[2],b=e;continue}return b}}function
  A(a){return bt(a,0)}typeof
  hk==="number";function
  ad(b,a){if(a){var
  d=a[2],e=c(b,a[1]);return[0,e,ad(b,d)]}return 0}function
  a4(d,b){var
  a=b;for(;;){if(a){var
  e=a[2];c(d,a[1]);var
  a=e;continue}return 0}}function
  aT(f,e,c){var
  b=e,a=c;for(;;){if(a){var
  g=a[2],b=d(f,b,a[1]),a=g;continue}return b}}function
  a5(c,a,b){if(a){var
  e=a[1];return d(c,e,a5(c,a[2],b))}return b}function
  ao(e,d){var
  a=d;for(;;){if(a){var
  f=a[2],b=c(e,a[1]);if(b)return b;var
  a=f;continue}return 0}}function
  eA(d,c){var
  a=c;for(;;){if(a){var
  e=a[2],b=0===bn(a[1],d)?1:0;if(b)return b;var
  a=e;continue}return 0}}function
  a6(d,c){var
  a=c;for(;;){if(a){var
  b=a[1],e=a[2],f=b[2];if(0===bn(b[1],d))return f;var
  a=e;continue}throw q}}var
  hl=J(0);function
  de(c,b,a){if(0<=b)if(0<=a)if(!((aQ(c)-a|0)<b)){var
  d=J(a);az(c,b,d,0,a);return d}return K(hm)}function
  df(c,b,a){return aR(de(c,b,a))}function
  eB(b){var
  a=b-9|0,c=4<a>>>0?23===a?1:0:2===a?0:1;return c?1:0}function
  eC(g,b){var
  d=aQ(b);if(0===d)return b;var
  e=J(d),f=d-1|0,h=0;if(!(f<0)){var
  a=h;for(;;){gS(e,a,c(g,c5(b,a)));var
  i=a+1|0;if(f!==a){var
  a=i;continue}break}}return e}function
  dg(b,c){var
  a=J(b);st(a,0,b,c);return aR(a)}function
  ae(c,b,a){return aR(de(b6(c),b,a))}function
  af(k,g){if(g){var
  h=o(k),c=0,b=g,q=0;for(;;){if(b){var
  i=b[1];if(b[2]){var
  j=(o(i)+h|0)+c|0,m=b[2],n=c<=j?j:K(ho),c=n,b=m;continue}var
  l=o(i)+c|0}else
  var
  l=c;var
  f=J(l),e=q,d=g;for(;;){if(d){var
  a=d[1];if(d[2]){var
  p=d[2];bm(a,0,f,e,o(a));bm(k,0,f,e+o(a)|0,h);var
  e=(e+o(a)|0)+h|0,d=p;continue}bm(a,0,f,e,o(a))}return aR(f)}}}return hp}function
  eD(b){var
  a=b-9|0,c=4<a>>>0?23===a?1:0:2===a?0:1;return c?1:0}function
  eE(e,d,c,b){var
  a=c;for(;;){if(d<=a)throw q;if(aC(e,a)===b)return a;var
  a=a+1|0;continue}}function
  hr(a,b){return eE(a,o(a),0,b)}function
  hs(b,a,d){var
  c=o(b);if(0<=a)if(!(c<a))try{eE(b,c,a,d);var
  e=1;return e}catch(a){a=B(a);if(a===q)return 0;throw a}return K(ht)}function
  eF(b,a){return hs(b,0,a)}function
  dh(a){return aR(eC(hj,b6(a)))}var
  hu=b_;function
  hv(a){return aR(eC(hi,b6(a)))}function
  eG(e,b){var
  d=b.length-1;if(0===d)return[0];var
  f=aP(d,c(e,b[1])),g=d-1|0,h=1;if(!(g<1)){var
  a=h;for(;;){f[1+a]=c(e,b[1+a]);var
  i=a+1|0;if(g!==a){var
  a=i;continue}break}}return f}S(0);function
  ap(f,e,a){var
  c=sL(f,e,a),d=0<=c?1:0,g=d?a[12]!==cc?1:0:d;if(g){a[11]=a[12];var
  b=a[12];a[12]=[0,b[1],b[2],b[3],a[4]+a[6]|0]}return c}function
  eI(e,d){var
  f=e?e[1]:1,g=f?eH:cc,h=f?eH:cc,c=b6(d),a=aQ(c),b=J(a);az(c,0,b,0,a);var
  i=[0],j=1,k=0,l=0,m=0,n=0,p=0,q=o(d);return[0,function(a){a[9]=1;return 0},b,q,p,n,m,l,k,j,i,h,g]}function
  x(a){return df(a[2],a[5],a[6]-a[5]|0)}function
  di(c,a,b){return df(c[2],a,b-a|0)}function
  eJ(e){function
  r(a){return a?a[4]:0}function
  f(b,e,a){var
  c=b?b[4]:0,d=a?a[4]:0,f=d<=c?c+1|0:d+1|0;return[0,b,e,a,f]}function
  g(b,e,a){var
  g=b?b[4]:0,h=a?a[4]:0;if((h+2|0)<g){if(b){var
  c=b[3],k=b[2],i=b[1],m=r(c);if(m<=r(i))return f(i,k,f(c,e,a));if(c){var
  n=c[2],o=c[1],p=f(c[3],e,a);return f(f(i,k,o),n,p)}return K(hx)}return K(hy)}if((g+2|0)<h){if(a){var
  j=a[3],l=a[2],d=a[1],q=r(d);if(q<=r(j))return f(f(b,e,d),l,j);if(d){var
  s=d[2],t=d[1],u=f(d[3],l,j);return f(f(b,e,t),s,u)}return K(hz)}return K(hA)}var
  v=h<=g?g+1|0:h+1|0;return[0,b,e,a,v]}function
  a(c,b){if(b){var
  f=b[3],h=b[2],i=b[1],j=d(e[1],c,h);if(0===j)return b;if(0<=j){var
  k=a(c,f);return f===k?b:g(i,h,k)}var
  l=a(c,i);return i===l?b:g(l,h,f)}return[0,0,c,0,1]}function
  h(a){return[0,0,a,0,1]}function
  C(b,a){if(a){var
  c=a[3],d=a[2];return g(C(b,a[1]),d,c)}return h(b)}function
  D(b,a){if(a){var
  c=a[2],d=a[1];return g(d,c,D(b,a[3]))}return h(b)}function
  b(c,d,a){if(c){if(a){var
  e=a[4],h=c[4],i=a[3],j=a[2],k=a[1],l=c[3],m=c[2],n=c[1];return(e+2|0)<h?g(n,m,b(l,d,a)):(h+2|0)<e?g(b(c,d,k),j,i):f(c,d,a)}return D(d,c)}return C(d,a)}function
  l(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return a[2]}throw q}}function
  E(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return[0,a[2]]}return 0}}function
  F(b){var
  a=b;for(;;){if(a){if(a[3]){var
  a=a[3];continue}return a[2]}throw q}}function
  P(b){var
  a=b;for(;;){if(a){if(a[3]){var
  a=a[3];continue}return[0,a[2]]}return 0}}function
  t(a){if(a){var
  b=a[1];if(b){var
  c=a[3],d=a[2];return g(t(b),d,c)}return a[3]}return K(hB)}function
  m(c,a){if(c){if(a){var
  d=t(a);return b(c,l(a),d)}return c}return a}function
  i(c,a){if(a){var
  f=a[3],g=a[2],h=a[1],l=d(e[1],c,g);if(0===l)return[0,h,1,f];if(0<=l){var
  j=i(c,f),m=j[3],n=j[2];return[0,b(h,g,j[1]),n,m]}var
  k=i(c,h),o=k[2],p=k[1];return[0,p,o,b(k[3],g,f)]}return hC}var
  u=0;function
  Q(a){return a?0:1}function
  R(g,f){var
  a=f;for(;;){if(a){var
  h=a[3],i=a[1],b=d(e[1],g,a[2]),c=0===b?1:0;if(c)return c;var
  j=0<=b?h:i,a=j;continue}return 0}}function
  v(f,b){if(b){var
  a=b[3],h=b[2],c=b[1],i=d(e[1],f,h);if(0===i){if(c){if(a){var
  m=t(a);return g(c,l(a),m)}return c}return a}if(0<=i){var
  j=v(f,a);return a===j?b:g(c,h,j)}var
  k=v(f,c);return c===k?b:g(k,h,a)}return 0}function
  j(d,c){if(d){if(c){var
  g=c[4],e=c[2],h=d[4],f=d[2],m=c[3],n=c[1],o=d[3],p=d[1];if(g<=h){if(1===g)return a(e,d);var
  k=i(f,c),q=k[1],r=j(o,k[3]);return b(j(p,q),f,r)}if(1===h)return a(f,c);var
  l=i(e,d),s=l[1],t=j(l[3],m);return b(j(s,n),e,t)}return d}return c}function
  n(a,d){if(a){if(d){var
  e=a[3],f=a[2],g=a[1],c=i(f,d),h=c[1];if(c[2]){var
  j=n(e,c[3]);return b(n(g,h),f,j)}var
  k=n(e,c[3]);return m(n(g,h),k)}return 0}return 0}function
  w(f,a){if(a){var
  j=a[3],g=a[2],k=a[1],l=d(e[1],f,g);if(0===l)return 0;if(0<=l){var
  h=w(f,j);if(h){var
  m=h[2];return[0,b(k,g,h[1]),m]}return 0}var
  i=w(f,k);if(i){var
  n=i[2],o=i[1];return[0,o,function(a){return b(c(n,0),g,j)}]}return 0}return[0,0,function(a){return 0}]}function
  G(g,f){var
  a=g,b=f;for(;;){if(a)if(b){var
  h=a[3],i=a[2],j=a[1];if(a===b)return 0;var
  d=w(i,b);if(d){var
  k=d[2],e=G(j,d[1]);if(e){var
  a=h,b=c(k,0);continue}return e}return 0}return 1}}function
  o(a,d){if(a){if(d){var
  e=a[3],f=a[2],g=a[1],c=i(f,d),h=c[1];if(c[2]){var
  j=o(e,c[3]);return m(o(g,h),j)}var
  k=o(e,c[3]);return b(o(g,h),f,k)}return a}return 0}function
  k(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[2],a[3],b],a=a[1],b=e;continue}return b}}function
  H(m,l){var
  n=k(l,0),b=k(m,0),a=n;for(;;){if(b){if(a){var
  f=a[3],g=a[2],h=b[3],i=b[2],c=d(e[1],b[1],a[1]);if(0===c){var
  j=k(g,f),b=k(i,h),a=j;continue}return c}return 1}return a?-1:0}}function
  S(b,a){return 0===H(b,a)?1:0}function
  s(o,n){var
  a=o,b=n;for(;;){if(a){if(b){var
  h=b[3],i=b[1],c=a[3],f=a[2],g=a[1],j=d(e[1],f,b[2]);if(0===j){var
  k=s(g,i);if(k){var
  a=c,b=h;continue}return k}if(0<=j){var
  l=s([0,0,f,c,0],h);if(l){var
  a=g;continue}return l}var
  m=s([0,g,f,0,0],i);if(m){var
  a=c;continue}return m}return 0}return 1}}function
  I(b,d){var
  a=d;for(;;){if(a){var
  e=a[3],f=a[2];I(b,a[1]);c(b,f);var
  a=e;continue}return 0}}function
  J(c,f,e){var
  a=f,b=e;for(;;){if(a){var
  g=a[3],h=a[2],i=d(c,h,J(c,a[1],b)),a=g,b=i;continue}return b}}function
  L(b,g){var
  a=g;for(;;){if(a){var
  h=a[3],i=a[1],d=c(b,a[2]);if(d){var
  e=L(b,i);if(e){var
  a=h;continue}var
  f=e}else
  var
  f=d;return f}return 1}}function
  M(b,g){var
  a=g;for(;;){if(a){var
  h=a[3],i=a[1],d=c(b,a[2]);if(d)var
  e=d;else{var
  f=M(b,i);if(!f){var
  a=h;continue}var
  e=f}return e}return 0}}function
  x(d,a){if(a){var
  g=a[3],h=a[2],i=a[1],e=x(d,i),j=c(d,h),f=x(d,g);if(j){if(i===e)if(g===f)return a;return b(e,h,f)}return m(e,f)}return 0}function
  y(d,a){if(a){var
  e=a[2],l=a[3],f=y(d,a[1]),g=f[2],h=f[1],n=c(d,e),i=y(d,l),j=i[2],k=i[1];if(n){var
  o=m(g,j);return[0,b(h,e,k),o]}var
  p=b(g,e,j);return[0,m(h,k),p]}return hD}function
  z(a){if(a){var
  b=a[1],c=z(a[3]);return(z(b)+1|0)+c|0}return 0}function
  N(d,c){var
  b=d,a=c;for(;;){if(a){var
  e=a[2],f=a[1],b=[0,e,N(b,a[3])],a=f;continue}return b}}function
  T(a){return N(0,a)}function
  U(g,f){var
  a=f;for(;;){if(a){var
  b=a[2],h=a[3],i=a[1],c=d(e[1],g,b);if(0===c)return b;var
  j=0<=c?h:i,a=j;continue}throw q}}function
  V(f,j){var
  b=j;for(;;){if(b){var
  g=b[2],k=b[3],l=b[1];if(c(f,g)){var
  d=g,a=l;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(c(f,e)){var
  d=e,a=i;continue}var
  a=h;continue}return d}}var
  b=k;continue}throw q}}function
  W(f,j){var
  b=j;for(;;){if(b){var
  g=b[2],k=b[3],l=b[1];if(c(f,g)){var
  d=g,a=l;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(c(f,e)){var
  d=e,a=i;continue}var
  a=h;continue}return[0,d]}}var
  b=k;continue}return 0}}function
  X(f,j){var
  b=j;for(;;){if(b){var
  g=b[2],k=b[3],l=b[1];if(c(f,g)){var
  d=g,a=k;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(c(f,e)){var
  d=e,a=h;continue}var
  a=i;continue}return d}}var
  b=l;continue}throw q}}function
  Y(f,j){var
  b=j;for(;;){if(b){var
  g=b[2],k=b[3],l=b[1];if(c(f,g)){var
  d=g,a=k;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(c(f,e)){var
  d=e,a=h;continue}var
  a=i;continue}return[0,d]}}var
  b=l;continue}return 0}}function
  Z(g,f){var
  a=f;for(;;){if(a){var
  b=a[2],h=a[3],i=a[1],c=d(e[1],g,b);if(0===c)return[0,b];var
  j=0<=c?h:i,a=j;continue}return 0}}function
  A(k,f){if(f){var
  m=f[3],n=f[2],o=f[1],g=A(k,o),h=c(k,n),i=A(k,m);if(o===g)if(n===h)if(m===i)return f;if(0===g)var
  p=0;else
  var
  s=F(g),p=0<=d(e[1],s,h)?1:0;if(!p){if(0===i)var
  q=0;else
  var
  r=l(i),q=0<=d(e[1],h,r)?1:0;if(!q)return b(g,h,i)}return j(g,a(h,i))}return 0}function
  _(c){if(c){var
  k=c[2],g=c[1];if(k){var
  l=k[2],i=k[1];if(l){var
  n=l[2],o=l[1];if(n){var
  q=n[2],s=n[1];if(q){if(q[2]){var
  b=e[1],m=function(j,g){if(2===j){if(g){var
  m=g[2];if(m){var
  n=m[1],k=g[1],J=m[2],y=d(b,k,n),K=0===y?[0,k,0]:0<=y?[0,n,[0,k,0]]:[0,k,[0,n,0]];return[0,K,J]}}}else
  if(3===j)if(g){var
  o=g[2];if(o){var
  p=o[2];if(p){var
  c=p[1],a=o[1],e=g[1],N=p[2],D=d(b,e,a);if(0===D)var
  E=d(b,a,c),O=0===E?[0,a,0]:0<=E?[0,c,[0,a,0]]:[0,a,[0,c,0]],q=O;else
  if(0<=D){var
  F=d(b,e,c);if(0===F)var
  r=[0,a,[0,e,0]];else
  if(0<=F)var
  G=d(b,a,c),P=0===G?[0,a,[0,e,0]]:0<=G?[0,c,[0,a,[0,e,0]]]:[0,a,[0,c,[0,e,0]]],r=P;else
  var
  r=[0,a,[0,e,[0,c,0]]];var
  q=r}else{var
  H=d(b,a,c);if(0===H)var
  s=[0,e,[0,a,0]];else
  if(0<=H)var
  I=d(b,e,c),Q=0===I?[0,e,[0,a,0]]:0<=I?[0,c,[0,e,[0,a,0]]]:[0,e,[0,c,[0,a,0]]],s=Q;else
  var
  s=[0,e,[0,a,[0,c,0]]];var
  q=s}return[0,q,N]}}}var
  z=j>>1,A=x(z,g),L=A[1],B=x(j-z|0,A[2]),i=L,h=B[1],f=0,M=B[2];for(;;){if(i){if(h){var
  t=h[2],u=h[1],v=i[2],l=i[1],w=d(b,l,u);if(0===w){var
  i=v,h=t,f=[0,l,f];continue}if(0<w){var
  i=v,f=[0,l,f];continue}var
  h=t,f=[0,u,f];continue}var
  C=bt(i,f)}else
  var
  C=bt(h,f);return[0,C,M]}},x=function(j,g){if(2===j){if(g){var
  n=g[2];if(n){var
  o=n[1],k=g[1],J=n[2],y=d(b,k,o),K=0===y?[0,k,0]:0<y?[0,k,[0,o,0]]:[0,o,[0,k,0]];return[0,K,J]}}}else
  if(3===j)if(g){var
  p=g[2];if(p){var
  q=p[2];if(q){var
  c=q[1],a=p[1],e=g[1],N=q[2],D=d(b,e,a);if(0===D)var
  E=d(b,a,c),O=0===E?[0,a,0]:0<E?[0,a,[0,c,0]]:[0,c,[0,a,0]],r=O;else
  if(0<D){var
  F=d(b,a,c);if(0===F)var
  s=[0,e,[0,a,0]];else
  if(0<F)var
  s=[0,e,[0,a,[0,c,0]]];else
  var
  G=d(b,e,c),P=0===G?[0,e,[0,a,0]]:0<G?[0,e,[0,c,[0,a,0]]]:[0,c,[0,e,[0,a,0]]],s=P;var
  r=s}else{var
  H=d(b,e,c);if(0===H)var
  t=[0,a,[0,e,0]];else
  if(0<H)var
  t=[0,a,[0,e,[0,c,0]]];else
  var
  I=d(b,a,c),Q=0===I?[0,a,[0,e,0]]:0<I?[0,a,[0,c,[0,e,0]]]:[0,c,[0,a,[0,e,0]]],t=Q;var
  r=t}return[0,r,N]}}}var
  z=j>>1,A=m(z,g),L=A[1],B=m(j-z|0,A[2]),i=L,h=B[1],f=0,M=B[2];for(;;){if(i){if(h){var
  u=h[2],v=h[1],w=i[2],l=i[1],x=d(b,l,v);if(0===x){var
  i=w,h=u,f=[0,l,f];continue}if(0<=x){var
  h=u,f=[0,v,f];continue}var
  i=w,f=[0,l,f];continue}var
  C=bt(i,f)}else
  var
  C=bt(h,f);return[0,C,M]}},r=aS(c),t=2<=r?m(r,c)[1]:c,j=function(b,a){if(!(3<b>>>0))switch(b){case
  0:return[0,0,a];case
  1:if(a)return[0,[0,0,a[1],0,1],a[2]];break;case
  2:if(a){var
  d=a[2];if(d)return[0,[0,[0,0,a[1],0,1],d[1],0,2],d[2]]}break;default:if(a){var
  e=a[2];if(e){var
  g=e[2];if(g)return[0,[0,[0,0,a[1],0,1],e[1],[0,0,g[1],0,1],2],g[2]]}}}var
  h=b/2|0,i=j(h,a),c=i[2],l=i[1];if(c){var
  m=c[1],k=j((b-h|0)-1|0,c[2]),n=k[2];return[0,f(l,m,k[1]),n]}throw[0,p,hE]};return j(aS(t),t)[1]}var
  v=q[1];return a(v,a(s,a(o,a(i,h(g)))))}return a(s,a(o,a(i,h(g))))}return a(o,a(i,h(g)))}return a(i,h(g))}return h(g)}return u}function
  O(c,b){return ez(function(c,b){return a(b,c)},b,c)}function
  $(a){return O(a,u)}function
  B(a,d){if(a){var
  b=a[1],c=k(a[2],a[3]);return[0,b,function(a){return B(c,a)}]}return 0}function
  aa(a){var
  b=k(a,0);return function(a){return B(b,a)}}return[0,u,Q,R,a,h,v,j,n,G,o,H,S,s,I,A,J,L,M,x,y,z,T,l,E,F,P,l,E,i,U,Z,V,W,X,Y,_,function(j,i){var
  a=i,b=0;for(;;){if(a){var
  c=a[3],f=a[2],k=a[1],g=d(e[1],f,j);if(0!==g){if(0<=g){var
  a=k,b=[0,f,c,b];continue}var
  a=c;continue}var
  h=[0,f,c,b]}else
  var
  h=b;return function(a){return B(h,a)}}},aa,O,$]}function
  dj(g){function
  h(a){return a?a[5]:0}function
  b(b,f,e,a){var
  c=h(b),d=h(a),g=d<=c?c+1|0:d+1|0;return[0,b,f,e,a,g]}function
  r(b,a){return[0,0,b,a,0,1]}function
  a(c,g,f,a){var
  i=c?c[5]:0,j=a?a[5]:0;if((j+2|0)<i){if(c){var
  d=c[4],m=c[3],n=c[2],k=c[1],q=h(d);if(q<=h(k))return b(k,n,m,b(d,g,f,a));if(d){var
  r=d[3],s=d[2],t=d[1],u=b(d[4],g,f,a);return b(b(k,n,m,t),s,r,u)}return K(hF)}return K(hG)}if((i+2|0)<j){if(a){var
  l=a[4],o=a[3],p=a[2],e=a[1],v=h(e);if(v<=h(l))return b(b(c,g,f,e),p,o,l);if(e){var
  w=e[3],x=e[2],y=e[1],z=b(e[4],p,o,l);return b(b(c,g,f,y),x,w,z)}return K(hH)}return K(hI)}var
  A=j<=i?i+1|0:j+1|0;return[0,c,g,f,a,A]}var
  B=0;function
  N(a){return a?0:1}function
  l(e,c,b){if(b){var
  f=b[4],i=b[3],j=b[2],h=b[1],o=b[5],k=d(g[1],e,j);if(0===k)return i===c?b:[0,h,e,c,f,o];if(0<=k){var
  m=l(e,c,f);return f===m?b:a(h,j,i,m)}var
  n=l(e,c,h);return h===n?b:a(n,j,i,f)}return[0,0,e,c,0,1]}function
  O(e,c){var
  a=c;for(;;){if(a){var
  f=a[4],h=a[3],i=a[1],b=d(g[1],e,a[2]);if(0===b)return h;var
  j=0<=b?f:i,a=j;continue}throw q}}function
  P(g,l){var
  b=l;for(;;){if(b){var
  h=b[2],m=b[4],n=b[3],o=b[1];if(c(g,h)){var
  e=h,d=n,a=o;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(c(g,f)){var
  e=f,d=j,a=k;continue}var
  a=i;continue}return[0,e,d]}}var
  b=m;continue}throw q}}function
  Q(g,l){var
  b=l;for(;;){if(b){var
  h=b[2],m=b[4],n=b[3],o=b[1];if(c(g,h)){var
  e=h,d=n,a=o;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(c(g,f)){var
  e=f,d=j,a=k;continue}var
  a=i;continue}return[0,[0,e,d]]}}var
  b=m;continue}return 0}}function
  R(g,l){var
  b=l;for(;;){if(b){var
  h=b[2],m=b[4],n=b[3],o=b[1];if(c(g,h)){var
  e=h,d=n,a=m;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(c(g,f)){var
  e=f,d=j,a=i;continue}var
  a=k;continue}return[0,e,d]}}var
  b=o;continue}throw q}}function
  S(g,l){var
  b=l;for(;;){if(b){var
  h=b[2],m=b[4],n=b[3],o=b[1];if(c(g,h)){var
  e=h,d=n,a=m;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(c(g,f)){var
  e=f,d=j,a=i;continue}var
  a=k;continue}return[0,[0,e,d]]}}var
  b=o;continue}return 0}}function
  T(e,c){var
  a=c;for(;;){if(a){var
  f=a[4],h=a[3],i=a[1],b=d(g[1],e,a[2]);if(0===b)return[0,h];var
  j=0<=b?f:i,a=j;continue}return 0}}function
  U(f,e){var
  a=e;for(;;){if(a){var
  h=a[4],i=a[1],b=d(g[1],f,a[2]),c=0===b?1:0;if(c)return c;var
  j=0<=b?h:i,a=j;continue}return 0}}function
  m(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return[0,a[2],a[3]]}throw q}}function
  C(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return[0,[0,a[2],a[3]]]}return 0}}function
  V(b){var
  a=b;for(;;){if(a){if(a[4]){var
  a=a[4];continue}return[0,a[2],a[3]]}throw q}}function
  W(b){var
  a=b;for(;;){if(a){if(a[4]){var
  a=a[4];continue}return[0,[0,a[2],a[3]]]}return 0}}function
  s(b){if(b){var
  c=b[1];if(c){var
  d=b[4],e=b[3],f=b[2];return a(s(c),f,e,d)}return b[4]}return K(hJ)}function
  D(c,b){if(c){if(b){var
  d=m(b),e=d[2],f=d[1];return a(c,f,e,s(b))}return c}return b}function
  t(f,b){if(b){var
  c=b[4],i=b[3],h=b[2],e=b[1],j=d(g[1],f,h);if(0===j)return D(e,c);if(0<=j){var
  k=t(f,c);return c===k?b:a(e,h,i,k)}var
  l=t(f,e);return e===l?b:a(l,h,i,c)}return 0}function
  u(e,i,b){if(b){var
  f=b[4],j=b[3],k=b[2],h=b[1],r=b[5],l=d(g[1],e,k);if(0===l){var
  m=c(i,[0,j]);if(m){var
  n=m[1];return j===n?b:[0,h,e,n,f,r]}return D(h,f)}if(0<=l){var
  o=u(e,i,f);return f===o?b:a(h,k,j,o)}var
  p=u(e,i,h);return h===p?b:a(p,k,j,f)}var
  q=c(i,0);return q?[0,0,e,q[1],0,1]:0}function
  E(b,c){var
  a=c;for(;;){if(a){var
  e=a[4],f=a[3],g=a[2];E(b,a[1]);d(b,g,f);var
  a=e;continue}return 0}}function
  v(b,a){if(a){var
  d=a[5],e=a[4],f=a[3],g=a[2],h=v(b,a[1]),i=c(b,f);return[0,h,g,i,v(b,e),d]}return 0}function
  w(b,a){if(a){var
  c=a[2],e=a[5],f=a[4],g=a[3],h=w(b,a[1]),i=d(b,c,g);return[0,h,c,i,w(b,f),e]}return 0}function
  F(c,e,d){var
  a=e,b=d;for(;;){if(a){var
  f=a[4],g=a[3],h=a[2],i=ai(c,h,g,F(c,a[1],b)),a=f,b=i;continue}return b}}function
  G(b,g){var
  a=g;for(;;){if(a){var
  h=a[4],i=a[1],c=d(b,a[2],a[3]);if(c){var
  e=G(b,i);if(e){var
  a=h;continue}var
  f=e}else
  var
  f=c;return f}return 1}}function
  H(b,g){var
  a=g;for(;;){if(a){var
  h=a[4],i=a[1],c=d(b,a[2],a[3]);if(c)var
  e=c;else{var
  f=H(b,i);if(!f){var
  a=h;continue}var
  e=f}return e}return 0}}function
  I(d,c,b){if(b){var
  e=b[4],f=b[3],g=b[2];return a(I(d,c,b[1]),g,f,e)}return r(d,c)}function
  J(d,c,b){if(b){var
  e=b[3],f=b[2],g=b[1];return a(g,f,e,J(d,c,b[4]))}return r(d,c)}function
  e(d,g,f,c){if(d){if(c){var
  h=c[5],i=d[5],j=c[4],k=c[3],l=c[2],m=c[1],n=d[4],o=d[3],p=d[2],q=d[1];return(h+2|0)<i?a(q,p,o,e(n,g,f,c)):(i+2|0)<h?a(e(d,g,f,m),l,k,j):b(d,g,f,c)}return J(g,f,d)}return I(g,f,c)}function
  n(b,a){if(b){if(a){var
  c=m(a),d=c[2],f=c[1];return e(b,f,d,s(a))}return b}return a}function
  o(c,d,b,a){return b?e(c,d,b[1],a):n(c,a)}function
  i(b,a){if(a){var
  c=a[4],f=a[3],h=a[2],j=a[1],m=d(g[1],b,h);if(0===m)return[0,j,[0,f],c];if(0<=m){var
  k=i(b,c),n=k[3],o=k[2];return[0,e(j,h,f,k[1]),o,n]}var
  l=i(b,j),p=l[2],q=l[1];return[0,q,p,e(l[3],h,f,c)]}return hK}function
  j(c,b,a){if(b){var
  d=b[2],k=b[5],l=b[4],m=b[3],n=b[1];if(h(a)<=k){var
  e=i(d,a),q=e[2],r=e[1],s=j(c,l,e[3]),t=ai(c,d,[0,m],q);return o(j(c,n,r),d,t,s)}}else
  if(!a)return 0;if(a){var
  f=a[2],u=a[4],v=a[3],w=a[1],g=i(f,b),x=g[2],y=g[1],z=j(c,g[3],u),A=ai(c,f,x,[0,v]);return o(j(c,y,w),f,A,z)}throw[0,p,hL]}function
  k(c,b,a){if(b){if(a){var
  j=a[3],d=a[2],l=b[3],f=b[2],u=a[4],v=a[1],w=b[4],x=b[1];if(a[5]<=b[5]){var
  g=i(f,a),m=g[2],y=g[3],n=k(c,x,g[1]),p=k(c,w,y);return m?o(n,f,ai(c,f,l,m[1]),p):e(n,f,l,p)}var
  h=i(d,b),q=h[2],z=h[3],r=k(c,h[1],v),s=k(c,z,u);return q?o(r,d,ai(c,d,q[1],j),s):e(r,d,j,s)}var
  t=b}else
  var
  t=a;return t}function
  x(b,a){if(a){var
  g=a[4],h=a[3],i=a[2],j=a[1],c=x(b,j),k=d(b,i,h),f=x(b,g);if(k){if(j===c)if(g===f)return a;return e(c,i,h,f)}return n(c,f)}return 0}function
  y(b,a){if(a){var
  c=a[3],f=a[2],m=a[4],g=y(b,a[1]),h=g[2],i=g[1],o=d(b,f,c),j=y(b,m),k=j[2],l=j[1];if(o){var
  p=n(h,k);return[0,e(i,f,c,l),p]}var
  q=e(h,f,c,k);return[0,n(i,l),q]}return hM}function
  f(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[2],a[3],a[4],b],a=a[1],b=e;continue}return b}}function
  X(j,i,h){var
  r=f(h,0),b=f(i,0),a=r;for(;;){if(b){if(a){var
  k=a[4],l=a[3],m=a[2],n=b[4],o=b[3],p=b[2],c=d(g[1],b[1],a[1]);if(0===c){var
  e=d(j,p,m);if(0===e){var
  q=f(l,k),b=f(o,n),a=q;continue}return e}return c}return 1}return a?-1:0}}function
  Y(k,j,i){var
  s=f(i,0),b=f(j,0),a=s;for(;;){if(b){if(a){var
  l=a[4],m=a[3],n=a[2],o=b[4],p=b[3],q=b[2],c=0===d(g[1],b[1],a[1])?1:0;if(c){var
  e=d(k,q,n);if(e){var
  r=f(m,l),b=f(p,o),a=r;continue}var
  h=e}else
  var
  h=c;return h}return 0}return a?0:1}}function
  z(a){if(a){var
  b=a[1],c=z(a[4]);return(z(b)+1|0)+c|0}return 0}function
  L(d,c){var
  b=d,a=c;for(;;){if(a){var
  e=a[3],f=a[2],g=a[1],b=[0,[0,f,e],L(b,a[4])],a=g;continue}return b}}function
  Z(a){return L(0,a)}function
  M(b,a){return ez(function(b,a){return l(a[1],a[2],b)},a,b)}function
  _(a){return M(a,B)}function
  A(a,e){if(a){var
  b=a[2],c=a[1],d=f(a[3],a[4]);return[0,[0,c,b],function(a){return A(d,a)}]}return 0}function
  $(a){var
  b=f(a,0);return function(a){return A(b,a)}}return[0,B,N,U,l,u,r,t,j,k,X,Y,E,F,G,H,x,y,z,Z,m,C,V,W,m,C,i,O,T,P,Q,R,S,v,w,$,function(k,j){var
  a=j,b=0;for(;;){if(a){var
  c=a[4],f=a[3],e=a[2],l=a[1],h=d(g[1],e,k);if(0!==h){if(0<=h){var
  a=l,b=[0,e,f,c,b];continue}var
  a=c;continue}var
  i=[0,e,f,c,b]}else
  var
  i=b;return function(a){return A(i,a)}}},M,_]}var
  dk=[z,hN,S(0)];function
  eK(a){return[0,0,0]}function
  dl(b,a){a[1]=[0,b,a[1]];a[2]=a[2]+1|0;return 0}function
  bu(a){var
  b=a[1];if(b){var
  c=b[1];a[1]=b[2];a[2]=a[2]-1|0;return c}throw dk}function
  hO(b,a){return a4(b,a[1])}var
  hQ=[z,hP,S(0)];function
  hR(a){throw hQ}function
  bv(a){var
  d=a[1];a[1]=hR;try{var
  b=c(d,0);sW(a,b);return b}catch(b){b=B(b);a[1]=function(a){throw b};throw b}}S(0);var
  eL=[0,0];function
  eM(a){eL[1]=[0,a,eL[1]];return 0}try{var
  sg=hd(sf),eO=sg}catch(a){a=B(a);if(a!==q)throw a;try{var
  se=hd(sd),eN=se}catch(a){a=B(a);if(a!==q)throw a;var
  eN=hW}var
  eO=eN}var
  hX=eF(eO,82),cd=[N,function(C){var
  p=s6(0),c=[0,aP(55,0),0],l=0===p.length-1?[0,0]:p,h=l.length-1,b=0;for(;;){u(c[1],b)[1+b]=b;var
  B=b+1|0;if(54!==b){var
  b=B;continue}var
  j=[0,hV],v=0,w=55,x=sv(55,h)?w:h,m=54+x|0;if(!(m<0)){var
  d=v;for(;;){var
  e=d%55|0,n=g4(d,h),y=u(l,n)[1+n],k=i(j[1],a(g+y));j[1]=sO(k,0,o(k));var
  f=j[1],q=aB(f,3)<<24,r=aB(f,2)<<16,s=aB(f,1)<<8,t=((aB(f,0)+s|0)+r|0)+q|0,z=(u(c[1],e)[1+e]^t)&dW;u(c[1],e)[1+e]=z;var
  A=d+1|0;if(m!==d){var
  d=A;continue}break}}c[2]=0;return c}}];function
  dm(a,b){return 3<=a.length-1?sw(10,gu,a[3],b)&(a[2].length-1-1|0):g4(sB(10,gu,b),a[2].length-1)}function
  eP(f,b){var
  g=dm(f,b),c=u(f[2],g)[1+g];if(c){var
  d=c[3],j=c[2];if(0===bn(b,c[1]))return j;if(d){var
  e=d[3],k=d[2];if(0===bn(b,d[1]))return k;if(e){var
  l=e[2],m=e[3];if(0===bn(b,e[1]))return l;var
  a=m;for(;;){if(a){var
  h=a[2],i=a[3];if(0===bn(b,a[1]))return h;var
  a=i;continue}throw q}}throw q}throw q}throw q}function
  hY(b,a){var
  c=bq(a)===z?a:a[1];return s2(b,c)}var
  hZ=2;function
  h0(c){var
  a=[0,0],d=o(c)-1|0,e=0;if(!(d<0)){var
  b=e;for(;;){var
  g=aB(c,b);a[1]=(223*a[1]|0)+g|0;var
  h=b+1|0;if(d!==b){var
  b=h;continue}break}}a[1]=a[1]&2147483647;var
  f=dW<a[1]?a[1]+2147483648|0:a[1];return f}var
  dn=dj([0,b_]),ce=dj([0,b_]),cf=dj([0,gY]),eQ=g7(0,0),h1=[0,0];function
  eR(a){return 2<a?eR((a+1|0)/2|0)*2|0:a}function
  eS(d){h1[1]++;var
  b=d.length-1,c=aP((b*2|0)+2|0,eQ);u(c,0)[1]=b;var
  g=((eR(b)*32|0)/8|0)-1|0;u(c,1)[2]=g;var
  e=b-1|0,h=0;if(!(e<0)){var
  a=h;for(;;){var
  f=(a*2|0)+3|0,i=u(d,a)[1+a];u(c,f)[1+f]=i;var
  j=a+1|0;if(e!==a){var
  a=j;continue}break}}return[0,hZ,c,ce[1],cf[1],0,0,dn[1],0]}function
  dp(b,d){var
  a=b[2].length-1,e=a<d?1:0;if(e){var
  c=aP(d,eQ),f=b[2],h=0<=a?(f.length-1-a|0)<0?0:(c.length-1-a|0)<0?0:(sl(f,0,c,0,a),1):0;if(!h)K(hw);b[2]=c;var
  g=0}else
  var
  g=e;return g}var
  eT=[0,0],h2=[0,0];function
  h3(a){var
  b=a[2].length-1;dp(a,b+1|0);return b}function
  h4(a,e){try{var
  b=d(ce[27],e,a[3]);return b}catch(b){b=B(b);if(b===q){var
  c=h3(a);a[3]=ai(ce[4],e,c,a[3]);a[4]=ai(cf[4],c,1,a[4]);return c}throw b}}function
  h5(a){var
  b=a[1];a[1]=b+1|0;return b}function
  h6(a,c){try{var
  b=d(dn[27],c,a[7]);return b}catch(b){b=B(b);if(b===q){var
  e=h5(a);if(f(c,h7))a[7]=ai(dn[4],c,e,a[7]);return e}throw b}}function
  h8(b,a){var
  c=o(b);return c<a?h9:ae(b,a,c-a|0)}var
  h_=34;function
  ib(a){var
  d=0;for(;;){var
  b=ap(aq,d,a);if(7<b>>>0){c(a[1],a);var
  d=b;continue}switch(b){case
  0:return 0;case
  1:return 3;case
  2:return 6;case
  3:var
  e=x(a);return[0,ae(e,1,o(e)-1|0)];case
  4:var
  f=x(a);return[1,ae(f,2,o(f)-2|0)];case
  5:return ig;case
  6:return 13;default:return[2,x(a)]}}}function
  eU(a){var
  d=33;for(;;){var
  b=ap(aq,d,a);if(9<b>>>0){c(a[1],a);var
  d=b;continue}switch(b){case
  0:return 9;case
  1:return 10;case
  2:return[3,o(x(a))];case
  3:return[4,x(a)];case
  4:return 11;case
  5:return 12;case
  6:return 12;case
  7:return[5,x(a)];case
  8:return 13;default:return 12}}}function
  ic(a,e){var
  d=e;for(;;){var
  b=ap(aq,d,a);if(7<b>>>0){c(a[1],a);var
  d=b;continue}switch(b){case
  0:return 9;case
  1:return 10;case
  2:return[3,o(x(a))];case
  3:try{var
  f=[5,id(a)];return f}catch(a){return 12}case
  4:try{var
  g=[5,ie(a)];return g}catch(a){return 12}case
  5:return[5,x(a)];case
  6:return 13;default:return 12}}}function
  id(a){var
  b=53;for(;;){var
  d=ap(aq,b,a);if(0===d)return di(a,a[5],a[6]-1|0);c(a[1],a);var
  b=d;continue}}function
  ie(a){var
  b=55;for(;;){var
  d=ap(aq,b,a);if(0===d)return di(a,a[5],a[6]-1|0);c(a[1],a);var
  b=d;continue}}var
  ag=[z,ii,S(0)],eV=[z,ij,S(0)];function
  aD(b,a){return[0,d6,[0,b,a]]}var
  iN=[0,iM,[0,iL,[0,iK,[0,iJ,[0,iI,[0,iH,[0,iG,[0,iF,[0,iE,[0,iD,[0,iC,[0,iB,[0,iA,[0,iz,[0,iy,[0,ix,[0,iw,[0,iv,[0,iu,[0,it,[0,is,[0,ir,[0,iq,[0,ip,[0,io,[0,[0,im,[0,j,aD(h,il)]],ik]]]]]]]]]]]]]]]]]]]]]]]]]],iU=[0,iT,[0,iS,[0,iR,[0,iQ,[0,[0,iP,[0,j,aD(h,iO)]],iN]]]]],iZ=[0,iY,[0,iX,[0,[0,iW,[0,l,aD(h,iV)]],iU]]],i3=[0,[0,i2,[0,l,[0,C,[0,i1,aD(h,i0)]]]],iZ],jt=[0,js,[0,jr,[0,jq,[0,jp,[0,jo,[0,jn,[0,jm,[0,jl,[0,jk,[0,jj,[0,ji,[0,jh,[0,jg,[0,jf,[0,je,[0,jd,[0,jc,[0,[0,jb,[0,l,[0,C,[0,eW,ja]]]],[0,[0,i$,[0,l,[0,C,[0,eW,i_]]]],[0,i9,[0,i8,[0,i7,[0,i6,[0,[0,i5,[0,l,aD(h,i4)]],i3]]]]]]]]]]]]]]]]]]]]]]]],jx=[0,jw,[0,[0,jv,[0,b,aD(h,ju)]],jt]],jB=[0,jA,[0,[0,jz,[0,b,aD(h,jy)]],jx]],dq=[0,j2,[0,j1,[0,j0,[0,jZ,[0,jY,[0,jX,[0,jW,[0,jV,[0,jU,[0,jT,[0,jS,[0,jR,[0,jQ,[0,jP,[0,jO,[0,jN,[0,jM,[0,jL,[0,jK,[0,jJ,[0,jI,[0,jH,[0,jG,[0,jF,[0,jE,[0,[0,jD,[0,b,aD(h,jC)]],jB]]]]]]]]]]]]]]]]]]]]]]]]]];function
  j3(a){function
  c(a){if(typeof
  a==="number"){if(b===a)return h}else
  if(C===a[1]){var
  d=a[2],e=d[1];return[0,C,[0,e,c(d[2])]]}return a}return ad(function(d){var
  e=d[2],f=e[2],a=e[1],g=d[1];return b===a?[0,g,[0,a,c(f)]]:[0,g,[0,a,f]]},a)}function
  j4(a,f){return ad(function(b){var
  c=b[2],d=c[2],e=c[1],a=b[1];if(l===e)if(eA(a,f))return[0,a,[0,at,d]];return[0,a,[0,e,d]]},a)}j4(j3(dq),j5);function
  eX(a){var
  e=17;for(;;){var
  d=ap(aq,e,a);if(3<d>>>0){c(a[1],a);var
  e=d;continue}switch(d){case
  0:var
  b=1;break;case
  1:var
  b=2;break;case
  2:var
  b=13;break;default:var
  b=2}if(2===b){var
  f=x(a);return i(f,eX(a))}if(13<=b)throw ag;return j6}}function
  eY(a){var
  e=23;for(;;){var
  d=ap(aq,e,a);if(2<d>>>0){c(a[1],a);var
  e=d;continue}switch(d){case
  0:var
  b=4;break;case
  1:var
  b=13;break;default:var
  b=5}if(5===b){var
  f=x(a);return i(f,eY(a))}if(13<=b)throw ag;return j7}}function
  eZ(b){var
  e=27;for(;;){var
  d=ap(aq,e,b);if(4<d>>>0){c(b[1],b);var
  e=d;continue}switch(d){case
  0:var
  a=7;break;case
  1:var
  a=7;break;case
  2:var
  a=13;break;case
  3:var
  a=8;break;default:var
  a=8}if(8===a){var
  f=x(b);return i(f,eZ(b))}if(13<=a)throw ag;return j8}}function
  j9(k){var
  c=16,l=aS(k);for(;;){if(!(l<=c))if(!(dd<(c*2|0))){var
  c=c*2|0;continue}if(hX){var
  i=bq(cd),b=aH===i?cd[1]:N===i?bv(cd):cd;b[2]=(b[2]+1|0)%55|0;var
  d=b[2],e=u(b[1],d)[1+d],f=(b[2]+24|0)%55|0,g=(u(b[1],f)[1+f]+(e^(e>>>25|0)&31)|0)&dW,h=b[2];u(b[1],h)[1+h]=g;var
  j=g}else
  var
  j=0;var
  a=[0,0,aP(c,0),j,c];a4(function(x){var
  y=x[1],I=x[2],g=dm(a,y),H=[0,y,I,u(a[2],g)[1+g]];u(a[2],g)[1+g]=H;a[1]=a[1]+1|0;var
  w=a[2].length-1<<1<a[1]?1:0;if(w){var
  k=a[2],l=k.length-1,f=l*2|0,m=f<dd?1:0;if(m){var
  n=aP(f,0),h=aP(f,0),z=a.length-1<4?1:0,A=z||(a[4]<0?1:0),i=1-A;a[2]=n;var
  q=l-1|0,D=0;if(!(q<0)){var
  e=D;a:for(;;){var
  b=u(k,e)[1+e];for(;;){if(b){var
  o=b[1],B=b[2],C=b[3],j=i?b:[0,o,B,0],c=dm(a,o),p=u(h,c)[1+c];if(p)p[3]=j;else
  u(n,c)[1+c]=j;u(h,c)[1+c]=j;var
  b=C;continue}var
  G=e+1|0;if(q!==e){var
  e=G;continue a}break}break}}if(i){var
  r=f-1|0,E=0;if(!(r<0)){var
  d=E;for(;;){var
  v=u(h,d)[1+d];if(v)v[3]=0;var
  F=d+1|0;if(r!==d){var
  d=F;continue}break}}var
  s=0}else
  var
  s=i;var
  t=s}else
  var
  t=m;return t}return w},k);return a}}var
  dr=eJ([0,b_]);function
  j_(N,M,L,K,J,j){var
  X=N?N[1]:dq,Y=M?M[1]:0,Z=L?L[1]:0,_=K?K[1]:0,$=J?J[1]:0,g=[0,j$],k=[0,0],a=[0,0],s=[0,dr[1]],t=eK(0),O=j9(X),m=$?function(a){return a}:hv;function
  v(a){if(e(a,ka))return kb;function
  b(a){var
  b=a[2];if(typeof
  b!=="number")if(C===b[1])return[0,a[1],b[2][2]];return a}try{var
  c=b(eP(O,a));return c}catch(a){a=B(a);if(a===q)return kc;throw a}}function
  ac(k,j,f){var
  c=v(f)[1];function
  e(o){var
  a=o;for(;;){if(typeof
  a==="number"){if(dN<=a){if(bh<=a){if(h<=a){var
  g=b===c?1:0;if(g)var
  i=g;else
  var
  q=l===c?1:0,i=q||(at===c?1:0);return i}return 0}return r<=a?0:1}if(b<=a)return b===c?1:0;var
  s=l===c?1:0,t=s||(at===c?1:0);return t}var
  d=a[1];if(d6<=d){if(C<=d)throw[0,p,ke];var
  j=a[2],u=j[2],k=e(j[1]);if(k)return k;var
  a=u;continue}if(-260921543<=d){var
  m=a[2],v=m[2],n=e(m[1]),w=n?1-e(v):n;return w}return eA(f,a[2])}}var
  a=aV===c?1:0;if(a)var
  g=a;else{var
  i=1-d(dr[3],f,j);if(i)return e(v(k)[2]);var
  g=i}return g}function
  D(d){var
  c=eK(0),e=g[1],f=k[1],h=a[1],i=s[1];try{for(;;){if(ac(g[1],s[1],d)){var
  j=0;return j}if(at===v(g[1])[1])throw dk;var
  b=bu(t);dl(b,c);var
  l=b[4],m=b[3],n=b[2],o=b[1],p=A(a[1]),q=[0,[0,g[1],k[1],p]];g[1]=o;k[1]=n;s[1]=l;a[1]=[0,q,m];continue}}catch(b){b=B(b);if(b===dk)for(;;){if(0<c[2]){dl(bu(c),t);continue}g[1]=e;k[1]=f;a[1]=h;s[1]=i;return 0}throw b}}function
  E(a){function
  b(b){for(;;){var
  a=b?ic(j,44):eU(j);if(typeof
  a!=="number"&&3===a[0])continue;return a}}function
  e(j){var
  c=j;for(;;){if(typeof
  c==="number")switch(c){case
  9:return kf;case
  10:return kg;case
  13:throw ag}else
  if(4===c[0]){var
  a=c[1],f=b(0);if(typeof
  f==="number")switch(f){case
  9:var
  o=m(a);return[0,[0,[0,m(a),o],0],0];case
  10:var
  p=m(a);return[0,[0,[0,m(a),p],0],1];case
  11:var
  d=b(1);if(typeof
  d==="number")switch(d){case
  9:return kh;case
  10:return ki;case
  13:throw ag}else
  switch(d[0]){case
  4:var
  q=d[1],h=e(b(0)),r=h[2],s=h[1];return[0,[0,[0,m(a),q],s],r];case
  5:var
  t=d[1],i=e(b(0)),u=i[2],v=i[1];return[0,[0,[0,m(a),t],v],u]}var
  c=b(0);continue;case
  13:throw ag}var
  g=e(f),k=g[2],l=g[1],n=m(a);return[0,[0,[0,m(a),n],l],k]}var
  c=b(0);continue}}return e(b(0))}function
  F(d){a:for(;;){var
  f=11;for(;;){var
  b=ap(aq,f,j);if(3<b>>>0){c(j[1],j);var
  f=b;continue}switch(b){case
  0:var
  g=x(j),a=[1,ae(g,2,o(g)-2|0)];break;case
  1:var
  a=ih;break;case
  2:var
  a=13;break;default:var
  a=[2,x(j)]}if(typeof
  a==="number"){if(13===a)throw ag}else
  switch(a[0]){case
  1:var
  h=a[1];return e(m(h),d)?kj:i(kk,i(h,F(d)));case
  2:var
  k=a[1];return i(k,F(d))}continue a}}}function
  P(b){for(;;){var
  a=eU(j);if(typeof
  a==="number")if(11<=a){if(13<=a)throw ag}else
  if(9<=a)return 0;continue}}try{a:for(;;){var
  u=ib(j);if(typeof
  u==="number")switch(u){case
  0:var
  ad=eX(j);if(_)a[1]=[0,[0,[0,km,[0,[0,kl,ad],0],0]],a[1]];continue;case
  3:var
  af=eY(j);if(Y)a[1]=[0,[0,[0,ko,[0,[0,kn,af],0],0]],a[1]];continue;case
  6:var
  ah=eZ(j);if(Z)a[1]=[0,[0,[0,kq,[0,[0,kp,ah],0],0]],a[1]];continue;case
  13:throw ag}else
  switch(u[0]){case
  0:var
  n=m(u[1]),Q=v(n)[2];if(r===Q){var
  ai=E(0)[1];D(n);a[1]=[0,[0,[0,n,ai,0]],a[1]];continue}if(bh===Q){var
  R=E(0),aj=R[2],ak=R[1];D(n);if(aj)var
  S=kr;else{var
  al=F(n);P(0);var
  S=al}a[1]=[0,[0,[0,n,ak,[0,[1,S],0]]],a[1]];continue}var
  T=E(0),U=T[1],am=T[2];D(n);if(am)a[1]=[0,[0,[0,n,U,0]],a[1]];else{if(e(n,kd))var
  G=0;else{var
  aa=function(b){var
  a=b[2];if(typeof
  a!=="number")if(C===a[1])return a[2][1];return 0};try{var
  ab=aa(eP(O,n)),G=ab}catch(a){a=B(a);if(a!==q)throw a;var
  G=0,aG=a}}dl([0,g[1],k[1],a[1],s[1]],t);g[1]=n;k[1]=U;a[1]=0;a4(function(a){s[1]=d(dr[4],a,s[1]);return 0},G)}continue;case
  1:var
  H=m(u[1]);P(0);var
  V=e(H,g[1]);if(V)var
  I=V;else
  try{hO(function(c){return function(b){var
  a=b[1];if(e(c,a))throw eV;if(at===v(a)[1])throw q;return 0}}(H),t);var
  aA=0,I=aA}catch(a){a=B(a);if(a===eV)var
  W=1;else{if(a!==q)throw a;var
  W=0}var
  I=W,aH=a}if(I)for(;;){if(f(g[1],H)){var
  w=bu(t),an=w[4],ao=w[3],ar=w[2],as=w[1],au=A(a[1]);a[1]=[0,[0,[0,g[1],k[1],au]],ao];g[1]=as;k[1]=ar;s[1]=an;continue}var
  y=bu(t),av=y[4],aw=y[3],ax=y[2],ay=y[1],az=A(a[1]);a[1]=[0,[0,[0,g[1],k[1],az]],aw];g[1]=ay;k[1]=ax;s[1]=av;continue a}continue;case
  2:a[1]=[0,[1,u[1]],a[1]];continue}continue}}catch(b){b=B(b);if(b===ag)for(;;){if(0<t[2]){var
  z=bu(t),aB=z[4],aC=z[3],aD=z[2],aE=z[1],aF=A(a[1]);a[1]=[0,[0,[0,g[1],k[1],aF]],aC];g[1]=aE;k[1]=aD;s[1]=aB;continue}return A(a[1])}throw b}}function
  ks(l,k,a,b){function
  i(g){if(0===g[0]){var
  h=g[1],d=h[2],b=h[1],m=h[3];if(f(b,ku)){if(f(b,kv)){if(f(b,kw)){try{var
  o=r===a6(b,l)[2]?1:0,j=o}catch(a){a=B(a);if(a!==q)throw a;var
  j=0}c(a,kx);c(a,b);a4(function(b){var
  f=b[2],g=b[1];c(a,ky);c(a,g);c(a,kz);function
  d(b,a){try{var
  c=hr(a,h_),f=ae(a,0,c),g=h8(a,c+1|0),h=e(f,ia)?d(b,g):d([0,f,b],g);return h}catch(c){c=B(c);if(c===q)return e(a,h$)?b:[0,a,b];throw c}}c(a,af(kt,A(d(0,f))));return c(a,kA)},d);if(j){var
  n=k?kB:kC;return c(a,n)}c(a,kD);a4(i,m);c(a,kE);c(a,b);return c(a,kF)}c(a,kG);c(a,a6(kH,d));return c(a,kI)}c(a,kJ);c(a,a6(kK,d));return c(a,kL)}c(a,kM);c(a,a6(kN,d));return c(a,kO)}return c(a,g[1])}try{var
  d=a4(i,b);return d}catch(a){a=B(a);if(a===q)return ex(kP);throw a}}function
  kQ(b){return a(g+b)}function
  I(b,a){return a?a[1]:b}function
  cg(a,b){if(b)return b;var
  c=bq(a);return aH===c?a[1]:N===c?bv(a):a}function
  ds(b,a){return a?[0,c(b,a[1])]:0}function
  ch(b,a){return a?c(b,a[1])?a:0:0}function
  e0(a,b){if(b)return b[1];var
  c=bq(a);return aH===c?a[1]:N===c?bv(a):a}function
  aE(a,b){return a?c(b,a[1]):0}function
  T(b,a){return ds(a,b)}function
  e1(a){return a?[0,a[1],0]:0}function
  e2(b,a){if(a){var
  d=a[2],e=a[1],f=c(b,e),g=e2(b,d),h=e===f?1:0,i=h?d===g?1:0:h;return i?a:[0,f,g]}return a}function
  U(d,a){if(a){var
  e=a[2],f=a[1],g=c(d,f),b=U(d,e);return g?e===b?a:[0,f,b]:b}return a}function
  ah(e,d){var
  a=d;for(;;){if(a){var
  f=a[2],b=c(e,a[1]);if(b)return b;var
  a=f;continue}return 0}}function
  dt(d,a){var
  b=0;return a5(function(e,a){var
  b=c(d,e);return b?[0,b[1],a]:a},a,b)}function
  du(e,d){var
  a=d;for(;;){if(a){var
  b=a[1];if(c(e,b))return[0,b];var
  a=a[2];continue}return 0}}function
  dv(d,c){var
  b=d,a=c;for(;;){if(0===b)return a;if(a){var
  b=b-1|0,a=a[2];continue}return 0}}function
  dw(f){function
  a(a,b){var
  d=b[2],e=b[1];return c(f,a)?[0,[0,a,e],d]:[0,e,[0,a,d]]}return function(b){return a5(a,b,kR)}}function
  ci(b,a){var
  d=0;return a5(function(d,a){return w(c(b,d),a)},a,d)}function
  dx(b,a){if(a){var
  d=a[1],f=a[2];if(c(b,d)){var
  e=dx(b,f);return[0,[0,d,e[1]],e[2]]}}return[0,0,a]}function
  e3(b,a){return dx(function(a){return 1-c(b,a)},a)}function
  e4(b){if(e(b,hq))var
  f=b;else{if(eD(aC(b,0)))var
  g=0;else
  if(eD(aC(b,o(b)-1|0)))var
  g=0;else
  var
  f=b,g=1;if(!g){var
  d=b6(b),h=aQ(d),a=[0,0];for(;;){if(a[1]<h)if(eB(c5(d,a[1]))){a[1]++;continue}var
  c=[0,h-1|0];for(;;){if(a[1]<=c[1])if(eB(c5(d,c[1]))){c[1]+=-1;continue}var
  i=a[1]<=c[1]?de(d,a[1],(c[1]-a[1]|0)+1|0):hl,f=aR(i);break}break}}}return e(f,kS)}function
  ar(b,a){var
  c=o(a);return o(b)<c?0:e(ae(b,0,c),a)}function
  dy(c,b){var
  d=o(c),a=o(b);return d<a?0:e(ae(c,d-a|0,a),b)}var
  s=eJ([0,hu]);function
  e5(b){var
  a=ae(b,5,o(b)-5|0);try{var
  c=gZ(a);return c}catch(a){a=B(a);if(a[1]===cb)throw[0,da,i(kY,i(b,kX))];throw a}}function
  e6(a){return a?[0,af(kZ,a)]:0}var
  k5=w(k4,k3),k6=c(s[36],k5);function
  cj(a){return d(s[3],a,k6)}function
  dz(b,a){return bo(a[1],b)}function
  a7(a){function
  b(b){return dz(a,b)}return function(a){return du(b,a)}}function
  dA(c,b,a){var
  d=e2(function(a){var
  d=a[1];return bo(c,d)?[0,d,b]:a},a);return d===a?[0,[0,c,b],a]:d}function
  k9(b,a){return aE(b,function(b){return 0===a[0]?[0,i(b,a[1])]:0})}function
  e7(a){return aT(k9,k8,a)}function
  e8(b){function
  c(a){if(1===a[0])if(e(a[1],b))return[0,a];return 0}return function(a){if(0===a[0])throw[0,p,k_];return dt(c,a[4])}}function
  e9(a){return 0===a[0]?0:a[4]}function
  dB(c,b,a){if(0===a[0])return 0;var
  f=a[3],g=a[2];if(e(a[1],c))return d(b,g,f);var
  h=a[4];return ah(function(a){return dB(c,b,a)},h)}function
  ck(d,c,g,f,b){var
  a=bo(c,d),e=a?0===b?1:0:a;return e}function
  k$(k,j,i,h){var
  b=c(dw(function(a){return e(a[1],la)}),j),a=b[1],l=b[2];if(a){var
  d=a[1];if(!f(d[1],lb))if(!a[2]){var
  g=d[2];return ar(g,lc)?[0,[1,k,w(l,[0,[0,ld,g],0]),i,h]]:0}}return 0}var
  lf=[0,function(c,b,g,f){var
  a=e(c,le),d=a?0!==b?1:0:a;return d},k$];function
  e_(a){return e(a[1],lg)}function
  lh(g,b,a,d){var
  c=du(e_,b);if(c){var
  h=c[1][2],i=U(function(a){return f(a[1],li)},b),j=ao(function(a){return e(a[1][1],lj)},a)?a:[0,[0,lk,[0,[0,h,0]]],a];return[0,[1,g,i,j,d]]}return 0}var
  lm=[0,function(c,b,f,d){var
  a=e(c,ll);return a?ao(e_,b):a},lh];function
  cl(a){return eF(a,32)?i(lo,i(a,ln)):a}var
  ly=c(s[36],lx);function
  lz(l){var
  b=l[2][1],c=l[1][1];if(d(s[3],c,ly)){var
  a=aT(function(b,a){if(b){var
  c=b[2],d=b[1];if(e(a,lq))return[0,lr,[0,cl(d),c]];if(ar(a,ls)){var
  f=i(lt,a);return[0,lu,[0,i(cl(d),f),c]]}return[0,i(d,a),c]}return 0},lp,b);if(a){var
  h=a[1];if(f(h,lv))var
  m=a[2],j=[0,cl(h),m];else
  var
  j=a[2];var
  k=j}else
  var
  k=a;var
  g=af(lw,A(k))}else
  var
  p=dy(c,lD)?b:ad(cl,b),g=af(lE,p);var
  n=0===o(g)?lA:g;return i(c,i(lC,i(n,lB)))}function
  e$(a,b){var
  c=a?a[1]:lF;return af(c,ad(lz,b))}function
  dC(a){return i(lH,e$(lG,a))}function
  lJ(b){switch(b[0]){case
  0:var
  c=b[1],d=i(lL,i(dC(b[2]),lK));return i(af(lI,c[1]),d);case
  1:var
  e=b[1];return i(lO,i(e,i(lN,i(dC(b[2]),lM))));default:var
  f=b[3],h=b[2],j=b[1][1],k=i(lQ,i(dC(b[4]),lP)),l=i(I(lS,ds(function(a){return i(lR,a)},f)),k);return i(lV,i(j,i(I(lU,ds(function(b){return i(lT,a(g+b[1]))},h)),l)))}}function
  bw(a){return af(lX,a[1])}var
  cm=[z,lY,S(0)];function
  L(j,d,c){var
  a=d,b=c;for(;;)switch(b){case
  0:throw cm;case
  1:var
  e=a[2],a=a[1],b=e;continue;case
  2:var
  f=a[2],a=a[1],b=f;continue;case
  3:throw cm;case
  4:var
  g=a[2],a=a[1],b=g;continue;case
  5:var
  h=a[2],a=a[1],b=h;continue;case
  6:var
  i=a[2],a=a[1],b=i;continue;default:throw cm}}function
  fa(a,e,d,c){if(a[4])throw[0,p,mu];var
  b=a[3];if(typeof
  b==="number")if(3===b)return I(0,c);if(a[4])throw[0,p,mv];a[4]=1;return L(a,e,d)}function
  bx(a){dc(mk);throw[0,p,ml]}function
  fb(b,i,h){var
  a=i,c=h,d=0;for(;;){if(2===c){var
  f=a[2],g=[0,a[3],d],a=a[1],c=f,d=g;continue}if(3===c){if(b[4])throw[0,p,mq];var
  e=b[3];if(typeof
  e==="number")if(3===e)return d;if(b[4])throw[0,p,mr];b[4]=1;return L(b,a,c)}return bx(0)}}function
  cC(i,a,f,l){var
  b=f[2],c=f[1],k=f[3],j=[0,s[1],0],e=aT(function(a,b){var
  c=b[1][1],e=a[1],f=a[2];return d(s[3],c,e)?a:[0,d(s[4],c,e),[0,b,f]]},j,k)[2];if(b)return 7<=b?fa(a,c,b,[0,e]):bx(0);if(a[4])throw[0,p,mb];var
  g=a[3];if(typeof
  g==="number")if(1===g){var
  h=y(a);return i<50?cA(i+1|0,h,c,e):m(cA,[0,h,c,e])}if(a[4])throw[0,p,mc];a[4]=1;return L(a,c,b)}function
  cB(d,b,n,l,k){var
  a=[0,n,l,k];if(b[4])throw[0,p,md];var
  f=b[3];if(typeof
  f==="number")switch(f){case
  0:var
  c=y(b),e=c[3];if(typeof
  e==="number")switch(e){case
  1:case
  3:var
  g=[0,0];return d<50?cC(d+1|0,c,a,g):m(cC,[0,c,a,g])}else
  if(4===e[0]){var
  h=e[1],i=6;return d<50?bC(d+1|0,c,a,i,h):m(bC,[0,c,a,i,h])}if(c[4])throw[0,p,me];c[4]=1;return L(c,a,6);case
  1:case
  3:var
  j=0;return d<50?cC(d+1|0,b,a,j):m(cC,[0,b,a,j])}if(b[4])throw[0,p,mf];b[4]=1;return L(b,a[1],a[2])}function
  cA(c,a,r,h){var
  j=r[3],B=r[2],C=r[1];if(j)if(f(j[1],l5))var
  e=0;else{var
  g=j[2];if(g)if(f(g[1],l2))var
  d=1;else{var
  n=g[2];if(n){var
  o=n[2],s=n[1];if(o)if(f(o[1],l4))var
  d=1;else{var
  q=o[2];if(q)var
  y=q[1],z=e6(q[2]),k=[2,[0,s],[0,[0,e5(y)]],z,h],e=1,d=0;else
  var
  d=1}else
  var
  k=[2,[0,s],0,0,h],e=1,d=0}else
  var
  d=1}else
  var
  d=1;if(d)var
  k=[1,af(l3,g),h],e=1}else
  var
  e=0;if(!e)var
  k=[0,[0,A(aT(function(a,b){if(a){var
  c=a[2],d=a[1];return f(b,l0)?[0,i(d,b),c]:[0,l1,[0,d,c]]}return 0},lZ,j))],h];var
  b=[0,C,B,k];if(a[4])throw[0,p,mg];var
  l=a[3];if(typeof
  l==="number")switch(l){case
  3:return fb(a,b,2);case
  4:var
  t=2;return c<50?bA(c+1|0,a,b,t):m(bA,[0,a,b,t]);case
  5:var
  u=2;return c<50?bz(c+1|0,a,b,u):m(bz,[0,a,b,u]);case
  6:var
  v=2;return c<50?by(c+1|0,a,b,v):m(by,[0,a,b,v])}else
  if(4===l[0]){var
  w=l[1],x=2;return c<50?bB(c+1|0,a,b,x,w):m(bB,[0,a,b,x,w])}if(a[4])throw[0,p,mh];a[4]=1;return L(a,b,2)}function
  D(c,a,I,H,G){var
  b=[0,I,H,G];if(a[4])throw[0,p,mi];var
  d=a[3];if(typeof
  d==="number")switch(d){case
  4:var
  q=4;return c<50?ct(c+1|0,a,b,q):m(ct,[0,a,b,q]);case
  5:var
  r=4;return c<50?cs(c+1|0,a,b,r):m(cs,[0,a,b,r]);case
  2:case
  6:if(a[4])throw[0,p,mj];a[4]=1;return L(a,b,4);default:var
  e=b[1],h=b[2],g=[0,b[3],0];for(;;){if(4===h){var
  E=e[2],F=[0,e[3],g],e=e[1],h=E,g=F;continue}if(5===h){var
  i=e[2],f=e[1],j=[0,e[3],[0,g]];switch(i){case
  6:var
  l=f[2],n=f[1],o=[0,j,f[3]];return c<50?cB(c+1|0,a,n,l,o):m(cB,[0,a,n,l,o]);case
  0:case
  7:var
  k=[0,j,0];return c<50?cB(c+1|0,a,f,i,k):m(cB,[0,a,f,i,k]);default:return bx(0)}}return bx(0)}}else
  switch(d[0]){case
  0:var
  s=d[1],t=4;return c<50?cz(c+1|0,a,b,t,s):m(cz,[0,a,b,t,s]);case
  1:var
  u=d[1],v=4;return c<50?cy(c+1|0,a,b,v,u):m(cy,[0,a,b,v,u]);case
  2:var
  w=d[1],x=4;return c<50?cx(c+1|0,a,b,x,w):m(cx,[0,a,b,x,w]);case
  3:var
  y=d[1],z=4;return c<50?cw(c+1|0,a,b,z,y):m(cw,[0,a,b,z,y]);case
  4:var
  A=d[1],B=4;return c<50?cv(c+1|0,a,b,B,A):m(cv,[0,a,b,B,A]);default:var
  C=d[1],D=4;return c<50?cu(c+1|0,a,b,D,C):m(cu,[0,a,b,D,C])}}function
  cz(e,f,c,b,a){var
  d=y(f);return e<50?D(e+1|0,d,c,b,a):m(D,[0,d,c,b,a])}function
  cy(e,f,c,b,a){var
  d=y(f);return e<50?D(e+1|0,d,c,b,a):m(D,[0,d,c,b,a])}function
  cx(e,j,c,b,f){var
  a=y(j),d=a[3];if(typeof
  d==="number")switch(d){case
  2:case
  6:if(a[4])throw[0,p,mp];a[4]=1;return L(a,c,b)}else
  if(4===d[0]){var
  k=d[1],g=y(a),h=i(f,k);return e<50?D(e+1|0,g,c,b,h):m(D,[0,g,c,b,h])}return e<50?D(e+1|0,a,c,b,f):m(D,[0,a,c,b,f])}function
  cw(e,f,c,b,a){var
  d=y(f);return e<50?D(e+1|0,d,c,b,a):m(D,[0,d,c,b,a])}function
  cv(e,f,c,b,a){var
  d=y(f);return e<50?D(e+1|0,d,c,b,a):m(D,[0,d,c,b,a])}function
  cu(e,f,c,b,a){var
  d=y(f);return e<50?D(e+1|0,d,c,b,a):m(D,[0,d,c,b,a])}function
  ct(d,e,b,a){var
  c=y(e);return d<50?D(d+1|0,c,b,a,fc):m(D,[0,c,b,a,fc])}function
  cs(d,e,b,a){var
  c=y(e);return d<50?D(d+1|0,c,b,a,fd):m(D,[0,c,b,a,fd])}function
  aG(c,a,A,z,x){var
  b=[0,A,z,x];if(a[4])throw[0,p,ms];var
  g=a[3];if(typeof
  g==="number")switch(g){case
  2:var
  k=b[1],h=b[2],j=[0,b[3],0];for(;;){var
  d=[0,k,h,j];switch(h){case
  1:var
  i=d[1],k=i[1],h=i[2],j=[0,i[3],d[3]];continue;case
  2:case
  3:if(a[4])throw[0,p,mm];var
  l=a[3];if(typeof
  l==="number")if(2===l){var
  e=y(a),f=e[3];if(typeof
  f==="number"){if(1===f){var
  n=y(e),o=0;return c<50?cA(c+1|0,n,d,o):m(cA,[0,n,d,o])}}else
  if(4===f[0]){var
  q=f[1],r=0;return c<50?bC(c+1|0,e,d,r,q):m(bC,[0,e,d,r,q])}if(e[4])throw[0,p,mn];e[4]=1;return L(e,d,0)}if(a[4])throw[0,p,mo];a[4]=1;return L(a,d[1],d[2]);default:return bx(0)}}case
  4:var
  s=1;return c<50?bA(c+1|0,a,b,s):m(bA,[0,a,b,s]);case
  5:var
  t=1;return c<50?bz(c+1|0,a,b,t):m(bz,[0,a,b,t]);case
  6:var
  u=1;return c<50?by(c+1|0,a,b,u):m(by,[0,a,b,u])}else
  if(4===g[0]){var
  v=g[1],w=1;return c<50?bB(c+1|0,a,b,w,v):m(bB,[0,a,b,w,v])}if(a[4])throw[0,p,mt];a[4]=1;return L(a,b,1)}function
  bC(c,A,z,x,w){var
  e=y(A),b=[0,z,x,[0,w]];if(e[4])throw[0,p,mw];var
  f=e[3];if(typeof
  f==="number")if(5===f){var
  a=y(e),d=a[3];if(typeof
  d==="number")switch(d){case
  4:var
  g=5;return c<50?ct(c+1|0,a,b,g):m(ct,[0,a,b,g]);case
  5:var
  h=5;return c<50?cs(c+1|0,a,b,h):m(cs,[0,a,b,h]);default:if(a[4])throw[0,p,mx];a[4]=1;return L(a,b,5)}else
  switch(d[0]){case
  0:var
  i=d[1],j=5;return c<50?cz(c+1|0,a,b,j,i):m(cz,[0,a,b,j,i]);case
  1:var
  k=d[1],l=5;return c<50?cy(c+1|0,a,b,l,k):m(cy,[0,a,b,l,k]);case
  2:var
  n=d[1],o=5;return c<50?cx(c+1|0,a,b,o,n):m(cx,[0,a,b,o,n]);case
  3:var
  q=d[1],r=5;return c<50?cw(c+1|0,a,b,r,q):m(cw,[0,a,b,r,q]);case
  4:var
  s=d[1],t=5;return c<50?cv(c+1|0,a,b,t,s):m(cv,[0,a,b,t,s]);default:var
  u=d[1],v=5;return c<50?cu(c+1|0,a,b,v,u):m(cu,[0,a,b,v,u])}}if(e[4])throw[0,p,my];e[4]=1;return L(e,b[1],b[2])}function
  bB(e,f,c,b,a){var
  d=y(f);return e<50?aG(e+1|0,d,c,b,a):m(aG,[0,d,c,b,a])}function
  bA(d,e,b,a){var
  c=y(e);return d<50?aG(d+1|0,c,b,a,fe):m(aG,[0,c,b,a,fe])}function
  bz(d,e,b,a){var
  c=y(e);return d<50?aG(d+1|0,c,b,a,ff):m(aG,[0,c,b,a,ff])}function
  by(d,e,b,a){var
  c=y(e);return d<50?aG(d+1|0,c,b,a,fg):m(aG,[0,c,b,a,fg])}function
  l6(a,b,c,d){return b$(bC(0,a,b,c,d))}function
  l7(a,b,c,d){return b$(bB(0,a,b,c,d))}function
  l8(a,b,c){return b$(bA(0,a,b,c))}function
  l9(a,b,c){return b$(bz(0,a,b,c))}function
  l_(a,b,c){return b$(by(0,a,b,c))}function
  y(a){var
  b=a[1],d=a[2];return[0,b,d,c(b,d),0]}function
  l$(f,e){var
  d=[0,f,e,0,0],c=[0,0,d[2][12]],a=y(d),b=a[3];if(typeof
  b==="number"){if(3===b)return fa(a,c,7,0)}else
  if(4===b[0])return l6(a,c,7,b[1]);if(a[4])throw[0,p,mz];a[4]=1;return L(a,c,7)}function
  ma(f,e){var
  d=[0,f,e,0,0],b=[0,0,d[2][12]],a=y(d),c=a[3];if(typeof
  c==="number")switch(c){case
  3:return fb(a,b,3);case
  4:return l8(a,b,3);case
  5:return l9(a,b,3);case
  6:return l_(a,b,3)}else
  if(4===c[0])return l7(a,b,3,c[1]);if(a[4])throw[0,p,mA];a[4]=1;return L(a,b,3)}var
  fh=[z,mB,S(0)];function
  mD(b){a:for(;;){b[10]=aP(5,-1);var
  k=0;for(;;){var
  d=sU(mC,k,b),j=0<=d?1:0,m=j?b[12]!==cc?1:0:j;if(m){b[11]=b[12];var
  e=b[12];b[12]=[0,e[1],e[2],e[3],b[4]+b[6]|0]}if(20<d>>>0){c(b[1],b);var
  k=d;continue}switch(d){case
  0:return 3;case
  1:var
  f=b[12];b[12]=[0,f[1],f[2]+1|0,b[6],f[4]];continue a;case
  2:continue a;case
  3:return 4;case
  4:return 2;case
  5:return 1;case
  6:return 0;case
  7:return 5;case
  8:return 6;case
  9:return[3,x(b)];case
  10:return[5,x(b)];case
  11:return[4,x(b)];case
  12:return[0,x(b)];case
  13:return[2,x(b)];case
  14:return[1,x(b)];case
  15:var
  n=u(b[10],0)[1];return[1,di(b,u(b[10],1)[2],n)];case
  16:return[4,x(b)];case
  17:continue a;case
  18:continue a;case
  19:continue a;default:var
  h=x(b),l=aB(h,0);if(aw<l)return[1,h];throw[0,fh,i(mG,i(h,i(mF,i(a(g+l),mE))))]}}}}function
  fi(c){var
  d=c[11],b=c[12],e=i(mH,a(g+((b[4]-b[3]|0)+1|0))),f=i(mI,i(a(g+((d[4]-d[3]|0)+1|0)),e));return i(a(g+b[2]),f)}function
  fj(c,a){var
  b=eI(0,c);try{var
  g=d(a,mD,b);return g}catch(a){a=B(a);if(a[1]===fh){var
  e=i(mJ,a[2]);dc(i(fi(b),e));return 0}if(a===cm){var
  f=i(mK,c);dc(i(fi(b),f));return 0}throw a}}function
  fk(a){return fj(a,ma)}function
  dD(b){if(0===b[0])return e4(b[1]);var
  d=b[4],e=f(b[1],mM);if(e){var
  g=0!==d?1:0;if(g){var
  a=d;for(;;){if(a){var
  i=a[2],c=dD(a[1]);if(c){var
  a=i;continue}return c}return 1}}var
  h=g}else
  var
  h=e;return h}function
  fl(a){var
  b=0;return A(aT(function(f,a){if(0===a[0]){var
  b=a[1],j=b[3],k=b[2],l=b[1],h=function(a){return fj(a[2],l$)},d=c(dw(function(a){return e(a[1],mL)}),k),i=d[2],g=aT(w,0,ad(h,d[1]));return[0,[1,l,i,g,fl(j)],f]}return[0,[0,a[1]],f]},b,a))}function
  fm(g,k){var
  b=g?g[1]:0;function
  f(a){return i(mO,dg(b*2|0,32))}var
  l=0,a=aT(function(g,a){if(0===a[0])return[0,[1,a[1]],g];var
  j=a[3],k=a[2],h=a[1],p=a[4],q=0===j?k:[0,[0,mN,e$(0,j)],k],l=fm([0,b+1|0],p),c=e(h,mP)?w([0,mQ,l],[0,[1,f(0)],0]):l;if(c){var
  m=c[1];if(0===m[0])if(cj(m[1][1]))var
  n=[0,[1,i(mR,dg((b+1|0)*2|0,32))],c],d=1;else
  var
  d=0;else
  var
  d=0}else
  var
  d=0;if(!d)var
  n=c;var
  o=[0,[0,h,q,n]];return cj(h)?[0,[1,f(0)],[0,o,g]]:[0,o,g]},l,k);if(a){var
  h=a[1];if(0===h[0])var
  c=0;else{var
  m=a[2],n=h[1];if(0<b)if(e(n,f(0)))var
  j=[0,[1,i(mS,dg((b-1|0)*2|0,32))],m],c=1,d=0;else
  var
  d=1;else
  var
  d=1;if(d)var
  c=0}}else
  var
  c=0;if(!c)var
  j=a;return A(j)}function
  fn(u){var
  d=0,a=u;for(;;){if(a){var
  f=a[1];if(0===f[0]){var
  g=a[2];if(g){var
  h=g[1],k=f[1];if(0===h[0]){var
  v=g[2],a=[0,[0,i(k,h[1])],v];continue}var
  o=g[2],n=k,m=h,l=h[1],e=0}else
  var
  e=1}else{var
  j=a[2];if(j){var
  s=j[1],z=f[1];if(0===s[0])var
  o=j[2],n=s[1],m=f,l=z,e=0;else
  var
  e=1}else
  var
  e=1}if(!e)if(cj(l))if(e4(n)){var
  a=[0,m,o];continue}var
  b=a[1];if(0===b[0]){var
  d=[0,[0,b[1]],d],a=a[2];continue}var
  p=b[4],q=b[1],w=a[2],x=b[3],y=b[2];if(cj(q)){var
  c=p;for(;;){if(c){var
  t=c[2];if(dD(c[1])){var
  c=t;continue}}var
  r=a5(function(b,a){if(dD(b))if(0===a)return a;return[0,b,a]},c,0);break}}else
  var
  r=p;var
  d=[0,[1,q,y,x,fn(r)],d],a=w;continue}return A(d)}}S(0);S(0);function
  aF(b,a){return[0,d6,[0,b,a]]}var
  nj=[0,ni,[0,nh,[0,ng,[0,nf,[0,ne,[0,nd,[0,nc,[0,nb,[0,na,[0,m$,[0,m_,[0,m9,[0,m8,[0,m7,[0,m6,[0,m5,[0,m4,[0,m3,[0,m2,[0,m1,[0,m0,[0,mZ,[0,mY,[0,mX,[0,mW,[0,[0,mV,[0,j,aF(h,mU)]],mT]]]]]]]]]]]]]]]]]]]]]]]]]],nq=[0,np,[0,no,[0,nn,[0,nm,[0,[0,nl,[0,j,aF(h,nk)]],nj]]]]],nv=[0,nu,[0,nt,[0,[0,ns,[0,l,aF(h,nr)]],nq]]],nz=[0,[0,ny,[0,l,[0,C,[0,nx,aF(h,nw)]]]],nv],n1=[0,n0,[0,nZ,[0,nY,[0,nX,[0,nW,[0,nV,[0,nU,[0,nT,[0,nS,[0,nR,[0,nQ,[0,nP,[0,nO,[0,nN,[0,nM,[0,nL,[0,nK,[0,[0,nJ,[0,l,[0,C,[0,fo,nI]]]],[0,[0,nH,[0,l,[0,C,[0,fo,nG]]]],[0,nF,[0,nE,[0,nD,[0,nC,[0,[0,nB,[0,l,aF(h,nA)]],nz]]]]]]]]]]]]]]]]]]]]]]]],n5=[0,n4,[0,[0,n3,[0,b,aF(h,n2)]],n1]],n9=[0,n8,[0,[0,n7,[0,b,aF(h,n6)]],n5]],oz=[0,oy,[0,ox,[0,ow,[0,ov,[0,ou,[0,ot,[0,os,[0,or,[0,oq,[0,op,[0,oo,[0,on,[0,om,[0,ol,[0,ok,[0,oj,[0,oi,[0,oh,[0,og,[0,of,[0,oe,[0,od,[0,oc,[0,ob,[0,oa,[0,[0,n$,[0,b,aF(h,n_)]],n9]]]]]]]]]]]]]]]]]]]]]]]]]];function
  fp(a){return fn(fl(j_([0,oz],oC,oB,oA,0,eI(0,a))))}function
  fq(a){return[0,[0,oY,a],oX]}var
  dE=fq(oZ);function
  fr(n,m,a){var
  h=U(function(b){var
  a=b[1],c=e(a,fs),d=c||e(a,ft);return d},n);function
  p(c){var
  b=c[2],a=o(b),d=7;return a<7?kT:ae(b,d,a-7|0)}var
  q=c(a7(fs),h),r=I(dE,T(T(cg([N,function(a){return c(a7(ft),h)}],q),p),fq)),d=c(a7(oV),a),g=c(a7(oW),a);if(g)if(d)var
  j=d[1][2],k=g[1][2],l=function(b){if(f(b,oQ))if(f(b,oR))return a;return dA(oT,j,dA(oS,k,a))},i=T(ah(function(a){return f(a[1][1],oU)?0:[0,bw(a[2])]},m),l),b=1;else
  var
  b=0;else
  var
  b=0;if(!b)var
  i=0;return w(r,I(a,i))}function
  fu(b,a){var
  c=[1,o0,0,0,a];return dB(b,function(b,a){return[0,[0,b,a]]},c)}function
  o1(f,d,a,c){function
  e(b){var
  c=b[2],e=fr(d,a,b[1]);return[0,e,w(c,a)]}var
  b=I([0,dE,0],T(fu(o2,c),e));return[0,[1,o3,b[1],b[2],0]]}var
  o5=[0,function(a,d,c,b){return e(a,o4)},o1];function
  o6(a,b){return[0,a]}function
  fv(a){return dB(o7,o6,a)}function
  fw(d,a){if(0===a[0])return 0;var
  b=a[1];if(f(b,o8)){var
  e=a[4],g=a[3],h=a[2],c=function(b){if(b){var
  a=b[1];if(0===a[0])return[0,a,c(b[2])];var
  e=a[1];if(f(e,o9)){var
  g=a[4],h=a[3],i=a[2],j=c(b[2]);return[0,[1,e,i,h,c(g)],j]}return[0,[1,o_,d,a[3],a[4]],b[2]]}return 0};return[0,[1,b,h,g,c(e)]]}return[0,[1,o$,d,a[3],a[4]]]}function
  pd(x,c,v,u,b){if(c){var
  m=c[1];if(!f(m[1],pe))if(!c[2]){var
  a=m[2],l=ar(a,pa),s=l?dy(a,pb):l;if(s){if(b){var
  d=b[1];if(0!==d[0])if(!f(d[1],pf)){var
  e=d[2];if(e){var
  n=e[1];if(!f(n[1],pg))if(!f(n[2],ph))if(!e[2]){var
  g=b[2];if(g){var
  h=g[2];if(h){var
  i=h[1],j=g[1];if(0!==i[0])if(!f(i[1],pi)){var
  k=i[2];if(k){var
  p=k[1];if(!f(p[1],pj))if(!f(p[2],pk))if(!k[2]){var
  t=h[2],q=fp(ae(a,15,(o(a)-9|0)-15|0)),r=function(a){function
  b(b){return fr(b[1],b[2],a)}var
  c=T(fu(pc,q),b);return fw(e0([N,function(b){return w(dE,a)}],c),j)};return[0,[0,I(j,aE(fv(j),r)),t]]}}}}}}}}}return 0}return 0}}return 0}var
  pm=[0,function(a,b,c,d){return ck(pl,a,b,c,d)},pd];function
  pq(u,b,t,s,a){if(b){var
  l=b[1];if(!f(l[1],pr))if(!b[2]){var
  m=l[2],k=ar(m,pn),q=k?dy(m,po):k;if(q){if(a){var
  c=a[1];if(0!==c[0])if(!f(c[1],ps)){var
  d=c[2];if(d){var
  n=d[1];if(!f(n[1],pt))if(!f(n[2],pu))if(!d[2]){var
  e=a[2];if(e){var
  g=e[2];if(g){var
  h=g[1],i=e[1];if(0!==h[0])if(!f(h[1],pv)){var
  j=h[2];if(j){var
  o=j[1];if(!f(o[1],pw))if(!f(o[2],px))if(!j[2]){var
  r=g[2],p=function(a){return fw([0,pp,a],i)};return[0,[0,I(i,aE(fv(i),p)),r]]}}}}}}}}}return 0}return 0}}return 0}var
  pz=[0,function(a,b,c,d){return ck(py,a,b,c,d)},pq];function
  fx(h,g,o){var
  i=h?h[1]:0,c=0,a=o;for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  c=[0,b,c],a=a[2];continue}var
  d=a[2],f=b[4],j=b[3],k=b[2],l=b[1],e=fW(g,l,k,j,f,d);if(typeof
  e==="number"){if(0===e){var
  a=d;continue}var
  m=fx([0,i],g,f),n=[1,l,k,j,m];if(i)if(sV(m,f)){var
  a=[0,n,d];continue}var
  c=[0,n,c],a=d;continue}else{if(0===e[0]){var
  a=[0,e[1],d];continue}var
  a=e[1];continue}}return A(c)}}function
  fy(a,e,d,c,b){return I(0,ah(function(f){if(dJ(f[1],e,d,c,b)){var
  a=dJ(f[2],e,d,c,b);if(typeof
  a==="number")if(!a)return 0;return[0,a]}return 0},a))}function
  fz(a,i,h,b){var
  c=a?a[1]:0;return fx([0,c],function(f,e,d,b,c){var
  a=fy(i,f,e,d,b);if(typeof
  a==="number")switch(a){case
  0:var
  g=ah(function(a){return dJ(a[1],f,e,d,b)?fW(a[2],f,e,d,b,c):0},h);return g?[1,g[1]]:1;case
  1:return[1,w(b,c)];default:return 0}else
  return 0===a[0]?[0,a[1]]:[1,w(a[1],c)]},b)}function
  pB(c){if(!f(c[1][1],pC)){var
  a=c[2][1];if(a){var
  b=a[2];if(b){var
  d=b[2],e=b[1],g=a[1],h=[N,function(a){return e6(d)}];return[0,[0,[0,g],e5(e),h]]}}}return 0}function
  dF(a){return ah(pB,a)}function
  cn(b,a){return ah(function(a){var
  c=a[2];return e(a[1][1],b)?[0,bw(c)]:0},a)}function
  fA(l){var
  c=0,a=l;for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  c=[0,b,c],a=a[2];continue}var
  h=a[2],g=b[2],d=b[1],m=b[3],i=fA(b[4]);if(g){var
  j=g[1];if(!f(j[1],pD))if(!f(j[2],pE))if(!g[2])if(!i){var
  n=e(d,pF)?0:e(d,pG)?0:1;if(!n){var
  k=[0,d]?d:k2,a=dv(1,e3(function(g){return function(a){if(1===a[0]){var
  b=a[2];if(b){var
  c=b[1],d=a[1];if(!f(c[1],k0))if(!f(c[2],k1))if(!b[2])if(e(d,g))return 1}}return 0}}(k),h)[2]);continue}}}var
  c=[0,[1,d,g,m,i],c],a=h;continue}return A(c)}}function
  fB(a){return[0,pI,a[2],[0,pH,a[3]],0]}function
  p4(b){var
  a=b[1][1],c=e(a,p5),d=c||e(a,p6);return d}function
  p7(a){if(!f(a[1][1],p8)){var
  c=a[2],b=c[1];if(b)if(!f(b[1],p9))if(!b[2])return[0,p_,c]}return a}function
  fC(k,d,c){function
  q(h){var
  j=h[3],b=h[2],i=b[3],c=b[2],l=b[1],o=b[4],p=h[1],m=0===j?0:fC(k,d,j),n=[0,qn,a(g+p)];if(e(l,qo))return[1,l,[0,n,c],i,m];var
  q=ao(function(a){return dz(qp,a)},c)?c:[0,qr,c],r=[0,n,w(dt(function(a){return f(a[1][1],qc)?0:[0,[0,qd,bw(a[2])]]},i),q)],s=ad(p7,i);return[1,qq,r,s,w(fA(o),m)]}if(c){var
  h=c[1],l=h[2][3],m=h[1],r=h[4],j=function(g,d,c){function
  f(a){if(2===a[0]){var
  b=a[3];if(e(a[1][1],g)){if(c)var
  f=c[1],d=b?bo(f,b[1]):0;else
  var
  d=0===b?1:0;if(d)return 1}}return 0}function
  h(a){if(2===a[0]){var
  b=a[2];if(b){var
  c=a[4];if(b[1][1]===d)return[0,c]}}return 0}function
  i(a){if(2===a[0])if(!a[2])return[0,a[4]];return 0}var
  b=U(f,k),a=ah(h,b);return a?a:ah(i,b)},o=function(b){var
  a=b[3],d=b[2],e=b[1][1],c=bq(a),f=aH===c?a[1]:N===c?bv(a):a;return j(e,d,f)},p=function(d){var
  a=[N,function(c){function
  b(a){return aE(dF(l),o)}var
  a=cn(p$,d);return aE(ch(function(a){return e(qa,a)},a),b)}];function
  n(h){var
  a=dh(h);if(f(a,pR))if(f(a,pS)){if(f(a,pT))if(f(a,pU))if(f(a,pV))if(f(a,pW))var
  b=0,c=1;else
  var
  b=pY,c=1;else
  var
  b=pZ,c=1;else
  var
  c=0;else
  var
  c=0;if(!c)var
  g=function(a){return e(a,kV)?pO:e(a,kW)?pP:e(a,kU)?0:0},b=aE(cn(pQ,d),g)}else
  var
  b=p0;else
  var
  b=p1;return cg([N,function(a){return pX}],b)}var
  h=I(p3,aE(cn(p2,d),n)),b=h[1],j=h[2],c=e1(T(j,function(a){return[0,pJ,[0,[0,a,0]]]}));if(e(b,pK))return[0,b,0,c];function
  g(a){return cn(pL,a)}var
  i=[N,function(d){var
  b=bq(a),c=aH===b?a[1]:N===b?bv(a):a;return aE(c,g)}];function
  k(a){return[0,pM,a]}var
  m=cg(i,g(d));return[0,b,e1(T(ch(function(a){return f(a,pN)},m),k)),c]},b=I(qb,T(j(d,m,0),p)),n=b[1],s=b[3],t=b[2],u=ad(q,c),v=e(n,qs)?0:[0,[0,qm,i(d,i(ql,i(a(g+m),i(qk,af(qj,ad(kQ,A(r)))))))],0],x=w(v,t);return[0,[1,n,x,w(s,U(p4,l)),u],0]}return 0}var
  qv=c(s[36],qu);function
  fD(e){var
  a=e;for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  c=a[2];if(c){var
  d=c[1],f=b[1];if(0===d[0]){var
  g=c[2],a=[0,[0,i(f,d[1])],g];continue}}}return[0,b,fD(a[2])]}return a}}function
  co(b){if(b){var
  a=b[1];if(0===a[0])return[0,a,co(b[2])];var
  c=a[1],g=b[2],h=a[3],i=a[2],e=co(a[4]),f=co(g);if(d(s[3],c,qv))if(0===e)return f;return[0,[1,c,i,h,fD(e)],f]}return b}function
  fE(a){var
  d=3<=o(a)?1:0;if(d){var
  e=58===aB(a,1)?1:0;if(e){var
  b=aB(a,0),f=113<=b?2<(b-118|0)>>>0?0:1:111<=b?1:0;if(!f)return 0;var
  c=1}else
  var
  c=e}else
  var
  c=d;return c}function
  qB(d,c,b,a){return a?1:2}var
  qC=[0,function(a,d,c,b){return fE(a)},qB];function
  qD(e,a,d,c){if(a){var
  b=a[1];if(!f(b[1],qE))if(!a[2])if(ar(b[2],qF))return 1}return 0}var
  qH=[0,function(a,d,c,b){return e(a,qG)},qD];function
  qM(h,b,g,d){if(b){var
  c=b[1];if(!f(c[1],qN))if(!b[2]){var
  a=dh(c[2]);if(!e(a,qI))if(!e(a,qJ))if(!ar(a,qK))if(!ar(a,qL))return 0;return 2}}return 0}var
  qQ=[0,function(a,h,g,c){var
  d=e(a,qO),b=d||e(a,qP),f=b?0===c?1:0:b;return f},qM];function
  qR(d,a,c,b){if(a)if(!f(a[1][1],qS))if(!a[2])return 2;return 0}var
  qU=[0,function(a,b,c,d){return ck(qT,a,b,c,d)},qR],fF=c(s[36],qV),qX=c(s[36],qW),qY=d(s[7],fF,qX);function
  q6(d,c,b,a){return 1}var
  fG=[0,function(g,f,d,h){var
  a=e(g,q7);if(a)var
  b=0===f?1:0,c=b?0===d?1:0:b;else
  var
  c=a;return c},q6];function
  q8(i,h,g,b){if(b){var
  a=b[1];if(0!==a[0])if(!f(a[1],q9))if(!b[2]){var
  j=a[4],k=a[3],l=a[2],e=c(dw(function(a){return dz(q_,a)}),l),d=e[1],m=e[2];if(d)if(!d[2])return[0,[1,i,dA(ra,d[1][2],h),g,[0,[1,q$,m,k,j],0]]];return 0}}return 0}var
  rc=[0,function(a,d,c,b){return e(a,rb)},q8];function
  rg(q,p,c,o){if(ao(function(a){var
  b=e(a[1][1],rh),c=a[2][1],d=b?bo(c,ri):b;return d},c)){var
  b=A(o);if(b){var
  a=b[1];if(0===a[0])var
  d=0;else
  var
  j=b[2],k=a[4],l=a[3],m=a[2],n=a[1],h=I(0,ah(function(a){var
  b=a[2];return e(a[1][1],rd)?[0,[0,[0,re,bw(b)],0]]:0},c)),f=U(function(a){return ar(a[1][1],rf)},c),i=w(U(function(a){var
  b=a[1][1];return 1-ao(function(a){return e(a[1][1],b)},f)},l),f),g=[0,[1,n,w(h,m),i,k],j],d=1}else
  var
  d=0;if(!d)var
  g=0;return[1,A(g)]}return 0}var
  rk=[0,function(a,d,c,b){return e(a,rj)},rg],rm=c(s[36],rl);function
  fH(a){return d(s[3],a,rm)}var
  ro=c(s[36],rn);function
  rD(c,a,b){return fH(a)}function
  dG(r,a,c){var
  m=fH(a);if(m)var
  n=m;else{var
  o=d(s[3],a,ro);if(o)var
  i=o;else{var
  k=45===aB(a,0)?1:0;if(k)var
  h=k;else{var
  l=ar(a,rB);if(l)var
  q=1-r,h=q||f(a,rC);else
  var
  h=l}var
  i=h}if(i)var
  p=i;else{if(f(a,rp))if(f(a,rq)){if(f(a,rr)){if(f(a,rs))if(f(a,rt))if(f(a,ru))if(f(a,rv))if(f(a,rw))var
  b=1,e=0,g=0;else
  var
  g=1;else
  var
  b=0,e=0,g=0;else
  if(c)if(f(c[1],rz))var
  b=1,e=0,g=0;else
  if(c[2])var
  b=1,e=0,g=0;else
  var
  b=2,e=0,g=0;else
  var
  b=1,e=0,g=0;else
  var
  e=1,g=0;else
  var
  g=1;if(g)if(c)if(f(c[1],rx))var
  b=1,e=0;else
  if(c[2])var
  b=1,e=0;else
  var
  b=2,e=0;else
  var
  b=1,e=0}else
  var
  e=1;if(e)var
  b=c?f(c[1],rA)?1:c[2]?1:2:1}else
  var
  b=0;else
  var
  b=0;switch(b){case
  0:var
  j=c?f(c[1],ry)?0:c[2]?0:1:0;break;case
  1:var
  j=0;break;default:var
  j=1}var
  t=j?1:0,p=t}var
  n=1-p}return n}var
  rF=c(s[36],rE);function
  fI(a,c,b){var
  d=a?a[1]:0,e=c?dG:rD;return U(function(a){return e(d,a[1][1],a[2][1])},b)}function
  fJ(a,c){var
  e=a?a[1]:0;function
  b(g,f,a,d){var
  b=fI([0,e],c,a);return a===b?0:[0,[1,g,f,b,d]]}return[0,function(c,g,b,f){var
  a=0!==b?1:0,e=a?1-d(s[3],c,rF):a;return e},b]}function
  rG(b,a){if(0===b[0]){var
  d=b[1],c=fI(0,1,b[2]);return 0===c?a:[0,[0,d,c],a]}return a}var
  fK=c(s[36],rH);function
  dH(a){return U(function(a){return 1-d(s[3],a[1],fK)},a)}function
  fL(g,f,d,a){return U(function(c){var
  a=c[1][1],h=c[2][1];if(g){var
  b=1-e(a,d);return b?dG(1,a,h):b}return e(a,f)},a)}function
  rK(k,d,c,j){var
  b=du(function(a){return e(a[1],rJ)},d);if(b){var
  f=b[1];if(!ao(function(a){return e(a[1][1],rL)},c)){var
  a=f[2],g=f[1],h=hg(a)?i(a,rI):a;return[0,[1,k,d,[0,[0,[0,g],[0,[0,h,0]]],c],j]]}}return 0}var
  cp=n,fM=null,fN=undefined,rN=[0,function(a,d,c,b){return e(a,rM)},rK],rV=[0,o5,[0,qC,[0,qH,0]]];function
  cq(a){return a==fM?0:[0,a]}var
  r0=cp.Array,fO=[z,r1,S(0)];hY(r2,[0,fO,{}]);(function(a){throw a});eM(function(a){return a[1]===fO?[0,aO(a[2].toString())]:0});eM(function(a){return a
  instanceof
  r0?0:[0,aO(a.toString())]});var
  fP=cp.document;S(0);cp.HTMLElement===fN;var
  fQ="wordimport.js",r3=sH(0);function
  fR(a){return a===fN?0:[0,a]}function
  r4(a){return e(aO(typeof
  a),r5)}function
  r6(d){var
  a=d.src,b=a.length;if(0<b){var
  c=a.indexOf(d5),e=c<0?b:c,f=fQ.length;return(a.indexOf(fQ)+f|0)===e?1:0}return 0}function
  fS(a){var
  b=a.split("."),c=[0,cp];return b.reduce(sJ(function(a,b,d,c){return a?fR(a[1][b]):a}),c)}function
  fT(a){if(a){var
  b=a[1],c="data-main",d=cq(b.getAttribute(c));if(d){var
  e=d[1];b.removeAttribute(c);return ch(r4,fS(e))}return 0}return 0}var
  r7=[N,function(g){var
  d=fP.getElementsByTagName(P),b=0,a=0,e=d.length;for(;;){if(a<e){var
  c=cq(d.item(a));if(c){var
  b=[0,c[1],b],a=a+1|0;continue}var
  a=a+1|0;continue}var
  f=A(b);return ah(function(a){var
  b=a.tagName.toLowerCase()===P?a:fM;return fT(ch(r6,cq(b)))},f)}}],fU=cg(r7,fT(fR(fP.currentScript))),dI=[0,0,0,0];function
  r8(n,m){if(!dI[1]){if(0===cr)var
  a=eS([0]);else{var
  e=eS(eG(h0,cr)),g=cr.length-1-1|0,k=0;if(!(g<0)){var
  b=k;for(;;){var
  h=(b*2|0)+2|0;e[3]=ai(ce[4],cr[1+b],h,e[3]);e[4]=ai(cf[4],h,1,e[4]);var
  l=b+1|0;if(g!==b){var
  b=l;continue}break}}var
  a=e}var
  i=h6(a,r9),f=h4(a,r_),j=function(b){var
  a=b[1+i];return c(a[2],a[1])};h2[1]++;if(d(cf[27],f,a[4])){dp(a,f+1|0);u(a[2],f)[1+f]=j}else
  a[6]=[0,[0,f,j],a[6]];var
  o=function(d){var
  b=g7(z,a[1]);b[1]=a[2];var
  c=s3(b);c[1+i]=d;return c};eT[1]=(eT[1]+a[1]|0)-1|0;a[8]=A(a[8]);dp(a,3+((u(a[2],1)[2]*16|0)/32|0)|0);dI[1]=o}return c(dI[1],[0,n,m])}function
  r$(aR,aN,aM,aL){var
  X=cq(aL);if(X){var
  W=eG(aO,sI(X[1])),i=W.length-1-1|0,n=0;for(;;){if(0<=i){var
  $=[0,W[1+i],n],i=i-1|0,n=$;continue}var
  Y=n;break}}else
  var
  Y=0;var
  Z=aO(aN),_=aM|0,S=[0,_]?_:0,aP=[0,0],h=[0,S]?S:0,y=fp(Z),j=y;for(;;){if(j){var
  r=j[1];if(1===r[0])if(f(r[1],k7))var
  C=0;else
  var
  v=[0,r[4]],C=1;else
  var
  C=0;if(!C){var
  j=j[2];continue}}else
  var
  v=0;if(v){var
  ag=v[1],G=dx(function(a){if(1===a[0])if(!f(a[1],oE))return 1;return 0},ag),t=G[2],ab=G[1],ac=I(0,T(e7(ci(e9,ci(e8(oI),ab))),fk));if(t){var
  k=t[1];if(0===k[0])var
  m=0;else
  if(f(k[1],oD))var
  m=0;else
  var
  u=[0,k[3],k[4]],m=1}else
  var
  m=0;if(!m)var
  u=[0,0,t];var
  l=[0,ac,u[2],u[1]]}else
  var
  H=e3(function(a){if(1===a[0]){var
  b=a[2];if(b){var
  c=b[1],g=a[1];if(!f(c[1],oF))if(!b[2]){var
  h=c[2],d=ck(oG,g,b,a[3],a[4]),i=d?e(dh(h),oH):d;return i}}}return 0},y),L=H[2],ae=H[1],l=L?[0,I(0,T(e7(ci(e9,ci(e8(oK),[0,[1,oJ,0,0,ae],0]))),fk)),L,0]:[0,0,y,0];var
  z=l[1],aH=l[3],aI=l[2],Q=[0,0],aG=0,aF=[0,rk,[0,fJ(rX,h),[0,fG,0]]],aj=function(v,r,j,p,o){function
  a(x){var
  f=x[1][1],y=[0,[1,v,r,j,p],o];function
  i(p){var
  a=p;for(;;){if(a){var
  c=a[1];if(0!==c[0]){var
  j=a[2],g=c[4],h=c[3],k=c[2],l=c[1],m=dF(h);if(m){var
  n=m[1],q=n[2];if(e(n[1][1],f)){var
  o=i(j);return[0,[0,[0,q,[0,l,k,h,g]],o[1]],o[2]]}return[0,0,a]}var
  b=fy(aF,l,k,h,g);if(typeof
  b==="number")switch(b){case
  0:var
  d=0;break;case
  1:var
  d=[0,g];break;default:var
  d=pA}else
  var
  d=0===b[0]?[0,[0,b[1],0]]:[0,b[1]];if(d){var
  a=w(d[1],j);continue}return[0,0,a]}}return[0,0,a]}}var
  c=i(y),b=c[1],C=c[2];function
  g(k,j){var
  a=k,b=j;for(;;){if(b){var
  e=b[1],f=e[2],d=e[1],l=b[2];if(a){var
  h=a[1],c=h[1],m=a[2],n=h[2];if(c<d){var
  o=d===(c+1|0)?0:[0,[0,c+1|0,fB(f),0],0],i=g(o,b),p=i[2],a=[0,[0,c,n,A(i[1])],m],b=p;continue}if(d<c)return[0,a,b]}var
  a=[0,[0,d,f,0],a],b=l;continue}return[0,a,0]}}if(b)var
  d=b[1],m=d[2],n=1===d[1]?0:[0,[0,1,fB(m),0],0],k=A(g(n,b)[1]);else
  var
  k=0;var
  s=Q[1],D=0;function
  t(a){var
  b=a[3];return ao(function(a){var
  b=e(a[1][1],qe),c=a[2][1],d=b?bo(c,qf):b;return d},b)}function
  h(a){if(a){var
  b=a[1];if(a[2])return[0,b+1|0,a[2]]}return a}function
  u(a,b,e){var
  c=aS(a);if(c===b)return[0,a,0,h(a)];if(b<c){var
  d=dv(c-b|0,a),f=e?[0,a]:0;return[0,d,f,h(d)]}return qg}function
  a(b,i,h,g){if(b){var
  c=b[1],j=c[2],k=c[1],v=b[2],w=c[3],d=function(l){var
  c=t(j),a=[0,h,i],d=k+1|0;if(g)return u(g[1],d,c);if(c){try{var
  b=a6(f,s)}catch(b){b=B(b);if(b===q)return[0,a,0,a];throw b}var
  e=dv(aS(b)-d|0,b);return[0,e,[0,b],e]}return[0,a,0,a]}(0),e=d[3],l=d[1],x=d[2],y=e?e[1]:h,m=a(w,e,1,x),n=m[2],z=m[1],o=a(v,i,y+1|0,0),p=o[2],A=o[1];if(0!==p)var
  r=p;else
  var
  C=0!==n?n:l,r=C;return[0,[0,[0,k,j,z,l],A],r]}return qh}var
  l=a(k,qi,1,D),E=l[1];Q[1]=[0,[0,f,l[2]],s];return[0,w(fC(z,f,E),C)]}return I(0,T(dF(j),a))},aJ=fz(0,rV,[0,pz,[0,pm,[0,[0,function(a,d,c,b){return e(a,qt)},aj],aG]]],aI),aw=[0,fG,[0,rc,0]],au=c(s[36],rS),av=function(f,b,a,e){var
  c=dH(b),d=fL(h,rU,rT,a);if(c===b)if(d===a)return 0;return[0,[1,f,c,d,e]]},ax=[0,[0,function(a,e,c,b){return d(s[3],a,au)},av],aw],at=function(f,b,a,e){var
  c=dH(b),d=fL(h,rQ,rP,a);if(c===b)if(d===a)return 0;return[0,[1,f,c,d,e]]},ay=[0,[0,function(a,d,c,b){return e(a,rR)},at],ax],as=function(g,b,a,f){var
  c=dH(b),e=U(function(a){var
  b=a[1][1],c=a[2][1];return h?dG(1,b,c):d(s[3],b,fK)},a);if(c===b)if(e===a)return 0;return[0,[1,g,c,e,f]]},aA=[0,rN,[0,[0,function(a,d,c,b){return e(a,rO)},as],ay]],ar=function(d,c,b,a){return a?1:2},aB=[0,[0,function(a,g,f,e){if(h)return 0;var
  b=c(s[36],Y);return d(s[3],a,b)},ar],aA],aC=[0,qU,[0,qQ,[0,fJ(rW,h),aB]]],aq=function(d,c,b,a){return ao(function(b){if(0===b[0]){var
  a=b[1][1];if(a)if(!f(a[1],q3))if(!a[2])return 1}return 0},z)?[0,[1,d,U(function(a){var
  b=e(a[1],q4),c=a[2],d=b?e(c,q5):b;return 1-d},c),b,a]]:1},aD=[0,[0,function(d,a,c,b){return h?ao(function(a){var
  b=e(a[1],q1),c=a[2],d=b?e(c,q2):b;return d},a):0},aq],aC],an=h?fF:qY,ap=function(g,a,f,c){var
  b=U(function(c){var
  a=c[1],i=c[2],f=fE(a);if(f)var
  g=f;else
  var
  b=e(a,qZ),h=b?e(i,q0):b,g=h||d(s[3],a,an);return 1-g},a);return a===b?0:[0,[1,g,b,f,c]]},x=[0,0],aE=[0,lm,[0,lf,[0,[0,function(d,a,c,b){return 0!==a?1:0},ap],aD]]],al=0,ak=function(i,d,h,b){var
  e=[N,function(f){function
  e(j){var
  c=j[2],e=x[1];try{var
  f=a6(c,e)}catch(a){a=B(a);if(a===q){x[1]=[0,[0,c,aS(b)],e];return 0}throw a}x[1]=[0,[0,c,f+aS(b)|0],e];return[0,[1,i,[0,[0,qw,a(g+(f+1|0))],d],h,b]]}return I(0,T(c(a7(qx),d),e))}];function
  f(a){return 0}return e0(e,T(c(a7(qy),d),f))},am=co(fz(qA,[0,[0,function(a,d,c,b){return e(a,qz)},ak],aE],al,aJ)),R=h?a5(rG,z,0):0,aK=h?dt(function(a){var
  b=a[2];return e(a[1][1],rY)?[0,[0,rZ,bw(b)]]:0},aH):0,M=0===R?0:[0,[1,oP,0,0,[0,[0,af(lW,ad(lJ,R))],0]],0],O=[0,oL,0],P=0===O?1:0,ah=P?0===M?1:0:P,ai=ah?0:[0,[1,oO,0,0,w(O,M)],0],V=o(Z),aa=fm(0,[0,[1,oN,0,0,w(ai,[0,[1,oM,aK,0,am],0])],0]),D=1<=V?V:1,E=bs<D?bs:D,F=J(E),b=[0,F,0,E,F];ks(dq,1,function(i){var
  c=o(i),h=b[2]+c|0;if(b[3]<h){var
  e=b[2],a=[0,b[3]];for(;;){if(a[1]<(e+c|0)){a[1]=2*a[1]|0;continue}if(bs<a[1])if((e+c|0)<=bs)a[1]=bs;else
  ex(hU);var
  f=J(a[1]),d=b[2],g=b[1],j=0<=d?(aQ(g)-d|0)<0?0:(aQ(f)-d|0)<0?0:(az(g,0,f,0,d),1):0;if(!j)K(hn);b[1]=f;b[3]=a[1];if(!((b[2]+c|0)<=b[3]))throw[0,p,hT];if(!((e+c|0)<=b[3]))throw[0,p,hS];break}}bm(i,0,b[1],b[2],c);b[2]=h;return 0},aa);return df(b[1],0,b[2]).toString()}}var
  fV=function(a,b){return{"cleanDocument":sK(a)}}(r$,r8);function
  sa(c,b){var
  a=fS("tinymce.Resource.add");return a?a[1](c,b):r3.error("Unable to find Word Import registration API")}function
  sb(a){return a(fV)}if(fU)sb(fU[1]);else
  sa(sc,fV);hh(0);return}(function(){return this}()));
