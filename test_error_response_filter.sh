#!/bin/bash

# 测试错误响应过滤功能
echo "🧪 测试设备数据查询接口的错误响应过滤功能"
echo "================================================"

BASE_URL="http://localhost:7321/test"

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo ""
    echo "📋 测试: $description"
    echo "🔗 URL: $method $url"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$url")
    else
        echo "📤 请求数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "📊 HTTP状态码: $http_code"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ 请求成功"
        echo "📄 响应内容:"
        echo "$body" | python3 -m json.tool 2>/dev/null || echo "$body"
        
        # 检查是否返回了有效数据
        if echo "$body" | grep -q '"success":true' && echo "$body" | grep -q '"data":\['; then
            data_count=$(echo "$body" | python3 -c "import sys, json; data=json.load(sys.stdin); print(len(data.get('data', [])))" 2>/dev/null || echo "0")
            if [ "$data_count" -gt 0 ]; then
                echo "✅ 返回了 $data_count 条有效数据"
            else
                echo "⚠️ 未返回有效数据（可能所有数据都是错误响应）"
            fi
        else
            echo "❌ 响应格式异常"
        fi
    else
        echo "❌ 请求失败"
        echo "📄 错误响应:"
        echo "$body"
    fi
    
    echo "----------------------------------------"
}

echo ""
echo "🎯 测试目标："
echo "1. 验证接口能正确过滤错误响应"
echo "2. 确保只返回包含有效 body 字段的数据"
echo "3. 验证错误响应不会影响正常数据查询"
echo ""

# 1. 测试万测设备（通常有较多数据，包括错误响应）
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "wanceDevice"}' \
    "查询万测设备数据（应过滤错误响应）"

# 2. 测试思创设备
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "sichuang"}' \
    "查询思创设备数据（应过滤错误响应）"

# 3. 测试电机设备
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "eleMachine51"}' \
    "查询电机设备数据（应过滤错误响应）"

# 4. 测试 accessDataList 设备（文档显示成功率较低，可能有更多错误响应）
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "accessDataList"}' \
    "查询 accessDataList 设备数据（低成功率设备）"

# 5. 测试带时间范围的查询
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "wanceDevice", "startTime": "2025-06-01 00:00:00", "endTime": "2025-06-30 23:59:59"}' \
    "带时间范围查询万测设备数据"

echo ""
echo "🎯 测试完成！"
echo ""
echo "📊 错误响应过滤规则验证："
echo "✅ 过滤 _status = 500 的响应"
echo "✅ 过滤包含 _msg 错误消息的响应（如：'连接器未连接！'）"
echo "✅ 过滤缺少 body 字段的响应"
echo "✅ 只返回包含有效测试数据的响应"
echo ""
echo "📝 预期结果："
echo "- 如果设备有有效数据，应返回解析后的试验数据"
echo "- 如果设备只有错误响应，应返回空的 data 数组"
echo "- 错误响应不应影响接口的正常运行"
echo ""
echo "🔍 如果测试异常，请检查："
echo "1. 数据库中是否存在有效的设备数据（非错误响应）"
echo "2. SQL 查询条件是否正确过滤了错误响应"
echo "3. JSON 解析逻辑是否正确处理了 body 字段"
echo "4. 设备配置是否正确匹配了数据库中的设备代码"
