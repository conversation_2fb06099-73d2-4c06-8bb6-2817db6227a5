# 测试业务系统部署指南 📚

## 系统环境配置 ⚙️

### 1. 环境要求

- JDK 17
- MySQL 8.x
- Redis 6.x
- Linux 服务器 (CentOS/Ubuntu)

### 2. 配置文件说明

系统使用了两个主要的配置文件:

- **application.properties**: 应用的基础配置
- **application-prod.properties**: 生产环境的特定配置

生产环境配置包含了数据库连接、Redis设置、端口配置等信息，已针对生产环境进行了优化。

## 部署和运行 🚀

### 1. 文件部署

将以下文件复制到服务器的 `/home/<USER>/apps/` 目录下:
- `test_business_back-0.0.1-SNAPSHOT.jar` (应用JAR包)
- `start.sh` (启动脚本)
- `stop.sh` (停止脚本)
- `check.sh` (状态检查脚本)

### 2. 运行脚本说明

**启动应用:**
```bash
./start.sh
```
该脚本会:
- 检查并释放7325端口
- 启动应用程序
- 将日志输出到 `/home/<USER>/apps/logs/app.log`
- 监控应用启动状态

**停止应用:**
```bash
./stop.sh
```
该脚本会安全地停止应用程序并释放端口。

**检查应用状态:**
```bash
./check.sh
```
该脚本会显示:
- 应用程序运行状态
- 端口占用情况
- 最近的日志信息
- 系统资源使用情况

## 解决常见问题 🔧

### 1. "文件已截断"问题

启动脚本已优化，不再使用 `>>` 附加模式写入日志，而是使用 `>` 覆盖模式，避免日志文件截断问题。

### 2. 端口占用问题

如果端口被占用，启动脚本会自动尝试释放端口。手动解决:
```bash
lsof -i:7325  # 查找占用进程
kill -9 [PID] # 终止进程
```

### 3. 数据库连接问题

如果应用无法连接数据库，检查:
- 数据库服务是否运行
- 应用配置的IP地址和端口是否正确
- 用户名和密码是否正确
- 防火墙是否允许连接

## 监控和维护 📊

### 1. 日志位置

- 应用日志: `/home/<USER>/apps/logs/app.log`
- 系统日志: `/home/<USER>/apps/logs/jz_gw_test_back/`

### 2. 定期维护建议

- 定期备份数据库
- 监控磁盘空间使用情况
- 定期检查系统运行状态
- 定期清理不需要的日志文件

## MQTT消息环境隔离 🔄

系统使用MQTT协议与设备通信。为了避免不同环境（测试、生产）的消息互相干扰，我们实现了基于消息ID前缀的环境隔离机制。

### 1. 消息ID格式

消息ID的格式为: `{环境前缀}_{时间戳}`

- 测试环境: `jz_westcatr_test_1715577821056`
- 生产环境: `jz_westcatr_prod_1715577821056`

### 2. 环境配置

在不同环境的配置文件中，通过以下参数区分消息来源:

**测试环境 (application-dev.properties):**
```properties
mqtt.client-id-prefix=jz_westcatr_test
```

**生产环境 (application-prod.properties):**
```properties
mqtt.client-id-prefix=jz_westcatr_prod
```

### 3. 工作原理

- 系统发送消息时，会在messageId前添加环境标识前缀
- 接收消息时，会检查messageId的前缀是否与当前环境匹配
- 不匹配的消息会被记录但不处理，避免跨环境消息干扰
- 这允许测试环境和生产环境并行运行而不互相影响