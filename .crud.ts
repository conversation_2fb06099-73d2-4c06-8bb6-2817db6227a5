const CrudConfig = {
  // 后端swagger地址
  url: 'http://10.36.16.164:7320/test/v3/api-docs/apis',
  projectName: 'base',
  customClassName: {
    /** ============ 校验器 ============ */
    /** 校验器 */
    captchaController: 'captcha',
    /** ============ end 校验器 ============ */

    /** ============ 用户管理 ============ */
    /** 用户管理——系统用户接口 */
    yonghuguanlixitongyonghujiekou: 'userSys',
    /** 用户管理——用户密码相关接口 */
    yonghuguanliyonghumimaxiangguanjiekou: 'userPassword',
    /** ============ end 用户管理 ============ */

    /** ============ 权限模块 ============ */
    /** 权限模块——登录接口 */
    quanxianmokuaidenglujiekou: 'authLogin',
    /** 权限模块——角色管理 */
    quanxianmokuaijiaoseguanli: 'authRole',
    /** 权限模块——前端页面权限管理 */
    quanxianmokuaiqianduanyemianquanxianguanli: 'authFe',
    /** 权限模块——后端接口信息接口 */
    quanxianmokuaihouduanjiekouxinxijiekou: 'authBe',
    /** 权限模块——登录日志管理 */
    quanxianmokuaidenglurizhiguanli: 'authLoginLog',
    /** 权限模块——系统接口日志接口 */
    quanxianmokuaixitongjiekourizhijiekou: 'authApiLog',
    /** ============ end 权限模块 ============ */

    /** ============ 消息模块 ============ */
    /** 消息模块——系统公告信息接口 */
    xiaoximokuaixitonggonggaoxinxijiekou: 'msgNotice',
    /** 消息模块——通知信息表接口 */
    xiaoximokuaitongzhixinxibiaojiekou: 'msgNotify',
    /** 消息模块——系统公告接收对象信息接口 */
    xiaoximokuaixitonggonggaojieshouxinxijiekou: 'msgRecipient',
    /** 消息模块——待办信息接口 */
    xiaoximokuaidaibanxinxijiekou: 'msgTodo',
    /** 消息模块——待办类型接口 */
    xiaoximokuaidaibanleixingjiekou: 'msgTodoType',
    /** 消息模块——系统消息发送记录接口 */
    xiaoximokuaixitongxiaoxifasongjilujiekou: 'msgSendLog',
    /** 消息模块——系统公告已读用户记录接口 */
    xiaoximokuaixitonggonggaoyiduyonghujilujiekou: 'msgReadLog',
    /** 消息模块——用户消息模块配置接口 */
    xiaoximokuaiyonghuxiaoximokuaipeizhijiekou: 'msgConfig',
    /** ============ end 消息模块 ============ */

    /** ============ 字典模块 ============ */
    /** 基础模块——字典主信息接口 */
    jichumokuaizidianzhuxinxijiekou: 'baseDictMain',
    /** 基础模块——字典信息接口 */
    jichumokuaizidianxinxijiekou: 'baseDict',
    /** 基础模块——系统配置信息接口 */
    jichumokuaixitongpeizhixinxijiekou: 'baseCfg',
    /** 基础模块——通用树主信息管理 */
    jichumokuaitongyongshuzhuxinxiguanli: 'baseTreeMain',
    /** 基础模块——通用树数据管理 */
    jichumokuaitongyongshushujuguanli: 'baseTree',
    /** ============ end 字典模块 ============ */

    /** ============ 文件模块 ============ */
    /** 扩展功能——文件管理 */
    kuozhangongnengwenjianguanli: 'extFile',
    /** 扩展功能——obs文件管理 上传视频 */
    kuozhangongnengobswenjianguanli: 'extFileObs',
    /** ============ end 文件模块 ============ */

    /** ============ 监控模块 ============ */
    /** 监控模块——系统监控信息 */
    jiankongmokuaixitongjiankongxinxi: 'monitorSys',
    /** ============ 监控模块 ============ */

    /** ============ 缓存模块 ============ */
    /** 缓存模块——两级缓存管理 */
    huancunmokuailiangjihuancunguanli: 'cacheManage',
    /** 缓存模块——两级缓存监控 */
    huancunmokuailiangjihuancunjiankong: 'cacheMonitor',

    /** ============ 数据权限模块 ============ */
    /** ============ 缓存模块 ============ */
    /** 角色数据权限配置信息接口 */
    jiaoseshujuquanxianpeizhixinxijiekou: 'dataAuthRole',
    /** 数据权限模板信息接口 */
    shujuquanxianmobanxinxijiekou: 'dataAuthTpl',
    /** ============ 数据权限模块 ============ */
    OriginalRecordInfoVO: 'TestDataTestDataOriginalRecordInfoVO',
    jichuxinxichanpinleibiebiao: 'jichuxinxichanpinleibiebiaotongbujiekou',
  },
  // cutomVoPages:['/flowTaskInfo/todo']
};

module.exports = CrudConfig;
