#!/bin/bash

# 测试修改后的设备数据查询接口
# 现在从 zzzzz_log_device_function_call 表获取数据

echo "🧪 测试修改后的设备数据查询接口"
echo "=================================="

BASE_URL="http://localhost:7321/test"

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo ""
    echo "📋 测试: $description"
    echo "🔗 URL: $method $url"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$url")
    else
        echo "📤 请求数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    echo "📊 HTTP状态码: $http_code"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ 请求成功"
        echo "📄 响应内容:"
        echo "$body" | python3 -m json.tool 2>/dev/null || echo "$body"
    else
        echo "❌ 请求失败"
        echo "📄 错误响应:"
        echo "$body"
    fi
    
    echo "----------------------------------------"
}

# 1. 测试 GET 方式查询万测设备数据（不传时间参数，查询最新数据）
test_api "GET" \
    "$BASE_URL/device-data/query?deviceCode=wanceDevice" \
    "" \
    "GET方式查询万测设备最新数据"

# 2. 测试 POST 方式查询万测设备数据（不传时间参数）
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "wanceDevice"}' \
    "POST方式查询万测设备最新数据"

# 3. 测试查询思创设备数据
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "sichuang"}' \
    "查询思创设备最新数据"

# 4. 测试查询电机设备数据
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "eleMachine51"}' \
    "查询电机设备最新数据"

# 5. 测试带时间范围的查询
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "wanceDevice", "startTime": "2025-06-01 00:00:00", "endTime": "2025-06-30 23:59:59"}' \
    "带时间范围查询万测设备数据"

# 6. 测试不存在的设备
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "nonExistentDevice"}' \
    "查询不存在的设备"

# 7. 测试无效参数
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": ""}' \
    "测试空设备代码"

echo ""
echo "🎯 测试完成！"
echo ""
echo "📝 说明："
echo "1. 修改后的接口现在从 zzzzz_log_device_function_call 表获取数据"
echo "2. 通过 device_id 或 product 字段匹配设备代码"
echo "3. 解析 output_params JSON 字段获取试验数据"
echo "4. 自动过滤错误响应（_status=500、_msg字段、缺少body字段）"
echo "5. 保持原有的配置映射机制不变"
echo ""
echo "🔍 如果测试失败，请检查："
echo "- 应用是否正常启动"
echo "- 数据库连接是否正常"
echo "- zzzzz_log_device_function_call 表是否有有效数据（非错误响应）"
echo "- bu_device_data_config 表是否有对应的设备配置"
echo ""
echo "⚠️ 错误响应过滤规则："
echo "- 过滤 _status = 500 的响应"
echo "- 过滤包含 _msg 错误消息的响应"
echo "- 过滤缺少 body 字段的响应"
echo "- 只返回包含有效测试数据的响应"
