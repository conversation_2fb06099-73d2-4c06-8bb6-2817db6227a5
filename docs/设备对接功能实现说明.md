# 🚀 设备对接功能实现说明

## 📋 概述

本文档详细说明了基于数据库配置的统一设备对接功能实现，支持动态读取设备配置参数，实现所有mqttdemo中定义的设备对接。

## 🎯 实现目标

1. ✅ 实现统一的设备对接框架
2. ✅ 支持从数据库动态读取设备配置
3. ✅ 支持设备参数的自定义配置
4. ✅ 实现变压器设备的完整对接
5. ✅ 支持所有mqttdemo中的设备类型
6. ✅ 提供统一的API接口

## 🏗️ 架构设计

### 核心组件

1. **UnifiedDeviceIntegrationService** - 统一设备对接服务
2. **DeviceIntegrationConfig** - 设备对接配置实体
3. **InstrumentNewInfo** - 设备信息实体
4. **MqttClientService** - MQTT客户端服务

### 数据库表结构

#### bu_device_integration_config (设备对接规范表)
- `device_type` - 设备类型
- `device_name` - 设备名称
- `product_code` - 产品代码
- `device_code` - 设备代码
- `type_code` - 类型代码
- `request_template` - 请求模板
- `response_template` - 响应模板
- `param_mapping` - 参数映射

#### bu_instrument_new_info (设备表)
- `device_type_code` - 设备类型代码
- `device_params` - 设备参数(JSON格式)
- `device_integration_config_id` - 关联的配置ID

## 🔧 功能特性

### 1. 统一设备对接
- 支持所有设备类型的统一对接逻辑
- 自动从数据库读取设备配置
- 支持设备配置缓存机制

### 2. 动态参数配置
- 设备参数可在数据库中自定义配置
- 支持JSON格式的复杂参数结构
- 针对不同设备类型自动添加特殊参数

### 3. 智能数据解析
- 根据设备类型选择不同的解析策略
- 支持变压器设备的复杂参数映射
- 通用设备的标准化数据处理

### 4. 容错机制
- 配置缺失时自动生成默认结果
- 异常情况下的优雅降级
- 详细的日志记录和错误追踪

## 📡 API接口

### 主要接口

#### 1. 统一设备测试数据获取
```http
POST /instrumentNewInfo/getUnifiedDeviceTestData
Content-Type: application/json

{
  "equipmentId": 1003,
  "parameterIds": [1, 2, 3]
}
```

#### 2. 原有设备测试数据获取（增强版）
```http
POST /instrumentNewInfo/getDeviceTestData
Content-Type: application/json

{
  "equipmentId": 1003,
  "parameterIds": [1, 2, 3]
}
```

#### 3. 异步设备请求
```http
POST /instrumentNewInfo/sendDeviceRequestAsync
Content-Type: application/json

{
  "equipmentId": 1003,
  "parameterIds": [1, 2, 3]
}
```

#### 4. 获取设备响应
```http
POST /instrumentNewInfo/getDeviceResponse
Content-Type: application/json

{
  "equipmentId": 1003
}
```

## 🎛️ 支持的设备类型

### 1. 变压器设备 (万测变压器)
- **设备类型**: 变压器
- **产品代码**: accessId
- **设备代码**: wanceDevice
- **支持试验**: 直阻、变比、空载、负载试验
- **特殊功能**: 复杂参数映射、多试验类型支持

### 2. 电子万能试验机
- **设备类型**: 电子万能试验机
- **产品代码**: electronicUTM
- **设备代码**: electronicUTM10T
- **连接方式**: SMB文件共享

### 3. 电机设备
- **设备类型**: 电机设备
- **产品代码**: eleMachine
- **设备代码**: eleMachine51
- **连接方式**: SMB文件共享

### 4. 思创设备
- **设备类型**: 思创设备
- **产品代码**: sichuangDevice
- **设备代码**: sichuang
- **特殊功能**: 自定义测试数据处理

### 5. 绝缘子机电破坏试验机
- **设备类型**: 绝缘子机电破坏试验机
- **产品代码**: jyzjdph
- **设备代码**: jyaDevice

### 6. 维卡万能试验机
- **设备类型**: 维卡万能试验机
- **产品代码**: wkProduct
- **设备代码**: wkDevice

## 🔄 工作流程

### 1. 设备配置查询
1. 优先使用设备表中指定的配置ID
2. 根据设备类型代码查找匹配的配置
3. 使用配置缓存提高性能

### 2. 参数准备
1. 解析设备表中的自定义参数
2. 根据设备类型添加特殊参数
3. 合并所有参数形成最终请求参数

### 3. 设备通信
1. 使用MQTT客户端发送设备请求
2. 等待设备响应或超时处理
3. 记录通信日志和状态

### 4. 数据解析
1. 根据设备类型选择解析策略
2. 使用参数映射配置解析响应数据
3. 生成标准化的测试结果

## 🧪 测试方法

### 1. 使用测试脚本
```bash
chmod +x test_device_integration.sh
./test_device_integration.sh
```

### 2. 手动测试
使用Postman或curl工具调用API接口进行测试

### 3. 单元测试
运行项目中的单元测试用例

## 📊 配置示例

### 变压器设备配置示例
```json
{
  "device_type": "变压器",
  "device_name": "万测变压器",
  "product_code": "accessId",
  "device_code": "wanceDevice",
  "type_code": "getDataByTable",
  "request_template": "...",
  "response_template": "...",
  "param_mapping": {
    "experiments": [
      {
        "name": "直阻试验",
        "code": "DTS_T_RZZLDZCL",
        "tableName": "DTS_T_RZZLDZCL",
        "mapping": {
          "高压1档R1": "r1a",
          "高压1档R2": "r1b",
          "高压1档R3": "r1c"
        }
      }
    ]
  }
}
```

### 设备参数配置示例
```json
{
  "tableName": "DTS_T_RZZLDZCL",
  "resultNum": "10",
  "host": "*************",
  "userName": "admin",
  "pwd": "password"
}
```

## 🔍 故障排查

### 常见问题

1. **设备配置未找到**
   - 检查设备类型代码是否正确
   - 确认配置状态为enabled
   - 查看配置缓存是否需要刷新

2. **MQTT连接失败**
   - 检查MQTT服务器连接状态
   - 确认网络连通性
   - 查看MQTT客户端日志

3. **数据解析失败**
   - 检查参数映射配置是否正确
   - 确认设备响应数据格式
   - 查看解析逻辑日志

### 日志查看
```bash
# 查看应用日志
tail -f logs/test_business_back.log | grep "设备对接"

# 查看MQTT日志
tail -f logs/test_business_back.log | grep "MQTT"
```

## 🚀 部署说明

1. 确保数据库连接正常
2. 配置MQTT服务器地址
3. 启动应用程序
4. 验证设备对接功能

## 📝 注意事项

1. 设备参数配置需要严格按照JSON格式
2. MQTT连接需要稳定的网络环境
3. 设备响应超时时间可根据实际情况调整
4. 建议定期清理设备配置缓存

## 🔄 后续优化

1. 增加设备状态监控
2. 实现设备配置的热更新
3. 添加更多设备类型支持
4. 优化数据解析性能
