# sichuangDevice 类型设备对接文档

> 四川设备对接详细说明  
> 设备类型: sichuangDevice  
> 主要设备: sichuang, sichuangTest, sichang  

## 📋 设备信息

| 设备ID | 调用次数 | 成功率 | 活跃期 | 状态 |
|--------|----------|--------|--------|------|
| sichuang | 60 | 56.7% | 2025-04-18 至今 | 🟢 开发中 |
| sichuangTest | 8 | 0% | 2025-04-18 | 🔴 测试失败 |
| sichang | 3 | 0% | 2025-04-18 | 🔴 测试失败 |

## 🔄 调用特征

- **调用方式**: 100% 异步调用
- **响应时间**: 快速响应，主要为状态信息
- **数据量**: 轻量级状态数据
- **更新频率**: 中等频率，集中在开发测试期

## 📊 响应数据结构

### 主要响应格式

#### 错误响应 (主要格式)
```json
{
  "_msg": "连接器未启动！",
  "_status": 500
}
```

#### 可能的成功响应格式
```json
{
  "status": "success",
  "data": {
    // 设备状态或测试数据
  }
}
```

## 📝 典型响应示例

### 示例1: 连接器未启动错误
```json
{
  "_msg": "连接器未启动！",
  "_status": 500
}
```

### 示例2: 其他可能的错误状态
```json
{
  "_msg": "设备离线",
  "_status": 500
}
```

```json
{
  "_msg": "通信超时",
  "_status": 500
}
```

## ⚠️ 当前状态分析

### 问题诊断
1. **sichuang设备**: 
   - 60次调用中仅34次有响应数据
   - 主要错误: "连接器未启动！"
   - 成功率: 56.7%

2. **sichuangTest设备**:
   - 8次调用全部无响应数据
   - 可能处于调试阶段
   - 成功率: 0%

3. **sichang设备**:
   - 3次调用全部无响应数据  
   - 可能为早期测试设备
   - 成功率: 0%

### 常见错误类型
- **连接器未启动**: 设备服务未正常启动
- **通信异常**: 网络或协议问题
- **设备离线**: 硬件连接问题

## 🔧 对接建议

### 1. 错误处理策略
```javascript
// 响应处理示例
function handleSichuangResponse(response) {
  if (response._status === 500) {
    switch(response._msg) {
      case "连接器未启动！":
        // 尝试重启连接器或通知运维
        return handleConnectorNotStarted();
      case "设备离线":
        // 检查设备连接状态
        return handleDeviceOffline();
      default:
        // 记录未知错误
        return handleUnknownError(response._msg);
    }
  }
  
  // 处理成功响应
  return handleSuccessResponse(response);
}
```

### 2. 重试机制
- **重试间隔**: 30秒 - 5分钟
- **最大重试次数**: 3次
- **退避策略**: 指数退避
- **熔断机制**: 连续失败10次后暂停1小时

### 3. 监控告警
- **成功率低于50%**: 发送告警
- **连续失败超过5次**: 立即通知
- **设备长时间离线**: 每小时提醒

## 🛠️ 故障排查

### 检查清单
1. **网络连接**
   - [ ] 设备网络是否正常
   - [ ] 防火墙设置是否正确
   - [ ] 端口是否开放

2. **设备状态**
   - [ ] 设备电源是否正常
   - [ ] 连接器服务是否启动
   - [ ] 设备配置是否正确

3. **软件环境**
   - [ ] 驱动程序是否安装
   - [ ] 服务进程是否运行
   - [ ] 日志是否有异常

### 常用命令
```bash
# 检查设备连接状态
ping [设备IP]

# 检查端口连通性
telnet [设备IP] [端口]

# 重启连接器服务
systemctl restart sichuang-connector

# 查看设备日志
tail -f /var/log/sichuang/device.log
```

## 📈 优化建议

### 短期优化
1. **提升连接稳定性**
   - 优化连接器启动逻辑
   - 增加连接重试机制
   - 改善错误处理

2. **完善监控**
   - 添加设备状态监控
   - 实现自动故障恢复
   - 优化告警策略

### 长期规划
1. **架构改进**
   - 考虑连接池机制
   - 实现负载均衡
   - 增加容错能力

2. **运维自动化**
   - 自动故障检测
   - 智能重启机制
   - 预防性维护

## 🔗 相关资源

- [四川设备技术文档](./四川设备技术规格.md)
- [连接器配置指南](./连接器配置说明.md)
- [故障排查手册](./故障排查指南.md)
- [监控告警配置](./监控配置文档.md)

---

*本文档基于 sichuang、sichuangTest、sichang 设备的实际运行数据编写。由于设备目前处于开发测试阶段，成功率较低，建议重点关注连接稳定性和错误处理机制的完善。*
