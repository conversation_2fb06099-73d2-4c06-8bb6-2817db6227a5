# eleMachine 类型设备对接文档

> 电机测试设备对接详细说明  
> 设备类型: eleMachine  
> 主要设备: eleMachine51, eleMachine50  

## 📋 设备信息

| 设备ID | 调用次数 | 成功率 | 活跃期 | IP地址 | 状态 |
|--------|----------|--------|--------|--------|------|
| eleMachine51 | 7 | 100% | 2025-04-22 至今 | ************ | 🟢 生产环境 |
| eleMachine50 | 3 | 100% | 2025-04-22 | ************ | 🟡 备用设备 |

## 🔄 调用特征

- **调用方式**: 100% 异步调用
- **响应时间**: 中等，取决于试验复杂度
- **数据量**: 中等规模的试验数据
- **更新频率**: 低频，按需测试

## 📊 响应数据结构

### 成功响应格式
```json
{
  "body": "{JSON字符串格式的试验数据}"
}
```

### 试验数据字段说明

#### 基础信息
- `_deviceId`: 设备ID (如: "eleMachine51")
- `_product`: 产品类型 (固定值: "eleMachine")
- `address`: 设备IP地址 (如: "************")
- `fileName`: 数据文件名 (如: "tmp.txt")
- `readDataTime`: 数据读取时间 (如: "2025-04-22 10:41:09.809")

#### 试验参数
- `曲线ID`: 试验曲线标识符
- `批号`: 产品批次号
- `编号`: 试验编号
- `管类型`: 测试管材类型
- `生产厂家`: 制造商信息
- `生产日期`: 产品生产日期
- `试验日期`: 测试执行日期
- `试验人`: 操作人员
- `长度`: 试样长度
- `内径`: 试样内径
- `外径`: 试样外径

#### 测试结果
- `F0`: 初始力值 (N)
- `变形`: 变形量 (mm)
- `F_3`: 3%变形时的力值 (N)
- `环刚度`: 环刚度值
- `极限变形`: 极限变形量 (mm)

#### 曲线数据
- `试验力(N)`: 施加的试验力
- `变形(mm)`: 对应的变形量
- `位移(mm)`: 位移数据
- `时间(s)`: 时间戳

## 📝 典型响应示例

### 示例1: 完整试验数据
```json
{
  "body": "{
    \"_deviceId\": \"eleMachine51\",
    \"_product\": \"eleMachine\",
    \"content\": \"曲线ID:13602569\\n批号:\\n编号:1\\n管类型:\\n生产厂家:\\n生产日期:\\n试验日期:2025-04-16\\n试验人:\\n长度:\\n内径:\\n外径:\\nF0:7.500\\n变形:0.000\\nF_3:0.000\\n环刚度:0\\n极限变形:0.100\\n\\n==========================================曲线数据=========================================\\n\\n 试验力(N)\\t  变形(mm)\\t  位移(mm)\\t   时间(s)\\t          \\t          \\n\",
    \"address\": \"************\",
    \"fileName\": \"tmp.txt\",
    \"readDataTime\": \"2025-04-22 10:41:09.809\"
  }"
}
```

### 示例2: 解析后的数据结构
```javascript
{
  _deviceId: "eleMachine51",
  _product: "eleMachine",
  address: "************",
  fileName: "tmp.txt",
  readDataTime: "2025-04-22 10:41:09.809",
  content: {
    curveId: "13602569",
    batchNumber: "",
    serialNumber: "1",
    tubeType: "",
    manufacturer: "",
    productionDate: "",
    testDate: "2025-04-16",
    operator: "",
    length: "",
    innerDiameter: "",
    outerDiameter: "",
    F0: 7.500,
    deformation: 0.000,
    F_3: 0.000,
    ringStiffness: 0,
    limitDeformation: 0.100,
    curveData: [
      // 试验力、变形、位移、时间数据点
    ]
  }
}
```

## 🔧 数据解析处理

### 解析流程
```javascript
function parseEleMachineResponse(response) {
  try {
    // 1. 解析外层JSON
    const outerData = JSON.parse(response.body);
    
    // 2. 解析content字段中的试验数据
    const content = parseTestContent(outerData.content);
    
    // 3. 构建完整的数据对象
    return {
      deviceId: outerData._deviceId,
      product: outerData._product,
      address: outerData.address,
      fileName: outerData.fileName,
      readDataTime: new Date(outerData.readDataTime),
      testData: content
    };
  } catch (error) {
    console.error('解析eleMachine响应失败:', error);
    throw new Error('数据格式错误');
  }
}

function parseTestContent(contentString) {
  const lines = contentString.split('\\n');
  const result = {};
  
  // 解析基础信息
  lines.forEach(line => {
    if (line.includes('曲线ID:')) {
      result.curveId = line.split(':')[1];
    }
    if (line.includes('F0:')) {
      result.F0 = parseFloat(line.split(':')[1]);
    }
    // ... 其他字段解析
  });
  
  // 解析曲线数据
  result.curveData = parseCurveData(contentString);
  
  return result;
}
```

## ⚠️ 对接注意事项

### 1. 数据格式
- **双重JSON**: 外层JSON包含内层JSON字符串
- **转义字符**: content字段包含`\\n`等转义字符
- **文本解析**: 需要解析结构化文本数据

### 2. 设备管理
- **IP地址**: 每个设备有固定IP地址
- **设备编号**: 通过设备ID区分不同设备
- **文件管理**: 注意临时文件的清理

### 3. 数据验证
- **必要字段**: 检查设备ID、产品类型等关键字段
- **数值范围**: 验证力值、变形量等数据的合理性
- **时间格式**: 确保时间字段的正确解析

## 📈 性能优化

### 1. 缓存策略
- **设备信息缓存**: 缓存设备IP和配置信息
- **历史数据**: 缓存最近的试验结果
- **解析结果**: 缓存解析后的数据结构

### 2. 异步处理
- **数据解析**: 在后台线程进行复杂解析
- **文件处理**: 异步读取和处理数据文件
- **结果通知**: 使用回调或事件机制

## 🛠️ 故障排查

### 常见问题
1. **数据解析失败**
   - 检查JSON格式是否正确
   - 验证转义字符处理
   - 确认字段名称匹配

2. **设备连接问题**
   - 检查设备IP地址
   - 验证网络连通性
   - 确认设备服务状态

3. **数据不完整**
   - 检查试验是否完成
   - 验证数据文件完整性
   - 确认设备状态正常

### 调试工具
```bash
# 检查设备连接
ping ************

# 测试端口连通性
telnet ************ [端口号]

# 查看设备日志
ssh user@************ "tail -f /var/log/elemachine.log"
```

## 🔗 相关文档

- [电机测试设备操作手册](./电机设备操作指南.md)
- [试验数据格式规范](./试验数据标准.md)
- [设备维护指南](./设备维护手册.md)

---

*本文档基于 eleMachine51 和 eleMachine50 设备的实际运行数据编写。这类设备运行稳定，成功率100%，主要用于材料力学性能测试。*
