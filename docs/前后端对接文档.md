# 标准检测业务系统前后端对接文档

## 📋 系统概述

标准检测业务系统主要包含以下功能模块：

1. **实验项目管理** - 管理实验项目的基本信息、关联仪器等
2. **参数管理** - 管理实验项目的参数，包括仪器可检参数、鉴别参数等
3. **规则模型管理** - 配置判定规则，用于检测数据验证
4. **标准管理** - 管理标准信息，关联实验项目
5. **数据验证** - 基于规则模型进行数据验证

## 🖥️ 前端开发内容

前端需要开发以下页面和组件：

### 1. 实验项目管理页面

- 实验项目列表展示（分页、查询）
- 实验项目新增/编辑/删除功能
- 实验项目与仪器关联功能
- 根据关联仪器自动生成参数功能

### 2. 项目参数管理页面

- 参数列表展示（按项目ID筛选）
- 参数新增/编辑/删除功能
- 参数类型区分展示（仪器可检参数、鉴别参数等）

### 3. 规则模型配置页面

- 规则模型列表展示（按标准ID筛选）
- 规则模型新增/编辑/删除功能
- 判定公式编辑器组件（支持公式输入和验证）
- 判定类型选择组件

### 4. 标准管理页面更新

- 标准与实验项目关联功能
- 关联实验项目列表展示
- 标准详情中展示关联的规则模型

### 5. 规则验证页面

- 参数输入表单
- 公式计算功能
- 数据验证结果展示

### 6. 设备列表页面更新

- 设备列表中新增"在线状态"列，显示设备是否在线
- 针对在线状态的设备，提供"获取数据"按钮
- 点击"获取数据"按钮时，调用获取设备检测数据接口
- 获取到的数据以合适的方式展示（如表格或详情面板）

### 7. 设备数据展示组件

- 创建设备检测数据展示组件
- 支持查看检测结果
- 支持查看/下载关联的文件
- 提供数据刷新功能

## 🔌 API接口说明

### 1. 实验项目管理接口

#### 1.1 获取实验项目分页数据

- **URL**: `/experimentProject/page`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "pageNum": 1,
    "pageSize": 10,
    "projectName": "项目名称",
    "projectCode": "项目编码"
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "records": [
        {
          "id": 1,
          "projectName": "项目名称",
          "projectCode": "项目编码",
          "projectDesc": "项目描述",
          "instrumentIds": "[1, 2, 3]",
          "createTime": "2024-03-25 10:00:00",
          "updateTime": "2024-03-25 10:00:00"
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1
    }
  }
  ```

#### 1.2 获取实验项目详情

- **URL**: `/experimentProject/get`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "projectName": "项目名称",
      "projectCode": "项目编码",
      "projectDesc": "项目描述",
      "instrumentIds": "[1, 2, 3]",
      "createTime": "2024-03-25 10:00:00",
      "updateTime": "2024-03-25 10:00:00"
    }
  }
  ```

#### 1.3 新增实验项目

- **URL**: `/experimentProject/add`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "projectName": "项目名称",
    "projectCode": "项目编码",
    "projectDesc": "项目描述"
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 1.4 更新实验项目

- **URL**: `/experimentProject/update`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1,
    "projectName": "项目名称",
    "projectCode": "项目编码",
    "projectDesc": "项目描述"
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 1.5 删除实验项目

- **URL**: `/experimentProject/delete`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": "1,2,3"
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 1.6 获取实验项目关联的仪器列表

- **URL**: `/experimentProject/getInstruments`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "sbmc": "仪器名称1",
        "sbxh": "仪器型号1"
      },
      {
        "id": 2,
        "sbmc": "仪器名称2",
        "sbxh": "仪器型号2"
      }
    ]
  }
  ```

#### 1.7 关联仪器到实验项目

- **URL**: `/experimentProject/associateInstruments`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "projectId": 1,
    "instrumentIds": [1, 2, 3]
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 1.8 根据仪器生成项目参数

- **URL**: `/experimentProject/generateParams`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "projectId": 1,
    "instrumentIds": [1, 2, 3]
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 2. 实验项目参数管理接口

#### 2.1 获取项目参数列表

- **URL**: `/experimentProjectParam/listByProjectId`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "projectId": 1,
        "paramName": "参数名称",
        "paramNameEn": "param_name",
        "paramUnit": "单位",
        "paramType": 1,
        "instrumentId": 1
      }
    ]
  }
  ```

#### 2.2 新增项目参数

- **URL**: `/experimentProjectParam/add`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "projectId": 1,
    "paramName": "参数名称",
    "paramNameEn": "param_name",
    "paramUnit": "单位",
    "paramType": 1,
    "instrumentId": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 2.3 更新项目参数

- **URL**: `/experimentProjectParam/update`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1,
    "projectId": 1,
    "paramName": "参数名称",
    "paramNameEn": "param_name",
    "paramUnit": "单位",
    "paramType": 1,
    "instrumentId": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 2.4 删除项目参数

- **URL**: `/experimentProjectParam/delete`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 3. 规则模型管理接口

#### 3.1 获取规则模型列表

- **URL**: `/ruleModel/listByStandardId`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "standardId": 1,
        "modelName": "规则模型名称",
        "judgmentType": 1,
        "judgmentFormula": "x1 > 200",
        "qualifiedStandard": "大于200",
        "createTime": "2024-03-25 10:00:00",
        "updateTime": "2024-03-25 10:00:00"
      }
    ]
  }
  ```

#### 3.2 获取规则模型详情

- **URL**: `/ruleModel/get`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "standardId": 1,
      "modelName": "规则模型名称",
      "judgmentType": 1,
      "judgmentFormula": "x1 > 200",
      "qualifiedStandard": "大于200",
      "createTime": "2024-03-25 10:00:00",
      "updateTime": "2024-03-25 10:00:00"
    }
  }
  ```

#### 3.3 新增规则模型

- **URL**: `/ruleModel/add`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "standardId": 1,
    "modelName": "规则模型名称",
    "judgmentType": 1,
    "judgmentFormula": "x1 > 200",
    "qualifiedStandard": "大于200"
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 3.4 更新规则模型

- **URL**: `/ruleModel/update`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1,
    "standardId": 1,
    "modelName": "规则模型名称",
    "judgmentType": 1,
    "judgmentFormula": "x1 > 200",
    "qualifiedStandard": "大于200"
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 3.5 删除规则模型

- **URL**: `/ruleModel/delete`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 4. 标准与实验项目关联接口

#### 4.1 获取标准关联的实验项目

- **URL**: `/standardBasicInstrumentInfo/getExperimentProjects`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "projectName": "项目名称1",
        "projectCode": "项目编码1"
      },
      {
        "id": 2,
        "projectName": "项目名称2",
        "projectCode": "项目编码2"
      }
    ]
  }
  ```

#### 4.2 关联实验项目到标准

- **URL**: `/standardBasicInstrumentInfo/associateProjects`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "standardId": 1,
    "projectIds": [1, 2, 3]
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 5. 规则验证接口

#### 5.1 计算公式结果

- **URL**: `/ruleValidation/calculate`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "formula": "x1 + x2",
    "paramValues": {
      "x1": 100,
      "x2": 200
    }
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": 300
  }
  ```

#### 5.2 验证数据是否符合规则

- **URL**: `/ruleValidation/validate`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "ruleModelId": 1,
    "paramValues": {
      "x1": 100,
      "x2": 200
    }
  }
  ```
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": true
  }
  ```

### 6. 设备管理接口

#### 6.1 获取设备检测数据

- **URL**: `/instrumentNewInfo/getDeviceTestData`
- **方法**: POST
- **功能描述**: 针对在线状态的设备，获取最近的检测数据
- **请求参数**:
  ```json
  {
    "equipmentId": 1,
    "parameterIds": [1, 2, 3]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | equipmentId | Long | 是 | 设备ID |
  | parameterIds | List<Long> | 是 | 参数ID列表 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "result": "检测结果1",
        "fileId": 101,
        "fileInfo": {
          "id": 101,
          "fileName": "测试文件1.xlsx",
          "filePath": "/path/to/file1"
        }
      },
      {
        "id": 2,
        "result": "检测结果2",
        "fileId": 102,
        "fileInfo": {
          "id": 102,
          "fileName": "测试文件2.xlsx",
          "filePath": "/path/to/file2"
        }
      }
    ]
  }
  ```
  | 参数名 | 类型 | 说明 |
  | --- | --- | --- |
  | id | Long | 数据ID |
  | result | String | 检测结果 |
  | fileId | Long | 关联文件ID |
  | fileInfo | Object | 文件信息对象 |

## 📊 数据模型

### 1. 实验项目(ExperimentProject)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| projectName | String | 项目名称 |
| projectCode | String | 项目编码 |
| projectDesc | String | 项目描述 |
| instrumentIds | String | 关联仪器ID列表(JSON数组字符串) |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |
| createBy | String | 创建人 |
| updateBy | String | 更新人 |

### 2. 实验项目参数(ExperimentProjectParam)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| projectId | Long | 项目ID |
| paramName | String | 参数名称 |
| paramNameEn | String | 参数英文名称 |
| paramUnit | String | 参数单位 |
| paramType | Integer | 参数类型(1-仪器可检参数，2-鉴别参数) |
| instrumentId | Long | 关联仪器ID |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |

### 3. 规则模型(RuleModel)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| standardId | Long | 标准ID |
| modelName | String | 模型名称 |
| judgmentType | Integer | 判别类型(1-比较型，2-计算比较依据型，3-计算比较结果型，4-不做判定型，5-人工判定型，6-其他型) |
| judgmentFormula | String | 判定公式 |
| qualifiedStandard | String | 合格标准 |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |
| createBy | String | 创建人 |
| updateBy | String | 更新人 |

### 4. 标准基本信息(StandardBasicInstrumentInfo)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| standardCode | String | 标准编号 |
| standardName | String | 标准名称 |
| experimentProjectIds | String | 关联实验项目ID列表(JSON数组字符串) |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |
| createBy | String | 创建人 |
| updateBy | String | 更新人 |

### 5. 设备检测数据(AutoResultDto)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| result | String | 检测结果 |
| fileId | Long | 关联文件ID |
| fileInfo | FileInfo | 文件信息对象 |

## 🔄 主要交互流程

### 1. 实验项目管理流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取实验项目列表
    后端-->>前端: 返回实验项目数据
    
    前端->>后端: 创建/编辑实验项目
    后端-->>前端: 返回操作结果
    
    前端->>后端: 关联仪器到实验项目
    后端-->>前端: 返回操作结果
    
    前端->>后端: 根据仪器生成参数
    后端-->>前端: 返回操作结果
```

### 2. 参数管理流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取项目参数列表
    后端-->>前端: 返回参数数据
    
    前端->>后端: 创建/编辑/删除参数
    后端-->>前端: 返回操作结果
```

### 3. 规则模型管理流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取规则模型列表
    后端-->>前端: 返回规则模型数据
    
    前端->>后端: 创建/编辑规则模型
    后端-->>前端: 返回操作结果
    
    前端->>后端: 规则验证
    后端-->>前端: 返回验证结果
```

### 4. 标准与实验项目关联流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取标准关联的实验项目
    后端-->>前端: 返回关联的实验项目
    
    前端->>后端: 关联实验项目到标准
    后端-->>前端: 返回操作结果
```

### 5. 设备检测数据获取流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取设备列表（包含在线状态）
    后端-->>前端: 返回设备列表数据
    
    前端->>后端: 点击"获取数据"按钮，请求设备检测数据
    后端-->>前端: 返回设备检测数据
    
    前端->>前端: 展示检测数据和关联文件
```

## 📝 注意事项

1. 所有POST请求的Content-Type均为`application/json`
2. 所有响应均采用统一的IResult格式包装
3. 参数值类型需要严格按照API定义传递
4. 判定公式语法需符合系统规定，支持基本的算术运算、比较操作符和特殊符号
5. 开发过程中注意处理数据类型转换和空值判断
6. 与后端对接时注意处理错误情况和提示信息展示