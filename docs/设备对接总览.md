# 设备对接总览文档

> 基于 zzzzz_log_device_function_call 表数据分析生成  
> 生成时间: 2025-06-14  
> 数据统计时间范围: 2025-03-21 至 2025-06-13  

## 📊 数据概览

- **总记录数**: 344条
- **设备总数**: 12个唯一设备
- **产品类型**: 8种
- **异步调用比例**: 95.6%
- **有效响应比例**: 78.2%

## 🏷️ 设备清单

| 设备ID | 产品类型 | 调用次数 | 首次调用 | 最后调用 | 异步调用 | 有效响应 | 状态 |
|--------|----------|----------|----------|----------|----------|----------|------|
| wanceDevice | accessId | 230 | 2025-04-20 | 2025-06-13 | 230 | 225 | 🟢 活跃 |
| sichuang | sichuangDevice | 60 | 2025-04-18 | 2025-06-13 | 60 | 34 | 🟢 活跃 |
| accessDataList | accessId | 11 | 2025-04-18 | 2025-04-18 | 11 | 5 | 🟡 测试 |
| sichuangTest | sichuangDevice | 8 | 2025-04-18 | 2025-04-18 | 8 | 0 | 🔴 测试 |
| d1902260351119826944 | streetlight | 8 | 2025-03-21 | 2025-03-21 | 0 | 0 | 🔴 停用 |
| eleMachine51 | eleMachine | 7 | 2025-04-22 | 2025-06-13 | 7 | 7 | 🟢 活跃 |
| jufangDevice | jufang | 6 | 2025-05-10 | 2025-05-10 | 4 | 1 | 🟡 测试 |
| accessDeviceList170 | wanceTransformer | 5 | 2025-06-12 | 2025-06-12 | 0 | 1 | 🟡 新增 |
| eleMachine50 | eleMachine | 3 | 2025-04-22 | 2025-04-22 | 3 | 3 | 🟡 备用 |
| sichang | sichuangDevice | 3 | 2025-04-18 | 2025-04-18 | 3 | 0 | 🔴 测试 |
| jyaDevice | jyzjdph | 2 | 2025-04-22 | 2025-04-22 | 2 | 2 | 🟡 测试 |
| wkDevice | wkProduct | 1 | 2025-06-13 | 2025-06-13 | 1 | 1 | 🟡 新增 |

## 📈 产品类型统计

### 1. accessId (241条记录, 70.1%)
- **主要设备**: wanceDevice (万测设备)
- **特点**: 高频调用，复杂的电阻测量数据
- **响应格式**: JSON数组，包含多组测试数据
- **状态**: 生产环境主力设备

### 2. sichuangDevice (71条记录, 20.6%)
- **主要设备**: sichuang (四川设备)
- **特点**: 中频调用，部分响应为错误状态
- **响应格式**: 简单状态信息或错误消息
- **状态**: 测试和开发阶段

### 3. eleMachine (10条记录, 2.9%)
- **设备**: eleMachine51, eleMachine50
- **特点**: 电机测试设备，结构化数据
- **响应格式**: 包含试验数据和设备信息
- **状态**: 稳定运行

### 4. 其他类型 (22条记录, 6.4%)
- **jufang**: 局放试验设备，详细试验报告
- **streetlight**: 路灯设备，同步调用
- **wanceTransformer**: 万测变压器，新增设备
- **jyzjdph**: 检测设备，少量测试
- **wkProduct**: 新产品，单次测试

## 🔄 调用模式分析

### 异步调用 (329条, 95.6%)
- 主要用于数据采集和长时间处理
- 设备类型: accessId, sichuangDevice, eleMachine, jufang, jyzjdph, wkProduct

### 同步调用 (15条, 4.4%)
- 主要用于状态查询和简单操作
- 设备类型: streetlight, wanceTransformer

## 📅 时间分布特征

### 活跃期分析
- **2025-04-18 至 2025-04-23**: 集中测试期，多设备同时调试
- **2025-05-07 至 2025-05-10**: 高峰期，单日最高79条记录
- **2025-06-10 至 2025-06-13**: 近期活跃，持续数据采集

### 设备生命周期
- **新增设备**: wanceTransformer, wkDevice (6月新增)
- **稳定设备**: wanceDevice, sichuang, eleMachine51
- **测试设备**: sichuangTest, accessDataList, jufangDevice
- **停用设备**: streetlight (3月后无活动)

## ⚠️ 异常情况统计

### 错误响应类型
1. **连接器未连接** - 主要出现在 wanceDevice
2. **连接器未启动** - 主要出现在 sichuangDevice
3. **空响应** - 部分测试设备

### 成功率分析
- **wanceDevice**: 97.8% (225/230)
- **eleMachine**: 100% (10/10)
- **sichuang**: 56.7% (34/60)
- **其他设备**: 变化较大

## 🔗 相关文档

- [设备对接-accessId类型.md](./设备对接-accessId类型.md) - 万测设备详细对接文档
- [设备对接-sichuangDevice类型.md](./设备对接-sichuangDevice类型.md) - 四川设备对接文档
- [设备对接-eleMachine类型.md](./设备对接-eleMachine类型.md) - 电机设备对接文档
- [设备对接-其他类型.md](./设备对接-其他类型.md) - 其他设备类型对接文档

## 📝 对接注意事项

1. **异步调用为主**: 95.6%的调用为异步，需要实现异步处理机制
2. **错误处理**: 统一的错误响应格式 `{"_msg": "错误信息", "_status": 500}`
3. **数据格式多样**: 不同设备类型返回的数据结构差异较大
4. **连接稳定性**: 部分设备存在连接不稳定的情况
5. **测试环境**: 多个测试设备，注意区分生产和测试环境

---

*本文档基于实际运行数据生成，反映了当前设备对接的真实状况。如需了解具体设备的详细对接信息，请参考对应的分类文档。*
