# 设备对接数据管理功能说明

## 📋 功能概述

设备对接数据管理功能提供了一个统一的接口来查询和管理设备试验数据。该功能支持根据设备代码和时间范围查询设备的试验数据，并将数据按照预定义的映射规则转换为标准格式返回。

## 🏗️ 架构设计

### 核心组件

1. **DeviceDataConfig** - 设备数据配置实体，存储设备的响应体JSON配置
2. **DeviceDataService** - 设备数据服务，提供数据查询和转换功能
3. **DeviceDataController** - REST API控制器，提供HTTP接口
4. **DeviceDataQueryMapper** - 数据查询Mapper，支持动态表查询

### 数据库表结构

#### bu_device_data_config (设备对接数据管理配置表)
- `id` - 主键ID
- `device_code` - 设备代码（唯一）
- `device_name` - 设备名称
- `response_body` - 设备返回体JSON配置
- `status` - 状态(enabled/disabled)
- `description` - 描述
- `create_time` - 创建时间
- `update_time` - 更新时间

## 📊 配置格式

设备响应体配置采用JSON格式，包含多个试验配置：

```json
{
  "experiments": [
    {
      "name": "直阻试验",
      "code": "DTS_T_RZZLDZCL",
      "tableName": "DTS_T_RZZLDZCL",
      "mapping": {
        "高压1档R1": "r1a",
        "高压1档R2": "r1b",
        "高压1档R3": "r1c",
        "试验温度": "temp"
      }
    }
  ]
}
```

### 配置字段说明

- `name` - 试验名称（显示用）
- `code` - 试验代码（标识用）
- `tableName` - 数据表名
- `mapping` - 字段映射配置
  - key: 显示名称
  - value: 数据库字段名

## 🔌 API接口

### 1. 查询设备数据 (GET)

**接口地址**: `GET /device-data/query`

**请求参数**:
- `deviceCode` (string, required) - 设备代码
- `startTime` (datetime, required) - 开始时间，格式: yyyy-MM-dd HH:mm:ss
- `endTime` (datetime, required) - 结束时间，格式: yyyy-MM-dd HH:mm:ss

**示例请求**:
```bash
curl -X GET "http://localhost:7321/test/device-data/query?deviceCode=wanceDevice&startTime=2025-01-01 00:00:00&endTime=2025-12-31 23:59:59"
```

### 2. 查询设备数据 (POST)

**接口地址**: `POST /device-data/query`

**请求体**:
```json
{
  "deviceCode": "wanceDevice",
  "startTime": "2025-01-01T00:00:00",
  "endTime": "2025-12-31T23:59:59"
}
```

### 3. 查询设备配置

**接口地址**: `GET /device-data/config/{deviceCode}`

**路径参数**:
- `deviceCode` (string, required) - 设备代码

## 📤 响应格式

### 成功响应

```json
{
  "success": true,
  "message": "查询成功",
  "count": 4,
  "data": [
    {
      "name": "直阻试验",
      "code": "DTS_T_RZZLDZCL",
      "result": [
        {
          "name": "高压1档R1",
          "code": "r1a",
          "value": "11.0800000"
        },
        {
          "name": "高压1档R2",
          "code": "r1b",
          "value": "11.1200000"
        }
      ]
    }
  ]
}
```

### 错误响应

```json
{
  "success": false,
  "message": "未找到设备配置，设备代码: nonexistent",
  "data": null
}
```

## 🧪 测试方法

### 1. 使用测试脚本

```bash
chmod +x test_device_data_api.sh
./test_device_data_api.sh
```

### 2. 手动测试

使用Postman或curl工具调用API接口进行测试。

### 3. 单元测试

```bash
mvn test -Dtest=DeviceDataServiceTest
```

## 🔧 配置管理

### 添加新设备配置

```sql
INSERT INTO `bu_device_data_config` (`device_code`, `device_name`, `response_body`, `status`, `description`)
VALUES ('newDevice', '新设备', '{配置JSON}', 'enabled', '新设备配置');
```

### 更新设备配置

```sql
UPDATE `bu_device_data_config` 
SET `response_body` = '{新配置JSON}', `update_time` = NOW()
WHERE `device_code` = 'deviceCode';
```

## ⚠️ 注意事项

1. **数据表存在性检查**: 系统会自动检查配置中指定的数据表是否存在
2. **时间字段**: 查询时使用`testdate`字段进行时间范围过滤
3. **数据排序**: 查询结果按`testdate`降序排列，返回最新的一条记录
4. **异常处理**: 完善的异常处理机制，确保系统稳定性
5. **日志记录**: 详细的日志记录，便于问题排查

## 🚀 扩展功能

1. **支持多条记录查询**: 可以修改查询逻辑支持返回多条记录
2. **支持分页查询**: 添加分页参数支持大数据量查询
3. **支持数据缓存**: 添加Redis缓存提高查询性能
4. **支持数据导出**: 添加Excel导出功能
5. **支持实时数据**: 集成WebSocket支持实时数据推送

## 📝 更新日志

- **2025-06-14**: 初始版本发布
  - 实现基础的设备数据查询功能
  - 支持万测变压器四种试验类型
  - 完善的异常处理和日志记录
