# 其他类型设备对接文档

> 包含 jufang、streetlight、wanceTransformer、jyzjdph、wkProduct 等设备类型  
> 涵盖局放试验、路灯控制、变压器、检测设备等多种应用场景  

## 📋 设备概览

| 设备ID | 产品类型 | 调用次数 | 成功率 | 调用方式 | 状态 |
|--------|----------|----------|--------|----------|------|
| jufangDevice | jufang | 6 | 16.7% | 异步 | 🟡 测试中 |
| d1902260351119826944 | streetlight | 8 | 0% | 同步 | 🔴 停用 |
| accessDeviceList170 | wanceTransformer | 5 | 20% | 同步 | 🟡 新增 |
| jyaDevice | jyzjdph | 2 | 100% | 异步 | 🟢 稳定 |
| wkDevice | wkProduct | 1 | 100% | 异步 | 🟡 新增 |

---

## 🔬 jufang - 局放试验设备

### 设备信息
- **设备ID**: jufangDevice
- **调用次数**: 6次
- **成功率**: 16.7% (1/6)
- **活跃期**: 2025-05-10
- **调用方式**: 异步

### 响应数据结构
```json
{
  "body": "详细的局放试验记录文本"
}
```

### 典型响应示例
```json
{
  "body": " \n  局  放  试  验  记  录\n ------------------------------------------------------------------------------------------ \n\n试验名称：10.1kv\n产品型号：400\n试验人员：xu\n试验日期：2021.01.11\n试验频率：50 Hz\n\n ------------------------------------------------------------------------------------------ \n\n        校准pc,低频kHz,高频kHz,同步频率Hz,同步方式\n通道1:    10,     40,   300,    50,  内同步\n通道2:    10,     40,   300,    50,  内同步\n通道3:    10,     40,   300,    50,  内同步\n通道4:  没有校准，不输出无意义的数据！\n\n ------------------------------------------------------------------------------------------ 次数，时 间 ,通道1放电/校准(pC), 放电(次/s)，放电能量(J),平均电流(A),均方率(C\n2\n/s),通道说明,电压kV, dB值 , uV 值, mV 值 01, 15:22:19, 10.1/ 10, 0,0.0000E+000,3.5424E-008,1.2404E-019, Ah , 0.00kV, 0.00dB, 0.00uV, 265.00mV \n..."
}
```

### 数据字段说明
- **试验基础信息**: 试验名称、产品型号、试验人员、日期、频率
- **通道配置**: 校准值、频率范围、同步方式
- **测试数据**: 时间、放电量、电流、电压等详细测量数据
- **波形信息**: 各通道的波形文件引用

### 对接注意事项
- 数据为纯文本格式，需要文本解析
- 包含大量测试数据，建议分段处理
- 注意中文字符编码问题

---

## 💡 streetlight - 路灯控制设备

### 设备信息
- **设备ID**: d1902260351119826944
- **调用次数**: 8次
- **成功率**: 0%
- **活跃期**: 2025-03-21 (已停用)
- **调用方式**: 同步

### 当前状态
- 所有调用均无响应数据
- 可能已停用或迁移
- 建议确认设备状态

### 预期响应格式
```json
{
  "status": "success",
  "data": {
    "lightId": "d1902260351119826944",
    "state": "on|off",
    "brightness": 80,
    "lastUpdate": "2025-03-21T10:00:00Z"
  }
}
```

---

## ⚡ wanceTransformer - 万测变压器

### 设备信息
- **设备ID**: accessDeviceList170
- **调用次数**: 5次
- **成功率**: 20% (1/5)
- **活跃期**: 2025-06-12 (新增设备)
- **调用方式**: 同步

### 响应数据结构
```json
{
  "transformerId": "accessDeviceList170",
  "voltage": {
    "primary": 10000,
    "secondary": 400
  },
  "current": {
    "primary": 1.5,
    "secondary": 37.5
  },
  "temperature": 45.2,
  "status": "normal"
}
```

### 对接建议
- 新增设备，需要稳定性观察
- 同步调用，响应时间较快
- 建议增加错误处理机制

---

## 🔍 jyzjdph - 检测设备

### 设备信息
- **设备ID**: jyaDevice
- **调用次数**: 2次
- **成功率**: 100%
- **活跃期**: 2025-04-22
- **调用方式**: 异步

### 响应特征
- 小规模测试，成功率高
- 异步调用，适合长时间检测
- 数据格式待进一步确认

### 预期应用场景
- 质量检测
- 性能测试
- 数据采集

---

## 🆕 wkProduct - 新产品设备

### 设备信息
- **设备ID**: wkDevice
- **调用次数**: 1次
- **成功率**: 100%
- **活跃期**: 2025-06-13 (最新)
- **调用方式**: 异步

### 当前状态
- 新增设备，单次测试成功
- 需要更多数据确认稳定性
- 建议持续监控

---

## 🔧 通用对接建议

### 1. 错误处理策略
```javascript
function handleOtherDeviceResponse(deviceType, response) {
  switch(deviceType) {
    case 'jufang':
      return parseJufangTextData(response.body);
    case 'streetlight':
      return handleStreetlightResponse(response);
    case 'wanceTransformer':
      return parseTransformerData(response);
    case 'jyzjdph':
      return handleDetectionData(response);
    case 'wkProduct':
      return handleNewProductData(response);
    default:
      throw new Error(`未知设备类型: ${deviceType}`);
  }
}
```

### 2. 监控策略
- **新设备**: 密切监控前期稳定性
- **停用设备**: 定期检查是否需要重新激活
- **测试设备**: 关注成功率变化趋势

### 3. 数据处理
- **文本数据**: 实现专门的文本解析器
- **同步调用**: 设置合理的超时时间
- **异步调用**: 实现状态查询机制

## ⚠️ 特别注意事项

### jufang设备
- 数据量大，建议流式处理
- 文本格式复杂，需要专门解析器
- 中文编码问题需要特别处理

### streetlight设备
- 当前无响应，建议暂停调用
- 如需重新启用，先确认设备状态
- 同步调用，注意超时设置

### 新增设备 (wanceTransformer, wkProduct)
- 持续监控稳定性
- 建立基线性能指标
- 及时发现和解决问题

## 📊 性能指标

### 目标指标
- **成功率**: >90%
- **响应时间**: 同步<5s, 异步<30s
- **可用性**: >99%

### 当前状态
- **需要改进**: jufang, streetlight, wanceTransformer
- **表现良好**: jyzjdph, wkProduct
- **需要观察**: 所有新增设备

---

*本文档涵盖了多种不同类型的设备，由于设备特性差异较大，建议针对每种设备类型制定专门的对接策略和监控方案。*
