# 设备数据弹窗功能优化测试说明

## 📋 功能优化概述

本次优化主要实现了以下功能：

1. **弹窗初始化优化**：弹窗打开时只显示实验框架结构，测试值显示为"暂无数据"
2. **刷新数据功能**：用户点击"刷新数据"按钮时，调用设备对接数据管理API获取实际测试数据
3. **API接口优化**：设备对接数据管理API的时间参数变为可选，未传入时自动查询最新数据

## 🧪 测试步骤

### 1. 后端API测试

#### 1.1 运行自动化测试脚本

```bash
# 给脚本执行权限
chmod +x test_device_data_optimization.sh

# 运行测试脚本
./test_device_data_optimization.sh
```

#### 1.2 手动API测试

**测试设备对接数据管理API（不传时间参数）：**

```bash
curl -X POST "http://localhost:7321/test/device-data/query" \
  -H "Content-Type: application/json" \
  -d '{"deviceCode": "wanceDevice"}'
```

**期望结果：**
- HTTP状态码：200
- 返回格式：
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "name": "直阻试验",
      "code": "DTS_T_RZZLDZCL",
      "result": [
        {
          "name": "高压1档R1",
          "code": "r1a",
          "value": "0.123"
        }
      ]
    }
  ],
  "count": 4
}
```

### 2. 前端功能测试

#### 2.1 弹窗初始化测试

**测试步骤：**
1. 打开设备管理页面
2. 找到一个设备记录，点击"查看测试数据"按钮
3. 观察弹窗初始状态

**期望结果：**
- 弹窗正常打开
- 显示实验标签页（如：直阻试验、变比试验等）
- 表格显示参数名称和映射关系
- 所有测试值显示为"暂无数据"
- 不会自动调用API获取实际数据

#### 2.2 刷新数据功能测试

**测试步骤：**
1. 在弹窗中选择一个实验标签页
2. 点击"刷新数据"按钮
3. 观察数据加载过程和结果

**期望结果：**
- 按钮显示加载状态
- 调用设备对接数据管理API
- 成功获取数据后，表格中的测试值被实际数据替换
- 显示成功提示消息

#### 2.3 多实验标签测试

**测试步骤：**
1. 切换不同的实验标签页
2. 分别点击"刷新数据"按钮
3. 验证每个实验的数据独立加载

**期望结果：**
- 每个实验标签页的数据独立管理
- 切换标签页不会自动加载数据
- 每个实验的数据映射正确

### 3. 错误处理测试

#### 3.1 设备代码不存在测试

**测试步骤：**
1. 修改设备的deviceTypeCode为不存在的值
2. 打开弹窗并点击刷新数据

**期望结果：**
- 显示友好的错误提示
- 不会导致页面崩溃

#### 3.2 网络错误测试

**测试步骤：**
1. 断开网络连接或停止后端服务
2. 点击刷新数据按钮

**期望结果：**
- 显示网络错误提示
- 按钮恢复正常状态

## 🔍 调试信息

### 浏览器控制台日志

优化后的前端代码会输出详细的调试日志：

```javascript
🔍 开始获取测试数据，实验名称: 直阻试验, 设备类型代码: wanceDevice
📡 API响应数据: {success: true, data: [...]}
📊 解析到4个实验数据: [...]
✅ 找到实验数据: {...}
```

### 后端日志

后端服务会输出相关的处理日志：

```
🔍 开始查询设备数据，设备代码: wanceDevice, 时间范围: null - null
⏰ 时间参数为空，设置默认时间范围查询最新数据
✅ 设备数据查询完成，设备代码: wanceDevice, 返回试验数量: 4
```

## 📊 性能测试

### 响应时间测试

**测试方法：**
1. 使用浏览器开发者工具的Network面板
2. 记录API调用的响应时间
3. 多次测试取平均值

**期望指标：**
- API响应时间 < 2秒
- 前端渲染时间 < 500ms

### 并发测试

**测试方法：**
1. 同时打开多个设备的测试数据弹窗
2. 同时点击刷新数据按钮
3. 观察系统稳定性

**期望结果：**
- 系统保持稳定
- 每个弹窗的数据正确加载
- 无数据混乱现象

## ✅ 验收标准

### 功能验收

- [ ] 弹窗初始化时只显示框架结构，不显示实际数据
- [ ] 点击刷新数据按钮能正确获取并显示实际测试数据
- [ ] 多个实验标签页的数据独立管理
- [ ] 错误情况下有友好的提示信息

### 性能验收

- [ ] API响应时间在可接受范围内
- [ ] 前端渲染流畅，无明显卡顿
- [ ] 内存使用正常，无内存泄漏

### 兼容性验收

- [ ] 在主流浏览器中正常工作
- [ ] 移动端适配良好
- [ ] 与现有功能无冲突

## 🐛 常见问题排查

### 1. 弹窗显示"没有可用的实验数据配置"

**可能原因：**
- 设备的device_type_code字段为空
- bu_device_data_config表中没有对应的配置

**解决方法：**
1. 检查设备表中的device_type_code字段
2. 确认bu_device_data_config表中有对应的配置记录

### 2. 点击刷新数据没有反应

**可能原因：**
- 设备的deviceTypeCode字段未正确传递
- API接口返回格式不符合预期

**解决方法：**
1. 检查浏览器控制台的错误信息
2. 查看网络请求的详细信息
3. 确认后端API的返回格式

### 3. 数据映射不正确

**可能原因：**
- 配置中的mapping字段格式错误
- 前端解析逻辑有问题

**解决方法：**
1. 检查bu_device_data_config表中的response_body配置
2. 确认mapping字段的格式正确
3. 查看前端调试日志

## 📝 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

功能测试结果：
□ 弹窗初始化 - 通过/失败
□ 刷新数据功能 - 通过/失败
□ 多标签页测试 - 通过/失败
□ 错误处理测试 - 通过/失败

性能测试结果：
- API平均响应时间：____ms
- 前端渲染时间：____ms

发现的问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```
