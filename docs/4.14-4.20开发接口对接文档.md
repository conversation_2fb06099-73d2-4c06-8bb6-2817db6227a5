# 4.14-4.20开发接口对接文档

## 📋 系统功能概述

本文档详细说明2025年4月14日至4月20日开发周期内的新增功能及接口对接要求。

## 🖥️ 前端开发内容概览

本次开发涉及以下功能模块：

1. **设备管理功能增强** 
   - 新增在线状态及数据获取功能
   - 设备新增/编辑支持选择多个实验人员，顺序具有优先级
2. **实验项目管理** - 新增二级菜单/模块，用于管理被试品所进行的实验项目
3. **实验标准管理** - 原先标准管理下的列表管理页，增强规则模型功能
4. **其他功能模块** - 待补充

## 🔌 API接口详细说明

### 1. 设备管理模块

#### 1.1 获取设备检测数据

- **URL**: `/instrumentNewInfo/getDeviceTestData`
- **方法**: POST
- **功能描述**: 针对在线状态的设备，获取最近的检测数据
- **原需求描述**: 功能点 ：设备管理新增在线状态，针对在线的设备有获取数据按钮，可以获取最近的数据
- **备注**: 这里类似工单检毕传入每个项目的参数ID（但是这里实际没有id，前端可以造一个id传入到后台，以为这里的功能只是查看设备的实时数据，并不会存入数据库，这个id只是为了前端方便回显）
- **请求参数**:
  ```json
  {
    "equipmentId": 1,
    "parameterIds": [1, 2, 3]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | equipmentId | Long | 是 | 设备ID |
  | parameterIds | List<Long> | 是 | 参数ID列表 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "result": "检测结果1",
        "fileId": 101,
        "fileInfo": {
          "id": 101,
          "fileName": "测试文件1.xlsx",
          "filePath": "/path/to/file1"
        }
      },
      {
        "id": 2,
        "result": "检测结果2",
        "fileId": 102,
        "fileInfo": {
          "id": 102,
          "fileName": "测试文件2.xlsx",
          "filePath": "/path/to/file2"
        }
      }
    ]
  }
  ```
  | 参数名 | 类型 | 说明 |
  | --- | --- | --- |
  | id | Long | 数据ID |
  | result | String | 检测结果 |
  | fileId | Long | 关联文件ID |
  | fileInfo | Object | 文件信息对象 |

#### 1.2 设备新增/编辑功能增强

- **功能描述**: 设备新增和编辑时支持选择多个实验人员，顺序具有优先级
- **注意**: <span style="color:red">返回参数vo之前是单个testUsers，现在是testUserIds和testUserFullNames</span>
- **原需求描述**: 功能点： 编辑时可以选择1个或多个实验人员（顺序具有优先级）
- **相关接口**:

##### 1.2.1 新增设备

- **URL**: `/instrumentNewInfo/add`
- **方法**: POST
- **功能描述**: 新增设备信息，支持关联多个实验人员
- **请求参数**:
  ```json
  {
    "sbbh": "SB2025001",
    "sbmc": "气相色谱仪",
    "sblb": "分析仪器",
    "scc": "某仪器制造商",
    "professionalType": "化学分析",
    "equipmentAdmin": "张三",
    "commissionDate": "2025-04-01",
    "statusEquipmentUsage": "正常",
    "internetThingId": 1001,
    "workstationId": 2001,
    "parametersInspectedEquipment": "温度,压力,浓度",
    "collectionMethod": "直采",
    "gwBzIds": [101, 102],
    "testUserIds": [1001, 1002, 1003]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | sbbh | String | 是 | 设备编号 |
  | sbmc | String | 是 | 设备名称 |
  | sblb | String | 是 | 设备类型 |
  | scc | String | 否 | 生产厂 |
  | professionalType | String | 否 | 专业类型 |
  | equipmentAdmin | String | 否 | 设备管理员 |
  | commissionDate | Date | 否 | 投入使用时间 |
  | statusEquipmentUsage | String | 否 | 设备使用状态 |
  | internetThingId | Long | 否 | 物联网id |
  | workstationId | Long | 否 | 工位id |
  | parametersInspectedEquipment | String | 否 | 设备可检参数（用逗号分开） |
  | collectionMethod | String | 否 | 数据采集方式（直采/填报） |
  | gwBzIds | List<Long> | 否 | 关联的检测实验id列表 |
  | testUserIds | List<Long> | 是 | 实验人员ID列表，顺序具有优先级 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

##### 1.2.2 更新设备

- **URL**: `/instrumentNewInfo/update`
- **方法**: POST
- **功能描述**: 更新设备信息，支持关联多个实验人员
- **请求参数**:
  ```json
  {
    "id": 1,
    "sbbh": "SB2025001",
    "sbmc": "气相色谱仪",
    "sblb": "分析仪器",
    "scc": "某仪器制造商",
    "professionalType": "化学分析",
    "equipmentAdmin": "张三",
    "commissionDate": "2025-04-01",
    "statusEquipmentUsage": "正常",
    "internetThingId": 1001,
    "workstationId": 2001,
    "parametersInspectedEquipment": "温度,压力,浓度",
    "collectionMethod": "直采",
    "gwBzIds": [101, 102],
    "testUserIds": [1001, 1002, 1003]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | Long | 是 | 设备ID |
  | sbbh | String | 否 | 设备编号 |
  | sbmc | String | 否 | 设备名称 |
  | sblb | String | 否 | 设备类型 |
  | scc | String | 否 | 生产厂 |
  | professionalType | String | 否 | 专业类型 |
  | equipmentAdmin | String | 否 | 设备管理员 |
  | commissionDate | Date | 否 | 投入使用时间 |
  | statusEquipmentUsage | String | 否 | 设备使用状态 |
  | internetThingId | Long | 否 | 物联网id |
  | workstationId | Long | 否 | 工位id |
  | parametersInspectedEquipment | String | 否 | 设备可检参数（用逗号分开） |
  | collectionMethod | String | 否 | 数据采集方式（直采/填报） |
  | gwBzIds | List<Long> | 否 | 关联的检测实验id列表 |
  | testUserIds | List<Long> | 否 | 实验人员ID列表，顺序具有优先级 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 2. 实验项目管理模块

#### 2.1 获取实验项目分页数据

- **原需求描述**: 功能点：新增1个二级菜单/模块：“实验项目管理”
实验项目管理主要用于管理被试品所进行的实验项目，包含字段：实验项目、检测仪器（可多选）、检项、创建时间、修改时间。详情应包含创建人、最后操作人信息

- **URL**: `/standardBasicProjectInstrument/voPage`
- **方法**: POST
- **功能描述**: 获取实验项目管理分页数据，包含详细信息
- **请求参数**:
  ```json
  {
    "pageNum": 1,
    "pageSize": 10,
    "projectName": "实验项目名称",
    "createTimeStart": "2025-04-14 00:00:00",
    "createTimeEnd": "2025-04-20 23:59:59"
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | pageNum | Integer | 是 | 页码 |
  | pageSize | Integer | 是 | 每页条数 |
  | projectName | String | 否 | 实验项目名称（模糊查询） |
  | createTimeStart | String | 否 | 创建时间开始 |
  | createTimeEnd | String | 否 | 创建时间结束 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "records": [
        {
          "id": 1,
          "projectName": "气相色谱分析",
          "instrumentId": 101,
          "standardBasicInstrumentId": 201,
          "createTime": "2025-04-16 10:00:00",
          "updateTime": "2025-04-16 11:00:00",
          "createUserId": 1001,
          "updateUserId": 1002,
          "createUserFullName": "张三",
          "updateUserFullName": "李四",
          "projectParams": [
            {
              "id": 1,
              "paramName": "温度",
              "paramType": "环境参数",
              "standardBasicProjectId": 1
            },
            {
              "id": 2,
              "paramName": "湿度",
              "paramType": "环境参数",
              "standardBasicProjectId": 1
            }
          ],
          "instrumentNewInfo": {
            "id": 101,
            "sbmc": "气相色谱仪",
            "sbbh": "SB2025001"
          },
          "standardBasicProjectInstrument": {
            "id": 201,
            "standardName": "GB/T XXXXX-2025"
          }
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1
    }
  }
  ```

#### 2.2 获取实验项目详情

- **URL**: `/standardBasicProjectInstrument/getVo`
- **方法**: POST
- **功能描述**: 获取单个实验项目的详细信息
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | Long | 是 | 实验项目ID |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "projectName": "气相色谱分析",
      "instrumentId": 101,
      "standardBasicInstrumentId": 201,
      "createTime": "2025-04-16 10:00:00",
      "updateTime": "2025-04-16 11:00:00",
      "createUserId": 1001,
      "updateUserId": 1002,
      "createUserFullName": "张三",
      "updateUserFullName": "李四",
      "projectParams": [
        {
          "id": 1,
          "paramName": "温度",
          "paramType": "环境参数",
          "standardBasicProjectId": 1
        },
        {
          "id": 2,
          "paramName": "湿度",
          "paramType": "环境参数",
          "standardBasicProjectId": 1
        }
      ],
      "instrumentNewInfo": {
        "id": 101,
        "sbmc": "气相色谱仪",
        "sbbh": "SB2025001"
      },
      "standardBasicProjectInstrument": {
        "id": 201,
        "standardName": "GB/T XXXXX-2025"
      }
    }
  }
  ```

#### 2.3 新增实验项目

- **原需求描述**: 功能点：新增实验项目时，实验项目名手填，检测仪器通过工位-设备树形选择仪器

- **URL**: `/standardBasicProjectInstrument/add`
- **方法**: POST
- **功能描述**: 新增实验项目信息，支持同时添加检项参数
- **请求参数**:
  ```json
  {
    "projectName": "气相色谱分析",
    "instrumentId": 101,
    "standardBasicInstrumentId": 201,
    "createUserFullName": "张三",
    "projectParams": [
      {
        "paramName": "温度",
        "paramType": "环境参数"
      },
      {
        "paramName": "湿度",
        "paramType": "环境参数"
      }
    ]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | projectName | String | 是 | 实验项目名称 |
  | instrumentId | Long | 是 | 检测仪器ID |
  | standardBasicInstrumentId | Long | 是 | 标准ID |
  | createUserFullName | String | 否 | 创建人姓名 |
  | projectParams | List | 否 | 检项参数列表 |
  | projectParams.paramName | String | 是 | 参数名称 |
  | projectParams.paramType | String | 是 | 参数类别 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 2.4 更新实验项目

- **URL**: `/standardBasicProjectInstrument/update`
- **方法**: POST
- **功能描述**: 更新实验项目信息，支持同时更新检项参数
- **请求参数**:
  ```json
  {
    "id": 1,
    "projectName": "气相色谱分析（更新）",
    "instrumentId": 102,
    "standardBasicInstrumentId": 202,
    "updateUserFullName": "李四",
    "projectParams": [
      {
        "id": 1,
        "paramName": "温度（更新）",
        "paramType": "环境参数",
        "standardBasicProjectId": 1
      },
      {
        "paramName": "压力",
        "paramType": "环境参数"
      }
    ]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | Long | 是 | 实验项目ID |
  | projectName | String | 否 | 实验项目名称 |
  | instrumentId | Long | 否 | 检测仪器ID |
  | standardBasicInstrumentId | Long | 否 | 标准ID |
  | updateUserFullName | String | 否 | 更新人姓名 |
  | projectParams | List | 否 | 检项参数列表 |
  | projectParams.id | Long | 否 | 参数ID（有值表示更新，无值表示新增） |
  | projectParams.paramName | String | 是 | 参数名称 |
  | projectParams.paramType | String | 是 | 参数类别 |
  | projectParams.standardBasicProjectId | Long | 否 | 实验项目ID |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 2.5 删除实验项目

- **URL**: `/standardBasicProjectInstrument/delete`
- **方法**: POST
- **功能描述**: 删除实验项目信息，同时删除关联的检项参数
- **请求参数**:
  ```json
  {
    "id": "1,2,3"
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | String | 是 | 实验项目ID，多个ID用逗号分隔 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 2.6 获取工位-设备树形结构

- **URL**: `/buInstrumentWorkstationInfo/getWorkstationDeviceTree`
- **方法**: POST
- **功能描述**: 获取工位-设备树形结构，用于实验项目选择检测仪器
- **请求参数**: 无需参数
- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": [
      {
        "id": 1,
        "label": "一号工位",
        "type": "workstation",
        "children": [
          {
            "id": 101,
            "label": "气相色谱仪",
            "type": "device",
            "children": null
          },
          {
            "id": 102,
            "label": "液相色谱仪",
            "type": "device",
            "children": null
          }
        ]
      },
      {
        "id": 2,
        "label": "二号工位",
        "type": "workstation",
        "children": [
          {
            "id": 201,
            "label": "质谱仪",
            "type": "device",
            "children": null
          }
        ]
      }
    ]
  }
  ```
  | 参数名 | 类型 | 说明 |
  | --- | --- | --- |
  | id | Long | 节点ID |
  | label | String | 节点名称 |
  | type | String | 节点类型（workstation-工位，device-设备） |
  | children | Array | 子节点列表 |

### 3. 实验标准管理模块

#### 3.1 获取实验标准分页数据

- **原需求描述**: 功能点：原先标准管理下的列表管理页放在"实验标准管理"，包含样品名称、样品型号、样品规格、检测能力级别、实验项目（编辑时可选多个）、标准要求、规则模型、创建时间、修改时间，详情应包含创建人、最后操作人信息。

- **URL**: `/standardBasicInstrumentInfo/voPage`
- **方法**: POST
- **功能描述**: 获取实验标准管理分页数据，包含详细信息
- **请求参数**:
  ```json
  {
    "pageNum": 1,
    "pageSize": 10,
    "standardName": "标准名称",
    "sampleName": "样品名称",
    "sampleModel": "样品型号",
    "createTimeStart": "2025-04-14 00:00:00",
    "createTimeEnd": "2025-04-20 23:59:59"
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | pageNum | Integer | 是 | 页码 |
  | pageSize | Integer | 是 | 每页条数 |
  | standardName | String | 否 | 标准名称（模糊查询） |
  | sampleName | String | 否 | 样品名称（模糊查询） |
  | sampleModel | String | 否 | 样品型号（模糊查询） |
  | createTimeStart | String | 否 | 创建时间开始 |
  | createTimeEnd | String | 否 | 创建时间结束 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "records": [
        {
          "id": 1,
          "standardCode": "GB/T XXXXX-2025",
          "standardName": "某产品检测标准",
          "sampleName": "样品名称",
          "sampleModel": "样品型号",
          "sampleSpec": "样品规格",
          "testLevel": "检测能力级别",
          "standardRequire": "标准要求描述",
          "createTime": "2025-04-16 10:00:00",
          "updateTime": "2025-04-16 11:00:00",
          "createUserId": 1001,
          "updateUserId": 1002,
          "createUserFullName": "张三",
          "updateUserFullName": "李四",
          "standardBasicProjectInstrumentList": [
            {
              "id": 1,
              "projectName": "气相色谱分析"
            },
            {
              "id": 2,
              "projectName": "液相色谱分析"
            }
          ],
          "standardBasicModelParamList": [
            {
              "id": 1,
              "testItem": "波长测试",
              "paramKey": "x1",
              "unit": "nm",
              "judgeType": "比较型",
              "qualifiedStandard": "大于等于500nm",
              "judgeFormula": "x1 ≥ 500"
            }
          ]
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1
    }
  }
  ```

#### 3.2 获取实验标准详情

- **URL**: `/standardBasicInstrumentInfo/getVo`
- **方法**: POST
- **功能描述**: 获取单个实验标准的详细信息，包含规则模型
- **请求参数**:
  ```json
  {
    "id": 1
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | Long | 是 | 实验标准ID |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {
      "id": 1,
      "standardCode": "GB/T XXXXX-2025",
      "standardName": "某产品检测标准",
      "sampleName": "样品名称",
      "sampleModel": "样品型号",
      "sampleSpec": "样品规格",
      "testLevel": "检测能力级别",
      "standardRequire": "标准要求描述",
      "createTime": "2025-04-16 10:00:00",
      "updateTime": "2025-04-16 11:00:00",
      "createUserId": 1001,
      "updateUserId": 1002,
      "createUserFullName": "张三",
      "updateUserFullName": "李四",
      "standardBasicProjectInstrumentList": [
        {
          "id": 1,
          "projectName": "气相色谱分析"
        },
        {
          "id": 2,
          "projectName": "液相色谱分析"
        }
      ],
      "standardBasicModelParamList": [
        {
          "id": 1,
          "testItem": "波长测试",
          "paramKey": "x1",
          "unit": "nm",
          "judgeType": "比较型",
          "qualifiedStandard": "大于等于500nm",
          "judgeFormula": "x1 ≥ 500"
        },
        {
          "id": 2,
          "testItem": "波型测试",
          "paramKey": "y1",
          "unit": "Hz",
          "judgeType": "计算比较依据型",
          "qualifiedStandard": "小于10kg",
          "judgeFormula": "y1 < 10"
        }
      ]
    }
  }
  ```

#### 3.3 新增实验标准

- **URL**: `/standardBasicInstrumentInfo/add`
- **方法**: POST
- **功能描述**: 新增实验标准信息，不包含规则模型（规则模型只能在编辑时添加）
- **请求参数**:
  ```json
  {
    "standardCode": "GB/T XXXXX-2025",
    "standardName": "某产品检测标准",
    "sampleName": "样品名称",
    "sampleModel": "样品型号",
    "sampleSpec": "样品规格",
    "testLevel": "检测能力级别",
    "standardRequire": "标准要求描述",
    "createUserFullName": "张三"
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | standardCode | String | 是 | 标准编号 |
  | standardName | String | 是 | 标准名称 |
  | sampleName | String | 是 | 样品名称 |
  | sampleModel | String | 否 | 样品型号 |
  | sampleSpec | String | 否 | 样品规格 |
  | testLevel | String | 否 | 检测能力级别 |
  | standardRequire | String | 否 | 标准要求 |
  | createUserFullName | String | 否 | 创建人姓名 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 3.4 更新实验标准

- **URL**: `/standardBasicInstrumentInfo/update`
- **方法**: POST
- **功能描述**: 更新实验标准信息，支持同时添加/更新规则模型和关联实验项目
- **请求参数**:
  ```json
  {
    "id": 1,
    "standardCode": "GB/T XXXXX-2025（更新）",
    "standardName": "某产品检测标准（更新）",
    "sampleName": "样品名称（更新）",
    "sampleModel": "样品型号（更新）",
    "sampleSpec": "样品规格（更新）",
    "testLevel": "检测能力级别（更新）",
    "standardRequire": "标准要求描述（更新）",
    "updateUserFullName": "李四",
    "standardBasicModelParams": [
      {
        "id": 1,
        "testItem": "波长测试（更新）",
        "paramKey": "x1",
        "unit": "nm",
        "judgeType": "比较型",
        "qualifiedStandard": "大于等于600nm",
        "judgeFormula": "x1 ≥ 600",
        "standardBasicInstrumentId": 1
      },
      {
        "testItem": "新增测试项",
        "paramKey": "z1",
        "unit": "kg",
        "judgeType": "计算比较依据型",
        "qualifiedStandard": "小于10kg",
        "judgeFormula": "z1 < 10"
      }
    ]
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | Long | 是 | 实验标准ID |
  | standardCode | String | 否 | 标准编号 |
  | standardName | String | 否 | 标准名称 |
  | sampleName | String | 否 | 样品名称 |
  | sampleModel | String | 否 | 样品型号 |
  | sampleSpec | String | 否 | 样品规格 |
  | testLevel | String | 否 | 检测能力级别 |
  | standardRequire | String | 否 | 标准要求 |
  | updateUserFullName | String | 否 | 更新人姓名 |
  | standardBasicModelParams | List | 否 | 规则模型列表 |
  | standardBasicModelParams.id | Long | 否 | 规则模型ID（有值表示更新，无值表示新增） |
  | standardBasicModelParams.testItem | String | 是 | 检项 |
  | standardBasicModelParams.paramKey | String | 是 | 检项参数（英文，在一个标准模型中唯一） |
  | standardBasicModelParams.unit | String | 否 | 单位 |
  | standardBasicModelParams.judgeType | String | 是 | 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） |
  | standardBasicModelParams.qualifiedStandard | String | 是 | 合格标准（描述合格的参数） |
  | standardBasicModelParams.judgeFormula | String | 是 | 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) |
  | standardBasicModelParams.standardBasicInstrumentId | Long | 否 | 关联的实验标准ID |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

#### 3.5 删除实验标准

- **URL**: `/standardBasicInstrumentInfo/delete`
- **方法**: POST
- **功能描述**: 删除实验标准信息，同时删除关联的规则模型
- **请求参数**:
  ```json
  {
    "id": "1,2,3"
  }
  ```
  | 参数名 | 类型 | 必填 | 说明 |
  | --- | --- | --- | --- |
  | id | String | 是 | 实验标准ID，多个ID用逗号分隔 |

- **响应结构**:
  ```json
  {
    "success": true,
    "code": 200,
    "message": "操作成功"
  }
  ```

### 4. 其他功能模块

#### 4.1 待补充接口

待补充...

## 🖥️ 前端开发内容概览

本次开发涉及以下功能模块：

1. **设备管理功能增强** 
   - 新增在线状态及数据获取功能
   - 设备新增/编辑支持选择多个实验人员，顺序具有优先级
2. **实验项目管理** - 新增二级菜单/模块，用于管理被试品所进行的实验项目
3. **实验标准管理** - 原先标准管理下的列表管理页，增强规则模型功能
4. **其他功能模块** - 待补充

## 📊 数据模型说明

### 1. 设备检测数据(AutoResultDto)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| result | String | 检测结果 |
| fileId | Long | 关联文件ID |
| fileInfo | FileInfo | 文件信息对象 |

### 2. 设备信息(InstrumentNewInfo)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| sbbh | String | 设备编号 |
| sbmc | String | 设备名称 |
| sblb | String | 设备类型 |
| scc | String | 生产厂 |
| professionalType | String | 专业类型 |
| equipmentAdmin | String | 设备管理员 |
| commissionDate | Date | 投入使用时间 |
| statusEquipmentUsage | String | 设备使用状态 |
| internetThingId | Long | 物联网id |
| workstationId | Long | 工位id |
| testUserId | String | 管理测试人员id(旧字段，已不再使用) |
| testUserIds | List<Long> | 实验人员ID列表，顺序具有优先级(新字段) |
| gwBzId | Long | 关联的检测实验id |
| gwBzIds | List<Long> | 关联的检测实验id列表 |
| parametersInspectedEquipment | String | 设备可检参数（用逗号分开） |
| collectionMethod | String | 数据采集方式（直采/填报） |

### 3. 实验项目管理(StandardBasicProjectInstrument)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| projectName | String | 实验项目名称 |
| instrumentId | Long | 检测仪器ID |
| standardBasicInstrumentId | Long | 标准ID |
| createTime | Date | 创建时间 |
| updateTime | Date | 修改时间 |
| createUserId | Long | 创建人ID |
| updateUserId | Long | 最后操作人ID |
| createUserFullName | String | 创建人姓名 |
| updateUserFullName | String | 最后操作人姓名 |
| projectParams | List | 检项参数列表 |
| instrumentNewInfo | Object | 关联的仪器设备信息 |
| standardBasicProjectInstrument | Object | 关联的实验标准信息 |

### 4. 实验项目参数(StandardBasicProjectParam)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| paramName | String | 参数名称 |
| paramType | String | 参数类别 |
| standardBasicProjectId | Long | 实验项目ID |
| createTime | Date | 创建时间 |
| updateTime | Date | 修改时间 |

### 5. 实验标准管理(StandardBasicInstrumentInfo)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| standardCode | String | 标准编号 |
| standardName | String | 标准名称 |
| sampleName | String | 样品名称 |
| sampleModel | String | 样品型号 |
| sampleSpec | String | 样品规格 |
| testLevel | String | 检测能力级别 |
| standardRequire | String | 标准要求 |
| createTime | Date | 创建时间 |
| updateTime | Date | 修改时间 |
| createUserId | Long | 创建人ID |
| updateUserId | Long | 最后操作人ID |
| createUserFullName | String | 创建人姓名 |
| updateUserFullName | String | 最后操作人姓名 |
| standardBasicProjectInstrumentList | List | 关联的实验项目列表 |
| standardBasicModelParamList | List | 关联的规则模型列表 |

### 6. 规则模型(StandardBasicModelParam)

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| id | Long | 主键ID |
| testItem | String | 检项 |
| paramKey | String | 检项参数（英文，在一个标准模型中唯一） |
| unit | String | 单位 |
| judgeType | String | 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） |
| qualifiedStandard | String | 合格标准（描述合格的参数） |
| judgeFormula | String | 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) |
| createTime | Date | 创建时间 |
| updateTime | Date | 修改时间 |
| standardBasicInstrumentId | Long | 关联的实验标准ID |

## 🔄 交互流程说明

### 1. 设备检测数据获取流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取设备列表（包含在线状态）
    后端-->>前端: 返回设备列表数据
    
    前端->>后端: 点击"获取数据"按钮，请求设备检测数据
    后端-->>前端: 返回设备检测数据
    
    前端->>前端: 展示检测数据和关联文件
```

### 2. 设备实验人员关联流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>前端: 打开设备新增/编辑表单
    前端->>后端: 获取实验人员列表
    后端-->>前端: 返回实验人员数据
    
    前端->>前端: 用户选择多个实验人员并排序
    前端->>后端: 提交设备信息(包含testUserIds数组)
    后端->>后端: 保存设备信息并关联实验人员
    后端-->>前端: 返回操作结果
```

### 3. 实验项目管理流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取实验项目列表
    后端-->>前端: 返回实验项目数据
    
    前端->>前端: 用户点击新增/编辑按钮
    前端->>后端: 获取工位-设备树形结构
    后端-->>前端: 返回工位-设备树形数据
    
    前端->>后端: 获取可选标准列表
    后端-->>前端: 返回标准列表
    
    前端->>前端: 用户填写实验项目信息和检项参数
    前端->>后端: 提交实验项目信息
    后端->>后端: 保存实验项目和检项参数
    后端-->>前端: 返回操作结果
```

### 4. 实验标准管理流程

```mermaid
sequenceDiagram
    participant 前端
    participant 后端
    
    前端->>后端: 获取实验标准列表
    后端-->>前端: 返回实验标准数据
    
    前端->>前端: 用户点击新增按钮
    前端->>前端: 用户填写基本信息（不含规则模型）
    前端->>后端: 提交实验标准基本信息
    后端->>后端: 保存实验标准信息
    后端-->>前端: 返回操作结果
    
    前端->>前端: 用户点击编辑按钮
    前端->>后端: 获取实验标准详情
    后端-->>前端: 返回实验标准详情（含规则模型）
    
    前端->>前端: 用户编辑基本信息和规则模型
    前端->>后端: 提交更新后的实验标准信息
    后端->>后端: 保存实验标准和规则模型
    后端-->>前端: 返回操作结果
```

## 📝 开发注意事项

1. 所有POST请求的Content-Type均为`application/json`
2. 所有响应均采用统一的IResult格式包装
3. 设备在线状态判断逻辑由后端实现，前端只需根据返回值展示
4. 获取数据按钮仅对在线设备显示，离线设备不显示此按钮
5. 数据获取可能存在一定延迟，建议添加加载状态提示
6. 文件下载需要调用系统已有的文件下载接口
7. 设备关联实验人员时，前端需要提供多选并且可排序的界面组件
8. 实验人员的顺序即为优先级顺序，前端传递的testUserIds数组顺序必须保持不变
9. 设备编辑时需要回显已关联的实验人员，并保持原有顺序
10. 实验项目管理模块需要支持多个检测仪器的选择
11. 实验项目的检项参数需要支持动态添加和删除
12. 实验项目详情页面需要展示创建人和最后操作人信息
13. 实验项目名称需要手动填写，检测仪器需要通过工位-设备树形结构选择
14. 实验标准管理中，规则模型只能在编辑时进行添加和修改，新增时不支持
15. 规则模型中的检项参数(paramKey)在同一个标准模型中必须唯一
16. 判定公式支持的符号包括: + - × ÷ ( ) ∪ ∩ ≥ ≤ < > =
17. 前端需要提供规则模型的编辑界面，类似Excel公式编辑

## 📅 开发计划

| 日期 | 计划内容 | 负责人 |
| --- | --- | --- |
| 4.14 | 设备管理功能开发 | 待定 |
| 4.15-4.18 | 实验项目管理和实验标准管理功能开发 | 待定 |
| 4.19-4.20 | 联调测试 | 待定 |
