方式：以mqtt为消息传递服务，以下是下发指令topic

**请求报文示例**

{

  \"header\":{

    \"deviceId\":\"44444\",//设备标识

    \"product\": \"sichuangDevice\",//厂商标识

    \"typeCode\": \"test\",//指令名

    \"messageId\": \"2342342342342\"//消息唯一标识（最好用时间戳）

  },

  \"body\":{//指令参数集

    \"outputParams\": {

\"body\": {}//设备结果集\*\*\*\*\*\*\*\*\*\*\*\*\*

},

  }

}

**请求报文示例**

{

  \"header\":{

    \"deviceId\":\"44444\",//设备标识

    \"product\": \"sichuangDevice\",//厂商标识

    \"typeCode\": \"test\",//指令名

    \"messageId\": \"2342342342342\"//消息唯一标识（最好用时间戳）

  },

  \"body\":{//指令参数集

    \"test\":\"1\"

  }

}

  ------------------ --------------------------------------------------------
         名称        命令下发

         主题        /jcxt/link/down/edgeGateway/edgeTopic

        发送方       检测系统

      接收方系统     设备采集系统

       调用频率      手动

       调用方式      Mqtt

       调用参数      Json消息字符串

   消息参数字段说明  
  ------------------ --------------------------------------------------------

  ------------------ --------------------------------------------------------
         名称        返回接收

         主题        /jcxt/link/up/edgeGateway/edgeTopic

      调用方系统     设备采集系统

      接收方系统     检测系统

       调用频率      自动

       调用方式      Mqtt

       调用参数      Json消息字符串

   消息参数字段说明  
  ------------------ --------------------------------------------------------

思创（厂商标识sichuang）消息体

  ------------------ ------------------ --------- ---------- ----------------------------
         名称        思创检测结果获取                        

   JSON参数字段说明                                          

       字段描述      字段名             类型      是否必须   备注

       任务编号      encodingcode       String    是         唯一

        检测项       items              String    是         多个检测项逗号（半角）隔开

       试品名称      devicename         String    是         试品名称

       试品类别      categoryname       String    是         试品类别(配电变压器\...)

     检测中心编码    testcentercode     String    是         编码（可默认）

       设备地址      sichuangSeverUrl   String    是         设备地址（可默认）

       指定工位      workstationName    String    是         绑定工位（可默认）

       试品参数      deviceparams       String    是         没有测试参数，则填null值
  ------------------ ------------------ --------- ---------- ----------------------------

**请求报文示例**

{

  \"header\":{

    \"deviceId\":\"sichuang\",

    \"product\": \"sichuangDevice\",

    \"typeCode\": \"getData\",

    \"messageId\": \"984759837459867\"

  },

  \"body\":{

    \"encodingcode\":\"20250304163316334536\",

    \"items\":\"E006,E007\",

    \"devicename\":\"JP柜\",

    \"categoryname\":\"JP柜\",

    \"testcentercode\":\"2019061900049522\",

    \"workstationName\":\"A1\",

    \"sichuangSeverUrl\":\"http://192.168.16.101:5000/TaskNR/TaskData\",

    \"deviceparams\":\"\[{\\\"SPCS001\\\":\\\"630\\\"}\]\"

  }

}

返回参数映射表，见附件"荆州物资数据对接映射关系表"

厂商NARI，标识accessId

  ------------------ -------------------- ----------- ---------- -----------------------
         名称        万测变压器结果获取                          

   JSON参数字段说明                                              

       字段描述      字段名               类型        是否必须   备注

         表名        tableName            String      是         唯一

     返回结果数量    resultNum            String      是         

       开始时间      beginTime            String      否         不能单个存在

       结束时间      endTime              String      否         
  ------------------ -------------------- ----------- ---------- -----------------------

**获取最新数据请求报文示例**

{//获取四张表最新的数据

  \"header\":{

    \"deviceId\":\"wanceDevice\",

    \"product\": \"accessId\",

    \"typeCode\": \"getAllData\",

    \"messageId\": \"3452876876238\"

  },

  \"body\":{

    \"type\":\"1\"//模拟值，固定的

  }

}

{//获取指定表的指定条数数据

  \"header\":{

    \"deviceId\":\"wanceDevice\",

    \"product\": \"accessId\",

    \"typeCode\": \"getDataByTable\",

    \"messageId\": \"7987239458\"

  },

  \"body\":{

    \"tableName\":\"DTS_T_RZZLDZCL\",

\"resultNum\":\"1\",

\"beginTime\":\"2024-4-21\",

\"endTime\":\"2025-4-22\"

  }

}

返回参数映射表，见附件"荆州物资数据对接映射关系表

  ------------------ ------------------ ----------- ---------- -----------------------
         名称        电子式静载试验机                          

   JSON参数字段说明                                            

       字段描述      字段名             类型        是否必须   备注

        连接ip       host               String      是         

        用户名       userName           String      是         

         密码        pwd                String      是         

      文件夹名称     share              String      是         
  ------------------ ------------------ ----------- ---------- -----------------------

**请求报文示例**

{

  \"header\":{

    \"deviceId\":\"eleMachine50\",

    \"product\": \"eleMachine\",

    \"typeCode\": \"getData\",

    \"messageId\": \"345823736531\"

  },

  \"body\":{

\"host\":\"************\",

\"userName\":\"smb\",

\"pwd\":\"123456\",

\"share\":\"xxlpr\"

  }

}

  ------------------ --------------------------------------------------------
         名称        10T电子万能试验机

   JSON参数字段说明  

         备注        同上 电子式静载试验机 一样
  ------------------ --------------------------------------------------------

**请求报文示例**

{

  \"header\":{

    \"deviceId\":\"eleMachine51\",

    \"product\": \"eleMachine\",

    \"typeCode\": \"getData\",

    \"messageId\": \"345823736531\"

  },

  \"body\":{

\"host\":\"************\",

\"userName\":\"smb\",

\"pwd\":\"123456\",

\"share\":\"data\"

  }

}

  ------------------ --------------------------------------------------------
         名称        2T电子万能试验机

   JSON参数字段说明  

         备注        同上 电子式静载试验机 一样
  ------------------ --------------------------------------------------------

厂商绝缘子机电破坏试验机，标识jyzjdph

  ------------------ -------------------------- ----------- ---------- -----------------------
         名称        绝缘子机电破坏试验机获取                          

   JSON参数字段说明                                                    

       字段描述      字段名                     类型        是否必须   备注
  ------------------ -------------------------- ----------- ---------- -----------------------

**获取最新数据请求报文示例**

{

  \"header\":{

    \"deviceId\":\"jyaDevice\",

    \"product\": \"jyzjdph\",

    \"typeCode\": \"getAllData\",

    \"messageId\": \"4262452345\"

  },

  \"body\":{

    \"test\":\"1\"//占位值

  }

}

返回参数映射表，见附件"

厂商维卡万能试验机，标识wkProduct

  ------------------ -------------------------- ----------- ---------- -----------------------
         名称        绝缘子机电破坏试验机获取                          

   JSON参数字段说明                                                    

       字段描述      字段名                     类型        是否必须   备注
  ------------------ -------------------------- ----------- ---------- -----------------------

**获取最新数据请求报文示例**

{

  \"header\":{

    \"deviceId\":\"wkDevice\",

    \"product\": \"wkProduct\",

    \"typeCode\": \"getAllData\",

    \"messageId\": \"4262452345\"

  },

  \"body\":{

    \"test\":\"1\"//占位值

  }

}

返回参数映射表，见附件"
