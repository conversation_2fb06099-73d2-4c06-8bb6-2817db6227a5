# accessId 类型设备对接文档

> 万测设备对接详细说明  
> 设备类型: accessId  
> 主要设备: wanceDevice, accessDataList  

## 📋 设备信息

| 设备ID | 调用次数 | 成功率 | 活跃期 | 状态 |
|--------|----------|--------|--------|------|
| wanceDevice | 230 | 97.8% | 2025-04-20 至今 | 🟢 生产环境 |
| accessDataList | 11 | 45.5% | 2025-04-18 | 🟡 测试环境 |

## 🔄 调用特征

- **调用方式**: 100% 异步调用
- **响应时间**: 变化较大，取决于测试复杂度
- **数据量**: 大量结构化测试数据
- **更新频率**: 高频，主要在工作时间

## 📊 响应数据结构

### 成功响应格式

```json
{
  "body": "[{测试数据数组}]"
}
```

### 测试数据字段说明

#### 基础信息字段
- `pid`: 产品ID/批次号 (如: "20250415", "2025012107002")
- `DTS_T_RZZLDZCL_ID`: 数据表记录ID
- `DEVICEINFOID`: 设备信息ID (通常为0)
- `temp`: 环境温度 (°C)
- `testdate`: 测试日期时间戳

#### 电阻测量字段 (单位: Ω)
**主绕组电阻 (r1-r3系列)**
- `r1a`, `r1b`, `r1c`: A、B、C相主绕组电阻
- `r2a`, `r2b`, `r2c`: A、B、C相次级绕组电阻  
- `r3a`, `r3b`, `r3c`: A、B、C相第三绕组电阻

**辅助绕组电阻 (r4-r9系列)**
- `r4a`, `r4b`, `r4c`: A、B、C相第四绕组电阻
- `r5a`, `r5b`, `r5c`: A、B、C相第五绕组电阻
- `r6a`, `r6b`, `r6c`: A、B、C相第六绕组电阻
- `r7a`, `r7b`, `r7c`: A、B、C相第七绕组电阻
- `r8a`, `r8b`, `r8c`: A、B、C相第八绕组电阻
- `r9a`, `r9b`, `r9c`: A、B、C相第九绕组电阻

**绕组间电阻 (rd系列)**
- `rdab`: A-B相间电阻
- `rdbc`: B-C相间电阻  
- `rdca`: C-A相间电阻

**分接电阻 (bp系列)**
- `r1bp`, `r2bp`, `r3bp`: 各绕组分接电阻
- `r4bp`, `r5bp`, `r6bp`: 辅助绕组分接电阻
- `r7bp`, `r8bp`, `r9bp`: 高压绕组分接电阻
- `rdbp`: 分接间电阻

**零位电阻**
- `rda0`, `rdb0`, `rdc0`: 各相零位电阻
- `rdbp0`: 分接零位电阻

## 📝 典型响应示例

### 示例1: 正常测试数据
```json
{
  "body": "[{
    \"r3b\": 11.0600000,
    \"r3a\": 11.0800000,
    \"r3c\": 11.0500000,
    \"pid\": \"20250415\",
    \"rda0\": 0E-7,
    \"r8a\": 0E-7,
    \"r8c\": 0E-7,
    \"r8b\": 0E-7,
    \"r4a\": 0E-7,
    \"r4c\": 0E-7,
    \"r4b\": 0E-7,
    \"r9bp\": 0E-7,
    \"r5bp\": 0E-7,
    \"rdca\": 0.0111600,
    \"r7bp\": 0E-7,
    \"rdbp\": 0.5382730,
    \"r1bp\": 0E-7,
    \"r9b\": 0E-7,
    \"r3bp\": 0.2711640,
    \"r9a\": 0E-7,
    \"r9c\": 0E-7,
    \"r5b\": 0E-7,
    \"r5a\": 0E-7,
    \"r5c\": 0E-7,
    \"r1b\": 0E-7,
    \"r1a\": 11.0800000,
    \"r1c\": 0E-7,
    \"rdbp0\": 0E-7,
    \"rdbc\": 0.0111100,
    \"DTS_T_RZZLDZCL_ID\": 249,
    \"r6a\": 0E-7,
    \"r6c\": 0E-7,
    \"r6b\": 0E-7,
    \"r2a\": 0E-7,
    \"r8bp\": 0E-7,
    \"DEVICEINFOID\": 0,
    \"temp\": 19.5000000,
    \"r2c\": 0E-7,
    \"r2b\": 0E-7,
    \"r4bp\": 0E-7,
    \"r6bp\": 0E-7,
    \"rdab\": 0.0111700,
    \"rdc0\": 0E-7,
    \"testdate\": 1744646400000,
    \"rdb0\": 0E-7,
    \"r2bp\": 0E-7,
    \"r7b\": 0E-7,
    \"r7a\": 0E-7,
    \"r7c\": 0E-7
  }]"
}
```

### 示例2: 错误响应
```json
{
  "_msg": "连接器未连接！",
  "_status": 500
}
```

## ⚠️ 对接注意事项

### 1. 数据解析
- `body` 字段包含JSON字符串，需要二次解析
- 数组格式，可能包含多组测试数据
- 数值使用科学计数法表示 (如: 0E-7 表示 0)

### 2. 错误处理
- 连接错误: "连接器未连接！"
- 设备状态错误: "连接器未启动！"
- 统一错误码: 500

### 3. 数据验证
- 检查 `body` 字段是否存在
- 验证JSON格式的有效性
- 确认必要字段 (pid, temp, testdate) 的存在

### 4. 性能考虑
- 数据量较大，建议异步处理
- 考虑数据压缩和缓存策略
- 监控响应时间和成功率

## 🔧 集成建议

### 数据处理流程
1. **接收响应** → 检查基本格式
2. **解析body** → JSON字符串转对象
3. **数据验证** → 必要字段检查
4. **业务处理** → 根据测试数据进行分析
5. **异常处理** → 记录错误并重试

### 监控指标
- 响应成功率 (目标: >95%)
- 平均响应时间
- 数据完整性
- 连接稳定性

---

*本文档基于 wanceDevice 和 accessDataList 的实际运行数据编写，涵盖了主要的数据格式和对接要点。*
