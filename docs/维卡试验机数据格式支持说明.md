# 维卡试验机数据格式支持说明

## 📋 概述

本次更新为设备数据转换系统新增了对维卡试验机嵌套数组格式的支持，能够正确解析和处理维卡试验机返回的复杂数据结构。

## 🔄 新支持的数据格式

### 维卡试验机数据格式
```json
{
  "message": {
    "_product": "wkProduct",
    "bodyData": [
      {
        "bodyData": [
          {
            "AllRoutNo": 1,
            "SmpRstNot": "无",
            "TestPreRst": 150.0,
            "InTestOrNot": 1,
            "TestLoadRst": 0.485,
            "TestTimeRst": 5664.8
          }
        ],
        "tableName": "RstTab"
      }
    ],
    "_deviceId": "accessDeviceList106",
    "readDataTime": "2025-06-17 17:28:57.137"
  },
  "product": "wkProduct",
  "deviceId": "accessDeviceList106",
  "messageId": 1934906097583370240,
  "timestamp": 1750152537137,
  "deviceType": "virtualDevice",
  "originalId": "accessDeviceList106",
  "connectorId": 1932989916540989440,
  "messageType": "PROPERTY",
  "connectorKey": "accessDeviceList",
  "platformDeviceId": "accessDeviceList106"
}
```

### 对应的配置格式
```json
{
  "experiments": [
    {
      "code": "accessDeviceList106",
      "name": "维卡试验机",
      "tableName": "RstTab",
      "mapping": {
        "AllRoutNo": "AllRoutNo",
        "SmpRstNot": "SmpRstNot",
        "TestPreRst": "TestPreRst",
        "InTestOrNot": "InTestOrNot",
        "TestLoadRst": "TestLoadRst",
        "TestTimeRst": "TestTimeRst"
      }
    }
  ]
}
```

## 🔧 技术实现

### 1. 数据解析逻辑增强

在 `DeviceDataServiceImpl.extractTestData()` 方法中新增了对嵌套数组格式的支持：

```java
// 新增：处理 bodyData 为数组的情况（如维卡试验机格式）
else if (actualBodyData instanceof List) {
    List<?> actualBodyDataList = (List<?>) actualBodyData;
    if (!actualBodyDataList.isEmpty() && actualBodyDataList.get(0) instanceof Map) {
        @SuppressWarnings("unchecked")
        Map<String, Object> firstDataItem = (Map<String, Object>) actualBodyDataList.get(0);
        log.debug("✅ 找到匹配的试验数据（数组格式），tableName: {}, 数据: {}", 
                targetTableName, firstDataItem);
        return firstDataItem;
    }
}
```

### 2. 数据结构特点

- **双层嵌套**: `message.bodyData[].bodyData[]`
- **表名匹配**: 通过 `tableName` 字段进行数据匹配
- **数组提取**: 从内层 `bodyData` 数组中提取第一个元素作为测试数据

### 3. 兼容性保证

新的解析逻辑完全向后兼容，支持以下格式：
1. ✅ 新格式：维卡试验机嵌套数组格式
2. ✅ 原格式：万测变压器数组格式
3. ✅ 旧格式：传统 body 字段格式

## 📁 相关文件

### 核心代码文件
- `src/main/java/com/westcatr/rd/testbusiness/business/devicedata/service/impl/DeviceDataServiceImpl.java`
  - 新增嵌套数组格式解析逻辑
  - 更新方法注释和文档

### 配置文件
- `src/main/resources/db/migration/V20250617__init_wika_device_config.sql`
  - 维卡试验机设备配置初始化

### 测试文件
- `src/test/java/com/westcatr/rd/testbusiness/business/devicedata/service/DeviceDataServiceWikaTest.java`
  - 维卡试验机数据格式解析测试用例

## 🧪 测试验证

### 单元测试
运行测试用例验证新格式解析：
```bash
mvn test -Dtest=DeviceDataServiceWikaTest
```

### API测试
使用设备代码 `accessDeviceList106` 调用查询接口：
```bash
GET /device-data/query?deviceCode=accessDeviceList106
```

## 📊 字段映射说明

| 显示名称 | 数据字段 | 数据类型 | 说明 |
|---------|---------|---------|------|
| AllRoutNo | AllRoutNo | Integer | 路由编号 |
| SmpRstNot | SmpRstNot | String | 样品结果备注 |
| TestPreRst | TestPreRst | Double | 测试预设结果 |
| InTestOrNot | InTestOrNot | Integer | 是否在测试中 |
| TestLoadRst | TestLoadRst | Double | 测试负载结果 |
| TestTimeRst | TestTimeRst | Double | 测试时间结果 |

## 🔍 日志监控

系统会输出详细的解析日志，便于调试和监控：

```
📊 从 message.bodyData 中提取数据: [...]
📋 检测到 bodyData 数组格式，数组长度: 1
✅ 找到匹配的试验数据（数组格式），tableName: RstTab, 数据: {...}
```

## 🚀 部署说明

1. **数据库迁移**: 执行 `V20250617__init_wika_device_config.sql` 初始化配置
2. **代码部署**: 部署包含新解析逻辑的代码版本
3. **功能验证**: 使用测试用例或API接口验证功能正常

## 📝 更新记录

- **2025-06-17**: 新增维卡试验机嵌套数组格式支持
- **作者**: liusheng
- **版本**: v1.0.0
