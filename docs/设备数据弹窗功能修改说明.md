# 设备数据弹窗功能修改说明

## 📋 修改概述

本次修改将设备管理功能中的获取设备数据弹窗显示逻辑从原来的 `bu_device_integration_config` 表切换到 `bu_device_data_config` 表，实现更灵活的设备数据配置管理。

## 🎯 修改目标

- **原逻辑**：根据设备的 `device_integration_config_id` 查询 `bu_device_integration_config` 表的 `param_mapping` 字段
- **新逻辑**：根据设备的 `device_type_code` 查询 `bu_device_data_config` 表的 `response_body` 字段

## 🔧 技术实现

### 1. 数据库表结构

#### bu_device_data_config (设备对接数据管理配置表)
```sql
CREATE TABLE `bu_device_data_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_code` varchar(64) NOT NULL COMMENT '设备代码',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `response_body` text NOT NULL COMMENT '设备返回体JSON配置',
  `status` varchar(32) DEFAULT 'enabled' COMMENT '状态(enabled/disabled)',
  `description` varchar(512) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_code` (`device_code`)
);
```

#### 关联关系
- `bu_instrument_new_info.device_type_code` ↔ `bu_device_data_config.device_code`

### 2. JSON配置格式

`response_body` 字段采用以下JSON格式：

```json
{
  "experiments": [
    {
      "name": "直阻试验",
      "code": "DTS_T_RZZLDZCL",
      "tableName": "DTS_T_RZZLDZCL",
      "mapping": {
        "高压1档R1": "r1a",
        "高压1档R2": "r1b",
        "高压1档R3": "r1c",
        "试验温度": "temp"
      }
    }
  ]
}
```

### 3. 后端接口修改

#### 修改文件
- `src/main/java/com/westcatr/rd/testbusiness/business/mqtt/controller/DeviceIntegrationController.java`

#### 修改内容
```java
// 原逻辑：通过device_integration_config_id查询
DeviceIntegrationConfig deviceConfig = deviceIntegrationService
    .getDeviceConfigById(request.getEquipmentId());

// 新逻辑：通过device_type_code查询
InstrumentNewInfo deviceInfo = instrumentNewInfoService.getById(request.getEquipmentId());
String deviceTypeCode = deviceInfo.getDeviceTypeCode();
DeviceDataConfig deviceDataConfig = deviceDataService.getDeviceConfig(deviceTypeCode);
```

### 4. 前端组件
前端组件 `src/pages/config/newInstrument/deviceTestData.tsx` 无需修改，因为：
- 接口调用方式不变
- 返回数据结构保持一致
- 渲染逻辑基于 `experiments` 数组

## 📊 配置数据

### 变压器设备配置
```json
{
  "experiments": [
    {
      "name": "直阻试验",
      "code": "DTS_T_RZZLDZCL",
      "tableName": "DTS_T_RZZLDZCL",
      "mapping": {
        "高压1档R1": "r1a",
        "高压1档R2": "r1b",
        "高压1档R3": "r1c",
        "试验温度": "temp"
      }
    },
    {
      "name": "变比试验",
      "code": "DTS_T_RZDYBCLHLJZBHJD",
      "tableName": "DTS_T_RZDYBCLHLJZBHJD",
      "mapping": {
        "低压侧电压": "lvu",
        "分接1高压侧电压": "hvu1",
        "分接1额定变比": "bb1"
      }
    }
  ]
}
```

### 电子万能试验机配置
```json
{
  "experiments": [
    {
      "name": "拉伸试验",
      "code": "tensile_test",
      "tableName": "utm_tensile_data",
      "mapping": {
        "最大拉力": "max_force",
        "断裂强度": "break_strength",
        "屈服强度": "yield_strength"
      }
    }
  ]
}
```

## 🧪 测试验证

### 测试脚本
使用提供的测试脚本验证功能：
```bash
chmod +x test_device_popup_api.sh
./test_device_popup_api.sh
```

### 测试用例
1. **变压器设备** (ID: 1003) - device_type_code: '变压器'
2. **电子万能试验机** (ID: 1001) - device_type_code: '电子万能试验机'
3. **电机设备** (ID: 1002) - device_type_code: '电机设备'
4. **思创设备** (ID: 1004) - device_type_code: '思创设备'
5. **不存在设备** (ID: 9999) - 错误处理测试

### 预期结果
- 接口返回 `success: true`
- `data.experiments` 包含实验配置数组
- 每个实验包含 `name`, `code`, `tableName`, `mapping` 字段
- 前端弹窗正常显示实验数据

## 🔍 故障排查

### 常见问题
1. **设备配置未找到**
   - 检查 `device_type_code` 是否与 `bu_device_data_config.device_code` 匹配
   - 确认配置状态为 `enabled`

2. **JSON解析失败**
   - 检查 `response_body` 字段的JSON格式是否正确
   - 确认包含 `experiments` 数组

3. **前端显示异常**
   - 检查后端接口返回的数据结构
   - 确认 `experiments` 数组格式正确

### 日志查看
```bash
# 查看应用日志
tail -f logs/test_business_back.log | grep "获取设备实验数据"
```

## ✅ 修改完成清单

- [x] 修改后端接口逻辑
- [x] 创建数据库表结构
- [x] 添加设备配置数据
- [x] 创建测试脚本
- [x] 编写文档说明
- [x] 验证前端兼容性

## 🚀 部署说明

1. 执行数据库迁移脚本
2. 重启应用程序
3. 运行测试脚本验证功能
4. 检查前端弹窗显示是否正常
