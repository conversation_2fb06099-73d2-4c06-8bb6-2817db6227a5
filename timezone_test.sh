#!/bin/bash

# PostgreSQL 时区测试脚本
# 用于测试PostgreSQL中的时间是如何存储和读取的

echo "=== PostgreSQL 时区测试 ==="

# 连接PostgreSQL并执行查询
psql -h 192.168.1.36 -p 5432 -U jiance -d jlkj_iot_edge_dev_temp << EOF
-- 查看当前时区设置
SHOW timezone;

-- 查看一些示例数据的create_time
SELECT id, create_time, 
       create_time AT TIME ZONE 'UTC' AS create_time_utc,
       create_time AT TIME ZONE 'Asia/Shanghai' AS create_time_beijing
FROM zzzzz_log_device_report 
ORDER BY create_time DESC 
LIMIT 5;

-- 查看当前时间
SELECT 
    NOW() AS current_time,
    NOW() AT TIME ZONE 'UTC' AS current_time_utc,
    NOW() AT TIME ZONE 'Asia/Shanghai' AS current_time_beijing;
EOF

echo "=== 测试完成 ==="