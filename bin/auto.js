const { default: axios } = require('axios');
const dayjs = require('dayjs');
const { random } = require('lodash');
const puppeteer = require('puppeteer');

// 服务端地址
const HOST = 'http://localhost:8000';

const BASE_URL = 'http://localhost:8000/api/test';

// 需要复制的委托书
const ENTRUST_NAME = '0401-tal';
// 项目经理账号
const MANAGER_USERNAME = 'admin';



function waitForTimeout(ms) {
  // eslint-disable-next-line no-promise-executor-return
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function getReactFiberNode(element) {
  for (let key in element) {
    if (key.startsWith('__reactFiber')) {
      return element[key];
    }
  }
  return null;
}

// 递归遍历 FiberNode，查找 React 组件实例对象
function getReactComponentInstance(fiberNode) {
  if (fiberNode?.stateNode && fiberNode?.stateNode.hasOwnProperty('state')) {
    return fiberNode?.stateNode;
  }

  let child = fiberNode?.child;
  while (child) {
    let instance = getReactComponentInstance(child);
    if (instance) {
      return instance;
    }
    child = child.sibling;
  }

  return null;
}

/**
 * 获取用户的token
 * @param {*} userName 用户名
 */
async function getUserToken(username) {
  const res = await axios.post(`${BASE_URL}/fastLogin`, {
    username,
  });
  return Promise.resolve(res?.data?.data);
}

async function login(page, username) {
  // 获取token
  const token = await getUserToken(username);

  // 设置 localStorage
  await page.evaluate((token) => {
    localStorage.setItem('token', token);
  }, token);

  await page.goto(`${HOST}`);

  await page.reload();
}

/**
 * 找到按钮并提交
 * @param {*} page 页面对象
 * @param {*} selector 需要查询的父级选择器
 * @param {*} text 需要找到的按钮文字
 */
async function findBtnAndClick(page, selector, text) {
  const copyButtonHandle = await page.evaluateHandle(
    (selector, text) => {
      const buttons = document.querySelectorAll(selector);
      return Array.from(buttons).find((button) => button.textContent.trim() === text);
    },
    selector,
    text,
  );

  if (copyButtonHandle) {
    await copyButtonHandle?.click?.();
  }
}

/**
 * 提价委托书
 * @param {*} page
 * @returns name 新的委托书名称
 */
async function submitEntrust(page) {
  await page.goto(`${HOST}/#/attorney`);

  await page.reload();

  // 等待页面加载完成
  await page.waitForSelector('#entrustName');
  await page.waitForSelector('.ant-btn-primary');

  // 找到id为entrustName的表单进行输入
  await page.type('#entrustName', ENTRUST_NAME);
  // 等待1秒再提交，antd form表单提交
  await waitForTimeout(1000);
  await page.click('.ant-btn-primary');

  // 等待1秒再提交
  await waitForTimeout(1000);
  // 找到文字为复制的按钮并点击
  await findBtnAndClick(page, '.ant-table-row:nth-child(2) .text-primary', '复制');

  await waitForTimeout(1000);

  const newEntrustName = `${ENTRUST_NAME}-${dayjs().format('HHmm')}`;

  // 我现在要找到form的实例
  await page.evaluate(
    (value, getReactFiberNodeStr, getReactComponentInstanceStr) => {
      const getReactFiberNode = new Function(`return ${getReactFiberNodeStr}`)();
      const getReactComponentInstance = new Function(`return ${getReactComponentInstanceStr}`)();
      // 在这里，你可以使用 getReactFiberNode 和 getReactComponentInstance 函数
      const fiberNode = getReactFiberNode(document.querySelectorAll('form')[1]);

      const instance = getReactComponentInstance(fiberNode);

      instance?.context?.setFieldsValue({ entrustName: value });

      instance?.context?.submit();
    },
    newEntrustName,
    getReactFiberNode?.toString(),
    getReactComponentInstance?.toString(),
  );

  return newEntrustName;
}

// 受理人员受理分配检测室
async function acceptTask(page, entrustName) {
  // 样品列表，任务信息
  let samples,
    taskInfo = null;
  page.on('response', async (response) => {
    // 检查是否是你关心的接口
    if (response.url().endsWith('/sampleInfo/voPage')) {
      const res = await response.json(); // 获取接口数据
      samples = res?.data?.records;
    }

    if (response.url().endsWith('/taskInfo/voPage')) {
      const res = await response.json(); // 获取接口数据
      taskInfo = res?.data?.records?.[0];
    }
  });

  await page.goto(`${HOST}/#/task/list`);

  // 等待页面加载完成
  await page.waitForSelector('form');

  await waitForTimeout(3000);

  // 查询到委托书对应的任务
  await page.evaluate(
    async (entrustName, getReactFiberNodeStr, getReactComponentInstanceStr) => {
      const getReactFiberNode = new Function(`return ${getReactFiberNodeStr}`)();
      const getReactComponentInstance = new Function(`return ${getReactComponentInstanceStr}`)();
      // 获取到查询表单
      const fiberNode = getReactFiberNode(document.querySelectorAll('form')[0]);
      const instance = getReactComponentInstance(fiberNode);
      console.log(instance, entrustName, 'instance');

      instance?.context?.setFieldsValue({ name: entrustName });
      // 查询到委托书
      instance?.context?.submit();
    },
    entrustName,
    getReactFiberNode?.toString(),
    getReactComponentInstance?.toString(),
  );

  await waitForTimeout(1000);

  await findBtnAndClick(page, '.ant-table-row:nth-child(2) .ant-btn', '受理');

  await waitForTimeout(2000);

  // 受理任务
  await page.evaluate(async (getReactFiberNodeStr,getReactComponentInstanceStr) => {
    const getReactFiberNode = new Function(`return ${getReactFiberNodeStr}`)();
    const getReactComponentInstance = new Function(`return ${getReactComponentInstanceStr}`)();
    // 获取到查询表单
    const fiberNode = getReactFiberNode(document.querySelector('.ant-modal-body').querySelector('form'));
    const instance = getReactComponentInstance(fiberNode);

    instance?.context?.setFieldsValue({ tfAccept: true,remarks:'受理备注' });
    // 查询到委托书
    instance?.context?.submit();
  },getReactFiberNode?.toString(),getReactComponentInstance?.toString());

  await waitForTimeout(1000);

  // 点击编辑
  await findBtnAndClick(page, '.ant-table-row:nth-child(2) a', '编辑');

  // 等待编辑页面请求的接口请求完成
  await waitForTimeout(2000);

  // 点击选择样品按钮
  await findBtnAndClick(page, 'a', '选择样品');

  await waitForTimeout(1000);

  // 分配检测室
  await page.evaluate(
    async (getReactFiberNodeStr, getReactComponentInstanceStr, samples) => {
      const getReactFiberNode = new Function(`return ${getReactFiberNodeStr}`)();
      const getReactComponentInstance = new Function(`return ${getReactComponentInstanceStr}`)();
      // 获取到查询表单
      const fiberNode = getReactFiberNode(document.querySelectorAll('form')[1]);
      const instance = getReactComponentInstance(fiberNode);
      console.log(document.querySelectorAll('form')[1], instance, '分配检测室');

      instance?.context?.setFieldsValue({
        businessAreaType: 'CQXTY-CPLB-2023-02507',
        addTaskDeptIds: [
          {
            rowId: Date.now() + 10000,
            index: 0,
            deptId: '1',
            standardInfo:
              '《YD/T 2868-2020》移动通信系统多频段基站无源天线 《GB/T 9410-2008》移动通信天线通用技术规范',
            testMethod:
              '《YD/T 2868-2020》移动通信系统多频段基站无源天线 《GB/T 9410-2008》移动通信天线通用技术规范',
          },
          {
            rowId: Date.now() + 20000,
            index: 1,
            deptId: '2',
            standardInfo:
              '《YD/T 2868-2020》移动通信系统多频段基站无源天线 《GB/T 9410-2008》移动通信天线通用技术规范',
            testMethod:
              '《YD/T 2868-2020》移动通信系统多频段基站无源天线 《GB/T 9410-2008》移动通信天线通用技术规范',
          },
        ],
        sampleInfos: samples,
      });
      // 提交表单
      instance?.context?.submit();
    },
    getReactFiberNode?.toString(),
    getReactComponentInstance?.toString(),
    samples,
  );

  await waitForTimeout(1000);

  return {
    samples,
    taskInfo,
  };
}

/**
 * 领取样品
 * @param {*} page page对象
 * @param {*} sampleInfos 样品
 * @param {*} taskInfo
 */
async function receiveSample(page, sampleInfos, taskInfo) {
  await page.goto(`${HOST}/#/task/sample`);

  await page.reload();

  // 查询到样品
  await page.evaluate(
    async (number, getReactFiberNodeStr, getReactComponentInstanceStr) => {
      const getReactFiberNode = new Function(`return ${getReactFiberNodeStr}`)();
      const getReactComponentInstance = new Function(`return ${getReactComponentInstanceStr}`)();
      // 获取到查询表单
      const fiberNode = getReactFiberNode(document.querySelectorAll('form')[0]);
      const instance = getReactComponentInstance(fiberNode);

      instance?.context?.setFieldsValue({ number: number });
      // 查询到委托书
      instance?.context?.submit();
    },
    taskInfo?.number,
    getReactFiberNode?.toString(),
    getReactComponentInstance?.toString(),
  );



}

// 提交委托书 -> 受理任务 -> 分配多个检测室 ->不同检测室领取样品 -> 创建子项 -> 生成原始记录 -> 原始记录审核
async function main() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();

  // 打开你的应用
  await page.goto(HOST);
  // 项目经理发起
  await login(page, MANAGER_USERNAME);
  // 提交委托书
  const newEntrustName = await submitEntrust(page);
  // 受理人员登录
  await login(page, 'yuanenzhong');

  const { samples, taskInfo } = await acceptTask(page,newEntrustName);

  console.log(taskInfo,'taskINfo----')

  await receiveSample(page,samples,taskInfo);
}

main();
