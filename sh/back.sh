#!/bin/bash

# 设置 JAR 文件路径和日志文件路径
JAR_PATH="/home/<USER>/test_business_back-0.0.1-SNAPSHOT.jar"
LOG_PATH="/home/<USER>"
LOG_FILE="$LOG_PATH/output.log"  # 用于记录日志，但不打印到文件
PORT=7325  # 端口号

# 设置 Java 命令
JAVA_CMD="java -jar $JAR_PATH --spring.profiles.active=prod -Djna.library.path=/usr/local/lib --mqtt.broker-url=tcp://192.168.1.36:1883 --gw.template.file.path=/home/<USER>"

# 判断端口是否被占用
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 正在检查端口 $PORT 是否被占用..."
PID=$(lsof -t -i:$PORT)

if [ -n "$PID" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口 $PORT 被占用，正在停止占用进程 PID $PID..."
    # 停止占用端口的进程
    kill -9 $PID
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 进程 PID $PID 已停止，端口已释放。"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口 $PORT 没有被占用。"
fi

# 判断 JAR 程序是否已经在运行
PID=$(ps -ef | grep "$JAR_PATH" | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 程序已在运行，PID: $PID，正在停止程序..."
    # 停止正在运行的程序
    kill -9 $PID
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 程序已停止，重新启动应用程序..."
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 程序未在运行，准备启动..."
fi

# 启动 JAR 程序并将输出打印到控制台
nohup $JAVA_CMD > $LOG_FILE 2>&1 &

# 获取启动后的进程 ID 并记录
PID=$!
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 应用程序已启动，PID: $PID。"

# 等待 20 秒，确保程序启动
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 等待 20 秒以确保程序完全启动..."
sleep 20

# 记录最大等待时间
MAX_WAIT_TIME=60
CHECK_INTERVAL=10
elapsed_time=0

# 循环检查端口是否启动，最多等待 60 秒，每隔 10 秒检查一次
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始检查端口 $PORT 是否已启动，最多等待 $MAX_WAIT_TIME 秒..."
while [ $elapsed_time -lt $MAX_WAIT_TIME ]; do
    if lsof -i:$PORT; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口 $PORT 已启动，程序启动成功！"
        break
    fi
    # 每次检查间隔 10 秒
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 等待 $CHECK_INTERVAL 秒后重新检查端口..."
    sleep $CHECK_INTERVAL
    elapsed_time=$((elapsed_time + CHECK_INTERVAL))
done

if [ $elapsed_time -ge $MAX_WAIT_TIME ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口 $PORT 尚未启动，程序启动失败！"
    exit 1
fi

# 实时打印程序日志（从控制台获取）
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 以下是程序的实时日志，你可以随时按 Ctrl+C 停止查看日志。"
tail -f $LOG_FILE