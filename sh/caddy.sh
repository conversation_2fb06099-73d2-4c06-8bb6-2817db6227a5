#!/bin/bash

# 设置 Caddy 的路径和配置文件
CADDY_PATH="/usr/local/bin/caddy"  # Caddy 的安装路径
CADDY_CONFIG="/home/<USER>"     # Caddyfile 的配置文件路径
LOG_PATH="/home"                   # 日志文件目录
LOG_FILE="$LOG_PATH/caddy.log"     # Caddy 日志文件

# 检查 Caddy 是否正在运行
CADDY_PID=$(ps -ef | grep "$CADDY_PATH" | grep -v grep | awk '{print $2}')
if [ -n "$CADDY_PID" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Caddy 已经在运行，PID: $CADDY_PID，正在停止它..."
    # 停止 Caddy 服务
    kill -9 $CADDY_PID
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Caddy 已停止，PID: $CADDY_PID。"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] Caddy 当前没有在运行。"
fi

# 启动 Caddy 服务，确保它不会因终端关闭而停止
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动 Caddy 服务..."
setsid $CADDY_PATH run --config $CADDY_CONFIG > $LOG_FILE 2>&1 &
CADDY_PID=$!
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Caddy 已启动，PID: $CADDY_PID。"

# 实时打印 Caddy 的日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 以下是 Caddy 的实时日志，你可以随时按 Ctrl+C 停止查看日志。"
tail -f $LOG_FILE