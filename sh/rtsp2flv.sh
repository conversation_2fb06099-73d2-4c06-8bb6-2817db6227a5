#!/bin/bash

# 设置 JAR 文件路径和日志文件路径
JAR_PATH="/home/<USER>/rtsp-to-flv.jar"
LOG_PATH="/home/<USER>"
LOG_FILE="$LOG_PATH/application.log"  # 程序运行时的日志文件
PORT=8081  # 程序使用的端口号

# 设置 Java 命令
JAVA_CMD="java -jar $JAR_PATH --spring.profiles.active=prod"

# 判断端口是否被占用
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 正在检查端口 $PORT 是否被占用..."
PID=$(lsof -t -i:$PORT)

if [ -n "$PID" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口 $PORT 被占用，正在停止占用进程 PID $PID..."
    # 停止占用端口的进程
    kill -9 $PID
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 进程 PID $PID 已停止，端口已释放。"
else
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 端口 $PORT 没有被占用。"
fi

# 判断 JAR 程序是否已经在运行
PID=$(ps -ef | grep "$JAR_PATH" | grep -v grep | awk '{print $2}')
if [ -n "$PID" ]; then
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 程序已在运行，PID: $PID，无法重复启动。"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 以下是当前程序的实时日志："
    tail -n 100 $LOG_FILE  # 打印最近 100 行日志，方便查看
    exit 1
fi

# 启动 JAR 程序并将输出写入日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 启动应用程序..."
nohup $JAVA_CMD > $LOG_FILE 2>&1 &

# 获取启动后的进程 ID 并记录
PID=$!
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 应用程序已启动，PID: $PID。"

# 实时打印程序日志
echo "[$(date '+%Y-%m-%d %H:%M:%S')] 以下是程序的实时日志，你可以随时按 Ctrl+C 停止查看日志。"
tail -f $LOG_FILE