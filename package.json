{"name": "test-admin", "version": "0.0.1", "private": true, "description": "荆州物资检测数据直采模块", "scripts": {"build": "cross-env max build", "crud": "npx antd-pro-crud", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "prettierCrud": "prettier -c --write \"src/pages/crud/**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev UMI_UI=1 MOCK=none UMI_ENV=dev PORT=8000 max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@amap/amap-react": "^0.1.5", "@ant-design/fast-color": "^3.0.0", "@ant-design/icons": "^4.7.0", "@ant-design/plots": "^1.2.5", "@ant-design/pro-components": "^2.6.52", "@ant-design/pro-provider": "^2.4.2", "@ant-design/pro-table": "^3.12.1", "@ant-design/use-emotion-css": "1.0.4", "@tinymce/tinymce-react": "^4.3.0", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-color": "^3.0.6", "@types/react-window": "^1.8.5", "@umijs/route-utils": "^2.1.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "add": "^2.0.6", "ahooks": "^3.7.7", "antd": "^5.17.0", "antd-pro-crud": "^0.0.42", "axios": "^1.3.1", "classnames": "^2.3.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "decimal.js-light": "^2.5.1", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "eventemitter3": "^5.0.0", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "fs": "^0.0.1-security", "handlebars": "^4.7.7", "immer": "^10.0.2", "lib-flexible": "^0.3.2", "lodash": "^4.17.21", "mathjs": "^14.4.0", "moment": "^2.29.4", "node-rsa": "^1.1.1", "omit.js": "^2.0.2", "path": "^0.12.7", "path-to-regexp": "^6.2.0", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^6.0.0", "puppeteer": "^22.6.1", "qrcode.react": "^3.1.0", "qs": "^6.11.2", "quill-image-uploader": "^1.2.4", "rc-menu": "^9.6.4", "rc-util": "^5.24.4", "react": "^18.0.0", "react-activation": "^0.12.4", "react-amap": "^1.2.8", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-dev-inspector": "^1.8.1", "react-dom": "^18.0.0", "react-helmet-async": "^1.3.0", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-quill": "^2.0.0", "react-sticky-box": "^2.0.4", "react-window": "^1.8.8", "spark-md5": "^3.0.2", "tinymce": "^6.3.2", "umi-plugin-keep-alive": "^0.0.1-beta.35", "use-dynamic-refs": "^1.0.0", "video.js": "^8.22.0", "yarn": "^1.22.22"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.13", "@ant-design/pro-cli": "^2.1.0", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/crypto-js": "^4.1.1", "@types/express": "^4.17.14", "@types/file-saver": "^2.0.5", "@types/history": "^4.7.11", "@types/jest": "^29.2.1", "@types/lodash": "^4.14.186", "@types/mime": "^4.0.0", "@types/node-rsa": "^1.1.1", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-helmet": "^6.1.5", "@types/spark-md5": "^3.0.3", "@umijs/lint": "^4.0.34", "@umijs/max": "^4.0.66", "@umijs/preset-ui": "^2.2.9", "colors-console": "^1.0.3", "cross-env": "^7.0.3", "eslint": "^8.0.0", "express": "^4.18.2", "fast-typescript-to-jsonschema": "^0.0.9", "gh-pages": "^3.2.0", "glob": "^10.2.2", "husky": "^7.0.4", "jest": "^29.2.2", "jest-environment-jsdom": "^29.2.2", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "prettier": "^2.7.1", "swagger-ui-dist": "^4.14.2", "ts-node": "^10.9.1", "typescript": "^4.8.4", "umi-presets-pro": "^2.0.0"}, "engines": {"node": ">=12.0.0"}}