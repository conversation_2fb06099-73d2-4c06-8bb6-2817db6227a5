#!/bin/bash

# 测试设备数据弹窗功能优化
echo "🚀 开始测试设备数据弹窗功能优化"
echo "=================================="

# 设置API基础URL
BASE_URL="http://localhost:7321/test"

echo ""
echo "📋 测试用例1: 设备对接数据管理API - POST方式（查询最新数据）"
echo "-----------------------------------"

curl -X POST "${BASE_URL}/device-data/query" \
  -H "Content-Type: application/json" \
  -d '{"deviceCode": "wanceDevice"}' | jq '.'

echo ""
echo "📋 测试用例2: 设备配置查询API"
echo "-----------------------------------"

curl -X GET "${BASE_URL}/device-data/config/wanceDevice" | jq '.'

echo ""
echo "✅ 基础测试完成"
