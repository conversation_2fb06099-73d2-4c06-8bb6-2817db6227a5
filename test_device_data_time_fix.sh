#!/bin/bash

# 测试设备数据时间显示功能修复
# 验证时间类型转换是否正常工作

echo "🚀 开始测试设备数据时间显示功能"
echo "=================================="

# 设置API基础URL
BASE_URL="http://localhost:7325/test"

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo ""
    echo "📋 测试: $description"
    echo "-----------------------------------"
    
    if [ "$method" = "GET" ]; then
        echo "🔍 请求: GET $url"
        response=$(curl -s -X GET "$url" -H "Content-Type: application/json")
    else
        echo "🔍 请求: POST $url"
        echo "📤 请求体: $data"
        response=$(curl -s -X POST "$url" -H "Content-Type: application/json" -d "$data")
    fi
    
    echo "📥 响应:"
    echo "$response" | jq '.'
    
    # 检查是否包含dataTime字段
    if echo "$response" | jq -e '.data[0].dataTime' > /dev/null 2>&1; then
        echo "✅ 包含dataTime字段"
        dataTime=$(echo "$response" | jq -r '.data[0].dataTime')
        echo "⏰ 数据时间: $dataTime"
    else
        echo "❌ 未找到dataTime字段"
    fi
    
    # 检查是否有错误
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        success=$(echo "$response" | jq -r '.success')
        if [ "$success" = "true" ]; then
            echo "✅ 请求成功"
        else
            echo "❌ 请求失败"
            echo "错误信息: $(echo "$response" | jq -r '.message')"
        fi
    fi
}

# 1. 测试万测设备数据（POST方式）
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "wanceDevice"}' \
    "POST方式查询万测设备最新数据（测试时间字段）"

# 2. 测试思创设备数据
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "sichuang"}' \
    "查询思创设备最新数据（测试时间字段）"

# 3. 测试电机设备数据
test_api "POST" \
    "$BASE_URL/device-data/query" \
    '{"deviceCode": "eleMachine51"}' \
    "查询电机设备最新数据（测试时间字段）"

echo ""
echo "🎯 测试完成"
echo "=================================="
echo "请检查以上输出，确认："
echo "1. API响应成功（success: true）"
echo "2. 响应数据中包含dataTime字段"
echo "3. dataTime字段值格式正确"
echo "4. 没有类型转换错误"
