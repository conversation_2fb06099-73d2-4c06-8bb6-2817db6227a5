# 设备数据查询接口修改说明

## 📋 修改概述

本次修改解决了 `/test/device-data/query` 接口中数据获取逻辑的问题。原来的代码从动态表名（如 `wance_dc_resistance_data`）获取数据，现在修改为从 `zzzzz_log_device_function_call` 表获取设备数据。

## 🔧 主要修改内容

### 1. 修改 DeviceDataQueryMapper

**文件**: `src/main/java/com/westcatr/rd/testbusiness/business/devicedata/mapper/DeviceDataQueryMapper.java`

**修改前**:
```java
@Select("SELECT * FROM ${tableName} WHERE testdate >= #{startTime} AND testdate <= #{endTime} ORDER BY testdate DESC LIMIT 1")
Map<String, Object> selectLatestDataByTable(@Param("tableName") String tableName,
                                            @Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);
```

**修改后**:
```java
@Select("SELECT id, device_id, product, output_params, async_call, create_time " +
        "FROM zzzzz_log_device_function_call " +
        "WHERE (device_id = #{deviceCode} OR product = #{deviceCode}) " +
        "AND create_time >= #{startTime} AND create_time <= #{endTime} " +
        "AND output_params IS NOT NULL " +
        "AND JSON_EXTRACT(output_params, '$._status') != 500 " +
        "AND JSON_EXTRACT(output_params, '$._msg') IS NULL " +
        "AND JSON_EXTRACT(output_params, '$.body') IS NOT NULL " +
        "ORDER BY create_time DESC LIMIT 1")
Map<String, Object> selectLatestDataByDeviceCode(@Param("deviceCode") String deviceCode,
                                                 @Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime);
```

**关键改进**:
- 增加了错误响应过滤条件
- 过滤 `_status = 500` 的错误响应
- 过滤包含 `_msg` 字段的错误响应
- 确保 `body` 字段存在且不为空

### 2. 修改 DeviceDataServiceImpl

**文件**: `src/main/java/com/westcatr/rd/testbusiness/business/devicedata/service/impl/DeviceDataServiceImpl.java`

#### 2.1 查询逻辑修改

**修改前**: 基于动态表名查询
```java
data = deviceDataQueryMapper.selectLatestDataByTable(
        experiment.getTableName(), request.getStartTime(), request.getEndTime());
```

**修改后**: 基于设备代码查询
```java
data = deviceDataQueryMapper.selectLatestDataByDeviceCode(
        request.getDeviceCode(), request.getStartTime(), request.getEndTime());
```

#### 2.2 错误响应过滤逻辑

**新增**: `isErrorResponse` 方法，用于检查和过滤错误响应
```java
private boolean isErrorResponse(Map<String, Object> outputParams) {
    // 检查是否包含错误状态码
    Object status = outputParams.get("_status");
    if (status != null && (status.equals(500) || "500".equals(status.toString()))) {
        return true;
    }

    // 检查是否包含错误消息
    Object msg = outputParams.get("_msg");
    if (msg != null && StringUtils.hasText(msg.toString())) {
        String msgStr = msg.toString();
        // 检查常见的错误消息
        if (msgStr.contains("连接器未连接") ||
            msgStr.contains("连接器未启动") ||
            msgStr.contains("错误") ||
            msgStr.contains("失败")) {
            return true;
        }
    }

    // 检查是否缺少有效的 body 字段
    Object body = outputParams.get("body");
    if (body == null || !StringUtils.hasText(body.toString())) {
        return true;
    }

    return false;
}
```

#### 2.3 数据转换逻辑修改

**新增**: `extractTestData` 方法，用于从 `output_params` JSON 中提取试验数据
```java
private Map<String, Object> extractTestData(Map<String, Object> outputParams, ExperimentConfig experiment) {
    // 检查是否有 body 字段
    if (outputParams.containsKey("body")) {
        Object bodyObj = outputParams.get("body");

        if (bodyObj instanceof String) {
            String bodyStr = (String) bodyObj;

            // 尝试解析 body 字符串为 JSON 数组或对象
            if (bodyStr.startsWith("[") && bodyStr.endsWith("]")) {
                List<Map<String, Object>> bodyList = objectMapper.readValue(bodyStr,
                        new TypeReference<List<Map<String, Object>>>() {});
                if (!bodyList.isEmpty()) {
                    return bodyList.get(0); // 返回第一条数据
                }
            }
            // ... 其他解析逻辑
        }
    }
    return outputParams;
}
```

## 📊 数据结构对比

### 修改前的数据结构
从动态表（如 `wance_dc_resistance_data`）直接获取字段：
```json
{
  "r1a": "0.123",
  "r1b": "0.124",
  "r1c": "0.125",
  "temp": "25.5"
}
```

### 修改后的数据结构

#### 有效响应格式
从 `zzzzz_log_device_function_call` 表的 `output_params` 字段解析：
```json
{
  "id": 12345,
  "device_id": "wanceDevice",
  "product": "accessId",
  "output_params": "{\"body\":\"[{\\\"A相直流电阻\\\":\\\"0.123\\\",\\\"B相直流电阻\\\":\\\"0.124\\\"}]\",\"_status\":200}",
  "create_time": "2025-06-14 10:30:00"
}
```

#### 错误响应格式（会被过滤）
```json
{
  "id": 12346,
  "device_id": "wanceDevice",
  "product": "accessId",
  "output_params": "{\"_msg\":\"连接器未连接！\",\"_status\":500}",
  "create_time": "2025-06-14 10:25:00"
}
```

#### 解析后的试验数据
```json
{
  "A相直流电阻": "0.123",
  "B相直流电阻": "0.124",
  "C相直流电阻": "0.125",
  "温度": "25.5"
}
```

## 🔄 配置映射机制保持不变

配置格式依然使用原有的 JSON 结构：
```json
{
  "experiments": [
    {
      "name": "直阻试验",
      "code": "DTS_T_RZZLDZCL",
      "tableName": "DTS_T_RZZLDZCL",  // 此字段现在不再用于查询表，仅作标识
      "mapping": {
        "A相直阻": "A相直流电阻",      // 显示名称 -> 实际字段名
        "B相直阻": "B相直流电阻",
        "C相直阻": "C相直流电阻",
        "试验温度": "温度"
      }
    }
  ]
}
```

## 🧪 测试验证

### 1. 运行基础测试脚本
```bash
chmod +x test_device_data_query_fix.sh
./test_device_data_query_fix.sh
```

### 2. 运行错误响应过滤测试
```bash
chmod +x test_error_response_filter.sh
./test_error_response_filter.sh
```

### 3. 手动测试示例
```bash
# 查询万测设备最新数据
curl -X POST "http://localhost:7321/test/device-data/query" \
  -H "Content-Type: application/json" \
  -d '{"deviceCode": "wanceDevice"}'

# 查询思创设备数据
curl -X POST "http://localhost:7321/test/device-data/query" \
  -H "Content-Type: application/json" \
  -d '{"deviceCode": "sichuang"}'
```

## ⚠️ 注意事项

1. **设备代码匹配**: 现在通过 `device_id` 或 `product` 字段匹配设备代码
2. **数据解析**: 需要解析 `output_params` JSON 字段获取试验数据
3. **时间字段**: 使用 `create_time` 字段进行时间范围过滤
4. **配置兼容**: 保持现有配置格式不变，只修改底层数据获取逻辑

## 🔍 故障排查

如果接口调用失败，请检查：

1. **数据库表**: 确认 `zzzzz_log_device_function_call` 表存在且有数据
2. **设备配置**: 确认 `bu_device_data_config` 表有对应的设备配置
3. **数据格式**: 确认 `output_params` 字段包含有效的 JSON 数据
4. **设备代码**: 确认设备代码与数据库中的 `device_id` 或 `product` 字段匹配

## 📈 性能优化

1. **索引优化**: 已在 `zzzzz_log_device_function_call` 表上创建相关索引
2. **查询优化**: 只查询必要的字段，减少数据传输量
3. **缓存机制**: 可考虑对设备配置进行缓存优化

## 🚀 后续扩展

1. **多设备支持**: 可扩展支持批量查询多个设备
2. **数据聚合**: 可添加数据统计和聚合功能
3. **实时数据**: 可结合 WebSocket 实现实时数据推送
