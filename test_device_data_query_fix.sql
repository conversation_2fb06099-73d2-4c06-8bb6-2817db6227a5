-- 测试设备数据查询修正效果
-- 作者: liusheng
-- 日期: 2025-06-14

-- 1. 检查表基本信息
SELECT '=== 表基本信息 ===' as info;
SELECT COUNT(*) as total_records FROM zzzzz_log_device_function_call;
SELECT COUNT(*) as non_null_output_params FROM zzzzz_log_device_function_call WHERE output_params IS NOT NULL;

-- 2. 分析数据格式分布
SELECT '=== 数据格式分析 ===' as info;
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN JSON_EXTRACT(output_params, '$._status') IS NOT NULL THEN 1 ELSE 0 END) as has_status,
    SUM(CASE WHEN JSON_EXTRACT(output_params, '$._status') = 500 THEN 1 ELSE 0 END) as status_500,
    SUM(CASE WHEN JSON_EXTRACT(output_params, '$._msg') IS NOT NULL THEN 1 ELSE 0 END) as has_msg,
    SUM(CASE WHEN JSON_EXTRACT(output_params, '$.body') IS NOT NULL THEN 1 ELSE 0 END) as has_body
FROM zzzzz_log_device_function_call 
WHERE output_params IS NOT NULL;

-- 3. 对比修正前后的查询结果
SELECT '=== 修正前后查询结果对比 ===' as info;

-- 修正前的查询（错误的）
SELECT 'BEFORE_FIX' as query_type, COUNT(*) as result_count
FROM zzzzz_log_device_function_call 
WHERE output_params IS NOT NULL 
AND JSON_EXTRACT(output_params, '$._status') != 500 
AND JSON_EXTRACT(output_params, '$._msg') IS NULL 
AND JSON_EXTRACT(output_params, '$.body') IS NOT NULL

UNION ALL

-- 修正后的查询（正确的）
SELECT 'AFTER_FIX' as query_type, COUNT(*) as result_count
FROM zzzzz_log_device_function_call 
WHERE output_params IS NOT NULL 
AND (JSON_EXTRACT(output_params, '$._status') IS NULL OR JSON_EXTRACT(output_params, '$._status') != 500)
AND JSON_EXTRACT(output_params, '$._msg') IS NULL 
AND JSON_EXTRACT(output_params, '$.body') IS NOT NULL;

-- 4. 各设备的有效数据统计
SELECT '=== 各设备有效数据统计 ===' as info;
SELECT 
    device_id,
    COUNT(*) as total_records,
    SUM(CASE WHEN output_params IS NOT NULL 
             AND (JSON_EXTRACT(output_params, '$._status') IS NULL OR JSON_EXTRACT(output_params, '$._status') != 500)
             AND JSON_EXTRACT(output_params, '$._msg') IS NULL 
             AND JSON_EXTRACT(output_params, '$.body') IS NOT NULL 
        THEN 1 ELSE 0 END) as valid_records
FROM zzzzz_log_device_function_call 
WHERE output_params IS NOT NULL
GROUP BY device_id 
ORDER BY valid_records DESC;

-- 5. 测试具体设备查询
SELECT '=== 测试 wanceDevice 设备查询 ===' as info;
SELECT COUNT(*) as wanceDevice_valid_count
FROM zzzzz_log_device_function_call 
WHERE device_id = 'wanceDevice'
AND output_params IS NOT NULL 
AND (JSON_EXTRACT(output_params, '$._status') IS NULL OR JSON_EXTRACT(output_params, '$._status') != 500)
AND JSON_EXTRACT(output_params, '$._msg') IS NULL 
AND JSON_EXTRACT(output_params, '$.body') IS NOT NULL;

-- 6. 查看最新的有效数据样本
SELECT '=== 最新有效数据样本 ===' as info;
SELECT 
    device_id, 
    product, 
    LEFT(output_params, 100) as output_preview,
    create_time
FROM zzzzz_log_device_function_call 
WHERE output_params IS NOT NULL 
AND (JSON_EXTRACT(output_params, '$._status') IS NULL OR JSON_EXTRACT(output_params, '$._status') != 500)
AND JSON_EXTRACT(output_params, '$._msg') IS NULL 
AND JSON_EXTRACT(output_params, '$.body') IS NOT NULL
ORDER BY create_time DESC 
LIMIT 5;
