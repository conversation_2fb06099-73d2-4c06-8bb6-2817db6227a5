/**
 * 用户对象
 * swagger定义不全,这里补充一下
 */
declare type IUser = BASE.IUser & {
  /** 扩展数据 */
  extendData: {
    /** 用户对象 */
    user: BASE.OrgUserInfoVO;
    /** 是否完成上年度自评 */
    entselfAssessmentComplete: boolean;
    orgUserRoleFullNames: string[];
    /** 企业id */
    entId: string;
    /** 企业信息 */
    orgInfo: BASE.BasicCompanyVO;
    /** 组织结构信息 */
    orgDeptInfos?: OrgDeptInfoType[];
    photoUrl?:string;
  };
};

type OrgDeptInfoType = {
  deptName: string;
  id: string;
};
