declare type Action = {
  /** 权限名称 */
  name: string;
  /** 权限key */
  auth: string;
  /** 绑定的后端权限列表 格式 请求方式:请求地址 */
  apiList?: string[];
  /** 子权限 */
  children?: Action[];
}

/**
 * 路由配置定义
 * 源码未定义完整,故此重新定义
 */
declare type RouteData = {
  /** 路径 */
  path?: string;
  /** 要渲染的组件 */
  component?: string | (() => any);
  /**
   * 要渲染的组件
   * @see https://procomponents.ant.design/components/layout#packages-layout-src-components-layout-demo-multiplemenuonepath
   * */
  indexRoute?: {
    /** 要渲染的组件 */
    component?: string;
  };
  /** 菜单名称 */
  name?: string;
  /**
   * 是否是增删改查通用页面
   * true: 生成相应的权限list/add/delete/edit/detail
   */
  crud?:
    | boolean
    | {
        list?: boolean;
        add?: boolean;
        delete?: boolean;
        edit?: boolean;
        detail?: boolean;
        export?: boolean;
      };
  /**
   * 权限校验函数
   * 参考
   * 1. src/access.ts
   * 2. https://umijs.org/zh-CN/plugins/plugin-access
   */
  /** 配置了crud，根据crudKey生成按钮下的接口 */
  crudKey?: string;
  access?: string;
  /** 权限标识 */
  auth?: string;
  /** 图标 */
  icon?: React.ReactNode;
  /** 绑定的后端权限列表 格式 请求方式:请求地址 */
  apiList?: string[];
  /** 按钮权限 */
  actionList?: Action[];
  /**
   * 配置路由的高阶组件封装
   * https://umijs.org/zh-CN/docs/routing#wrappers
   */
  wrappers?: string[];
  /** 重定向地址 */
  redirect?: string;
  /** 绝对匹配 */
  exact?: boolean;
  /** 是否包含在pro layout布局内, 例如登录页面 */
  layout?: boolean;
  /** 新页面打开 */
  target?: '_blank';

  /** 不展示顶栏 */
  headerRender?: boolean;
  /** 不展示页脚 */
  footerRender?: boolean;
  /** 不展示菜单 */
  menuRender?: boolean;
  /** 不展示菜单顶栏 */
  menuHeaderRender?: boolean;
  /** 隐藏子菜单 */
  hideChildrenInMenu?: boolean;
  /** 隐藏自己和子菜单 */
  hideInMenu?: boolean;
  /** 隐藏全局tab */
  hideInTab?: boolean;
  /** 在面包屑中隐藏 */
  hideInBreadcrumb?: boolean;
  /** 子项往上提，仍旧展示 */
  flatMenu?: boolean;

  routes?: RouteData[];

  // [k: string]: any;
};
