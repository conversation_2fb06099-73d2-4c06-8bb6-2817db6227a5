import type { getInitialState } from '@/app';
import type { ValueType } from '@/valueType';
import type { ProSchemaValueEnumType } from '@ant-design/pro-components';

declare global {
  /** ====== 辅助类型/工具方法 ====== */
  /**  强制判断非null/undefined */
  declare type NonNullable<T> = T extends null | undefined ? never : T;

  /**  强制判断非null */
  declare type NonNull<T> = T extends null ? never : T;

  /**  强制判断非undefined */
  declare type NonUndefined<T> = T extends undefined ? never : T;

  /**  强制判断非boolean */
  declare type NonBoolean<T> = T extends boolean ? never : T;

  /**  强制判断非array */
  declare type NonArray<T> = T extends Array ? never : T;

  /**  强制判断非传入的泛型类型 */
  declare type Non<T, R> = T extends R ? never : T;

  /** 获取Promise.then返回值 */
  declare type ThenReturn<T> = T extends Promise<infer U> ? U : T;

  /** 工具类型 为布尔或者传入的反省 */
  declare type WithBool<T> = boolean | T;

  /** 获取函数参数类型 */
  declare type ArgumentsType<T extends (...args: any[]) => any> = T extends (
    ...args: infer A
  ) => any
    ? A
    : never;

  /** 获取数组元素类型 */
  declare type ArrayElement<ArrayType extends readonly unknown[]> =
    ArrayType extends readonly (infer ElementType)[] ? ElementType : never;
  /** ====== end 辅助类型/工具方法 ====== */

  /** ====== webpack.DefinePlugin插件声明全局变量 ====== */
  /** 样式类型前缀 */
  declare const PREFIX: string;
  /** 网站标题 */
  declare const APP_TITLE: string;
  /** 版权信息 */
  declare const APP_COPYRIGHT: string;
  /** 网站描述 */
  declare const APP_DESCRIPTION: string;
  /** 网站关键字 */
  declare const APP_KEYWORDS: string;
  /** 技术支持 */
  declare const APP_SUPPORT: string;
  /** 服务器地址 */
  declare const SERVER_URL: string;
  /** 接口代理标识 */
  declare const PROXY_KEY: string;
  /** ====== end webpack.DefinePlugin插件声明全局变量 ====== */
  /** 海康视频地址 */
  declare const HIK_VIDEO_URL: string;

  /**
   * 全局数据
   * 数据来源: app.tsx/getInitialState()
   */
  declare type GlobalStore = ThenReturn<ReturnType<typeof getInitialState>>;

  /** antd pro枚举变量 */
  declare type ValueEnumMap<T extends number | string = string> = Map<T, ProSchemaValueEnumType>;

  /**
   * 自定义valueType
   * 地址 /src/valueType
   */
  declare type GlobalValueType = ValueType;

  /**
   * 服务器返回格式
   */
  declare interface ServerResponse<T = unknown> {
    /** 接口状态 */
    success: boolean;
    /** 错误编码 */
    status: 200 | number;
    /** 错误说明 */
    message: string;
    /** 接口返回时间戳 */
    timestamp: number;
    /** 实体 */
    data: undefined | T;
  }
}
