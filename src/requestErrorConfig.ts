﻿import { history, RequestConfig, RequestOptions } from '@umijs/max';
import { message as antMessage, notification } from 'antd';

// 错误处理方案： 错误类型
enum ErrorShowType {
  SILENT = 0,
  WARN_MESSAGE = 1,
  ERROR_MESSAGE = 2,
  NOTIFICATION = 3,
  REDIRECT = 9,
}
// 与后端约定的响应数据格式
export interface ResponseStructure<T = any> {
  /** 成功标志 */
  success: boolean;
  /** 结果对象 */
  data: T;
  /** 状态码 */
  status: number;
  /** 返回消息 */
  message: string;
  /** 时间戳 */
  timestamp: number;
  showType?: ErrorShowType;
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // errorThrower: (res: any) => {
    //   console.log(res, '进入errorThrower---');
    //   const { success, data, errorCode, errorMessage, showType } = res;
    //   if (!success) {
    //     const error: any = new Error(errorMessage);
    //     error.name = 'BizError';
    //     error.info = { errorCode, errorMessage, showType, data };
    //     throw error; // 抛出自制的错误
    //   }
    // },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo) {
          const { message, status } = errorInfo;
          switch (errorInfo.showType) {
            case ErrorShowType.SILENT:
              // do nothing
              break;
            case ErrorShowType.WARN_MESSAGE:
              antMessage.warning(message);
              break;
            case ErrorShowType.ERROR_MESSAGE:
              antMessage.error(message);
              break;
            case ErrorShowType.NOTIFICATION:
              notification.open({
                description: message,
                message: status,
              });
              break;
            case ErrorShowType.REDIRECT:
              // TODO: redirect
              break;
            default:
              antMessage.error(message);
          }
        }
      } else if (error.response) {
        if (error?.response?.status === 401) {
          localStorage.removeItem('token');
          setTimeout(() => {
            history.push('/user/login');
          }, 0);
        }
        if (error.response.data instanceof Blob) {
          const reader = new FileReader();
          reader.readAsText(error.response.data);
          reader.onload = (result) => {
            antMessage.error(JSON.parse(result?.target?.result as string)?.message || '');
          };
        } else {
          if (error.response.data && error.response.data.message) {
            antMessage.error(error.response.data.message);
          } else {
            // Axios 的错误
            // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
            antMessage.error(`Response status:${error.response.status}`);
          }
        }
        
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        antMessage.error('None response! Please retry.');
      } else {
        // 发送请求时出了点问题
        antMessage.error('Request error, please retry.');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      const token = localStorage.getItem('token');

      // 添加统一请求前缀
      const url = config?.url?.includes(`${PROXY_KEY}test`)
        ? config?.url
        : `${PROXY_KEY}test${config?.url}`;

      // 拦截请求配置，进行个性化处理。
      // const url = config?.url?.concat('?token = 123');
      return {
        ...config,
        headers: {
          ...config.headers,
          token,
        },
        url,
      };
      // return { ...config, url };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
    
      const bizData = response.data as ServerResponse;
      if(bizData instanceof Blob){
        return response
      }

      if (!bizData) {
        throw new Error('服务器异常，请稍后再试！');
      }

      // 登录失效
      if (bizData.status === 401) {
        antMessage.error('登录失效，请重新登录');
        localStorage.removeItem('token');
        setTimeout(() => {
          history.push('/user/login');
        }, 0);
      }

      if (!bizData.success) {
        const error = new Error(bizData.message || '服务器异常') as Error & {
          info: ResponseStructure;
        };

        error.name = 'BizError';
        error.info = {
          /** 成功标志 */
          success: bizData.success,
          /** 结果对象 */
          data: bizData.data,
          /** 状态码 */
          status: bizData.status,
          /** 返回消息 */
          message: bizData.message,
          /** 时间戳 */
          timestamp: Date.now(),
          showType: ErrorShowType.ERROR_MESSAGE,
        };

        throw error; // 抛出自制的错误
      }

      return response;
    },
  ],
};
