// @ts-nocheck
import { useRequest } from 'ahooks';
import type { Options, Result as AhooksResult, Service } from 'ahooks/lib/useRequest/src/types';
import { Modal, Popconfirm } from 'antd';
import React, { useImperativeHandle, useState } from 'react';

type Result<TData, TParams extends any[]> = AhooksResult<TData, TParams> & {
  success: boolean;
};

export type WithRequestConfig = {
  /**
   * 是否提示
   * false:      不提示,点击直接调用接口
   * true:       弹框提醒
   * popconfirm: 气泡确认框
   */
  confirm?: boolean | 'popconfirm';
  /** 提示内容 */
  confirmText?: React.ReactNode;
  /** 提示属性 */
  popconfirmpProps?: Partial<React.ComponentProps<typeof Popconfirm>>;
  /**
   * 点击获取数据
   */
  clickable?: boolean;
  onSuccess?: (res?: any, params?: any) => void;
  onFail?: (err: any) => void;
};

export type WithRequestRef<TData = any, TParams extends any[] = any[]> = Result<TData, TParams>;

export type WithRequestProps<Props, TData, TParams extends any[]> = WithRequestConfig & {
  requestRef?: React.Ref<WithRequestRef<TData, TParams>>;
  request?: {
    service?: Service<TData, TParams>;
    options?: Options<TData, TParams>;
    format?: (request: Result<TData, TParams>, props: Props) => Props;
  };
  onSuccess?: (res?: TData, params?: TParams) => void;
  onFail?: (err: any) => void;
};

function withRequest(_withRequestConfig: WithRequestConfig = {}) {
  return function <Props extends object = {}>(WrapComponent: React.ComponentType<Props>) {
    return function<TData = any, TParams extends any[] = any[]>({
      request: propsRequest = {},
      requestRef,
      onSuccess,
      onFail,

      confirm,
      confirmText,
      popconfirmpProps,
      clickable,

      ...props
    }: Props & WithRequestProps<Props, TData, TParams>){
      // 合并参数
      const withRequestConfig = {
        confirm: confirm || _withRequestConfig.confirm,
        confirmText: confirmText || _withRequestConfig.confirmText || '确定此项操作吗?',
        popconfirmpProps: popconfirmpProps || _withRequestConfig.popconfirmpProps,
        clickable: clickable || _withRequestConfig.clickable,
      };

      const [success, setSuccess] = useState(false);
      const request = useRequest<TData, TParams>(propsRequest.service!, {
        /** 默认不主动请求 */
        manual: true,
        skipErrorHandler: true,
        ...propsRequest.options,
        onBefore: (params) => {
          setSuccess(false);
          propsRequest.options?.onBefore?.(data, params);
        },
        onSuccess: (data, params) => {
          setSuccess(true);
          onSuccess?.(data, params);
          withRequestConfig.onSuccess?.(data, params);
          propsRequest.options?.onSuccess?.(data, params);
        },
        onError: (err) => {
          // message.error(err.message);
          onFail?.(err);
          withRequestConfig.onFail?.(err);
          propsRequest.options?.onFail?.(err);
        },
      }) as Result<TData, TParams>;
      const [visiblePopconfirm, setPopconfirmVisible] = useState(false);
      // 挂载success状态
      request.success = success;

      useImperativeHandle(requestRef, () => {
        return request;
      });

      const onClick = props.onClick;

      const handleClick = (...args: any) => {
        onClick?.call(props, ...args);

        if (withRequestConfig.confirm === 'popconfirm') {
          return;
        }

        if (withRequestConfig.confirm === true) {
          const m = Modal.confirm({
            title: withRequestConfig.confirmText,
            onOk: async () => {
              m.update({
                cancelButtonProps: {
                  disabled: true,
                },
              });

              try {
                await request.run();
              } catch (e) {}

              m.update({
                cancelButtonProps: {
                  disabled: false,
                },
              });
            },
          });

          return;
        }

        request.run();
      };

      const transProps =
        propsRequest.service && propsRequest.format
          ? propsRequest.format(request, props as Props)
          : props;

      // 绑定click事件
      if (withRequestConfig.clickable && request) {
        (transProps as any).onClick = handleClick;
      }

      const render = <WrapComponent {...(transProps as Props)} />;

      if (
        withRequestConfig.clickable &&
        propsRequest.service &&
        withRequestConfig.confirm === 'popconfirm'
      ) {
        return (
          <Popconfirm
            title={withRequestConfig.confirmText}
            open={visiblePopconfirm}
            onOpenChange={(e) => {
              if (withRequestConfig.confirm === 'popconfirm') {
                if (!e && request.loading) {
                  return;
                }

                setPopconfirmVisible(e);
              }
            }}
            onConfirm={() => {
              request.run();
            }}
            okText="确定"
            cancelText="取消"
            {...withRequestConfig.popconfirmpProps}
          >
            {render}
          </Popconfirm>
        );
      }

      return render;
    };
  };
}

export { withRequest };
