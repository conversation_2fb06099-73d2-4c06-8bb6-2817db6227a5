import { ModalProps } from 'antd';
import React, { useCallback, useState } from 'react';

function useModal<T>(ModalComponent: React.ComponentType<ModalProps>) {
  const [open, setOpen] = useState(false);
  const [componentProps, setComponentProps] = useState<T>();

  const showModal = (props: T & ModalProps) => {
    setComponentProps(props);
    setOpen(true);
  };

  const handleOk = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const ModalContent = useCallback(() => {
    return (
      <ModalComponent open={open} onOk={handleOk} onCancel={handleCancel} {...componentProps} />
    );
  }, [componentProps, open]);

  return {
    showModal,
    ModalContent,
  };
}

export default useModal;
