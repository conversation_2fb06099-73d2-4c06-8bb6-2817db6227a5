import { message, Upload, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { RcFile } from 'antd/lib/upload';

interface TemplateUploadProps {
    disabled?: boolean;
    standardId?: number | string;
    fileList?: any[];
    onSuccess?: (file: any) => void;
    uploadAction: (params: any, body: FormData) => Promise<any>;
    buttonText?: string;
    accept?: string;
}

const TemplateUpload: React.FC<TemplateUploadProps> = ({
    disabled = false,
    standardId,
    fileList = [],
    onSuccess,
    uploadAction,
    buttonText = '上传模板文件',
    accept = '.docx,.doc'
}) => {
    const [uploading, setUploading] = useState<boolean>(false);

    const handleCustomRequest = async (options: any) => {
        const { file, onSuccess: onUploadSuccess, onError } = options;

        if (!standardId) {
            message.warning('请先保存标准信息后再上传模板文件');
            onError('请先保存标准信息后再上传模板文件');
            return;
        }

        setUploading(true);
        try {
            // 构造FormData
            const formData = new FormData();
            formData.append('file', file);

            // 构造参数
            const params = {
                standardId: standardId
            };

            const res = await uploadAction(params, formData);
            if (res?.success) {
                message.success('上传模板文件成功');
                onUploadSuccess(res.data, file);
                onSuccess?.(res.data);
            } else {
                message.error(res?.message || '上传模板文件失败');
                onError(res?.message || '上传模板文件失败');
            }
        } catch (error) {
            console.error('上传模板文件失败', error);
            message.error('上传模板文件失败');
            onError('上传模板文件失败');
        } finally {
            setUploading(false);
        }
    };

    const beforeUpload = (file: RcFile) => {
        const isDocx = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.type === 'application/msword' ||
            file.name.endsWith('.docx') ||
            file.name.endsWith('.doc');

        if (!isDocx) {
            message.error('只能上传Word文档文件!');
            return Upload.LIST_IGNORE;
        }

        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
            message.error('文件大小不能超过10MB!');
            return Upload.LIST_IGNORE;
        }

        return true;
    };

    return (
        <Upload
            maxCount={1}
            fileList={fileList}
            customRequest={handleCustomRequest}
            accept={accept}
            beforeUpload={beforeUpload}
            disabled={disabled || uploading}
        >
            <Button
                icon={<UploadOutlined />}
                disabled={disabled || uploading}
                loading={uploading}
            >
                {buttonText}
            </Button>
        </Upload>
    );
};

export default TemplateUpload;
