import { BaseResult } from '@ahooksjs/use-request/es/types';
import type { Options } from '@ahooksjs/use-request/lib/types';
import { useRequest } from '@umijs/max';
import { Divider, Skeleton } from 'antd';
import cs from 'classnames';
import { json } from 'express';
import { nanoid } from 'nanoid';
import type { ReactNode, Ref } from 'react';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
export interface InfiniteListProps<T> {
  service?: (args: any) => Promise<any>;
  listHeight: number;
  pageSize?: number;
  children?: (data: T[]) => JSX.Element | ReactNode;
  /** 固定请求参数 */
  query?: Record<string, any>;
  /**
   * 是否初始阶段请求
   * 取消初始请求后需要手动触发ref.getInitData
   */
  initRequest?: boolean;

  /**
   * useRequest配置
   */
  serviceOptions?: Options<any, any[], any, any>;

  className?: string;

  /**
   * 直接给useRequest，这种情况会忽略service设置
   */

  request?: BaseResult<
    { size?: number; current?: number; records?: any[]; total?: number; pages?: number },
    any
  >;
}

export interface InfiniteListRef {
  /**
   * 清空数据
   */
  clearList: () => void;
  /**
   * 滚动到顶部
   */
  scorllToTop: () => void;
  /**
   * 手动请求数据
   * @param query 请求参数
   * @param init 重置为第一页，默认`true`
   */
  getInitData: (query?: Record<string, any>, init?: boolean) => void;
  /**
   * 获取列表所有数据
   */
  getData: () => any[];
}
const InfiniteList = <T = any,>(
  {
    service,
    listHeight,
    pageSize = 20,
    children,
    query = {},
    initRequest = true,
    serviceOptions = {},
    className,
    request,
  }: InfiniteListProps<T>,
  ref: Ref<InfiniteListRef>,
) => {
  const componentId = useRef(nanoid());
  const serviceData = useRequest(service ?? (async () => {}), { manual: true, ...serviceOptions });
  const getData = useMemo(() => request ?? serviceData, [serviceData, request]);
  const [data, setData] = useState([]);
  const hasMore = useMemo(() => {
    if (getData.data) {
      return getData.data.current < getData.data.pages;
    }
    return false;
  }, [getData, request]);

  console.log(query, 'query')

  const loadMoreData = useCallback(
    async (init?: boolean, initQuery?: Record<string, any>) => {
      if (getData.loading) {
        return;
      }
      const res = await getData.run({
        // @ts-ignore
        page: init ? 1 : getData.data.current + 1,
        size: pageSize,

        ...query,
        ...initQuery,
      });
      setData(init ? res?.records || [] : data.concat(res?.records || []));
    },
    [getData, pageSize, query],
  );

  useEffect(() => {
    if (initRequest) {
      console.log(1111111)
      loadMoreData(true);
    }
  }, []);

  // ref
  useImperativeHandle(
    ref,
    () => ({
      clearList: () => {
        setData([]);
      },
      getInitData: (initQuery = {}, init = true) => {
        loadMoreData(init, initQuery);
      },
      scorllToTop: () => {
        const ele = document.querySelector('.' + componentId.current);
        if (ele) {
          ele.scrollTop = 0;
        }
      },
      getData: () => {
        return data;
      },
    }),
    [loadMoreData, data],
  );

  return (
    <InfiniteScroll
      dataLength={data.length}
      next={loadMoreData}
      hasMore={hasMore}
      loader={<Skeleton loading = {getData.loading} paragraph={{ rows: 3 }} active />}
      endMessage={<Divider plain>没有更多</Divider>}
      height={listHeight}
      scrollThreshold={0.9}
      className={cs([componentId.current, className])}
    >
      {typeof children === 'function' ? children(data) : children}
    </InfiniteScroll>
  );
};

export default forwardRef(InfiniteList) as <T>(
  props: InfiniteListProps<T> & React.RefAttributes<InfiniteListRef>,
) => React.ReactElement | null;
