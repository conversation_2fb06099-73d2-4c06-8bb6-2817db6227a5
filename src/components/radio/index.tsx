import styles from './index.less';
import cs from 'classnames';
import React, { useState } from 'react';
import { color as Color } from '@/config/color';

export type RadioProps = {
  options?: { label: string | React.ReactNode; value: string | number; key: string | number }[];
  // 默认颜色 激活颜色
  color?: [string, string];
  // 默认背景 激活背景颜色
  background?: [string, string];
  value?: string | number | undefined;
  onChange?: (value: string | undefined | number) => void;
  itemStyle?: React.CSSProperties;
  type?: 'square' | 'round' | 'square1';
  className?: string | undefined;
};

/** 默认圆角按钮 */
const MyRadio = ({
  options,
  color = ['#fff', Color.primary],
  value,
  background = ['#5891fe', '#ffffff'],
  onChange,
  type = 'round',
  className,
  ...rest
}: RadioProps) => {
  const [active, setActive] = useState<string | undefined | number>(value);
  return (
    <div
      className={cs(className, {
        [styles.my_radio]: true,
        [styles.my_radio_square]: type === 'square' || type === 'square1',
        [styles.my_radio_square1]: type === 'square1',
      })}
    >
      {options?.map(({ label, value: val, key }, index) => {
        return (
          <span
            onClick={() => {
              if (active === val) return;
              setActive(val);
              if (onChange) onChange(val);
            }}
            key={key || index}
            style={{
              color: active === val ? color?.[1] : color?.[0],
              background: active === val ? background?.[1] : background?.[0],
              ...rest?.itemStyle,
            }}
            className={cs({
              [styles.my_radio_item]: true,
              [styles.my_radio_item_active]: active === val,
            })}
            data-value={val}
          >
            {label}
          </span>
        );
      })}
    </div>
  );
};

export default MyRadio;
