.my_radio {
  display: flex;
  font-size: 14px;
  white-space: nowrap;
  &_item {
    display: inline-block;
    padding: 6px 24px;
    font-weight: 600;
    border-radius: 32px;
    // box-shadow: 0 4px 12px 0 rgba(33, 85, 181, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    &:not(:last-child) {
      margin-right: 16px;
    }
    &_active {
      box-shadow: 0 4px 12px 0 rgba(33, 85, 181, 0.4);
    }
  }
}

// 方形按钮
.my_radio_square {
  .my_radio_item {
    margin-right: unset;
    padding: 6px 12px;
    background: unset !important;
    border-radius: 2px;
    box-shadow: unset;
    &_active {
      background: #fff !important;
    }
  }
}

.my_radio_square1 {
  padding: 2px;
  background: #f2f6fe;
  .my_radio_item {
    flex: 1;
    text-align: center;
  }
}
