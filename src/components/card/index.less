.my_title {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 6px 18px 0 rgba(33, 85, 181, 0.1);
  &_header {
    display: flex;
    flex: 0 0 64px;
    align-items: center;
    justify-content: space-between;
    height: 64px;
    padding: 21px 16px;
    font-size: 16px;
    border-bottom: 1px solid #eff1f5;
    &_small {
      flex: 0 0 47px;
      height: 47px;
    }
    &_left {
      display: flex;
      align-items: center;
      &_default {
        display: inline-block;
        width: 4px;
        height: 20px;
        margin-right: 8px;
        background: #37f;
        border-radius: 2px;
      }
      &_icon {
        position: relative;
        // 偏移量
        // top: 2px;
        margin-right: 8px;
        font-size: 21px;
      }
      &_text {
        color: #282d30;
        font-weight: 600;
        font-size: 16px;
      }
    }
    &_right {
      padding-right: 4px;
      color: #8f9bb3;
      font-size: 14px;
      cursor: pointer;
      &:hover {
        color: @primary;
      }
    }
  }
  &_body {
    flex: 1;
    padding: 16px;
    overflow: hidden;
  }
}
