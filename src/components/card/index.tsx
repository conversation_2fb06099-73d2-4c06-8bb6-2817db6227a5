import classNames from 'classnames';
import React from 'react';
import { MyIcon } from '../icon';
import styles from './index.less';
export type TitleProps = Omit<React.HTMLAttributes<HTMLDivElement>, 'title'> & {
  title?: string | React.ReactNode;
  titleStyle?: React.CSSProperties;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  // 头部右侧的文字
  extra?: WithBool<string | React.ReactNode>;
  // 头部右侧点击事件
  onClick?: () => void;
  titleSize?: 'default' | 'small';
  bodyClassName?: string;
};
export default ({
  title,
  children,
  icon,
  titleSize = 'default',
  extra,
  titleStyle,
  className,
  bodyClassName,
  style,
  onClick,
}: TitleProps) => {
  return (
    <div
      className={classNames(styles.my_title, className, {
        [styles.my_title_small]: titleSize === 'small',
      })}
      style={style}
    >
      <div className={styles.my_title_header} style={{ ...titleStyle }}>
        <div className={styles.my_title_header_left}>
          {(icon && <span className={styles.my_title_header_left_icon}>{icon}</span>) || (
            <span className={styles.my_title_header_left_default} />
          )}
          <span className={styles.my_title_header_left_text}>{title}</span>
        </div>
        <div className={styles.my_title_header_right} onClick={() => onClick?.()}>
          {typeof extra === 'boolean'
            ? null
            : extra || (
                <span>
                  查看更多
                  <MyIcon type="icon-chakanquanbu" />
                </span>
              )}
        </div>
      </div>
      <div className={classNames(styles.my_title_body, bodyClassName)}>{children}</div>
    </div>
  );
};
