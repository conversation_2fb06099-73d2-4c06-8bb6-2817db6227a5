// import { dotnetGetPreviewUrl } from '@/services/base/officewendangxiangguanjiekou';
import { downloadFile } from '@/utils';
import { useModel } from '@umijs/max';
import { Typography } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
const { Text, Link, Paragraph } = Typography;

export default ({
  item,
  preview,
  isDowmload = true,
}: {
  item?: BASE.FileInfo;
  preview?: boolean;
  isDowmload?: boolean;
}) => {
  if (!item) {
    return '-';
  }

  const { fileName, updateTime, fileUrl, id, fileCode } = item;

  const { initialState } = useModel('@@initialState');

  // if (value?.match(/^http.*/))
  // 判断是否为http开头 字符串
  function getUrl(url: string) {
    if (!url) return undefined;
    return !!url.match(/^http.*/)
      ? url
      : fileUrl?.includes('/test')
      ? fileUrl.replace('/test', '')
      : fileUrl;
  }

  const [tfDowmload, setTfDowmload] = useState(isDowmload);

  const _fileName = decodeURIComponent(
    fileName || fileUrl?.split('/')?.[fileUrl?.split('/').length - 1] || '',
  );

  return (
    <div key={fileUrl}>
      {/* <a href={getUrl(fileUrl as string)} download={getUrl(fileUrl as string)} > */}
      {/* 是否可下载 */}
      {tfDowmload ? (
        <a
          onClick={() =>
            downloadFile(getUrl(fileUrl as string), (isDowmload) => {
              setTfDowmload(isDowmload);
            })
          }
          style={{
            display: 'block',
            width: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
          title={_fileName}
        >
          {(item as any)?.type ? (
            <Text type={(item as any)?.type} delete={(item as any)?.type === 'delete'}>
              {_fileName}
            </Text>
          ) : (
            _fileName
          )}
        </a>
      ) : (
        <div>
          {(item as any)?.type ? (
            <Text type={(item as any)?.type} delete={(item as any)?.type === 'delete'}>
              {_fileName}
            </Text>
          ) : (
            _fileName
          )}
        </div>
      )}
      {/* {!!preview && (
        <Tooltip title="预览">
          <FolderViewOutlined
            style={{ marginLeft: '5px', cursor: 'pointer', color: 'green' }}
            onClick={async () => {
              try {
                const res = await dotnetGetPreviewUrl({
                  fileId: id?.toString(),
                  tfEdit: false,
                });

                if (res.success && !!res.data) {
                  window.open(res.data);
                }
              } catch (e) {}
            }}
          />
        </Tooltip>
      )} */}
      {initialState?.dict?.showTime?.[0] && (
        <Paragraph
          type={(item as any)?.type}
          style={{ marginBottom: '0px' }}
          delete={(item as any)?.type === 'delete'}
        >
          {dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss')}
        </Paragraph>
      )}
    </div>
  );
};
