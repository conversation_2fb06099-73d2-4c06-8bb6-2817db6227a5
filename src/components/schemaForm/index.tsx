import type { ProFormInstance } from '@ant-design/pro-components';
import { BetaSchemaForm } from '@ant-design/pro-components';
import { Button } from 'antd';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';

export type SchemaFormProps<T = any, ValueType = 'text'> = Partial<
  React.ComponentProps<typeof BetaSchemaForm<T, GlobalValueType>>
>;
export type SchemaFormRef<T = any, ValueType = 'text'> = {
  open: (arg?: { editable?: boolean; onSuccess?: () => void }) => void;
};

export const SchemaForm = <T = any, ValueType = 'text'>(
  {
    formRef: propsFormRef,
    layoutType: propsLayoutType,
    columns: propsColumns = [],
    ...rest
  }: SchemaFormProps<T, ValueType>,
  ref: React.Ref<SchemaFormRef<T, ValueType>>,
) => {
  const formRef = React.useRef<ProFormInstance>();
  const [open, setOpen] = useState(false);
  // 是否是编辑状态
  const editable = useRef(false);
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      propsFormRef: () => formRef.current,
      open: (arg) => {
        // 保存成功回调
        successCb.current = arg?.onSuccess;
        // 是否是编辑状态
        editable.current = arg?.editable ?? false;

        setOpen(true);
      },
    };
  });

  const handleSubmit: SchemaFormProps<T, ValueType>['onFinish'] = async (valus) => {
    successCb.current?.();
    setOpen(false);
  };

  const props: Partial<React.ComponentProps<typeof BetaSchemaForm<T, ValueType>>> = {
    layoutType: propsLayoutType ?? 'DrawerForm',
  };

  if (props.layoutType === 'ModalForm' || props.layoutType === 'DrawerForm') {
    props.open = open;
    props.onOpenChange = (visible) => {
      setOpen(visible);
    };

    props.submitter = {
      render: () => {
        return [
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              formRef.current?.submit();
            }}
          >
            确定
          </Button>,
          <Button
            key="close"
            onClick={() => {
              setOpen(false);
            }}
          >
            关闭
          </Button>,
        ];
      },
    };
  }

  return (
    <BetaSchemaForm<T, ValueType>
      formRef={formRef}
      onFinish={handleSubmit}
      {...(props as any)}
      {...(rest as any)}
      columns={propsColumns}
    />
  );
};

export default forwardRef(SchemaForm) as <T = any, ValueType = 'text'>(
  props: SchemaFormProps<T, ValueType> & {
    ref?: React.Ref<SchemaFormRef<T, ValueType>>;
  },
) => React.ReactElement;
