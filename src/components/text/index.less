.text {
  position: relative;
  display: inline-block;
  color: @color-base;
}

.text-primary {
  .color(@primary);
}

.text-success {
  .color(@success);
}

.text-warning {
  .color(@warning);
}

.text-danger {
  .color(@danger);
}

.text.disabled,
.text[disabled] {
  cursor: not-allowed;
  pointer-events: none;
}

.color(@color) {
  @link-hover-color: darken(@color, 10%);
  @link-active-color: darken(@color, 20%);
  @disabled-color: fade(@color, 35%);

  color: @color;
  text-decoration: none;
  background-color: transparent; // remove the gray background on active links in IE 10.
  outline: none;
  cursor: pointer;
  transition: color 0.3s;
  -webkit-text-decoration-skip: objects; // remove gaps in links underline in iOS 8+ and Safari 8+.

  &:hover {
    color: @link-hover-color;
  }

  &:active {
    color: @link-active-color;
  }

  &:active,
  &:hover {
    text-decoration: none;
    outline: 0;
  }

  &:focus {
    text-decoration: none;
    outline: 0;
  }

  &.disabled,
  &[disabled] {
    color: @disabled-color;
  }
}
