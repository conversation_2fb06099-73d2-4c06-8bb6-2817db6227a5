import { withRequest } from '@/components/withRequest';
import cs from 'classnames';
import './index.less';

export interface TextProps
  extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  type?: 'primary' | 'success' | 'warning' | 'danger';
  /** 禁用 */
  disabled?: boolean;
}

const Text: React.FC<TextProps> & {
  Action: typeof Action;
} = ({ type = 'primary', className, children, disabled, ...rest }) => {
  return (
    <div
      className={cs(
        'text',
        {
          [`text-${type}`]: type,
          disabled: disabled,
        },
        className,
      )}
      {...rest}
    >
      {children}
    </div>
  );
};

const Action = withRequest({
  clickable: true,
  confirm: 'popconfirm',
  confirmText: '确定此项操作吗?',
})(Text);
Text.Action = Action;

export default Text;
