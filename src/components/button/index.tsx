import { withRequest, WithRequestProps } from '@/components/withRequest';
import type { ButtonProps as AntButtonProps } from 'antd';
import { Button as AntButton } from 'antd';
import cs from 'classnames';
import React, { Component, ComponentType } from 'react';

import styles from './index.less';

export type ButtonProps = AntButtonProps & {};



const Button: React.FC<ButtonProps> & {
  Group: typeof AntButton.Group;
  Action: typeof Action;
} = ({ className, ...rest }) => {
  return <AntButton className={cs(styles['button'], className)} {...rest} />;
};

const Action = withRequest({
  clickable: true,
})(Button as ComponentType<ButtonProps>);

Button.Action = Action;
Button.Group = AntButton.Group;

export default Button;

export * from 'antd/lib/button';
