import { ActionType, EditableProTable, ProColumns } from '@ant-design/pro-components';
import { useEffect, useMemo, useState } from 'react';

// https://fettblog.eu/typescript-react-generic-forward-refs/
// declare module 'react' {
//   function forwardRef<T extends Record<string, any>>(
//     render: (
//       props: EditProps<T>,
//       ref: React.ForwardedRef<ActionType | undefined>,
//     ) => React.ReactElement | null,
//   ): (
//     props: EditProps<T> & React.RefAttributes<ActionType | undefined>,
//   ) => React.ReactElement | null;
// }

type EditProps<T extends Record<string, any>> = Omit<
  React.ComponentProps<typeof EditableProTable<T>>,
  'dataSource'
> & {
  value?: T[];
  defaultData?: T[];
  onChange?: (value: T[]) => void;
  hidenEdit?: boolean;
  hidenDelete?: boolean;
  onDatasourceChange?: (value: T[]) => void;
  // 格式化数据
  formatDataSource?: (dataSource: T[]) => T[];
  /** 操作栏 */
  actions?: React.ReactElement[];
};

const EditTable = <T extends Record<string, any> = Record<string, any>>(props: EditProps<T>) => {
  const {
    columns,
    value,
    defaultData,
    hidenEdit = false,
    hidenDelete = false,
    actions = [],
    formatDataSource,
    onDatasourceChange,
    ...rest
  } = props;

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [dataSource, setDataSource] = useState<T[]>(
    defaultData?.map((i) => ({ ...i, rowId: i?.id })) || [],
  );

  useEffect(() => {
    setDataSource(
      formatDataSource
        ? formatDataSource(value?.map((i) => ({ ...i, rowId: i?.id || i?.rowId } as T)) || [])
        : value?.map((i) => ({ ...i, rowId: i?.id || i?.rowId } as T)) || [],
    );
    onDatasourceChange?.(
      formatDataSource
        ? formatDataSource(value?.map((i) => ({ ...i, rowId: i?.id || i?.rowId } as T)) || [])
        : value?.map((i) => ({ ...i, rowId: i?.id || i?.rowId } as T)) || [],
    );
  }, [value]);

  const _columns: ProColumns<T>[] = useMemo(() => {
    const hasOption = columns?.some((i) => i.valueType === 'option');
    if (!hasOption) {
      return [
        ...(columns || []),
        {
          title: '操作',
          valueType: 'option',
          fixed: 'right',
          width: 'auto',
          render: (text, record, _, action) => [
            ...(actions as any),
            !hidenEdit && (
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record?.rowId);
                }}
              >
                编辑
              </a>
            ),
            !hidenDelete && (
              <a
                key="delete"
                style={{ color: 'red' }}
                onClick={() => {
                  setDataSource(dataSource.filter((item) => item.rowId !== record.rowId));
                  props?.onChange?.(dataSource.filter((item) => item.rowId !== record.rowId));
                }}
              >
                删除
              </a>
            ),
          ],
        },
      ];
    }
    return columns || [];
  }, [columns, dataSource, actions]);

  return (
    <EditableProTable<T>
      rowKey="rowId"
      toolBarRender={false}
      value={dataSource}
      onChange={(value) => {
        setDataSource(formatDataSource ? (formatDataSource(value as T[]) as any) : value);
        props?.onChange?.(value);
      }}
      style={{ marginBottom: '20px' }}
      recordCreatorProps={{
        // newRecordType: 'dataSource',
        position: 'bottom',
        record: () =>
          ({
            rowId: Date.now(),
          } as unknown as T),
      }}
      editable={{
        ...props.editable,
        type: 'multiple',
        editableKeys,
        onChange: setEditableRowKeys,
        actionRender: (row, _, dom) => {
          return [dom.save, dom.cancel];
        },
      }}
      {...rest}
      columns={_columns}
    />
  );
};

export default EditTable;
