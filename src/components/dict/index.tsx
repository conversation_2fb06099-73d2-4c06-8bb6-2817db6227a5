import type { TagProps } from 'antd';
import { Tag } from 'antd';
import { useMemo } from 'react';
import { useModel } from 'umi';

type DictProps = TagProps & {
  dicCode: string;
  value?: string | number | null | undefined;
};

const Dict: React.FC<DictProps> = ({ dicCode, value, ...rest }) => {
  const { initialState } = useModel('@@initialState');

  const render = useMemo(() => {
    if (initialState?.dict) {
      const list = initialState.dict[dicCode] || [];

      const find = list.find((v) => {
        if (/\d+/.test(v.dicValue) && typeof value === 'number') {
          return Number(v.dicValue) === value;
        }

        return v.dicValue === value;
      });

      if (find) {
        return find;
      }
    }

    return null;
  }, [dicCode, value, initialState?.dict]);

  if (render) {
    return (
      <Tag color={render.dicColour} {...rest}>
        {render.dicName}
      </Tag>
    );
  }

  return <Tag {...rest}>{value}</Tag>;
};

export default Dict;
