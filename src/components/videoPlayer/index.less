.video-player-container {
  position: relative;
  background: #000;
  // 确保视频容器填充整个区域
  &::before {
    content: '';
    display: block;
  }

  .video-js {
    width: 100%;
    height: 100%;
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    margin: 0;

    // 视频元素样式
    video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain; // 或 contain，根据需要选择
    }
    // 自定义控制栏样式
    .vjs-control-bar {
      background-color: rgba(0, 0, 0, 0.7);
    }

    // 加载动画样式
    .vjs-loading-spinner {
      border: 3px solid rgba(255, 255, 255, 0.7);
    }

    // 直播标记样式
    .vjs-live-control {
      line-height: 2.5em;
    }
  }
}