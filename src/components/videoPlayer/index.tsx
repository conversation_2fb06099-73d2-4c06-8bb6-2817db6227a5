import React, { useEffect, useRef } from 'react';
import videojs from 'video.js';
import flvjs from 'flv.js';
import 'video.js/dist/video-js.css';
import './index.less';

interface VideoPlayerProps {
  url: string;
  type?: string; // 视频类型
  width?: string | number;
  height?: string | number;
  autoplay?: boolean;
  controls?: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  url,
  type = 'application/x-mpegURL', // 默认HLS格式
  width = '100%',
  height = '100%',
  controls = true,
}) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const flvPlayerRef = useRef<any>(null);
  const playerRef = useRef<any>(null);
  const initPlayer = () => {
    if (!videoRef.current) return;
    if (!url) return;
    // flv.js 支持检测
    if (flvjs.isSupported() && flvPlayerRef.current === null) {
      flvPlayerRef.current = flvjs.createPlayer({
        type: 'flv',
        url: url,
        isLive: true,
        cors: true,
      });
      // 自动播放
      flvPlayerRef.current.attachMediaElement(videoRef.current);
      flvPlayerRef.current.load();
      flvPlayerRef.current.play();
    } else {
      console.error('FLV format is not supported in this browser!');
    }
  }
  useEffect(() => {
    if (!videoRef.current) return;
    initPlayer()

    // 清理函数
    return () => {
      if (flvPlayerRef.current) {
        flvPlayerRef.current?.destroy?.();
        flvPlayerRef.current = null;
      }
    };
  }, [url, type, videoRef.current]);

  return (
    <div className="video-player-container" style={{ width, height }}>
      <div data-vjs-player>
        <video
          ref={videoRef}
          // autoPlay={true}
          // crossOrigin="anonymous"
          className="video-js vjs-big-play-centered"
          controls={controls}
          // poster={require('@/assets/bigscreen/noLink.png')}
        />
      </div>
    </div>
  );
};

export default VideoPlayer;
