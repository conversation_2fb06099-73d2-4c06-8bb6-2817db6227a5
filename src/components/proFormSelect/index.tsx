import { orgUserInfoUserInfoNoPhoto } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import {
  ProFormSelect as AntProFormSelect,
  ProFormSelectProps as AntProFormSelectProps,
} from '@ant-design/pro-components';
import { SelectProps } from 'antd';
import { useEffect, useMemo } from 'react';
import { useModel, useRequest } from 'umi';

const TYPES = ['user'];

export type ProFormSelectProps = AntProFormSelectProps & {
  /** 系统数据字典 */
  dicCode?: string;
  type?: 'user';
  query?: Record<string, any>;
  

};

const ProFormSelect: React.FC<ProFormSelectProps> = ({ dicCode, type, query, ...rest }) => {
  const { initialState } = useModel('@@initialState');

  // 用户列表请求
  const userRequest = useRequest(
    () => {
      return orgUserInfoUserInfoNoPhoto({
        roleIds: query?.roleId && [query?.roleId],
        deptIds: query?.deptId && (query?.deptId as string).split(','),
      });
    },
    {
      manual: true,
      formatResult(res) {
        return res?.data?.map((i,index) => ({ label: i?.fullName, value: i?.id,key:i?.id } || []));
      },
    },
  );

  const typeReuqest: { [key: string]: any } = {
    user: userRequest,
  };

  useEffect(() => {
    switch (type) {
      case 'user':
        userRequest.run();
        break;
      default:
        break;
    }
  }, [type]);

  const options = useMemo(() => {
    const res: SelectProps['options'] = [];
    if (dicCode && initialState?.dict) {
      const list = initialState.dict?.[dicCode] || [];
      list.forEach((item,index) => {
        res.push({
          label: item.dicName,
          value: item.dicValue,
          key:`${item.dicName}-${index}`
        });
      });

      return res;
    }

    return undefined;
  }, [dicCode, initialState?.dict]);

  return (
    <AntProFormSelect
      {...rest}
      
      fieldProps={{
        options: options || (type && typeReuqest[type]?.data) || undefined,
        showSearch: true,
        loading: type && typeReuqest[type]?.loading,
        // filterOption: (input, option) => {
        //   return true;
        //   // return option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
        // },
        ...rest.fieldProps,
      }}
    />
  );
};

export default ProFormSelect;
