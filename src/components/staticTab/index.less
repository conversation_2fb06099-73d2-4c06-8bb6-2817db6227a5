.container {
  display: flex;
  margin-bottom: 16px;
  padding: 10px 16px;
  background: #fff;
  border-radius: 6px;
  &_horizontal {
    flex-direction: column;
  }
  &_tabs {
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 14px;
    &_horizontal {
      display: flex;
      line-height: 33px;
      border-bottom: 1px solid #eaeaea;
      .tabs_item {
        display: flex;
        flex-direction: column;
        padding: 0 15px;
        font-size: 16px;

        &_active::before {
          display: none !important;
        }
        &_active::after {
          position: absolute;
          bottom: -6px;
          width: 100%;
          height: 2px;
          background: #108ee8;
          content: '';
        }
      }
    }
    .tabs_item {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      cursor: pointer;
      &_active {
        color: #108ee8;
        &::before {
          position: absolute;
          left: -8px;
          display: inline-block;
          width: 2px;
          height: 16px;
          background: #108ee8;
          content: '';
        }
      }
    }
  }
}

.content {
  display: flex;
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 15px;

    &_title {
      font-size: 14px;
      // font-weight: 600;
    }
    &_value {
      vertical-align: bottom;
      .text {
        font-weight: 600;
        font-size: 23px;
      }
      .unit {
        margin-left: 5px;
        font-weight: unset;
        font-size: 13px;
      }
    }
    &_desx {
      font-size: 13px;
    }
  }
}

.divider {
  width: 1px;
  height: 80px;
  margin: 0 15px;
  background: #eaeaea;
}
