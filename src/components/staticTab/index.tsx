import classNames from 'classnames';
import { useState } from 'react';
import styles from './index.less';

type TabProps = {
  label?: React.ReactNode;
  value?: string | number | undefined;
};

type StaticProps = {
  title?: React.ReactNode;
  value?: React.ReactNode;
  unit?: string;
  desc?: React.ReactNode;
  render?: () => React.ReactNode;
};

type StaticPanelProps = {
  tabs?: TabProps[];
  tabLayoutType?: 'horizontal' | 'vertical';
  staticData?: StaticProps[];
  onTabChange?: (val: string | number | undefined) => void;
  frontExtra?: React.ReactNode;
};

const StaticPanel: React.FC<StaticPanelProps> = (props) => {
  const { tabs, tabLayoutType = 'vertical', staticData, onTabChange, frontExtra, ...rest } = props;

  const [activeTab, setActiveTab] = useState(tabs?.[0]?.value);

  return (
    <div
      className={classNames(styles['container'], {
        [styles['container_horizontal']]: tabLayoutType === 'horizontal',
      })}
    >
      {frontExtra}
      {tabs && (
        <div
          className={classNames(styles['container_tabs'], {
            [styles['container_tabs_horizontal']]: tabLayoutType === 'horizontal',
          })}
        >
          {tabs?.map((i, index) => {
            return (
              <div
                className={classNames(styles['tabs_item'], {
                  [styles['tabs_item_active']]: activeTab === i?.value,
                })}
                onClick={() => {
                  setActiveTab(i?.value);
                  onTabChange?.(i?.value);
                }}
                key={index}
              >
                {i?.label}
              </div>
            );
          })}
        </div>
      )}
      {tabLayoutType === 'vertical' && tabs?.length && <div className={styles['divider']} />}
      <div className={styles['content']}>
        {staticData?.map((i, index) => {
          return (
            <div key={index} style={{ display: 'flex' }}>
              <div className={styles['item']}>
                {(i?.render && i?.render?.()) || (
                  <>
                    <div className={styles['item_title']}>{i?.title}</div>
                    <div className={styles['item_value']}>
                      <span className={styles['text']}>{i?.value}</span>
                      <span className={styles['unit']}>{i?.unit}</span>
                    </div>
                    <div className={styles['item_desc']}>{i?.desc}</div>
                  </>
                )}
              </div>
              {index !== staticData.length - 1 && <div className={styles['divider']} />}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StaticPanel;
