import React from 'react';

import cs from 'classnames';

import { Col } from 'antd';
import styles from './index.less';

interface ITitleProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'title'> {
  title: React.ReactNode;
  extra?: React.ReactNode;
  color?: string;
  span?: number;
}

export default ({
  title,
  extra,
  className,
  color = '#1677ff',
  span = 24,
  style,
  ...rest
}: ITitleProps) => {
  const classString = cs(styles['title'], {}, className);

  return (
    <Col span={span}>
      <div className={classString} style={style}>
        <div
          className={styles['title_wrapper']}
          style={{
            color,
          }}
        >
          <span
            className={styles['tag']}
            style={{
              backgroundColor: color,
            }}
          ></span>
          <span className={styles['title_text']}>{title}</span>
        </div>
        {extra && <div className={styles['title-extra']}>{extra}</div>}
      </div>
    </Col>
  );
};
