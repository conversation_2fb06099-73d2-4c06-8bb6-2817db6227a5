import { CloseCircleOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import cs from 'classnames';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { VerifySlideRef } from '../verifySlide';
import { VerifySlide } from '../verifySlide';
import styles from './index.less';

export type VerifySlideModalRef = {
  open: (arg?: { onSuccess: (token: string) => void; onCancel?: () => void }) => void;
};

export interface VerifySlideModalProps {
  ref?: React.Ref<VerifySlideModalRef>;
}

export const VerifySlideModal: React.FC<VerifySlideModalProps> = forwardRef<
  VerifySlideModalRef,
  VerifySlideModalProps
>(({}, ref) => {
  const [open, setOpen] = useState(false);
  const [validLoading, setValidLoading] = useState(false);
  const verify = useRef<VerifySlideRef>(null);
  const successTimer = useRef<NodeJS.Timer>();
  const onSuccess = useRef<(token: string) => void>();
  const onCancel = useRef<() => void>();

  useImperativeHandle(ref, () => ({
    open: (arg) => {
      setOpen(true);
      verify.current?.reset();

      if (arg) {
        onSuccess.current = arg.onSuccess;
        onCancel.current = arg.onCancel;
      }
    },
  }));

  const handleCancel = () => {
    if (validLoading) {
      return;
    }

    onCancel.current?.();
    setOpen(false);
  };

  return (
    <Modal
      open={open}
      closable={!validLoading}
      wrapClassName={cs(styles['modal'])}
      title="请完成安全验证"
      footer={false}
      maskClosable={false}
      width={360}
      closeIcon={<CloseCircleOutlined />}
      transitionName="ant-fade"
      centered
      onCancel={handleCancel}
    >
      <VerifySlide
        ref={verify}
        onValidStart={() => {
          setValidLoading(true);
        }}
        onValidEnd={() => {
          setValidLoading(false);
        }}
        onSuccess={(token, e) => {
          if (successTimer.current) {
            clearTimeout(successTimer.current);
          }

          successTimer.current = setTimeout(() => {
            setOpen(false);
            onSuccess.current?.(token);
          }, 1000);
        }}
        className={cs(styles.verify)}
      />
    </Modal>
  );
});
