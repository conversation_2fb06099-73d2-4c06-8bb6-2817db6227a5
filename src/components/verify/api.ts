export const RepCode = {
  '0000': '无异常，代表成功',
  '9999': '服务器内部异常',
  '0011': '参数不能为空',
  '6110': '验证码已失效，请重新获取',
  '6111': '验证失败',
  '6112': '获取验证码失败,请联系管理员',
  '6113': '底图未初始化成功，请检查路径',
  '6201': 'get接口请求次数超限，请稍后再试!',
  '6206': '无效请求，请重新获取验证码',
  '6202': '接口验证失败数过多，请稍后再试',
  '6204': 'check接口请求次数超限，请稍后再试',
} as const;

export type GetPictureResponseRepData = {
  browserInfo: string;
  captchaFontSize: string;
  captchaFontType: string;
  captchaId: string;
  captchaOriginalPath: string;
  captchaType: string;
  captchaVerification: string;
  clientUid: string;
  /** 滑块图base64 */
  jigsawImageBase64: string;
  /** 底图base64 */
  originalImageBase64: string;
  point: string;
  /** aes加密坐标信息 */
  pointJson: string;
  pointList: string;
  projectCode: string;
  result: boolean;
  /** aes秘钥，开关控制，前端根据此值决定是否加密 */
  secretKey: string;
  /** 一次校验唯一标识 */
  token: string;
  ts: string;
  wordList: null | string[];
};

export type GetPictureQuery = {
  /**
   * 验证码类型
   * blockPuzzle: 滑动
   * clickWord: 点击文字
   */
  captchaType: 'blockPuzzle' | 'clickWord';
  /**
   * 客户端UI组件id,组件初始化时设置一次，UUID（非必传参数）
   */
  clientUid?: string;
  /**
   * 时间戳,防止缓存
   */
  ts?: number;
};

export type GetPictureResponse = {
  repCode: string;
  repData: GetPictureResponseRepData;
  repMsg: null | string;
  success: boolean;
};

export type CheckPictureQuery = {
  /**
   * 验证码类型
   * blockPuzzle: 滑动
   * clickWord: 点击文字
   */
  captchaType: 'blockPuzzle' | 'clickWord';
  pointJson: string;
  token: string;
  clientUid?: string;
  ts?: number;
};

export type CheckPictureResponseRepData = {
  captchaFontSize: string;
  captchaFontType: string;
  captchaId: string;
  captchaOriginalPath: string;
  captchaType: 'blockPuzzle' | 'clickWord';
  captchaVerification: string;
  jigsawImageBase64: string;
  originalImageBase64: string;
  point: string;
  pointJson: string;
  pointList: string;
  projectCode: string;
  result: boolean;
  secretKey: string;
  token: string;
  wordList: string;
};

export type CheckPictureResponse = {
  repCode: string;
  repData: null | CheckPictureResponseRepData;
  repMsg: null | string;
  success: boolean;
};

/** 获取验证码图片 */
export async function getPicture(
  body: GetPictureQuery,
  options?: Record<string, any>,
): Promise<GetPictureResponse> {
  return fetch('/api/test/captcha/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
    ...options,
  }).then((res) => res.json());
}

/** 校验验证结果 */
export async function checkPicture(
  body: CheckPictureQuery,
  options?: Record<string, any>,
): Promise<CheckPictureResponse> {
  return fetch('/api/test/captcha/check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
    ...options,
  }).then((res) => res.json());
}
