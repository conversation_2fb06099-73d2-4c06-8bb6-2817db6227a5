import CryptoJS from 'crypto-js';

/**
 * 生成uuid
 */
export function genUUID(): string {
  const uuid: string[] = [];
  const hexDigits = '0123456789abcdef';

  for (let i = 0; i < 36; i++) {
    uuid[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }

  uuid[14] = '4';
  uuid[19] = hexDigits.substr(((uuid as any)[19] & 0x3) | 0x8, 1);
  uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';

  return `u${(uuid.join('') as any).replaceAll('-', '')}`;
}

/**
 * 加密字符串
 */
export function aesEncrypt(word: string, keyWord: string): string {
  const key = CryptoJS.enc.Utf8.parse(keyWord);
  const srcs = CryptoJS.enc.Utf8.parse(word);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });

  return encrypted.toString();
}

/**
 * 获取图片尺寸
 */
export function getImgSize(src: string): Promise<{
  width: number;
  height: number;
}> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.addEventListener('load', () => {
      resolve({
        width: img.width,
        height: img.height,
      });
    });

    img.addEventListener('error', (e) => {
      reject(e);
    });

    img.src = src;
  });
}
