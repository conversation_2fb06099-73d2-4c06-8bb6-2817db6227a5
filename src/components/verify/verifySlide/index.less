.wrap {
  position: relative;
  margin-bottom: 24px;
  // padding: 10px;
  // border: 1px solid #d9d9d9;
  // border-radius: 2px;

  img {
    user-select: none;
    -webkit-user-drag: none;
  }

  :global {
    .ant-spin-nested-loading {
      overflow: hidden;
    }

    .ant-spin-nested-loading,
    .ant-spin-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

.wrap.disabled {
  .drag-handler {
    background-color: #bbb;
    box-shadow: none;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.wrap.visible-err-msg {
  .drag-status-note {
    display: block;
  }
}

.wrap.get-img-success {
  .canvas-puzzle-wrap {
    display: block;
  }
}

.wrap.check-img-success {
  .canvas-success {
    display: block;
  }
}

.canvas {
  position: relative;
  padding-bottom: 50%;
}

.canvas-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 0;
  background-color: #eee;

  img {
    width: 100%;
    height: 100%;
  }
}

.canvas-success {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 4;
  display: none;
  width: 100%;
  height: 100%;
  background-color: rgba(#fff, 0.8);

  &-icon {
    margin-top: 30px;
    color: @success;
    font-size: 50px;
    line-height: 1.1;
    text-align: center;
  }

  &-text {
    margin-top: 10px;
    padding: 0 20px;
    color: @success;
    font-size: 16px;
    line-height: 1.2;
    text-align: center;
  }
}

.canvas-puzzle-wrap {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  height: 100%;
}

.canvas-puzzle {
  position: relative;
  top: 0;
  left: 0;
  height: 100%;

  img {
    height: 100%;
  }
}

.drag-bar {
  position: relative;
  padding-top: 24px;
  padding-bottom: 24px;
}

.drag-track {
  height: 14px;
  background-color: #e4e4e4;
  border-radius: 7px;
}

.drag-handler-wrap {
  position: absolute;
  top: 50%;
  height: 46px;
  margin-top: -19px;
  cursor: pointer;
}

.drag-handler {
  position: absolute;
  // top: 38px * -0.5 + 7px;
  width: 46px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: no-repeat center center;
  background-color: @primary;
  background-size: 14px auto;
  border-radius: 19px;
  box-shadow: 0 0 4px 4px rgba(@primary, 0.52);

  img {
    display: inline-block;
    height: 14px;
    vertical-align: -2px;
  }
}

.backing {
  transition: transform 0.3s;
}

.shake {
  animation: shake 0.4s;
}

@keyframes shake {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(-2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.drag-status {
  display: flex;
  align-items: center;
  height: 36px;
  margin-top: 5px;
}

.drag-status-note {
  display: none;
  width: 80%;
  min-width: 0;
  height: 36px;
  padding-top: 10px;
  overflow: hidden;
  color: @danger;
  font-size: 14px;
  white-space: normal;
  text-overflow: ellipsis;
}

.drag-status-action {
  display: flex;
  justify-content: flex-end;
  width: 20%;
  margin-left: auto;
}

.drag-status-action-item {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  color: #999;
  font-size: 24px;
  line-height: 36px;
  text-align: center;
  cursor: pointer;

  &.disabled {
    cursor: not-allowed;
    pointer-events: none;

    &:hover {
      color: initial;
    }
  }

  &:hover {
    color: #666;
  }

  + .drag-status-action-item {
    margin-left: 10px;
  }
}
