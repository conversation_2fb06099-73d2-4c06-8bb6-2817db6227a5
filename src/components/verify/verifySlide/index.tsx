/**
 * 拖拽图片验证组件
 */

import { useForceUpdate } from '@/utils/hooks';
import { CheckCircleFilled, ReloadOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import cs from 'classnames';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type {
  CheckPictureQuery,
  CheckPictureResponse,
  GetPictureQuery,
  GetPictureResponse,
} from '../api';
import { checkPicture as apiCheckPicture, getPicture as apiGetPicture } from '../api';
import { aesEncrypt, genUUID, getImgSize } from '../util';
import $thumb from './img/thumb.png';
import styles from './index.less';

export type VerifySlideRef = {
  reset: () => void;
};

export interface VerifySlideProps {
  /** 校验开始 */
  onValidStart?: () => void;
  /** 校验结束 */
  onValidEnd?: () => void;
  /** 校验成功 */
  onSuccess?: (token: string, res: CheckPictureResponse['repData']) => void;
  className?: string;
  ref?: React.Ref<VerifySlideRef>;
}

export const VerifySlide: React.FC<VerifySlideProps> = forwardRef<VerifySlideRef, VerifySlideProps>(
  ({ className, onValidStart, onValidEnd, onSuccess }, ref) => {
    const force = useForceUpdate();
    /** 背景图片包裹层 */
    const $cover = useRef<HTMLDivElement>(null);
    /** 滚动条 */
    const $bar = useRef<HTMLDivElement>(null);
    /** 拖拽把手 */
    const $handler = useRef<HTMLDivElement>(null);
    /** 缺失图形 */
    const $puzzle = useRef<HTMLDivElement>(null);
    /** 唯一编码 */
    const uuid = useRef<string>();
    /** 记录拖拽的坐标 */
    const pos = useRef({
      x: 0,
    });
    /** 背景图尺寸,用于计算比例 */
    const coverOriginSize = useRef({
      width: 0,
      height: 0,
    });
    /** 拖拽开始时间 */
    const dragStartTime = useRef(0);
    /** 拖拽结束时间 */
    const dragEndTime = useRef(0);
    // 是否显示错误信息
    const [visibleErrMsg, setVisibleErrMsg] = useState(false);
    // 错误信息计时器
    const visibleErrMsgTimer = useRef<NodeJS.Timer>();
    // 获取图片状态
    const getPictureState = useRef({
      loading: false,
      msg: '',
      data: {} as GetPictureResponse,
    });
    // 校验图片状态
    const checkPictureState = useRef({
      loading: false,
      msg: '',
      data: {} as CheckPictureResponse,
    });

    // 获取图片
    const getPicture = async (query?: Partial<GetPictureQuery>) => {
      // 有错误的情况下清空错误
      setVisibleErrMsg(false);
      if (visibleErrMsgTimer.current) {
        clearTimeout(visibleErrMsgTimer.current);
      }

      // 重置坐标
      pos.current.x = 0;

      // 清空校验状态
      checkPictureState.current.loading = false;
      checkPictureState.current.msg = '';
      checkPictureState.current.data = {} as CheckPictureResponse;

      getPictureState.current.loading = true;
      getPictureState.current.msg = '';
      getPictureState.current.data = {} as GetPictureResponse;
      force();

      try {
        // 生成uuid
        uuid.current = genUUID();

        const res = await apiGetPicture({
          captchaType: 'blockPuzzle',
          clientUid: uuid.current,
          ts: Date.now(),
          ...query,
        });

        if (!res.success) {
          throw Error(res.repMsg || '请求失败');
        }

        const jigsawImageBase64 = 'data:image/png;base64,' + res!.repData.jigsawImageBase64;
        const originalImageBase64 = 'data:image/png;base64,' + res!.repData.originalImageBase64;

        getImgSize(originalImageBase64).then(({ width, height }) => {
          coverOriginSize.current = {
            width,
            height,
          };
        });

        getPictureState.current.loading = false;
        getPictureState.current.data = {
          ...res,
          repData: {
            ...res.repData,
            jigsawImageBase64,
            originalImageBase64,
          },
        };

        force();

        return getPictureState.current.data;
      } catch (e) {
        getPictureState.current.loading = false;
        getPictureState.current.msg = (e as Error).message;
        force();

        setVisibleErrMsg(true);

        if (visibleErrMsgTimer.current) {
          clearTimeout(visibleErrMsgTimer.current);
        }
      }
    };

    // 校验图片
    const checkPicture = async (query: CheckPictureQuery) => {
      checkPictureState.current.loading = true;
      checkPictureState.current.msg = '';
      checkPictureState.current.data = {} as CheckPictureResponse;
      force();

      try {
        const res = await apiCheckPicture({
          ...query,
        });

        if (!res.success) {
          throw Error(res.repMsg || '请求失败');
        }

        checkPictureState.current.loading = false;
        checkPictureState.current.data = res;
        force();

        return res;
      } catch (e) {
        checkPictureState.current.loading = false;
        checkPictureState.current.msg = (e as Error).message;
        force();
      }
    };

    useEffect(() => {
      getPicture();
    }, []);

    // 校验失败后回滚
    const handleRollback = (res?: GetPictureResponse) => {
      setVisibleErrMsg(true);

      if (visibleErrMsgTimer.current) {
        clearTimeout(visibleErrMsgTimer.current);
      }

      visibleErrMsgTimer.current = setTimeout(setVisibleErrMsg.bind({}, false), 3 * 1000);

      if ($handler.current && $puzzle.current) {
        $handler.current.classList.add(styles.shake);
        $puzzle.current.classList.add(styles.shake);

        function handleEnd() {
          if ($handler.current && $puzzle.current) {
            $handler.current.style.left = '0';
            $handler.current.classList.remove(styles.shake);
            $puzzle.current.classList.remove(styles.shake);
            $handler.current.removeEventListener('animationend', handleEnd);
            $puzzle.current.removeEventListener('animationend', handleEnd);

            getPicture();
          }
        }

        $handler.current.addEventListener('animationend', handleEnd, {
          once: true,
        });
        $puzzle.current.addEventListener('animationend', handleEnd, {
          once: true,
        });
      }
    };

    useImperativeHandle(ref, () => ({
      reset: () => {
        getPicture();
      },
    }));

    useEffect(() => {
      if (!$handler.current) {
        return;
      }

      let isDrag = false;
      let startX = 0;
      let currentX = 0;

      function handleDown(e: MouseEvent | TouchEvent) {
        if (isDrag) {
          return;
        }

        onValidStart?.();

        isDrag = true;
        startX = (e as TouchEvent).touches
          ? (e as TouchEvent).touches[0].pageX
          : (e as MouseEvent).pageX;
        currentX = startX;
        dragStartTime.current = Date.now();
      }

      function handleMove(e: MouseEvent | TouchEvent) {
        if (!isDrag) {
          return;
        }

        const min = 0;
        const max = $bar.current!.clientWidth - $handler.current!.clientWidth;

        currentX = (e as TouchEvent).touches
          ? (e as TouchEvent).touches[0].pageX
          : (e as MouseEvent).pageX;
        const diff = Math.max(Math.min(currentX - startX, max), min);
        pos.current.x = diff;
        force();
      }

      function handleUp() {
        if (isDrag) {
          dragEndTime.current = Date.now();

          if (getPictureState.current && uuid.current && $cover.current) {
            const coverOriginWidth = coverOriginSize.current.width;
            const coverWidth = $cover.current.clientWidth;

            const scale = coverOriginWidth / coverWidth;

            const pointJson = JSON.stringify({
              x: pos.current.x * scale,
              // 固定5
              y: 5,
            });

            checkPicture({
              captchaType: 'blockPuzzle',
              token: getPictureState.current.data.repData.token,
              pointJson: getPictureState.current.data.repData.secretKey
                ? aesEncrypt(pointJson, getPictureState.current.data.repData.secretKey)
                : pointJson,
              ts: Date.now(),
            })
              .then((res) => {
                if (res && res.repCode && res.repCode === '0000' && res.repData) {
                  const tokenStr = `${res.repData.token}---${pointJson}`;

                  const token = getPictureState.current.data.repData.secretKey
                    ? aesEncrypt(tokenStr, getPictureState.current.data.repData.secretKey)
                    : tokenStr;

                  onSuccess?.(token, res.repData);

                  return;
                }
                handleRollback();
              })
              .catch(() => {
                handleRollback();
              })
              .finally(() => {
                onValidEnd?.();
              });
          }
        }

        isDrag = false;
      }

      $handler.current.addEventListener('mousedown', handleDown);
      document.addEventListener('mousemove', handleMove);
      document.addEventListener('mouseup', handleUp);

      $handler.current.addEventListener('touchstart', handleDown);
      document.addEventListener('touchmove', handleMove);
      document.addEventListener('touchend', handleUp);

      return () => {
        $handler.current?.removeEventListener('mousedown', handleDown);
        document.removeEventListener('mousemove', handleMove);
        document.removeEventListener('mouseup', handleUp);
        $handler.current?.removeEventListener('touchstart', handleDown);
        document.removeEventListener('touchmove', handleMove);
        document.removeEventListener('touchend', handleUp);
      };
    }, []);

    const getImgSuccess = Boolean(getPictureState.current.data?.success);
    const checkImgSuccess = Boolean(checkPictureState.current.data?.success);
    const disabled = !getImgSuccess || checkPictureState.current.loading;
    const errMsg = getPictureState.current.msg || checkPictureState.current.msg;

    return (
      <div
        className={cs(
          styles.wrap,
          {
            [styles.disabled]: disabled,
            [styles['get-img-success']]: getImgSuccess,
            [styles['check-img-success']]: checkImgSuccess,
            [styles['visible-err-msg']]: visibleErrMsg,
          },
          className,
        )}
      >
        <div className={styles.canvas}>
          <Spin spinning={checkPictureState.current.loading} delay={300} size="large">
            <div className={styles['canvas-cover']} ref={$cover}>
              {getImgSuccess && (
                <img src={getPictureState.current.data.repData?.originalImageBase64} />
              )}
            </div>

            <div className={styles['canvas-success']}>
              <div className={styles['canvas-success-icon']}>
                <CheckCircleFilled />
              </div>
              <div className={styles['canvas-success-text']}>
                验证成功,用了{((dragEndTime.current - dragStartTime.current) / 1000).toFixed(1)}s
              </div>
            </div>

            <div
              className={styles['canvas-puzzle-wrap']}
              style={{
                transform: `translate(${pos.current.x}px, 0)`,
              }}
            >
              <div className={styles['canvas-puzzle']} ref={$puzzle}>
                {getImgSuccess && (
                  <img src={getPictureState.current.data.repData?.jigsawImageBase64} />
                )}
              </div>
            </div>
          </Spin>
        </div>

        <div className={styles['drag-bar']} ref={$bar}>
          <div className={styles['drag-track']} />
          <div className={styles['drag-handler-wrap']}>
            <div
              className={styles['drag-handler']}
              ref={$handler}
              style={{
                left: pos.current.x + 'px',
                backgroundImage: `url(${$thumb})`,
              }}
              title="请控制拼图块对齐缺口"
            />
          </div>
        </div>

        <div className={styles['drag-status']}>
          <div className={styles['drag-status-note']} title={errMsg}>
            <span>{errMsg}</span>
          </div>
          <div className={styles['drag-status-action']}>
            <span
              className={cs(styles['drag-status-action-item'], {
                [styles.disabled]:
                  getPictureState.current.loading || checkPictureState.current.loading,
              })}
              onClick={getPicture.bind({}, {})}
            >
              <ReloadOutlined />
            </span>
          </div>
        </div>
      </div>
    );
  },
);
