import Button from '@/components/button';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  SyncOutlined,
} from '@ant-design/icons';
import { ConfigProvider, theme, Tooltip, TooltipProps } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import React from 'react';

export type ActionIconColor = 'green' | 'red' | 'blue' | 'yellow' | 'orange' | 'purple';
export type ActionIconType = 'add' | 'edit' | 'delete' | 'refresh' | 'detail';

export type ActionIcon = Omit<React.ComponentProps<typeof Button.Action>, 'icon'> & {
  color?: ActionIconColor | string;
  icon?: ActionIconType | React.ReactElement;
  tooltip?: TooltipProps['title'];
};

const AcitonIcon: React.FC<ActionIcon> = ({ color, icon, tooltip, children, ...rest }) => {
  const { token } = theme.useToken();

  const getColor = (color?: ActionIcon['color']) => {
    // 预设颜色
    const colorMap: { [key in ActionIconColor]: string } = {
      green: '#52c41a',
      red: '#f5222d',
      blue: '#1890ff',
      yellow: '#faad14',
      orange: '#fa541c',
      purple: '#722ed1',
    };

    if (!color) {
      return token.colorPrimary;
    }

    if (color in colorMap) {
      return colorMap[color as ActionIconColor];
    }

    return color;
  };

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: getColor(color),
        },
      }}
    >
      <Tooltip title={tooltip}>
        <Button.Action size="small" type="primary" {...rest as any}>
          {React.isValidElement(icon)
            ? icon
            : (() => {
                switch (icon) {
                  case 'add':
                    return <PlusOutlined />;
                  case 'edit':
                    return <EditOutlined />;
                  case 'delete':
                    return <DeleteOutlined />;
                  case 'refresh':
                    return <SyncOutlined />;
                  case 'detail':
                    return <EyeOutlined />;
                  default:
                    return null;
                }
              })()}
          {children}
        </Button.Action>
      </Tooltip>
    </ConfigProvider>
  );
};

export default AcitonIcon;
