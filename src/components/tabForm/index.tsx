import { ProForm, ProFormInstance, ProFormProps } from '@ant-design/pro-components';
import { Button, Space, Tabs } from 'antd';
import { initial } from 'lodash';
import { forwardRef, ReactNode, useImperativeHandle, useRef, useState } from 'react';

export type TabsFormProps = {
  tabs: Array<{
    label: React.ReactNode;
    formRefName: string;
    initialValues?: Record<string, any>;
    children: React.ReactNode;
    proFormProps?: Omit<ProFormProps, 'children'>;
  }>;
  
  onFinish?: (values: Record<string, any>[], isSave: boolean) => void;
  renderFooter?: (doms: {
    resetDom: React.ReactNode;
    saveDom: React.ReactNode;
    submitDom: React.ReactNode;
  }) => React.ReactNode[];
};

interface ErrorFields {
  name: string[];
  errors: string[];
  warnings: any[];
}

const RenderProForm = forwardRef<ProFormInstance | undefined, ProFormProps>((props, ref) => {
  return (
    <ProForm
      formRef={ref as React.RefObject<ProFormInstance<Record<string, any>> | undefined>}
      {...props}
      submitter={false}
      grid
      colProps={{
        xs: 24,
        sm: 24,
        md: 12,
        lg: 8,
        xl: 8,
        xxl: 6,
      }}
      omitNil={false}
      // labelCol={{ span: 7 }}
      labelWrap={true}
      scrollToFirstError={{
        behavior: 'smooth',
      }}
    >
      {props.children as ReactNode}
    </ProForm>
  );
});

const TabsForm = forwardRef<Record<string, ProFormInstance | null>, TabsFormProps>(
  (
    {
      tabs,
      onFinish: propsOnFinish,
      renderFooter = ({ resetDom, saveDom, submitDom }) => [resetDom, saveDom, submitDom],
    },
    ref,
  ) => {
    const formRefObj = useRef<Record<string, ProFormInstance | null>>({}); // 存储每个表单的ref
    const [currentKey, setCurrentKey] = useState(tabs[0].formRefName); // 当前激活的表单
    const [loading, setLoading] = useState<{
      reset: boolean;
      save: boolean;
      submit: boolean;
    }>(); // loading

    useImperativeHandle(ref, () => {
      return formRefObj.current;
    });

    const onFinish = async (isSave: boolean) => {
      const errorInfos: { errorFields: ErrorFields[] }[] = []; // 错误信息
      const result = await Promise.all(
        tabs.map(async (item) => {
          try {
            const values = await formRefObj?.current?.[
              item.formRefName
            ]?.validateFieldsReturnFormatValue?.();
            return values;
          } catch (info) {
            errorInfos.push(info as { errorFields: ErrorFields[] });
            setLoading({
              submit: false,
              save: false,
              reset: false,
            });
            return false;
          }
        }),
      );

      const errorIndex = result?.findIndex((i) => !i);
      if (errorIndex === -1) {
        setLoading({
          reset: true,
          save: true,
          submit: true,
        });
        propsOnFinish?.(result as Record<string, any>[], isSave);
      } else {
        setLoading({
          reset: false,
          save: false,
          submit: false,
        });
        const formRefName = tabs[errorIndex].formRefName;
        setCurrentKey(formRefName);
        formRefObj?.current?.[formRefName]?.scrollToField(
          errorInfos?.[errorIndex]?.errorFields?.[0]?.name?.[0],
        );
      }

      console.log(result, 'result-1-1-1-');
    };

    const onReset = async () => {
      await Promise.all(
        tabs.map(async (item) => {
          await formRefObj?.current?.[item.formRefName]?.resetFields?.();
        }),
      );
    };

    return (
      <>
        <Tabs activeKey={currentKey} onChange={(key) => setCurrentKey(key)}>
          {tabs?.map((item, index) => {
            return (
              <Tabs.TabPane key={item.formRefName} tab={item.label} forceRender>
                <RenderProForm
                  {...item.proFormProps}
                  key={item.formRefName}
                  ref={(form) => {
                    formRefObj.current[item.formRefName] = form as ProFormInstance;
                  }}
                  initialValues={item.initialValues}
                >
                  {item.children}
                </RenderProForm>
              </Tabs.TabPane>
            );
          })}
        </Tabs>

        <Space style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
          {renderFooter?.({
            resetDom: (
              <Button disabled={loading?.save || loading?.submit} onClick={onReset} key={'reset'}>
                重置
              </Button>
            ),
            saveDom: (
              <Button
                type="primary"
                onClick={() => onFinish(true)}
                loading={loading?.save}
                disabled={loading?.submit}
                key={'save'}
              >
                保存
              </Button>
            ),
            submitDom: (
              <Button
                type="primary"
                key={'submit'}
                onClick={() => onFinish(false)}
                loading={loading?.submit}
                disabled={loading?.save}
              >
                提交
              </Button>
            ),
          })}
        </Space>
      </>
    );
  },
);

export default TabsForm;
