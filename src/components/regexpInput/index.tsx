import type { InputProps } from 'antd';
import { Input, Select } from 'antd';
import useMergedState from 'rc-util/lib/hooks/useMergedState';

export interface RegexpInputProps extends InputProps {
  /** 下拉框宽度 */
  extraWidth?: number;
}

const RegexpInput: React.FC<RegexpInputProps> = ({
  extraWidth,
  defaultValue,
  value,
  onChange,
  ...rest
}) => {
  const [internalValue, setInternalValue] = useMergedState(defaultValue, {
    value,
  });

  return (
    <Input.Group compact>
      <Input
        style={{ width: `calc(100% - ${extraWidth}px)` }}
        value={internalValue}
        onChange={(v) => {
          setInternalValue(v.target.value);
          onChange?.(v);
        }}
        allowClear
        {...rest}
      />
      <Select
        placeholder="选择预设正则"
        style={{ width: extraWidth }}
        dropdownMatchSelectWidth={false}
        showSearch
        filterOption={(input, option) => {
          return ((option?.options as any[]) || []).some((v) =>
            v.label.toLowerCase().includes(input.toLowerCase()),
          );
        }}
        options={[
          {
            label: '常用表单',
            options: [
              {
                label: '匹配Email地址',
                value: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.toString(),
              },
              {
                label: '匹配URL地址',
                value:
                  /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i.toString(),
              },
              {
                label: '匹配手机号码',
                value:
                  /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$/.toString(),
              },
              {
                label: '匹配身份证号',
                value:
                  /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.toString(),
              },
              { label: '匹配邮编号', value: /^[1-9]\d{5}(?!\d)$/.toString() },
              {
                label: '匹配日期(yyyy-MM-dd)',
                value: /^[1-2][0-9][0-9][0-9]-[0-1]{0,1}[0-9]-[0-3]{0,1}[0-9]$/.toString(),
              },
            ],
          },
          {
            label: '常用字符',
            options: [
              { label: '匹配中文字符', value: /[\u4e00-\u9fa5]/gm.toString() },
              { label: '匹配双字节字符', value: /[^\x00-\xff]/gim.toString() },
              { label: '匹配行尾行首空白', value: /(^\s*)|(\s*$)/.toString() },
              { label: '只能输入数字', value: /^\d+$/.toString() },
              { label: '只能输入n个数字', value: /^\d{n}$/.toString() },
              { label: '至少输入n个以上的数字', value: /^\d{n,}$/.toString() },
              { label: '只能输入m到n个数字', value: /^\d{m,n}$/.toString() },
              { label: '只能由英文字母组成', value: /^[a-z]+$/i.toString() },
              { label: '只能由大写英文字母组成', value: /^[A-Z]+$/.toString() },
              { label: '只能由英文和数字组成', value: /^[a-z0-9]+$/i.toString() },
              { label: '只能由英文、数字、下划线组成', value: /^\w+$/.toString() },
            ],
          },
        ]}
        onSelect={(v) => {
          setInternalValue(v as string);
          onChange?.({
            target: {
              value: v as string,
            },
          } as any);
        }}
      />
    </Input.Group>
  );
};

RegexpInput.defaultProps = {
  extraWidth: 200,
};

export default RegexpInput;
