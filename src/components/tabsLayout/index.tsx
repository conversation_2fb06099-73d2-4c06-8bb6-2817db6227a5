import { CloseOutlined } from '@ant-design/icons';
import type { ProLayoutProps } from '@ant-design/pro-layout/es/ProLayout';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { history, useAccess, useLocation } from '@umijs/max';
import type { MenuDataItem } from '@umijs/route-utils';
import { getMatchMenu, transformRoute } from '@umijs/route-utils';
import { Dropdown, theme } from 'antd';
import cs from 'classnames';
import { createContext, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { createPortal } from 'react-dom';

import './index.less';
import { useKeepOutlets } from '../keepAlive';
import EventEmitter from 'eventemitter3';

const insertDom = document.createElement('div');


export const TabsLayoutEvent = new EventEmitter();

const reorder = (list: any[], startIndex: number, endIndex: number) => {
  const result = Array.from(list);
  const [removed] = result.splice(startIndex, 1);
  result.splice(endIndex, 0, removed);

  return result;
};

type List = MenuDataItem & {
  pathname: string;
  state?: Record<string, any>;
};

export type TableLayoutProps = React.PropsWithChildren & {
  props: ProLayoutProps;
};

const TabsLayout: React.FC<TableLayoutProps> = ({ props, children }) => {
  const token = theme.useToken();
  const location = useLocation();
  const access = useAccess();
  // 记录列表
  const [list, setList] = useState<List[]>([]);
  // 当前的key
  const [activeKey, setActiveKey] = useState<string>();

  const currentPathConfig: undefined | RouteData = useMemo(() => {
    const { menuData } = transformRoute(props?.route?.routes || [], undefined, undefined, true);
    // 动态路由匹配
    return getMatchMenu(props.location?.pathname as string, menuData).pop();
  }, [props.location?.pathname, props?.route?.routes]);



  useLayoutEffect(() => {
    const dom = document.querySelector('.ant-pro-layout-container > main');

    if (dom && dom.parentElement) {
      dom.parentElement!.insertBefore(insertDom, dom);
    }

    return () => {
      dom?.parentElement?.removeChild(insertDom);
    };
  }, []);

  useEffect(() => {
    const pathname = props.location?.pathname;

    if (!currentPathConfig) {
      return;
    }

    if (!pathname) {
      return;
    }

    if (!access.commonAccess(currentPathConfig.auth) || currentPathConfig.hideInTab) {
      return;
    }

    
    setList((draft) => {
      if (!draft.some((v) => v.pathname === pathname)) {
        return draft.concat({
          ...currentPathConfig,
          pathname,
          state: (location.state) as Record<string, any>
        });
      }

      return draft;
    });

    setActiveKey(pathname);
  }, [access, currentPathConfig, props.location?.pathname]);

  useEffect(() => {
    if (!list.length || !activeKey) {
      return;
    }

    const findIndex = list.findIndex((v) => v.pathname === activeKey);

    if (findIndex > -1) {
      document.querySelectorAll('.global-tabs-layout-item')[findIndex]?.scrollIntoView();
    }
  }, [list, activeKey]);

  const handleOnDragEnd: React.ComponentProps<typeof DragDropContext>['onDragEnd'] = (result) => {
    // dropped outside the list
    if (!result.destination) {
      return;
    }

    const newList = reorder(list, result.source.index, result.destination.index);

    setList(newList);
  };

  // 水平滚动条监听滑动事件
  useEffect(() => {
    const $list = document.querySelector('.global-tabs-layout-list') as HTMLDivElement | undefined;

    function scrollHorizontally(event: any) {
      const e = window.event || event;
      const delta = Math.max(-1, Math.min(1, e.wheelDelta || -e.detail));
      if ($list) {
        $list.scrollLeft -= delta * 50;
      }
      e.preventDefault();
    }

    if ($list) {
      $list.addEventListener('mousewheel', scrollHorizontally, false);
      $list.addEventListener('DOMMouseScroll', scrollHorizontally, false);
    }

    return () => {
      $list?.addEventListener('mousewheel', scrollHorizontally, false);
      $list?.addEventListener('DOMMouseScroll', scrollHorizontally, false);
    };
  }, []);

  const closeSelf = (targetKey: string | undefined) => {
    setList((draft) => {
      const findIndex = draft.findIndex((v) => v.pathname === targetKey);

      if (findIndex <= -1) {
        return draft;
      }

      if (draft[findIndex].pathname === activeKey) {
        const offsetActiveKey =
          findIndex + 1 < draft.length
            ? draft[findIndex + 1].pathname
            : findIndex - 1 >= 0
            ? draft[findIndex - 1].pathname
            : draft[0].pathname;

        setActiveKey(offsetActiveKey);
        history.push(offsetActiveKey);
      }

      return draft.filter((_, i) => i !== findIndex);
    });
  };

  useEffect(()=>{
    TabsLayoutEvent.on('close',(activeKey:string)=>{
      closeSelf(activeKey);
    })
  },[])


  const getDropMenu = (targetKey: string | undefined) => {
    const menu: React.ComponentProps<typeof Dropdown>['menu'] = {
      items: [],
    };

    if (list.length > 1) {
      menu.items!.push({
        key: '关闭',
        icon: <CloseOutlined />,
        label: '关闭',
        onClick: closeSelf.bind({}, targetKey),
      });
    }

    menu.items!.push({
      key: '关闭其他',
      icon: <CloseOutlined />,
      label: '关闭其他',
      onClick: () => {
        setList((draft) => {
          const newList = draft.filter((v) => v.pathname === targetKey);

          setActiveKey(newList[0].pathname);
          history.push(newList[0].pathname);

          return newList;
        });
      },
    });

    menu.items!.push({
      key: '关闭右侧',
      icon: <CloseOutlined />,
      label: '关闭右侧',
      onClick: () => {
        setList((draft) => {
          const findIndex = draft.findIndex((v) => v.pathname === targetKey);

          if (findIndex <= -1) {
            return draft;
          }

          return draft.filter((_, i) => i <= findIndex);
        });
      },
    });

    menu.items!.push({
      key: '关闭左侧',
      icon: <CloseOutlined />,
      label: '关闭左侧',
      onClick: () => {
        setList((draft) => {
          const findIndex = draft.findIndex((v) => v.pathname === targetKey);

          if (findIndex <= -1) {
            return draft;
          }

          return draft.filter((_, i) => i >= findIndex);
        });
      },
    });

    return menu;
  };

  const itemClassName = useEmotionCss(({ token }) => {
    return {
      '&::after': {
        backgroundColor: token.colorPrimary,
      },
      '&:hover,&.active': {
        color: token.colorPrimary,
        backgroundColor: token.colorPrimaryBgHover,

        '> .global-tabs-layout-item-close': {
          color: token.colorPrimary,
        },
      },
    };
  });

  return createPortal(
    <DragDropContext onDragEnd={handleOnDragEnd}>
      <div className="global-tabs-layout-placeholder" />
      <div className="global-tabs-layout">
        <Droppable direction="horizontal" droppableId="droppable">
          {(wrapProvided, wrapSnapshot) => {
            return (
              <div
                className={cs('global-tabs-layout-list', {
                  'is-draging': wrapSnapshot.isUsingPlaceholder,
                })}
                {...wrapProvided.droppableProps}
                ref={wrapProvided.innerRef}
              >
                {list.map((item, index) => {
                  return (
                    <Draggable key={item.pathname} draggableId={item.pathname} index={index}>
                      {(provided, snapshot) => (
                        <Dropdown menu={getDropMenu(item.pathname)} trigger={['contextMenu']}>
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={cs('global-tabs-layout-item', itemClassName, {
                              active: item.pathname === activeKey,
                            })}
                            onClick={() => {
                              if (item.pathname === activeKey) {
                                return;
                              }

                              setActiveKey(item.pathname);
                              history.push(item.pathname, item?.state || {});
                            }}
                          >
                            <div className="global-tabs-layout-item-label">{item.name}</div>

                            {list.length > 1 && (
                              <div
                                onClick={(e) => {
                                  e.stopPropagation();

                                  closeSelf(item.pathname);
                                }}
                                className="global-tabs-layout-item-close"
                              >
                                <CloseOutlined />
                              </div>
                            )}
                          </div>
                        </Dropdown>
                      )}
                    </Draggable>
                  );
                })}
                {wrapProvided.placeholder}
              </div>
            );
          }}
        </Droppable>
      </div>
    </DragDropContext>,
    insertDom,
  );
};

export default TabsLayout;
