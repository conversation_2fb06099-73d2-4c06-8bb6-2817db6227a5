// rest
.ant-pro-global-header,
.ant-pro-top-nav-header {
  // border-bottom: 1PX solid #f0f0f0;
  box-shadow: none !important;
}

.global-tabs-layout-placeholder {
  height: 50PX;
}

.global-tabs-layout {
  position: fixed;
  top: 56PX;
  z-index: 10;
  display: flex;
  align-items: center;
  width: 100%;
  height: 50PX;
  padding-right: 20PX;
  padding-left: 20PX;
  background-color: #fff;
  border-bottom: 1PX solid #f0f0f0;
}

.global-tabs-layout-extra {
  flex-shrink: 0;
  margin-left: 10PX;
}

.global-tabs-layout-list {
  display: flex;
  flex-grow: 1;
  align-items: center;
  min-width: 0;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;

  // &::-webkit-scrollbar {
  //   width: 0;
  //   height: 0;
  // }
}

.global-tabs-layout-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 34PX;
  margin-right: 5PX;
  padding-right: 20PX;
  padding-left: 20PX;
  line-height: 34PX;
  white-space: nowrap;
  background-color: transparent;
  cursor: pointer !important;

  &-label {
    font-size: 14PX;
  }

  &-close {
    // width: 0;
    width: 14PX;
    height: 14PX;
    margin-top: 1PX;
    margin-left: 5PX;
    overflow: hidden;
    color: #999;
    font-size: 12PX;
    line-height: 14PX;
    text-align: center;
    border-radius: 7PX;
    transition: width 0.3s;
  }

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2PX;
    // background-color: @primary;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    content: '';
  }

  &:hover,
  &.active {
    // color: @primary;
    // background-color: lighten(@primary, 42%);

    > .global-tabs-layout-item-close {
      width: 14PX;
      // color: @primary;

      &:hover {
        color: #fff;
        background-color: #c0c4cc;
      }
    }

    &::after {
      width: 100%;
    }
  }
}

// rest
// .ant-pro-basicLayout-fix-siderbar .ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {

// }
// end rest
