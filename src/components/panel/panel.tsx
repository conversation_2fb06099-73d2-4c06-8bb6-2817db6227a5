import { BetaSchemaForm } from '@ant-design/pro-components';
import { Drawer } from 'antd';
import * as fs from 'fs';
import * as handlebars from 'handlebars';
import * as path from 'path';
import { useEffect, useMemo, useState } from 'react';
import { SwaggerObj } from '.';
import { DrawerProps } from '../drawer';

const template = `
  import ProTable from '@/components/proTable';
  import { ProTableProps } from '@/components/proTable';
  import { dict2ValueEnum,arr2ValueEnum } from '@/utils';
  import { PageContainer } from '@ant-design/pro-components';
  import { mobileValidator,emailValidator,creditCodeValidator } from '@/config/rule'
  import { useModel } from '@umijs/max';

  const Page: React.FC = () => {
    const { initialState } = useModel('@@initialState');
    const columns: ProTableProps<BASE.{{recordsVo}}>['columns'] = [
      {{#each columns}}
        {
          {{#each this}}
            {{#if (eq @key 'valueEnum')}}
              {{@key}}: arr2ValueEnum({{{this}}})
            {{else}}
              {{#if (isObject this)}}
                "{{@key}}": {{{toJson this}}},
              {{else if (isBoolean this)}}
                "{{@key}}": {{this}},
              {{else}}
                "{{@key}}": "{{this}}",
              {{/if}}
            {{/if}}
          {{/each}}
        },
      {{/each}}
    ];

    return (
      <PageContainer>
        <ProTable<BASE.{{recordsVo}}> crudKey="{{crudKey}}" columns={columns} />
      </PageContainer>
    );
  };

  export default Page;
`;

// 注册一个helper，用来判断是否是对象
handlebars.registerHelper('isObject', function (value) {
  return typeof value === 'object';
});
// 注册一个helper，用来将对象转换为json字符串
handlebars.registerHelper('toJson', function (value) {
  return JSON.stringify(value);
});
// 注册一个helper，用来判断是否是布尔值
handlebars.registerHelper('isBoolean', function (value) {
  return typeof value === 'boolean';
});

handlebars.registerHelper('eq', function (v1, v2) {
  return v1 === v2;
});

type PanelProps = DrawerProps & {
  swaggerJson: SwaggerObj | undefined;
};

// 1、选择本地src下的目录下的某个文件
// 2、选择需要生成的swagger接口
const Panel: React.FC<PanelProps> = ({ swaggerJson, ...rest }) => {
  const [srcDir, setSrcDirs] = useState<string[]>([]);

  // // 获取src下的目录
  const getDirectoryTree = (requireContext: any, path: any) => {
    const tree: any = {};

    // 获取所有文件路径
    const keys = requireContext.keys();
    keys.forEach((key: any) => {
      const pathArr = key.split('/');
      let currentNode = tree;

      // 遍历文件路径，生成 tree 结构
      for (let i = 0; i < pathArr.length; i++) {
        const name = pathArr[i];

        if (name === '.') continue;
        if (!currentNode[name]) {
          currentNode[name] = { children: {} };
        }

        currentNode = currentNode[name].children;
      }
    });

    // 将 tree 转换为需要的格式
    const formatTree: any = (tree: any, currentPath: any) => {
      const res = [];
      // eslint-disable-next-line guard-for-in
      for (const name in tree) {
        const path = currentPath ? `${currentPath}/${name}` : name;
        const children = tree[name].children;

        if (Object.keys(children).length) {
          res.push({
            name,
            path,
            children: formatTree(children, path),
          });
        } else {
          res.push({ name, path });
        }
      }

      return res;
    };

    return formatTree(tree, path);
  };

  // 获取src下的目录
  const getSrcDir = () => {
    const context = (require as any).context('/src/pages', true, /\.tsx$/);
    const tree = getDirectoryTree(context, '/src/pages');
    setSrcDirs(tree);
  };

  const modules = useMemo(() => {
    const tags = swaggerJson?.tags || [];

    // 递归tags，将——前面的作为父级，后面的作为子级
    const getTags = (tags: any) => {
      const result: any = [];
      tags.forEach((tag: any) => {
        const [parent, child] = tag.name.split('——');
        const parentIndex = result.findIndex((item: any) => item.label === parent);
        if (parentIndex > -1) {
          result[parentIndex].children.push({
            label: child,
            value: child,
            parent: parent,
          });
        } else {
          result.push({
            label: parent,
            value: parent,
            children: [
              {
                label: child,
                value: child,
                parent: parent,
              },
            ],
          });
        }
      });
      return result;
    };
    return getTags(tags);
  }, [swaggerJson]);

  // 获取接口对应的配置
  const getApiConfig = (tag: string) => {
    const paths = swaggerJson?.paths || {};
    let apiConfig = {};
    // 遍历所有的接口
    Object.keys(paths).forEach((path) => {
      // path:/orgDistrictCountyInfo/voPage
      const crudKey = path.split('/')[1];
      // 只需要voPage对应的接口对象就可以了
      if (path !== `/${crudKey}/voPage`) return;
      const pathObj = paths[path];
      // 对应的接口对象
      const apiObj: any = Object.values(pathObj)[0];
      const { tags = [], responses } = apiObj;
      if (!tags.includes(tag)) return;
      // @ts-ignore
      const responsesVo = Object.values(responses['200'].content)[0].schema.$ref.split('/').pop();
      const { properties } = swaggerJson?.components.schemas[responsesVo];
      const dataVo = properties['data'].$ref.split('/').pop();
      const { properties: dataProperties } = swaggerJson?.components.schemas[dataVo];
      const recordsVo = dataProperties['records'].items.$ref.split('/').pop();
      const { properties: recordsProperties } = swaggerJson?.components.schemas[recordsVo];

      const _tag = tags[0];
      const [moduleName, featureName] = _tag.split('——');
      apiConfig = {
        moduleName,
        featureName,
        crudKey,
        recordsVo,
        columns: [],
      };
    });
    return apiConfig;
  };

  useEffect(() => {
    getSrcDir();
  }, []);

  const onFinish = async (values: any) => {
    const crudObj = getApiConfig(values.module);
    const code = handlebars.compile(template)(crudObj);
    // 将代码写入到对应的文件中
    const filePath = path.join(values?.srcDir);
    // const filePath = path.join(featureDir, `index.tsx`);
    fs.writeFileSync(filePath, code);
  };

  return (
    <Drawer size="large" title="CRUD辅助工具" maskClosable {...rest}>
      <BetaSchemaForm
        grid
        colProps={{ span: 12 }}
        columns={[
          [
            {
              dataIndex: 'srcDir',
              title: '代码存放目录',
              valueType: 'treeSelect',
              fieldProps(form, config) {
                return {
                  options: srcDir,
                  fieldNames: { label: 'name', value: 'path' },
                };
              },
            },
            {
              dataIndex: 'module',
              title: '选择模块',
              valueType: 'select',
              fieldProps(form, config) {
                return {
                  options: modules,
                  onChange: (value: any, label: any, extra: any) => {
                    form.setFieldsValue({
                      module: `${label.parent}——${label.value}`,
                    });
                  },
                };
              },
            },
          ],
        ]}
        layoutType="StepsForm"
        steps={[
          {
            title: '第一步',
          },
        ]}
        onCurrentChange={(current) => {
        }}
        onFinish={onFinish}
      />
    </Drawer>
  );
};

export default Panel;
