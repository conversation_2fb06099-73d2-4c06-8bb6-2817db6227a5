import { CustomerServiceOutlined } from '@ant-design/icons';
import { FloatButton } from 'antd';
import { useEffect, useState } from 'react';
import Panel from './panel';

interface Tag {
  name: string;
  description: string;
  'x-order': string;
}

interface Info {
  title: string;
  contact: Contact;
  version: string;
}

interface Contact {
  name: string;
  url: string;
  email: string;
}

interface Server {
  url: string;
  description: string;
}

interface Components {
  schemas: any;
}

export interface SwaggerObj {
  openapi: string;
  info: Info;
  servers: Server[];
  tags: Tag[];
  paths: Record<string, any>;
  components: Components;
}

type CrudPanelProps = {
  swaggerUrl: string;
};

export const CrudPanel: React.FC<CrudPanelProps> = ({ swaggerUrl, ...rest }) => {
  const [open, setOpen] = useState<boolean>(false);
  const [swaggerJson, setSwaggerJson] = useState<SwaggerObj>();

  const getSwaggerJson = async () => {
    fetch('http://************:8000/umi-plugins_base.json')
      .then((response) => response.json())
      .then((data) => {
        setSwaggerJson(data);
      })
      .catch((error) => console.error(error));
  };
  useEffect(() => {
    getSwaggerJson();
  }, []);

  return (
    <div>
      <FloatButton
        shape="circle"
        type="primary"
        style={{ bottom: 50, right: 50 }}
        onClick={() => setOpen(!open)}
        icon={<CustomerServiceOutlined />}
      />
      {open && <Panel swaggerJson={swaggerJson} open={open} onClose={() => setOpen(false)} />}
    </div>
  );
};
