// import { flowRecordDetails } from '@/services/base/liuchengjiekou';
// import { useRequest } from '@umijs/max';
// import { Descriptions, Popover, Skeleton, Timeline } from 'antd';
// import classNames from 'classnames';
// import dayjs from 'dayjs';
// import duration from 'dayjs/plugin/duration';
// import styles from './process.less';

// dayjs.extend(duration);
// type ProcessProps = {
//   // 流程id
//   flowId?: string;
// };

// const labelType = {
//   AGREE: '同意',
//   REJECT: '拒绝',
//   APPROVING: '审核中',
//   APPROVED: '审核通过',
//   APPROVED_REJECT: '审核拒绝',
//   NOT_REACH: '未达到',
//   SUBMIT: '提交',
//   PROCESSING: '处理中',
// };
// const formatTime = (s: number): string => {
//   let seconds: string | number = s;
//   const days = Math.floor(seconds / 86400)
//     .toString()
//     .padStart(2, '0');
//   seconds = seconds % 86400;
//   let hours = Math.floor(seconds / 3600)
//     .toString()
//     .padStart(2, '0');
//   seconds = seconds % 3600;
//   const minutes = Math.floor(seconds / 60)
//     .toString()
//     .padStart(2, '0');
//   seconds = (seconds % 60).toString().padStart(2, '0');

//   if (days !== '00') {
//     return `${days}天${hours}小时${minutes}分钟${seconds}秒`;
//   }
//   if (hours !== '00') {
//     return `${hours}小时${minutes}分钟${seconds}秒`;
//   }
//   if (minutes !== '00') {
//     return `${minutes}分钟${seconds}秒`;
//   }
//   return `${seconds}秒`;
// };

// const Node = ({ data }: { data: any }) => {
//   return (
//     <div className={styles.content}>
//       <div>
//         <div style={{ color: '#000', fontSize: 16 }}>{data?.nodeName}</div>
//         {data?.actionRecordVOList?.map((i: any) => {
//           return (
//             <div key={i.id}>
//               <div>
//                 <Popover
//                   content={
//                     <Descriptions style={{ width: 300 }} column={1} bordered size="small">
//                       <Descriptions.Item label="昵称">{i?.userInfo?.fullName}</Descriptions.Item>
//                       <Descriptions.Item label="用户名">{i?.userInfo?.userName}</Descriptions.Item>
//                       <Descriptions.Item label="用户角色">
//                         {i?.userInfo?.roleName}
//                       </Descriptions.Item>
//                     </Descriptions>
//                   }
//                   title="用户信息"
//                 >
//                   <a>{i?.userInfo?.fullName}</a>
//                 </Popover>
//                 {i?.actionName && `- ${i?.actionName}`}
//               </div>
//               <div>{`接收时间：${dayjs(i?.createTime).format('YYYY-MM-DD HH:mm:ss')}`}</div>
//               <div>{`提交时间：${
//                 (i?.id &&
//                   dayjs(i?.createTime).add(i?.timeConsume, 's').format('YYYY-MM-DD HH:mm:ss')) ||
//                 '--'
//               }`}</div>
//               <div>{`用时：${formatTime(i?.timeConsume) || '--'}`}</div>
//               {i?.extend2 && (
//                 <div>
//                   {Object.keys(JSON.parse(i?.extend2))?.map((key) => {
//                     return <div key={key}>{`${key}：${JSON.parse(i?.extend2)[key] || '--'}`}</div>;
//                   })}
//                 </div>
//               )}
//               <div>{`审核说明：${i?.extend1 || '--'}`}</div>
//             </div>
//           );
//         })}
//       </div>
//     </div>
//   );
// };

// const Process: React.FC<ProcessProps> = ({ flowId }) => {
//   const flowList = useRequest(
//     () => {
//       return flowRecordDetails({ id: flowId });
//     },
//     {
//       formatResult: (res) => {
//         return res?.data?.nodeRecordVOList?.map((i) => {
//           return {
//             label: (
//               <span
//                 className={classNames(styles.label, {
//                   [styles.label_active]: i?.actionRecordVOList?.length,
//                 })}
//               >
//                 {labelType?.[i.nodeRecordState!]}
//               </span>
//             ),
//             children: <Node data={i} />,
//             color: i?.actionRecordVOList?.length ? 'blue' : 'gray',
//           };
//         });
//       },
//     },
//   );
//   return (
//     <Skeleton loading={flowList?.loading} active>
//       <Timeline mode={'left'} items={flowList?.data || []} />
//     </Skeleton>
//   );
// };

// export default Process;

export default () => {
  return <div>123</div>;
};
