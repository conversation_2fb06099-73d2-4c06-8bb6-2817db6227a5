import { Modal, ModalProps, Tabs, TabsProps } from 'antd';
import { launch } from 'antd-pro-crud';

type ProcessProps = {
  detailTitle?: string;
  content?: React.ReactNode;
  /** 流程id */
  flowId: string;
};

const ProcesWithDetail: React.FC<ProcessProps & ModalProps> = ({
  content,
  detailTitle,
  flowId,
  ...rest
}) => {
  const items: TabsProps['items'] = [
    {
      key: '1',
      label: detailTitle || '详细信息',
      children: content,
    },
    // {
    //   key: '2',
    //   label: `流程信息`,
    //   children: <Process flowId={flowId} />,
    // },
  ];
  return (
    <Modal {...rest}>
      <Tabs defaultActiveKey="1" items={items} onChange={console.log} />
    </Modal>
  );
};

export default launch(ProcesWithDetail);
