.input {
  :global {
    .ant-select-selection-item > .anticon {
      font-size: 16px;
    }
  }
}

.dropdown {
  width: initial !important;
  min-width: initial !important;
  padding: 0;
}

.list {
  .item {
    position: relative;
    width: 20%;
    padding-bottom: 20%;

    &:nth-child(odd) {
      background-color: #f8f8f0;
    }

    &.active {
      color: #fff;
      background-color: @primary;
    }

    > button {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      color: inherit;
      background: none;
      border: none;
      border-radius: 5px;
      outline: none;
      cursor: pointer;
      transition: 0.1s background;

      &:hover,
      &:focus-visible {
        background-color: rgba(@primary, 0.2);
      }

      > .icon {
        font-size: 24px;
      }
    }
  }
}
