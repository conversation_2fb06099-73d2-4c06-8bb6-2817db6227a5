import { antdIconList, IconAnt } from '@/components/icon/antd';
import { getBrowserScrollWidth } from '@/utils';
import { Tooltip } from 'antd';
import cs from 'classnames';
import useMergedState from 'rc-util/lib/hooks/useMergedState';
import { FixedSizeGrid } from 'react-window';
import type { SelectProps } from '../index';
import Select from '../index';

import { useMemo } from 'react';
import styles from './index.less';

const options: IconSelectProps['options'] = antdIconList.map((key) => {
  return {
    label: <IconAnt type={key} />,
    value: key,
  };
});

export type IconSelectProps = SelectProps;

const IconSelect: React.FC<IconSelectProps> = ({
  defaultValue,
  value,
  onChange,
  popupClassName,
  className,
  searchValue,
  onSearch,
  ...rest
}) => {
  const [internalValue, setInternalValue] = useMergedState(defaultValue, {
    value,
  });
  const [internalSearchValue, setInternalSearchValue] = useMergedState(searchValue, {
    value: searchValue,
  });

  const list = useMemo(() => {
    if (!internalSearchValue) {
      return options;
    }

    const reg = new RegExp(internalSearchValue);

    return options.filter((v) => {
      return reg.test(v.value);
    });
  }, [internalSearchValue]);

  const DROPDOWN_WIDTH = 300;
  const DROPDOWN_HEIGHT = 320;
  const COLUMN_COUNT = 5;
  const COLUMN_WIDTH = (DROPDOWN_WIDTH - getBrowserScrollWidth()) / 5;
  const ROW_COUNT = Math.ceil(list.length / 5);
  const ROW_HEIGHT = COLUMN_WIDTH;

  return (
    <Select
      {...rest}
      options={options}
      defaultValue={defaultValue}
      value={internalValue}
      onChange={(v, o) => {
        setInternalValue(v);
        onChange?.(v, o);
      }}
      showSearch
      searchValue={internalSearchValue}
      onSearch={(v) => {
        setInternalSearchValue(v);
        onSearch?.(v);
      }}
      allowClear
      className={cs(styles['input'], className)}
      popupClassName={cs(styles['dropdown'], popupClassName)}
      dropdownMatchSelectWidth={false}
      dropdownRender={(v) => {
        return (
          <FixedSizeGrid
            width={DROPDOWN_WIDTH}
            height={DROPDOWN_HEIGHT}
            columnCount={COLUMN_COUNT}
            columnWidth={COLUMN_WIDTH}
            rowCount={ROW_COUNT}
            rowHeight={ROW_HEIGHT}
            className={styles['list']}
          >
            {({ columnIndex, rowIndex, style }) => {
              const item = list[rowIndex * 5 + columnIndex];

              if (!item) {
                return null;
              }

              return (
                <Tooltip
                  title={item.value}
                  key={item.value}
                  destroyTooltipOnHide={{ keepParent: false }}
                >
                  <div
                    className={cs(styles['item'], {
                      [styles['active']]: item.value === internalValue,
                    })}
                    onClick={() => {
                      setInternalValue(item.value);
                      onChange?.(item.value, item);
                    }}
                    style={style}
                  >
                    <button type="button">
                      <div className={styles['icon']}>{item.label}</div>
                    </button>
                  </div>
                </Tooltip>
              );
            }}
          </FixedSizeGrid>
        );
      }}
    />
  );
};

export default IconSelect;
