import { useMemo } from 'react';
import { useModel } from 'umi';
import type { SelectProps } from '../index';
import Select from '../index';

export type DictSelectProps = SelectProps & {
  dicCode: string;
  /** 格式化选项 */
  formatOptions?: (v: DictSelectProps['options']) => DictSelectProps['options'];
};

const DictSelect: React.FC<DictSelectProps> = ({ dicCode, formatOptions, ...rest }) => {
  const { initialState } = useModel('@@initialState');

  const options = useMemo(() => {
    let res: DictSelectProps['options'] = [];

    if (initialState?.dict) {
      const list = initialState.dict[dicCode] || [];

      list.forEach((item) => {
        res?.push({
          label: item.dicName,
          value: item.dicValue,
        });
      });
    }

    if (formatOptions) {
      res = formatOptions(res);
    }

    return res;
  }, [dicCode, initialState?.dict]);

  return <Select options={options} placeholder="请选择" {...rest} />;
};

export { DictSelect };
