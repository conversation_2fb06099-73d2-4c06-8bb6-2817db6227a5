import { Select as AntSelect } from 'antd';
import Color from './color';
import Icon from './icon';

import cs from 'classnames';

import styles from './index.less';

export type SelectProps = React.ComponentProps<typeof AntSelect>;

const Select: React.FC<SelectProps> & {
  SECRET_COMBOBOX_MODE_DO_NOT_USE: typeof AntSelect.SECRET_COMBOBOX_MODE_DO_NOT_USE;
  Option: typeof AntSelect.Option;
  OptGroup: typeof AntSelect.OptGroup;
  Icon: typeof Icon;
  Color: typeof Color;
} = ({ className, ...rest }) => {
  return <AntSelect className={cs(styles['select'], className)} {...rest} />;
};

Select.SECRET_COMBOBOX_MODE_DO_NOT_USE = AntSelect.SECRET_COMBOBOX_MODE_DO_NOT_USE;
Select.Option = AntSelect.Option;
Select.OptGroup = AntSelect.OptGroup;
Select.Icon = Icon;
Select.Color = Color;

export default Select;

export * from 'antd/lib/input';
