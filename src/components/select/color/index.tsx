import colors from '@/config/color';
import { Tooltip } from 'antd';
import cs from 'classnames';
import useMergedState from 'rc-util/lib/hooks/useMergedState';
import { SketchPicker } from 'react-color';
import type { SelectProps } from '../index';
import Select from '../index';

import styles from './index.less';

export type ColorSelectProps = SelectProps;

export function renderColor(color?: ColorSelectProps['value']): React.ReactElement {
  if (typeof color === 'string') {
    return (
      <Tooltip title={color} color={color}>
        <div className={styles['color-render-wrap']}>
          <div
            className={styles['color-render']}
            style={{
              background: color,
            }}
          />
        </div>
      </Tooltip>
    );
  }

  return <></>;
}

function isHexColor(str: string) {
  return /^#?([0-9A-F]{3}|[0-9A-F]{6})$/i.test(str);
}

const ColorSelect: React.FC<ColorSelectProps> = ({
  defaultValue,
  value,
  onChange,
  popupClassName,
  className,
  ...rest
}) => {
  const [internalValue, setInternalValue] = useMergedState(defaultValue, {
    value,
  });

  return (
    <Select
      {...rest}
      defaultValue={defaultValue}
      value={internalValue}
      onChange={(v, o) => {
        setInternalValue(v);
        onChange?.(v, o);
      }}
      options={
        internalValue
          ? ([
              {
                value: internalValue,
                label: renderColor(internalValue),
              },
            ] as ColorSelectProps['options'])
          : []
      }
      allowClear
      className={cs(styles['input'], className)}
      popupClassName={cs(styles['dropdown'], popupClassName)}
      dropdownRender={() => {
        return (
          <SketchPicker
            color={isHexColor(internalValue as string) ? (internalValue as string) : undefined}
            onChangeComplete={(color) => {
              setInternalValue(color.hex);
              onChange?.(
                color.hex,
                {} as ArgumentsType<NonUndefined<ColorSelectProps['onChange']>>[1],
              );
            }}
            presetColors={colors}
          />
        );
      }}
    />
  );
};

export default ColorSelect;
