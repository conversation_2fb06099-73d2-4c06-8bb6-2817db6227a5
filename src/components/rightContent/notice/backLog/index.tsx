import Dict from '@/components/dict';
import * as Enums from '@/enums/msg';
import { backlogInfoPage } from '@/services/base/msgTodo';
import { formatDate } from '@/utils';
import { history, useAccess, useRequest } from '@umijs/max';
import { Empty, Skeleton, Tag } from 'antd';
import cs from 'classnames';
import styles from './index.less';

export type ListProps = {};

const List: React.FC<ListProps> = () => {
  const access = useAccess();
  const list = useRequest(() => {
    return backlogInfoPage({
      page: 1,
      size: 10,
      state: Enums.BackLogStatus.待处理,
    }).then((res) => {
      return {
        ...res,
        data: res.data?.records || [],
      };
    });
  });

  const handleMore = () => {
    history.push({
      pathname: '/msg/backlog',
    });
  };
  const data = list.data || [];
  return (
    <Skeleton loading={list.loading} style={{ padding: 24 }}>
      <div className={styles['list']}>
        {data.length <= 0 && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无待办" />}
        {data.map((item, i) => {
          return (
            <div
              className={cs(styles['item'], {
                [styles['read']]: item.state === Enums.BackLogStatus.已处理,
              })}
              key={item.id || i}
              onClick={() => {
                if (item.jumpUrl) {
                  window.open(item.jumpUrl);
                }
              }}
            >
              <div className={styles['title']}>
                <div className={styles['text']}>{item.backlogBody}</div>
                <div className={styles['extra']}>
                  <Dict className={styles['tag']} dicCode="urgency" value={item.urgency} />
                </div>
              </div>

              <div className={styles['bot']}>
                <div className={styles['des']}>
                  <Tag color="red">{item.backlogType}</Tag>
                </div>
                <div className={styles['datetime']}>{formatDate(item.createTime)}</div>
              </div>
            </div>
          );
        })}
      </div>

      <div className={styles['footer']}>
        {access.commonAccess('admin:/msg/backlog') && (
          <div onClick={handleMore} className={styles['action']}>
            查看更多
          </div>
        )}
      </div>
    </Skeleton>
  );
};

export default List;
