import Dict from '@/components/dict';
import * as Enums from '@/enums/msg';
import { alert_notify_detail } from '@/pages/msg/notify/detail';
import { notifyInfoPage } from '@/services/base/xiaoximokuaitongzhixinxijiekou';
import { formatDate } from '@/utils';
import { history, useAccess, useRequest } from '@umijs/max';
import { Empty, Skeleton } from 'antd';
import cs from 'classnames';
import styles from './index.less';

export type ListProps = {
  onChange: () => void;
};

const List: React.FC<ListProps> = ({ onChange }) => {
  const access = useAccess();
  const list = useRequest(() => {
    return notifyInfoPage({
      page: 1,
      size: 10,
      state: Enums.NotifyStatus.未读,
    }).then((res) => {
      return {
        ...res,
        data: res.data?.records || [],
      };
    });
  });

  const handleMore = () => {
    history.push({
      pathname: '/msg?activeKey=notify',
    });
  };

  const data = list.data || [];

  return (
    <Skeleton loading={list.loading} style={{ padding: 24 }}>
      <div className={styles['list']}>
        {data.length <= 0 && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无通知" />}
        {list.data?.map((item, i) => {
          return (
            <div
              className={cs(styles['item'], {
                [styles['read']]: item.state === Enums.BackLogStatus.已处理,
              })}
              key={item.id || i}
              onClick={() => {
                alert_notify_detail({
                  userType: 'receiver',
                  data: item,
                  onRead: () => {
                    onChange();
                    list.refresh();
                  },
                });
              }}
            >
              <div className={styles['title']}>
                <div className={styles['text']}>{item.notifyBody}</div>
                <div className={styles['extra']}>
                  <Dict className={styles['tag']} dicCode="notifyType" value={item.notifyType} />
                </div>
              </div>

              <div className={styles['bot']}>
                <div className={styles['datetime']}>{formatDate(item.createTime)}</div>
              </div>
            </div>
          );
        })}
      </div>

      <div className={styles['footer']}>
        {access.commonAccess('admin:/msg') && (
          <div onClick={handleMore} className={styles['action']}>
            查看更多
          </div>
        )}
      </div>
    </Skeleton>
  );
};

export default List;
