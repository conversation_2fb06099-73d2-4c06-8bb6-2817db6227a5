import { noticeInfoCount } from '@/services/base/msgNotice';
import { BellOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Badge, Tabs } from 'antd';
import HeaderDropdown from '../headerDropdown';
import BackLog from './backLog';
import styles from './index.less';
import Notice from './notice';
import Notify from './notify';

export type NoticeProps = {};

const Component: React.FC<NoticeProps> = () => {
  const countData = useRequest(async () => {
    const res = await noticeInfoCount({
      skipErrorHandler: true,
    });

    /** 待办未读数量 */
    const backlogCount = Number(res.data?.backlogCount) || 0;
    /** 公告未读数量 */
    const noticeCount = Number(res.data?.noticeCount) || 0;
    /** 通知未读数量 */
    const notifyCount = Number(res.data?.notifyCount) || 0;
    /** 总计未读数量 */
    const total = backlogCount + notifyCount + noticeCount;

    return {
      ...res,
      data: {
        backlogCount,
        notifyCount,
        noticeCount,
        total,
      },
    };
  });

  /** 待办未读数量 */
  const backlogCount = countData.data?.backlogCount || 0;
  /** 公告未读数量 */
  const noticeCount = countData.data?.noticeCount || 0;
  /** 通知未读数量 */
  const notifyCount = countData.data?.notifyCount || 0;

  const content = (
    <div className={styles['dropdown']}>
      <Tabs
        centered
        className={styles['tabs']}
        items={[
          {
            label: '公告' + (noticeCount > 0 ? ` (${noticeCount})` : ''),
            key: '公告',
            forceRender: true,
            children: (
              <Notice
                onChange={() => {
                  countData.run();
                }}
              />
            ),
          },
          // {
          //   label: '通知' + (notifyCount > 0 ? ` (${notifyCount})` : ''),
          //   key: '通知',
          //   forceRender: true,
          //   children: (
          //     <Notify
          //       onChange={() => {
          //         countData.run();
          //       }}
          //     />
          //   ),
          // },
          // {
          //   label: '待办' + (backlogCount > 0 ? ` (${backlogCount})` : ''),
          //   key: '待办',
          //   forceRender: true,
          //   children: <BackLog />,
          // },
        ]}
      />
    </div>
  );

  return (
    <HeaderDropdown dropdownRender={() => content} placement="bottomRight">
      <div className={styles['notice']}>
        <Badge count={countData.data?.total} offset={[5, -5]}>
          <BellOutlined />
        </Badge>
      </div>
    </HeaderDropdown>
  );
};

export default Component;
