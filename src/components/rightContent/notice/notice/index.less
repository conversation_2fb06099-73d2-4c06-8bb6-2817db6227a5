.list {
  max-height: 400px;
  overflow: auto;

  &::-webkit-scrollbar {
    display: none;
  }
}

.item {
  padding: 12px;
  overflow: hidden;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  &:last-child {
    border-bottom: 0;
  }

  &.read {
    opacity: 0.4;
  }

  .title {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    .text {
      width: 75%;
      min-width: 0;

      .text-overflow();
    }

    .extra {
      width: 25%;
      margin-left: auto;
      text-align: right;

      .tag {
        margin: 0;
      }
    }
  }

  .bot {
    display: flex;
    align-items: center;

    .datetime {
      margin-left: auto;
    }
  }
}

.footer {
  display: flex;
  height: 46px;
  overflow: hidden;
  color: #333;
  line-height: 46px;
  text-align: center;
  border-top: 1px solid #ddd;
  border-radius: 0 0 4px 4px;
  transition: all 0.3s;

  .action {
    flex: 1 1 0;
    min-width: 0;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }

    + .action {
      border-left: 1px solid #ddd;
    }
  }
}
