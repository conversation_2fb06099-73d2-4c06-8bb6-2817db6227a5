.list {
  width: 300px;
  max-height: 400px;
  overflow: auto;
  background-color: #fff;

  &::-webkit-scrollbar {
    display: block;
  }

  .item {
    padding: 5px 24px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      .title {
        color: @primary-color;
      }
    }
  }
  .title_text_gray {
    color: rgba(0, 0, 0, 0.45)
  }
}
.notFound {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  padding: 20px;
  background: #fff;
}
