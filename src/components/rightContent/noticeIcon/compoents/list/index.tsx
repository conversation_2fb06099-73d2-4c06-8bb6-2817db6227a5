
import { noticeInfoRead } from '@/services/base/msgNotice';
import { notifyInfoRead } from '@/services/base/xiaoximokuaitongzhixinxijiekou';
import { history } from '@umijs/max';
import { List } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import React from 'react';
import styles from './index.less';

// 原函数类型
export type NoticeIconTabProps = {
  onClick?: (item: BASE.NotifyInfo) => void;
  emptyText?: string;
  list: BASE.NotifyInfo[];
};

// 原组件
const NoticeList: React.FC<NoticeIconTabProps> = ({
  list = [], // 传过来的数据列表
  onClick,
  emptyText,
}) => {
  // 如果无数据的话 则给空图片占位
  if (!list || list.length === 0) {
    return (
      <div className={styles.notFound}>
        <img
          src="https://gw.alipayobjects.com/zos/rmsportal/sAuJeJzSKbUmHfBQRzmZ.svg"
          alt="not found"
        />
        <div>{emptyText}</div>
      </div>
    );
  }

  // 列表
  return (
    <div>
      <List<BASE.NotifyInfo & { hasRead: boolean; dataType?: string }>
        className={styles.list}
        dataSource={list as any}
        renderItem={(item, i) => {
          const itemCls = classNames(styles.item, {
            [styles.read]: item,
          });
          return (
            <div
              onClick={() => {
                onClick?.(item);
              }}
            >
              <List.Item className={itemCls} key={item.id || i}>
                <List.Item.Meta
                  className={styles.meta}
                  title={
                    <div
                      className={styles.title}
                      onClick={async () => {
                        await notifyInfoRead({
                          id: String(item.id),
                        });
                        // history.push(item.buRoute!);
                      }}
                    >
                      <span
                        className={classNames(styles?.title_text, {
                          [styles?.title_text_gray]: !!item.state,
                        })}
                      >
                        {item?.notifyBody}
                      </span>
                    </div>
                  }
                  description={
                    <div>
                      <div className={styles.line}>
                        {/* <div className={styles.notifyBody}>{item?.notifyBody}</div> */}
                        <div className={styles.datetime}>
                          {dayjs(item?.createTime).format('YYYY-MM-DD HH:mm:ss')}
                        </div>
                      </div>
                    </div>
                  }
                />
              </List.Item>
              <div className={styles.hr}> </div>
            </div>
          );
        }}
      />
    </div>
  );
};

export default NoticeList;
