
import { noticeInfoPageByUser } from '@/services/base/msgNotice';
import { BellOutlined } from '@ant-design/icons';
import { useModel, useRequest } from '@umijs/max';
import { Avatar, Badge, Dropdown } from 'antd';
import { useState } from 'react';
import List from './compoents/list';
import styles from './index.less';
import * as Enums from '@/enums/msg';
import { notifyInfoGet, notifyInfoPage } from '@/services/base/xiaoximokuaitongzhixinxijiekou';

export default () => {
  const [visible, setVisible] = useState<boolean>(false);
  const { initialState } = useModel('@@initialState');
  const messageRequest = useRequest(
    () => {
      return notifyInfoPage({
        page: 1,
        size: 100,
        userId: initialState?.currentUser?.id,
        state: 0  // 未读状态
      });
    },
    {
      refreshDeps: [visible],
    },
  );

  return (
    <Dropdown
      placement="bottomLeft"
      trigger={['click']}
      open={visible}
      onOpenChange={(open) => {
        setVisible(open);
      }}
      dropdownRender={(menu) => (
        <div style={{ border: '1px solid #ccc' }}>
          <div className={styles.notice} style={{ background: '#eee' }}>
            <span>
              未读消息（
              {messageRequest?.data?.records?.length}）
            </span>
          </div>
          <List list={messageRequest?.data?.records || []} />
        </div>
      )}
    >
      <Badge
        count={messageRequest?.data?.records?.length}
        size="small"
        offset={[-5, 3]}
        color={'#f53f3f'}
      >
        <Avatar
          shape="circle"
          style={{ background: '#F2F6FE' }}
          icon={<BellOutlined style={{ color: '#3377FF', cursor: 'pointer' }} />}
        />
      </Badge>
    </Dropdown>
  );
};
