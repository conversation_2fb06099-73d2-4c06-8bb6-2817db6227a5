import { Link, useModel } from '@umijs/max';
import React from 'react';

import Avatar from './avatarDropdown';

import styles from './index.less';
import NoticeIcon from './noticeIcon';

const GlobalHeaderRight: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  if (!initialState || !initialState.settings) {
    return null;
  }

  const access = initialState?.permissions;
  return (
    <div className={styles['right-content']}>
      {/* <Notice /> */}
      {/* <Text
        type="primary"
        onClick={async () => {
          const res = await openGetNetworkAddress();
          if (res?.success && res?.data) {
            window.open(res.data);
          }
        }}
      >
        进网系统
      </Text> */}
      {
        access?.includes('admin:/bigScreen') && (
          <Link style={{color:'#fff'}} to="/bigScreen">领导驾驶舱</Link>
        )
      }
      {/* <NoticeIcon /> */}
      <Avatar menu />
    </div>
  );
};
export default GlobalHeaderRight;
