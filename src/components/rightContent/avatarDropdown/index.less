.action {
  display: flex;
  align-items: center;
  height: 48px;
  margin-left: auto;
  padding: 0 8px;
  overflow: hidden;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.spin {
  margin-right: 8px;
  margin-left: 8px;
}

.name {
  width: 70px;
  height: 48px;
  overflow: hidden;
  line-height: 48px;
  color: #fff;

  .text-overflow();

  @media @phone {
    display: none;
  }
}

:global(.ant-dropdown-menu.auto-dropdown-menu) {
  background-color: rgb(0, 101, 105);
}
:global(.ant-dropdown-menu.auto-dropdown-menu .ant-dropdown-menu-item) {
  color: #fff;
}

.avatar {
  margin-right: 8px;
  color: @primary;
  vertical-align: top;
  background-color: #ddd;
  img{
    object-fit: contain;
  }

  @media @phone {
    margin-right: 0;
  }
}
