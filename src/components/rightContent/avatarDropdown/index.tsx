import { parseSrc } from '@/utils';
import { LockOutlined, LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Avatar, Dropdown, message, Modal, Spin } from 'antd';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useCallback } from 'react';
import { flushSync } from 'react-dom';
import styles from './index.less';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const Name = () => {
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;

  return <span className={styles['name']}>{currentUser?.fullName ?? currentUser?.username}</span>;
};

const AvatarLogo = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const token = localStorage.getItem('token');

  return (
    <Avatar
      size="small"
      src={
        (currentUser?.extendData?.photoUrl &&
          parseSrc(
            `/test/templateInfo/downTemplate?filePathfileName=${currentUser?.extendData?.photoUrl}&token=${token}`,
          )) ||
        require('@/pages/home/<USER>/avatar.png')
      }
      className={styles['avatar']}
      alt="avatar"
    />
  );
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const { initialState, setInitialState } = useModel('@@initialState');

  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    await initialState!.logout!();
  };

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event;

      if (key === 'logout') {
        const m = Modal.confirm({
          title: '是否退出当前系统?',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            m.update({
              cancelButtonProps: {
                disabled: true,
              },
            });

            flushSync(() => {
              setInitialState((s) => ({ ...s, currentUser: null } as typeof initialState));
            });

            await loginOut();

            m.update({
              cancelButtonProps: {
                disabled: false,
              },
            });

            message.success('退出系统成功');
          },
        });

        return;
      }

      history.push(`/i/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={styles['action']}>
      <Spin className={styles['spin']} size="small" />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.fullName) {
    return loading;
  }

  const menuItems = [
    {
      key: 'center',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      key: 'password',
      icon: <LockOutlined />,
      label: '修改密码',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  return (
    <Dropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
        className: 'auto-dropdown-menu',
      }}
      trigger={['hover']}
    >
      <span className={styles['action']}>
        <AvatarLogo />
        <Name />
      </span>
    </Dropdown>
  );
};

export default AvatarDropdown;
