import {
  userTableColumnInfoAdd,
  userTableColumnInfoUpdate,
  userTableColumnInfoVoPage,
} from '@/services/base/zidingyizhanshiliebiaojiekou';
import { ColumnsState } from '@ant-design/pro-components';
import { SettingOptionType } from '@ant-design/pro-table/es/components/ToolBar';
import { request, useAccess, useLocation, useModel, useRequest } from '@umijs/max';
import { ConfigProvider, message } from 'antd';
import {
  MyColumns,
  ProTable as ProTableCRUD,
  ProTableProps as ProTableCRUDProps,
} from 'antd-pro-crud';
import { useState } from 'react';

export type ProTableProps<
  T = Record<string, any>,
  U = Record<string, any>,
  ValueType = 'text',
> = Partial<Omit<ProTableCRUDProps<T, U, ValueType | GlobalValueType>, 'location' | 'umiRequest'>> & {
  /** 是否需要自定义显示列 */
  needSelfColumns?:boolean;
  childrenKey?: string;
};

const ProTable = function <T = Record<string, any>, U = Record<string, any>, ValueType = 'text'>(
  props: ProTableProps<T, U, ValueType>,
) {
  const access = useAccess();
  const location = useLocation();
  const { columns,needSelfColumns = true, childrenKey, ...rest } = props;
  const [selectRowId, setSelectRowId] = useState<T | undefined>(); // 选中的行
  const [selfColumns, setSelfColumns] = useState<Record<string, ColumnsState>>(); // 配置显示的列
  const { initialState } = useModel('@@initialState');

  const getSelfColumns = useRequest(
    () => {
      return userTableColumnInfoVoPage({
        keyName: childrenKey ? location.pathname + '/' +  childrenKey : location.pathname,
        page: 1,
        size: 1,
        userId: initialState?.currentUser?.id,
      });
    },
    {
      manual:!needSelfColumns,
      onSuccess: (data) => {
        if (!!data?.records?.length) {
          setSelfColumns(JSON.parse(data?.records?.[0]?.columnsInfo || '{}'));
        }
      },
    },
  );

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            rowHoverBg: '#C2E7FF',
            rowSelectedBg: '#C2E7FF',
            rowSelectedHoverBg: '#C2E7FF',
          },
        },
      }}
    >
      <ProTableCRUD<T, U, ValueType>
        defaultSize="small"
        pagination={{
          showSizeChanger: true,
          defaultPageSize: 15,
        }}
    
        search={{
          span: 6,
          ...rest?.search,
        }}

        columnsState={{
          value: selfColumns,
          onChange: (opt) => {
            setSelfColumns(opt);
            rest?.columnsState?.onChange?.(opt);
          },
        }}
        options={
          rest?.options !== false && {
            ...rest?.options,
            setting: rest?.options?.setting !== false && {
              ...(rest?.options?.setting as SettingOptionType),
              extra: needSelfColumns && (
                <a
                  onClick={async () => {
                    const submitValues = {
                      userId: initialState?.currentUser?.id,
                      keyName: childrenKey ? location.pathname + '/' +  childrenKey : location.pathname,
                      columnsInfo: JSON.stringify(selfColumns),
                    };
                    const res = !!getSelfColumns?.data?.records?.length
                      ? await userTableColumnInfoUpdate({
                          ...submitValues,
                          id: getSelfColumns?.data?.records?.[0]?.id,
                        })
                      : await userTableColumnInfoAdd(submitValues as BASE.UserTableColumnInfo);
                    if (res?.success) {
                      message.success('保存配置成功');
                    }
                  }}
                >
                  保存
                </a>
              ),
            },
          }
        }
        onRow={(record) => {
          return {
            onClick: (event) => {
              setSelectRowId(record?.id);
            },
          };
        }}
        rowClassName={(record) => {
          if (record?.id === selectRowId) {
            return 'self-row-selected';
          }
          return '';
        }}
        scroll={{ x: 'max-content' }}
        access={access?.commonAccess}
        style={{
          minHeight: '480px',
        }}
        onLoadingChange={(loading) => {
          // 触发layout重新渲染
          if (!loading) {
            setTimeout(() => {
              let height = document.body.offsetHeight;
              console.log('height', height);
              window.dispatchEvent(new Event('resize'));
              // 强制浏览器渲染
              requestAnimationFrame(() => {
                console.log('requestAnimationFrame');
              });
          }, 100);
          }
        }}
        {...(rest as any)}
        umiRequest={request}
        autoMock={false}
        columns={columns as MyColumns<T, ValueType>[]}
      />
    </ConfigProvider>
  );
};

export default ProTable;
