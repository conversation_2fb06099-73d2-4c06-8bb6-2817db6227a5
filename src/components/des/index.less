.des-item {
  position: relative;
  display: flex;
  align-items: center;
  line-height: 1.2;
  // border-bottom: 1px dashed transparent;

  .clearfix();

  &-label {
    flex-shrink: 0;
    // float: left;
    width: 80px;
    margin-right: 10px;
    // text-align: right;

    &::after {
      content: ':';
    }
  }

  &-value {
    flex-grow: 1;
    min-width: 0;
    // display: block;
    // margin-left: 80px;
    // padding-left: 10px;
  }

  &:hover {
    // color: @danger;
    // border-bottom-color: @danger;
  }
}

.des-item + .des-item {
  margin-top: 4px;
}

.json-string {
  .icon {
    display: inline-block;
    vertical-align: 1px;
  }

  .text {
    display: inline-block;
    width: 150px;
    color: #c7254e;
    background-color: #fff2f4;
    cursor: pointer;

    .text-overflow();
  }
}
