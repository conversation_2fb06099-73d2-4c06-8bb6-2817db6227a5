import { CopyrightCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import styles from './index.less';

const Footer: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  return (
    <div className={styles['footer']}>
      {/* <div className={styles['copryright']}>
        copyright <CopyrightCircleOutlined className={styles['icon']} />
        {initialState?.config?.['copyright']?.configValue ?? APP_COPYRIGHT}
      </div> */}
    </div>
  );
};
export default Footer;
