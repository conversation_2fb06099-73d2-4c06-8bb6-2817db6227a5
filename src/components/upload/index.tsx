import { fileUpload } from '@/services/base/extFile';
import { downloadFile, replaceMultiText } from '@/utils';
import { LoadingOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import {
  Button,
  message,
  Upload as AntUpload,
  UploadFile,
  UploadProps as AntUploadProps,
  Tooltip,
} from 'antd';
import { RcFile } from 'antd/lib/upload';
import dayjs from 'dayjs';
import useMergedState from 'rc-util/lib/hooks/useMergedState';
import React, { forwardRef, useImperativeHandle } from 'react';
import styles from './index.less';
/** 文件对象 */
type BizData = BASE.IResultFileInfo;

// 图片文件类型
const imgFileType = ['gif', 'jpeg', 'jpg', 'png'];
const imgAccept = imgFileType.map((v) => `image/${v}`).join(',');
// 默认文件大小限制
const defaultMaxFileSize = 150 * 1024 * 1024;

export type BizUploadFile = UploadFile<BizData>;
export const LIST_IGNORE = AntUpload.LIST_IGNORE;

export type UploadProps<T extends BizData = BizData> = Omit<AntUploadProps<T>, 'onChange'> & {
  /** 是否是拖拽上传 */
  draggable?: boolean;
  /** 是否只图片上传 */
  onlyImg?: boolean;
  /** 文件大小限制 */
  maxFileSize?: number;

  value?: (UploadFile<T> | string)[];
  onChange?: (fileList: UploadFile<T>[]) => void;
  onSuccess?: (res: ThenReturn<ReturnType<typeof fileUpload>>) => void;
  children?: React.ReactNode;
};

export const transSysFile2BizFile = <T extends BizData = BizData>(file: BASE.FileInfo): UploadFile<T> => {
  return {
    uid: file.id,
    name: file.fileName,
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    url: getPrevUrl(file.fileUrl!),
    status: 'done',
    response: { data: file } as unknown as T,
    originFileObj: file as unknown as RcFile,
  } as unknown as UploadFile<T>;
};

/** 获取文件名 */
function getFileName(name: string): string {
  return name.split('/').pop() || name;
}

/** 获取图片预览地址 */
export function getPrevUrl(url: string): string | undefined {
  if (!url) return;
  if (url.startsWith('http')) {
    return url;
  }

  return replaceMultiText(`${PROXY_KEY}${url}`, '/');
}

export const formatUploadFile = <T extends BizData = BizData>(
  fileList: UploadProps<T>['value'],
): UploadFile<T>[] => {
  if (!fileList) {
    return [];
  }

  if (Array.isArray(fileList)) {
    return fileList.map((file) => {
      if (typeof file === 'string') {
        return {
          uid: file,
          name: getFileName(file),
          url: getPrevUrl(file),
          status: 'done',
          response: {
            data: {
              fileUrl: file,
            },
          } as unknown as T,
        } as unknown as UploadFile<T>;
      }
      if (!file?.originFileObj) {
        return transSysFile2BizFile(file as any);
      }

      return file as UploadFile<T>;
    });
  }

  return [transSysFile2BizFile(fileList as any)];
};

export type UploadRef<T extends BizData = BizData> = {
  /** 清空文件列表 */
  clear: () => void;
};

const Upload = <T extends BizData = BizData>(
  {
    value: valueProps,
    draggable: draggableProps,
    onlyImg: onlyImgProps,
    defaultFileList: defaultFileListProps,
    maxFileSize: maxFileSizeProps = defaultMaxFileSize,
    fileList: fileListProps,
    onChange: onChangeProps,
    beforeUpload: beforeUploadProps,
    onSuccess: onSuccessProps,
    maxCount: maxCountProps,
    children,
    ...rest
  }: UploadProps<T>,
  ref: React.Ref<UploadRef<T>>,
) => {
  // q: 这里用了 useMergedState，是不是我不需要显示调用props.onChange了？
  // a: useMergedState 会自动调用 props.onChange

  /**
   * defaultFileList: 默认文件列表
   * value: 上传文件列表
   * onChange: 上传文件列表变化回调
   */
  const [internalValue, setInternalValue] = useMergedState<UploadFile<T>[]>(
    defaultFileListProps || [],
    {
      value: formatUploadFile(valueProps),
      onChange: onChangeProps,
    },
  );

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => {
    return {
      clear: () => {
        setInternalValue([]);
      },
    };
  });

  // 上传前操作
  const beforeUpload: AntUploadProps<T>['beforeUpload'] = async (file, fileList) => {
    const suffix = file.name.split('.').pop();

    if (maxFileSizeProps > 0 && file.size > maxFileSizeProps) {
      message.error(`上传文件大小不能超过: ${maxFileSizeProps / 1024 / 1024}MB`);
      return LIST_IGNORE;
    }

    if (onlyImgProps) {
      if (!imgFileType.includes(suffix!.toLowerCase())) {
        message.info(`只能上传${imgFileType.join('/')}格式的文件`);
        return LIST_IGNORE;
      }
    }

    return beforeUploadProps?.(file, fileList);
  };

  // 自定义上传
  const customRequest: AntUploadProps<T>['customRequest'] = async (options) => {
    const file = options.file as RcFile;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', file.name);
    formData.append('filePurpose', '1');
    formData.append('showState', '1');

    try {
      const res = await fileUpload(
        {
          showState: 3
        } as ArgumentsType<typeof fileUpload>[0],
        {},
        {
          method: 'POST',
          data: formData,
          requestType: 'form',
          timeout: 60000,
          skipErrorHandler: true,
        },
      );

      // 调用成功回调
      onSuccessProps?.(res);
      options.onSuccess?.(res as any);

      // 类型兼容处理
      const responseData = { ...res } as unknown as T;

      // 构建上传文件对象
      const newFile: UploadFile<T> = {
        ...file,
        status: 'done',
        response: responseData,
        url: getPrevUrl(res?.data?.fileUrl || ''),
      } as unknown as UploadFile<T>;

      // 更新文件列表
      const newFileList: UploadFile<T>[] = internalValue.map(v => {
        if (v.uid === file.uid) {
          return {
            ...v,
            status: 'done',
            response: responseData,
            url: getPrevUrl(res?.data?.fileUrl || ''),
          } as unknown as UploadFile<T>;
        }
        return v;
      });

      // 触发onChange事件
      if (onChange) {
        onChange({
          file: newFile,
          fileList: newFileList,
        });
      }
    } catch (e) {
      options.onError?.(e as Error);
      message.error({ content: `${file.name}上传失败！` });
    }
  };

  // 上传
  const onChange: AntUploadProps<T>['onChange'] = (info) => {
    // 处理文件列表
    const newFileList = info.fileList.map((file) => ({
      ...file,
      url: file.url || getPrevUrl((file.response as any)?.data?.fileUrl || ''),
    })) as unknown as UploadFile<T>[];

    // 更新内部状态
    setInternalValue(newFileList);
  };

  const props: Partial<AntUploadProps<T>> = {
    fileList: internalValue,
    onChange,
    customRequest,
    beforeUpload,
    itemRender: (originNode, file, fileList, actions) => {
      return onlyImgProps ? originNode : (
        <div className={styles.file_item}>
          <div className={styles.file_item_name}>
            {originNode}
          </div>

          <span className={styles.file_item_time}>
            {((file?.response as any)?.data?.createTime &&
              dayjs((file?.response as any)?.data?.createTime).format('YYYY-MM-DD HH:mm:ss')) ||
              (file?.originFileObj?.lastModified &&
                dayjs(file?.originFileObj?.lastModified).format('YYYY-MM-DD HH:mm:ss')) ||
              '--'}
          </span>
        </div>
      );
    },
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
    },
  };

  // 简单图片上传需求
  if (onlyImgProps) {
    const file = internalValue[0];
    const loading = file?.status === 'uploading';

    props.listType = 'picture-card';
    props.accept = imgAccept;

    props.children = ((): React.ReactNode => {
      const uploadButton = (
        <div>
          {loading ? <LoadingOutlined /> : <PlusOutlined />}
          <div style={{ marginTop: 8 }}>上传</div>
        </div>
      );

      if (!file) {
        return uploadButton;
      }
      if (internalValue?.length === maxCountProps) {
        return null;
      }

      return uploadButton;
    })();
  }

  if (draggableProps) {
    return <AntUpload.Dragger {...props} {...rest} />;
  }

  return (
    <AntUpload<T> maxCount={maxCountProps} {...props} {...rest} onPreview={(file) => {
      downloadFile(file?.url)
    }}>
      {
        <Tooltip title="严禁传输、存储国家秘密、内部文件、敏感信息、工作秘密！"
          overlayInnerStyle={{
            backgroundColor: '#fff',
            color: 'red'
          }}
          overlayStyle={{
            backgroundColor: '#fff',
          }}
        >
          {
            children || <Button icon={<UploadOutlined />}>上传</Button>
          }
        </Tooltip>
      }
    </AntUpload>
  );
};

export default forwardRef(Upload) as <T extends BizData = BizData>(
  props: UploadProps<T> & {
    ref?: React.Ref<UploadRef<T>>;
  },
) => React.ReactElement;
