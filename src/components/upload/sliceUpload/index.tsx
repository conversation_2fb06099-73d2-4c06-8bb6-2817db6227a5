﻿import { fileSliceConfirm, fileSliceUpload } from '@/services/base/extFile';

import { downloadFile } from '@/utils';

import UploadOutlined from '@ant-design/icons/lib/icons/UploadOutlined';

import Upload, { type UploadProps as AntdUploadProps } from 'antd/es/upload/Upload';

import { Button, ButtonProps, Modal, Tooltip, UploadFile } from 'antd/lib';

import useMergedState from 'rc-util/lib/hooks/useMergedState';

import React, { PropsWithChildren, useMemo, useState } from 'react';

import SparkMd5 from 'spark-md5';

import { getPrevUrl } from '..';

type FileStatus = 'none' | 'slicing' | 'uploading' | 'done';

type SliceUploadProps = PropsWithChildren<{
  value?: (string | UploadFile)[];

  onChange?: (e: SliceUploadFileList[]) => void;

  /** 上传成功回调 */

  onSuccess?: (e: SliceUploadFileList, eList: SliceUploadFileList[]) => void;

  /** Upload组件Props */

  uploadProps?: AntdUploadProps;

  /** 默认上传按钮 props */

  defaultUploadButtonProps?: ButtonProps;

  isDowmload?: boolean;

  /** 展示上传进度Modal */

  showProgressModal?: boolean;
}>;

interface CutFileObj {
  start: number;

  end: number;

  index: number;

  hash: string;
}

/** 切片上传返回对象 */

type SendRequestInTurnObj = BASE.fileSliceConfirmParams;

/** 切片完成确认接口返回体 自定义 */

interface SliceConfirmObj {
  fileName: string;

  fileCode: string;

  filePath: string;

  filePostfix: string;

  filePurpose: number;

  fileType: number;

  fileUrl: string;

  fullName: string;

  userId: string;

  id: number;
}

export type SliceUploadFileList = {
  uid: string;

  name: string;

  status: string;

  response: SliceConfirmObj;

  url: string;
};

/** 切片大小 MB */

export const CHUNKSIZE = 1024 * 1024 * 5;

/** 线程数量 */

export const TREADCOUNT = 5;

/** 全段Hash */

let mainHash = '';

const transformFile2UploadFile = (file: Record<string, any>): SliceUploadFileList => {
  if (!file?.uid || !file?.url) {
    return {
      uid: file?.fileCode || '',

      name: file?.fileName || '',

      status: 'done',

      response: file as unknown as SliceConfirmObj,

      url: getPrevUrl(file?.fileUrl || '') || '',
    };
  } else {
    return file as SliceUploadFileList;
  }
};

function formatUploadFile(fileList: SliceUploadFileList[]) {
  if (!fileList) {
    return [];
  }

  if (Array.isArray(fileList)) {
    return fileList.map((i) => transformFile2UploadFile(i));
  }

  return [transformFile2UploadFile(fileList)];
}

/** 切片上传 */

const SliceUpload: React.FC<SliceUploadProps> = ({
  children,

  uploadProps = {},

  defaultUploadButtonProps = {},

  value: valueProps,

  showProgressModal = true,

  isDowmload = true,

  onChange: onChangeProps,

  onSuccess,
}) => {
  const [type, setType] = useState<FileStatus>('none');

  // 提示

  const [open, setOpen] = useState(false);

  // 进度 0 ~ 100

  const [progress, setProgress] = useState(0);

  // 是否多上传

  const multiple = useMemo(() => !!uploadProps?.multiple, [uploadProps.multiple]);

  // 上传文件的个数

  const [fileCount, setFileCount] = useState(0);

  const [internalValue, setInternalValue] = useMergedState<SliceUploadFileList[]>(
    (valueProps as unknown as SliceUploadFileList[]) || [],

    {
      value: formatUploadFile(valueProps as unknown as SliceUploadFileList[]),

      onChange: onChangeProps,
    },
  );

  /** 剪文件 */

  function cutFile(file: File, chunkSize: number, index: number) {
    return new Promise<CutFileObj>((resolve) => {
      const start = index * chunkSize;

      const end = start + chunkSize;

      const spark = new SparkMd5.ArrayBuffer();

      const fileReader = new FileReader();

      fileReader.onload = (e) => {
        spark.append(e.target?.result as ArrayBuffer);

        if (!mainHash) {
          mainHash = spark.end();
        }

        resolve({
          start,

          end,

          index,

          hash: file.uid,
        });
      };

      fileReader.readAsArrayBuffer(file.slice(start, end));
    });
  }

  /** 直接使用脚本处理文件 */

  async function sliceFile(file: File) {
    const chunkCount = Math.ceil(file.size / CHUNKSIZE);

    const result = [];

    for (let i = 0; i < chunkCount; i++) {
      const chunk = await cutFile(file, CHUNKSIZE, i);

      setProgress(Number(Math.ceil(((i + 1) / chunkCount) * 100).toFixed(2)));

      result.push(chunk);
    }

    return result;
  }

  /** 依次发送请求 */

  function sendRequestInTurn(reqList: CutFileObj[], count: number, fileInfo: File) {
    return new Promise<SendRequestInTurnObj | undefined>((resolve) => {
      if (!reqList || !reqList.length || !count) {
        return;
      }

      let max = count;

      const totalCount = reqList.length;

      let currentIndex = 0;

      let counter = 0;

      // const result: BASE.IResultString[] = [];

      // 开始函数

      const begin = async () => {
        while (currentIndex < totalCount && max > 0) {
          max--;

          const { start, end, hash } = reqList[currentIndex];

          currentIndex++;

          const file = new File([fileInfo.slice(start, end)], fileInfo.name || '');

          const formdata = new FormData();

          formdata.append('file', file);

          formdata.append('seq', '' + currentIndex);

          formdata.append('id', hash);

          const r = await fileSliceUpload({} as ArgumentsType<typeof fileSliceUpload>[0], {
            method: 'POST',

            data: formdata,

            requestType: 'form',

            timeout: 60000,

            skipErrorHandler: true,
          });

          // result.push(r);

          max++;

          counter++;

          setProgress(Number(Math.ceil(((counter + 1) / totalCount) * 100).toFixed(2)));

          if (counter === totalCount) {
            resolve({
              id: hash,

              name: fileInfo.name,

              byteLength: fileInfo.size as unknown as string,

              sliceLength: totalCount as unknown as string,

              filePurpose: '1',
            } as unknown as SendRequestInTurnObj);
          } else {
            begin();
          }
        }
      };

      begin();
    });
  }

  const onRemove: AntdUploadProps['onRemove'] = (e) => {
    if (multiple) {
      setInternalValue((v) => {
        return internalValue.filter((i) => i.uid !== (e as unknown as SliceUploadFileList).uid);
      });
    } else {
      setInternalValue([]);
    }

    return true;
  };

  return (
    <>
      <Upload
        maxCount={1}
        onRemove={onRemove}
        {...uploadProps}
        onPreview={async (file) => {
          if (isDowmload) {
            downloadFile(file?.url);
          }
        }}
        beforeUpload={(file, fileList) => {
          setFileCount(fileList?.length || 0);
        }}
        fileList={internalValue as unknown as UploadFile[]}
        customRequest={async (info) => {
          if (showProgressModal) {
            setOpen(true);
          }

          if (info) {
            setType('slicing');
          }

          // 置空

          mainHash = '';

          const r = await sliceFile(info.file as unknown as File);

          setType('uploading');

          const result = await sendRequestInTurn(r, 3, info.file as File);

          if (!result) {
            return;
          }

          // 完成标识确认

          const res = (await fileSliceConfirm({
            ...result,
          })) as unknown as ServerResponse<SliceConfirmObj>;

          setType('done');

          if (showProgressModal) {
            setOpen(false);
          }

          setInternalValue((v) => {
            // 将文件返回给调用组件
            const newValue = v && v?.length > 0 ? [...v] : [...internalValue];
            onSuccess?.(
              transformFile2UploadFile(res.data as unknown as SliceConfirmObj),

              newValue,
            );

            const value = multiple
              ? [...newValue, transformFile2UploadFile(res.data as unknown as SliceConfirmObj)]
              : [transformFile2UploadFile(res.data as unknown as SliceConfirmObj)];

            return value;
          });
        }}
      >
        {
          <Tooltip title="严禁传输、存储国家秘密、内部文件、敏感信息、工作秘密！"
          overlayInnerStyle={{
            backgroundColor: '#fff',
            color: 'red'
          }}
          overlayStyle={{
            backgroundColor: '#fff',
          }}>
          {!children ? (
          <Button
            icon={<UploadOutlined />}
            {...defaultUploadButtonProps}
            loading={type === 'slicing' || type === 'uploading'}
          >
            {type === 'none'
              ? '上传'
              : type === 'slicing'
              ? '切片中'
              : type === 'uploading'
              ? '上传中'
              : '上传'}
          </Button>
        ) : (
          children
        )}
        
          </Tooltip>

        }
        
      </Upload>

      {showProgressModal && (
        <Modal
          open={open}
          onCancel={() => {
            setOpen(false);
          }}
          centered
          closable={false}
          zIndex={10000}
          keyboard={false}
          footer={null}
        >
          <div style={{ padding: '10px' }}>
            <h3>
              {type === 'slicing' ? '切片中...' : type === 'uploading' ? '上传中...' : '上传完成'}
            </h3>

            <progress style={{ width: '100%' }} max={100} value={progress} />
          </div>
        </Modal>
      )}
    </>
  );
};

export default SliceUpload;
