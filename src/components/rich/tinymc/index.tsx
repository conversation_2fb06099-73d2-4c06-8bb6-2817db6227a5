import { fileUpload } from '@/services/base/extFile';
import { parseSrc } from '@/utils';
import { Editor, IAllProps } from '@tinymce/tinymce-react';
import { message } from 'antd';
import React, { useRef } from 'react';
import type { Editor as TinyMCEEditor } from 'tinymce';

export type TinymcProps = IAllProps & {};

const Tinymc: React.FC<TinymcProps> = ({
  onChange: onChangeProps,
  onInit: onInitProps,
  init: initProps,
  ...rest
}) => {
  const instance = useRef<TinyMCEEditor>();

  return (
    <Editor
      // 传递onChange
      onEditorChange={(e, editor) => {
        onChangeProps?.(
          e as unknown as ArgumentsType<NonUndefined<TinymcProps['onChange']>>['0'],
          editor,
        );
      }}
      tinymceScriptSrc={`${process.env.PUBLIC_PATH}lib/tinymce/tinymce.min.js`}
      onInit={(evt, editor) => {
        instance.current = editor;

        onInitProps?.(evt, editor);
      }}
      init={{
        language: 'zh-Hans',
        height: 500, //编辑器高度
        min_height: 300,
        // 去除logo
        branding: false,
        // 出去升级
        promotion: false,
        upgrade: false,
        paste_data_images: false,
        file_picker_types: 'file image media',
        // 自定义图片上传
        images_upload_handler(blobInfo) {
          const formData = new FormData();
          formData.append('file', blobInfo.blob());
          formData.append('name', blobInfo.filename());
          formData.append('filePurpose', '1');
          formData.append('showState', '1');

          // message.loading({ content: `${file.name}上传中...`, key: file.name });

          return fileUpload(
            {
              showState:3
            } as ArgumentsType<typeof fileUpload>[0],
            {},
            {
              method: 'POST',
              data: formData,
              requestType: 'form',
              timeout: 60000,
              skipErrorHandler: true,
            },
          ).then((res) => {
            if (res.data && res.data.fileUrl) {
              return parseSrc(res.data.fileUrl) as string;
            }

            return Promise.reject(res.message || '上传失败！');
          });
        },
        // 自定义文件上传
        file_picker_callback(callback, value, meta) {
          const ipt = document.createElement('input');
          ipt.setAttribute('type', 'file');

          if (meta.filetype === 'image') {
            ipt.setAttribute(
              'accept',
              'image/apng, image/avif, image/gif, image/jpeg, image/png, image/svg+xml, image/webp',
            );
          } else if (meta.filetype === 'media') {
            ipt.setAttribute('accept', 'video/*, audio/*');
          }

          ipt.onchange = (e) => {
            const [file] = Array.from((e.target as HTMLInputElement).files || []);

            if (!file) {
              return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('name', file.name);
            formData.append('filePurpose', '1');
            formData.append('showState', '1');

            message.loading({ content: `${file.name}上传中...`, key: file.name });

            fileUpload(
              {} as ArgumentsType<typeof fileUpload>[0],
              {},
              {
                method: 'POST',
                data: formData,
                requestType: 'form',
                timeout: 60000,
                skipErrorHandler: true,
              },
            )
              .then((res) => {
                if (res.data && res.data.fileUrl) {
                  const src = parseSrc(res.data.fileUrl) as string;

                  if (meta.filetype === 'file') {
                    callback(src, { text: file.name });
                  } else if (meta.filetype === 'image') {
                    callback(src, { alt: file.name });
                  } else if (meta.filetype === 'media') {
                    callback(src, {});
                  }
                } else {
                  message.error(res.message || '上传失败！');
                }
              })
              .finally(() => {
                message.destroy(file.name);
                ipt.remove();
              });
          };

          ipt.click();
        },
        powerpaste_word_import: 'propmt', // 参数可以是propmt, merge, clear，效果自行切换对比
        powerpaste_html_import: 'propmt', // propmt, merge, clear
        powerpaste_allow_local_images: true,
        // end 自定义文件上传
        plugins: [
          'powerpaste',
          'advlist',
          'autolink',
          'lists',
          'link',
          'image',
          'charmap',
          'anchor',
          'searchreplace',
          'visualblocks',
          'code',
          'fullscreen',
          'insertdatetime',
          'media',
          'table',
          'preview',
          'help',
          'wordcount',
        ],
        toolbar: [
          `fullscreen | preview | undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright | removeformat`,
          `image | link | media | alignright alignjustify | bullist numlist outdent indent`,
        ],
        content_style: `
            body { font-family:Helvetica,Arial,sans-serif; font-size:14px };
            img {max-width:100%;}
          `,
        ...initProps,
      }}
      {...rest}
    />
  );
};

export default Tinymc;
