import '@wangeditor/editor/dist/css/style.css'; // 引入 css

import type { IDomEditor, IEditorConfig } from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import { message } from 'antd';
import { useEffect, useState } from 'react';
import { request } from 'umi';

function Neditor({
  value,
  width,
  onChange,
  height,
}: {
  value?: any;
  width?: number | string;
  height?: number;
  onChange?: (v: string) => void;
}) {
  const [editor, setEditor] = useState<IDomEditor | null>(null); // 存储 editor 实例

  const toolbarConfig = {};
  const editorConfig: Partial<IEditorConfig> = {
    placeholder: '请输入内容...',
    MENU_CONF: {
      uploadImage: {
        // 自定义上传
        async customUpload(file: File, insertFn: (url: string, alt: string, href: string) => void) {
          const maxFileSize = 50; //单文件最大容量
          const fileSize = maxFileSize * 1024 * 1024;
          if (file.size > fileSize) {
            message.error(`上传文件大小不能超过: ${maxFileSize}MB`);
            return;
          }

          const formData = new FormData();
          formData.append('file', file);
          formData.append('filePurpose', '1');
          formData.append('showState', '1');
          message.loading({
            content: `${file.name}文件上传中···`,
            key: 'upload',
          });
          request<Record<string, any>>('/file/upload', {
            method: 'POST',
            data: formData,
            timeout: 60000,
          })
            .then((res) => {
              if (!res || !res?.success) {
                message.error({ content: `${file.name}上传失败！`, key: 'upload' });
                return;
              }
              message.success({
                content: `${file.name}文件上传成功`,
                key: 'upload',
              });
              insertFn(`/api${res?.data?.fileUrl}`, '', `/api${res?.data?.fileUrl}`);
              // onChange?.(res?.data?.fileUrl);
            })
            .catch(() => {
              message.error({ content: `${file.name}上传失败！`, key: 'upload' });
            });

          // file 即选中的文件
          // 自己实现上传，并得到图片 url alt href
          // 最后插入图片
          // insertFn(url, alt, href);
        },
      },
    },
  };

  // 及时销毁 editor ，重要！
  useEffect(() => {
    return () => {
      if (editor === null) return;
      editor.destroy();
      setEditor(null);
    };
  }, [editor]);

  return (
    <>
      <div
        style={{ border: '1px solid #ccc', borderRadius: '6px', zIndex: 100, paddingTop: '3px' }}
      >
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #ccc', boderRadius: '6px' }}
        />
        <Editor
          defaultConfig={editorConfig}
          value={value}
          onCreated={setEditor}
          onChange={(editor) => {
            // setHtml(editor.getHtml());
            onChange?.(editor.getHtml());
          }}
          mode="default"
          style={{ height: height || 400, width: width, overflowY: 'hidden', padding: '10px' }}
        />
      </div>
    </>
  );
}

export default Neditor;
