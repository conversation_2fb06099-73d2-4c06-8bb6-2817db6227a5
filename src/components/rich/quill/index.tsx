import { fileUpload } from '@/services/base/extFile';
import { parseSrc } from '@/utils';
import { message } from 'antd';
import cs from 'classnames';
import Q from 'quill';
import { forwardRef, useImperativeHandle, useRef } from 'react';
import ReactQuill, { ReactQuillProps } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import styles from './index.less';

// 图片上传
Q.register('modules/imageUploader', require('quill-image-uploader').default);
Q.register(Q.import('attributors/style/align'), true);
Q.register(Q.import('attributors/style/background'), true);
Q.register(Q.import('attributors/style/color'), true);
Q.register(Q.import('attributors/style/direction'), true);
Q.register(Q.import('attributors/style/font'), true);

export type QuillProps = ReactQuillProps & {};

export type QuillRef = ReactQuill;

const Quill: React.FC<QuillProps> = forwardRef<QuillRef, QuillProps>(
  ({ modules: modulesProps, formats: formatsProps, className: classNameProps, ...rest }, ref) => {
    const instance = useRef<ReactQuill>(null);
    // const [value,setValue] = useState(propsValue)

    useImperativeHandle(ref, () => instance.current!);

    const modules: QuillProps['modules'] = Object.assign(
      {},
      {
        toolbar: [
          [{ header: [1, 2, false] }],
          ['bold', 'italic', 'underline', 'strike', 'blockquote'],
          [{ list: 'ordered' }, { list: 'bullet' }],
          ['link', 'image'],
        ],
        // 图片上传
        imageUploader: {
          async upload(file: File) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('name', file.name);
            formData.append('filePurpose', '1');
            formData.append('showState', '1');

            message.loading({ content: `${file.name}上传中...`, key: file.name });

            return fileUpload(
              {
                showState:3
              } as ArgumentsType<typeof fileUpload>[0],
              {},
              {
                method: 'POST',
                data: formData,
                requestType: 'form',
                timeout: 60000,
                skipErrorHandler: true,
              },
            )
              .then((res) => {
                message.success({ content: `${file.name}上传成功！`, key: file.name });

                if (res.data && res.data.fileUrl) {
                  return parseSrc(res.data.fileUrl);
                }

                return Promise.reject('上传失败！');
              })
              .catch(() => {
                message.error({ content: `${file.name}上传失败！` });
              })
              .finally(() => {
                message.destroy(file.name);
              });
          },
        },
        // end 图片上传
        // 图片尺寸修改
        // imageResize: {},
        // end 图片尺寸修改
      },
      modulesProps,
    );
    const formats: QuillProps['formats'] = (
      [
        'header',
        'bold',
        'italic',
        'underline',
        'strike',
        'blockquote',
        'list',
        'bullet',
        'indent',
        'link',
        'image',
      ] as NonUndefined<QuillProps['formats']>
    ).concat(formatsProps || []);

    return (
      <ReactQuill
        className={cs(styles['quill'], classNameProps)}
        modules={modules}
        formats={formats}
        theme="snow"
        ref={instance}
        // onChange={(value, delta, source, editor)=>{
        //   setValue(value)
        //   instance?.current?.editor?.setContents(editor?.getContents())
        // }}
        // value={value}
        placeholder="请输入..."
        {...rest}
      />
    );
  },
);

export default Quill;
