import { Col, TabPaneProps, Tabs as AntdTabs, TabsProps } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

type TargetKey = React.MouseEvent | React.KeyboardEvent | string;

interface Tab extends Omit<TabPaneProps, 'tab'> {
  key: string;
  label: React.ReactNode;
  data?: Record<string, any>;
}

export type MyTabsProps = {
  // 每个tab的from表单项
  renderTabForm: (index: number, data?: Record<string, any>) => React.ReactNode;
  // 初始化时的数据
  initValues?: Record<string, any>[];
  // tab每一项标题的label，如tabLabelKey = 项目信息，则tab的标题为 项目信息1，项目信息2
  tabLabelKey?: React.ReactNode;
  // 修正tab的items的一些参数
  formatItems?: (items: Tab[]) => Tab[];
  // 新增tab时的回调
  afterAddTab?: (key: number | string) => void;
  // 删除tab时的回调
  afterRemoveTab?: (key: number | string) => void;
} & TabsProps;

export type MyTabsRef =
  | {
      add: (data?: Record<string, any>) => void;
      remove: (targetKey: TargetKey) => void;
    }
  | undefined;

const MyTabs = forwardRef<MyTabsRef, MyTabsProps>((props, ref) => {
  const {
    renderTabForm,
    initValues,
    tabLabelKey,
    formatItems,
    onEdit: propsOnEdit,
    afterAddTab,
    afterRemoveTab,
    ...rest
  } = props;
  const [activeKey, setActiveKey] = useState<string | undefined>();
  const [items, setItems] = useState<Tab[]>();
  const newTabIndex = useRef(0);

  useEffect(() => {
    if (initValues) {
      const newPanes = initValues?.map((item, index) => {
        newTabIndex.current++;
        return {
          label: `${tabLabelKey}${index + 1}`,
          children: renderTabForm(index, item),
          key: String(index),
          forceRender: true,
          data: item,
        };
      });
      setItems(formatItems?.(newPanes) || newPanes);
    }
  }, [initValues]);

  const onChange = (newActiveKey: string) => {
    setActiveKey(newActiveKey);
  };

  const add = (data?: Record<string, any>) => {
    const newActiveKey = newTabIndex.current++;
    afterAddTab?.(newActiveKey);
    const newPanes = [...(items || [])];
    newPanes.push({
      label: `${tabLabelKey}${newActiveKey + 1}`,
      children: renderTabForm(newActiveKey, data),
      key: String(newActiveKey),
      forceRender: true,
      data,
    });
    setItems(formatItems?.(newPanes) || newPanes);
    setActiveKey(String(newActiveKey));
  };

  const remove = (targetKey: TargetKey) => {
    let newActiveKey = activeKey;
    let lastIndex = -1;
    items?.forEach((item, i) => {
      if (item.key === targetKey) {
        lastIndex = i - 1;
      }
    });
    const newPanes = items?.filter((item) => item.key !== targetKey);
    if (newPanes?.length && newActiveKey === targetKey) {
      if (lastIndex >= 0) {
        newActiveKey = newPanes[lastIndex].key;
      } else {
        newActiveKey = newPanes[0].key;
      }
    }
    setItems(newPanes);
    setActiveKey(newActiveKey);
    // afterRemoveTab?.(targetKey as string);
  };

  useImperativeHandle(ref, () => ({
    add,
    remove,
  }));

  const onEdit = (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove',
  ) => {
    propsOnEdit?.(targetKey, action);
    if (action === 'add') {
      add();
    } else {
      remove(targetKey);
      newTabIndex.current--;
    }
  };

  return (
    <Col span={24}>
      <AntdTabs
        style={{ padding: '0 1px', width: '100%' }}
        size="small"
        {...rest}
        hideAdd
        type="editable-card"
        onChange={onChange}
        activeKey={activeKey}
        onEdit={onEdit}
        items={items}
      />
    </Col>
  );
});

export default MyTabs;
