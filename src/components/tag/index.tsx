import { Tag as AntTag, TagProps as AntTagProps } from 'antd';
import cs from 'classnames';
import { useMemo } from 'react';

import styles from './index.less';

export type TagProps = AntTagProps & {
  valueEnum?: ValueEnumMap<string | number>;
};

const Tag: React.FC<TagProps> & {
  CheckableTag: typeof AntTag.CheckableTag;
} = ({ valueEnum, children, className, ...rest }) => {
  const findEnum = useMemo(() => {
    if (valueEnum && (typeof children === 'string' || typeof children === 'number')) {
      return valueEnum.get(children as string);
    }
  }, [valueEnum, children]);

  return (
    <AntTag color={findEnum?.color} className={cs(styles['button'], className)} {...rest}>
      {findEnum?.text ?? children}
    </AntTag>
  );
};
Tag.CheckableTag = AntTag.CheckableTag;

export default Tag;
