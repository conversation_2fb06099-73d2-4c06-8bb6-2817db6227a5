import { getInitialState } from '@/app';
import { KeepAlive, useLocation } from '@umijs/max';
import React, { useState } from 'react';

const withKeepAlive = () => { 
  return <P extends {}>(Component: React.ComponentType<P>) => {
    return (props: P) => {
      const location = useLocation();
      const [state, setState] = useState<any>({})
      getInitialState().then((res) => {
        console.log(res, 'ressssd')
        setState(res)
      })

      return (
        <KeepAlive id={location.pathname}>
          <Component {...{...(props as P), ...{state}} } />
        </KeepAlive>
      );
    };
  };
};

export { withKeepAlive };
