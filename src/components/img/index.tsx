import { Image } from 'antd';
import type { ImgHTMLAttributes } from 'react';
import { useMemo } from 'react';

export default ({ src, ...rest }: ImgHTMLAttributes<any>) => {
  const urlReal = useMemo(() => {
    const isHttp = src?.includes('http');
    if (!src) {
      return require('@/assets/common/noImg.png');
    } else if (isHttp) {
      return src;
    } else {
      let url = src.includes('api') ? src : `${PROXY_KEY}${src}`;
      return url + '?token=' + localStorage.getItem('token');
    }
  }, [src]);
  return <Image src={urlReal} alt="" {...rest} />;
};
