import type { TreeProps as AntdTreeProps } from 'antd';
import { Tree as AntdTree } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import type { BasicDataNode } from 'rc-tree/lib';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

function dictByKey(flatTree: DataNode[]): Record<React.Key, DataNode> {
  const dict: Record<React.Key, DataNode> = {};

  flatTree.forEach((item) => {
    dict[item.key] = item;
  });

  return dict;
}

/** 一维数组转树 */
function flat2tree(data: DataNode[]): DataNode[] {
  const copyData = [...data].map((v) => ({ ...v }));
  const dict = dictByKey(copyData);
  const tree: DataNode[] = [];

  copyData.forEach((item) => {
    const parent =
      dict[
        (
          item as DataNode & {
            pid: string;
          }
        ).pid
      ];

    if (parent && parent.key !== item.key) {
      if (!parent.children) {
        parent.children = [];
      }

      parent.children.push(item);

      return;
    }

    tree.push(item);
  });

  return tree;
}

/**
 * 树转一维数组
 */
const tree2list = (tree: DataNode[]): DataNode[] => {
  let res: DataNode[] = [];

  tree.forEach((item) => {
    res.push(item);
    res = res.concat(tree2list(item.children || []));
  });

  return res;
};

export type TreeProps<T extends BasicDataNode = DataNode> = AntdTreeProps<T> & {
  /** 是否可搜索 */
  searchable?: boolean;
  wrapperClassName?: string;
};

export type TreeRef<T extends BasicDataNode = DataNode> = {};

const Tree = <T extends BasicDataNode = DataNode>(
  {
    treeData: propsTreeData,
    searchable: propsSearchable,
    wrapperClassName: propsWrapperClassName,
    ...rest
  }: TreeProps<T>,
  ref: React.Ref<TreeRef<T>>,
) => {
  // 筛选项值
  const [search, setSearch] = useState('');
  const searchable = propsSearchable ?? true;
  const [treeData, setTreeData] = useState(propsTreeData);

  useEffect(() => {
    setTreeData(propsTreeData);
  }, [propsTreeData]);

  useImperativeHandle(ref, () => {
    return {};
  });

  return (
    <AntdTree<T> treeData={treeData} {...rest} />
    // <div className={cs(styles['tree-wrapper'], propsWrapperClassName)}>
    //   {searchable && (
    //     <div className={styles['toolbar']}>
    //       <Input.Search
    //         value={search}
    //         onChange={(e) => {
    //           e.persist();

    //           setSearch(e.target.value);
    //         }}
    //         placeholder="搜索"
    //       />
    //     </div>
    //   )}

    //   <AntdTree<T> treeData={treeData} {...rest} />
    // </div>
  );
};

export default forwardRef(Tree) as <T extends BasicDataNode = DataNode>(
  props: TreeProps<T> & {
    ref?: React.Ref<TreeRef<T>>;
  },
) => React.ReactElement;

export type { DataNode, BasicDataNode };
