.nodes {
  display: flex;
  align-items: center;

  &-label {
    display: flex;
    flex-grow: 1;
    align-items: center;
    min-width: 0;
    margin-right: 20px;
    user-select: text;

    &-tag {
      margin-right: 10px;
    }

    &-text {
      min-width: 0;
      font-size: 14px;

      .text-overflow();
    }

    .badge {
      margin-left: 4px;

      :global {
        .ant-badge-count {
          background-color: @primary;
        }
      }
    }
  }

  &-extra {
    flex-shrink: 0;
    margin-left: auto;
    text-align: right;
  }

  &-bot {
    margin-top: 5px;
    padding: 5px;
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 3px;

    &:empty {
      display: none;
    }
  }
}
