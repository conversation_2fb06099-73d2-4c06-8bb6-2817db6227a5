/**
 * 权限树
 */

import Tag from '@/components/tag';
import * as Enums from '@/enums/auth';
import { permissionFrontList } from '@/services/base/authFe';
import { useRequest } from '@umijs/max';
import type { DataNode } from 'antd/lib/tree';
import type { BasicDataNode } from 'rc-tree/lib';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Tree, { TreeProps } from '../index';

import styles from './index.less';

type Key = BASE.PagePermission['permissionCode'];
type AuthTreeData = DataNode & {
  data: BASE.PagePermission;
  children: AuthTreeData[];
};

export type AuthTreeProps = Omit<TreeProps<AuthTreeData>, 'treeData'> & {
  titleBotRender?: TreeProps<AuthTreeData>['titleRender'];
  onLoadData?: (tree: AuthTreeData[], list: BASE.PagePermission[]) => void;
};

export type AuthTreeRef = {
  reload: () => Promise<AuthTreeData[]>;
  expand: (open: boolean) => void;
};

/**
 * 列表转树
 */
function list2tree(list: BASE.PagePermission[]): AuthTreeData[] {
  const dict: Record<Key, AuthTreeData> = {};
  const result: AuthTreeData[] = [];

  for (let i = 0; i < list.length; i++) {
    const item = list[i];

    dict[item.id] = {
      key: item.permissionCode,
      title: item.permissionName,
      data: item,
      children: [],
    } as AuthTreeData;
  }

  for (let i = 0; i < list.length; i++) {
    const item = dict[list[i].id];
    const parent = dict[item.data.pid];

    if (parent && parent.data.id !== item.data.id) {
      parent.children!.push(item);
    } else {
      result.push(item);
    }
  }

  return result;
}

const AuthTree = (
  {
    onLoadData,
    titleRender: propsTitleRender,
    titleBotRender: propsTitleBotRender,
    defaultExpandAll = true,
    ...rest
  }: AuthTreeProps,
  ref: React.Ref<AuthTreeRef>,
) => {
  const wrap = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(100);
  const allKeys = useRef<Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const data = useRequest(async () => {
    const res = await permissionFrontList();

    const list = res.data || [];
    const keys = list.map((v) => v.permissionCode);
    const tree = list2tree(list);

    allKeys.current = keys;

    if (defaultExpandAll) {
      setExpandedKeys(keys);
    }

    onLoadData?.(tree, list);

    return {
      ...res,
      data: tree,
    };
  });

  useEffect(() => {
    if (wrap.current && wrap.current.parentElement) {
      const div = wrap.current.parentElement;
      setHeight(
        div.offsetHeight -
          parseFloat(window.getComputedStyle(div).paddingTop) -
          parseFloat(window.getComputedStyle(div).paddingBottom),
      );
    }
  }, []);

  useImperativeHandle(ref, () => {
    return {
      expand: (open) => {
        if (open) {
          setExpandedKeys(allKeys.current);
        } else {
          setExpandedKeys([]);
        }
      },
      reload: () => {
        return data.run();
      },
    };
  });

  const titleRender: AuthTreeProps['titleRender'] = (data) => {
    return (
      <div className={styles['nodes']}>
        <div className={styles['nodes-label']}>
          <Tag valueEnum={Enums.FeAuthTypeObj}>{data.data.type}</Tag>
          {data.data.permissionName}({data.data.permissionCode})
          {propsTitleRender ? (
            <div className={styles['nodes-extra']}>{propsTitleRender(data)}</div>
          ) : null}
        </div>

        {propsTitleBotRender ? (
          <div className={styles['nodes-bot']}>{propsTitleBotRender(data)}</div>
        ) : null}
      </div>
    );
  };

  return (
    <div ref={wrap}>
      <Tree<AuthTreeData>
        treeData={data.data || []}
        expandedKeys={expandedKeys}
        onExpand={(e) => {
          setExpandedKeys(e as Key[]);
        }}
        titleRender={titleRender}
        blockNode
        defaultExpandAll
        // height={height}
        {...rest}
      />
    </div>
  );
};

export default forwardRef(AuthTree) as (
  props: AuthTreeProps & {
    ref?: React.Ref<AuthTreeRef>;
  },
) => React.ReactElement;

export type { AuthTreeData, BasicDataNode };
