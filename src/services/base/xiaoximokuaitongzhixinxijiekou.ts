// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 删除通知信息表数据 POST /notifyInfo/delete */
export async function notifyInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/notifyInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取通知信息数据 POST /notifyInfo/get */
export async function notifyInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultNotifyInfo>(`/api/test/notifyInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取通知信息表分页数据 POST /notifyInfo/page */
export async function notifyInfoPage(body: BASE.NotifyInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageNotifyInfo>(`/api/test/notifyInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将通知消息设置为已读 POST /notifyInfo/read */
export async function notifyInfoRead(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/notifyInfo/read`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
