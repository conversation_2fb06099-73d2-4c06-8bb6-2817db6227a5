// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增部门信息表数据 POST /orgDeptInfo/add */
export async function orgDeptInfoAdd(body: BASE.OrgDeptInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgDeptInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除部门信息表数据 POST /orgDeptInfo/delete */
export async function orgDeptInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgDeptInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取部门信息表数据 POST /orgDeptInfo/get */
export async function orgDeptInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultOrgDeptInfo>(`/api/test/orgDeptInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取部门信息表VO数据 POST /orgDeptInfo/getVo */
export async function orgDeptInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultOrgDeptInfoVO>(`/api/test/orgDeptInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取部门信息表分页数据 POST /orgDeptInfo/page */
export async function orgDeptInfoPage(
  body: BASE.OrgDeptInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageOrgDeptInfo>(`/api/test/orgDeptInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取部门树型信息表VO数据 GET /orgDeptInfo/tree */
export async function orgDeptInfoTree(options?: { [key: string]: any }) {
  return request<BASE.IResultListOrgDeptInfo>(`/api/test/orgDeptInfo/tree`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新部门信息表数据 POST /orgDeptInfo/update */
export async function orgDeptInfoUpdate(body: BASE.OrgDeptInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgDeptInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取部门信息表VO分页数据 POST /orgDeptInfo/voPage */
export async function orgDeptInfoVoPage(
  body: BASE.OrgDeptInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageOrgDeptInfoVO>(`/api/test/orgDeptInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
