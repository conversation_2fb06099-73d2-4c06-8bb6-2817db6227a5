// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 工作台底部数据统计 POST /workStation/bottomDataCount */
export async function workStationBottomDataCount(
  body: BASE.RolesDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultObject>(`/api/test/workStation/bottomDataCount`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 工作台顶部数据统计 POST /workStation/topDataCount */
export async function workStationTopDataCount(
  body: BASE.RolesDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultObject>(`/api/test/workStation/topDataCount`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
