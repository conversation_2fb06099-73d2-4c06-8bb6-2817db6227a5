// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增荆州—工单列表表数据 POST /jzTaskWorkOrderInfo/add */
export async function jzTaskWorkOrderInfoAdd(
  body: BASE.JzTaskWorkOrderInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备检测数据 POST /jzTaskWorkOrderInfo/autoResult */
export async function jzTaskWorkOrderInfoAutoResult(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListAutoResultDto>(`/api/test/jzTaskWorkOrderInfo/autoResult`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除荆州—工单列表表数据 POST /jzTaskWorkOrderInfo/delete */
export async function jzTaskWorkOrderInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 结束任务 POST /jzTaskWorkOrderInfo/endWorkOrder */
export async function jzTaskWorkOrderInfoEndWorkOrder(
  body: BASE.JzTaskWorkOrderInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderInfo/endWorkOrder`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—工单列表表数据 POST /jzTaskWorkOrderInfo/get */
export async function jzTaskWorkOrderInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskWorkOrderInfo>(`/api/test/jzTaskWorkOrderInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—工单列表表VO数据 POST /jzTaskWorkOrderInfo/getVo */
export async function jzTaskWorkOrderInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskWorkOrderInfoVO>(`/api/test/jzTaskWorkOrderInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—工单列表表分页数据 POST /jzTaskWorkOrderInfo/page */
export async function jzTaskWorkOrderInfoPage(
  body: BASE.JzTaskWorkOrderInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskWorkOrderInfo>(`/api/test/jzTaskWorkOrderInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 启动任务 POST /jzTaskWorkOrderInfo/startWorkOrder */
export async function jzTaskWorkOrderInfoStartWorkOrder(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderInfo/startWorkOrder`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新荆州—工单列表表数据 POST /jzTaskWorkOrderInfo/update */
export async function jzTaskWorkOrderInfoUpdate(
  body: BASE.JzTaskWorkOrderInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—工单列表表VO分页数据 POST /jzTaskWorkOrderInfo/voPage */
export async function jzTaskWorkOrderInfoVoPage(
  body: BASE.JzTaskWorkOrderInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskWorkOrderInfoVO>(`/api/test/jzTaskWorkOrderInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
