// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增标准—集成装置设备信息表数据 POST /standardBasicInfo/add */
export async function standardBasicInfoAdd(
  body: BASE.StandardBasicInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除标准—集成装置设备信息表数据 POST /standardBasicInfo/delete */
export async function standardBasicInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/standardBasicInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—集成装置设备信息表数据 POST /standardBasicInfo/get */
export async function standardBasicInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultStandardBasicInfo>(`/api/test/standardBasicInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—集成装置设备信息表VO数据 POST /standardBasicInfo/getVo */
export async function standardBasicInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultStandardBasicInfoVO>(`/api/test/standardBasicInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—集成装置设备信息表分页数据 POST /standardBasicInfo/page */
export async function standardBasicInfoPage(
  body: BASE.StandardBasicInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicInfo>(`/api/test/standardBasicInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新标准—集成装置设备信息表数据 POST /standardBasicInfo/update */
export async function standardBasicInfoUpdate(
  body: BASE.StandardBasicInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—集成装置设备信息表VO分页数据 POST /standardBasicInfo/voPage */
export async function standardBasicInfoVoPage(
  body: BASE.StandardBasicInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicInfoVO>(`/api/test/standardBasicInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
