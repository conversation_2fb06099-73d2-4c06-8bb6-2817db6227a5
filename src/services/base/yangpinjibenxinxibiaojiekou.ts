// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增样品——基本信息表数据 POST /sampleInfo/add */
export async function sampleInfoAdd(body: BASE.SampleInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sampleInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 返样 POST /sampleInfo/backSampleInfo */
export async function sampleInfoBackSampleInfo(
  body: BASE.SampleInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/sampleInfo/backSampleInfo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除样品——判断是否可以删除 POST /sampleInfo/canDelSampleInfo */
export async function sampleInfoCanDelSampleInfo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sampleInfo/canDelSampleInfo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除样品——基本信息表数据 POST /sampleInfo/delete */
export async function sampleInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sampleInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品——导出模板 GET /sampleInfo/exportTemplate */
export async function sampleInfoExportTemplate(options?: { [key: string]: any }) {
  return request<any>(`/api/test/sampleInfo/exportTemplate`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取样品——随机生成样品编号和二次盲样编号 GET /sampleInfo/generateSampleNumber */
export async function sampleInfoGenerateSampleNumber(options?: { [key: string]: any }) {
  return request<BASE.IResultAutoSampleNumberDto>(`/api/test/sampleInfo/generateSampleNumber`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取样品——基本信息表数据 POST /sampleInfo/get */
export async function sampleInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSampleInfo>(`/api/test/sampleInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品——基本信息表VO数据 POST /sampleInfo/getVo */
export async function sampleInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSampleInfoVO>(`/api/test/sampleInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品——批量导入 POST /sampleInfo/importExcel */
export async function sampleInfoImportExcel(body: {}, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sampleInfo/importExcel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品——基本信息表分页数据 POST /sampleInfo/page */
export async function sampleInfoPage(body: BASE.SampleInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageSampleInfo>(`/api/test/sampleInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新样品——基本信息表数据 POST /sampleInfo/update */
export async function sampleInfoUpdate(body: BASE.SampleInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sampleInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品——基本信息表VO分页数据 POST /sampleInfo/voPage */
export async function sampleInfoVoPage(
  body: BASE.SampleInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageSampleInfoVO>(`/api/test/sampleInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
