// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增样品基本信息-关联表数据 POST /buSampleBaseInfo/add */
export async function buSampleBaseInfoAdd(
  body: BASE.BuSampleBaseInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buSampleBaseInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除样品基本信息-关联表数据 POST /buSampleBaseInfo/delete */
export async function buSampleBaseInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/buSampleBaseInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品基本信息-关联表数据 POST /buSampleBaseInfo/get */
export async function buSampleBaseInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultBuSampleBaseInfo>(`/api/test/buSampleBaseInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品基本信息-关联表VO数据 POST /buSampleBaseInfo/getVo */
export async function buSampleBaseInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultBuSampleBaseInfoVO>(`/api/test/buSampleBaseInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品基本信息-关联表分页数据 POST /buSampleBaseInfo/page */
export async function buSampleBaseInfoPage(
  body: BASE.BuSampleBaseInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBuSampleBaseInfo>(`/api/test/buSampleBaseInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新样品基本信息-关联表数据 POST /buSampleBaseInfo/update */
export async function buSampleBaseInfoUpdate(
  body: BASE.BuSampleBaseInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buSampleBaseInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取样品基本信息-关联表VO分页数据 POST /buSampleBaseInfo/voPage */
export async function buSampleBaseInfoVoPage(
  body: BASE.BuSampleBaseInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBuSampleBaseInfoVO>(`/api/test/buSampleBaseInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
