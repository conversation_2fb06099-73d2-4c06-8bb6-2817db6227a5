// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增海康威视摄像头管理数据 POST /buInstrumentCameraInfo/add */
export async function buInstrumentCameraInfoAdd(
  body: BASE.BuInstrumentCameraInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentCameraInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除海康威视摄像头管理数据 POST /buInstrumentCameraInfo/delete */
export async function buInstrumentCameraInfoDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentCameraInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取海康威视摄像头管理数据 POST /buInstrumentCameraInfo/get */
export async function buInstrumentCameraInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultBuInstrumentCameraInfo>(`/api/test/buInstrumentCameraInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取海康威视摄像头管理VO数据 POST /buInstrumentCameraInfo/getVo */
export async function buInstrumentCameraInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultBuInstrumentCameraInfoVO>(`/api/test/buInstrumentCameraInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取海康威视摄像头管理列表 GET /buInstrumentCameraInfo/list */
export async function buInstrumentCameraInfoList(options?: { [key: string]: any }) {
  return request<BASE.IResultListBuInstrumentCameraInfoVO>(
    `/api/test/buInstrumentCameraInfo/list`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 获取海康威视摄像头管理分页数据 POST /buInstrumentCameraInfo/page */
export async function buInstrumentCameraInfoPage(
  body: BASE.BuInstrumentCameraInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBuInstrumentCameraInfo>(`/api/test/buInstrumentCameraInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /buInstrumentCameraInfo/stream */
export async function buInstrumentCameraInfoStream(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.buInstrumentCameraInfoStreamParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/buInstrumentCameraInfo/stream`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新海康威视摄像头管理数据 POST /buInstrumentCameraInfo/update */
export async function buInstrumentCameraInfoUpdate(
  body: BASE.BuInstrumentCameraInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentCameraInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取海康威视摄像头管理VO分页数据 POST /buInstrumentCameraInfo/voPage */
export async function buInstrumentCameraInfoVoPage(
  body: BASE.BuInstrumentCameraInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBuInstrumentCameraInfoVO>(
    `/api/test/buInstrumentCameraInfo/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
