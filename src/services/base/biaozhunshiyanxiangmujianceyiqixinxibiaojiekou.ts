// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增标准—实验项目检测仪器信息表数据 POST /standardBasicProjectInstrument/add */
export async function standardBasicProjectInstrumentAdd(
  body: BASE.StandardBasicProjectInstrument,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicProjectInstrument/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除标准—实验项目检测仪器信息表数据 POST /standardBasicProjectInstrument/delete */
export async function standardBasicProjectInstrumentDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicProjectInstrument/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—实验项目检测仪器信息表数据 POST /standardBasicProjectInstrument/get */
export async function standardBasicProjectInstrumentGet(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicProjectInstrument>(
    `/api/test/standardBasicProjectInstrument/get`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取标准—实验项目检测仪器信息表VO数据 POST /standardBasicProjectInstrument/getVo */
export async function standardBasicProjectInstrumentGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicProjectInstrumentVO>(
    `/api/test/standardBasicProjectInstrument/getVo`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取标准—实验项目检测仪器信息表分页数据 POST /standardBasicProjectInstrument/page */
export async function standardBasicProjectInstrumentPage(
  body: BASE.StandardBasicProjectInstrumentQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicProjectInstrument>(
    `/api/test/standardBasicProjectInstrument/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新标准—实验项目检测仪器信息表数据 POST /standardBasicProjectInstrument/update */
export async function standardBasicProjectInstrumentUpdate(
  body: BASE.StandardBasicProjectInstrument,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicProjectInstrument/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—实验项目检测仪器信息表VO分页数据 POST /standardBasicProjectInstrument/voPage */
export async function standardBasicProjectInstrumentVoPage(
  body: BASE.StandardBasicProjectInstrumentQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicProjectInstrumentVO>(
    `/api/test/standardBasicProjectInstrument/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
