// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 用户自己修改密码接口 POST /orgUserInfo/self/password */
export async function orgUserInfoSelfPassword(
  body: BASE.UpdatePasswordDTO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/orgUserInfo/self/password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改系统用户密码接口 POST /orgUserInfo/update/password */
export async function orgUserInfoUpdatePassword(
  body: BASE.UpdatePasswordDTO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/orgUserInfo/update/password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
