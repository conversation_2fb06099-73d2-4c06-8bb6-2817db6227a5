// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取设备实验数据 POST /deviceIntegration/getExperimentData */
export async function deviceIntegrationGetExperimentData(
  body: BASE.DeviceTestDataRequestDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultMapStringObject>(`/api/test/deviceIntegration/getExperimentData`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备实验测试数据 POST /deviceIntegration/getExperimentTestData */
export async function deviceIntegrationGetExperimentTestData(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultMapStringObject>(`/api/test/deviceIntegration/getExperimentTestData`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询设备对接数据管理API (GET) GET /device-data/query */
export async function deviceDataQueryGet(
  params: {
    deviceCode: string;
    startTime?: string;
    endTime?: string;
  },
  options?: { [key: string]: any },
) {
  return request<BASE.IResultMapStringObject>(`/api/test/device-data/query`, {
    method: 'GET',
    params: params,
    ...(options || {}),
  });
}

/** 查询设备对接数据管理API (POST) POST /device-data/query */
export async function deviceDataQueryPost(
  body: {
    deviceCode: string;
    startTime?: string;
    endTime?: string;
  },
  options?: { [key: string]: any },
) {
  return request<BASE.IResultMapStringObject>(`/api/test/device-data/query`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
