// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增角色信息 POST /role/add */
export async function roleAdd(body: BASE.baocunjiaoseDTO, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/role/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给用户分配角色 POST /role/addByUser */
export async function roleAddByUser(
  body: BASE.AutoDtoWithRoleAddbyuser,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/role/addByUser`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除角色信息 POST /role/delete */
export async function roleDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/role/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取角色信息 POST /role/get */
export async function roleGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultRole>(`/api/test/role/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户的角色信息 POST /role/getByUser */
export async function roleGetByUser(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultListRole>(`/api/test/role/getByUser`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取角色树形数据 POST /role/tree */
export async function roleTree(body: BASE.AutoDtoWithRoleTree, options?: { [key: string]: any }) {
  return request<BASE.IResultListRoleVO>(`/api/test/role/tree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新角色信息 POST /role/update */
export async function roleUpdate(body: BASE.baocunjiaoseDTO, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/role/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
