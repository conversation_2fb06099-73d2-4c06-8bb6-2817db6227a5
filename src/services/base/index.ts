// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as authApiLog from './authApiLog';
import * as authBe from './authBe';
import * as authFe from './authFe';
import * as authLogin from './authLogin';
import * as authLoginLog from './authLoginLog';
import * as authRole from './authRole';
import * as baseCfg from './baseCfg';
import * as baseDict from './baseDict';
import * as baseDictMain from './baseDictMain';
import * as baseTree from './baseTree';
import * as baseTreeMain from './baseTreeMain';
import * as biaozhunjianceyiqibiaozhunxinxibiaojiekou from './biaozhunjianceyiqibiaozhunxinxibiaojiekou';
import * as biaozhunjichengzhuangzhishebeixinxibiaojiekou from './biaozhunjichengzhuangzhishebeixinxibiaojiekou';
import * as biaozhunmoxingcanshubiaojiekou from './biaozhunmoxingcanshubiaojiekou';
import * as biaozhunshiyanxiangmujianceyiqixinxibiaojiekou from './biaozhunshiyanxiangmujianceyiqixinxibiaojiekou';
import * as bumenxinxibiaojiekou from './bumenxinxibiaojiekou';
import * as dapingjiekou from './dapingjiekou';
import * as extFile from './extFile';
import * as extFileObs from './extFileObs';
import * as gongweixinxiguanlijiekou from './gongweixinxiguanlijiekou';
import * as gongzuotaiguanlijiekou from './gongzuotaiguanlijiekou';
import * as guowangbaogaoxinxibiaojiekou from './guowangbaogaoxinxibiaojiekou';
import * as haikangweishishexiangtouguanlijiekou from './haikangweishishexiangtouguanlijiekou';
import * as jiancerenwuceshishujubiaojiekou from './jiancerenwuceshishujubiaojiekou';
import * as jichumokuaimorenyonghuxinxichaxunjiekou from './jichumokuaimorenyonghuxinxichaxunjiekou';
import * as jingzhougongdanliebiaobiaojiekou from './jingzhougongdanliebiaobiaojiekou';
import * as jingzhoujianxiangliebiaobiaojiekou from './jingzhoujianxiangliebiaobiaojiekou';
import * as jingzhourenwuliebiaobiaojiekou from './jingzhourenwuliebiaobiaojiekou';
import * as msgConfig from './msgConfig';
import * as msgNotice from './msgNotice';
import * as msgSendLog from './msgSendLog';
import * as msgTodo from './msgTodo';
import * as msgTodoType from './msgTodoType';
import * as shebeiduijieguifanjiekou from './shebeiduijieguifanjiekou';
import * as shebeijichengjiekou from './shebeijichengjiekou';
import * as shiyanxiangmucanshubiaojiekou from './shiyanxiangmucanshubiaojiekou';
import * as tianqixinxiguanlijiekou from './tianqixinxiguanlijiekou';
import * as userPassword from './userPassword';
import * as xiaoximokuaitongzhixinxijiekou from './xiaoximokuaitongzhixinxijiekou';
import * as yangpinjibenxinxibiaojiekou from './yangpinjibenxinxibiaojiekou';
import * as yangpinjibenxinxiguanlianbiaojiekou from './yangpinjibenxinxiguanlianbiaojiekou';
import * as yiqishebeijiancerenyuanbiaojiekou from './yiqishebeijiancerenyuanbiaojiekou';
import * as yiqiyibiaojiekou from './yiqiyibiaojiekou';
import * as yonghubumenxinxibiaojiekou from './yonghubumenxinxibiaojiekou';
import * as zidingyizhanshiliebiaojiekou from './zidingyizhanshiliebiaojiekou';
import * as zuzhijiagourenyuanbiaojiekou from './zuzhijiagourenyuanbiaojiekou';
export default {
  gongzuotaiguanlijiekou,
  zidingyizhanshiliebiaojiekou,
  baseTreeMain,
  baseTree,
  baseCfg,
  jichumokuaimorenyonghuxinxichaxunjiekou,
  shiyanxiangmucanshubiaojiekou,
  biaozhunshiyanxiangmujianceyiqixinxibiaojiekou,
  biaozhunmoxingcanshubiaojiekou,
  biaozhunjianceyiqibiaozhunxinxibiaojiekou,
  biaozhunjichengzhuangzhishebeixinxibiaojiekou,
  msgSendLog,
  dapingjiekou,
  yangpinjibenxinxibiaojiekou,
  authRole,
  authFe,
  authBe,
  zuzhijiagourenyuanbiaojiekou,
  userPassword,
  yonghubumenxinxibiaojiekou,
  bumenxinxibiaojiekou,
  xiaoximokuaitongzhixinxijiekou,
  msgNotice,
  msgConfig,
  authLogin,
  authLoginLog,
  jingzhougongdanliebiaobiaojiekou,
  jiancerenwuceshishujubiaojiekou,
  jingzhoujianxiangliebiaobiaojiekou,
  jingzhourenwuliebiaobiaojiekou,
  guowangbaogaoxinxibiaojiekou,
  yiqishebeijiancerenyuanbiaojiekou,
  yiqiyibiaojiekou,
  extFileObs,
  extFile,
  tianqixinxiguanlijiekou,
  baseDictMain,
  baseDict,
  shebeiduijieguifanjiekou,
  shebeijichengjiekou,
  yangpinjibenxinxiguanlianbiaojiekou,
  gongweixinxiguanlijiekou,
  haikangweishishexiangtouguanlijiekou,
  msgTodoType,
  msgTodo,
  authApiLog,
};
