// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增仪器设备—检测人员表数据 POST /instrumentUserInfo/add */
export async function instrumentUserInfoAdd(
  body: BASE.InstrumentUserInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/instrumentUserInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除仪器设备—检测人员表数据 POST /instrumentUserInfo/delete */
export async function instrumentUserInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/instrumentUserInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取仪器设备—检测人员表数据 POST /instrumentUserInfo/get */
export async function instrumentUserInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultInstrumentUserInfo>(`/api/test/instrumentUserInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取仪器设备—检测人员表VO数据 POST /instrumentUserInfo/getVo */
export async function instrumentUserInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultInstrumentUserInfoVO>(`/api/test/instrumentUserInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取仪器设备—检测人员表分页数据 POST /instrumentUserInfo/page */
export async function instrumentUserInfoPage(
  body: BASE.InstrumentUserInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageInstrumentUserInfo>(`/api/test/instrumentUserInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新仪器设备—检测人员表数据 POST /instrumentUserInfo/update */
export async function instrumentUserInfoUpdate(
  body: BASE.InstrumentUserInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/instrumentUserInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取仪器设备—检测人员表VO分页数据 POST /instrumentUserInfo/voPage */
export async function instrumentUserInfoVoPage(
  body: BASE.InstrumentUserInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageInstrumentUserInfoVO>(`/api/test/instrumentUserInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
