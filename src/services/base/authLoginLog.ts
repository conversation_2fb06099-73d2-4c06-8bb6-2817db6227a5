// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 删除登录日志数据 POST /loginLog/delete */
export async function loginLogDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/loginLog/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取登录日志数据 POST /loginLog/get */
export async function loginLogGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultLoginLog>(`/api/test/loginLog/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将登录的账号踢下线 POST /loginLog/kick */
export async function loginLogKick(body: BASE.KickUserDTO, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/loginLog/kick`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取登录日志分页数据 POST /loginLog/page */
export async function loginLogPage(body: BASE.LoginLogQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageLoginLog>(`/api/test/loginLog/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
