declare namespace BASE {
  type ApiLog = {
    id?: number;
    /** 请求ip */
    reqIp?: string;
    /** 请求城市 */
    reqCity?: string;
    /** 请求接口地址 */
    reqApi: string;
    /** 请求类型 */
    reqType: string;
    /** 请求token */
    reqToken?: string;
    /** 请求header */
    reqHeader?: string;
    /** 请求参数 */
    reqParam?: string;
    /** 用户id */
    userId?: number;
    /** 请求用户姓名 */
    fullName?: string;
    /** 请求用户名 */
    username?: string;
    /** 请求用户类型@dic<userType> */
    userType: number;
    /** 请求客户端类型@dic<clientType> */
    clientType: number;
    /** 返回结果消息 */
    resMessage: string;
    /** 返回结果状态 */
    resStatus: number;
    /** 返回结果字符串 */
    resStr?: string;
    /** 请求开始时间 */
    startTime: string;
    /** 请求结束时间 */
    endTime: string;
    /** 请求耗时 */
    costTime: number;
    /** 创建时间 */
    createTime?: string;
    logLevel: number;
    logExplain?: string;
    extend?: string;
    /** 操作系统 */
    systemName?: string;
    /** 登录浏览器 */
    browser?: string;
  };

  type ApiLogQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    id?: number;
    /** 请求ip */
    reqIp?: string;
    /** 请求城市 */
    reqCity?: string;
    /** 请求接口地址 */
    reqApi?: string;
    /** 请求接口地址(精确查询) */
    reqApi2?: string;
    /** 请求类型 */
    reqType?: string;
    /** 请求token */
    reqToken?: string;
    /** 请求header */
    reqHeader?: string;
    /** 请求参数 */
    reqParam?: string;
    /** 用户id */
    userId?: number;
    /** 请求用户姓名 */
    fullName?: string;
    /** 请求用户名 */
    username?: string;
    /** 请求用户类型 */
    userType?: number;
    /** 请求客户端类型 */
    clientType?: number;
    /** 返回结果消息 */
    resMessage?: string;
    /** 返回结果状态 */
    resStatus?: number;
    /** 返回结果字符串 */
    resStr?: string;
    /** 日志级别 */
    logLevel?: number;
    /** 日志说明 */
    logExplain?: string;
    /** 日志扩展信息 */
    extend?: string;
    /** 请求开始开始时间 */
    startBeginTime?: string;
    /** 请求开始结束时间 */
    startEndTime?: string;
    /** 请求结束开始时间 */
    endBeginTime?: string;
    /** 请求结束结束时间 */
    endEndTime?: string;
    /** 是否根据请求耗时排序 */
    costTime?: number;
  };

  type ApiPermission = {
    id: number;
    /** 名称 */
    permissionName: string;
    /** 父级id */
    pid: number;
    /** 排序号 */
    sort: number;
    /** 接口地址 */
    url?: string;
    /** 请求方式 */
    requestWay?: string;
    /** 接口key */
    apiCode?: string;
    /** 服务名称 */
    serveName: string;
    /** 权限类型@enum<1:接口,2:模块,3:服务> */
    permissionType: number;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 状态@enum<0:不需要验证,1:需要登录,2:需要验证> */
    status: number;
    /** 修改锁定，用于自动更新权限信息时不修改某些权限 */
    updateLockType?: number;
    /** 是否保存请求日志 */
    saveLog: boolean;
    /** 日志说明 */
    logExplain?: string;
    /** 日志级别@enum<1:正常,2:敏感,3:危险> */
    logLevel: number;
    /** ip白名单 */
    whiteList?: string;
    /** ip黑名单 */
    blackList?: string;
    /** 扩展预留字段 */
    extend?: string;
    /** 数据权限：0关闭，1开启 */
    dataPermission?: number;
    /** 涉及到的数据权限表名,多张表用;分割 */
    referTableName?: string;
    child?: ApiPermission[];
  };

  type ApiPermissionQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 授权标识 */
    permissionCode?: string;
    /** 名称 */
    permissionName?: string;
    pid?: string;
    /** 排序号 */
    sort?: number;
    /** url */
    url?: string;
    /** 请求方式 */
    requestWay?: string;
    /** 1路由，2操作 */
    type?: number;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
  };

  type AutoDtoWithDicinfoList = {
    /** 字典code */
    code: string;
  };

  type AutoDtoWithFastlogin = {
    /** 用户名 */
    username: string;
  };

  type AutoDtoWithLoginGetpwd = {
    /** 密码文本 */
    password?: string;
  };

  type AutoDtoWithLoginRenewal = {
    /** 续期时长，单位毫秒 */
    time: number;
  };

  type AutoDtoWithNoticeinfoGetrecipient = {
    /** 接收类型 */
    receivingType: number;
  };

  type AutoDtoWithPermissionApiTree = {
    /** 接口名称或者接口路径 */
    name?: string;
  };

  type AutoDtoWithPermissionApiTreepage = {
    /** 1路由，2操作 */
    type: number;
  };

  type AutoDtoWithPermissionFrontTreepage = {
    type: number;
  };

  type AutoDtoWithPermissionFrontUpdatesort = {
    /** 数据集合 */
    list: string;
  };

  type AutoDtoWithRoleAddbyuser = {
    /** 角色id集合 */
    roleIds?: number[];
    /** 用户id */
    userId: number;
  };

  type AutoDtoWithRoleTree = {
    /** 是否获取全部角色，1：是，0：否 */
    all: number;
  };

  type AutoDtoWithSystemconfigGetbody = {
    /** 系统配置模块 */
    sysConfigModule: string;
  };

  type AutoDtoWithSystemconfigSetbody = {
    /** 系统配置模块 */
    sysConfigModule: string;
    /** body */
    body: string;
  };

  type AutoResultDto = {
    id?: number;
    result?: string;
    fileId?: number;
    fileInfo?: FileInfo;
    tfQualified?: string;
    paramKey?: string;
  };

  type AutoSampleNumberDto = {
    /** 样品编号 */
    sampleNumber?: string;
    /** 二次盲样编号 */
    secondaryBlindSampleNumber?: string;
  };

  type BacklogInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    id?: number;
    /** 待办类型id */
    backlogTypeId?: number;
    /** 待办类型 */
    backlogType?: string;
    /** 待办内容 */
    backlogBody?: string;
    /** 通知方式 */
    notificationMethod?: string;
    /** 创建时间(开始) */
    createBeginTime?: string;
    /** 创建时间(结束) */
    createEndTime?: string;
    /** 到期时间 */
    expirationTime?: string;
    /** 来源id */
    sourceId?: number;
    /** 状态 */
    state?: number;
    /** 处理时间(开始) */
    handleBeginTime?: string;
    /** 处理时间(结束) */
    handleEndTime?: string;
    /** 处理用户 */
    handleUserId?: number;
    /** 处理用户姓名 */
    handleUserName?: string;
    /** 处理说明 */
    handleExplain?: string;
    /** 紧急程度 */
    urgency?: number;
    /** 待办级别 */
    backlogLevel?: number;
    /** 用户id */
    userId?: number;
    stateOrder?: number;
    backlogLevelOrder?: number;
    urgencyOrder?: number;
    createTimeOrder?: number;
  };

  type BacklogInfoVO = {
    id: number;
    /** 待办类型id */
    backlogTypeId: number;
    /** 标识 */
    tag?: string;
    /** 通知方式 */
    notificationMethod?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 定时提醒设置 */
    regularReminder?: string;
    /** 下一次定时提醒时间 */
    nextRegularReminderRemindTime?: string;
    /** 周期提醒设置 */
    cycleReminder: number;
    /** 下一次周期提醒时间 */
    nextCycleReminderRemindTime?: string;
    /** 开始后多久提醒 */
    afterTheStart?: number;
    /** 开始后提醒时间 */
    afterTheStartRemindTime?: string;
    /** 结束前多久提醒 */
    beforeTheEnd?: number;
    /** 结束前提醒时间 */
    beforeTheEndRemindTime?: string;
    /** 有效期，以分钟为单位，默认一天。-1无限制 */
    termOfValidity: number;
    /** 到期时间 */
    expirationTime?: string;
    /** 待办内容 */
    backlogBody?: string;
    /** 跳转链接 */
    jumpUrl?: string;
    /** 来源id */
    sourceId?: number;
    /** 状态 */
    state: number;
    /** 处理用户 */
    handleUserId?: number;
    /** 处理时间 */
    handleTime?: string;
    /** 处理用户姓名 */
    handleUserName?: string;
    /** 处理说明 */
    handleExplain?: string;
    /** 紧急程度 */
    urgency: number;
    /** 待办级别 */
    backlogLevel: number;
    userState?: number;
    /** 待办类型名称 */
    backlogType?: string;
  };

  type BacklogMain = {
    id: number;
    /** 待办类型名称 */
    backlogType: string;
    /** 待办级别 */
    backlogLevel: number;
    /** 通知方式 */
    notificationMethod?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 定时提醒设置 */
    regularReminder?: string;
    /** 周期提醒设置 */
    cycleReminder?: number;
    /** 开始后多久提醒 */
    afterTheStart?: number;
    /** 结束前多久提醒 */
    beforeTheEnd?: number;
    /** 有效期，以分钟为单位，默认一天。-1无限制 */
    termOfValidity: number;
  };

  type BacklogMainQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 待办类型名称 */
    backlogType?: string;
    /** 待办级别 */
    backlogLevel?: number;
    /** 通知方式 */
    notificationMethod?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 定时提醒设置 */
    regularReminder?: string;
    /** 周期提醒设置 */
    cycleReminder?: string;
    /** 开始后多久提醒 */
    afterTheStart?: string;
    /** 结束前多久提醒 */
    beforeTheEnd?: string;
    /** 有效期，以分钟为单位，默认一天。-1无限制 */
    termOfValidity?: number;
  };

  type BacklogMainVO = {
    id: number;
    /** 待办类型名称 */
    backlogType: string;
    /** 待办级别 */
    backlogLevel: number;
    /** 通知方式 */
    notificationMethod?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 定时提醒设置 */
    regularReminder?: string;
    /** 周期提醒设置 */
    cycleReminder?: number;
    /** 开始后多久提醒 */
    afterTheStart?: number;
    /** 结束前多久提醒 */
    beforeTheEnd?: number;
    /** 有效期，以分钟为单位，默认一天。-1无限制 */
    termOfValidity: number;
    /** 发送消息方式配置 */
    sendMsgConfigList?: SendMsgConfigDTO[];
  };

  type baocunjiaoseDTO = {
    id: number;
    /** 父级角色 */
    pid: number;
    /** 权限 */
    permissions?: string;
    /** 角色名称 */
    roleName: string;
    /** 角色编码 */
    roleCode: string;
    /** 角色类型@enum<1:系统角色,2:普通角色> */
    roleType: number;
    /** 角色排序 */
    roleSort?: number;
    /** 状态 */
    state?: number;
    /** 角色说明 */
    roleExplain?: string;
    /** 扩展预留字段 */
    extend?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 权限编码列表 */
    permissionCodes?: string[];
  };

  type BuInstrumentCameraInfo = {
    /** 摄像头类型(球机/枪机/半球等) */
    cameraType?: string;
    /** 通道号 */
    channelNum?: number;
    /** 创建时间 */
    createTime?: string;
    /** 设备编号 */
    deviceCode?: string;
    /** 摄像头型号 */
    deviceModel?: string;
    /** 摄像头名称 */
    deviceName?: string;
    /** 设备状态 */
    deviceStatus?: string;
    /** 主键 */
    id: number;
    /** 安装位置 */
    installationLocation?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 登录密码 */
    password?: string;
    /** 端口号 */
    port?: number;
    /** RTSP流地址 */
    rtspUrl?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 登录用户名 */
    username?: string;
    /** 工位id */
    workstationId?: number;
  };

  type BuInstrumentCameraInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 摄像头类型(球机/枪机/半球等) */
    cameraType?: string;
    /** 通道号 */
    channelNum?: number;
    /** 创建时间 */
    createTime?: string;
    /** 设备编号 */
    deviceCode?: string;
    /** 摄像头型号 */
    deviceModel?: string;
    /** 摄像头名称 */
    deviceName?: string;
    /** 设备状态 */
    deviceStatus?: string;
    /** 主键 */
    id?: number;
    /** 安装位置 */
    installationLocation?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 是否在线(1在线/0离线) */
    isOnline?: number;
    /** 登录密码 */
    password?: string;
    /** 端口号 */
    port?: number;
    /** RTSP流地址 */
    rtspUrl?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 登录用户名 */
    username?: string;
    /** 工位id */
    workstationId?: number;
    workstationIds?: number[];
  };

  type buInstrumentCameraInfoStreamParams = {
    id: number;
  };

  type BuInstrumentCameraInfoVO = {
    /** 摄像头类型(球机/枪机/半球等) */
    cameraType?: string;
    /** 通道号 */
    channelNum?: number;
    /** 创建时间 */
    createTime?: string;
    /** 设备编号 */
    deviceCode?: string;
    /** 摄像头型号 */
    deviceModel?: string;
    /** 摄像头名称 */
    deviceName?: string;
    /** 设备状态 */
    deviceStatus?: string;
    /** 主键 */
    id: number;
    /** 安装位置 */
    installationLocation?: string;
    /** IP地址 */
    ipAddress?: string;
    /** 登录密码 */
    password?: string;
    /** 端口号 */
    port?: number;
    /** RTSP流地址 */
    rtspUrl?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 登录用户名 */
    username?: string;
    /** 工位id */
    workstationId?: number;
    buInstrumentWorkstationInfo?: BuInstrumentWorkstationInfo;
    flvUrl?: string;
    tfTesting?: boolean;
    largeScreenTitle?: string;
  };

  type BuInstrumentWorkstationInfo = {
    /** 创建时间 */
    createTime?: string;
    /** 工位描述 */
    description?: string;
    /** 主键 */
    id: number;
    /** 工位位置 */
    location?: string;
    /** 负责人 */
    responsiblePerson?: string;
    /** 工位状态(空闲/使用中/维护中) */
    status?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 工位编号 */
    workstationCode: string;
    /** 工位名称 */
    workstationName: string;
  };

  type BuInstrumentWorkstationInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 创建时间 */
    createTime?: string;
    /** 工位描述 */
    description?: string;
    /** 主键 */
    id?: number;
    /** 工位位置 */
    location?: string;
    /** 负责人 */
    responsiblePerson?: string;
    /** 工位状态(空闲/使用中/维护中) */
    status?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 工位编号 */
    workstationCode?: string;
    /** 工位名称 */
    workstationName?: string;
  };

  type BuInstrumentWorkstationInfoVO = {
    /** 创建时间 */
    createTime?: string;
    /** 工位描述 */
    description?: string;
    /** 主键 */
    id: number;
    /** 工位位置 */
    location?: string;
    /** 负责人 */
    responsiblePerson?: string;
    /** 工位状态(空闲/使用中/维护中) */
    status?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 工位编号 */
    workstationCode: string;
    /** 工位名称 */
    workstationName: string;
    instrumentNewInfos?: InstrumentNewInfo[];
    buInstrumentCameraInfo?: BuInstrumentCameraInfo;
  };

  type BuSampleBaseInfo = {
    id: number;
    /** 样品名称 */
    name?: string;
    /** 样品型号 */
    type?: string;
    /** 样品规格 */
    model?: string;
    createTime?: string;
    updateTime?: string;
  };

  type BuSampleBaseInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 样品名称 */
    name?: string;
    /** 样品型号 */
    type?: string;
    /** 样品规格 */
    model?: string;
    createTime?: string;
    updateTime?: string;
  };

  type BuSampleBaseInfoVO = {
    id: number;
    /** 样品名称 */
    name?: string;
    /** 样品型号 */
    type?: string;
    /** 样品规格 */
    model?: string;
    createTime?: string;
    updateTime?: string;
  };

  type ClearFileDTO = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 文件类型 */
    fileType?: number;
    /** 文件用途 */
    filePurpose: number;
    /** 用户id */
    userId?: number;
  };

  type CodeDTO = {
    /** code */
    code: string;
  };

  type Columns = {
    title?: string;
    dataIndex?: string;
  };

  type DeviceIntegrationConfig = {
    /** 主键ID */
    id: number;
    /** 设备类型 */
    deviceType?: string;
    /** 设备名称 */
    deviceName?: string;
    /** 产品代码 */
    productCode?: string;
    /** 设备代码 */
    deviceCode?: string;
    /** 类型代码 */
    typeCode?: string;
    /** 请求模板 */
    requestTemplate?: string;
    /** 响应模板 */
    responseTemplate?: string;
    /** 参数映射 */
    paramMapping?: string;
    /** 状态(enabled/disabled) */
    status?: string;
    /** 描述 */
    description?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type DeviceIntegrationConfigQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 主键ID */
    id?: number;
    /** 设备类型 */
    deviceType?: string;
    /** 设备名称 */
    deviceName?: string;
    /** 产品代码 */
    productCode?: string;
    /** 设备代码 */
    deviceCode?: string;
    /** 类型代码 */
    typeCode?: string;
    /** 状态(enabled/disabled) */
    status?: string;
  };

  type DeviceIntegrationConfigVO = {
    /** 主键ID */
    id: number;
    /** 设备类型 */
    deviceType?: string;
    /** 设备名称 */
    deviceName?: string;
    /** 产品代码 */
    productCode?: string;
    /** 设备代码 */
    deviceCode?: string;
    /** 类型代码 */
    typeCode?: string;
    /** 请求模板 */
    requestTemplate?: string;
    /** 响应模板 */
    responseTemplate?: string;
    /** 参数映射 */
    paramMapping?: string;
    /** 状态(enabled/disabled) */
    status?: string;
    /** 描述 */
    description?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 关联设备数量 */
    deviceCount?: number;
  };

  type DeviceTestDataRequestDto = {
    equipmentId?: number;
    parameterIds?: number[];
  };

  type DicCopyDTO = {
    /** 要复制的字典id */
    id: number;
    /** 复制后的字典code */
    dicCode: string;
    /** 复制后的字典名称 */
    dicName: string;
  };

  type dicInfoImportParams = {
    /** 文件 */
    file: any;
    /** 字典标识 */
    dicCode: any;
    /** 字典值存在时是否覆盖 */
    cover: number;
  };

  type DicInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 主表id */
    mainId?: number;
    /** 字典值 */
    dicValue?: string;
    /** 字典名称 */
    dicName?: string;
    /** 说明 */
    explain?: string;
    /** 字典状态@enum<1:启用,0:禁用> */
    dicStatus: number;
    /** 排序 */
    dicSort?: number;
  };

  type DicInfoVO = {
    id?: number;
    /** 字典标识 */
    dicCode: string;
    /** 字典值 */
    dicValue: string;
    /** 数字字典值 */
    intValue?: number;
    /** 字典名称 */
    dicName: string;
    /** 说明 */
    dicExplain?: string;
    /** 排序 */
    dicSort: number;
    /** 字典状态@enum<1:启用,0:禁用> */
    dicStatus: number;
    /** 字典图标 */
    dicIcon?: string;
    /** 字典颜色 */
    dicColour?: string;
    /** 字典扩展信息 */
    dicExtend?: string;
    /** 主表id */
    mainId: number;
    /** 字典标识 */
    dicMainName: string;
  };

  type DicMain = {
    id?: number;
    /** 字典标识 */
    dicCode: string;
    /** 字典名称 */
    dicName: string;
    /** 说明 */
    dicExplain?: string;
    /** 字典值类型@enum<1:数值,2:布尔,3:字符串> */
    valueType: number;
  };

  type DicMainQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 字典标识 */
    dicCode?: string;
    /** 字典名称 */
    dicName?: string;
    /** 说明 */
    explain?: string;
  };

  type docOpenDocParams = {
    fileCode: string;
    fileName: string;
  };

  type environmentInfoGetWeatherParams = {
    cityName?: string;
  };

  type fileBase64UploadParams = {
    /** 文件 */
    file: string;
    /** 文件名称 */
    name: string;
    /** 文件可见性（0，不可见（删除），1，不限制，2本人，3登录，4其他） */
    showState: number;
    /** 文件用途 */
    filePurpose: number;
    fullName: string;
  };

  type fileDownLoadParams = {
    fileCode: string;
    fileName: string;
    preview?: boolean;
  };

  type FileInfo = {
    id?: number;
    /** 文件后缀 */
    filePostfix?: string;
    /** 文件名称 */
    fileName?: string;
    /** 文件地址 */
    fileUrl?: string;
    /** 文件存放地址 */
    filePath?: string;
    /** 文件编号 */
    fileCode?: string;
    /** 文件类型@enum<1:图片,2:文档,3:视频,4:音乐,5:其他> */
    fileType?: number;
    /** 文件长度 */
    fileLength?: number;
    /** 文件大小（中文） */
    fileSize?: string;
    /** 文件md5 */
    fileMd5?: string;
    /** 用户id */
    userId?: number;
    /** 上传用户姓名 */
    fullName?: string;
    /** 状态@enum<0:已删除,1:不限制,2:本人,3:登录,4:其他> */
    showState?: number;
    /** 文件用途@dic<filePurpose> */
    filePurpose?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 备用字段1 */
    extraOne?: string;
    /** 备用字段2 */
    extraTwo?: string;
  };

  type FileInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 文件名称 */
    fileName?: string;
    /** 文件地址 */
    fileUrl?: string;
    /** 文件存放地址 */
    filePath?: string;
    /** 文件编号 */
    fileCode?: string;
    /** 文件类型 */
    fileType?: string;
    /** 文件用途 */
    filePurpose?: string;
    /** 用户id */
    userId?: number;
    /** 0已删除，1不限制，2本人，3登录，4其他 */
    showState?: string;
  };

  type fileObsSliceConfirmParams = {
    /** 文件上下文编号 */
    id: string;
    /** 文件名称 */
    name: string;
    /** 总字节数 */
    byteLength: string;
    /** 分片个数 */
    sliceLength: string;
    /** 0，不可见（删除），1，不限制，2本人，3登录，4其他 */
    showState?: string;
    /** 文件用途，根据字典filePurpose获取 */
    filePurpose?: string;
  };

  type fileObsSliceUploadParams = {
    /** 文件 */
    file: any;
    /** 文件上下文编号 */
    id: string;
    /** 分片序号 */
    seq: number;
  };

  type fileObsUploadParams = {
    /** 文件 */
    file: any;
    /** 文件名称 */
    name: string;
    /** 文件可见性（0，不可见（删除），1，不限制，2本人，3登录，4其他） */
    showState: number;
    /** 文件用途 */
    filePurpose: number;
  };

  type filePlayParams = {
    fileCode: string;
    fileName: string;
  };

  type fileSliceConfirmParams = {
    /** 文件上下文编号 */
    id: string;
    /** 文件名称 */
    name: string;
    /** 总字节数 */
    byteLength: string;
    /** 分片个数 */
    sliceLength: string;
    /** 0，不可见（删除），1，不限制，2本人，3登录，4其他 */
    showState?: string;
    /** 文件用途，根据字典filePurpose获取 */
    filePurpose?: string;
  };

  type fileSliceUploadParams = {
    /** 文件 */
    file: any;
    /** 文件上下文编号 */
    id: string;
    /** 分片序号 */
    seq: number;
  };

  type fileUploadParams = {
    /** 文件 */
    file: any;
    /** 文件名称 */
    name: string;
    /** 文件可见性（0，不可见（删除），1，不限制，2本人，3登录，4其他） */
    showState: number;
    /** 文件用途 */
    filePurpose: number;
  };

  type ID = {
    /** id */
    id?: string;
  };

  type InstrumentNewInfo = {
    /** 专业类型 */
    professionalType?: string;
    /** 设备管理员 */
    equipmentAdmin?: string;
    /** 投入使用时间 */
    commissionDate?: string;
    /** 主键 */
    id: number;
    /** 设备编号 */
    sbbh?: string;
    /** 设备类型 */
    sblb?: string;
    /** 设备名称 */
    sbmc?: string;
    /** 生产厂 */
    scc?: string;
    createTime?: string;
    updateTime?: string;
    jzyxq?: string;
    serialNumber?: string;
    /** 设备使用状态 */
    statusEquipmentUsage?: string;
    /** 物联网id */
    internetThingId?: number;
    /** 工位id */
    workstationId?: number;
    /** 管理测试人员id */
    testUserId?: string;
    /** 关联的检测实验id */
    gwBzId?: number;
    /** 设备可检参数（用逗号分开） */
    parametersInspectedEquipment?: string;
    /** 数据采集方式（直采/填报） */
    collectionMethod?: string;
    /** 关联的检测实验id */
    gwBzIds?: number[];
    testUserIds?: number[];
    /** 设备类型代码 */
    deviceTypeCode?: string;
    /** 设备参数JSON */
    deviceParams?: string;
    /** 设备对接规范id */
    deviceIntegrationConfigId?: number;
  };

  type instrumentNewInfoExportCheckParamsTemplateParams = {
    equipmentId?: number;
  };

  type instrumentNewInfoImportCheckParamsParams = {
    equipmentId: number;
  };

  type InstrumentNewInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 保管人 */
    bgr?: string;
    /** 设备出厂编号（序列号） */
    ccbh?: string;
    /** 到货日期 */
    dhrq?: string;
    /** 到货日期，1倒叙，0正序 */
    dhrqSort?: number;
    /** 放置地点 */
    fzdd?: string;
    /** 供应商 */
    gys?: string;
    /** 主键 */
    id?: number;
    /** 计划计量溯源日期 */
    jhjlrq?: string;
    /** 计划计量溯源日期，1倒叙，0正序 */
    jhjlrqSort?: number;
    /** 计量溯源机构 */
    jlsyjg?: string;
    /** 计量溯源类型 */
    jlsylx?: string;
    /** 计量证书编号 */
    jlzsbh?: string;
    /** 软件版本 */
    rjbb?: string;
    /** 设备编号 */
    sbbh?: string;
    /** 设备类型 */
    sblb?: string;
    /** 设备名称 */
    sbmc?: string;
    /** 设备型号 */
    sbxh?: string;
    /** 设备状态 */
    sbzt?: string;
    /** 生产厂 */
    scc?: string;
    /** 实际计量日期 */
    sjjlrq?: string;
    /** 到货日期，1倒叙，0正序 */
    sjjlrqSort?: number;
    /** 使用部门 */
    sybm?: string;
    /** 使用人 */
    syr?: string;
    /** 硬件版本 */
    yjbb?: string;
    /** 业务状态 */
    ywzt?: string;
    /** 资产编号 */
    zcbh?: string;
    /** 资产类型 */
    zclx?: string;
    /** 类型 */
    typeInfo?: string;
    /** 所属系统 */
    sysName?: string;
    /** 部门Id */
    deptId?: number;
    /** 领域名称 */
    fieldChildName?: string;
    /** 专业类型 */
    professionalType?: string;
    /** 数据采集方式（直采/填报） */
    collectionMethod?: string;
  };

  type InstrumentNewInfoVO = {
    /** 专业类型 */
    professionalType?: string;
    /** 设备管理员 */
    equipmentAdmin?: string;
    /** 投入使用时间 */
    commissionDate?: string;
    /** 主键 */
    id: number;
    /** 设备编号 */
    sbbh?: string;
    /** 设备类型 */
    sblb?: string;
    /** 设备名称 */
    sbmc?: string;
    /** 生产厂 */
    scc?: string;
    createTime?: string;
    updateTime?: string;
    jzyxq?: string;
    serialNumber?: string;
    /** 设备使用状态 */
    statusEquipmentUsage?: string;
    /** 物联网id */
    internetThingId?: number;
    /** 工位id */
    workstationId?: number;
    /** 管理测试人员id */
    testUserId?: string;
    /** 关联的检测实验id */
    gwBzId?: number;
    /** 设备可检参数（用逗号分开） */
    parametersInspectedEquipment?: string;
    /** 数据采集方式（直采/填报） */
    collectionMethod?: string;
    /** 关联的检测实验id */
    gwBzIds?: number[];
    testUserIds?: number[];
    /** 设备类型代码 */
    deviceTypeCode?: string;
    /** 设备参数JSON */
    deviceParams?: string;
    /** 设备对接规范id */
    deviceIntegrationConfigId?: number;
    /** 计时信息（测试子项） */
    workTimeByResult?: number;
    /** 计时信息（测试子项历史记录） */
    workTimeByResultHistory?: number;
    buInstrumentWorkstationInfo?: BuInstrumentWorkstationInfo;
    /** 工位信息名称 */
    workstationName?: string;
    proName?: string[];
    joinInfos?: JoinInfo[];
    testUsers?: InstrumentUserInfo[];
    testUserFullNames?: string[];
  };

  type InstrumentUserInfo = {
    id: number;
    instrumentNewId?: number;
    userId?: number;
    userFullName?: string;
    sortNum?: number;
  };

  type InstrumentUserInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    instrumentNewId?: number;
    userId?: number;
    userFullName?: string;
    sortNum?: number;
  };

  type InstrumentUserInfoVO = {
    id: number;
    instrumentNewId?: number;
    userId?: number;
    userFullName?: string;
    sortNum?: number;
  };

  type IPageApiLog = {
    total?: number;
    current?: number;
    pages?: number;
    records?: ApiLog[];
    size?: number;
  };

  type IPageApiPermission = {
    total?: number;
    current?: number;
    pages?: number;
    records?: ApiPermission[];
    size?: number;
  };

  type IPageBacklogInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BacklogInfoVO[];
    size?: number;
  };

  type IPageBacklogMain = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BacklogMain[];
    size?: number;
  };

  type IPageBacklogMainVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BacklogMainVO[];
    size?: number;
  };

  type IPageBuInstrumentCameraInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BuInstrumentCameraInfo[];
    size?: number;
  };

  type IPageBuInstrumentCameraInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BuInstrumentCameraInfoVO[];
    size?: number;
  };

  type IPageBuInstrumentWorkstationInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BuInstrumentWorkstationInfo[];
    size?: number;
  };

  type IPageBuInstrumentWorkstationInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BuInstrumentWorkstationInfoVO[];
    size?: number;
  };

  type IPageBuSampleBaseInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BuSampleBaseInfo[];
    size?: number;
  };

  type IPageBuSampleBaseInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: BuSampleBaseInfoVO[];
    size?: number;
  };

  type IPageDeviceIntegrationConfig = {
    total?: number;
    current?: number;
    pages?: number;
    records?: DeviceIntegrationConfig[];
    size?: number;
  };

  type IPageDeviceIntegrationConfigVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: DeviceIntegrationConfigVO[];
    size?: number;
  };

  type IPageDicInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: DicInfoVO[];
    size?: number;
  };

  type IPageDicMain = {
    total?: number;
    current?: number;
    pages?: number;
    records?: DicMain[];
    size?: number;
  };

  type IPageFileInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: FileInfo[];
    size?: number;
  };

  type IPageInstrumentNewInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: InstrumentNewInfo[];
    size?: number;
  };

  type IPageInstrumentNewInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: InstrumentNewInfoVO[];
    size?: number;
  };

  type IPageInstrumentUserInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: InstrumentUserInfo[];
    size?: number;
  };

  type IPageInstrumentUserInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: InstrumentUserInfoVO[];
    size?: number;
  };

  type IPageIUser = {
    total?: number;
    current?: number;
    pages?: number;
    records?: IUser[];
    size?: number;
  };

  type IPageJzReportInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzReportInfo[];
    size?: number;
  };

  type IPageJzReportInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzReportInfoVO[];
    size?: number;
  };

  type IPageJzTaskInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskInfo[];
    size?: number;
  };

  type IPageJzTaskInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskInfoVO[];
    size?: number;
  };

  type IPageJzTaskInspectionItemInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskInspectionItemInfo[];
    size?: number;
  };

  type IPageJzTaskInspectionItemInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskInspectionItemInfoVO[];
    size?: number;
  };

  type IPageJzTaskTestDataInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskTestDataInfo[];
    size?: number;
  };

  type IPageJzTaskTestDataInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskTestDataInfoVO[];
    size?: number;
  };

  type IPageJzTaskWorkOrderInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskWorkOrderInfo[];
    size?: number;
  };

  type IPageJzTaskWorkOrderInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskWorkOrderInfoVO[];
    size?: number;
  };

  type IPageJzTaskWorkOrderModelParam = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskWorkOrderModelParam[];
    size?: number;
  };

  type IPageJzTaskWorkOrderModelParamVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: JzTaskWorkOrderModelParamVO[];
    size?: number;
  };

  type IPageLoginLog = {
    total?: number;
    current?: number;
    pages?: number;
    records?: LoginLog[];
    size?: number;
  };

  type IPageNoticeInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: NoticeInfo[];
    size?: number;
  };

  type IPageNoticeInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: NoticeInfoVO[];
    size?: number;
  };

  type IPageNotifyInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: NotifyInfo[];
    size?: number;
  };

  type IPageOrgDeptInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: OrgDeptInfo[];
    size?: number;
  };

  type IPageOrgDeptInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: OrgDeptInfoVO[];
    size?: number;
  };

  type IPageOrgUserDeptInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: OrgUserDeptInfo[];
    size?: number;
  };

  type IPageOrgUserDeptInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: OrgUserDeptInfoVO[];
    size?: number;
  };

  type IPageOrgUserInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: OrgUserInfo[];
    size?: number;
  };

  type IPageOrgUserInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: OrgUserInfoVO[];
    size?: number;
  };

  type IPagePagePermission = {
    total?: number;
    current?: number;
    pages?: number;
    records?: PagePermission[];
    size?: number;
  };

  type IPageSampleInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: SampleInfo[];
    size?: number;
  };

  type IPageSampleInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: SampleInfoVO[];
    size?: number;
  };

  type IPageSendRecord = {
    total?: number;
    current?: number;
    pages?: number;
    records?: SendRecord[];
    size?: number;
  };

  type IPageStandardBasicInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicInfo[];
    size?: number;
  };

  type IPageStandardBasicInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicInfoVO[];
    size?: number;
  };

  type IPageStandardBasicInstrumentInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicInstrumentInfo[];
    size?: number;
  };

  type IPageStandardBasicInstrumentInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicInstrumentInfoVO[];
    size?: number;
  };

  type IPageStandardBasicModelParam = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicModelParam[];
    size?: number;
  };

  type IPageStandardBasicModelParamVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicModelParamVO[];
    size?: number;
  };

  type IPageStandardBasicProjectInstrument = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicProjectInstrument[];
    size?: number;
  };

  type IPageStandardBasicProjectInstrumentVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicProjectInstrumentVO[];
    size?: number;
  };

  type IPageStandardBasicProjectParam = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicProjectParam[];
    size?: number;
  };

  type IPageStandardBasicProjectParamVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: StandardBasicProjectParamVO[];
    size?: number;
  };

  type IPageSystemConfig = {
    total?: number;
    current?: number;
    pages?: number;
    records?: SystemConfig[];
    size?: number;
  };

  type IPageTreeInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: TreeInfo[];
    size?: number;
  };

  type IPageTreeMain = {
    total?: number;
    current?: number;
    pages?: number;
    records?: TreeMain[];
    size?: number;
  };

  type IPageUserTableColumnInfo = {
    total?: number;
    current?: number;
    pages?: number;
    records?: UserTableColumnInfo[];
    size?: number;
  };

  type IPageUserTableColumnInfoVO = {
    total?: number;
    current?: number;
    pages?: number;
    records?: UserTableColumnInfoVO[];
    size?: number;
  };

  type IResult = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Record<string, any>;
  };

  type IResultApiLog = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: ApiLog;
  };

  type IResultApiPermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: ApiPermission;
  };

  type IResultAutoSampleNumberDto = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: AutoSampleNumberDto;
  };

  type IResultBacklogMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BacklogMain;
  };

  type IResultBacklogMainVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BacklogMainVO;
  };

  type IResultBoolean = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: boolean;
  };

  type IResultBuInstrumentCameraInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BuInstrumentCameraInfo;
  };

  type IResultBuInstrumentCameraInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BuInstrumentCameraInfoVO;
  };

  type IResultBuInstrumentWorkstationInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BuInstrumentWorkstationInfo;
  };

  type IResultBuInstrumentWorkstationInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BuInstrumentWorkstationInfoVO;
  };

  type IResultBuSampleBaseInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BuSampleBaseInfo;
  };

  type IResultBuSampleBaseInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: BuSampleBaseInfoVO;
  };

  type IResultDeviceIntegrationConfig = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: DeviceIntegrationConfig;
  };

  type IResultDicMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: DicMain;
  };

  type IResultFileInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: FileInfo;
  };

  type IResultInstrumentNewInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: InstrumentNewInfo;
  };

  type IResultInstrumentNewInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: InstrumentNewInfoVO;
  };

  type IResultInstrumentUserInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: InstrumentUserInfo;
  };

  type IResultInstrumentUserInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: InstrumentUserInfoVO;
  };

  type IResultIPageApiLog = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageApiLog;
  };

  type IResultIPageApiPermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageApiPermission;
  };

  type IResultIPageBacklogInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBacklogInfoVO;
  };

  type IResultIPageBacklogMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBacklogMain;
  };

  type IResultIPageBacklogMainVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBacklogMainVO;
  };

  type IResultIPageBuInstrumentCameraInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBuInstrumentCameraInfo;
  };

  type IResultIPageBuInstrumentCameraInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBuInstrumentCameraInfoVO;
  };

  type IResultIPageBuInstrumentWorkstationInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBuInstrumentWorkstationInfo;
  };

  type IResultIPageBuInstrumentWorkstationInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBuInstrumentWorkstationInfoVO;
  };

  type IResultIPageBuSampleBaseInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBuSampleBaseInfo;
  };

  type IResultIPageBuSampleBaseInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageBuSampleBaseInfoVO;
  };

  type IResultIPageDeviceIntegrationConfig = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageDeviceIntegrationConfig;
  };

  type IResultIPageDeviceIntegrationConfigVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageDeviceIntegrationConfigVO;
  };

  type IResultIPageDicInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageDicInfoVO;
  };

  type IResultIPageDicMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageDicMain;
  };

  type IResultIPageFileInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageFileInfo;
  };

  type IResultIPageInstrumentNewInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageInstrumentNewInfo;
  };

  type IResultIPageInstrumentNewInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageInstrumentNewInfoVO;
  };

  type IResultIPageInstrumentUserInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageInstrumentUserInfo;
  };

  type IResultIPageInstrumentUserInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageInstrumentUserInfoVO;
  };

  type IResultIPageIUser = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageIUser;
  };

  type IResultIPageJzReportInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzReportInfo;
  };

  type IResultIPageJzReportInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzReportInfoVO;
  };

  type IResultIPageJzTaskInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskInfo;
  };

  type IResultIPageJzTaskInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskInfoVO;
  };

  type IResultIPageJzTaskInspectionItemInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskInspectionItemInfo;
  };

  type IResultIPageJzTaskInspectionItemInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskInspectionItemInfoVO;
  };

  type IResultIPageJzTaskTestDataInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskTestDataInfo;
  };

  type IResultIPageJzTaskTestDataInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskTestDataInfoVO;
  };

  type IResultIPageJzTaskWorkOrderInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskWorkOrderInfo;
  };

  type IResultIPageJzTaskWorkOrderInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskWorkOrderInfoVO;
  };

  type IResultIPageJzTaskWorkOrderModelParam = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskWorkOrderModelParam;
  };

  type IResultIPageJzTaskWorkOrderModelParamVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageJzTaskWorkOrderModelParamVO;
  };

  type IResultIPageLoginLog = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageLoginLog;
  };

  type IResultIPageNoticeInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageNoticeInfo;
  };

  type IResultIPageNoticeInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageNoticeInfoVO;
  };

  type IResultIPageNotifyInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageNotifyInfo;
  };

  type IResultIPageOrgDeptInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageOrgDeptInfo;
  };

  type IResultIPageOrgDeptInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageOrgDeptInfoVO;
  };

  type IResultIPageOrgUserDeptInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageOrgUserDeptInfo;
  };

  type IResultIPageOrgUserDeptInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageOrgUserDeptInfoVO;
  };

  type IResultIPageOrgUserInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageOrgUserInfo;
  };

  type IResultIPageOrgUserInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageOrgUserInfoVO;
  };

  type IResultIPagePagePermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPagePagePermission;
  };

  type IResultIPageSampleInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageSampleInfo;
  };

  type IResultIPageSampleInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageSampleInfoVO;
  };

  type IResultIPageSendRecord = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageSendRecord;
  };

  type IResultIPageStandardBasicInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicInfo;
  };

  type IResultIPageStandardBasicInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicInfoVO;
  };

  type IResultIPageStandardBasicInstrumentInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicInstrumentInfo;
  };

  type IResultIPageStandardBasicInstrumentInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicInstrumentInfoVO;
  };

  type IResultIPageStandardBasicModelParam = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicModelParam;
  };

  type IResultIPageStandardBasicModelParamVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicModelParamVO;
  };

  type IResultIPageStandardBasicProjectInstrument = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicProjectInstrument;
  };

  type IResultIPageStandardBasicProjectInstrumentVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicProjectInstrumentVO;
  };

  type IResultIPageStandardBasicProjectParam = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicProjectParam;
  };

  type IResultIPageStandardBasicProjectParamVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageStandardBasicProjectParamVO;
  };

  type IResultIPageSystemConfig = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageSystemConfig;
  };

  type IResultIPageTreeInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageTreeInfo;
  };

  type IResultIPageTreeMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageTreeMain;
  };

  type IResultIPageUserTableColumnInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageUserTableColumnInfo;
  };

  type IResultIPageUserTableColumnInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IPageUserTableColumnInfoVO;
  };

  type IResultIUser = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: IUser;
  };

  type IResultJzReportInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzReportInfo;
  };

  type IResultJzReportInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzReportInfoVO;
  };

  type IResultJzTaskInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskInfo;
  };

  type IResultJzTaskInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskInfoVO;
  };

  type IResultJzTaskInspectionItemInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskInspectionItemInfo;
  };

  type IResultJzTaskInspectionItemInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskInspectionItemInfoVO;
  };

  type IResultJzTaskTestDataInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskTestDataInfo;
  };

  type IResultJzTaskTestDataInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskTestDataInfoVO;
  };

  type IResultJzTaskWorkOrderInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskWorkOrderInfo;
  };

  type IResultJzTaskWorkOrderInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskWorkOrderInfoVO;
  };

  type IResultJzTaskWorkOrderModelParam = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskWorkOrderModelParam;
  };

  type IResultJzTaskWorkOrderModelParamVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: JzTaskWorkOrderModelParamVO;
  };

  type IResultListApiPermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: ApiPermission[];
  };

  type IResultListAutoResultDto = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: AutoResultDto[];
  };

  type IResultListBuInstrumentCameraInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: BuInstrumentCameraInfoVO[];
  };

  type IResultListDicInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: DicInfoVO[];
  };

  type IResultListKeyValueIntegerString = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: KeyValueIntegerString[];
  };

  type IResultListKeyValueLongString = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: KeyValueLongString[];
  };

  type IResultListOrgDeptInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: OrgDeptInfo[];
  };

  type IResultListOrgUserInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: OrgUserInfo[];
  };

  type IResultListPagePermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: PagePermission[];
  };

  type IResultListPagePermissionVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: PagePermissionVO[];
  };

  type IResultListRole = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Role[];
  };

  type IResultListRoleVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: RoleVO[];
  };

  type IResultListString = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: string[];
  };

  type IResultListSystemConfig = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: SystemConfig[];
  };

  type IResultListTreeInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: TreeInfoVO[];
  };

  type IResultListTreeMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: TreeMain[];
  };

  type IResultListWorkstationDeviceTreeDTO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: WorkstationDeviceTreeDTO[];
  };

  type IResultLoginLog = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: LoginLog;
  };

  type IResultMapStringListUserDepInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Record<string, any>;
  };

  type IResultMapStringLong = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Record<string, any>;
  };

  type IResultMapStringObject = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Record<string, any>;
  };

  type IResultMsgUserConfigVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: MsgUserConfigVO;
  };

  type IResultNewScreenData = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: NewScreenData;
  };

  type IResultNoticeInfoEditVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: NoticeInfoEditVO;
  };

  type IResultNoticeInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: NoticeInfoVO;
  };

  type IResultNotifyInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: NotifyInfo;
  };

  type IResultObject = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Record<string, any>;
  };

  type IResultOrgDeptInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: OrgDeptInfo;
  };

  type IResultOrgDeptInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: OrgDeptInfoVO;
  };

  type IResultOrgUserDeptInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: OrgUserDeptInfo;
  };

  type IResultOrgUserDeptInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: OrgUserDeptInfoVO;
  };

  type IResultOrgUserInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: OrgUserInfo;
  };

  type IResultOrgUserInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: OrgUserInfoVO;
  };

  type IResultPagePermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: PagePermission;
  };

  type IResultRole = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: Role;
  };

  type IResultSampleInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: SampleInfo;
  };

  type IResultSampleInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: SampleInfoVO;
  };

  type IResultScreenData = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: ScreenData;
  };

  type IResultSendRecord = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: SendRecord;
  };

  type IResultSetPagePermission = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: PagePermission[];
  };

  type IResultStandardBasicInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicInfo;
  };

  type IResultStandardBasicInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicInfoVO;
  };

  type IResultStandardBasicInstrumentInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicInstrumentInfo;
  };

  type IResultStandardBasicInstrumentInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicInstrumentInfoVO;
  };

  type IResultStandardBasicModelParam = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicModelParam;
  };

  type IResultStandardBasicModelParamVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicModelParamVO;
  };

  type IResultStandardBasicProjectInstrument = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicProjectInstrument;
  };

  type IResultStandardBasicProjectInstrumentVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicProjectInstrumentVO;
  };

  type IResultStandardBasicProjectParam = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicProjectParam;
  };

  type IResultStandardBasicProjectParamVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: StandardBasicProjectParamVO;
  };

  type IResultString = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: string;
  };

  type IResultSystemConfig = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: SystemConfig;
  };

  type IResultTreeMain = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: TreeMain;
  };

  type IResultUserMsgCount = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: UserMsgCount;
  };

  type IResultUserTableColumnInfo = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: UserTableColumnInfo;
  };

  type IResultUserTableColumnInfoVO = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    data?: UserTableColumnInfoVO;
  };

  type IResultVoid = {
    /** 成功标志 */
    success?: boolean;
    /** 返回消息 */
    message?: string;
    /** 状态码 */
    status?: number;
    /** 时间戳 */
    timestamp?: number;
    /** 结果对象 */
    data?: Record<string, any>;
  };

  type ItemStyle = {
    color?: string;
  };

  type IUser = {
    /** id */
    id?: number;
    /** 用户登录名 */
    username?: string;
    /** 密码 */
    password?: string;
    /** 姓名 */
    fullName?: string;
    /** 用户类型 */
    userType?: number;
    /** 状态 */
    status?: number;
    /** 客户端类型 */
    clientType?: number;
    /** 角色 */
    roles?: string[];
    /** 角色id */
    roleIds?: number[];
    /** 前端页面权限 */
    pages?: string[];
    /** 前端页面权限id */
    pageIds?: number[];
    /** 后端接口权限 */
    permissions?: string[];
    /** 后端接口权限id */
    permissionIds?: number[];
    /** 扩展信息1 */
    extend1?: string;
    /** 扩展信息2 */
    extend2?: string;
    /** 扩展信息3 */
    extend3?: string;
    /** 扩展数据 */
    extendData?: { strictMode?: boolean; empty?: boolean };
  };

  type IUserQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 用户名 */
    username?: string;
    /** 用户昵称 */
    fullName?: string;
    /** 用户状态 */
    state?: number;
    /** 用户类型 */
    userType?: number;
    /** 手机号码 */
    phone?: string;
    /** 电子邮箱 */
    email?: string;
    orderBy?: string;
    /** 用户id集合 */
    userIds?: number[];
    /** 用户角色id集合，包含任一角色的用户 */
    roleIds?: number[];
    /** 用户角色code集合，包含任一角色的用户 */
    roleCodes?: string[];
    /** 用户部门id集合，包含任一部门的用户 */
    deptIds?: number[];
    /** 用户部门code集合，包含任一部门的用户 */
    deptCodes?: string[];
  };

  type JoinInfo = {
    id1: string;
    id2: string;
    createTime?: string;
    joinCode: number;
  };

  type JSONObject = {
    strictMode?: boolean;
    empty?: boolean;
  };

  type JzReportInfo = {
    createTime?: string;
    id: number;
    /** 原始记录文件id */
    originalRecordFileId?: number;
    /** 报告文件id */
    reportFileId?: number;
    /** 报告编号 */
    reportNumber?: string;
    /** 样品id */
    sampleId?: number;
    /** 任务id */
    taskId?: number;
    /** 是否合格 */
    tfQualified?: string;
    updateTime?: string;
  };

  type JzReportInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    createTime?: string;
    id?: number;
    /** 原始记录文件id */
    originalRecordFileId?: number;
    /** 报告文件id */
    reportFileId?: number;
    /** 报告编号 */
    reportNumber?: string;
    /** 样品id */
    sampleId?: number;
    /** 任务id */
    taskId?: number;
    /** 是否合格 */
    tfQualified?: string;
    updateTime?: string;
    /** 样品名称 */
    sampleName?: string;
    /** 检测级别 */
    testLevel?: string;
    /** 任务编号 */
    taskNumber?: string;
  };

  type JzReportInfoVO = {
    createTime?: string;
    id: number;
    /** 原始记录文件id */
    originalRecordFileId?: number;
    /** 报告文件id */
    reportFileId?: number;
    /** 报告编号 */
    reportNumber?: string;
    /** 样品id */
    sampleId?: number;
    /** 任务id */
    taskId?: number;
    /** 是否合格 */
    tfQualified?: string;
    updateTime?: string;
    originalRecordFileInfo?: FileInfo;
    reportFileInfo?: FileInfo;
    /** 样品名称 */
    sampleName?: string;
    /** 检测级别 */
    testLevel?: string;
    /** 任务编号 */
    taskNumber?: string;
  };

  type JzTaskArrangementInfo = {
    /** 设备id */
    equipmentId?: number;
    /** 实验标准id */
    gwBzId?: string;
    id: number;
    /** 测试人员id */
    testUserId?: number;
    /** 工位id */
    workstationId?: number;
    /** 任务id */
    taskId?: number;
    modelIds?: string;
    /** 实验标准名称 */
    gwBzName?: string;
  };

  type JzTaskArrangementInfoVO = {
    /** 设备id */
    equipmentId?: number;
    /** 实验标准id */
    gwBzId?: string;
    id: number;
    /** 测试人员id */
    testUserId?: number;
    /** 工位id */
    workstationId?: number;
    /** 任务id */
    taskId?: number;
    modelIds?: string;
    /** 实验标准名称 */
    gwBzName?: string;
    instrumentNewInfo?: InstrumentNewInfo;
    /** 设备名称 */
    equipmentName?: string;
    /** 测试人员姓名 */
    testUserFullName?: string;
    /** 工位名称 */
    workstationName?: string;
  };

  type JzTaskInfo = {
    /** 创建时间 */
    createTime?: string;
    /** 结束时间 */
    endDate?: string;
    id: number;
    /** 样品id */
    sampleId?: number;
    /** 开始时间 */
    startDate?: string;
    /** 任务生成时间 */
    taskCreationTime?: string;
    /** 任务编号 */
    taskNumber?: string;
    /** 任务状态（任务待检、任务在检、任务检毕） */
    taskStatus?: string;
    /** 检测级别 */
    testLevel?: string;
    /** 更新时间 */
    updateTime?: string;
    jzTaskArrangementInfoList?: JzTaskArrangementInfo[];
  };

  type JzTaskInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 创建时间 */
    createTime?: string;
    /** 结束时间 */
    endDate?: string;
    id?: number;
    /** 样品id */
    sampleId?: number;
    /** 开始时间 */
    startDate?: string;
    /** 任务生成时间 */
    taskCreationTime?: string;
    /** 任务编号 */
    taskNumber?: string;
    /** 任务状态（任务待检、任务在检、任务检毕） */
    taskStatus?: string;
    /** 检测级别 */
    testLevel?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type JzTaskInfoVO = {
    /** 创建时间 */
    createTime?: string;
    /** 结束时间 */
    endDate?: string;
    id: number;
    /** 样品id */
    sampleId?: number;
    /** 开始时间 */
    startDate?: string;
    /** 任务生成时间 */
    taskCreationTime?: string;
    /** 任务编号 */
    taskNumber?: string;
    /** 任务状态（任务待检、任务在检、任务检毕） */
    taskStatus?: string;
    /** 检测级别 */
    testLevel?: string;
    /** 更新时间 */
    updateTime?: string;
    jzTaskArrangementInfoList?: JzTaskArrangementInfo[];
    jzTaskArrangementInfoLists?: JzTaskArrangementInfoVO[];
    sampleInfo?: SampleInfo;
    /** 关联样品信息名称 */
    sampleName?: string;
    /** 详情：工位信息 */
    buInstrumentWorkstationInfoLists?: BuInstrumentWorkstationInfo[];
    /** 详情：设备信息 */
    instrumentNewInfoLists?: InstrumentNewInfoVO[];
    /** 详情：人员信息 */
    orgUserInfoLists?: OrgUserInfo[];
    /** 详情：工单详情 */
    jzTaskWorkOrderInfoLists?: JzTaskWorkOrderInfo[];
    findStandardBasicInstrumentInfoVO?: StandardBasicInstrumentInfoVO;
  };

  type JzTaskInspectionItemInfo = {
    /** 创建时间 */
    createTime?: string;
    id: number;
    /** 检毕时间 */
    inspectionCompletionTime?: string;
    /** 检项编号 */
    inspectionItemNumber?: string;
    /** 关联样品Id */
    sampleId?: number;
    /** 关联任务id */
    taskId?: number;
    /** 检测结果 */
    testResults?: string;
    /** 检测标准 */
    testStandard?: string;
    /** 是否合格 */
    tfQualified?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 检测人员 */
    userId?: number;
    /** 关联工单id */
    workOrderId?: number;
    /** 检测项目名称 */
    testName?: string;
    /** 数据采集方式 */
    dataCollectionMethod?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    paramType?: string;
    /** 参数公式 */
    paramFormula?: string;
    /** 参数定义值，比如x1 */
    paramDefinitionValue?: string;
    /** 单位 */
    unit?: string;
    /** 实验项目 */
    experimentalProject?: string;
    fileId?: number;
    sourceId?: number;
  };

  type JzTaskInspectionItemInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 创建时间 */
    createTime?: string;
    id?: number;
    /** 检项编号 */
    inspectionItemNumber?: string;
    /** 关联样品Id */
    sampleId?: number;
    /** 关联任务id */
    taskId?: number;
    /** 检测结果 */
    testResults?: string;
    /** 检测标准 */
    testStandard?: string;
    /** 是否合格 */
    tfQualified?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 检测人员 */
    userId?: number;
    /** 关联工单id */
    workOrderId?: number;
    /** 实验项目 */
    experimentalProject?: string;
    /** 小于检毕结束时间 */
    inspectionCompletionTimeEnd?: string;
    /** 大于检毕开始时间 */
    inspectionCompletionTimeStart?: string;
  };

  type JzTaskInspectionItemInfoVO = {
    /** 创建时间 */
    createTime?: string;
    id: number;
    /** 检毕时间 */
    inspectionCompletionTime?: string;
    /** 检项编号 */
    inspectionItemNumber?: string;
    /** 关联样品Id */
    sampleId?: number;
    /** 关联任务id */
    taskId?: number;
    /** 检测结果 */
    testResults?: string;
    /** 检测标准 */
    testStandard?: string;
    /** 是否合格 */
    tfQualified?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 检测人员 */
    userId?: number;
    /** 关联工单id */
    workOrderId?: number;
    /** 检测项目名称 */
    testName?: string;
    /** 数据采集方式 */
    dataCollectionMethod?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    paramType?: string;
    /** 参数公式 */
    paramFormula?: string;
    /** 参数定义值，比如x1 */
    paramDefinitionValue?: string;
    /** 单位 */
    unit?: string;
    /** 实验项目 */
    experimentalProject?: string;
    fileId?: number;
    sourceId?: number;
    jzTaskInfo?: JzTaskInfo;
    sampleInfo?: SampleInfo;
    fileInfo?: FileInfo;
    jzTaskWorkOrderInfo?: JzTaskWorkOrderInfo;
    userName?: string;
  };

  type JzTaskTestDataInfo = {
    /** 主键 ID */
    id: number;
    /** 样品名称 */
    sampleName?: string;
    /** 模式 */
    mode?: string;
    /** 测试项目 */
    testProject?: string;
    /** 参数 */
    parameter?: string;
    /** 单位 */
    unit?: string;
    /** 数值 */
    value?: string;
    /** 反馈信息 */
    backInfo?: string;
  };

  type JzTaskTestDataInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 主键 ID */
    id?: number;
    /** 样品名称 */
    sampleName?: string;
    /** 模式 */
    mode?: string;
    /** 测试项目 */
    testProject?: string;
    /** 参数 */
    parameter?: string;
    /** 单位 */
    unit?: string;
    /** 数值 */
    value?: string;
    /** 反馈信息 */
    backInfo?: string;
  };

  type JzTaskTestDataInfoVO = {
    /** 主键 ID */
    id: number;
    /** 样品名称 */
    sampleName?: string;
    /** 模式 */
    mode?: string;
    /** 测试项目 */
    testProject?: string;
    /** 参数 */
    parameter?: string;
    /** 单位 */
    unit?: string;
    /** 数值 */
    value?: string;
    /** 反馈信息 */
    backInfo?: string;
  };

  type JzTaskWorkOrderInfo = {
    /** 创建时间 */
    createTime?: string;
    /** 结束时间 */
    endDate?: string;
    id: number;
    /** 检毕时间 */
    inspectionCompletionTime?: string;
    /** 关联任务 */
    sampleId?: number;
    /** 开始时间 */
    startDate?: string;
    /** 关联样品 */
    taskId?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 工单编号 */
    workOrderNumber?: string;
    /** 设备id */
    equipmentId?: number;
    /** 实验标准id */
    gwBzId?: string;
    /** 测试人员id */
    testUserId?: number;
    /** 工位id */
    workstationId?: number;
    /** 工单状态 */
    statusInfo?: string;
    /** 关联数据项更新 */
    jzTaskInspectionItemInfosUpdate?: JzTaskInspectionItemInfo[];
    /** 检测标准名称 */
    gwBzName?: string;
  };

  type JzTaskWorkOrderInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 创建时间 */
    createTime?: string;
    /** 结束时间 */
    endDate?: string;
    id?: number;
    /** 检毕时间 */
    inspectionCompletionTime?: string;
    /** 关联任务 */
    sampleId?: number;
    /** 开始时间 */
    startDate?: string;
    /** 关联样品 */
    taskId?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 工单编号 */
    workOrderNumber?: string;
    /** 实验项目 */
    gwBzName?: string;
    /** 工单状态 */
    statusInfo?: string;
  };

  type JzTaskWorkOrderInfoVO = {
    /** 创建时间 */
    createTime?: string;
    /** 结束时间 */
    endDate?: string;
    id: number;
    /** 检毕时间 */
    inspectionCompletionTime?: string;
    /** 关联任务 */
    sampleId?: number;
    /** 开始时间 */
    startDate?: string;
    /** 关联样品 */
    taskId?: number;
    /** 更新时间 */
    updateTime?: string;
    /** 工单编号 */
    workOrderNumber?: string;
    /** 设备id */
    equipmentId?: number;
    /** 实验标准id */
    gwBzId?: string;
    /** 测试人员id */
    testUserId?: number;
    /** 工位id */
    workstationId?: number;
    /** 工单状态 */
    statusInfo?: string;
    /** 关联数据项更新 */
    jzTaskInspectionItemInfosUpdate?: JzTaskInspectionItemInfo[];
    /** 检测标准名称 */
    gwBzName?: string;
    /** 设备名称 */
    equipmentName?: string;
    /** 测试人员姓名 */
    testUserFullName?: string;
    /** 工位名称 */
    workstationName?: string;
    jzTaskInfo?: JzTaskInfo;
    sampleInfo?: SampleInfo;
    /** 关联子项信息 */
    jzTaskInspectionItemInfos?: JzTaskInspectionItemInfoVO[];
  };

  type JzTaskWorkOrderModelParam = {
    /** 主键ID */
    id: number;
    /** 检项 */
    testItem?: string;
    /** 检项参数（英文，唯一） */
    paramKey?: string;
    /** 单位 */
    unit?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    judgeType?: string;
    /** 合格标准（描述合格的参数） */
    qualifiedStandard?: string;
    /** 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) */
    judgeFormula?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 采集方式 */
    collectionMethod?: string;
    orderId?: number;
    testResult?: string;
    standardBasicInstrumentId?: number;
    tfQualified?: string;
    sourceId?: number;
    sourceProjectParamId?: number;
    taskId?: number;
    instrumentId?: number;
  };

  type JzTaskWorkOrderModelParamQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 主键ID */
    id?: number;
    /** 检项 */
    testItem?: string;
    /** 检项参数（英文，唯一） */
    paramKey?: string;
    /** 单位 */
    unit?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    judgeType?: string;
    /** 合格标准（描述合格的参数） */
    qualifiedStandard?: string;
    /** 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) */
    judgeFormula?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 采集方式 */
    collectionMethod?: string;
    orderId?: number;
  };

  type JzTaskWorkOrderModelParamVO = {
    /** 主键ID */
    id: number;
    /** 检项 */
    testItem?: string;
    /** 检项参数（英文，唯一） */
    paramKey?: string;
    /** 单位 */
    unit?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    judgeType?: string;
    /** 合格标准（描述合格的参数） */
    qualifiedStandard?: string;
    /** 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) */
    judgeFormula?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 采集方式 */
    collectionMethod?: string;
    orderId?: number;
    testResult?: string;
    standardBasicInstrumentId?: number;
    tfQualified?: string;
    sourceId?: number;
    sourceProjectParamId?: number;
    taskId?: number;
    instrumentId?: number;
  };

  type KeyValueIntegerString = {
    /** 键 */
    key?: number;
    /** 值 */
    value?: string;
  };

  type KeyValueLongString = {
    /** 键 */
    key?: number;
    /** 值 */
    value?: string;
  };

  type KickUserDTO = {
    /** 用户id */
    id: number;
    /** 说明 */
    explain?: string;
  };

  type LoginDTO = {
    /** 登录名 */
    username: string;
    /** 密码 */
    password: string;
    /** 保存登录，默认2小时，保存后7天 */
    saveLogin?: boolean;
    /** 客户端类型 */
    clientType?: number;
    /** 验证码 */
    code?: string;
    /** 滑动验证信息 */
    captchaVerification?: string;
    /** 其他信息 */
    otherData?: Record<string, any>;
  };

  type LoginLog = {
    id: number;
    /** 用户名 */
    username: string;
    /** 用户id */
    userId: number;
    /** 用户姓名 */
    fullName?: string;
    /** 用户类型@dic<userType> */
    userType?: number;
    /** 客户端类型@dic<clientType> */
    clientType?: number;
    /** 登录时间 */
    loginTime?: string;
    /** 登录城市 */
    loginCity?: string;
    /** 登录ip */
    loginIp?: string;
    /** 操作系统 */
    systemName?: string;
    /** 登录浏览器 */
    browser?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** token */
    token?: string;
    /** 失效时间 */
    expireTime?: string;
    /** 是否在线 */
    online?: boolean;
    /** 登出时间 */
    logoutTime?: string;
    /** 登出方式@enum<1:用户正常登出,2:token到期,3:管理员踢出,4:其他地方登陆,5:其他> */
    logoutType?: number;
    /** 登出说明 */
    logoutExplain?: string;
    /** 扩展预留字段 */
    extend?: string;
    systemBrowserInfo?: string;
  };

  type LoginLogQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 用户名 */
    username?: string;
    /** 姓名 */
    fullName?: string;
    /** 用户id */
    userId?: number;
    /** 用户类型 */
    userType?: number;
    /** 客户端类型 */
    clientType?: number;
    /** 登录地址 */
    loginCity?: string;
    /** 登录ip */
    loginIp?: string;
    /** 操作系统 */
    system?: string;
    /** 登录浏览器 */
    browser?: string;
    /** 在线 */
    online?: number;
    /** 在线 */
    onlineFlag?: boolean;
    /** 登出方式 */
    logoutType?: number;
    /** 是否根据在线状态排序 */
    onlineSort?: number;
    /** 是否根据登录时间排序 */
    loginTimeSort?: number;
    /** 登录开始时间 */
    loginBeginTime?: string;
    /** 登录结束时间 */
    loginEndTime?: string;
    /** 登出开始时间 */
    outBeginTime?: string;
    /** 登出结束时间 */
    outEndTime?: string;
  };

  type ManyToManyDTOLong = {
    /** 主数据id */
    id: number;
    /** 关联数据id，多个英文逗号（,）隔开 */
    manyIds: string;
  };

  type MidCenter = {
    threeBeforeFiveDay?: number;
    overFiveDay?: number;
  };

  type MidLeft = {
    allOfSample?: number;
    waitingToCheck?: number;
    checking?: number;
    checkOver?: number;
  };

  type ModifyRecipientDTO = {
    id: number;
    /** 接收人信息 */
    receiveInfoList: NoticeReceiveInfo[];
  };

  type Monitor = {
    title?: string;
    monitorData?: MonitorData[];
  };

  type MonitorData = {
    id?: number;
    cameraCode?: string;
    cameraName?: string;
    workstation?: string;
    flvUrl?: string;
    tfTest?: boolean;
  };

  type MsgUserConfigVO = {
    /** 通知模块配置 */
    notifyList?: SendMsgConfigDTO[];
    /** 待办模块配置 */
    backlogList?: BacklogMainVO[];
    /** 钉钉通知url */
    dingDingUrl?: string;
    /** 钉钉通知secret */
    dingDingSecret?: string;
  };

  type NewScreenData = {
    top?: Record<string, any>;
    left?: Record<string, any>;
    center?: Record<string, any>;
    right?: JzReportInfoVO[];
    bottom?: TaskDto[];
  };

  type NoticeInfo = {
    id: number;
    /** 公告状态 */
    state?: number;
    createTime?: string;
    updateTime?: string;
    /** 创建人id */
    createUserId?: number;
    /** 修改人id */
    updateUserId?: number;
    /** 公告类型 */
    noticeType: string;
    /** 是否定时发布 */
    timingRelease: number;
    /** 发布时间 */
    releaseTime?: string;
    /** 截止时间 */
    deadlineTime: string;
    /** 公告标题 */
    noticeTitle: string;
    /** 公告内容 */
    noticeBody: string;
    /** 公告内容类型 */
    noticeBodyType: string;
    /** 通知方式 */
    notificationMethod?: string;
  };

  type NoticeInfoEditVO = {
    id: number;
    /** 公告状态 */
    state?: number;
    createTime?: string;
    updateTime?: string;
    /** 创建人id */
    createUserId?: number;
    /** 修改人id */
    updateUserId?: number;
    /** 公告类型 */
    noticeType: string;
    /** 是否定时发布 */
    timingRelease: number;
    /** 发布时间 */
    releaseTime?: string;
    /** 截止时间 */
    deadlineTime: string;
    /** 公告标题 */
    noticeTitle: string;
    /** 公告内容 */
    noticeBody: string;
    /** 公告内容类型 */
    noticeBodyType: string;
    /** 通知方式 */
    notificationMethod?: string;
    /** 公告接收者列表 */
    recipientList?: KeyValueIntegerString[];
  };

  type NoticeInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    id?: number;
    /** 公告状态 */
    state?: number;
    /** 创建人id */
    createUserId?: number;
    /** 修改人id */
    updateUserId?: number;
    /** 是否已读 */
    read?: number;
    /** 公告类型 */
    noticeType?: string;
    /** 是否定时发布 */
    timingRelease?: number;
    /** 公告标题 */
    noticeTitle?: string;
    /** 公告内容类型 */
    noticeBodyType?: string;
    /** 发布时间（开始） */
    beginTime?: string;
    /** 发布时间（结束） */
    endTime?: string;
    /** 创建时间（开始） */
    createBeginTime?: string;
    /** 创建时间（结束） */
    createEndTime?: string;
  };

  type NoticeInfoVO = {
    id: number;
    /** 公告状态 */
    state?: number;
    createTime?: string;
    updateTime?: string;
    /** 创建人id */
    createUserId?: number;
    /** 修改人id */
    updateUserId?: number;
    /** 公告类型 */
    noticeType: string;
    /** 是否定时发布 */
    timingRelease: number;
    /** 发布时间 */
    releaseTime?: string;
    /** 截止时间 */
    deadlineTime: string;
    /** 公告标题 */
    noticeTitle: string;
    /** 公告内容 */
    noticeBody: string;
    /** 公告内容类型 */
    noticeBodyType: string;
    /** 通知方式 */
    notificationMethod?: string;
    noticeId?: number;
    /** 是否已读 */
    read?: number;
    /** 发布人 */
    createUserName?: string;
  };

  type NoticeReceiveInfo = {
    id: number;
    /** 公告id */
    noticeId: number;
    /** 接收类型 */
    receiveType: number;
    /** 接收者id */
    receiveId?: number;
    createTime?: string;
    updateTime?: string;
  };

  type NotifyInfo = {
    id: number;
    /** 用户id */
    userId: number;
    /** 通知状态（0未读，1已读） */
    state: number;
    /** 通知内容 */
    notifyBody: string;
    createTime?: string;
    updateTime?: string;
    /** 通知类型 */
    notifyType: string;
  };

  type NotifyInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 用户id */
    userId?: number;
    /** 通知状态（0未读，1已读） */
    state?: number;
    /** 通知内容 */
    notifyBody?: string;
    createTime?: string;
    updateTime?: string;
    /** 通知类型 */
    notifyType?: string;
  };

  type ObsUrlQuery = {
    /** 对象编号 */
    objectKey: string;
    /** 有效时长 */
    expireSeconds?: number;
  };

  type OrderByDTO = {
    /** 排序字段优先级顺序 */
    sort?: number;
    /** 排序字段 */
    field?: string;
    /** 排序类型，0顺序，其他倒序 */
    orderByType?: number;
  };

  type OrgDeptInfo = {
    id: number;
    pid?: number;
    /** 部门名称 */
    deptName?: string;
    deptExplain?: string;
    createTime: string;
    updateTime?: string;
    /** 部门主任userid */
    executiveUserId?: number;
    /** 部门id（质量平台） */
    syncIdStr?: string;
    /** 部门编码（质量平台） */
    syncCode?: string;
  };

  type OrgDeptInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    pid?: number;
    /** 部门名称 */
    deptName?: string;
    deptExplain?: string;
    createTime?: string;
    updateTime?: string;
    /** 部门主任userid */
    executiveUserId?: number;
  };

  type OrgDeptInfoVO = {
    id: number;
    pid?: number;
    /** 部门名称 */
    deptName?: string;
    deptExplain?: string;
    createTime: string;
    updateTime?: string;
    /** 部门主任userid */
    executiveUserId?: number;
    /** 部门id（质量平台） */
    syncIdStr?: string;
    /** 部门编码（质量平台） */
    syncCode?: string;
    child?: OrgDeptInfo[];
  };

  type OrgUserDeptInfo = {
    id: number;
    userId?: number;
    deptId?: number;
  };

  type OrgUserDeptInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    userId?: number;
    deptId?: number;
  };

  type OrgUserDeptInfoVO = {
    id: number;
    userId?: number;
    deptId?: number;
  };

  type OrgUserInfo = {
    /** 主键 */
    id: number;
    /** 用户名 */
    userName: string;
    /** 密码 */
    passWord?: string;
    /** 真实姓名 */
    fullName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 用户电话 */
    phone?: string;
    /** 邮件 */
    email?: string;
    enable: number;
    tfPathSign?: boolean;
    photoUrl?: string;
    tfTestMain?: boolean;
    deptNames?: string[];
    roleNames?: string[];
    /** 所属专业 */
    major?: string;
    empId?: string;
  };

  type OrgUserInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 主键 */
    id?: number;
    /** 用户名 */
    userName?: string;
    /** 密码 */
    passWord?: string;
    /** 真实姓名 */
    fullName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 用户电话 */
    phone?: string;
    /** 邮件 */
    email?: string;
    /** 部门id */
    deptId?: string;
    /** 角色id */
    roleId?: string;
    roleNewId?: string;
    deptNewId?: string;
    enable?: number;
  };

  type OrgUserInfoVO = {
    /** 主键 */
    id: number;
    /** 用户名 */
    userName: string;
    /** 密码 */
    passWord?: string;
    /** 真实姓名 */
    fullName?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 用户电话 */
    phone?: string;
    /** 邮件 */
    email?: string;
    enable: number;
    tfPathSign?: boolean;
    photoUrl?: string;
    tfTestMain?: boolean;
    deptNames?: string[];
    roleNames?: string[];
    /** 所属专业 */
    major?: string;
    empId?: string;
    userSignInfo?: string;
  };

  type PagePermission = {
    id: number;
    /** 前端页面权限编码 */
    permissionCode: string;
    /** 前端页面权限名称 */
    permissionName: string;
    /** 父级id */
    pid: number;
    /** 排序号 */
    sort: number;
    /** 类型@enum<1:端,2:模块,3:菜单,4:按钮> */
    type: number;
    /** 组件地址 */
    componentUrl?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 状态@enum<0:不需要验证,1:需要登录,2:需要验证> */
    status?: number;
    /** 扩展预留字段 */
    extend?: string;
    /** 后端接口权限编码集合 */
    apiPermissions?: string[];
  };

  type PagePermissionApi = {
    id?: number;
    /** 前端页面权限key */
    pageCode?: string;
    /** 接口key */
    apiCode?: string;
    /** 创建时间 */
    createTime?: string;
  };

  type PagePermissionQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 小于 */
    le?: { strictMode?: boolean; empty?: boolean };
    ge?: JSONObject;
    eq?: JSONObject;
    like?: JSONObject;
    ne?: JSONObject;
    in?: JSONObject;
    isNull?: JSONObject;
    /** 排序对象集合 */
    order?: OrderByDTO[];
    /** 授权标识 */
    permissionCode?: string;
    /** 名称 */
    permissionName?: string;
    pid?: string;
    /** 排序号 */
    sort?: number;
    /** url */
    url?: string;
    /** 请求方式 */
    requestWay?: string;
    /** 1路由，2操作 */
    type?: number;
    /** 组件地址 */
    componentUrl?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    orderBy?: OrderByDTO[];
  };

  type PagePermissionTreeQuery = {
    /** 页面权限名称 */
    name?: string;
    /** 查询页面权限绑定的api权限信息 */
    queryApi?: number;
  };

  type PagePermissionVO = {
    id: number;
    /** 前端页面权限编码 */
    permissionCode: string;
    /** 前端页面权限名称 */
    permissionName: string;
    /** 父级id */
    pid: number;
    /** 排序号 */
    sort: number;
    /** 类型@enum<1:端,2:模块,3:菜单,4:按钮> */
    type: number;
    /** 组件地址 */
    componentUrl?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 状态@enum<0:不需要验证,1:需要登录,2:需要验证> */
    status?: number;
    /** 扩展预留字段 */
    extend?: string;
    /** 后端接口权限编码集合 */
    apiPermissions?: string[];
    child?: PagePermissionVO[];
    apiList?: ApiPermission[];
  };

  type RemoveTreeDTO = {
    /** id */
    id: string;
    /** code */
    code: string;
  };

  type ReportData = {
    value?: number;
    name?: string;
    itemStyle?: ItemStyle;
  };

  type ReportDataSource = {
    index?: string;
    name?: string;
    taskName?: string;
    reportTime?: string;
  };

  type ReportInfo = {
    title?: string;
    reportData?: ReportData[];
    subjectArr?: SubjectArr[];
    reportDataSource?: ReportDataSource[];
  };

  type resetPasswordVo = {
    /** 当前登录用户密码 */
    oldPassword?: string;
    /** 重置用户id */
    userId?: number;
  };

  type Role = {
    id: number;
    /** 父级角色 */
    pid: number;
    /** 权限 */
    permissions?: string;
    /** 角色名称 */
    roleName: string;
    /** 角色编码 */
    roleCode: string;
    /** 角色类型@enum<1:系统角色,2:普通角色> */
    roleType: number;
    /** 角色排序 */
    roleSort?: number;
    /** 状态 */
    state?: number;
    /** 角色说明 */
    roleExplain?: string;
    /** 扩展预留字段 */
    extend?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
  };

  type RolesDto = {
    roles?: string[];
  };

  type RoleVO = {
    id: number;
    /** 父级角色 */
    pid: number;
    /** 权限 */
    permissions?: string;
    /** 角色名称 */
    roleName: string;
    /** 角色编码 */
    roleCode: string;
    /** 角色类型@enum<1:系统角色,2:普通角色> */
    roleType: number;
    /** 角色排序 */
    roleSort?: number;
    /** 状态 */
    state?: number;
    /** 角色说明 */
    roleExplain?: string;
    /** 扩展预留字段 */
    extend?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 下级角色 */
    child?: RoleVO[];
  };

  type SampleInfo = {
    id?: number;
    /** 样品编号 */
    sampleNumber?: string;
    /** 样品名称 */
    sampleName?: string;
    /** 样品规格 */
    sampleModel?: string;
    /** 样品型号 */
    jzSampleModel?: string;
    /** 样品所属专业 */
    sampleProfession?: string;
    /** 收样时间 */
    sampleReceiptTime?: string;
    /** 样品备注 */
    sampleRemark?: string;
    /** 委托类型 */
    entrustType?: number;
    createTime?: string;
    updateTime?: string;
    serialNumber?: number;
    /** 盲样编号 */
    blindSampleNumber?: string;
    /** 二次盲样编号 */
    secondaryBlindSampleNumber?: string;
    /** 样品所属专业 */
    sampleMajor?: string;
    /** 检测类型 */
    testType?: string;
    /** 任务状态 */
    taskStatus?: string;
    /** 报告状态 */
    reportStatus?: string;
    /** 样品铭牌图片id */
    nameplateFileId?: number;
    /** 样品图片id */
    samplePicId?: number;
    /** 样品状态 */
    status?: string;
    /** 返样状态 */
    backStatus?: string;
  };

  type SampleInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    ids?: number[];
    /** 样品编号 */
    sampleNumber?: string;
    /** 样品名称 */
    sampleName?: string;
    /** 盲样编号 */
    blindSampleNumber?: string;
    /** 样品型号 */
    sampleModel?: string;
    /** 样品所属专业 */
    sampleMajor?: string;
    /** 样品状态 */
    sampleStatus?: string;
    /** 样品序列号 */
    sampleSerialNumber?: string;
    /** 样品版本号 */
    sampleVersionNumber?: string;
    /** 样品类型 */
    sampleType?: string;
    /** 样品数量 */
    sampleNum?: number;
    /** 样品处理状态 */
    sampleProcessStatus?: string;
    managerId?: number;
    /** 关联的任务状态 */
    taskDeptStatusInfo?: string;
    /** 领取部门id */
    deptReceDepId?: number;
    /** 样品备注 */
    sampleRemark?: string;
    /** 状态 */
    statusInfo?: string;
    /** 关联委托书id */
    entrustId?: number;
    /** 关联委托书id */
    entrustIds?: number[];
    createTime?: string;
    taskId?: number;
    updateTime?: string;
    /** 查询类型 1 样品列表查询(默认)  2 审核样品详情查询 */
    queryType?: number;
    tfentrustIdIsNull?: boolean;
    /** 检测类型 */
    testType?: string;
    /** 小于收样结束时间 */
    sampleReceiptTimeEnd?: string;
    /** 大于收样开始时间 */
    sampleReceiptTimeStart?: string;
    /** 样品状态 */
    status?: string;
    /** 返样状态 */
    backStatus?: string;
  };

  type SampleInfoVO = {
    id?: number;
    /** 样品编号 */
    sampleNumber?: string;
    /** 样品名称 */
    sampleName?: string;
    /** 样品规格 */
    sampleModel?: string;
    /** 样品型号 */
    jzSampleModel?: string;
    /** 样品所属专业 */
    sampleProfession?: string;
    /** 收样时间 */
    sampleReceiptTime?: string;
    /** 样品备注 */
    sampleRemark?: string;
    /** 委托类型 */
    entrustType?: number;
    createTime?: string;
    updateTime?: string;
    serialNumber?: number;
    /** 盲样编号 */
    blindSampleNumber?: string;
    /** 二次盲样编号 */
    secondaryBlindSampleNumber?: string;
    /** 样品所属专业 */
    sampleMajor?: string;
    /** 检测类型 */
    testType?: string;
    /** 任务状态 */
    taskStatus?: string;
    /** 报告状态 */
    reportStatus?: string;
    /** 样品铭牌图片id */
    nameplateFileId?: number;
    /** 样品图片id */
    samplePicId?: number;
    /** 样品状态 */
    status?: string;
    /** 返样状态 */
    backStatus?: string;
    receivedeptId?: number;
    nameplateFileInfo?: FileInfo;
    samplePicInfo?: FileInfo;
    /** 检测任务状态 */
    jzTaskStatus?: string;
    /** 报告状态 */
    jzReportStatus?: string;
  };

  type SaveNoticeDTO = {
    id: number;
    /** 公告状态 */
    state?: number;
    createTime?: string;
    updateTime?: string;
    /** 创建人id */
    createUserId?: number;
    /** 修改人id */
    updateUserId?: number;
    /** 公告类型 */
    noticeType: string;
    /** 是否定时发布 */
    timingRelease: number;
    /** 发布时间 */
    releaseTime?: string;
    /** 截止时间 */
    deadlineTime: string;
    /** 公告标题 */
    noticeTitle: string;
    /** 公告内容 */
    noticeBody: string;
    /** 公告内容类型 */
    noticeBodyType: string;
    /** 通知方式 */
    notificationMethod?: string;
    /** 接收人信息 */
    receiveInfoList: KeyValueIntegerString[];
  };

  type ScreenData = {
    title?: string;
    reportInfo?: ReportInfo;
    taskSummary?: TaskSummary;
    monitor?: Monitor;
  };

  type SendMsgConfigDTO = {
    /** 发送消息方式 */
    sendType?: string;
    dicInfo?: DicInfoVO;
    /** 是否启用该消息发送方式 */
    enable?: boolean;
  };

  type SendRecord = {
    id: number;
    createTime?: string;
    /** 消息id */
    msgId: number;
    /** 消息标题 */
    msgTitle?: string;
    /** 消息内容类型 */
    msgBodyType?: number;
    /** 消息内容 */
    msgBody?: string;
    /** 消息类型 */
    msgType: number;
    /** 发送方式 */
    sendMethod: string;
    /** 发送结果 */
    sendResult: number;
    /** 发送结果说明 */
    sendResultExplain: string;
    /** 接收人 */
    userId: number;
    /** 接收地址 */
    receiveAddress?: string;
  };

  type SendRecordQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    createTime?: string;
    /** 消息id */
    msgId?: number;
    /** 消息内容 */
    msgBody?: string;
    /** 消息类型 */
    msgType?: number;
    /** 发送方式 */
    sendMethod?: string;
    /** 发送结果 */
    sendResult?: number;
    /** 发送结果说明 */
    sendResultExplain?: string;
    /** 接收人 */
    userId?: number;
    /** 接收地址 */
    receiveAddress?: string;
  };

  type SetValueDTO = {
    id: number;
    value?: string;
  };

  type StandardBasicInfo = {
    id: number;
    /** 集成装置名称 */
    integratedDeviceName?: string;
  };

  type StandardBasicInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 集成装置名称 */
    integratedDeviceName?: string;
  };

  type StandardBasicInfoVO = {
    id: number;
    /** 集成装置名称 */
    integratedDeviceName?: string;
  };

  type StandardBasicInstrumentInfo = {
    id: number;
    /** 集成设备id */
    integratedDeviceId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 检测能力级别（A\B\C） */
    testCapabilityLevel?: string;
    /** 设备关键参数和要求 */
    keyParamRequirement?: string;
    /** 标准要求 */
    standardRequirement?: string;
    createTime?: string;
    updateTime?: string;
    /** 样品规格 */
    sampleSpecifications?: string;
    /** 单位 */
    unit?: string;
    projectIds?: number[];
    sampleName?: string;
    sampleModel?: string;
    createUserId?: number;
    createUserFullName?: string;
    updateUserId?: number;
    updateUserFullName?: string;
    standardBasicModelParams?: StandardBasicModelParam[];
    /** 报告模板文件ID */
    reportTemplateFileId?: number;
    /** 原始记录模板文件ID */
    originalRecordTemplateFileId?: number;
    reportTemplateFileInfo?: FileInfo;
    originalRecordTemplateFileInfo?: FileInfo;
    /** 报告模板自定义值 */
    reportTemplateValues?: string;
    /** 原始记录模板自定义值 */
    originalRecordTemplateValues?: string;
  };

  type StandardBasicInstrumentInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 集成设备id */
    integratedDeviceId?: number;
    /** 集成设备id */
    integratedDeviceIds?: number[];
    /** 项目名称 */
    projectName?: string;
    /** 检测能力级别（A\B\C） */
    testCapabilityLevel?: string;
    testCapabilityLevels?: string[];
    /** 仪器名称 */
    instrumentName?: string;
    /** 设备关键参数和要求 */
    keyParamRequirement?: string;
    /** 标准要求 */
    standardRequirement?: string;
    /** 型号 */
    sampleSpecifications?: string;
    /** 样品名称 */
    sampleName?: string;
    /** 样品模型 */
    sampleModel?: string;
    createTime?: string;
    updateTime?: string;
  };

  type standardBasicInstrumentInfoUploadOriginalRecordTemplateParams = {
    standardId: number;
  };

  type standardBasicInstrumentInfoUploadReportTemplateParams = {
    standardId: number;
  };

  type StandardBasicInstrumentInfoVO = {
    id: number;
    /** 集成设备id */
    integratedDeviceId?: number;
    /** 项目名称 */
    projectName?: string;
    /** 检测能力级别（A\B\C） */
    testCapabilityLevel?: string;
    /** 设备关键参数和要求 */
    keyParamRequirement?: string;
    /** 标准要求 */
    standardRequirement?: string;
    createTime?: string;
    updateTime?: string;
    /** 样品规格 */
    sampleSpecifications?: string;
    /** 单位 */
    unit?: string;
    projectIds?: number[];
    sampleName?: string;
    sampleModel?: string;
    createUserId?: number;
    createUserFullName?: string;
    updateUserId?: number;
    updateUserFullName?: string;
    standardBasicModelParams?: StandardBasicModelParam[];
    /** 报告模板文件ID */
    reportTemplateFileId?: number;
    /** 原始记录模板文件ID */
    originalRecordTemplateFileId?: number;
    reportTemplateFileInfo?: FileInfo;
    originalRecordTemplateFileInfo?: FileInfo;
    /** 报告模板自定义值 */
    reportTemplateValues?: string;
    /** 原始记录模板自定义值 */
    originalRecordTemplateValues?: string;
    standardBasicInfoName?: string;
    /** 仪器名称 */
    instrumentNames?: string[];
    instrumentNewIds?: number[];
    proName?: string[];
    standardBasicProjectInstrumentList?: StandardBasicProjectInstrument[];
    standardBasicModelParamList?: StandardBasicModelParam[];
  };

  type StandardBasicModelParam = {
    /** 主键ID */
    id: number;
    /** 检项 */
    testItem?: string;
    /** 检项参数（英文，唯一） */
    paramKey?: string;
    /** 单位 */
    unit?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    judgeType?: string;
    /** 合格标准（描述合格的参数） */
    qualifiedStandard?: string;
    /** 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) */
    judgeFormula?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 实验标准id */
    standardBasicInstrumentId?: number;
    standardBasicProjectParamId?: number;
    collectionMethod?: string;
  };

  type StandardBasicModelParamQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 主键ID */
    id?: number;
    /** 检项 */
    testItem?: string;
    /** 检项参数（英文，唯一） */
    paramKey?: string;
    /** 单位 */
    unit?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    judgeType?: string;
    /** 合格标准（描述合格的参数） */
    qualifiedStandard?: string;
    /** 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) */
    judgeFormula?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 实验标准id */
    standardBasicInstrumentId?: number;
  };

  type StandardBasicModelParamVO = {
    /** 主键ID */
    id: number;
    /** 检项 */
    testItem?: string;
    /** 检项参数（英文，唯一） */
    paramKey?: string;
    /** 单位 */
    unit?: string;
    /** 判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型） */
    judgeType?: string;
    /** 合格标准（描述合格的参数） */
    qualifiedStandard?: string;
    /** 判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =) */
    judgeFormula?: string;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 实验标准id */
    standardBasicInstrumentId?: number;
    standardBasicProjectParamId?: number;
    collectionMethod?: string;
  };

  type StandardBasicProjectInstrument = {
    /** 主键ID */
    id: number;
    /** 实验项目名称 */
    projectName?: string;
    instrumentId?: number;
    standardBasicInstrumentId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 创建人ID */
    createUserId?: number;
    /** 最后操作人ID */
    updateUserId?: number;
    createUserFullName?: string;
    updateUserFullName?: string;
    projectParams?: StandardBasicProjectParam[];
  };

  type StandardBasicProjectInstrumentQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 主键ID */
    id?: number;
    /** 实验项目名称 */
    projectName?: string;
    /** 仪器 */
    instrumentId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 创建人ID */
    createUserId?: number;
    /** 最后操作人ID */
    updateUserId?: number;
    createUserFullName?: string;
    updateUserFullName?: string;
  };

  type StandardBasicProjectInstrumentVO = {
    /** 主键ID */
    id: number;
    /** 实验项目名称 */
    projectName?: string;
    instrumentId?: number;
    standardBasicInstrumentId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 修改时间 */
    updateTime?: string;
    /** 创建人ID */
    createUserId?: number;
    /** 最后操作人ID */
    updateUserId?: number;
    createUserFullName?: string;
    updateUserFullName?: string;
    projectParams?: StandardBasicProjectParam[];
    instrumentNewInfo?: InstrumentNewInfo;
    standardBasicProjectInstrument?: StandardBasicInstrumentInfo;
  };

  type StandardBasicProjectParam = {
    /** 创建时间 */
    createTime?: string;
    /** 主键ID */
    id: number;
    /** 参数名称 */
    paramName: string;
    /** 参数类别 */
    paramType: string;
    /** 实验项目ID */
    standardBasicProjectId?: number;
    /** 修改时间 */
    updateTime?: string;
    /** 单位 */
    unit?: string;
    /** 采集方式 */
    collectionMethod?: string;
  };

  type StandardBasicProjectParamQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** 创建时间 */
    createTime?: string;
    /** 主键ID */
    id?: number;
    /** 参数名称 */
    paramName?: string;
    /** 参数类别 */
    paramType?: string;
    /** 实验项目ID */
    standardBasicProjectId?: number;
    standardBasicProjectIds?: number[];
    /** 修改时间 */
    updateTime?: string;
  };

  type StandardBasicProjectParamVO = {
    /** 创建时间 */
    createTime?: string;
    /** 主键ID */
    id: number;
    /** 参数名称 */
    paramName: string;
    /** 参数类别 */
    paramType: string;
    /** 实验项目ID */
    standardBasicProjectId?: number;
    /** 修改时间 */
    updateTime?: string;
    /** 单位 */
    unit?: string;
    /** 采集方式 */
    collectionMethod?: string;
    standardBasicModelParam?: StandardBasicModelParam;
  };

  type SubjectArr = {
    value?: number;
    name?: string;
    max?: number;
  };

  type SyncPageAndApiDTO = {
    /** 前端页面权限编码 */
    permissionCode: string;
    /** 前端页面权限名称 */
    permissionName: string;
    /** 1端，2模块，3菜单，4按钮 */
    type: number;
    child?: SyncPageAndApiDTO[];
    /** 后端接口权限编码集合 */
    apiList?: string[];
  };

  type SyncPermissionDTO = {
    /** 同步的服务名称 */
    serverName?: string;
    /** 同步类型，1自己，2指定服务，3全部 */
    syncType: number;
    /** 更新类型，1锁定的不更新，2强制更新所有 */
    updateType: number;
    /** 同步的服务swagger地址 */
    swaggerUrl?: string;
    /** token */
    token?: string;
    /** 自动同步权限的token */
    autoSyncApiToken?: string;
    /** 删除旧的权限 */
    removeOld?: boolean;
  };

  type SystemConfig = {
    id: number;
    /** 配置名称 */
    configName: string;
    /** 配置编码 */
    configCode: string;
    /** 配置值 */
    configValue: string;
    /** 配置类型@enum<1:int,2:long,3:string,4:date,5:bool,6:file,7:picture,8:colour,9:files,10:pictures,11:scopeDate> */
    configType: number;
    /** 配置环境@enum<1:后端应用环境> */
    configEnvironment: number;
    /** 配置说明 */
    configExplain?: string;
    /** 配置校验正则 */
    configCheckRegular?: string;
    /** 配置所属模块 */
    configModule: string;
    /** 排序号 */
    sortNo?: number;
    createTime?: string;
    updateTime?: string;
  };

  type SystemConfigQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 配置名称 */
    configName?: string;
    /** 配置编码 */
    configCode?: string;
    /** 配置值 */
    configValue?: string;
    /** 配置类型 */
    configType?: number;
    /** 配置说明 */
    configExplain?: string;
    /** 配置校验正则 */
    configCheckRegular?: string;
    /** 配置所属模块 */
    configModule?: string;
    timeSort0?: number;
  };

  type TaskCountData = {
    time?: string;
    count?: number;
  };

  type TaskDataSource = {
    index?: number;
    checkName?: string;
    checkType?: string;
    checkCode?: string;
    secondaryCheckCode?: string;
    checkTime?: string;
    createTime?: string;
  };

  type TaskDto = {
    taskNumber?: string;
    taskName?: string;
    secondaryBlindSampleNumber?: string;
    taskStartTime?: string;
    workStationName?: string;
    projectName?: string;
    testPersonName?: string;
    workdOrderStatus?: Record<string, any>;
    camera?: number;
  };

  type TaskListObj = {
    columns?: Columns[];
    dataSource?: Record<string, any>[];
  };

  type TaskStatusData = {
    value?: number;
    name?: string;
    itemStyle?: ItemStyle;
  };

  type TaskSummary = {
    title?: string;
    taskStatusData?: TaskStatusData[];
    taskCountData?: TaskCountData[];
    taskDataSource?: TaskDataSource[];
    taskListObj?: TaskListObj;
  };

  type TimeDTO = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
  };

  type TreeInfo = {
    /** id */
    id: number;
    /** 父级id */
    pid: number;
    /** id路径 */
    idPath?: string;
    /** 名称路径 */
    namePath?: string;
    /** 树类型标识 */
    treeCode: string;
    /** 数据类型 */
    dataType?: number;
    /** 数据名称 */
    dataName: string;
    /** 数据排序 */
    dataSort?: number;
    /** 数据标识 */
    dataCode?: string;
    /** 数据说明 */
    dataExplain?: string;
    /** 数据扩展 */
    dataExt?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type treeInfoImportParams = {
    /** 文件 */
    file: any;
    /** 树标识 */
    treeCode: any;
    /** 导入模式，1按名称，2按code */
    model: number;
  };

  type TreeInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    /** id */
    id?: number;
    /** 父级id */
    pid?: number;
    /** id路径 */
    idPath?: string;
    /** 名称路径 */
    namePath?: string;
    /** 树类型 */
    treeCode: string;
    /** 数据类型 */
    dataType?: number;
    /** 数据名称 */
    dataName?: string;
    /** 数据标识 */
    dataCode?: string;
    /** 数据说明 */
    dataExplain?: string;
    /** 数据扩展 */
    dataExt?: string;
  };

  type TreeInfoVO = {
    /** id */
    id: number;
    /** 父级id */
    pid: number;
    /** id路径 */
    idPath?: string;
    /** 名称路径 */
    namePath?: string;
    /** 树类型标识 */
    treeCode: string;
    /** 数据类型 */
    dataType?: number;
    /** 数据名称 */
    dataName: string;
    /** 数据排序 */
    dataSort?: number;
    /** 数据标识 */
    dataCode?: string;
    /** 数据说明 */
    dataExplain?: string;
    /** 数据扩展 */
    dataExt?: string;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 扩展的数据 */
    extraData?: { strictMode?: boolean; empty?: boolean };
    /** 子级数据 */
    child?: TreeInfoVO[];
  };

  type TreeMain = {
    id: number;
    /** 树类型标识 */
    treeCode: string;
    /** 树名称 */
    treeName: string;
    /** 树实现类 */
    treeService?: string;
    /** 树数据类型字典，为空则没有数据类型 */
    dataTypeDic?: string;
    createTime?: string;
    updateTime?: string;
    /** 树说明 */
    treeExplain?: string;
  };

  type TreeMainQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 树类型标识 */
    treeType?: number;
    /** 树名称 */
    treeName?: string;
    /** 树实现类 */
    treeService?: string;
    /** 树数据类型字典，为空则没有数据类型 */
    dataTypeDic?: string;
    createTime?: string;
    updateTime?: string;
    /** 树说明 */
    treeExplain?: string;
  };

  type UpdatePagePermissionSortDTO = {
    /** id */
    id: number;
    /** 排序 */
    sort: number;
    /** 父id */
    pid?: number;
  };

  type UpdatePasswordDTO = {
    id: number;
    checkPassword: string;
    password?: string;
  };

  type UserDepInfoVO = {
    id: number;
    userId?: number;
    deptId?: number;
    /** 部门名称 */
    deptName?: string;
    userInfo?: OrgUserInfo;
  };

  type UserInfoNoPhotoQuery = {
    deptIds?: number[];
    roleIds?: number[];
    userIds?: number[];
    userName?: string;
    fullName?: string;
  };

  type UserMsgCount = {
    /** 待办未读数量 */
    backlogCount?: number;
    /** 通知未读数量 */
    notifyCount?: number;
    /** 公告未读数量 */
    noticeCount?: number;
  };

  type UserTableColumnInfo = {
    id: number;
    /** 列表名 */
    keyName?: string;
    /** 列名详情 */
    columnsInfo?: string;
    /** 员工id */
    userId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type UserTableColumnInfoQuery = {
    /** 页码 */
    page: number;
    /** 条数 */
    size: number;
    likeFlag?: number;
    /** 开始时间 */
    beginTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 时间排序，1倒叙，0正序 */
    timeSort?: number;
    /** id排序，1倒叙，0正序 */
    idSort?: number;
    id?: number;
    /** 列表名 */
    keyName?: string;
    /** 列名详情 */
    columnsInfo?: string;
    /** 员工id */
    userId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type UserTableColumnInfoVO = {
    id: number;
    /** 列表名 */
    keyName?: string;
    /** 列名详情 */
    columnsInfo?: string;
    /** 员工id */
    userId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
  };

  type videoOpenVideoParams = {
    fileCode: string;
    fileName: string;
  };

  type WorkCameraDto = {
    /** 工位id */
    id?: number;
    /** 摄像头id */
    cameraId?: number;
    /** 操作类型(关联/解绑) */
    operationType?: string;
  };

  type WorkEquipmentDto = {
    /** 工位id */
    id?: number;
    /** 设备id */
    equipmentIds?: number[];
    /** 操作类型(关联/解绑) */
    operationType?: string;
  };

  type WorkstationDeviceTreeDTO = {
    /** 节点ID */
    id?: number;
    /** 节点名称 */
    label?: string;
    /** 节点类型（workstation-工位，device-设备） */
    type?: string;
    /** 子节点列表 */
    children?: WorkstationDeviceTreeDTO[];
  };
}
