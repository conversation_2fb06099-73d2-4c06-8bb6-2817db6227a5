// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增设备对接规范 POST /deviceIntegrationConfig/add */
export async function deviceIntegrationConfigAdd(
  body: BASE.DeviceIntegrationConfig,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/deviceIntegrationConfig/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除设备对接规范 POST /deviceIntegrationConfig/delete */
export async function deviceIntegrationConfigDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/deviceIntegrationConfig/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备对接规范详情 POST /deviceIntegrationConfig/detail */
export async function deviceIntegrationConfigDetail(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultDeviceIntegrationConfig>(`/api/test/deviceIntegrationConfig/detail`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备对接规范分页数据 POST /deviceIntegrationConfig/page */
export async function deviceIntegrationConfigPage(
  body: BASE.DeviceIntegrationConfigQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageDeviceIntegrationConfig>(
    `/api/test/deviceIntegrationConfig/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新设备对接规范 POST /deviceIntegrationConfig/update */
export async function deviceIntegrationConfigUpdate(
  body: BASE.DeviceIntegrationConfig,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/deviceIntegrationConfig/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备对接规范VO分页数据 POST /deviceIntegrationConfig/voPage */
export async function deviceIntegrationConfigVoPage(
  body: BASE.DeviceIntegrationConfigQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageDeviceIntegrationConfigVO>(
    `/api/test/deviceIntegrationConfig/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
