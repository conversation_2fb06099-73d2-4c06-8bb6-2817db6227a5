// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增系统消息发送记录数据 POST /sendRecord/add */
export async function sendRecordAdd(body: BASE.SendRecord, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sendRecord/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除系统消息发送记录数据 POST /sendRecord/delete */
export async function sendRecordDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sendRecord/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统消息发送记录数据 POST /sendRecord/get */
export async function sendRecordGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSendRecord>(`/api/test/sendRecord/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统消息发送记录分页数据 POST /sendRecord/page */
export async function sendRecordPage(body: BASE.SendRecordQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageSendRecord>(`/api/test/sendRecord/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新系统消息发送记录数据 POST /sendRecord/update */
export async function sendRecordUpdate(body: BASE.SendRecord, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/sendRecord/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
