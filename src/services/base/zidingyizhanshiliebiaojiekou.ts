// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增自定义展示列表数据 POST /userTableColumnInfo/add */
export async function userTableColumnInfoAdd(
  body: BASE.UserTableColumnInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/userTableColumnInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除自定义展示列表数据 POST /userTableColumnInfo/delete */
export async function userTableColumnInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/userTableColumnInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取自定义展示列表数据 POST /userTableColumnInfo/get */
export async function userTableColumnInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultUserTableColumnInfo>(`/api/test/userTableColumnInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取自定义展示列表VO数据 POST /userTableColumnInfo/getVo */
export async function userTableColumnInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultUserTableColumnInfoVO>(`/api/test/userTableColumnInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取自定义展示列表分页数据 POST /userTableColumnInfo/page */
export async function userTableColumnInfoPage(
  body: BASE.UserTableColumnInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageUserTableColumnInfo>(`/api/test/userTableColumnInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新自定义展示列表数据 POST /userTableColumnInfo/update */
export async function userTableColumnInfoUpdate(
  body: BASE.UserTableColumnInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/userTableColumnInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取自定义展示列表VO分页数据 POST /userTableColumnInfo/voPage */
export async function userTableColumnInfoVoPage(
  body: BASE.UserTableColumnInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageUserTableColumnInfoVO>(`/api/test/userTableColumnInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
