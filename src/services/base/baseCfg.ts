// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增系统配置信息数据 POST /systemConfig/add */
export async function systemConfigAdd(body: BASE.SystemConfig, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/systemConfig/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除系统配置信息数据 POST /systemConfig/delete */
export async function systemConfigDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/systemConfig/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取前端配置信息数据 POST /systemConfig/front */
export async function systemConfigFront(options?: { [key: string]: any }) {
  return request<BASE.IResultListSystemConfig>(`/api/test/systemConfig/front`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取系统配置信息数据 POST /systemConfig/get */
export async function systemConfigGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSystemConfig>(`/api/test/systemConfig/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统配置body POST /systemConfig/getBody */
export async function systemConfigGetBody(
  body: BASE.AutoDtoWithSystemconfigGetbody,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/systemConfig/getBody`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统配置信息列表数据 POST /systemConfig/list */
export async function systemConfigList(
  body: BASE.SystemConfigQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListSystemConfig>(`/api/test/systemConfig/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统配置信息分页数据 POST /systemConfig/page */
export async function systemConfigPage(
  body: BASE.SystemConfigQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageSystemConfig>(`/api/test/systemConfig/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 设置系统配置值 POST /systemConfig/set */
export async function systemConfigSet(body: BASE.SetValueDTO[], options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/systemConfig/set`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 设置系统配置body POST /systemConfig/setBody */
export async function systemConfigSetBody(
  body: BASE.AutoDtoWithSystemconfigSetbody,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/systemConfig/setBody`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新系统配置信息数据 POST /systemConfig/update */
export async function systemConfigUpdate(
  body: BASE.SystemConfig,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/systemConfig/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
