// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增数据 POST /instrumentNewInfo/add */
export async function instrumentNewInfoAdd(
  body: BASE.InstrumentNewInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultBoolean>(`/api/test/instrumentNewInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除数据 POST /instrumentNewInfo/delete */
export async function instrumentNewInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultVoid>(`/api/test/instrumentNewInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出可检参数模板 POST /instrumentNewInfo/exportCheckParamsTemplate */
export async function instrumentNewInfoExportCheckParamsTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.instrumentNewInfoExportCheckParamsTemplateParams,
  options?: { [key: string]: any },
) {
  return request<string>(`/api/test/instrumentNewInfo/exportCheckParamsTemplate`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取数据 POST /instrumentNewInfo/get */
export async function instrumentNewInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultInstrumentNewInfo>(`/api/test/instrumentNewInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备参数（不获取实际数据） POST /instrumentNewInfo/getDeviceParamsOnly */
export async function instrumentNewInfoGetDeviceParamsOnly(
  body: BASE.DeviceTestDataRequestDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListAutoResultDto>(`/api/test/instrumentNewInfo/getDeviceParamsOnly`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取异步设备响应 POST /instrumentNewInfo/getDeviceResponse */
export async function instrumentNewInfoGetDeviceResponse(
  body: BASE.DeviceTestDataRequestDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultMapStringObject>(`/api/test/instrumentNewInfo/getDeviceResponse`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取设备检测数据 POST /instrumentNewInfo/getDeviceTestData */
export async function instrumentNewInfoGetDeviceTestData(
  body: BASE.DeviceTestDataRequestDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListAutoResultDto>(`/api/test/instrumentNewInfo/getDeviceTestData`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取VO数据 POST /instrumentNewInfo/getVo */
export async function instrumentNewInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultInstrumentNewInfoVO>(`/api/test/instrumentNewInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导入可检参数 POST /instrumentNewInfo/importCheckParams */
export async function instrumentNewInfoImportCheckParams(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.instrumentNewInfoImportCheckParamsParams,
  body: {},
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/instrumentNewInfo/importCheckParams`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取分页数据 POST /instrumentNewInfo/page */
export async function instrumentNewInfoPage(
  body: BASE.InstrumentNewInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageInstrumentNewInfo>(`/api/test/instrumentNewInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送异步设备请求 POST /instrumentNewInfo/sendDeviceRequestAsync */
export async function instrumentNewInfoSendDeviceRequestAsync(
  body: BASE.DeviceTestDataRequestDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/instrumentNewInfo/sendDeviceRequestAsync`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新数据 POST /instrumentNewInfo/update */
export async function instrumentNewInfoUpdate(
  body: BASE.InstrumentNewInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultBoolean>(`/api/test/instrumentNewInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取VO分页数据 POST /instrumentNewInfo/voPage */
export async function instrumentNewInfoVoPage(
  body: BASE.InstrumentNewInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageInstrumentNewInfoVO>(`/api/test/instrumentNewInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
