// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取用户消息模块配置数据 POST /msgUserConfig/get */
export async function msgUserConfigGet(options?: { [key: string]: any }) {
  return request<BASE.IResultMsgUserConfigVO>(`/api/test/msgUserConfig/get`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 新增用户消息模块配置数据 POST /msgUserConfig/save */
export async function msgUserConfigSave(
  body: BASE.MsgUserConfigVO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/msgUserConfig/save`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
