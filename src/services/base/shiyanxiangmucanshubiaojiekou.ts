// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增实验项目参数表数据 POST /standardBasicProjectParam/add */
export async function standardBasicProjectParamAdd(
  body: BASE.StandardBasicProjectParam,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicProjectParam/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除实验项目参数表数据 POST /standardBasicProjectParam/delete */
export async function standardBasicProjectParamDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicProjectParam/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取实验项目参数表数据 POST /standardBasicProjectParam/get */
export async function standardBasicProjectParamGet(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicProjectParam>(`/api/test/standardBasicProjectParam/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取实验项目参数表VO数据 POST /standardBasicProjectParam/getVo */
export async function standardBasicProjectParamGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicProjectParamVO>(
    `/api/test/standardBasicProjectParam/getVo`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取实验项目参数表分页数据 POST /standardBasicProjectParam/page */
export async function standardBasicProjectParamPage(
  body: BASE.StandardBasicProjectParamQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicProjectParam>(
    `/api/test/standardBasicProjectParam/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新实验项目参数表数据 POST /standardBasicProjectParam/update */
export async function standardBasicProjectParamUpdate(
  body: BASE.StandardBasicProjectParam,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicProjectParam/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取实验项目参数表VO分页数据 POST /standardBasicProjectParam/voPage */
export async function standardBasicProjectParamVoPage(
  body: BASE.StandardBasicProjectParamQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicProjectParamVO>(
    `/api/test/standardBasicProjectParam/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
