// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增人员表数据 POST /orgUserInfo/add */
export async function orgUserInfoAdd(body: BASE.OrgUserInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgUserInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除人员表数据 POST /orgUserInfo/delete */
export async function orgUserInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgUserInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取人员表数据 POST /orgUserInfo/get */
export async function orgUserInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultOrgUserInfo>(`/api/test/orgUserInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询同部门用户 POST /orgUserInfo/getSameDepUserInfo */
export async function orgUserInfoGetSameDepUserInfo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListOrgUserInfo>(`/api/test/orgUserInfo/getSameDepUserInfo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询同角色用户 & 部门 POST /orgUserInfo/getUserInfoByRole */
export async function orgUserInfoGetUserInfoByRole(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultMapStringListUserDepInfoVO>(
    `/api/test/orgUserInfo/getUserInfoByRole`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取人员表VO数据 POST /orgUserInfo/getVo */
export async function orgUserInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultOrgUserInfoVO>(`/api/test/orgUserInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取人员表分页数据 POST /orgUserInfo/page */
export async function orgUserInfoPage(
  body: BASE.OrgUserInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageOrgUserInfo>(`/api/test/orgUserInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 重置密码 POST /orgUserInfo/resetPassword */
export async function orgUserInfoResetPassword(
  body: BASE.resetPasswordVo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/orgUserInfo/resetPassword`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步用户头像 GET /orgUserInfo/syncAvatar */
export async function orgUserInfoSyncAvatar(options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/orgUserInfo/syncAvatar`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新人员表数据 POST /orgUserInfo/update */
export async function orgUserInfoUpdate(body: BASE.OrgUserInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgUserInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询不带用户头像的用户信息 POST /orgUserInfo/userInfoNoPhoto */
export async function orgUserInfoUserInfoNoPhoto(
  body: BASE.UserInfoNoPhotoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListOrgUserInfo>(`/api/test/orgUserInfo/userInfoNoPhoto`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取人员表VO分页数据 POST /orgUserInfo/voPage */
export async function orgUserInfoVoPage(
  body: BASE.OrgUserInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageOrgUserInfoVO>(`/api/test/orgUserInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
