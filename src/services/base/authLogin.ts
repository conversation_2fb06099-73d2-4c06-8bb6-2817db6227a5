// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 快速登录，用于开发调试获取token POST /fastLogin */
export async function fastLogin(body: BASE.AutoDtoWithFastlogin, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/fastLogin`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 登录 POST /login */
export async function login(body: BASE.LoginDTO, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取加密后的密码文本 POST /login/getPwd */
export async function loginGetPwd(
  body: BASE.AutoDtoWithLoginGetpwd,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/login/getPwd`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 登出 POST /login/logout */
export async function loginLogout(options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/login/logout`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取公钥 POST /login/publicKey */
export async function loginPublicKey(options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/login/publicKey`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** token续期 POST /login/renewal */
export async function loginRenewal(
  body: BASE.AutoDtoWithLoginRenewal,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/login/renewal`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据token获取用户信息 POST /login/self */
export async function loginSelf(options?: { [key: string]: any }) {
  return request<BASE.IResultIUser>(`/api/test/login/self`, {
    method: 'POST',
    ...(options || {}),
  });
}
