// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增字典信息数据接口 POST /dicInfo/add */
export async function dicInfoAdd(body: BASE.DicInfoVO, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 字典信息列表数据接口 POST /dicInfo/all */
export async function dicInfoAll(options?: { [key: string]: any }) {
  return request<BASE.IResultListDicInfoVO>(`/api/test/dicInfo/all`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 删除字典信息数据接口 POST /dicInfo/delete */
export async function dicInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导入字典信息接口 POST /dicInfo/import */
export async function dicInfoImport(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.dicInfoImportParams,
  body: {
    dicCode?: string;
  },
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/dicInfo/import`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据code获取字典信息数据接口 POST /dicInfo/list */
export async function dicInfoList(
  body: BASE.AutoDtoWithDicinfoList,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListDicInfoVO>(`/api/test/dicInfo/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 字典信息分页数据接口 POST /dicInfo/page */
export async function dicInfoPage(body: BASE.DicInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageDicInfoVO>(`/api/test/dicInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取字典数据导入模板接口 POST /dicInfo/template */
export async function dicInfoTemplate(options?: { [key: string]: any }) {
  return request<any>(`/api/test/dicInfo/template`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 更新字典信息数据接口 POST /dicInfo/update */
export async function dicInfoUpdate(body: BASE.DicInfoVO, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
