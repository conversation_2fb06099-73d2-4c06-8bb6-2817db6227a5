// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增检测任务测试数据表数据 POST /jzTaskTestDataInfo/add */
export async function jzTaskTestDataInfoAdd(
  body: BASE.JzTaskTestDataInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskTestDataInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除检测任务测试数据表数据 POST /jzTaskTestDataInfo/delete */
export async function jzTaskTestDataInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskTestDataInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取检测任务测试数据表数据 POST /jzTaskTestDataInfo/get */
export async function jzTaskTestDataInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskTestDataInfo>(`/api/test/jzTaskTestDataInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取检测任务测试数据表VO数据 POST /jzTaskTestDataInfo/getVo */
export async function jzTaskTestDataInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskTestDataInfoVO>(`/api/test/jzTaskTestDataInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取检测任务测试数据表分页数据 POST /jzTaskTestDataInfo/page */
export async function jzTaskTestDataInfoPage(
  body: BASE.JzTaskTestDataInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskTestDataInfo>(`/api/test/jzTaskTestDataInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新检测任务测试数据表数据 POST /jzTaskTestDataInfo/update */
export async function jzTaskTestDataInfoUpdate(
  body: BASE.JzTaskTestDataInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskTestDataInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取检测任务测试数据表VO分页数据 POST /jzTaskTestDataInfo/voPage */
export async function jzTaskTestDataInfoVoPage(
  body: BASE.JzTaskTestDataInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskTestDataInfoVO>(`/api/test/jzTaskTestDataInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
