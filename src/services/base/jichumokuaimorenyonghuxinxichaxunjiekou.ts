// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 系统用户分页数据接口 POST /sysUser/page */
export async function sysUserPage(body: BASE.IUserQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageIUser>(`/api/test/sysUser/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
