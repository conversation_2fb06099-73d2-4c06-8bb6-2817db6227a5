// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增荆州—任务列表表数据 POST /jzTaskInfo/add */
export async function jzTaskInfoAdd(body: BASE.JzTaskInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除荆州—任务列表表数据 POST /jzTaskInfo/delete */
export async function jzTaskInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 任务检闭 POST /jzTaskInfo/endTask */
export async function jzTaskInfoEndTask(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskInfo/endTask`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—任务列表表数据 POST /jzTaskInfo/get */
export async function jzTaskInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskInfo>(`/api/test/jzTaskInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取首页统计图数据 GET /jzTaskInfo/getHomepage */
export async function jzTaskInfoGetHomepage(options?: { [key: string]: any }) {
  return request<BASE.IResultMapStringLong>(`/api/test/jzTaskInfo/getHomepage`, {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取荆州—任务列表表VO数据 POST /jzTaskInfo/getVo */
export async function jzTaskInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskInfoVO>(`/api/test/jzTaskInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—任务列表表分页数据 POST /jzTaskInfo/page */
export async function jzTaskInfoPage(body: BASE.JzTaskInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageJzTaskInfo>(`/api/test/jzTaskInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 启动任务 POST /jzTaskInfo/startTask */
export async function jzTaskInfoStartTask(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskInfo/startTask`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新荆州—任务列表表数据 POST /jzTaskInfo/update */
export async function jzTaskInfoUpdate(body: BASE.JzTaskInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzTaskInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—任务列表表VO分页数据 POST /jzTaskInfo/voPage */
export async function jzTaskInfoVoPage(
  body: BASE.JzTaskInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskInfoVO>(`/api/test/jzTaskInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
