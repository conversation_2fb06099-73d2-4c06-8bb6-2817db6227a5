// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增待办类型数据 POST /backlogMain/add */
export async function backlogMainAdd(body: BASE.BacklogMain, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/backlogMain/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除待办类型数据 POST /backlogMain/delete */
export async function backlogMainDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/backlogMain/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取待办类型数据 POST /backlogMain/get */
export async function backlogMainGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultBacklogMain>(`/api/test/backlogMain/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取待办类型VO数据 POST /backlogMain/getVo */
export async function backlogMainGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultBacklogMainVO>(`/api/test/backlogMain/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取待办类型分页数据 POST /backlogMain/page */
export async function backlogMainPage(
  body: BASE.BacklogMainQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBacklogMain>(`/api/test/backlogMain/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新待办类型数据 POST /backlogMain/update */
export async function backlogMainUpdate(body: BASE.BacklogMain, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/backlogMain/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取待办类型VO分页数据 POST /backlogMain/voPage */
export async function backlogMainVoPage(
  body: BASE.BacklogMainQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBacklogMainVO>(`/api/test/backlogMain/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
