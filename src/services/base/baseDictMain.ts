// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增字典主信息数据接口 POST /dicMain/add */
export async function dicMainAdd(body: BASE.DicMain, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicMain/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 复制字典主信息数据接口 POST /dicMain/copy */
export async function dicMainCopy(body: BASE.DicCopyDTO, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicMain/copy`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除字典主信息数据接口 POST /dicMain/delete */
export async function dicMainDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicMain/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取字典主信息数据接口 POST /dicMain/get */
export async function dicMainGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultDicMain>(`/api/test/dicMain/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据code获取详情 POST /dicMain/getDicCode */
export async function dicMainGetDicCode(body: BASE.CodeDTO, options?: { [key: string]: any }) {
  return request<BASE.IResultDicMain>(`/api/test/dicMain/getDicCode`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 字典主信息分页数据接口 POST /dicMain/page */
export async function dicMainPage(body: BASE.DicMainQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageDicMain>(`/api/test/dicMain/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新字典主信息数据接口 POST /dicMain/update */
export async function dicMainUpdate(body: BASE.DicMain, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/dicMain/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
