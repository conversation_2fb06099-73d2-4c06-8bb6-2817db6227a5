// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增国王—报告信息表数据 POST /jzReportInfo/add */
export async function jzReportInfoAdd(body: BASE.JzReportInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzReportInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 自动生成报告 POST /jzReportInfo/autoReport */
export async function jzReportInfoAutoReport(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzReportInfo/autoReport`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除国王—报告信息表数据 POST /jzReportInfo/delete */
export async function jzReportInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/jzReportInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取国王—报告信息表数据 POST /jzReportInfo/get */
export async function jzReportInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzReportInfo>(`/api/test/jzReportInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取国王—报告信息表VO数据 POST /jzReportInfo/getVo */
export async function jzReportInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzReportInfoVO>(`/api/test/jzReportInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取国王—报告信息表分页数据 POST /jzReportInfo/page */
export async function jzReportInfoPage(
  body: BASE.JzReportInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzReportInfo>(`/api/test/jzReportInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新国王—报告信息表数据 POST /jzReportInfo/update */
export async function jzReportInfoUpdate(
  body: BASE.JzReportInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzReportInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取国王—报告信息表VO分页数据 POST /jzReportInfo/voPage */
export async function jzReportInfoVoPage(
  body: BASE.JzReportInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzReportInfoVO>(`/api/test/jzReportInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
