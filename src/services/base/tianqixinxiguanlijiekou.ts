// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取环境 POST /environmentInfo/getWeather */
export async function environmentInfoGetWeather(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.environmentInfoGetWeatherParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/environmentInfo/getWeather`, {
    method: 'POST',
    params: {
      // cityName has a default value: 荆州
      cityName: '荆州',
      ...params,
    },
    ...(options || {}),
  });
}
