// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增工位信息管理数据 POST /buInstrumentWorkstationInfo/add */
export async function buInstrumentWorkstationInfoAdd(
  body: BASE.BuInstrumentWorkstationInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentWorkstationInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除工位信息管理数据 POST /buInstrumentWorkstationInfo/delete */
export async function buInstrumentWorkstationInfoDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentWorkstationInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取工位信息管理数据 POST /buInstrumentWorkstationInfo/get */
export async function buInstrumentWorkstationInfoGet(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultBuInstrumentWorkstationInfo>(
    `/api/test/buInstrumentWorkstationInfo/get`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取工位信息管理VO数据 POST /buInstrumentWorkstationInfo/getVo */
export async function buInstrumentWorkstationInfoGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultBuInstrumentWorkstationInfoVO>(
    `/api/test/buInstrumentWorkstationInfo/getVo`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取工位-设备树形结构 POST /buInstrumentWorkstationInfo/getWorkstationDeviceTree */
export async function buInstrumentWorkstationInfoGetWorkstationDeviceTree(options?: {
  [key: string]: any;
}) {
  return request<BASE.IResultListWorkstationDeviceTreeDTO>(
    `/api/test/buInstrumentWorkstationInfo/getWorkstationDeviceTree`,
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}

/** 获取工位信息管理分页数据 POST /buInstrumentWorkstationInfo/page */
export async function buInstrumentWorkstationInfoPage(
  body: BASE.BuInstrumentWorkstationInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBuInstrumentWorkstationInfo>(
    `/api/test/buInstrumentWorkstationInfo/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 关联摄像头 POST /buInstrumentWorkstationInfo/saveEntityWithCamera */
export async function buInstrumentWorkstationInfoSaveEntityWithCamera(
  body: BASE.WorkCameraDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentWorkstationInfo/saveEntityWithCamera`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 关联设备 POST /buInstrumentWorkstationInfo/saveEntityWithDevice */
export async function buInstrumentWorkstationInfoSaveEntityWithDevice(
  body: BASE.WorkEquipmentDto,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentWorkstationInfo/saveEntityWithDevice`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新工位信息管理数据 POST /buInstrumentWorkstationInfo/update */
export async function buInstrumentWorkstationInfoUpdate(
  body: BASE.BuInstrumentWorkstationInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/buInstrumentWorkstationInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取工位信息管理VO分页数据 POST /buInstrumentWorkstationInfo/voPage */
export async function buInstrumentWorkstationInfoVoPage(
  body: BASE.BuInstrumentWorkstationInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBuInstrumentWorkstationInfoVO>(
    `/api/test/buInstrumentWorkstationInfo/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
