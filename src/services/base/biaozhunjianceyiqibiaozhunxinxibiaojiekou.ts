// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增标准—检测仪器标准信息表数据 POST /standardBasicInstrumentInfo/add */
export async function standardBasicInstrumentInfoAdd(
  body: BASE.StandardBasicInstrumentInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultBoolean>(`/api/test/standardBasicInstrumentInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除标准—检测仪器标准信息表数据 POST /standardBasicInstrumentInfo/delete */
export async function standardBasicInstrumentInfoDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultVoid>(`/api/test/standardBasicInstrumentInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准—检测仪器标准信息表数据 POST /standardBasicInstrumentInfo/get */
export async function standardBasicInstrumentInfoGet(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicInstrumentInfo>(
    `/api/test/standardBasicInstrumentInfo/get`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取标准—检测仪器标准信息表VO数据 POST /standardBasicInstrumentInfo/getVo */
export async function standardBasicInstrumentInfoGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicInstrumentInfoVO>(
    `/api/test/standardBasicInstrumentInfo/getVo`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取标准—检测仪器标准信息表分页数据 POST /standardBasicInstrumentInfo/page */
export async function standardBasicInstrumentInfoPage(
  body: BASE.StandardBasicInstrumentInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicInstrumentInfo>(
    `/api/test/standardBasicInstrumentInfo/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新标准—检测仪器标准信息表数据 POST /standardBasicInstrumentInfo/update */
export async function standardBasicInstrumentInfoUpdate(
  body: BASE.StandardBasicInstrumentInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultBoolean>(`/api/test/standardBasicInstrumentInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传原始记录模板文件 POST /standardBasicInstrumentInfo/uploadOriginalRecordTemplate */
export async function standardBasicInstrumentInfoUploadOriginalRecordTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.standardBasicInstrumentInfoUploadOriginalRecordTemplateParams,
  body: {},
  options?: { [key: string]: any },
) {
  return request<BASE.IResultFileInfo>(
    `/api/test/standardBasicInstrumentInfo/uploadOriginalRecordTemplate`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        ...params,
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 上传报告模板文件 POST /standardBasicInstrumentInfo/uploadReportTemplate */
export async function standardBasicInstrumentInfoUploadReportTemplate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.standardBasicInstrumentInfoUploadReportTemplateParams,
  body: FormData,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultFileInfo>(
    `/api/test/standardBasicInstrumentInfo/uploadReportTemplate`,
    {
      method: 'POST',
      params: {
        ...params,
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取标准—检测仪器标准信息表VO分页数据 POST /standardBasicInstrumentInfo/voPage */
export async function standardBasicInstrumentInfoVoPage(
  body: BASE.StandardBasicInstrumentInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicInstrumentInfoVO>(
    `/api/test/standardBasicInstrumentInfo/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
