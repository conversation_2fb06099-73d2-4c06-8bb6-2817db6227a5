// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 大屏—报告汇总 POST /screen/newScreenDataCount */
export async function screenNewScreenDataCount(options?: { [key: string]: any }) {
  return request<BASE.IResultNewScreenData>(`/api/test/screen/newScreenDataCount`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 大屏—报告汇总 POST /screen/report */
export async function screenReport(options?: { [key: string]: any }) {
  return request<BASE.IResultScreenData>(`/api/test/screen/report`, {
    method: 'POST',
    ...(options || {}),
  });
}
