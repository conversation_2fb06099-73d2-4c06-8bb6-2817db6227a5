// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增荆州—检项列表表数据 POST /jzTaskInspectionItemInfo/add */
export async function jzTaskInspectionItemInfoAdd(
  body: BASE.JzTaskInspectionItemInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskInspectionItemInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除荆州—检项列表表数据 POST /jzTaskInspectionItemInfo/delete */
export async function jzTaskInspectionItemInfoDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskInspectionItemInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—检项列表表数据 POST /jzTaskInspectionItemInfo/get */
export async function jzTaskInspectionItemInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultJzTaskInspectionItemInfo>(`/api/test/jzTaskInspectionItemInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—检项列表表VO数据 POST /jzTaskInspectionItemInfo/getVo */
export async function jzTaskInspectionItemInfoGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultJzTaskInspectionItemInfoVO>(
    `/api/test/jzTaskInspectionItemInfo/getVo`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取荆州—检项列表表分页数据 POST /jzTaskInspectionItemInfo/page */
export async function jzTaskInspectionItemInfoPage(
  body: BASE.JzTaskInspectionItemInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskInspectionItemInfo>(
    `/api/test/jzTaskInspectionItemInfo/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新荆州—检项列表表数据 POST /jzTaskInspectionItemInfo/update */
export async function jzTaskInspectionItemInfoUpdate(
  body: BASE.JzTaskInspectionItemInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskInspectionItemInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取荆州—检项列表表VO分页数据 POST /jzTaskInspectionItemInfo/voPage */
export async function jzTaskInspectionItemInfoVoPage(
  body: BASE.JzTaskInspectionItemInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskInspectionItemInfoVO>(
    `/api/test/jzTaskInspectionItemInfo/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
