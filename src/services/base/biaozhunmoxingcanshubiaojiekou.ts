// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增标准-模型参数表数据 POST /jzTaskWorkOrderModelParam/add */
export async function jzTaskWorkOrderModelParamAdd(
  body: BASE.JzTaskWorkOrderModelParam,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderModelParam/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除标准-模型参数表数据 POST /jzTaskWorkOrderModelParam/delete */
export async function jzTaskWorkOrderModelParamDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderModelParam/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表数据 POST /jzTaskWorkOrderModelParam/get */
export async function jzTaskWorkOrderModelParamGet(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultJzTaskWorkOrderModelParam>(`/api/test/jzTaskWorkOrderModelParam/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表VO数据 POST /jzTaskWorkOrderModelParam/getVo */
export async function jzTaskWorkOrderModelParamGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultJzTaskWorkOrderModelParamVO>(
    `/api/test/jzTaskWorkOrderModelParam/getVo`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 获取标准-模型参数表分页数据 POST /jzTaskWorkOrderModelParam/page */
export async function jzTaskWorkOrderModelParamPage(
  body: BASE.JzTaskWorkOrderModelParamQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskWorkOrderModelParam>(
    `/api/test/jzTaskWorkOrderModelParam/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新标准-模型参数表数据 POST /jzTaskWorkOrderModelParam/update */
export async function jzTaskWorkOrderModelParamUpdate(
  body: BASE.JzTaskWorkOrderModelParam,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/jzTaskWorkOrderModelParam/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表VO分页数据 POST /jzTaskWorkOrderModelParam/voPage */
export async function jzTaskWorkOrderModelParamVoPage(
  body: BASE.JzTaskWorkOrderModelParamQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageJzTaskWorkOrderModelParamVO>(
    `/api/test/jzTaskWorkOrderModelParam/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 新增标准-模型参数表数据 POST /standardBasicModelParam/add */
export async function standardBasicModelParamAdd(
  body: BASE.StandardBasicModelParam,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicModelParam/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除标准-模型参数表数据 POST /standardBasicModelParam/delete */
export async function standardBasicModelParamDelete(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicModelParam/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表数据 POST /standardBasicModelParam/get */
export async function standardBasicModelParamGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultStandardBasicModelParam>(`/api/test/standardBasicModelParam/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表VO数据 POST /standardBasicModelParam/getVo */
export async function standardBasicModelParamGetVo(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultStandardBasicModelParamVO>(`/api/test/standardBasicModelParam/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表分页数据 POST /standardBasicModelParam/page */
export async function standardBasicModelParamPage(
  body: BASE.StandardBasicModelParamQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicModelParam>(
    `/api/test/standardBasicModelParam/page`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 更新标准-模型参数表数据 POST /standardBasicModelParam/update */
export async function standardBasicModelParamUpdate(
  body: BASE.StandardBasicModelParam,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/standardBasicModelParam/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取标准-模型参数表VO分页数据 POST /standardBasicModelParam/voPage */
export async function standardBasicModelParamVoPage(
  body: BASE.StandardBasicModelParamQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageStandardBasicModelParamVO>(
    `/api/test/standardBasicModelParam/voPage`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
