// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 删除待办信息表数据 POST /backlogInfo/delete */
export async function backlogInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/backlogInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 忽略待办信息表数据 POST /backlogInfo/ignore */
export async function backlogInfoIgnore(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/backlogInfo/ignore`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取待办信息表分页数据 POST /backlogInfo/page */
export async function backlogInfoPage(
  body: BASE.BacklogInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageBacklogInfoVO>(`/api/test/backlogInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
