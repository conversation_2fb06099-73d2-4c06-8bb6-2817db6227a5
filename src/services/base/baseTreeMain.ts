// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增通用树主信息表数据 POST /treeMain/add */
export async function treeMainAdd(body: BASE.TreeMain, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/treeMain/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除通用树主信息表数据 POST /treeMain/delete */
export async function treeMainDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/treeMain/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取通用树主信息表数据 POST /treeMain/get */
export async function treeMainGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultTreeMain>(`/api/test/treeMain/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有树住信息 POST /treeMain/list */
export async function treeMainList(options?: { [key: string]: any }) {
  return request<BASE.IResultListTreeMain>(`/api/test/treeMain/list`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取通用树主信息表分页数据 POST /treeMain/page */
export async function treeMainPage(body: BASE.TreeMainQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageTreeMain>(`/api/test/treeMain/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新通用树主信息表数据 POST /treeMain/update */
export async function treeMainUpdate(body: BASE.TreeMain, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/treeMain/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
