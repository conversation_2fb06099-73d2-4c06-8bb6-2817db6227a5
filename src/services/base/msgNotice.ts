// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取用户消息模块统计数据 POST /noticeInfo/count */
export async function noticeInfoCount(options?: { [key: string]: any }) {
  return request<BASE.IResultUserMsgCount>(`/api/test/noticeInfo/count`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 删除系统公告信息数据 POST /noticeInfo/delete */
export async function noticeInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/noticeInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查看系统公告信息数据 POST /noticeInfo/get */
export async function noticeInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultNoticeInfoVO>(`/api/test/noticeInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 编辑时获取系统公告信息数据 POST /noticeInfo/getByEdit */
export async function noticeInfoGetByEdit(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultNoticeInfoEditVO>(`/api/test/noticeInfo/getByEdit`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据接收类型获取接收者列表 POST /noticeInfo/getRecipient */
export async function noticeInfoGetRecipient(
  body: BASE.AutoDtoWithNoticeinfoGetrecipient,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListKeyValueLongString>(`/api/test/noticeInfo/getRecipient`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取公告接收者列表 POST /noticeInfo/getRecipientByNoticeId */
export async function noticeInfoGetRecipientByNoticeId(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListKeyValueIntegerString>(
    `/api/test/noticeInfo/getRecipientByNoticeId`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 下架公告 POST /noticeInfo/lowerShelf */
export async function noticeInfoLowerShelf(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/noticeInfo/lowerShelf`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改公告接收人 POST /noticeInfo/modifyRecipient */
export async function noticeInfoModifyRecipient(
  body: BASE.ModifyRecipientDTO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/noticeInfo/modifyRecipient`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统公告信息分页数据 POST /noticeInfo/page */
export async function noticeInfoPage(body: BASE.NoticeInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageNoticeInfo>(`/api/test/noticeInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用户获取系统公告信息分页数据 POST /noticeInfo/pageByUser */
export async function noticeInfoPageByUser(
  body: BASE.NoticeInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageNoticeInfoVO>(`/api/test/noticeInfo/pageByUser`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将公告信息设置为已读 POST /noticeInfo/read */
export async function noticeInfoRead(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/noticeInfo/read`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发布公告 POST /noticeInfo/release */
export async function noticeInfoRelease(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/noticeInfo/release`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 保存公告数据并立即发布 POST /noticeInfo/saveAndRelease */
export async function noticeInfoSaveAndRelease(
  body: BASE.SaveNoticeDTO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/noticeInfo/saveAndRelease`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 保存公告数据为草稿 POST /noticeInfo/saveAsDraft */
export async function noticeInfoSaveAsDraft(
  body: BASE.SaveNoticeDTO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/noticeInfo/saveAsDraft`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
