// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据用户id获取其所有的前端页面权限数据 POST /getAllByUserId */
export async function getAllByUserId(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSetPagePermission>(`/api/test/getAllByUserId`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增前端页面权限数据 POST /permission/front/add */
export async function permissionFrontAdd(
  body: BASE.PagePermission,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultPagePermission>(`/api/test/permission/front/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据角色id绑定前端页面权限数据 POST /permission/front/bindingByPageId */
export async function permissionFrontBindingByPageId(
  body: BASE.ManyToManyDTOLong,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/front/bindingByPageId`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据用户id直接给用户绑定前端页面权限数据 POST /permission/front/bindingByUserId */
export async function permissionFrontBindingByUserId(
  body: BASE.ManyToManyDTOLong,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/front/bindingByUserId`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除前端页面权限数据 POST /permission/front/delete */
export async function permissionFrontDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/permission/front/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取前端页面权限数据 POST /permission/front/get */
export async function permissionFrontGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultPagePermission>(`/api/test/permission/front/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据角色id获取前端页面权限数据 POST /permission/front/getByRoleId */
export async function permissionFrontGetByRoleId(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSetPagePermission>(`/api/test/permission/front/getByRoleId`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据用户id获取直接绑定的前端页面权限数据 POST /permission/front/getByUserId */
export async function permissionFrontGetByUserId(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultSetPagePermission>(`/api/test/permission/front/getByUserId`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据角色id获取前端页面树形权限数据 POST /permission/front/getTreeByRoleId */
export async function permissionFrontGetTreeByRoleId(
  body: BASE.ID,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListPagePermissionVO>(`/api/test/permission/front/getTreeByRoleId`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取前端页面权限列表 POST /permission/front/list */
export async function permissionFrontList(options?: { [key: string]: any }) {
  return request<BASE.IResultListPagePermission>(`/api/test/permission/front/list`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取前端页面权限分页数据 POST /permission/front/page */
export async function permissionFrontPage(
  body: BASE.PagePermissionQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPagePagePermission>(`/api/test/permission/front/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取前端页面权限树形结构数据 POST /permission/front/tree */
export async function permissionFrontTree(
  body: BASE.PagePermissionTreeQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListPagePermissionVO>(`/api/test/permission/front/tree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取前端页面权限树形分页结构数据 POST /permission/front/treePage */
export async function permissionFrontTreePage(
  body: BASE.AutoDtoWithPermissionFrontTreepage,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPagePagePermission>(`/api/test/permission/front/treePage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新前端页面权限数据 POST /permission/front/update */
export async function permissionFrontUpdate(
  body: BASE.PagePermission,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultPagePermission>(`/api/test/permission/front/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新前端页面权限数据排序接口 POST /permission/front/updateSort */
export async function permissionFrontUpdateSort(
  body: BASE.AutoDtoWithPermissionFrontUpdatesort,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/front/updateSort`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
