// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 删除文件信息数据 POST /fileObs/delete */
export async function fileObsDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/fileObs/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取obs访问url POST /fileObs/getUrl */
export async function fileObsGetUrl(body: BASE.ObsUrlQuery, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/fileObs/getUrl`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分片上传完成标识 POST /fileObs/slice/confirm */
export async function fileObsSliceConfirm(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileObsSliceConfirmParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/fileObs/slice/confirm`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 大文件分片上传 POST /fileObs/slice/upload */
export async function fileObsSliceUpload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileObsSliceUploadParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/fileObs/slice/upload`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 上传文件 POST /fileObs/upload */
export async function fileObsUpload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileObsUploadParams,
  body: {},
  options?: { [key: string]: any },
) {
  return request<BASE.IResultFileInfo>(`/api/test/fileObs/upload`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
