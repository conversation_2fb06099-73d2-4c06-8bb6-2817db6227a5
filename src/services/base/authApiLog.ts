// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 清理系统接口日志数据 POST /apiLog/clear */
export async function apiLogClear(body: BASE.TimeDTO, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/apiLog/clear`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除系统接口日志数据 POST /apiLog/delete */
export async function apiLogDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/apiLog/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统接口日志详情数据 POST /apiLog/get */
export async function apiLogGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultApiLog>(`/api/test/apiLog/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取系统接口日志分页数据 POST /apiLog/page */
export async function apiLogPage(body: BASE.ApiLogQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageApiLog>(`/api/test/apiLog/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
