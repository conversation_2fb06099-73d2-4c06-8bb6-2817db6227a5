// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 打开office文件 GET /doc/open/${param0}/${param1} */
export async function docOpenDoc(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.docOpenDocParams,
  options?: { [key: string]: any },
) {
  const { fileCode: param0, fileName: param1, ...queryParams } = params;
  return request<string>(`/api/test/doc/open/${param0}/${param1}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 将base64图片转为文件上传 POST /file/base64Upload */
export async function fileBase64Upload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileBase64UploadParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultFileInfo>(`/api/test/file/base64Upload`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 清理文件数据 POST /file/clear */
export async function fileClear(body: BASE.ClearFileDTO, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/file/clear`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除文件信息数据 POST /file/delete */
export async function fileDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/file/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 文件下载 GET /file/down/${param0}/${param1} */
export async function fileDownLoad(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileDownLoadParams,
  options?: { [key: string]: any },
) {
  const { fileCode: param0, fileName: param1, ...queryParams } = params;
  return request<string[]>(`/api/test/file/down/${param0}/${param1}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取文件信息数据 POST /file/get */
export async function fileGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultFileInfo>(`/api/test/file/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 打开文件 GET /file/open/${param0}/${param1} */
export async function filePlay(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.filePlayParams,
  options?: { [key: string]: any },
) {
  const { fileCode: param0, fileName: param1, ...queryParams } = params;
  return request<any>(`/api/test/file/open/${param0}/${param1}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 文件信息分页数据 POST /file/page */
export async function filePage(body: BASE.FileInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageFileInfo>(`/api/test/file/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分片上传完成标识 POST /file/slice/confirm */
export async function fileSliceConfirm(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileSliceConfirmParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/file/slice/confirm`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 大文件分片上传 POST /file/slice/upload */
export async function fileSliceUpload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileSliceUploadParams,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/file/slice/upload`, {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新文件信息数据 POST /file/update */
export async function fileUpdate(body: BASE.FileInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/file/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传文件 POST /file/upload */
export async function fileUpload(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.fileUploadParams,
  body: {},
  options?: { [key: string]: any },
) {
  return request<BASE.IResultFileInfo>(`/api/test/file/upload`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 打开视频文件 GET /video/open/${param0}/${param1} */
export async function videoOpenVideo(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.videoOpenVideoParams,
  options?: { [key: string]: any },
) {
  const { fileCode: param0, fileName: param1, ...queryParams } = params;
  return request<string>(`/api/test/video/open/${param0}/${param1}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
