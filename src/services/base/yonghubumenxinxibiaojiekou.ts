// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增用户部门信息表数据 POST /orgUserDeptInfo/add */
export async function orgUserDeptInfoAdd(
  body: BASE.OrgUserDeptInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/orgUserDeptInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除用户部门信息表数据 POST /orgUserDeptInfo/delete */
export async function orgUserDeptInfoDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/orgUserDeptInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户部门信息表数据 POST /orgUserDeptInfo/get */
export async function orgUserDeptInfoGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultOrgUserDeptInfo>(`/api/test/orgUserDeptInfo/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户部门信息表VO数据 POST /orgUserDeptInfo/getVo */
export async function orgUserDeptInfoGetVo(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultOrgUserDeptInfoVO>(`/api/test/orgUserDeptInfo/getVo`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户部门信息表分页数据 POST /orgUserDeptInfo/page */
export async function orgUserDeptInfoPage(
  body: BASE.OrgUserDeptInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageOrgUserDeptInfo>(`/api/test/orgUserDeptInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新用户部门信息表数据 POST /orgUserDeptInfo/update */
export async function orgUserDeptInfoUpdate(
  body: BASE.OrgUserDeptInfo,
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/orgUserDeptInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户部门信息表VO分页数据 POST /orgUserDeptInfo/voPage */
export async function orgUserDeptInfoVoPage(
  body: BASE.OrgUserDeptInfoQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageOrgUserDeptInfoVO>(`/api/test/orgUserDeptInfo/voPage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
