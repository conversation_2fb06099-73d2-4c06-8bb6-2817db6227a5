// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增后端接口权限数据 POST /permission/api/add */
export async function permissionApiAdd(body: BASE.ApiPermission, options?: { [key: string]: any }) {
  return request<BASE.IResultApiPermission>(`/api/test/permission/api/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增前后端权限绑定信息 POST /permission/api/add/pageApi */
export async function permissionApiAddPageApi(
  body: BASE.PagePermissionApi,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/api/add/pageApi`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 清空后端接口权限数据 POST /permission/api/clear */
export async function permissionApiClear(options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/permission/api/clear`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 删除前后端权限绑定信息 POST /permission/api/del/pageApi */
export async function permissionApiDelPageApi(
  body: BASE.PagePermissionApi,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/api/del/pageApi`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除后端接口权限数据 POST /permission/api/delete */
export async function permissionApiDelete(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultString>(`/api/test/permission/api/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取后端接口权限数据 POST /permission/api/get */
export async function permissionApiGet(body: BASE.ID, options?: { [key: string]: any }) {
  return request<BASE.IResultApiPermission>(`/api/test/permission/api/get`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有表数据 POST /permission/api/getTableName */
export async function permissionApiGetTableName(options?: { [key: string]: any }) {
  return request<BASE.IResultListString>(`/api/test/permission/api/getTableName`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取后端接口权限分页数据 POST /permission/api/page */
export async function permissionApiPage(
  body: BASE.ApiPermissionQuery,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageApiPermission>(`/api/test/permission/api/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 重新加载后端接口权限数据到缓存中 POST /permission/api/reload */
export async function permissionApiReload(options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/permission/api/reload`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 从服务中同步后端接口权限数据 POST /permission/api/sync */
export async function permissionApiSync(
  body: BASE.SyncPermissionDTO,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/api/sync`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 前后端权限绑定 POST /permission/api/syncPageAndApi */
export async function permissionApiSyncPageAndApi(
  body: BASE.SyncPageAndApiDTO[],
  options?: { [key: string]: any },
) {
  return request<BASE.IResultString>(`/api/test/permission/api/syncPageAndApi`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取后端接口权限树形结构数据 POST /permission/api/tree */
export async function permissionApiTree(
  body: BASE.AutoDtoWithPermissionApiTree,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultListApiPermission>(`/api/test/permission/api/tree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取后端接口权限树形分页结构数据 POST /permission/api/treePage */
export async function permissionApiTreePage(
  body: BASE.AutoDtoWithPermissionApiTreepage,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultIPageApiPermission>(`/api/test/permission/api/treePage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新后端接口权限数据 POST /permission/api/update */
export async function permissionApiUpdate(
  body: BASE.ApiPermission,
  options?: { [key: string]: any },
) {
  return request<BASE.IResultApiPermission>(`/api/test/permission/api/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
