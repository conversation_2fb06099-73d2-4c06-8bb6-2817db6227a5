// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 新增通用树数据 POST /treeInfo/add */
export async function treeInfoAdd(body: BASE.TreeInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/treeInfo/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除通用树数据 POST /treeInfo/delete */
export async function treeInfoDelete(body: BASE.RemoveTreeDTO, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/treeInfo/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出树形数据信息 POST /treeInfo/exportTree */
export async function treeInfoExportTree(body: BASE.CodeDTO, options?: { [key: string]: any }) {
  return request<any>(`/api/test/treeInfo/exportTree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导入树形数据信息接口 POST /treeInfo/import */
export async function treeInfoImport(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: BASE.treeInfoImportParams,
  body: {
    treeCode?: string;
  },
  options?: { [key: string]: any },
) {
  return request<BASE.IResult>(`/api/test/treeInfo/import`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取通用树数据分页数据 POST /treeInfo/page */
export async function treeInfoPage(body: BASE.TreeInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultIPageTreeInfo>(`/api/test/treeInfo/page`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取树形数据导入模板接口 POST /treeInfo/template */
export async function treeInfoTemplate(options?: { [key: string]: any }) {
  return request<any>(`/api/test/treeInfo/template`, {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取通用树形结构数据 POST /treeInfo/tree */
export async function treeInfoTree(body: BASE.TreeInfoQuery, options?: { [key: string]: any }) {
  return request<BASE.IResultListTreeInfoVO>(`/api/test/treeInfo/tree`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据树code获取树形结构数据 POST /treeInfo/tree/byCode */
export async function treeInfoTreeByCode(body: BASE.CodeDTO, options?: { [key: string]: any }) {
  return request<BASE.IResultListTreeInfoVO>(`/api/test/treeInfo/tree/byCode`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新通用树数据 POST /treeInfo/update */
export async function treeInfoUpdate(body: BASE.TreeInfo, options?: { [key: string]: any }) {
  return request<BASE.IResult>(`/api/test/treeInfo/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
