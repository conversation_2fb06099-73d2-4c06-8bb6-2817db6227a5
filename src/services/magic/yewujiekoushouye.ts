// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 预警信息接口 预警信息接口 GET /basicCompany/getEarlyWarnInfoByYear */
export async function basicCompanyGetEarlyWarnInfoByYear(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: MAGIC.basicCompanyGetEarlyWarnInfoByYearParams,
  options?: { [key: string]: any },
) {
  return request<MAGIC.__getEarlyWarnInfoByYearResponseRoot_>(
    '/basicCompany/getEarlyWarnInfoByYear',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
