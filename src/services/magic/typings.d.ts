declare namespace MAGIC {
  type __getEarlyWarnInfoByYearResponseRoot_ = {
    message?: string;
    data?: __getEarlyWarnInfoByYearResponseRoot_data_[];
    success?: boolean;
    status?: number;
    timestamp?: number;
  };

  type __getEarlyWarnInfoByYearResponseRoot_0_ = {
    message?: string;
    data?: __getEarlyWarnInfoByYearResponseRoot_data_2_[];
    success?: boolean;
    status?: number;
    timestamp?: number;
  };

  type __getEarlyWarnInfoByYearResponseRoot_data_ = {
    /** 11月 */
    november?: number;
    /** 6月 */
    june?: number;
    /** 9月 */
    september?: number;
    /** 5月 */
    may?: number;
    /** 8月 */
    august?: number;
    /** 1月 */
    january?: number;
    /** 2月 */
    february?: number;
    /** 7月 */
    july?: number;
    /** 12月 */
    december?: number;
    /** 10月 */
    october?: number;
    /** 4月 */
    april?: number;
    /** 3月 */
    march?: number;
  };

  type __getEarlyWarnInfoByYearResponseRoot_data_2_ = {
    /** 11月 */
    november?: number;
    /** 6月 */
    june?: number;
    /** 9月 */
    september?: number;
    /** 5月 */
    may?: number;
    /** 8月 */
    august?: number;
    /** 1月 */
    january?: number;
    /** 2月 */
    february?: number;
    /** 7月 */
    july?: number;
    /** 12月 */
    december?: number;
    /** 10月 */
    october?: number;
    /** 4月 */
    april?: number;
    /** 3月 */
    march?: number;
  };

  type basicCompanyGetEarlyWarnInfoByYearParams = {
    /** 年份 */
    year: number;
  };
}
