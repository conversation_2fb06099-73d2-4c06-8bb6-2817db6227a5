// 主色调
@primary: #1890ff;

/** 成功 */
@success: #52c41a;

/** 失败 */
@danger: #f5222d;

/** 警告 */
@warning: #faad14;

/** 字体基础色 */
@color-base: #1a1a1a;

// antd 主题色
@primary-color: @primary;

// Media queries breakpoints
// @screen-xs and @screen-xs-min is not used in Grid
// smallest break point is @screen-md
@screen-xs: 480px;
@screen-xs-min: @screen-xs;
// 👆 Extra small screen / phone

// 👇 Small screen / tablet
@screen-sm: 576px;
@screen-sm-min: @screen-sm;

// Medium screen / desktop
@screen-md: 768px;
@screen-md-min: @screen-md;

// Large screen / wide desktop
@screen-lg: 992px;
@screen-lg-min: @screen-lg;

// Extra large screen / full hd
@screen-xl: 1200px;
@screen-xl-min: @screen-xl;

// Extra extra large screen / large desktop
@screen-xxl: 1600px;
@screen-xxl-min: @screen-xxl;

// provide a maximum
@screen-xs-max: (@screen-sm-min - 1px);
@screen-sm-max: (@screen-md-min - 1px);
@screen-md-max: (@screen-lg-min - 1px);
@screen-lg-max: (@screen-xl-min - 1px);
@screen-xl-max: (@screen-xxl-min - 1px);

// @phone: ~'only screen and (max-width: @{screen-xs-min})';
@phone: ~'only screen and (max-width: @{screen-lg-min})';
@phone-strict: ~'only screen and (min-width: @{screen-xs-min}) and (max-width: @{screen-xs-max})';
@tablet: ~'only screen and (min-width: @{screen-sm-min})';
@tablet-strict: ~'only screen and (min-width: @{screen-sm-min}) and (max-width: @{screen-sm-max})';
@desktop: ~'only screen and (min-width: @{screen-md-min})';
@desktop-strict: ~'only screen and (min-width: @{screen-md-min}) and (max-width: @{screen-md-max})';
@large: ~'only screen and (min-width: @{screen-lg-min})';
