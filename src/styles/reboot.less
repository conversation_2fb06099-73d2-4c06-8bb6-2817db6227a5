@import 'variables';
@import 'mixin';

* {
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  color: @color-base;
  font-family: 'Helvetica Neue', 'Luxi Sans', '<PERSON>ja<PERSON><PERSON>s', <PERSON><PERSON><PERSON>, 'Hiragino Sans GB', STHeiti,
    'Microsoft YaHei';
  line-height: 1.5;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// ::-webkit-scrollbar {
//   display: none;
// }

// 弹窗高度自适应
.ant-modal-body {
  // max-height: 400px;
  max-height: calc(100vh - 306px);
  overflow-x: hidden;
  overflow-y: auto;

  @media (max-height: 768px) {
    max-height: 400px;
  }
}
// end 弹窗高度自适应


.ant-pro-table-extra {
  display: none;
}

// 下拉框如果选项是Tag，那么Tag的高度和下拉框的高度一致，这里去除line-height属性，让Tag上下居中
.ant-select-selection-item {
  display: flex;
  align-items: center;
  line-height: unset !important;
}
