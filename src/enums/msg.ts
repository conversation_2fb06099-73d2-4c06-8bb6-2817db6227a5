/** ===== 公告 ===== */

/** 公告状态 */
export enum NoticeStatus {
  草稿 = 0,
  已发布 = 1,
  已过期 = 2,
  已下架 = 3,
  定时发布中 = 4,
}

/** 公告状态 */
export const NoticeStatusObj: ValueEnumMap<number> = new Map([
  [
    NoticeStatus.草稿,
    {
      text: '草稿',
      color: 'orange',
    },
  ],
  [
    NoticeStatus.已发布,
    {
      text: '已发布',
      color: 'green',
    },
  ],
  [
    NoticeStatus.已过期,
    {
      text: '已过期',
      color: 'red',
    },
  ],
  [
    NoticeStatus.已下架,
    {
      text: '已下架',
      // color: 'grey',
    },
  ],
  [
    NoticeStatus.定时发布中,
    {
      text: '定时发布中',
      color: 'yellow',
    },
  ],
]);

/** 公告已读状态 */
export enum NoticeReadStatus {
  未读 = 0,
  已读 = 1,
}

/** 公告已读状态 */
export const NoticeReadStatusObj: ValueEnumMap<number> = new Map([
  [
    NoticeReadStatus.未读,
    {
      text: '未读',
    },
  ],
  [
    NoticeReadStatus.已读,
    {
      text: '已读',
    },
  ],
]);

/** 公告接收类型 */
export enum NoticeReceiveType {
  所有 = 0,
  指定用户 = 1,
  指定角色 = 2,
  指定部门 = 3,
  指定用户类型 = 4,
}

/** 公告接收类型 */
export const NoticeReceiveTypeObj: ValueEnumMap<number> = new Map([
  [
    NoticeReceiveType.所有,
    {
      text: '所有',
    },
  ],
  [
    NoticeReceiveType.指定用户,
    {
      text: '指定用户',
    },
  ],
  [
    NoticeReceiveType.指定角色,
    {
      text: '指定角色',
    },
  ],
  [
    NoticeReceiveType.指定部门,
    {
      text: '指定部门',
    },
  ],
  [
    NoticeReceiveType.指定用户类型,
    {
      text: '指定用户类型',
    },
  ],
]);

/** 公告内容类型 */
export enum NoticeContentType {
  富文本 = 'rich',
  文本 = 'text',
}

/** 公告内容类型 */
export const NoticeContentTypeObj: ValueEnumMap<string> = new Map([
  [
    NoticeContentType.富文本,
    {
      text: '富文本',
    },
  ],
  [
    NoticeContentType.文本,
    {
      text: '文本',
    },
  ],
]);

/** ===== end 公告 ===== */

/** ===== 待办 ===== */

/** 待办状态 */
export enum BackLogStatus {
  待处理 = 0,
  忽略 = 1,
  已处理 = 2,
  已过期 = 3,
}

/** 待办状态 */
export const BackLogStatusObj: ValueEnumMap<number> = new Map([
  [
    BackLogStatus.待处理,
    {
      text: '待处理',
      color: 'red',
    },
  ],
  [
    BackLogStatus.忽略,
    {
      text: '忽略',
    },
  ],
  [
    BackLogStatus.已过期,
    {
      text: '已过期',
      color: 'orange',
    },
  ],
  [
    BackLogStatus.已处理,
    {
      text: '已处理',
      color: 'blue',
    },
  ],
]);

/** ===== end 待办 ===== */

/** ===== 通知 ===== */

/** 通知状态 */
export enum NotifyStatus {
  未读 = 0,
  已读 = 1,
}

/** 通知状态 */
export const NotifyStatusObj: ValueEnumMap<number> = new Map([
  [
    NotifyStatus.未读,
    {
      text: '未读',
      color: 'red',
    },
  ],
  [
    NotifyStatus.已读,
    {
      text: '已读',
    },
  ],
]);

/** ===== end 通知 ===== */
