import colors, { color } from '@/config/color';

/** 前端权限类型 */
export enum FeAuthType {
  /** 端 */
  端 = 1,
  /** 模块 */
  模块 = 2,
  /** 菜单 */
  菜单 = 3,
  /** 按钮 */
  按钮 = 4,
}

/** 前端权限类型 */
export const FeAuthTypeObj: ValueEnumMap<number> = new Map([
  [
    FeAuthType.端,
    {
      text: '端',
      color: color.red,
    },
  ],
  [
    FeAuthType.模块,
    {
      text: '模块',
      color: color.orange,
    },
  ],
  [
    FeAuthType.菜单,
    {
      text: '菜单',
      color: color.blue,
    },
  ],
  [
    FeAuthType.按钮,
    {
      text: '按钮',
      color: color.green,
    },
  ],
]);

/** 前端权限状态 */
export enum FeAuthStatus {
  /** 不需要验证 */
  不需要验证 = 0,
  /** 需要登录 */
  需要登录 = 1,
  /** 需要验证 */
  需要验证 = 2,
}

/** 前端权限状态 */
export const FeAuthStatusObj: ValueEnumMap<number> = new Map([
  [
    FeAuthStatus.不需要验证,
    {
      text: '不需要验证',
      color: color.green,
    },
  ],
  [
    FeAuthStatus.需要登录,
    {
      text: '需要登录',
      color: color.orange,
    },
  ],
  [
    FeAuthStatus.需要验证,
    {
      text: '需要验证',
      color: color.red,
    },
  ],
]);

/** 后端权限类型 */
export enum BeAuthType {
  /** 接口 */
  接口 = 1,
  /** 模块 */
  模块 = 2,
  /** 服务 */
  服务 = 3,
}

/** 后端权限类型 */
export const BeAuthTypeObj: ValueEnumMap<number> = new Map([
  [
    BeAuthType.接口,
    {
      text: '接口',
      color: colors[0],
    },
  ],
  [
    BeAuthType.模块,
    {
      text: '模块',
      color: colors[1],
    },
  ],
  [
    BeAuthType.服务,
    {
      text: '服务',
      color: colors[2],
    },
  ],
]);

/** 后端权限日志级别 */
export enum BeAuthLevel {
  /** 正常 */
  正常 = 1,
  /** 敏感 */
  敏感 = 2,
  /** 危险 */
  危险 = 3,
}

/** 后端权限日志级别 */
export const BeAuthLevelObj: ValueEnumMap<number> = new Map([
  [
    BeAuthLevel.正常,
    {
      text: '正常',
      color: color.green,
    },
  ],
  [
    BeAuthLevel.敏感,
    {
      text: '敏感',
      color: color.orange,
    },
  ],
  [
    BeAuthLevel.危险,
    {
      text: '危险',
      color: color.red,
    },
  ],
]);

/** 后端权限状态 */
export enum BeAuthStatus {
  /** 不需要验证 */
  不需要验证 = 0,
  /** 需要登录 */
  需要登录 = 1,
  /** 需要验证 */
  需要验证 = 2,
}

/** 后端权限状态 */
export const BeAuthStatusObj: ValueEnumMap<number> = new Map([
  [
    BeAuthStatus.不需要验证,
    {
      text: '不需要验证',
      color: color.green,
    },
  ],
  [
    BeAuthStatus.需要登录,
    {
      text: '需要登录',
      color: color.orange,
    },
  ],
  [
    BeAuthStatus.需要验证,
    {
      text: '需要验证',
      color: color.red,
    },
  ],
]);

/** 后端权限请求方式 */
export enum BeAuthMethod {
  /** POST */
  POST = 'POST',
  /** GET */
  GET = 'GET',
  /** DELETE */
  DELETE = 'DELETE',
  /** PUT */
  PUT = 'PUT',
}

/** 后端权限请求方式 */
export const BeAuthMethodObj: ValueEnumMap<string> = new Map([
  [
    BeAuthMethod.POST,
    {
      text: 'POST',
      color: color.green,
    },
  ],
  [
    BeAuthMethod.GET,
    {
      text: 'GET',
      color: color.orange,
    },
  ],
  [
    BeAuthMethod.DELETE,
    {
      text: 'DELETE',
      color: color.red,
    },
  ],
  [
    BeAuthMethod.PUT,
    {
      text: 'PUT',
      color: color.purple,
    },
  ],
]);

/** 数据权限where值类型 */
export enum DataAuthWhereType {
  /** 值 */
  值 = 0,
  /** 参数 */
  参数 = 1,
  /** 表达式 */
  表达式 = 2,
}

/** where值类型 */
export const DataAuthWhereTypeObj: ValueEnumMap<number> = new Map([
  [
    DataAuthWhereType.值,
    {
      text: '值',
      status: color.red,
    },
  ],
  [
    DataAuthWhereType.参数,
    {
      text: '参数',
      status: color.blue,
    },
  ],
  [
    DataAuthWhereType.表达式,
    {
      text: '表达式',
      status: color.green,
    },
  ],
]);
