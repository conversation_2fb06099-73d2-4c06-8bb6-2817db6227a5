import colors from '@/config/color';

/** 配置类型 */
export enum SysCfgType {
  /** int */
  int = 1,
  /** long */
  long = 2,
  /** string */
  string = 3,
  /** date */
  date = 4,
  /** bool */
  bool = 5,
  /** file */
  file = 6,
  /** picture */
  picture = 7,
  /** colour */
  colour = 8,
  /** 多文件 */
  files = 9,
  /** 多图片 */
  pictures = 10,
  /** 时间范围 */
  scopeDate = 11,
  /** 长文本字符串 */
  text = 12,
}

/** 配置类型 */
export const SysCfgTypeObj: ValueEnumMap<number> = new Map([
  [
    SysCfgType.string,
    {
      text: '短字符串',
      status: colors[2 % colors.length],
    },
  ],
  [
    SysCfgType.text,
    {
      text: '长文本',
      status: colors[11 % colors.length],
    },
  ],

  [
    SysCfgType.int,
    {
      text: '短整型',
      status: colors[0 % colors.length],
    },
  ],
  [
    SysCfgType.long,
    {
      text: '长整型',
      status: colors[1 % colors.length],
    },
  ],

  [
    SysCfgType.bool,
    {
      text: '布尔',
      status: colors[4 % colors.length],
    },
  ],

  [
    SysCfgType.date,
    {
      text: '时间',
      status: colors[3 % colors.length],
    },
  ],
  [
    SysCfgType.scopeDate,
    {
      text: '时间范围',
      status: colors[10 % colors.length],
    },
  ],

  [
    SysCfgType.picture,
    {
      text: '图片',
      status: colors[6 % colors.length],
    },
  ],
  [
    SysCfgType.pictures,
    {
      text: '多图片',
      status: colors[9 % colors.length],
    },
  ],

  [
    SysCfgType.file,
    {
      text: '文件',
      status: colors[5 % colors.length],
    },
  ],
  [
    SysCfgType.files,
    {
      text: '多文件',
      status: colors[8 % colors.length],
    },
  ],

  [
    SysCfgType.colour,
    {
      text: '颜色',
      status: colors[7 % colors.length],
    },
  ],
]);

/** 环境 */
export enum SysCfgEnv {
  后端 = 1,
  前端 = 2,
}

/** 环境 */
export const SysCfgEnvObj: ValueEnumMap<number> = new Map([
  [
    SysCfgEnv.后端,
    {
      text: '后端',
      status: colors[0],
    },
  ],
  [
    SysCfgEnv.前端,
    {
      text: '前端',
      status: colors[1],
    },
  ],
]);
