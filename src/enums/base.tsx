/** 通用状态 */
export enum Status {
  启用 = 1,
  禁用 = 0,
}
export const StatusObj: ValueEnumMap<number> = new Map([
  [
    Status.启用,
    {
      text: '启用',
      color: 'blue',
    },
  ],
  [
    Status.禁用,
    {
      text: '禁用',
      color: 'red',
    },
  ],
]);

/** 筛选类型 精确或者模糊 针对文本输入筛选项 */
export enum SearchType {
  /** 精确 */
  精确 = 0,
  /** 模糊 */
  模糊 = 1,
}

/** 数值布尔 */
export enum NumBool {
  是 = 1,
  否 = 0,
}
export const NumBoolObj: ValueEnumMap<number> = new Map([
  [
    NumBool.是,
    {
      text: '是',
      color: 'blue',
    },
  ],
  [
    NumBool.否,
    {
      text: '否',
      color: 'red',
    },
  ],
]);

export const BoolObj = new Map([
  [true, '是'],
  [false, '否'],
]);

/** 时间排序 */
export enum TimeSort {
  /** 倒序 */
  倒序 = 1,
  /** 正序 */
  正序 = 0,
}

/** id排序 */
export enum IdSort {
  /** 倒序 */
  倒序 = 1,
  /** 正序 */
  正序 = 0,
}

/** 性别 */
export enum Sex {
  男 = 1,
  女 = 0,
}
export const SexObj: ValueEnumMap<number> = new Map([
  [
    Sex.男,
    {
      text: '男',
      color: 'blue',
    },
  ],
  [
    Sex.女,
    {
      text: '女',
      color: 'red',
    },
  ],
]);

export enum ROLE_TYPE {
  // 超级管理员 = 1
  superAdmin = '1',
  // 普通用户 = 2
  normal = '2',
  // 受理人员 = 3
  acceptor = '3',
  // 项目经理 = 4
  projectManager = '4',
  // 测试主管 = 5
  testManager = '5',
  // 报告审核人 = 6
  reportReviewer = '6',
  // 报告批准人 = 7
  reportApprover = '7',
  // 测试人员 = 8
  testPerson = '8',
  // 销售人员 = 9
  salesPerson = '9',
  // 市场主管
  marketDirector = '10',
}

/** 角色名称映射 */
export enum ROLE_NAME_TYPE {
  // 超级管理员 = 1
  超级管理员 = '1',
  // 普通用户 = 2
  普通用户 = '2',
  // 受理人员 = 3
  受理人员 = '3',
  // 项目经理 = 4
  项目经理 = '4',
  // 测试主管 = 5
  测试主管 = '5',
  // 报告审核人 = 6
  报告审核人 = '6',
  // 报告批准人 = 7
  报告批准人 = '7',
  // 测试人员 = 8
  测试人员 = '8',
  // 销售人员 = 9
  销售人员 = '9',
  // 市场主管
  市场主管 = '10',
}

/** 角色名称映射 */
export const ROLENAMEobj: Record<string, string> = {
  '1': '超级管理员',
  '2': '普通用户',
  '3': '受理人员',
  '4': '项目经理',
  '5': '测试主管',
  '6': '报告审核人',
  '7': '报告批准人',
  '8': '测试人员',
  '9': '销售人员',
  '10': '市场主管',
};
