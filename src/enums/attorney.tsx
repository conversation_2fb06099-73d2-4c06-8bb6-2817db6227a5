import { Tag } from 'antd';

/** 模板类型 */
export enum SYSTEM_TEMPLATE_TYPE {
  attorney = '0',
  record = '1',
  report = '2',
}
export const SYSTEM_TEMPLATE_TYPE_TEXT: { [k: string]: string } = {
  [SYSTEM_TEMPLATE_TYPE.attorney]: '委托书',
  [SYSTEM_TEMPLATE_TYPE.record]: '原始记录',
  [SYSTEM_TEMPLATE_TYPE.report]: '报告',
};
/** end模板类型 */

// 委托书类型
export enum ATTORNEY_TYPE {
  // 普通
  normal = 1,
  // 天线
  antenna = 2,
  // 电磁辐射
  radiation = 3,
  // 电磁环境
  environment = 4,
  // 软件测试
  softtest = 5,
  // 型号核准
  model = 6,
  // 进网委托
  enter = 7,
}

export const ATTORNEY_TYPE_TEXT = {
  [ATTORNEY_TYPE.normal]: '通用',
  [ATTORNEY_TYPE.antenna]: '天线',
  [ATTORNEY_TYPE.radiation]: '电磁辐射',
  [ATTORNEY_TYPE.environment]: '电磁环境',
  [ATTORNEY_TYPE.softtest]: '软件测试',
  [ATTORNEY_TYPE.model]: '型号核准',
  [ATTORNEY_TYPE.enter]: '进网委托',
};

export const ATTORNEY_TYPE_MAP = new Map([
  [ATTORNEY_TYPE.normal, '通用'],
  [ATTORNEY_TYPE.antenna, '天线'],
  [ATTORNEY_TYPE.radiation, '电磁辐射'],
  [ATTORNEY_TYPE.environment, '电磁环境'],
  [ATTORNEY_TYPE.softtest, '软件测试'],
  [ATTORNEY_TYPE.model, '型号核准'],
  [ATTORNEY_TYPE.enter, '进网委托'],
]);

/** 委托书状态 待提交、完善核准信息、测试主管审核、已审核、已撤销 */
export enum ATTORNEY_STATUS {
  /** 已审核 */
  approval = '已审核',
  /** 未审核 */
  noApproval = '测试主管审核',
  /** 未提交 */
  back = '待提交',
  /** 撤销 */
  cancel = '已撤销',
  /** 完善核准信息 */
  supple = '完善核准信息',
  /** 上传资料 */
  upload = '上传资料',
  /** 已完成 */
  finish = '已完成',

  lock = '委托书修改审核中'

}

export const ATTORNEY_STATUS_TEXT: { [k: string]: string } = {
  /** 正常 */
  [ATTORNEY_STATUS.approval]: '已审核',
  /** 禁用 */
  [ATTORNEY_STATUS.noApproval]: '待审核',
  [ATTORNEY_STATUS.back]: '未提交',
  [ATTORNEY_STATUS.cancel]: '已撤销',
};

export const ATTORNEY_STATUS_NODE: { [k: string]: React.ReactNode } = {
  /** 正常 */
  [ATTORNEY_STATUS.approval]: <Tag color="green">{ATTORNEY_STATUS.approval}</Tag>,
  /** 禁用 */
  [ATTORNEY_STATUS.noApproval]: <Tag color="red">{ATTORNEY_STATUS.noApproval}</Tag>,
  [ATTORNEY_STATUS.back]: <Tag color="gray">{ATTORNEY_STATUS.back}</Tag>,
  [ATTORNEY_STATUS.cancel]: <Tag color="volcano-inverse">{ATTORNEY_STATUS.cancel}</Tag>,
  [ATTORNEY_STATUS.supple]: <Tag color="#3b5999">{ATTORNEY_STATUS.supple}</Tag>,
  [ATTORNEY_STATUS.upload]: <Tag color="orange">{ATTORNEY_STATUS.upload}</Tag>,
  [ATTORNEY_STATUS.finish]: <Tag color="green">{ATTORNEY_STATUS.finish}</Tag>,
  [ATTORNEY_STATUS.lock]: <Tag color='cyan'>{'修改审核中'}</Tag>,
};
/** end 委托书状态 */



export enum ENTRUST_STATUS {
  '已完成' = 1,
  '进行中' = 0,
}