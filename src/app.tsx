import Footer from '@/components/footer';
import RightContent from '@/components/rightContent';
import { loginLogout, loginSelf } from '@/services/base/authLogin';
import { dicInfoAll } from '@/services/base/baseDict';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { history, Link, RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { ConfigProvider, Image } from 'antd';
import { Portal } from 'antd-pro-crud';
import zhCN from 'antd/lib/locale/zh_CN';
// import { ClickToComponent } from 'click-to-react-component';
import dayjs from 'dayjs';

import 'dayjs/locale/zh-cn';
import duration from 'dayjs/plugin/duration';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import relativeTime from 'dayjs/plugin/relativeTime';
import lodash, { groupBy, keyBy } from 'lodash';
import { stringify } from 'querystring';
import React, { createContext } from 'react';
import { autoFixContext, fixContext } from 'react-activation';
import { flatRoutes, whitePath } from '../config/routes';
import access from './access';
import KeepAlive from './components/keepAlive';
import TabsLayout from './components/tabsLayout';
import { errorConfig } from './requestErrorConfig';
import { systemConfigFront } from './services/base/baseCfg';
import { dicInfoPage } from './services/base/baseDict';
import { parseSrc } from './utils';
import { ValueTypeProvider } from './valueType';
dayjs.locale('zh-CN');
dayjs.extend(relativeTime);
dayjs.extend(quarterOfYear);
dayjs.extend(duration);

const Context = createContext({});

fixContext(Context);

autoFixContext(
  [require('react/jsx-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
  [require('react/jsx-dev-runtime'), 'jsx', 'jsxs', 'jsxDEV'],
);

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';
/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<typeof getInitialState.state> {
  // 获取系统配置,例如logo/标题/版权等信息
  const sysConfig = await systemConfigFront({
    skipErrorHandler: true,
  });
  const configMap = keyBy(sysConfig.data, 'configCode');
  getInitialState.state.config = configMap;

  // 如果不是登录页面，执行
  const { location } = history;

  if (location.pathname !== loginPath && !whitePath.some((regex) => regex.test(location.pathname))) {
    await getInitialState.state.fetchUserInfo();
    // await getInitialState.state.fetchDict();

    return getInitialState.state;
  }

  return getInitialState.state;
}

/**
 * 全局store,相当于简化版redux
 * 1. 是否已登录
 * 2. 当前登录用户信息
 * 3. 角色
 * 4. 权限信息
 * 5. 公共字典
 */
getInitialState.state = {
  /** 当前登录用户 */
  currentUser: null as null | IUser | undefined,
  /** 获取用户信息 */
  fetchUserInfo: async (): Promise<null | IUser> => {
    try {
      const msg = await loginSelf({
        // skipErrorHandler: true,
      });

      if (!msg.data) {
        throw new Error('获取用户信息失败，请重新登录');
      }

      getInitialState.state.currentUser = msg.data as IUser;
      getInitialState.state.roles = msg.data.roles || [];
      getInitialState.state.permissions = msg.data.pages || [];
      getInitialState.state.redirectMap = (() => {
        /** 找到第一个叶子节点 */
        function getFirstLeafRoute(routes: RouteData[]): null | RouteData {
          for (const item of routes) {
            if (!item.routes) {
              return item;
            }
          }

          return null;
        }

        // 引用权限方法
        const renderAccess = access(getInitialState.state);
        /** 重定向地址 */
        const redirectMap: Record<string, string> = {};

        // 所有有权限的路由
        const authRouteList = flatRoutes.filter((route) => {
          /**
           * 1. 非白名单路由
           * 2. 要有实际页面
           * 3. 权限校验通过
           */
          return (
            !whitePath.some((v) => v.test(route.path!)) &&
            route.path &&
            renderAccess.routeAccess(route)
          );
        });

        // 获取非叶子节点重定向路径
        authRouteList.forEach((route) => {
          // 过滤没有配置路径的组件
          if (!route.path) {
            return;
          }

          // 过滤叶子节点
          if (!route.routes?.length) {
            return;
          }

          const firstAuthRoute = route.routes.find(
            (cRoute) => cRoute.path && renderAccess.routeAccess(cRoute) && !cRoute.hideInMenu,
          );

          if (firstAuthRoute) {
            redirectMap[route.path] = firstAuthRoute.path!;
          }
        });

        const firstLeafAuth = getFirstLeafRoute(authRouteList);

        if (firstLeafAuth) {
          redirectMap['/'] = firstLeafAuth.path!;
        }
        return redirectMap;
      })();

      // 初始化字典
      const dict = await dicInfoAll();
      const dictMap = groupBy(dict.data, 'dicCode');

      getInitialState.state.dict = dictMap as Record<string, BASE.DicInfoVO[]>;
      // end 初始化字典

      return msg.data as IUser;
    } catch (error) {
      localStorage.removeItem('token');
      history.push(loginPath);
    }

    return null;
  },
  /** 获取字典 */
  fetchDict: async () => {
    try {
      const dict = await dicInfoPage(
        {
          page: 1,
          size: 9999,
          dicStatus: 1,
        },
        {
          skipErrorHandler: true,
          useCache: true,
          validateCache: (url: any, options: any) => {
            return options.method.toLowerCase() === 'post';
          },
        },
      );
      const dictArr = dict?.data?.records || [];

      const dictObj = lodash.groupBy(dictArr, 'dicMainName');

      getInitialState.state.dicts = dictObj;

      return dictObj;
    } catch (error) {}

    return null;
  },
  /** 退出 */
  logout: async (
    // 是否token失效
    isTokenInvalid = false,
  ) => {
    getInitialState.state.permissions = [];
    getInitialState.state.roles = [];
    getInitialState.state.currentUser = null;

    if (!isTokenInvalid) {
      try {
        await loginLogout({
          skipErrorHandler: true,
        });
      } catch (e) {}
    }

    localStorage.removeItem('token');

    const { search, pathname } = window.location;
    const urlParams = new URL(window.location.href).searchParams;
    /** 此方法会跳转到 redirect 参数所在的位置 */
    const redirect = urlParams.get('redirect');
    // Note: There may be security issues, please note
    if (window.location.pathname !== '/user/login' && !redirect) {
      history.replace({
        pathname: '/user/login',
        search: stringify({
          redirect: pathname + search,
        }),
      });
    }
  },
  /** 角色 */
  roles: [] as string[],
  /** 权限 */
  permissions: [] as string[],
  /** 字典 */
  dict: {} as Record<string, BASE.DicInfoVO[]>,
  /** 系统配置 */
  config: {} as Record<string, BASE.SystemConfig>,
  /** 过滤叶子节点重定向地址 */
  redirectMap: {} as Record<string, string>,
  settings: {} as Partial<LayoutSettings>,
  /** 字典 */
  dicts: {} as Record<string, BASE.DicInfoVO[]>,
};

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    // 项目标题
    title: initialState?.config?.['title']?.configValue ?? APP_TITLE,
    // 项目logo
    // logo: parseSrc(initialState?.config?.['logo']?.configValue) ?? require('@/assets/img/logo.png'),
    // logo: (
    //   <Image
    //     src={
    //       require('@/assets/img/caict.png')
    //     }
    //     fallback={require('@/assets/img/caict.png')}
    //     preview={false}
    //   />
    // ),
    // 颜色主题
    logo: parseSrc(initialState?.config?.['logo']?.configValue + `?token=${localStorage.getItem('token')}`),
    colorPrimary: initialState?.config?.['theme']?.configValue,
    layout: 'top',
    splitMenus: false,
    headerTheme: 'light',
    navTheme: 'light',
    fixedHeader: true,
    fixSiderbar: true,
    pageTitleRender: false,
    breadcrumbRender: false,
    token: {
      bgLayout: '#f5f5f5',
      sider: {
        colorMenuBackground: '#fff',
      },
      header: {
        colorBgHeader: 'rgb(0, 101, 105) !important',
        colorHeaderTitle: '#fff',
        colorTextMenu: 'rgba(255, 255, 255, 0.85)',
        colorTextMenuActive: '#fff',
        colorTextMenuSelected: '#fff',
        colorBgMenuItemHover: 'rgb(0, 101, 105, 0.6)',
        colorBgMenuItemSelected: 'rgb(0, 101, 105, 1)',
      },
      pageContainer: {
        paddingInlinePageContainerContent: 20,
        paddingBlockPageContainerContent: 16,
      },
      table:{
        rowSelectedBg:'#f00',
        rowHoverBg:'#f00',
        rowSelectedHoverBg:'#f00'
      }
    },
    rightContentRender: () => (
      <>
        <RightContent />
        <Portal />
      </>
    ),
    /** 全局tab导航 */
    headerRender: (props, defaultDom) => {
      return (
        <>
          {defaultDom}
          <TabsLayout props={props} />
        </>
      );
    },
    footerRender: () => <Footer />,
    layoutBgImgList: [],
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI 文档</span>
          </Link>,
        ]
      : [],
    // waterMarkProps: {
    //   content: initialState?.currentUser?.fullName,
    // },
    menu: {
      autoClose: false,
      ignoreFlatMenu: true,
    },
    // menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    // childrenRender: (children) => {
    //   return (
    //     <>
    //       {children}
    //       <SettingDrawer
    //         disableUrlParams
    //         enableDarkTheme
    //         settings={initialState?.settings}
    //         onSettingChange={(settings) => {
    //           setInitialState((preInitialState) => ({
    //             ...(preInitialState as typeof getInitialState.state),
    //             settings,
    //           }));
    //         }}
    //       />
    //     </>
    //   );
    // },
    ...initialState?.settings,
  };
};

/**
 * 1. 校验用户登录情况
 * 2. 重定向非叶子节点路由
 */
export async function onRouteChange({ location }: { location: Location }) {
  if (!location?.pathname) {
    return;
  }

  const isLogin = !!localStorage.getItem('token');
  const isWhitePath = location.pathname && whitePath.some((v) => v.test(location.pathname));

  // 过滤白名单
  if (isWhitePath) {
    return;
  }

  // 未登录
  if (!isLogin) {
    // 重定向到登录页面,且携带当前页面地址,待登录后重定向
    const search =
      location.pathname !== '/'
        ? '?' +
          stringify({
            r: location.pathname,
          })
        : undefined;

    history.push({
      pathname: loginPath,
      search,
    });

    return;
  }

  // 重定向路由
  const redirect = getInitialState.state.redirectMap[location.pathname];

  if (redirect) {
    history.replace(redirect);
  }
}

const PageWraper = ({ children, routes }: any) => {
  return (
    <>
      <ConfigProvider locale={zhCN} theme={{ token: { colorTextDisabled: 'rgba(0,0,0,0.65)' } }}>
        <KeepAlive keepalive={['/report','/attorney','/task/list','/task/plan', '/task/sample','/attorney']}>
          <ValueTypeProvider>
            {React.cloneElement(
              children,
              {
                ...children.props,
                routes,
              },
              children.props.children,
            )}
          </ValueTypeProvider>
        </KeepAlive>
      </ConfigProvider>
    </>
  );
};

/**
 * 修改交给 react-dom 渲染时的根组件
 * 1. 路由,不包裹一层弹窗获取不到路由信息
 * 2. 全局ValueType, 用于自定义表单渲染和字段渲染
 * https://umijs.org/zh-CN/docs/runtime-config#rootcontainerlastrootcontainer-args
 */
export function rootContainer(container: React.ReactElement, args: Record<string, any>) {
  return React.createElement(PageWraper, null, container);
}

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  ...errorConfig,
};
