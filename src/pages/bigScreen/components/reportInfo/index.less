.big-screen-report-info {
  width: 598px;
  height: 525px;
  background-color: transparent;
  border-radius: 6px;
  padding: 20px;
  background: url('@/assets/bigscreen/task_top_bg.png') no-repeat center center;
  background-size: 100% 100%;
  .big-screen-report-content {
    height: calc(100% - 40px);
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 12px;
    .big-screen-report-content-ecahrts {
      display: flex;
      gap: 16px;
      .big-screen-report-content-ecahrts-item {
        width: 50%;
        height: 189px;
        border-radius: 6px;
        border: 1px solid rgba(0,0,0,0.06);
        padding: 16px;
        #task-status-chart {
          height: calc(100% - 24px);
          overflow: hidden;
          display: flex;
          .task-status-chart-item {
            width: calc(100% - 80px);
            height: 100%;
          }
          .task-status-chart-item-extra {
            width: 80px;
            padding-left: 16px;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: rgba(255,255,255,0.85);
            .task-status-chart-item-name {
              font-size: 14px;
              span {
                display: inline-block;
                width: 10px;
                height: 10px;
                margin-right: 8px;
                border-radius: 50%;
              }
            }
          }
        }
        #task-count-chart {
          height: calc(100% - 24px);
          overflow: hidden;
        }
        .big-screen-report-content-ecahrts-item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .big-screen-report-content-ecahrts-item-title-text {
            font-size: 16px;
            color: rgba(255,255,255,0.85);
          }
          .big-screen-report-content-ecahrts-item-title-extra {
            .ant-select {
              font-size: 12px;
            }
          }
        }
      }
    }
    .big-screen-report-content-table {
      height: calc(100% - 200px);
      .big-screen-report-content-table-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        margin-top: 0px;
        background: url('@/assets/bigscreen/sub_title_bg.png') no-repeat left center;
        background-size: contain;
        color: rgba(255,255,255,0.85);
        padding-left: 36px;
      }
      .big-screen-report-content-table-content {
        height: calc(100% - 50px);
        overflow: hidden;
        .ant-pro-table, .ant-table-wrapper,
        .ant-spin-nested-loading,
        .ant-spin-container,
        .ant-table,
        .ant-table-container {
          height: 100%;
        }
        .ant-table-thead {
          height: 48px;
          .ant-table-cell {
            height: 48px;
            color: rgba(255,255,255,0.4);
            background-color: transparent;
          }
        }
      }
    }
  }
}