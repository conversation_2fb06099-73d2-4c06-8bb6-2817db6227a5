import ProTable from '@ant-design/pro-table';
import { Select } from 'antd';
import ReactECharts from 'echarts-for-react';
import CommonTitle from '../commonTitle';
import './index.less';

interface ReportInfoProps {
  data: {
    title: string;
    reportData: {
      value: number;
      name: string;
      itemStyle?: {
        color: string;
      };
    }[];
    subjectArr: {
      title: string;
      value: number;
      max: number;
    }[];
    reportDataSource: {
      index: string;
      name: string;
      taskName: string;
      reportTime: string;
    }[];
  };
}

const ReportInfo: React.FC<any> = ({ data }) => {
  const { reportData = [], subjectArr = [], reportDataSource = [] } = data;
  // const [reportData, setReportData] = useState([
  //   {
  //     value: 2,
  //     name: '合格',
  //     itemStyle: { color: '#2BA471' },
  //   },
  //   {
  //     value: 1,
  //     name: '不合格',
  //     itemStyle: { color: '#FF4D4F' },
  //   },
  // ])
  // const [subjectArr, setSubjectArr] = useState([
  //   {
  //     value: 64,
  //     name: '材料',
  //     max: 100,
  //   },
  //   {
  //     value: 82,
  //     name: '线缆',
  //     max: 100,
  //   },
  //   {
  //     value: 78,
  //     name: '线圈',
  //     max: 100,
  //   },
  //   {
  //     value: 64,
  //     name: '开关',
  //     max: 100,
  //   },
  // ])

  // const [dataSource, setDataSource] = useState<any>([
  //   {
  //     index: '1',
  //     name: '变压器检测报告',
  //     taskName: 'T25031903',
  //     reportTime: dayjs('2025-3-20 11:02:00').format('YYYY-MM-DD HH:mm:ss'),
  //   },
  //   {
  //     index: '2',
  //     name: '高压开关柜检测报告',
  //     taskName: 'T25032004',
  //     reportTime: dayjs('2025-3-20 12:04:01').format('YYYY-MM-DD HH:mm:ss'),
  //   },
  // ]);

  const columns = [
    {
      title: '编号',
      dataIndex: 'index',
      ellipsis: true,
      width: 60,
    },
    {
      title: '检验报告名',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '对应任务',
      dataIndex: 'taskName',
      ellipsis: true,
    },
    {
      title: '报告生成时间',
      dataIndex: 'reportTime',
      valueType: 'dateTime',
      fieldProps: {
        format: 'YYYY-MM-DD HH:mm',
      },
      width: 138,
    },
  ];
  let max = 0;
  subjectArr.forEach((item: any) => {
    if (item.value > max) {
      max = item.value;
    }
  });
  return (
    <div className="big-screen-report-info">
      <CommonTitle title="报告汇总" />
      <div className="big-screen-report-content">
        <div className="big-screen-report-content-ecahrts">
          <div className="big-screen-report-content-ecahrts-item">
            <div className="big-screen-report-content-ecahrts-item-title">
              <div className="big-screen-report-content-ecahrts-item-title-text">报告状态统计</div>
              <div className="big-screen-report-content-ecahrts-item-title-extra">
                <Select
                  size="small"
                  style={{ width: 60 }}
                  defaultValue="周"
                  className="big-screen-select"
                >
                  <Select.Option value="周">周</Select.Option>
                  <Select.Option value="月">月</Select.Option>
                  <Select.Option value="年">年</Select.Option>
                </Select>
              </div>
            </div>
            <div id="task-status-chart">
              <div className="task-status-chart-item">
                <ReactECharts
                  option={{
                    legend: {
                      data: reportData.map((item: any) => item.name),
                      orient: 'vertical',
                      right: '10%',
                    },
                    // tooltip: {
                    //   trigger: 'axis',
                    //   axisPointer: {
                    //     type: 'shadow',
                    //   },
                    //   confine: true,
                    //   backgroundColor: 'rgba(0, 0, 0, 0.85)',
                    //   textStyle: {
                    //     color: 'rgba(255, 255, 255, 0.85)',
                    //   },
                    // },
                    grid: {
                      left: '3%',
                      right: '4%',
                      bottom: '3%',
                      top: '10%',
                      containLabel: true,
                    },
                    xAxis: {
                      type: 'category',
                      data: reportData.map((item: any) => item.name),
                    },
                    yAxis: {
                      type: 'value',
                      minInterval: 1,
                      min: 0,
                      // 强制显示0刻度
                      axisLabel: {
                        formatter: (value: any) => {
                          return value === 0 ? '0' : value;
                        },
                      },
                    },
                    series: [
                      {
                        type: 'bar',
                        barMaxWidth: 38,
                        data: reportData.map((item: any) => ({
                          ...item,
                          value: item.value + ' ',
                        })),
                      },
                    ],
                  }}
                  style={{
                    height: '100%',
                    overflow: 'hidden',
                  }}
                />
              </div>
              <div className="task-status-chart-item-extra">
                {reportData.map((item: any) => (
                  <div key={item.name}>
                    <div className="task-status-chart-item-name">
                      <span style={{ backgroundColor: item.itemStyle?.color }}></span>
                      {item.name}
                    </div>
                    <div style={{ fontSize: 13, paddingLeft: 12 }}>
                      {item.value}
                      <span style={{ fontSize: 12, marginLeft: 2 }}>条</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="big-screen-report-content-ecahrts-item">
            <div className="big-screen-report-content-ecahrts-item-title">
              <div className="big-screen-report-content-ecahrts-item-title-text">
                专业合格率统计
              </div>
            </div>
            <div id="task-count-chart">
              <ReactECharts
                option={{
                  legend: {
                    data: [""]
                  },
                  // tooltip: {
                  //   confine: true,
                  // },
                  radar: {
                    shape: 'circle',
                    indicator: subjectArr.map((item: any) => ({
                      ...item,
                      max: max,
                    })),
                    startAngle: 45,
                    splitNumber: 3,
                  },
                  series: [
                    {
                      type: 'radar',
                      symbolSize: 4,
                      title: '',
                      data: [
                        {
                          value: subjectArr.map((item: any) => item.value),
                          name: '',
                        },
                      ],
                    },
                  ],
                }}
                style={{
                  height: '100%',
                  overflow: 'hidden',
                }}
              />
            </div>
          </div>
        </div>
        <div className="big-screen-report-content-table">
          <div className="big-screen-report-content-table-title">
            <div className="big-screen-report-content-table-title-text">报告列表</div>
          </div>
          <div className="big-screen-report-content-table-content">
            <ProTable
              search={false}
              toolBarRender={false}
              columns={columns}
              dataSource={reportDataSource}
              pagination={false}
              rowKey="index"
              scroll={{ y: 'calc(100% - 48px)' }}
              rowClassName={() => 'custom-row'}
              className="big-screen-task-list-table"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportInfo;
