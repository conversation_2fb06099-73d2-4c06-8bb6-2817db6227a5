.big-screen-common-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  background-image: url('@/assets/bigscreen/total_bg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: left;

  .big-screen-common-title-text {
    font-size: 18px;
    font-weight: 600;
    color: rgba(255,255,255,0.8);
    padding-left: 60px;
    position: relative;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 16px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

