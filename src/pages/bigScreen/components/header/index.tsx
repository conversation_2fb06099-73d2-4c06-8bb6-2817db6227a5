import { DatePicker } from "antd";
import dayjs, { Dayjs } from 'dayjs';
import { useState } from "react";
import './header.less';
import { history } from '@umijs/max';

function Header() {
  const [time, setTime] = useState<Dayjs>(dayjs());
  return <div className="big-screen-header">
    <div></div>
    <div className="big-screen-header-title" onClick={() => {
      history.push('/home');
    }}>国网荆州供电公司物资质量监测中心透明管理模块</div>
    <div className="big-screen-header-time">
      {/* <span>时间</span>
      <DatePicker size="small" value={time} onChange={(value) => setTime(value)} className="big-screen-header-time-picker" /> */}
    </div>
  </div>;
}

export default Header;


