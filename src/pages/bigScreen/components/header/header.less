.big-screen-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  background-color: #fff;
  // border-bottom: 1px solid #f0f0f0;
  padding-bottom: 24px;
  background: url('@/assets/bigscreen/total_header.png') no-repeat center center;
  background-size: 100% 100%;
  >div {
    flex: 1;
  }
  .big-screen-header-title {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    // 背景色根据图片的颜色
    background: linear-gradient(180deg, #FFFFFF 100%,  #7BDDDB 0%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .big-screen-header-time {
    margin-right: 25px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;
    font-weight: 400;
    font-size: 16px;
    color: rgba(246,249,254,0.9);
    .big-screen-header-time-picker {
      background: transparent;
      color: #fff;
      border-image: linear-gradient(84deg, rgba(160, 195, 230, 0.35), rgba(54, 208, 242, 1)) 1 1;
      .ant-picker-suffix,
      .ant-picker-clear,
      .ant-picker-input-placeholder >input {
        color: #ffffffab;
      }
      
    }
  }
}
