import LivePlayer from '@/components/videoPlayer';
import ModalViewLive from '@/pages/config/monitor/modalLive';
import { buInstrumentCameraInfoList, buInstrumentCameraInfoStream } from '@/services/base/haikangweishishexiangtouguanlijiek<PERSON>';
import { ProTable } from '@ant-design/pro-components';
import { Select } from 'antd';
import { useEffect, useState } from 'react';
import CommonTitle from '../commonTitle';
import './index.less';

function Monitor({ data }: { data: any }) {
  const { monitorData: historyList = [] } = data;
  const [monitorList, setMonitorList] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [itemTar, setItemTar] = useState({});

  const getMonitorList = async () => {
    const res = await buInstrumentCameraInfoList();
    if (res.data) {
      setMonitorList(res.data || []);
    }
  };

  useEffect(() => {
    getMonitorList();
  }, []);

  // 表格列摄像头编号，摄像头名，工位，历史查看，操作
  const columns = [
    {
      title: '摄像头编号',
      dataIndex: 'cameraCode',
      key: 'cameraCode',
      ellipsis: true,
    },
    {
      title: '摄像头名',
      dataIndex: 'cameraName',
      key: 'cameraName',
      ellipsis: true,
    },
    {
      title: '工位',
      dataIndex: 'workstation',
      key: 'workstation',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      key: 'action',
      render: (_: any, record: any) => [
        <a
          key="play"
          style={{ color: '#fff' }}
          onClick={() => {
            setOpen(true);
            setItemTar({
              ...record,
            });
          }}
        >
          播放
        </a>,
      ],
    },
  ];

  return (
    <div className="big-screen-monitor">
      <CommonTitle title="实时监控" />
      <div className="big-screen-monitor-title">
        <div className="big-screen-monitor-title-text">实时监控</div>
        <div className="big-screen-monitor-extra">
          <span>播放模式</span>
          <Select
            size="small"
            style={{ width: 100 }}
            defaultValue="轮播"
            className="big-screen-select"
          >
            <Select.Option value="轮播">轮播</Select.Option>
            <Select.Option value="单选">单选</Select.Option>
          </Select>
        </div>
      </div>
      {/* <Carousel autoplay> */}
      <div>
        <div className="big-screen-monitor-content">
          {monitorList.map((item, index) => {
            return (
              <div className="big-screen-monitor-content-item" key={index}>
                <div
                  className="big-screen-monitor-content-item-img"
                  onClick={() => {
                    setOpen(true);
                    setItemTar({
                      ...item,
                    });
                  }}
                >
                  <LivePlayer
                    url={
                      item?.flvUrl ||
                      'https://l3g8l8wn-8082.asse.devtunnels.ms/live/1liy60bt8ADSGTpv2UrplfJ52aDjKSWuxRTJkFauXOMMd2C8D71bvWpAXQGHAejdg64CrUhdcO0RsD53ft2vpUqf.flv'
                    }
                    autoplay={true}
                    controls={false}
                  />
                </div>
                <div className="big-screen-monitor-content-item-desc">
                  <div className="big-screen-monitor-content-item-desc-workstation">
                    <div title={item?.largeScreenTitle || '任务数量'}>
                      {item?.largeScreenTitle || '任务数量'}
                    </div>
                    <span
                      className="big-screen-monitor-content-item-desc-workstation-tips"
                      style={{
                        background: item?.tfTesting ? '#FFD700' : '#4CAF50',
                      }}
                    ></span>
                  </div>
                  {/* <div className="big-screen-monitor-content-item-desc-task">
                    {item?.installationLocation || '任务XXXXx'}
                  </div> */}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <div className="big-screen-monitor-title">
        <div className="big-screen-monitor-title-text">历史信息</div>
      </div>
      <div className="big-screen-report-content-table-content">
        <ProTable
          columns={columns}
          dataSource={historyList}
          search={false}
          toolBarRender={false}
          pagination={false}
          scroll={{ y: 'calc(100% - 48px)' }}
          rowKey="id"
          className="big-screen-task-list-table"
          rowClassName={() => 'custom-row'}
        />
      </div>

      {/* </Carousel> */}
      <ModalViewLive
        open={open}
        row={{ ...itemTar }}
        footer={false}
        width={600}
        maskClosable={false}
        destroyOnClose={true}
        onCancel={() => {
          setOpen(false);
        }}
        className="big-screen-monitor-modal"
      />
    </div>
  );
}

export default Monitor;
