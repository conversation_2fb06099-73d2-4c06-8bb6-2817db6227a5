.big-screen-monitor {
  width: 598px;
  height: 100%;
  background-color: transparent;
  border-radius: 6px;
  padding: 20px;
  overflow: hidden;
  background: url('@/assets/bigscreen/monitor_bg.png') no-repeat center center;
  background-size: 100% 100%;
  .big-screen-monitor-extra {
    display: flex;
    align-items: center;
    gap: 16px;
    color: rgba(255,255,255,0.85);
  }
  
  .big-screen-monitor-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 36px;
    margin-top: 12px;
    background: url('@/assets/bigscreen/sub_title_bg.png') no-repeat left center;
    background-size: contain;
    .big-screen-monitor-title-text {
      font-size: 16px;
      color: rgba(255,255,255,0.85);
    }
    .big-screen-monitor-title-extra {
      .ant-select {
        font-size: 12px;
      }
    }
  }
  .big-screen-monitor-content {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    margin-top: 16px;
    max-height: calc(264px * 3 + 18px * 3 );
    overflow: hidden scroll;
    // scroll 颜色
    scrollbar-color: rgba(255,255,255,0.2) transparent;
    .big-screen-monitor-content-item {
      width: calc(50% - 8px);
      height: 264px;
      background: linear-gradient( 0deg, rgba(18,207,255,0.2) 0%, rgba(18,207,255,0) 100%);
      // border: 1px solid rgba(0,0,0,0.06);
      padding: 12px;
      .big-screen-monitor-content-item-img {
        width: 100%;
        height: 180px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .big-screen-monitor-content-item-desc {
        display: flex;
        flex-direction: column;
        gap: 4px;
        padding: 8px 0;
        .big-screen-monitor-content-item-desc-workstation {
          font-size: 16px;
          color: rgba(255,255,255,0.85);
          display: flex;
          div {
            max-width: calc(100% - 16px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          span {
            display: inline-block;
            margin-top: 6px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 4px;
          }
        }
        .big-screen-monitor-content-item-desc-task {
          font-size: 14px;
          color: rgba(255,255,255,0.45);
        }
      }
    }
  }

  .big-screen-report-content-table-content {
    margin-top: 12px;
    height: 250px;
    .ant-pro-table, .ant-table-wrapper,
    .ant-spin-nested-loading,
    .ant-spin-container,
    .ant-table,
    .ant-table-container {
      height: 100%;
    }
    .ant-table-thead {
      height: 48px;
      .ant-table-cell {
        height: 48px;
        color: rgba(255,255,255,0.4);
        background-color: transparent;
      }
    }
  }
}
.big-screen-monitor-modal {
  .ant-modal-header {
    background: transparent;
    color: #fff;
    .ant-modal-title {
      color: rgba(255,255,255,0.85);
    }
  }
  .ant-modal-close {
    color: rgba(255,255,255,0.65);
    &:hover {
      color: rgba(255,255,255,0.85);
    }
  }
  .ant-modal-content {
    background: #010A2F;
    border: 1px solid #1e4686;
  }
}
