import ProTable from '@ant-design/pro-table';
import CommonTitle from '../commonTitle';
import './taskList.less';

function TaskList({ data }: { data: any }) {
  const { columns = [], dataSource = [] } = data;
  // const [dataSource, setDataSource] = useState<any>([]);

  const subject = columns?.map((item: any) => {
    item.render = (str: string, record: any, index: number) => {
      let data = record[item.dataIndex] || {};
      if (!record[item.dataIndex]) {
        data = {
          title: '',
          taskId: '',
          workStation: '',
          status: '',
        };
      }
      let boolean = data.status === '检毕';
      return (
        <div className={`normal ${boolean && 'over'}`}>
          <div className="normal-top line-ellipsis" title={data.title || '-'}>{data.title || '-'}</div>
          <div className="normal-bottom">
            <div className="normal-bottom-item">
              <span>工单</span>
              <span title={data.taskId || '-'} className='line-ellipsis'>{data.taskId || '-'}</span>
            </div>
            <div className="normal-bottom-item">
              <span title={data.workStation || '-'} className='line-ellipsis'>{data.workStation || '-'}</span>
            </div>
            <div className="normal-bottom-item">
              <span title={data.status || '-'} className='line-ellipsis'>{data.status || '-'}</span>
            </div>
          </div>
        </div>
      );
    }
    return {
      ...item,
      width: columns.length > 3 ? '220px' : '100%',
    };
  }) ?? []

  return (
    <div className="big-screen-task-list">
      <CommonTitle title="检测任务实时信息" />
      <ProTable
        search={false}
        toolBarRender={false}
        bordered={false}
        style={{ marginTop: 12 }}
        pagination={false}
        scroll={{ y: 'calc(100% - 36px)' }}
        rowKey="index"
        tableLayout="fixed"
        columns={[
          {
            title: (
              <div className="headerCell">
                <div className="headerCell-after">检项</div>
                <div className="headerCell-before">检测任务</div>
              </div>
            ),
            dataIndex: 'testName',
            key: 'testName',
            width: '130px',
            align: 'center',
            render: (str: string) => {
              return <span style={{ color: 'rgba(255,255,255,0.85)' }}>{str}</span>;
            },
            fixed: 'left',
          },
          ...subject,
        ]}
        dataSource={dataSource}
        className="big-screen-task-list-table"
        rowClassName={() => 'custom-row'}
      />
    </div>
  );
}

export default TaskList;
