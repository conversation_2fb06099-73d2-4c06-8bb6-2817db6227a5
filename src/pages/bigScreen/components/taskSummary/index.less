.big-screen-task-summary {
  width: 598px;
  height: 549px;
  background-color: transparent;
  border-radius: 6px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  padding: 20px;
  background: url('@/assets/bigscreen/task_top_bg.png') no-repeat center center;
  background-size: 100% 100%;
  .big-screen-task-summary-content {
    height: calc(100% - 40px);
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 12px;
    .big-screen-task-summary-content-ecahrts {
      display: flex;
      gap: 16px;
      .big-screen-task-summary-content-ecahrts-item {
        width: 50%;
        height: 189px;
        border-radius: 6px;
        padding: 8px;
        background: linear-gradient( 90deg, rgba(18,207,255,0.2) 0%, rgba(18,207,255,0.04) 100%);
        border-radius: 6px 6px 6px 6px;
        border: 1px solid rgba(18,207,255,0.06);
        #task-status-chart {
          height: calc(100% - 24px);
          overflow: hidden;
          display: flex;
          .task-status-chart-item {
            width: calc(100% - 80px);
            height: 100%;
          }
          .task-status-chart-item-extra {
            width: 80px;
            padding-left: 16px;
            height: 100%;
            display: flex;
            flex-direction: column;
            color: rgba(255,255,255,0.85);
            .task-status-chart-item-name {
              font-size: 14px;
              display: flex;
              align-self: flex-start;
              span.circle {
                display: inline-block;
                width: 10px;
                height: 10px;
                margin-right: 8px;
                margin-top: 6px;
                border-radius: 50%;
              }
            }
          }
        }
        #task-count-chart {
          height: calc(100% - 24px);
          overflow: hidden;
        }
        .big-screen-task-summary-content-ecahrts-item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .big-screen-task-summary-content-ecahrts-item-title-text {
            font-size: 16px;
            color: rgba(255,255,255,0.85);
          }
          .big-screen-task-summary-content-ecahrts-item-title-extra {
            .ant-select {
              font-size: 12px;
            }
          }
        }
      }
    }
    .big-screen-task-summary-content-table {
      height: calc(100% - 200px);
      .big-screen-task-summary-content-table-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        margin-top: 0px;
        background: url('@/assets/bigscreen/sub_title_bg.png') no-repeat left center;
        background-size: contain;
        color: rgba(255,255,255,0.85);
        padding-left: 36px;
      }

      .big-screen-task-summary-content-table-content {
        height: calc(100% - 50px);
        overflow: hidden;
        color: rgba(255,255,255,0.85);
        .ant-pro-table, .ant-table-wrapper,
        .ant-spin-nested-loading,
        .ant-spin-container,
        .ant-table,
        .ant-table-container {
          height: 100%;
        }
        .ant-table-thead {
          height: 30px;
          .ant-table-cell {
            height: 30px;
            color: rgba(255,255,255,0.4);
            background-color: transparent;
          }
        }
      }
    }
  }
}
