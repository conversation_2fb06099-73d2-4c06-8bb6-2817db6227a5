.big-screen-task-list {
  width: 1220px;
  height: 388px;
  padding: 12px 20px 20px 20px;
  background: linear-gradient(
    180deg,
    rgba(0, 72, 171, 0.18) 30%,
    rgba(0, 72, 171, 0.18) 46%,
    rgba(0, 74, 170, 0.09) 66%,
    rgba(0, 76, 178, 0.04) 80%,
    rgba(0, 72, 171, 0) 99%
  );
  background-color: transparent;
  border: 1px solid;
  border-radius: 6px;
  border-radius: 7px 7px 7px 7px;
  border-top-left-radius: 0;
  border-image: linear-gradient(180deg, rgba(19, 163, 255, 0.25), rgba(19, 163, 255, 0)) 1 1;

  .headerCell {
    position: relative;
    /*上边框宽度等于表格第一行行高*/
    width: 100%;
    /*让容器宽度为0*/
    height: 100%;
    overflow: hidden;
    background: linear-gradient(
      to top right,
      rgba(8, 31, 83, 0.9) 0%,
      rgba(8, 31, 83, 0.9) calc(50% - 1px),
      rgba(30, 70, 134, 1) 50%,
      rgba(8, 31, 83, 0.9) calc(50% + 1px),
      rgba(8, 31, 83, 0.9) 100%
    );

    .headerCell-after {
      position: absolute;
      top: 0px;
      right: 8px;
      color: rgba(255, 255, 255, 0.4);
      white-space: nowrap;
    }
    .headerCell-before {
      position: absolute;
      bottom: 0px;
      left: 8px;
      color: rgba(255, 255, 255, 0.4);
      white-space: nowrap;
    }
  }

  .ant-pro-table,
  .ant-table-wrapper,
  .ant-spin-nested-loading,
  .ant-spin-container,
  .ant-table,
  .ant-table-container {
    height: 100%;
  }
  .ant-pro-table {
    height: calc(100% - 60px);
    .ant-table-wrapper {
      border-radius: 0;
    }
    .ant-table-thead {
      > tr {
        th.ant-table-cell {
          height: 48px;
          padding: 0;
          color: rgba(255, 255, 255, 0.4);
          text-align: center;
          &:not(:last-child) {
            border-right: 1px solid #1e4686;
          }
        }
      }
    }
    .ant-table-tbody {
      .ant-table-row {
        height: 100%;
        td {
          border-top: 1px solid #1e4686;
          &:first-child {
            background-color: rgba(8, 31, 83, 0.9);
          }
        }
      }
      .ant-table-row:last-child {
        td {
          border-bottom: 1px solid #1e4686;
        }
      }
      > tr {
        td.ant-table-cell {
          padding: 0;
          &:not(:last-child) {
            border-right: 1px solid #1e4686;
          }
        }
      }
    }
  }

  .normal {
    display: flex;
    flex-direction: column;
    .normal-top {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      background-color: transparent;
      border-bottom: 1px solid #1e4686;
    }
    .normal-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 45px;
      background-color: transparent;
      .normal-bottom-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: calc(100% / 3);
        height: 100%;
        padding: 2px;
        font-size: 12px;
        &:not(:last-child) {
          border-right: 1px solid #1e4686;
        }
      }
    }
  }

  .over {
    .normal-top {
      background-color: rgba(105,31,32,0.25);
    }
    .normal-bottom {
      background-color:  rgba(105,31,32,0.3);
    }
  }
}
