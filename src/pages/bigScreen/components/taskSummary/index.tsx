import ProTable from '@ant-design/pro-table';
import { Select, Tooltip } from 'antd';
import ReactECharts from 'echarts-for-react';
import CommonTitle from '../commonTitle';
import './index.less';
function TaskSummary({ data }: { data: any }) {
  const { taskStatusData = [], taskCountData = [], taskDataSource = [] } = data;
  // const [taskStatusData, setTaskStatusData] = useState<any[]>([
  //   {
  //     value: 6,
  //     name: '待检',
  //     itemStyle: { color: '#FADB14' },
  //   },
  //   {
  //     value: 2,
  //     name: '在检',
  //     itemStyle: { color: '#2BA471' },
  //   },
  //   {
  //     value: 1,
  //     name: '已检',
  //     itemStyle: { color: '#999999' },
  //   },
  // ]);

  // const [taskCountData, setTaskCountData] = useState<any[]>(
  //   new Array(7).fill('').map((item, index) => {
  //     return {
  //       time: dayjs().subtract(index, 'day').format('MM-DD'),
  //       count: Math.floor(Math.random() * 10),
  //     };
  //   }),
  // );

  // const [dataSource, setDataSource] = useState<any>([
  //   {
  //     index: '1',
  //     checkName: '变压器（10kV）',
  //     checkType: 'B',
  //     checkCode: 'mx01g78L',
  //     checkTime: dayjs('2025-3-20 10:00:00').format('YYYY-MM-DD HH:mm:ss'),
  //   },
  //   {
  //     index: '2',
  //     checkName: '高压开关柜',
  //     checkType: 'B',
  //     checkCode: 'zd49jh24',
  //     checkTime: dayjs('2025-3-20 10:05:01').format('YYYY-MM-DD HH:mm:ss'),
  //   },
  // ]);

  const columns = [
    {
      title: '编号',
      dataIndex: 'index',
      width: 60,
    },
    {
      title: <span title="样品名称">样品名称</span>,
      dataIndex: 'checkName',
      ellipsis: true,
    },
    {
      title: <span title="检测类型">检测类型</span>,
      dataIndex: 'checkType',
      ellipsis: true,
    },
    {
      title: <span title="盲审编号">盲审编号</span>,
      dataIndex: 'checkCode',
      ellipsis: true,
    },
    {
      title: (
        <Tooltip title="二次盲样编号" color="rgba(0, 0, 0, 0.85)">
          <span>二次盲样编号</span>
        </Tooltip>
      ),
      dataIndex: 'secondaryCheckCode',
      ellipsis: true,
    },
    {
      title: (
        <Tooltip title="任务录入时间" color="rgba(0, 0, 0, 0.85)">
          <span>任务录入时间</span>
        </Tooltip>
      ),
      dataIndex: 'checkTime',
      ellipsis: true,
      valueType: 'date',
    },
  ];

  console.log(taskStatusData);

  return (
    <div className="big-screen-task-summary">
      <CommonTitle title="任务汇总" />
      <div className="big-screen-task-summary-content">
        <div className="big-screen-task-summary-content-ecahrts">
          <div className="big-screen-task-summary-content-ecahrts-item">
            <div className="big-screen-task-summary-content-ecahrts-item-title">
              <div className="big-screen-task-summary-content-ecahrts-item-title-text">
                任务状态统计
              </div>
            </div>
            <div id="task-status-chart">
              <div className="task-status-chart-item">
                <ReactECharts
                  option={{
                    legend: {
                      data: taskStatusData.map((item: any) => item.name),
                      orient: 'vertical',
                      right: '10%',
                    },
                    // tooltip: {
                    //   trigger: 'axis',
                    //   axisPointer: {
                    //     type: 'shadow',
                    //   },
                    //   confine: true,
                    //   backgroundColor: 'rgba(0, 0, 0, 0.85)',
                    //   textStyle: {
                    //     color: 'rgba(255, 255, 255, 0.85)',
                    //   },
                    // },
                    grid: {
                      left: '3%',
                      right: '4%',
                      bottom: '3%',
                      top: '10%',
                      containLabel: true,
                    },
                    xAxis: {
                      type: 'category',
                      data: taskStatusData.map((item: any) => item.name),
                    },
                    yAxis: {
                      type: 'value',
                      minInterval: 1,
                      min: 0,
                      // 强制显示0刻度
                      axisLabel: {
                        formatter: (value: any) => {
                          return value === 0 ? '0' : value;
                        },
                      },
                    },
                    series: [
                      {
                        type: 'bar',
                        barMaxWidth: 38,
                        data: taskStatusData.map((item: any) => ({
                          ...item,
                          value: item.value,
                        })),
                      },
                    ],
                  }}
                  style={{
                    height: '100%',
                    overflow: 'hidden',
                  }}
                />
              </div>
              <div className="task-status-chart-item-extra">
                {taskStatusData.map((item: any, index: any) => (
                  <div key={index}>
                    <div className="task-status-chart-item-name">
                      <span className="circle" style={{ backgroundColor: item.itemStyle.color }}></span>
                      <span style={{ display: 'inline-block', width: 'calc(100% - 26px)' }}>
                        {item.name}
                      </span>
                    </div>
                    <div style={{ fontSize: 13, paddingLeft: 12 }}>
                      {item.value}
                      <span style={{ fontSize: 12, marginLeft: 2 }}>条</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="big-screen-task-summary-content-ecahrts-item">
            <div className="big-screen-task-summary-content-ecahrts-item-title">
              <div className="big-screen-task-summary-content-ecahrts-item-title-text">
                检毕任务数量统计
              </div>
              <div className="big-screen-task-summary-content-ecahrts-item-title-extra">
                <Select
                  size="small"
                  style={{ width: 60 }}
                  defaultValue="周"
                  className="big-screen-select"
                >
                  <Select.Option value="周">周</Select.Option>
                  <Select.Option value="月">月</Select.Option>
                  <Select.Option value="年">年</Select.Option>
                </Select>
              </div>
            </div>
            <div id="task-count-chart">
              <ReactECharts
                option={{
                  grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '10%',
                    containLabel: true,
                  },
                  // tooltip: {
                  //   trigger: 'axis',
                  //   axisPointer: {
                  //     type: 'shadow',
                  //   },
                  //   backgroundColor: 'rgba(0, 0, 0, 0.85)',
                  //   textStyle: {
                  //     color: 'rgba(255, 255, 255, 0.85)',
                  //   },
                  //   confine: true,
                  // },
                  xAxis: {
                    type: 'category',
                    data: taskCountData.map((item: any) => item.time).reverse(),
                  },
                  yAxis: {
                    type: 'value',
                    minInterval: 1,
                    min: 0,
                    // 强制显示0刻度
                    axisLabel: {
                      formatter: (value: any) => {
                        return value === 0 ? '0' : value;
                      },
                    },
                  },
                  series: [
                    {
                      type: 'line',
                      smooth: true,
                      data: taskCountData.map((item: any) => item.count).reverse(),
                    },
                  ],
                }}
                style={{
                  height: '100%',
                  overflow: 'hidden',
                }}
              />
            </div>
          </div>
        </div>
        <div className="big-screen-task-summary-content-table">
          <div className="big-screen-task-summary-content-table-title">
            <div className="big-screen-task-summary-content-table-title-text">待检任务列表</div>
          </div>
          <div className="big-screen-task-summary-content-table-content">
            <ProTable
              search={false}
              toolBarRender={false}
              columns={columns}
              dataSource={taskDataSource}
              pagination={false}
              scroll={{ y: 'calc(100% - 48px)' }}
              rowKey="status1"
              className="big-screen-task-list-table"
              bordered={false}
              rowClassName={() => 'custom-row'}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default TaskSummary;
