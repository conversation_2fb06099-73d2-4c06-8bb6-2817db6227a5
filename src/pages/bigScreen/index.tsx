import { screenReport } from '@/services/base/dapingjiekou';
import { useEffect, useState } from 'react';
import Header from './components/header/index';
import Monitor from './components/monitor/index';
import ReportInfo from './components/reportInfo/index';
import TaskSummary from './components/taskSummary/index';
import TaskList from './components/taskSummary/taskList';
import bigData from './index.json';
import './index.less';

function BigScreen() {
  // 标题
  // 监控 翻译为英文   Monitor
  // 任务汇总 翻译为英文  Task Summary
  // 报告信息 翻译为英文  Report Information

  const [bigScreenData, setBigScreenData] = useState<any>({});

  const getBigScreenData = async () => {
    const res = await screenReport();
    if (res?.success) {
      setBigScreenData(res?.data);
    }
  };

  useEffect(() => {
    getBigScreenData();
  }, []);

  return (
    <div className="big-screen">
      <Header />
      <div className="big-screen-content">
        <Monitor data={bigScreenData?.monitor || bigData.monitor} />
        <div className="big-screen-content-right">
          <div className="big-screen-content-right-top">
            <TaskSummary data={bigScreenData?.taskSummary || bigData.taskSummary} />
            <ReportInfo data={bigScreenData?.reportInfo || bigData.reportInfo} />
          </div>
          <div className="big-screen-content-right-bottom">
            <TaskList data={bigScreenData?.taskSummary?.taskListObj || bigData.taskSummary.taskListObj} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default BigScreen;
