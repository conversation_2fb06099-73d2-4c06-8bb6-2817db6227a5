import formatRoutes, { adminPrefix, whitePath } from '@/../config/routes';
import Text from '@/components/text';
import * as Enum from '@/enums/auth';
import { Modal } from 'antd';
import { useCallback } from 'react';

import IconAction from '@/components/iconAction';
import { permissionApiSyncPageAndApi } from '@/services/base/authBe';
import { InteractionOutlined } from '@ant-design/icons';
import { message } from 'antd';

type TongBuQianHouDuanQuanXianDTO = ArrayElement<
  ArgumentsType<typeof permissionApiSyncPageAndApi>[0]
>;

// 给接口加前缀
const formatApiList = (apiList: string[] = []) => {
  // eslint-disable-next-line no-useless-escape
  return apiList.map((v) => v.replace(/([post|get]\:\/)(?!test\/)/, '$1test/'));
};

/** 生成后台管理系统权限 */
function gentAdminTree(data: RouteData[], level = 0): TongBuQianHouDuanQuanXianDTO[] {
  const res: TongBuQianHouDuanQuanXianDTO[] =
    level === 0
      ? [
          {
            permissionName: '后台管理系统',
            permissionCode: adminPrefix,
            type: Enum.FeAuthType.端,
            child: [],
          },
        ]
      : [];

  data.forEach((item) => {
    // 过滤
    if (!item.path || whitePath.some((v) => v.test(item.path!)) || item?.hideInMenu) {
      return;
    }

    const itemData: TongBuQianHouDuanQuanXianDTO = {
      permissionName: item.name ?? item.path,
      permissionCode: `${adminPrefix}:${item.path}`,
      type: Enum.FeAuthType.菜单,
      apiList: formatApiList(item.apiList),
      child: [],
    };

    if (item.routes) {
      itemData.child = itemData.child?.concat(gentAdminTree(item.routes, level + 1));
    }

    // 绑定自定义操作
    if (item.actionList) {
      item.actionList.forEach((action) => {
        itemData.child!.push({
          permissionName: action.name,
          permissionCode: `${adminPrefix}:${item.path}@${action.auth}`,
          type: Enum.FeAuthType.按钮,
          apiList: formatApiList(action.apiList),
          child: action?.children && action?.children?.map(i=>({
            permissionName: i.name,
            permissionCode: `${adminPrefix}:${item.path}@${action.auth}@${i.auth}`,
            type: Enum.FeAuthType.按钮,
            apiList: formatApiList(i.apiList)
          })),
        });
      });
    }

    if (item.crud) {
      if (!itemData.child) {
        itemData.child = [];
      }

      if (typeof item.crud === 'boolean') {
        itemData.child = itemData.child.concat([
          {
            permissionName: '列表',
            permissionCode: `${adminPrefix}:${item.path}@list`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/voPage`]) || [],
          },
          {
            permissionName: '新增',
            permissionCode: `${adminPrefix}:${item.path}@add`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/add`]) || [],
          },
          {
            permissionName: '删除',
            permissionCode: `${adminPrefix}:${item.path}@delete`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/delete`]) || [],
          },
          {
            permissionName: '编辑',
            permissionCode: `${adminPrefix}:${item.path}@edit`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/update`]) || [],
          },
          {
            permissionName: '详情',
            permissionCode: `${adminPrefix}:${item.path}@detail`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/getVo`]) || [],
          },
        ]);
      } else {
        if (item.crud.list) {
          itemData.child.push({
            permissionName: '列表',
            permissionCode: `${adminPrefix}:${item.path}@list`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/voPage`]) || [],
          });
        }
        if (item.crud.add) {
          itemData.child.push({
            permissionName: '新增',
            permissionCode: `${adminPrefix}:${item.path}@add`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/add`]) || [],
          });
        }
        if (item.crud.delete) {
          itemData.child.push({
            permissionName: '删除',
            permissionCode: `${adminPrefix}:${item.path}@delete`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/delete`]) || [],
          });
        }
        if (item.crud.edit) {
          itemData.child.push({
            permissionName: '编辑',
            permissionCode: `${adminPrefix}:${item.path}@edit`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/update`]) || [],
          });
        }
        if (item.crud.edit) {
          itemData.child.push({
            permissionName: '详情',
            permissionCode: `${adminPrefix}:${item.path}@detail`,
            type: Enum.FeAuthType.按钮,
            apiList: (item?.crudKey && [`post:/test/${item.crudKey}/getVo`]) || [],
          });
        }
      }
    }

    if (level === 0) {
      res[0].child?.push(itemData);
    } else {
      res.push(itemData);
    }
  });

  return res;
}

/** 生成权限树 */
function genTree(): TongBuQianHouDuanQuanXianDTO[] {
  const adminTree = gentAdminTree(formatRoutes);

  return [...adminTree];
}

const tree = genTree();

export interface GenProps {
  onSuccess: () => void;
}

const Gen: React.FC<GenProps> = ({ onSuccess }) => {
  const handleGen = useCallback(() => {
    Modal.confirm({
      title: (
        <div>
          <Text type="danger">此操作会删除目前所有的权限!</Text>,是否生成权限?
        </div>
      ),
      onOk: async () => {
        console.log('tree', tree);
        await permissionApiSyncPageAndApi(tree);

        message.success('生成成功!');

        onSuccess();
      },
    });
  }, [onSuccess]);

  return (
    <IconAction
      tooltip="生成权限"
      icon={<InteractionOutlined />}
      onClick={handleGen}
      size="middle"
    />
  );
};

export default Gen;
