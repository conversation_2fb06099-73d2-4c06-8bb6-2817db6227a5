import * as Enum from '@/enums/auth';
import { permissionApiTree as permissionApiTreeApi } from '@/services/base/authBe';
import { permissionFrontAdd, permissionFrontUpdate } from '@/services/base/authFe';
import { renderEnum } from '@/valueType/valueType/enum';
import {
  DrawerForm,
  FormInstance,
  ProFormDigit,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message, TreeSelect, TreeSelectProps } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { BizObject } from './index';

/** 根据节点id,新增跟级节点需要 */
const FE_ROOT_ID = 0;

export type EditRef = {
  open: (arg: {
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {
  /** 前端权限列表 */
  feAuthTreeData: BizObject[];
};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = ({ feAuthTreeData }, ref) => {
  // 后端权限树
  const permissionApiTree = useRequest(
    async () => {
      const formatTree = (data: BASE.ApiPermission[]): TreeSelectProps['treeData'] => {
        return data.map((item) => {
          return {
            title: (
              <div>
                {renderEnum(item.permissionType, Enum.BeAuthTypeObj)}
                {renderEnum(item.status, Enum.BeAuthStatusObj)}
                {item.permissionName}
              </div>
            ),
            // 只能绑定接口
            disabled: item.permissionType !== Enum.BeAuthType.接口,
            value: item.apiCode,
            children: item.child ? formatTree(item.child) : undefined,

            data: item,
          };
        });
      };

      const res = await permissionApiTreeApi({});

      return {
        ...res,
        data: formatTree(res.data || []),
      };
    },
    {
      cacheKey: 'permissionApiTree',
    },
  );
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);

        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              ...arg.rowData,
              apiPermissions: (arg.rowData?.apiList || []).map((v) => v.apiCode).filter(Boolean),
            });
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            form.current?.setFieldsValue({
              type: Enum.FeAuthType.端,
              status: Enum.FeAuthStatus.需要验证,
              sort: 0,
              pid: FE_ROOT_ID,
            });
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BizObject>
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
        };

        await (editable
          ? permissionFrontUpdate({
              ...baseQuery,
              id: rowData?.id as number,
            })
          : permissionFrontAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormTreeSelect
        label="上级权限"
        name="pid"
        rules={[{ required: true, message: '请选择上级权限' }]}
        fieldProps={{
          treeData: [
            {
              permissionName: '顶级权限',
              id: FE_ROOT_ID,
              child: feAuthTreeData,
            },
          ],
          treeDefaultExpandAll: true,
          fieldNames: {
            label: 'permissionName',
            value: 'id',
            children: 'child',
          },
        }}
      />

      <ProFormText
        label="权限名称"
        name="permissionName"
        rules={[{ required: true, message: '请填写权限名称' }]}
      />

      <ProFormText
        label="权限编码"
        name="permissionCode"
        rules={[{ required: true, message: '请填写权限编码' }]}
      />

      <ProFormTreeSelect
        label="接口权限"
        name="apiPermissions"
        fieldProps={{
          showSearch: true,
          filterTreeNode: (inputValue, treeNode) => {
            if (!inputValue) {
              return true;
            }

            return treeNode.data?.permissionName?.includes(inputValue);
          },
          treeData: permissionApiTree.data,
          multiple: true,
          treeDefaultExpandAll: true,
          treeCheckable: true,
          showCheckedStrategy: TreeSelect.SHOW_ALL,
        }}
      />

      <ProFormRadio.Group
        label="类型"
        name="type"
        valueEnum={Enum.FeAuthTypeObj}
        rules={[{ required: true, message: '请选择类型' }]}
      />

      <ProFormRadio.Group
        label="校验类型"
        name="status"
        valueEnum={Enum.FeAuthStatusObj}
        rules={[{ required: true, message: '请选择校验类型' }]}
      />

      <ProFormDigit
        label="排序"
        name="sort"
        rules={[{ required: true, message: '请填写排序' }]}
        fieldProps={{
          min: 0,
          step: 1,
        }}
      />

      <ProFormText label="组件地址" name="componentUrl" />

      <ProFormTextArea label="说明" name="extend" />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
