import Button from '@/components/button';
import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import * as Enum from '@/enums';
import { permissionFrontDelete, permissionFrontTree } from '@/services/base/authFe';
import { renderEnum } from '@/valueType/valueType/enum';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message, Space } from 'antd';
import { useRef, useState } from 'react';
import Edit, { EditRef } from './edit';
import Gen from './gen';

// 树转数组
const flatten = (arr: BASE.PagePermissionVO[]): BASE.PagePermissionVO[] => {
  return arr.reduce((prev, item) => {
    if (item.child) {
      return prev.concat(item, flatten(item.child));
    }

    return prev.concat(item);
  }, [] as BASE.PagePermissionVO[]);
};

/** 该模块增删改查对象 */
export type BizObject = BASE.PagePermissionVO;

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);
  const [ids, setIds] = useState<number[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [feAuthTreeData, setFeAuthTreeData] = useState<BizObject[]>([]);

  return (
    <PageContainer>
      <ProTable<BizObject, {}, GlobalValueType>
        search={false}
        pagination={false}
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        childrenColumnName="child"
        actionRef={table}
        rowKey="id"
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: (keys) => {
            setExpandedRowKeys(keys as number[]);
          },
        }}
        toolBarRender={(_, rows) => {
          const nodes: React.ReactElement[] = [
            <IconAction
              color="#40485b"
              tooltip="刷新"
              icon="refresh"
              key="refresh"
              onClick={() => {
                table.current?.reload();
              }}
              size="middle"
            />,
            <Button onClick={setExpandedRowKeys.bind({}, [])} key="fold">
              收缩所有
            </Button>,
            <Button onClick={setExpandedRowKeys.bind({}, ids)} key="unfold">
              展开所有
            </Button>,
          ];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                edit.current?.open({
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          // 只有超级管理员可以生成权限
          if (initialState?.roles.includes('superAdmin')) {
            nodes.push(
              <Gen
                key="gen"
                onSuccess={() => {
                  table.current?.reload();
                }}
              />,
            );
          }

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await permissionFrontTree({
            queryApi: Enum.NumBool.是,
          });

          const list = res.data || [];
          const addIds = flatten(list).map((item) => item.id);
          setFeAuthTreeData(list);
          setIds(addIds);
          setExpandedRowKeys(addIds);

          return {
            data: list,
            success: res.success,
            total: list.length,
          };
        }}
        scroll={{
          x: 'max-content',
          y: Math.max(document.documentElement.clientHeight - 444, 300),
        }}
        columns={[
          {
            title: '权限名称',
            dataIndex: 'permissionName',
            width: 300,
            render: (text, record) => {
              return (
                <div>
                  {renderEnum(record.type, Enum.FeAuthTypeObj)}
                  <span>{text}</span>
                </div>
              );
            },
          },
          {
            title: '权限编码',
            dataIndex: 'permissionCode',
            width: 200,
          },
          {
            title: '状态',
            dataIndex: 'status',
            valueEnum: Enum.FeAuthStatusObj,
            valueType: 'enum',
            width: 160,
          },
          {
            title: '组件地址',
            dataIndex: 'componentUrl',
          },
          {
            title: '排序',
            dataIndex: 'sort',
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        editable: true,
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    confirmText={`确定删除 ${row.permissionName} 吗?`}
                    request={{
                      service: () =>
                        permissionFrontDelete({ id: row.id } as unknown as ArgumentsType<
                          typeof permissionFrontDelete
                        >[0]),
                    }}
                    onSuccess={() => {
                      message.success('删除成功');
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit feAuthTreeData={feAuthTreeData} ref={edit} />
    </PageContainer>
  );
};

export default Page;
