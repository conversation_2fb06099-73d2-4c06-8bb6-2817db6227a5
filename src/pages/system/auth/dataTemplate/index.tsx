import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import * as Enum from '@/enums/auth';
import { TimeSort } from '@/enums/base';

import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message, Space } from 'antd';
import { useRef } from 'react';
import Edit, { EditRef } from './edit';

/** 该模块增删改查对象 */
export type BizObject = any;

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);

  return (
    <PageContainer>
      <ProTable<BizObject, {}, GlobalValueType>
        actionRef={table}
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        rowKey="id"
        toolBarRender={(_, rows) => {
          const nodes: React.ReactElement[] = [
            <IconAction
              color="#40485b"
              tooltip="刷新"
              icon="refresh"
              key="refresh"
              onClick={() => {
                table.current?.reload();
              }}
              size="middle"
            />,
          ];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                edit.current?.open({
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = {
            data: {
              records: [],
              total: 0,
            },
            success: true,
          };

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          {
            title: '模板名称',
            dataIndex: 'name',
            width: 150,
          },
          {
            title: '字段',
            dataIndex: 'whereField',
            width: 200,
          },
          {
            title: '类型',
            dataIndex: 'whereType',
            width: 160,
          },
          {
            title: '值',
            dataIndex: 'whereValue',
            width: 200,
          },
          {
            title: '值类型',
            dataIndex: 'whereValueType',
            valueEnum: Enum.DataAuthWhereTypeObj,
          },
          {
            title: 'join表达式',
            dataIndex: 'expression',
            hideInSearch: true,
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        editable: true,
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    confirmText={`确定删除 ${row.name} 吗?`}
                    request={{
                      service: () =>
                        Promise.resolve({
                          success: true,
                        }),
                    }}
                    onSuccess={() => {
                      message.success('删除成功');
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit ref={edit} />
    </PageContainer>
  );
};

export default Page;
