import * as Enum from '@/enums/auth';

import {
  DrawerForm,
  FormInstance,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { BizObject } from './index';

export type EditRef = {
  open: (arg: {
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = (props, ref) => {
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);

        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              ...arg.rowData,
            });
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            form.current?.setFieldsValue({});
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BizObject>
      data-key="role-edit"
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
        };

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormText label="模板名称" name="name" rules={[{ required: true, message: '此项必填' }]} />

      <ProFormText
        label="字段"
        name="whereField"
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormRadio.Group
        label="类型"
        name="whereType"
        request={async () => {
          const res = {
            data: [],
            success: true,
          };

          return (res.data || []).map((item: any) => {
            return {
              label: item.operateName,
              value: item.operator,
            };
          });
        }}
        rules={[{ required: true, message: '请选择' }]}
      />

      <ProFormTextArea
        label="值"
        name="whereValue"
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormRadio.Group
        label="值类型"
        name="whereValueType"
        valueEnum={Enum.DataAuthWhereTypeObj}
        rules={[{ required: true, message: '请选择' }]}
      />

      <ProFormTextArea label="join表达式" name="expression" />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
