import * as rules from '@/config/rule';
import * as Enum from '@/enums';
import { NumBool } from '@/enums/base';
import {
  permissionApiAdd,
  permissionApiGetTableName,
  permissionApiUpdate,
} from '@/services/base/authBe';
import {
  DrawerForm,
  FormInstance,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { BizObject } from './index';

/** 根据节点id,新增跟级节点需要 */
const BE_ROOT_ID = 0;

export type EditRef = {
  open: (arg: {
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {
  /** 后端权限列表 */
  beAuthTreeData: BizObject[];
};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = ({ beAuthTreeData }, ref) => {
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);

        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              ...arg.rowData,
              dataPermission: arg.rowData?.dataPermission === NumBool.是,
              updateLockType: arg.rowData?.updateLockType === NumBool.是,
              referTableName: arg.rowData?.referTableName?.split(';').filter(Boolean),
            });
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            form.current?.setFieldsValue({
              dataPermission: false,
              saveLog: true,
              updateLockType: true,
              logLevel: Enum.BeAuthLevel.正常,
            });
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BizObject>
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
          dataPermission:
            (query.dataPermission as unknown as boolean) === true ? NumBool.是 : NumBool.否,
          updateLockType:
            (query.updateLockType as unknown as boolean) === true ? NumBool.是 : NumBool.否,
          referTableName: Array.isArray(query.referTableName)
            ? query.referTableName.join(';')
            : query.referTableName,
        };

        await (editable
          ? permissionApiUpdate({
              ...baseQuery,
              id: rowData?.id as number,
            })
          : permissionApiAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormTreeSelect
        label="上级权限"
        name="pid"
        disabled={editable}
        hidden={editable}
        rules={[{ required: true, message: '请选择上级权限' }]}
        fieldProps={{
          treeData: [
            {
              permissionName: '顶级权限',
              id: BE_ROOT_ID,
              child: beAuthTreeData,
            },
          ],
          treeDefaultExpandAll: true,
          fieldNames: {
            label: 'permissionName',
            value: 'id',
            children: 'child',
          },
        }}
      />

      <ProFormText
        label="接口名称"
        name="permissionName"
        rules={[
          { required: true, message: '此项必填' },
          {
            validator: rules.noSpaceValidator,
          },
        ]}
      />

      <ProFormText
        label="服务名称"
        name="serveName"
        rules={[
          { required: true, message: '此项必填' },
          {
            validator: rules.noSpaceValidator,
          },
        ]}
      />

      <ProFormText
        label="接口key"
        name="apiCode"
        rules={[
          { required: true, message: '此项必填' },
          {
            validator: rules.noSpaceValidator,
          },
        ]}
      />

      <ProFormRadio.Group
        label="权限类型"
        name="permissionType"
        valueEnum={Enum.BeAuthTypeObj}
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormDependency name={['permissionType']}>
        {
          // 接口权限可以配置数据权限
          ({ permissionType }) => {
            if (permissionType === Enum.BeAuthType.接口) {
              // 1开启 0关闭
              return (
                <>
                  <ProFormSwitch
                    label="是否开启接口数据权限"
                    name="dataPermission"
                    rules={[{ required: true, message: '此项必填' }]}
                  />

                  <ProFormDependency name={['dataPermission']}>
                    {
                      // 接口权限可以配置数据权限
                      ({ dataPermission }) => {
                        if (dataPermission) {
                          return (
                            <ProFormSelect
                              label="表名"
                              name="referTableName"
                              rules={[{ required: true, message: '此项必填' }]}
                              mode="multiple"
                              request={async () => {
                                const res = await permissionApiGetTableName();

                                return (res.data || [])?.map((name) => {
                                  return {
                                    label: name,
                                    value: name,
                                  };
                                });
                              }}
                            />
                          );
                        }

                        return null;
                      }
                    }
                  </ProFormDependency>
                </>
              );
            }

            return null;
          }
        }
      </ProFormDependency>

      <ProFormRadio.Group
        label="日志级别"
        name="logLevel"
        valueEnum={Enum.BeAuthLevelObj}
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormTextArea label="日志说明" name="logExplain" />

      <ProFormSwitch
        label="是否保存请求日志"
        name="saveLog"
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormRadio.Group
        label="状态"
        name="status"
        valueEnum={Enum.BeAuthStatusObj}
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormText label="接口地址" name="url" />

      <ProFormRadio.Group label="请求方式" name="requestWay" valueEnum={Enum.BeAuthMethodObj} />

      <ProFormSwitch label="修改锁定，用于自动更新权限信息时不修改某些权限" name="updateLockType" />

      <ProFormTextArea label="ip白名单" name="whiteList" />
      <ProFormTextArea label="ip黑名单" name="blackList" />

      <ProFormDigit
        label="排序"
        name="sort"
        rules={[{ required: true, message: '请填写排序' }]}
        fieldProps={{
          min: 0,
          step: 1,
        }}
      />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
