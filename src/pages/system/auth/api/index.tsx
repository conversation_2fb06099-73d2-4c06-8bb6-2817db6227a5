import Button from '@/components/button';
import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import * as Enum from '@/enums';
import { permissionApiDelete, permissionApiTree } from '@/services/base/authBe';
import { renderEnum } from '@/valueType/valueType/enum';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message, Space } from 'antd';
import { useRef, useState } from 'react';
import Edit, { EditRef } from './edit';

// 树转数组
const flatten = (arr: BASE.ApiPermission[]): BASE.ApiPermission[] => {
  return arr.reduce((prev, item) => {
    if (item.child) {
      return prev.concat(item, flatten(item.child));
    }

    return prev.concat(item);
  }, [] as BASE.ApiPermission[]);
};

/** 该模块增删改查对象 */
export type BizObject = BASE.ApiPermission;

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);
  const [ids, setIds] = useState<number[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [beAuthTreeData, setBeAuthTreeData] = useState<BizObject[]>([]);

  return (
    <PageContainer>
      <ProTable<BizObject, { name: String }, GlobalValueType>
        // search={false}
        pagination={false}
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        childrenColumnName="child"
        actionRef={table}
        rowKey="id"
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: (keys) => {
            setExpandedRowKeys(keys as number[]);
          },
        }}
        toolBarRender={(_, rows) => {
          const nodes: React.ReactElement[] = [
            <IconAction
              color="#40485b"
              tooltip="刷新"
              icon="refresh"
              key="refresh"
              onClick={() => {
                table.current?.reload();
              }}
              size="middle"
            />,
            <Button onClick={setExpandedRowKeys.bind({}, [])} key="fold">
              收缩所有
            </Button>,
            <Button onClick={setExpandedRowKeys.bind({}, ids)} key="unfold">
              展开所有
            </Button>,
          ];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                edit.current?.open({
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, name, ...otherQuery }, sort, filter) => {
          const res = await permissionApiTree({
            name: name as string,
          });

          const list = res.data || [];
          const addIds = flatten(list).map((item) => item.id);
          setBeAuthTreeData(list);
          setIds(addIds);
          setExpandedRowKeys(addIds);

          return {
            data: list,
            success: res.success,
            total: list.length,
          };
        }}
        scroll={{
          x: 'max-content',
          y: Math.max(document.documentElement.clientHeight - 444, 300),
        }}
        search={{
          labelWidth: 150,
        }}
        columns={[
          {
            title: '接口名称/接口地址',
            dataIndex: 'name',
            hideInTable: true,
            hideInDescriptions: true,
            hideInForm: true,
          },
          {
            title: '权限名称',
            dataIndex: 'permissionName',
            render: (text, record) => {
              return (
                <div>
                  {renderEnum(record.permissionType, Enum.BeAuthTypeObj)}
                  <span>{text}</span>
                </div>
              );
            },
            hideInSearch: true,
          },
          {
            title: '服务名称',
            dataIndex: 'serveName',
            hideInSearch: true,
          },
          {
            title: '接口key',
            dataIndex: 'apiCode',
            hideInSearch: true,
          },
          {
            title: '请求方式',
            dataIndex: 'requestWay',
            hideInSearch: true,
          },
          {
            title: '接口地址',
            dataIndex: 'url',
            hideInSearch: true,
          },
          {
            title: '日志级别',
            dataIndex: 'logLevel',
            valueEnum: Enum.BeAuthLevelObj,
            valueType: 'enum',
            hideInSearch: true,
          },
          {
            title: '状态',
            dataIndex: 'status',
            valueEnum: Enum.BeAuthStatusObj,
            valueType: 'enum',
            hideInSearch: true,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            hideInSearch: true,
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        editable: true,
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    confirmText={`确定删除 ${row.permissionName} 吗?`}
                    request={{
                      service: () =>
                        permissionApiDelete({ id: row.id } as unknown as ArgumentsType<
                          typeof permissionApiDelete
                        >[0]),
                    }}
                    onSuccess={() => {
                      message.success('删除成功');
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit beAuthTreeData={beAuthTreeData} ref={edit} />
    </PageContainer>
  );
};

export default Page;
