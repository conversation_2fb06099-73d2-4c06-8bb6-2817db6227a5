import ProTable from '@/components/proTable';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';
import { orgUserInfoUserInfoNoPhoto } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import { useRequest } from '@umijs/max';
import { Col, Row, Tree } from 'antd';
import { useState } from 'react';

const Dept = () => {
  const [deptId, setDeptId] = useState<string | number>('');

  const treeRequest = useRequest(() => {
    return orgDeptInfoTree();
  });

  return (
    <div style={{ padding: 20 }}>
      <Row gutter={30}>
        <Col span={7} style={{ background: '#fff', padding: '20px', marginRight: 20 }}>
          <Tree
            defaultExpandedKeys={['0-0-0', '0-0-1']}
            defaultSelectedKeys={['0-0-0', '0-0-1']}
            defaultCheckedKeys={['0-0-0', '0-0-1']}
            onClick={(e, info) => {
              setDeptId((info as any)?.id);
            }}
            fieldNames={{
              title: 'deptName',
              children: 'child',
              key: 'id',
            }}
            treeData={(treeRequest?.data as any) || []}
          />
        </Col>
        <Col span={16} style={{ background: '#fff', padding: '20px' }}>
          <ProTable
            crudKey="orgUserInfo"
            request={async ({ current, pageSize, deptId, ...rest }) => {
              return orgUserInfoUserInfoNoPhoto({
                deptIds: deptId ? [deptId as number] : undefined,
                ...rest,
              }).then((res) => {
                return {
                  success: res?.success,
                  data: res?.data || [],
                };
              });
            }}
            params={{ deptId }}
            columns={[
              {
                title: '用户名',
                dataIndex: 'userName',
              },
              {
                title: '真实姓名',
                dataIndex: 'fullName',
              },
              {
                title: '是否部门主任',
                hideInSearch: true,
                dataIndex: 'tfTestMain',
                render: (v, r: any) => {
                  return r?.tfTestMain ? '是' : '--';
                },
              },
              {
                title: '电话',
                dataIndex: 'phone',
                hideInSearch: true,
              },
              {
                title: '邮箱',
                dataIndex: 'email',
                hideInSearch: true,
              },
            ]}
          />
        </Col>
      </Row>
    </div>
  );
};

export default Dept;
