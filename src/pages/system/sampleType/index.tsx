import ProTable, { ProTableProps } from '@/components/proTable';
import { PageContainer } from '@ant-design/pro-components';

function CheckItem() {
  const columns: ProTableProps<BASE.BuSampleBaseInfoVO>['columns'] = [
    //检项编号（任务编号-1-1、任务编号-1-2、任务编号-2-x形式）、检毕时间、检测人员、检测标准、检测结果、是否合格
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 80,
    },
    {
      title: '物资类别',
      dataIndex: 'name',
      ellipsis: true,
      width: 200,
      fieldProps: {
        allowClear: true,
        style: {
          width: '100%',
        },
      },
      formItemProps: {
        rules: [{ required: true, message: '请输入物资类别' }],
      },
    },
    {
      title: '物资型号',
      dataIndex: 'type',
      ellipsis: true,
      width: 200,
      fieldProps: {
        allowClear: true,
        style: {
          width: '100%',
        },
      },
      formItemProps: {
        rules: [{ required: true, message: '请输入物资型号' }],
      },
    },
    {
      title: '物资规格',
      dataIndex: 'model',
      ellipsis: true,
      width: 130,
      fieldProps: {
        allowClear: true,
        style: {
          width: '100%',
        },
      },
      formItemProps: {
        rules: [{ required: true, message: '请输入物资规格' }],
      },
    },
    // 操作
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record) => {
        return <></>;
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.BuSampleBaseInfoVO>
        crudKey="buSampleBaseInfo"
        hiddenBtns={[]}
        scroll={{
          scrollToFirstRowOnChange: true,
          x: 'max-content',
          y: 500,
        }}
        pagination={{
          pageSizeOptions: [10, 15, 20, 50, 100, 1000],
        }}
        toolBarRender={(action, { selectedRowKeys, selectedRows }) => {
          return [];
        }}
        columns={columns}
      />
    </PageContainer>
  );
}

export default CheckItem;
