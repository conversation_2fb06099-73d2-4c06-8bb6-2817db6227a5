import { color } from '@/config/color';

/** 请求方式 */
export enum Method {
  POST = 'POST',
  GET = 'GET',
  DELETE = 'DELETE',
  PUT = 'PUT',
}
export const MethodObj: ValueEnumMap<string> = new Map([
  [
    Method.POST,
    {
      text: 'POST',
      color: color.blue,
    },
  ],
  [
    Method.GET,
    {
      text: 'GET',
      color: color.cyan,
    },
  ],
  [
    Method.DELETE,
    {
      text: 'DELETE',
      color: color.red,
    },
  ],
  [
    Method.PUT,
    {
      text: 'PUT',
      color: color.purple,
    },
  ],
]);
/** end 请求方式 */

/** 级别 */
export enum Level {
  一般 = 1,
  敏感 = 2,
  危险 = 3,
}
export const LevelObj: ValueEnumMap<number> = new Map([
  [
    Level.一般,
    {
      text: '一般',
      color: color.green,
    },
  ],
  [
    Level.敏感,
    {
      text: '敏感',
      color: color.orange,
    },
  ],
  [
    Level.危险,
    {
      text: '危险',
      color: color.red,
    },
  ],
]);
/** end 级别 */

/** 登出方式 */
export enum LogoutType {
  用户正常登出 = 1,
  token到期 = 2,
  管理员踢出 = 3,
  其他地方登陆 = 4,
  其他 = 5,
}
export const LogoutTypeObj: ValueEnumMap<number> = new Map([
  [
    LogoutType.用户正常登出,
    {
      text: '用户正常登出',
      color: color.blue,
    },
  ],
  [
    LogoutType.token到期,
    {
      text: 'token到期',
      color: color.cyan,
    },
  ],
  [
    LogoutType.管理员踢出,
    {
      text: '管理员踢出',
      color: color.red,
    },
  ],
  [
    LogoutType.其他地方登陆,
    {
      text: '其他地方登陆',
      color: color.orange,
    },
  ],
  [
    LogoutType.其他,
    {
      text: '其他',
      color: color.purple,
    },
  ],
]);
/** end 登出方式 */
