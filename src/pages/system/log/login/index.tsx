import Action from '@/components/action';
import Des from '@/components/des';
import Dict from '@/components/dict';

import ProTable from '@/components/proTable';
import Text from '@/components/text';
import { color } from '@/config/color';
import { NumBool, NumBoolObj } from '@/enums/base';
import { loginLogKick, loginLogPage } from '@/services/base/authLoginLog';
import { formatDate, formatValue } from '@/utils';
import { renderEnum } from '@/valueType/valueType/enum';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Tag } from 'antd';
import { useRef, useState } from 'react';
import * as Enums from '../enum';
import Detail, { DetailRef } from './detail';

/** 该模块增删改查对象 */
export type BizObject = BASE.LoginLog;

const Page: React.FC = () => {
  const table = useRef<ActionType>();
  const detail = useRef<DetailRef>(null);
  const [sortKey, setSortKey] = useState<'loginTimeSort' | 'onlineSort'>('loginTimeSort');

  return (
    <PageContainer>
      <ProTable<BizObject>
        actionRef={table}
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        toolbar={{
          menu: {
            type: 'dropdown',
            items: [
              {
                label: '根据登录时间排序',
                key: 'loginTimeSort',
              },
              {
                label: '根据在线状态排序',
                key: 'onlineSort',
              },
            ],
            onChange: (activeKey) => {
              setSortKey(activeKey as 'loginTimeSort' | 'onlineSort');
              table.current?.reload();
            },
          },
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await loginLogPage({
            page: current,
            size: pageSize,
            [sortKey]: NumBool.是,
            ...otherQuery,
          });

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          // 列表项
          {
            title: 'id',
            dataIndex: 'id',
            hideInSearch: true,
          },
          {
            title: '请求用户',
            hideInSearch: true,
            render: (_, r) => {
              if (String(r.userId) === '0') {
                return formatValue();
              }

              return (
                <Des>
                  <Des.Item label="用户id" value={formatValue(r.userId)} />
                  <Des.Item
                    label="用户类型"
                    value={<Dict dicCode="userType" value={r.userType} />}
                  />
                  <Des.Item label="用户姓名" value={formatValue(r.fullName)} />
                  <Des.Item label="用户名" value={formatValue(r.username)} />
                  <Des.Item label="token" value={formatValue(r.token)} />
                </Des>
              );
            },
          },

          {
            title: '终端信息',
            hideInSearch: true,
            render: (_, r) => {
              return (
                <Des>
                  <Des.Item label="请求IP" value={formatValue(r.loginIp)} />
                  <Des.Item label="浏览器" value={formatValue(r.browser)} />
                  <Des.Item
                    label="客户端类型"
                    value={<Dict dicCode="clientType" value={r.clientType} />}
                  />
                  <Des.Item label="操作系统" value={formatValue(r.systemName)} />
                  <Des.Item label="城市" value={formatValue(r.loginCity)} />
                </Des>
              );
            },
          },
          {
            title: '时间',
            hideInSearch: true,
            render: (_, r) => {
              return (
                <Des>
                  <Des.Item label="登录时间" value={formatDate(r.loginTime)} />
                  <Des.Item label="登出时间" value={formatDate(r.logoutTime)} />
                  <Des.Item label="失效时间" value={formatDate(r.expireTime)} />
                  <Des.Item label="创建时间" value={formatDate(r.createTime)} />
                </Des>
              );
            },
          },
          {
            title: '状态',
            hideInSearch: true,
            render: (_, r) => {
              return (
                <Des>
                  <Des.Item
                    label="在线状态"
                    value={
                      r.online ? (
                        <Tag color={color.green}>在线</Tag>
                      ) : (
                        <Tag color={color.red}>离线</Tag>
                      )
                    }
                  />
                  <Des.Item
                    label="登出方式"
                    value={renderEnum(r.logoutType, Enums.LogoutTypeObj)}
                  />
                  <Des.Item label="登出说明" value={formatValue(r.logoutExplain)} />
                </Des>
              );
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            fixed: 'right',
            render: (_, row) => {
              return (
                <Action>
                  {row.online && (
                    <Text.Action
                      request={{
                        service: () =>
                          loginLogKick(
                            {
                              id: row.id,
                            },
                            {},
                          ),
                      }}
                      onSuccess={() => {
                        table.current?.reload();
                      }}
                      confirmText="是否强制将该账户下线"
                      type="danger"
                    >
                      强制下线
                    </Text.Action>
                  )}

                  <Text
                    onClick={() => {
                      detail.current?.open({
                        rowData: row,
                      });
                    }}
                  >
                    详情
                  </Text>
                </Action>
              );
            },
          },
          // end 列表项

          // 搜索项
          {
            title: '登录时间',
            dataIndex: '登录时间',
            valueType: 'dateTimeRange',
            hideInTable: true,
            hideInSetting: true,
            search: {
              transform: (res: string[]) => {
                return {
                  loginBeginTime: res[0],
                  loginEndTime: res[1],
                };
              },
            },
          },
          {
            title: '退出时间',
            dataIndex: '退出时间',
            valueType: 'dateTimeRange',
            hideInTable: true,
            hideInSetting: true,
            search: {
              transform: (res: string[]) => {
                return {
                  outBeginTime: res[0],
                  outEndTime: res[1],
                };
              },
            },
          },
          {
            title: '客户端类型',
            dataIndex: 'clientType',
            hideInTable: true,
            hideInSetting: true,
            valueType: 'dict',
            fieldProps: {
              dicCode: 'clientType',
            },
          },
          {
            title: '是否在线',
            dataIndex: 'online',
            valueType: 'enum',
            valueEnum: NumBoolObj,
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '请求IP',
            dataIndex: 'loginIp',
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '请求城市',
            dataIndex: 'loginCity',
            hideInTable: true,
            hideInSetting: true,
          },
          // end 搜索项
        ]}
      />

      <Detail ref={detail} />
    </PageContainer>
  );
};

export default Page;
