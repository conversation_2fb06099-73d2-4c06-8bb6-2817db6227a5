import Dict from '@/components/dict';
import { color } from '@/config/color';
import { formatValue } from '@/utils';
import { renderEnum } from '@/valueType/valueType/enum';
import { ProDescriptions } from '@ant-design/pro-components';
import { Drawer, Tag } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import * as Enums from '../enum';
import type { BizObject } from './index';

export type DetailRef = {
  open: (arg: {
    /** 当前编辑的数据 */
    rowData: BizObject;
  }) => void;
};

export type DetailProps = {};

const Detail: React.ForwardRefRenderFunction<DetailRef, DetailProps> = (props, ref) => {
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        setRowData(arg.rowData);
        setOpen(true);
      },
    };
  });

  return (
    <Drawer title={'查看详情'} width="50%" open={open} onClose={setOpen.bind({}, false)}>
      <ProDescriptions size="small" column={2} bordered title="登录信息">
        <ProDescriptions.Item label="是否在线">
          {rowData?.online ? (
            <Tag color={color.green}>在线</Tag>
          ) : (
            <Tag color={color.red}>离线</Tag>
          )}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="创建时间" valueType="dateTime">
          {rowData?.createTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="登录时间" valueType="dateTime">
          {rowData?.loginTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="登出时间" valueType="dateTime">
          {rowData?.logoutTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="失效时间" valueType="dateTime">
          {rowData?.expireTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="登出方式">
          {renderEnum(rowData?.logoutType, Enums.LogoutTypeObj)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="登出说明">{rowData?.logoutExplain}</ProDescriptions.Item>

        <ProDescriptions.Item label="token">{rowData?.token}</ProDescriptions.Item>
        {/* <ProDescriptions.Item label="扩展预留字段">{rowData?.extend}</ProDescriptions.Item> */}
      </ProDescriptions>

      <div style={{ height: 40 }}></div>

      <ProDescriptions size="small" column={2} bordered title="客户端信息">
        <ProDescriptions.Item label="登录城市">{rowData?.loginCity}</ProDescriptions.Item>
        <ProDescriptions.Item label="登录ip">{rowData?.loginIp}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求客户端类型">
          <Dict dicCode="clientType" value={rowData?.clientType} />
        </ProDescriptions.Item>
        <ProDescriptions.Item label="操作系统">{rowData?.systemName}</ProDescriptions.Item>
        <ProDescriptions.Item label="浏览器">{rowData?.browser}</ProDescriptions.Item>
      </ProDescriptions>

      <div style={{ height: 40 }}></div>

      <ProDescriptions size="small" column={2} bordered title="用户信息">
        {/* <ProDescriptions.Item label="id">{formatValue(rowData?.id)}</ProDescriptions.Item> */}
        <ProDescriptions.Item label="用户id">{formatValue(rowData?.userId)}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求用户名">
          {formatValue(rowData?.username)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求用户姓名">
          {formatValue(rowData?.fullName)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求用户类型">
          <Dict dicCode="userType" value={rowData?.userType} />
        </ProDescriptions.Item>
      </ProDescriptions>
    </Drawer>
  );
};

export default forwardRef(Detail);
