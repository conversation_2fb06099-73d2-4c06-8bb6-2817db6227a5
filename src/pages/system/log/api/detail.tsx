import Dict from '@/components/dict';
import Text from '@/components/text';
import { apiLogGet } from '@/services/base/authApiLog';
import { formatValue } from '@/utils';
import { renderEnum } from '@/valueType/valueType/enum';
import { ProDescriptions } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Drawer } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import ReactJson from 'react-json-view';
import * as Enums from '../enum';
import type { BizObject } from './index';

export type DetailRef = {
  open: (arg: {
    /** 当前编辑的数据 */
    rowData: BizObject;
  }) => void;
};

export type DetailProps = {};

const Detail: React.ForwardRefRenderFunction<DetailRef, DetailProps> = (props, ref) => {
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  const detail = useRequest(apiLogGet, {
    manual: true,
  });

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        setRowData(arg.rowData);
        setOpen(true);

        detail.run({
          id: arg.rowData.id as unknown as string,
        });
      },
    };
  });

  const data = detail.data;

  return (
    <Drawer title={'查看详情'} width="50%" open={open} onClose={setOpen.bind({}, false)}>
      <ProDescriptions size="small" column={1} bordered title="接口信息">
        <ProDescriptions.Item label="id">{formatValue(data?.id)}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求接口地址">
          {formatValue(data?.reqApi)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求类型">
          {renderEnum(data?.reqType, Enums.MethodObj)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求header">
          <ReactJson
            collapsed
            displayObjectSize={false}
            displayDataTypes={false}
            enableClipboard={false}
            theme="ashes"
            src={JSON.parse(data?.reqHeader || '{}')}
          />
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求参数">
          <ReactJson
            collapsed
            displayObjectSize={false}
            displayDataTypes={false}
            enableClipboard={false}
            theme="ashes"
            src={JSON.parse(data?.reqParam || '{}')}
          />
        </ProDescriptions.Item>

        <ProDescriptions.Item label="返回结果状态">
          {formatValue(data?.resStatus)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="返回结果消息">
          <ReactJson
            collapsed
            displayObjectSize={false}
            displayDataTypes={false}
            enableClipboard={false}
            theme="ashes"
            src={JSON.parse(data?.resStr || '{}')}
          />
        </ProDescriptions.Item>
        <ProDescriptions.Item label="日志级别">
          {renderEnum(data?.logLevel, Enums.LevelObj)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="日志说明">
          {formatValue(data?.logExplain)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求token">{formatValue(data?.reqToken)}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求开始时间" valueType="dateTime">
          {data?.startTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求结束时间" valueType="dateTime">
          {data?.endTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求结束时间" valueType="dateTime">
          {data?.createTime}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求耗时">
          <Text>{formatValue(((data?.costTime || 0) / 1000).toFixed(3))}s</Text>
        </ProDescriptions.Item>
        {/* <ProDescriptions.Item label="扩展字段">{data?.extend}</ProDescriptions.Item> */}
      </ProDescriptions>

      <div style={{ height: 40 }}></div>

      <ProDescriptions size="small" column={2} bordered title="客户端信息">
        <ProDescriptions.Item label="请求ip">{formatValue(data?.reqIp)}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求城市">{formatValue(data?.reqCity)}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求客户端类型">
          <Dict dicCode="clientType" value={data?.clientType} />
        </ProDescriptions.Item>
        <ProDescriptions.Item label="操作系统">{data?.systemName}</ProDescriptions.Item>
        <ProDescriptions.Item label="浏览器">{data?.browser}</ProDescriptions.Item>
      </ProDescriptions>

      <div style={{ height: 40 }}></div>

      <ProDescriptions size="small" column={2} bordered title="用户信息">
        <ProDescriptions.Item label="id">{formatValue(data?.id)}</ProDescriptions.Item>
        <ProDescriptions.Item label="用户id">{formatValue(data?.userId)}</ProDescriptions.Item>
        <ProDescriptions.Item label="请求用户名">
          {formatValue(data?.username)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求用户姓名">
          {formatValue(data?.fullName)}
        </ProDescriptions.Item>
        <ProDescriptions.Item label="请求用户类型">
          <Dict dicCode="userType" value={data?.userType} />
        </ProDescriptions.Item>
      </ProDescriptions>
    </Drawer>
  );
};

export default forwardRef(Detail);
