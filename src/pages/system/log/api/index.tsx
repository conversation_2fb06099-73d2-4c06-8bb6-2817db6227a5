import Action from '@/components/action';
import Des from '@/components/des';
import Dict from '@/components/dict';
import ProTable from '@/components/proTable';
import Text from '@/components/text';
import { color } from '@/config/color';
import { apiLogPage } from '@/services/base/authApiLog';
import { formatDate, formatValue } from '@/utils';
import { renderEnum } from '@/valueType/valueType/enum';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useRef } from 'react';
import * as Enums from '../enum';
import Detail, { DetailRef } from './detail';

/** 该模块增删改查对象 */
export type BizObject = BASE.ApiLog;

const Page: React.FC = () => {
  const table = useRef<ActionType>();
  const detail = useRef<DetailRef>(null);

  return (
    <PageContainer>
      <ProTable<BizObject>
        actionRef={table}
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await apiLogPage({
            page: current,
            size: pageSize,
            ...otherQuery,
          });

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          // 列表项
          {
            title: '日志级别',
            dataIndex: 'logLevel',
            valueType: 'enum',
            valueEnum: Enums.LevelObj,
            hideInSearch: true,
          },
          {
            title: '请求源',
            hideInSearch: true,
            render: (_, r) => {
              return (
                <Des>
                  <Des.Item label="请求IP" value={formatValue(r.reqIp)} />
                  <Des.Item label="浏览器" value={formatValue(r.browser)} />
                  <Des.Item label="操作系统" value={formatValue(r.systemName)} />
                  <Des.Item label="请求城市" value={formatValue(r.reqCity)} />
                </Des>
              );
            },
          },
          {
            title: '请求接口',
            hideInSearch: true,
            render: (_, r) => {
              return (
                <Des>
                  <Des.Item
                    label="接口"
                    value={
                      <span>
                        {renderEnum(r.reqType, Enums.MethodObj)}{' '}
                        {r.reqApi.length > 30 ? (
                          <Tooltip title={r.reqApi}>
                            <Tag color="blue">{r.reqApi.slice(0, 30)}...</Tag>
                          </Tooltip>
                        ) : (
                          <Tag color="blue">{r.reqApi}</Tag>
                        )}
                      </span>
                    }
                  />
                  <Des.Item label="接口描述" value={formatValue(r.logExplain)} />
                  {/* <Des.Item label="请求参数" value={r.reqParam} />
                  <Des.Item label="headers" value={r.reqHeader} /> */}
                </Des>
              );
            },
          },
          {
            title: '请求返回',
            hideInSearch: true,
            render: (_, r) => {
              const status =
                r.resStatus === 200 ? (
                  <Tag color={color.green}>{r.resStatus}</Tag>
                ) : (
                  <Tag color={color.red}>{r.resStatus}</Tag>
                );

              return (
                <Des>
                  <Des.Item label="状态" value={status} />
                  <Des.Item label="说明" value={r.resMessage} />
                  {/* <Des.Item label="返回数据" value={r.resStr} /> */}
                </Des>
              );
            },
          },
          {
            title: '请求时间',
            hideInSearch: true,
            render: (_, r) => {
              return (
                <Des>
                  <Des.Item
                    label="开始"
                    value={<Text>{formatDate(r.startTime, 'YYYY-MM-DD HH:mm:ss')}</Text>}
                  />
                  <Des.Item
                    label="结束"
                    value={<Text>{formatDate(r.endTime, 'YYYY-MM-DD HH:mm:ss')}</Text>}
                  />
                  <Des.Item label="距今" value={<Text>{dayjs(r.startTime).fromNow()}</Text>} />
                  <Des.Item
                    label="耗时"
                    value={<Text>{formatValue(((r.costTime || 0) / 1000).toFixed(3))}s</Text>}
                  />
                  <Des.Item
                    label="创建时间"
                    value={<Text>{formatDate(r.createTime, 'YYYY-MM-DD HH:mm:ss')}</Text>}
                  />
                </Des>
              );
            },
          },
          {
            title: '请求用户',
            hideInSearch: true,
            render: (_, r) => {
              if (String(r.userId) === '0') {
                return formatValue();
              }

              return (
                <Des>
                  <Des.Item label="用户id" value={formatValue(r.userId)} />
                  <Des.Item
                    label="用户类型"
                    value={<Dict dicCode="userType" value={r.userType} />}
                  />
                  <Des.Item label="用户姓名" value={formatValue(r.fullName)} />
                  <Des.Item label="用户名" value={formatValue(r.username)} />
                  <Des.Item
                    label="请求客户端"
                    value={<Dict dicCode="clientType" value={r.clientType} />}
                  />
                </Des>
              );
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            fixed: 'right',
            render: (_, row) => {
              return (
                <Action>
                  <Text
                    onClick={() => {
                      detail.current?.open({
                        rowData: row,
                      });
                    }}
                  >
                    详情
                  </Text>
                </Action>
              );
            },
          },
          // end 列表项

          // 搜索项
          {
            title: '日志级别',
            dataIndex: 'logLevel',
            valueType: 'enum',
            valueEnum: Enums.LevelObj,
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '请求时间',
            dataIndex: '请求时间',
            valueType: 'dateTimeRange',
            hideInTable: true,
            hideInSetting: true,
            fieldProps: {
              showTime: true,
            },
            search: {
              transform: (res: string[]) => {
                return {
                  endBeginTime: res[0],
                  endEndTime: res[1],
                };
              },
            },
          },
          {
            title: '用户类型',
            dataIndex: 'userType',
            hideInTable: true,
            hideInSetting: true,
            valueType: 'dict',
            fieldProps: {
              dicCode: 'userType',
            },
          },
          {
            title: '客户端类型',
            dataIndex: 'clientType',
            hideInTable: true,
            hideInSetting: true,
            valueType: 'dict',
            fieldProps: {
              dicCode: 'clientType',
            },
          },
          {
            title: '用户名',
            dataIndex: 'username',
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '用户姓名',
            dataIndex: 'fullName',
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '接口地址',
            dataIndex: 'reqApi',
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '请求IP',
            dataIndex: 'reqIp',
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '请求城市',
            dataIndex: 'reqCity',
            hideInTable: true,
            hideInSetting: true,
          },
          // end 搜索项
        ]}
      />

      <Detail ref={detail} />
    </PageContainer>
  );
};

export default Page;
