import RegexpInput from '@/components/regexpInput';
import * as rules from '@/config/rule';
import * as Enums from '@/enums/cfg';
import { systemConfigAdd, systemConfigUpdate } from '@/services/base/baseCfg';
import {
  DrawerForm,
  FormInstance,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { getComponentByCfgType } from './utils';

export type EditRef = {
  open: (arg: {
    configModule: string;
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BASE.SystemConfig;
    /** 成功回调 */

    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = (props, ref) => {
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  const [configModule, setConfigModule] = useState('');
  // 当前选中行数据
  const [rowData, setRowData] = useState<BASE.SystemConfig>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        setConfigModule(arg.configModule);
        successCb.current = arg.onSuccess;
        setOpen(true);
        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue(arg.rowData);
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({});
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BASE.SystemConfig>
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
          configModule,
          configValue: query.configValue ?? null,
        };

        await (editable
          ? systemConfigUpdate({
              ...baseQuery,
              id: rowData?.id as number,
            })
          : systemConfigAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormText
        label="配置名称"
        name="configName"
        rules={[
          {
            required: true,
            message: '此项必填',
          },
          {
            validator: rules.noSpaceValidator,
          },
          {
            validator: async (_, value) => {
              // 不对空值校验
              if (value === '' || value === null || typeof value === 'undefined') {
                return;
              }

              const max = 200;

              if (String(value).length > max) {
                throw new Error(`最多只能输入${max}个字符`);
              }
            },
          },
        ]}
      />

      <ProFormText
        label="配置编码"
        name="configCode"
        rules={[
          {
            required: true,
            message: '此项必填',
          },
          {
            validator: rules.noSpaceValidator,
          },
          {
            validator: async (_, value) => {
              // 不对空值校验
              if (value === '' || value === null || typeof value === 'undefined') {
                return;
              }

              const max = 200;

              if (String(value).length > max) {
                throw new Error(`最多只能输入${max}个字符`);
              }
            },
          },
        ]}
      />

      <ProFormRadio.Group
        label="配置环境"
        name="configEnvironment"
        valueEnum={Enums.SysCfgEnvObj}
        rules={[{ required: true, message: '请选择配置环境' }]}
      />

      <ProFormRadio.Group
        label="配置类型"
        name="configType"
        valueEnum={Enums.SysCfgTypeObj}
        rules={[{ required: true, message: '请选择配置类型' }]}
        fieldProps={{
          onChange: () => {
            // 清空配置值
            form.current?.setFieldsValue({
              configValue: undefined,
            });
          },
        }}
      />

      <ProFormDependency name={['configType']}>
        {({ configType }) => {
          const baseProps = {
            label: '配置值',
            name: 'configValue',
          };

          const Component = getComponentByCfgType(configType);

          if (React.isValidElement(Component)) {
            return React.cloneElement(Component, baseProps);
          }

          return null;
        }}
      </ProFormDependency>

      <ProForm.Item label="配置校验正则" name="configCheckRegular">
        <RegexpInput />
      </ProForm.Item>

      <ProFormDigit
        label="排序"
        name="sortNo"
        fieldProps={{
          step: 1,
        }}
      />

      <ProFormText label="配置说明" name="configExplain" />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
