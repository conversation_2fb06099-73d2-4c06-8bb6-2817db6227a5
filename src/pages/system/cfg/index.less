.container {
  :global {
    .ant-tabs-top > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
      padding-bottom: 0;
    }

    .ant-tabs-left > .ant-tabs-content-holder {
      // margin-left: 0;
      // border-left: none;
    }

    .ant-tabs-top > .ant-tabs-nav {
      margin-bottom: 0;
    }

    .ant-tabs-top > .ant-tabs-nav::before {
      display: none;
    }

    .ant-tabs-top {
      .ant-card {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }
    }
  }
}

.tabs-icon {
  margin-right: 4px !important;
}

.edit-icon {
  margin-right: -8px !important;
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.form-item-label {
  display: flex;

  &-text {
    min-width: 0;
  }

  &-icon {
    flex-shrink: 0;
    margin-left: 8px;
    color: @primary;

    &.delete {
      color: @danger;
    }
  }
}

.action {
  margin-bottom: 24px;
}
