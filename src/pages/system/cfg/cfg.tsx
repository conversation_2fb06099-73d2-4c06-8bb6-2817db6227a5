import { IconAnt } from '@/components/icon';
import DictEdit, { EditRef as DictEditRef } from '@/pages/system/dic/list/edit';
import { systemConfigDelete, systemConfigList, systemConfigSet } from '@/services/base/baseCfg';
import { dicInfoDelete } from '@/services/base/baseDict';
import { dicMainGetDicCode } from '@/services/base/baseDictMain';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { useModel, useRequest } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Empty,
  message,
  Modal,
  Popconfirm,
  Row,
  Skeleton,
  Switch,
  Tabs,
} from 'antd';
import cs from 'classnames';
import type { Rule } from 'rc-field-form/lib/interface';
import React, { useRef, useState } from 'react';
import Edit, { EditRef } from './edit';
import styles from './index.less';
import { getComponentByCfgType } from './utils';

type PanelProps = {
  editable: boolean;
  dict: BASE.DicInfoVO;
};

const Panel: React.FC<PanelProps> = ({ editable, dict }) => {
  const { refresh } = useModel('@@initialState');
  const edit = useRef<EditRef>(null);
  const formRef = useRef<ProFormInstance>();
  const data = useRequest(() => {
    return systemConfigList({
      configModule: dict.dicValue,
    } as ArgumentsType<typeof systemConfigList>[0]);
  });

  const list = data.data || [];

  return (
    <Card size="small">
      <Skeleton loading={data.loading}>
        {editable && (
          <div className={styles['action']}>
            <Button
              onClick={() => {
                edit.current?.open({
                  configModule: dict.dicValue,
                  editable: false,
                  onSuccess: () => {
                    data.run();
                  },
                });
              }}
            >
              新增字段
            </Button>
          </div>
        )}

        {list.length <= 0 ? (
          <Empty />
        ) : (
          <Row gutter={[24, 24]}>
            <Col lg={12} xs={24}>
              <ProForm
                layout="horizontal"
                formRef={formRef}
                onFinish={async (query) => {
                  const list = Object.entries(query).map((v) => ({
                    id: v[0],
                    value: v[1],
                  }));

                  await systemConfigSet({
                    list,
                  } as unknown as ArgumentsType<typeof systemConfigSet>[0]);

                  // 刷新列表
                  data.run();
                  // 刷新用户数据
                  refresh();
                  message.success('保存成功');

                  return true;
                }}
              >
                {list.map((item) => {
                  const baseProps = {
                    key: item.id,
                    name: item.id,
                    label: (
                      <div className={styles['form-item-label']}>
                        <div className={styles['form-item-label-text']}>{item.configName}</div>

                        {editable && (
                          <>
                            <EditOutlined
                              title="编辑"
                              className={styles['form-item-label-icon']}
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();

                                edit.current?.open({
                                  configModule: dict.dicValue,
                                  editable: true,
                                  rowData: item,
                                  onSuccess: () => {
                                    data.run();
                                  },
                                });
                              }}
                            />

                            <Popconfirm
                              title="确定删除吗?"
                              onConfirm={async (e) => {
                                e?.preventDefault();
                                e?.stopPropagation();

                                await systemConfigDelete({
                                  id: item.id,
                                } as unknown as ArgumentsType<typeof systemConfigDelete>[0]);

                                message.success('删除成功');
                                data.run();
                              }}
                            >
                              <DeleteOutlined
                                title="删除"
                                className={cs(styles['form-item-label-icon'], styles['delete'])}
                              />
                            </Popconfirm>
                          </>
                        )}
                      </div>
                    ),
                    initialValue: item.configValue,
                    extra: item.configExplain,
                    rules: [] as Rule[],
                  };

                  if (item.configCheckRegular) {
                    baseProps.rules.push({
                      pattern: new RegExp(item.configCheckRegular),
                      message: item.configExplain || '校验失败',
                    });
                  }
                  const Component = getComponentByCfgType(item.configType);

                  if (React.isValidElement(Component)) {
                    return React.cloneElement(Component, baseProps);
                  }

                  return null;
                })}
              </ProForm>
            </Col>
          </Row>
        )}
      </Skeleton>

      <Edit ref={edit} />
    </Card>
  );
};

const Cfg: React.FC = () => {
  const { initialState, refresh } = useModel('@@initialState');
  const dictEdit = useRef<DictEditRef>(null);
  const configTabList = initialState?.dict?.['sysConfigModule'] || [];
  const [editable, setEditable] = useState(true);

  const onEdit: React.ComponentProps<typeof Tabs>['onEdit'] = async (key, action) => {
    if (action === 'add') {
      // 获取主键id
      const main = await dicMainGetDicCode({
        code: 'sysConfigModule',
      });

      if (!main.data) {
        message.error('获取主键失败');
        return;
      }

      dictEdit.current?.open({
        mainId: main.data.id!,
        editable: false,
        onSuccess: () => {
          refresh();
        },
      });

      return;
    }

    if (action === 'remove') {
      Modal.confirm({
        title: '删除',
        content: '确定删除吗？',
        onOk: async () => {
          await dicInfoDelete({
            id: key as string,
          });

          message.success('删除成功');

          refresh();
        },
      });

      return;
    }
  };

  return (
    <div className={styles['container']}>
      <Tabs
        size="small"
        type={editable ? 'editable-card' : 'card'}
        items={configTabList.map((item) => {
          return {
            label: (
              <span>
                {item.dicIcon ? (
                  <IconAnt
                    className={styles['tabs-icon']}
                    type={item.dicIcon as React.ComponentProps<typeof IconAnt>['type']}
                  />
                ) : null}
                {item.dicName}
                {editable && (
                  <EditOutlined
                    title="编辑"
                    className={styles['edit-icon']}
                    onClick={(e) => {
                      e.stopPropagation();
                      dictEdit.current?.open({
                        mainId: item.mainId,
                        editable: true,
                        rowData: item,
                        onSuccess: () => {
                          refresh();
                        },
                      });
                    }}
                  />
                )}
              </span>
            ),
            key: item.id as unknown as string,
            children: <Panel dict={item} editable={editable} />,
          };
        })}
        onEdit={onEdit}
        tabBarExtraContent={
          <Switch
            checked={editable}
            onChange={setEditable}
            checkedChildren="开启编辑"
            unCheckedChildren="关闭编辑"
          />
        }
      />

      <DictEdit ref={dictEdit} />
    </div>
  );
};

export default Cfg;
