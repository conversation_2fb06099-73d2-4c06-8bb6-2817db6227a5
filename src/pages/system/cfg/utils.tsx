import SelectColor from '@/components/select/color';
import Upload, { BizUploadFile, formatUploadFile } from '@/components/upload';
import { SysCfgType } from '@/enums/cfg';
import { parseSrc } from '@/utils';
import {
  ProForm,
  ProFormDateTimePicker,
  ProFormDateTimeRangePicker,
  ProFormDigit,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';

/**
 * 根据配置类型获取配置表单项
 */
export const getComponentByCfgType = (configType?: undefined | SysCfgType): React.ReactNode => {
  const baseProps = {
    label: '配置值',
    name: 'configValue',
  };

  // 短字符串
  if (configType === SysCfgType.string) {
    return <ProFormText {...baseProps} />;
  }

  // 长文本
  if (configType === SysCfgType.text) {
    return <ProFormTextArea {...baseProps} />;
  }

  // 短整型 长整型
  if (configType === SysCfgType.int || configType === SysCfgType.long) {
    return <ProFormDigit {...baseProps} fieldProps={{}} />;
  }

  // 布尔
  if (configType === SysCfgType.bool) {
    return (
      <ProFormSwitch
        {...baseProps}
        convertValue={(value) => {
          return (value === true || value === 'true') as unknown as string;
        }}
        transform={(value, namePath) => {
          return {
            [namePath as string]: typeof value === 'boolean' ? String(value) : value,
          };
        }}
      />
    );
  }

  // 时间
  if (configType === SysCfgType.date) {
    return <ProFormDateTimePicker {...baseProps} />;
  }

  // 时间范围
  if (configType === SysCfgType.scopeDate) {
    return (
      <ProFormDateTimeRangePicker
        {...baseProps}
        convertValue={(value) => {
          return typeof value === 'string' ? value.split(',') : value;
        }}
        transform={(value, namePath) => {
          return {
            [namePath as string]: Array.isArray(value) ? value.join(',') : value,
          };
        }}
      />
    );
  }

  // 图片
  if (configType === SysCfgType.picture) {
    return (
      <ProForm.Item
        {...baseProps}
        convertValue={(value) => {
          const token = localStorage.getItem('token');
          return typeof value === 'string' ? [value + `?token=${token}`] : value;
        }}
        transform={(value, namePath) => {
          return {
            [namePath as string]: Array.isArray(value)
              ? (value as BizUploadFile[]).map((v) => v.response?.data?.fileUrl).join(',')
              : value,
          };
        }}
      >
        <Upload onlyImg  />
      </ProForm.Item>
    );
  }

  // 多图片
  if (configType === SysCfgType.pictures) {
    return (
      <ProForm.Item
        {...baseProps}
        convertValue={(value) => {
          return typeof value === 'string' ? formatUploadFile(value.split(',')) : value;
        }}
        transform={(value, namePath) => {
          return {
            [namePath as string]: Array.isArray(value)
              ? (value as BizUploadFile[]).map((v) => v.response?.data?.fileUrl).join(',')
              : value,
          };
        }}
      >
        <Upload
          multiple
          listType="picture-card"
          accept={['gif', 'jpeg', 'jpg', 'png'].map((v) => `image/${v}`).join(',')}
        >
          <div>
            <div style={{ marginTop: 8 }}>上传</div>
          </div>
        </Upload>
      </ProForm.Item>
    );
  }

  // 文件
  if (configType === SysCfgType.file) {
    return (
      <ProForm.Item
        {...baseProps}
        convertValue={(value) => {
          return typeof value === 'string' ? formatUploadFile(value.split(',')) : value;
        }}
        transform={(value, namePath) => {
          return {
            [namePath as string]: Array.isArray(value)
              ? (value as BizUploadFile[]).map((v) => v.response?.data?.fileUrl).join(',')
              : value,
          };
        }}
      >
        <Upload multiple={false}>
          <button type="button">上传</button>
        </Upload>
      </ProForm.Item>
    );
  }

  // 多文件
  if (configType === SysCfgType.files) {
    return (
      <ProForm.Item
        {...baseProps}
        convertValue={(value) => {
          return typeof value === 'string' ? formatUploadFile(value.split(',')) : value;
        }}
        transform={(value, namePath) => {
          return {
            [namePath as string]: Array.isArray(value)
              ? (value as BizUploadFile[]).map((v) => v.response?.data?.fileUrl).join(',')
              : value,
          };
        }}
      >
        <Upload multiple>
          <button type="button">上传</button>
        </Upload>
      </ProForm.Item>
    );
  }

  // 后端
  if (configType === SysCfgType.colour) {
    return (
      <ProForm.Item {...baseProps}>
        <SelectColor />
      </ProForm.Item>
    );
  }

  return null;
};
