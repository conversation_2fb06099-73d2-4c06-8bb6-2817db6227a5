import * as rules from '@/config/rule';
import { dicMainAdd, dicMainUpdate } from '@/services/base/baseDictMain';
import {
  DrawerForm,
  FormInstance,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import * as Enum from './enum';
import type { BizObject } from './index';

export type EditRef = {
  open: (arg: {
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = (props, ref) => {
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);
        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue(arg.rowData);
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              valueType: Enum.DicValueType.字符串,
            });
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BizObject>
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
        };

        await (editable
          ? dicMainUpdate({
              ...baseQuery,
              id: rowData?.id as number,
            })
          : dicMainAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormText
        label="字典名称"
        name="dicName"
        rules={[{ required: true, message: '请填写字典名称' }]}
      />

      <ProFormText
        label="字典标识"
        name="dicCode"
        rules={[
          { required: true, message: '请填写标识' },
          {
            validator: rules.noSpaceValidator,
          },
        ]}
      />

      <ProFormRadio.Group
        label="字典值类型"
        name="valueType"
        valueEnum={Enum.DicValueTypeObj}
        rules={[{ required: true, message: '请选择字典值类型' }]}
      />

      <ProFormTextArea label="说明" name="dicExplain" />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
