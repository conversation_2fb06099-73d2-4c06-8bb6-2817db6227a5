import { color } from '@/config/color';

/** 字典类型 */
export enum DicType {
  /** 系统字典 */
  系统字典 = 0,
  /** 用户字典 */
  用户字典 = 1,
}

/** 字典类型 */
export const DicTypeObj: ValueEnumMap<number> = new Map([
  [
    DicType.系统字典,
    {
      text: '系统字典',
      status: color.red,
    },
  ],
  [
    DicType.用户字典,
    {
      text: '用户字典',
      status: color.blue,
    },
  ],
]);

/** 字典值类型 */
export enum DicValueType {
  /** 字符串 */
  字符串 = 3,
  /** 数值 */
  数值 = 1,
  /** 布尔 */
  布尔 = 2,
}

/** 字典值类型 */
export const DicValueTypeObj: ValueEnumMap<number> = new Map([
  [
    DicValueType.字符串,
    {
      text: '字符串',
      status: color.gold,
    },
  ],
  [
    DicValueType.数值,
    {
      text: '数值',
      status: color.blue,
    },
  ],
  [
    DicValueType.布尔,
    {
      text: '布尔',
      status: color.cyan,
    },
  ],
]);
