import Button from '@/components/button';
import { NumBool, NumBoolObj, TimeSort } from '@/enums/base';
import { dicInfoImport, dicInfoTemplate } from '@/services/base/baseDict';
import { dicMainPage } from '@/services/base/baseDictMain';
import { DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import {
  DrawerForm,
  FormInstance,
  ProForm,
  ProFormRadio,
  ProFormSelect,
} from '@ant-design/pro-components';
import { message, Upload, UploadFile } from 'antd';
import Saver from 'file-saver';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

export type ImportRef = {
  open: (arg: {
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type ImportProps = {};

const Import: React.ForwardRefRenderFunction<ImportRef, ImportProps> = (props, ref) => {
  const form = useRef<FormInstance>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  // 成功回调函数
  const successCb = useRef<() => void>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        setFileList([]);
        successCb.current = arg.onSuccess;
        setOpen(true);
      },
    };
  });

  return (
    <DrawerForm
      title={'导入字典信息'}
      width={400}
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      isKeyPressSubmit
      onFinish={async (query) => {
        const formData = new FormData();
        formData.append('file', fileList[0].originFileObj!);
        formData.append('dicCode', query.dicCode);
        formData.append('cover', query.cover);

        await dicInfoImport(
          {} as ArgumentsType<typeof dicInfoImport>[0],
          {},
          {
            method: 'POST',
            data: formData,
            requestType: 'form',
            timeout: 60000,
          },
        );

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
      initialValues={{
        cover: NumBool.是,
      }}
      drawerProps={{
        extra: (
          <div>
            <Button.Action
              icon={<DownloadOutlined />}
              request={{
                service: async () => {
                  const res = await dicInfoTemplate({
                    method: 'POST',
                    skipErrorHandler: true,
                    getResponse: true,
                    responseType: 'blob',
                  });

                  // 获取文件名
                  const contentDisposition = res.headers['content-disposition'];
                  let filename = '';

                  if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                    let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    let matches = filenameRegex.exec(contentDisposition);
                    if (matches !== null && matches[1]) {
                      filename = decodeURIComponent(matches[1].replace(/['"]/g, ''));
                    }
                  }

                  Saver.saveAs(res.data, filename);
                },
                format: (_:any, props:any) => {
                  return {
                    ...props,
                    loading: _.loading,
                    disabled: _.loading,
                  };
                },
              }}
              type="primary"
            >
              下载导入模板
            </Button.Action>
          </div>
        ),
      }}
    >
      <ProFormSelect
        label="字典"
        name="dicCode"
        request={async () => {
          const res = await dicMainPage({
            page: 1,
            size: 99999,
            timeSort: TimeSort.倒序,
          });

          return (res.data?.records || []).map((item) => ({
            label: item.dicName,
            value: item.dicCode,
          }));
        }}
        rules={[{ required: true, message: '请选择字典' }]}
      />

      <ProForm.Item label="文件" name="file" rules={[{ required: true, message: '请选择文件' }]}>
        <Upload.Dragger
          maxCount={1}
          accept=".xls,.xlsx"
          beforeUpload={() => {
            return false;
          }}
          fileList={fileList}
          onChange={(info) => {
            setFileList(info.fileList);
          }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单个或批量上传。</p>
        </Upload.Dragger>
      </ProForm.Item>

      <ProFormRadio.Group name="cover" valueEnum={NumBoolObj} label="字典值存在时是否覆盖" />
    </DrawerForm>
  );
};

export default forwardRef(Import);
