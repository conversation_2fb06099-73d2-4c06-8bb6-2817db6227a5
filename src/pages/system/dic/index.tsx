import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import { TimeSort } from '@/enums';
import { dicMainDelete, dicMainPage } from '@/services/base/baseDictMain';
import { UnorderedListOutlined, UploadOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { message, Space } from 'antd';
import { useRef } from 'react';
import Edit, { EditRef } from './edit';
import * as Enum from './enum';
import Import, { ImportRef } from './import';
import List, { ListRef } from './list';

/** 该模块增删改查对象 */
export type BizObject = BASE.DicMain;

const Page: React.FC = () => {
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);
  const list = useRef<ListRef>(null);
  const importModal = useRef<ImportRef>(null);

  return (
    <PageContainer>
      <ProTable<BizObject, {}, GlobalValueType>
        actionRef={table}
        rowKey="id"
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        toolBarRender={(_) => {
          const nodes: React.ReactElement[] = [];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                edit.current?.open({
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          nodes.push(
            <IconAction
              tooltip="导入"
              icon={<UploadOutlined />}
              key="import"
              onClick={() => {
                importModal.current?.open({
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await dicMainPage({
            page: current,
            size: pageSize,
            timeSort: TimeSort.倒序,
            ...otherQuery,
          });

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          {
            title: '字典名称',
            dataIndex: 'dicName',
          },
          {
            title: '字典标识',
            dataIndex: 'dicCode',
          },
          {
            title: '字典值类型',
            dataIndex: 'valueType',
            valueType: 'enum',
            valueEnum: Enum.DicValueTypeObj,
            hideInSearch: true,
          },
          {
            title: '说明',
            hideInSearch: true,
            dataIndex: 'dicExplain',
            valueType: 'textarea',
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="字典"
                    icon={<UnorderedListOutlined />}
                    onClick={() => {
                      list.current?.open({
                        rowData: row,
                        onChange: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        editable: true,
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    confirmText={`确定删除 ${row.dicName} 吗?`}
                    request={{
                      service: () =>
                        dicMainDelete({ id: row.id } as unknown as ArgumentsType<
                          typeof dicMainDelete
                        >[0]),
                    }}
                    onSuccess={() => {
                      message.success('删除成功');
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit ref={edit} />

      <List ref={list} />

      <Import ref={importModal} />
    </PageContainer>
  );
};

export default Page;
