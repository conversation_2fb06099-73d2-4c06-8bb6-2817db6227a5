import IconAction from '@/components/iconAction';
import Text from '@/components/text';
import * as rules from '@/config/rule';
import * as BaseEnum from '@/enums/base';
import { dicInfoAdd, dicInfoDelete, dicInfoPage, dicInfoUpdate } from '@/services/base/baseDict';
import { ActionType, EditableProTable } from '@ant-design/pro-components';
import { Drawer, Space } from 'antd';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import * as Enum from '../enum';
import type { BizObject } from '../index';
import Edit, { EditRef } from './edit';

export type ListRef = {
  open: (arg: {
    /** 主字典对象 */
    rowData: BizObject;
    /** 当关闭弹窗时如果有修改 */
    onChange?: () => void;
  }) => void;
};

export type ListProps = {};

const List: React.ForwardRefRenderFunction<ListRef, ListProps> = (props, ref) => {
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<BizObject>();
  const onChangeCb = useRef<() => void>();
  // 是否修改过
  const [isChange, setIsChange] = useState(false);

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        setIsChange(false);
        setOpen(true);
        setData(arg.rowData);
        onChangeCb.current = arg.onChange;
      },
    };
  });

  return (
    <Drawer
      open={open}
      onClose={() => {
        setOpen(false);

        if (isChange) {
          onChangeCb.current?.();
        }
      }}
      title={
        <span>
          编辑 <Text>{data?.dicName}</Text> 字典列表
        </span>
      }
      width="50%"
    >
      <div
        style={{
          marginBottom: 16,
          textAlign: 'right',
        }}
      >
        <IconAction
          tooltip="新增"
          icon="add"
          onClick={() => {
            edit.current?.open({
              mainId: data?.id as number,
              editable: false,
              onSuccess: () => {
                table.current?.reload();
              },
            });
          }}
          size="middle"
        />
      </div>

      <EditableProTable<BASE.DicInfoVO, Record<string, any>, GlobalValueType>
        actionRef={table}
        size="small"
        search={false}
        pagination={{}}
        rowKey="id"
        scroll={{
          x: 'max-content',
        }}
        // recordCreatorProps={{
        //   position: 'bottom',
        //   record: (index, dataSource) => {
        //     return {
        //       id: -1,
        //     } as BASE.DicInfoVO;
        //   },
        // }}
        params={{
          mainId: data?.id,
        }}
        recordCreatorProps={false}
        request={async (page) => {
          const res = await dicInfoPage({
            page: page.current!,
            size: page.pageSize!,
            mainId: data?.id as number,
            // dicStatus: BaseEnum.Status.启用,
          } as ArgumentsType<typeof dicInfoPage>[0]);

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        editable={{
          onSave: async (_, { id, ...query }) => {
            const isEdit = id! >= 0;

            if (isEdit) {
              await dicInfoUpdate({
                ...query,
                mainId: data?.id as number,
                id,
              });
            } else {
              await dicInfoAdd({
                ...query,
                mainId: data?.id as number,
              });
            }

            setIsChange(true);
            table.current?.reload();
          },
          actionRender: (_, config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        columns={[
          {
            title: '字典名称',
            dataIndex: 'dicName',
            valueType: 'text',
            width: 120,
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项必填',
                },
                {
                  validator: rules.noSpaceValidator,
                },
                {
                  validator: async (_, value) => {
                    // 不对空值校验
                    if (value === '' || value === null || typeof value === 'undefined') {
                      return;
                    }

                    const max = 200;

                    if (String(value).length > max) {
                      throw new Error(`最多只能输入${max}个字符`);
                    }
                  },
                },
              ],
            },
          },
          {
            title: '字典值',
            dataIndex: 'dicValue',
            width: 120,
            valueType() {
              if (data?.valueType === Enum.DicValueType.数值) {
                return 'digit';
              }

              if (data?.valueType === Enum.DicValueType.布尔) {
                return 'switch';
              }

              if (data?.valueType === Enum.DicValueType.字符串) {
                return 'text';
              }

              return 'text';
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项必填',
                },
              ],
            },
          },
          {
            title: '状态',
            dataIndex: 'dicStatus',
            valueType: 'enum',
            valueEnum: BaseEnum.StatusObj,
            width: 80,
          },
          {
            title: '字典颜色',
            dataIndex: 'dicColour',
            valueType: 'color',
            width: 80,
          },
          {
            title: '字典图标',
            dataIndex: 'dicIcon',
            valueType: 'iconAnt',
            width: 80,
          },
          {
            title: '排序',
            dataIndex: 'dicSort',
            valueType: 'digit',
            width: 60,
          },
          {
            title: '说明',
            dataIndex: 'dicExplain',
            valueType: 'text',
            width: 50,
          },
          {
            title: '操作',
            valueType: 'option',
            fixed: 'right',
            align: 'center',
            width: 100,
            render: (text, r, _, action) => {
              return (
                <Space>
                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        mainId: data?.id as number,
                        editable: true,
                        rowData: r,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    request={{
                      service: () =>
                        dicInfoDelete(
                          {
                            id: r.id,
                          } as ArgumentsType<typeof dicInfoDelete>[0],
                          {
                            id: r.id,
                          } as ArgumentsType<typeof dicInfoDelete>[1],
                        ),
                    }}
                    onSuccess={() => {
                      setIsChange(true);
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit ref={edit} />
    </Drawer>
  );
};

export default forwardRef(List);
