import ColorSelect from '@/components/select/color';
import IconSelect from '@/components/select/icon';
import * as BaseEnum from '@/enums/base';
import { dicInfoAdd, dicInfoUpdate } from '@/services/base/baseDict';
import {
  DrawerForm,
  FormInstance,
  ProFormDigit,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

export type EditRef = {
  open: (arg: {
    /** 主表id */
    mainId: number;
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BASE.DicInfoVO;
    /** 成功回调 */

    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = (props, ref) => {
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BASE.DicInfoVO>();
  const [mainId, setMainId] = useState<number>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        setMainId(arg.mainId);
        successCb.current = arg.onSuccess;
        setOpen(true);
        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue(arg.rowData);
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              dicStatus: BaseEnum.Status.启用,
            });
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BASE.DicInfoVO>
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
          mainId: mainId as number,
        };

        await (editable
          ? dicInfoUpdate({
              ...baseQuery,
              id: rowData?.id,
            })
          : dicInfoAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormText
        label="字典名称"
        name="dicName"
        rules={[
          {
            required: true,
            message: '此项必填',
          },
          // {
          //   validator: rules.noSpaceValidator,
          // },
          {
            validator: async (_, value) => {
              // 不对空值校验
              if (value === '' || value === null || typeof value === 'undefined') {
                return;
              }

              const max = 200;

              if (String(value).length > max) {
                throw new Error(`最多只能输入${max}个字符`);
              }
            },
          },
        ]}
      />

      <ProFormText
        label="字典值"
        name="dicValue"
        rules={[
          {
            required: true,
            message: '请填写字典值',
          },
        ]}
      />

      <ProFormRadio.Group
        label="状态"
        name="dicStatus"
        valueEnum={BaseEnum.StatusObj}
        rules={[{ required: true, message: '请选择状态' }]}
      />

      <Form.Item label="字典颜色" name="dicColour">
        <ColorSelect />
      </Form.Item>

      <Form.Item label="字典图标" name="dicIcon">
        <IconSelect />
      </Form.Item>

      <ProFormDigit
        label="排序"
        name="dicSort"
        fieldProps={{
          step: 1,
        }}
      />

      <ProFormTextArea label="说明" name="dicExplain" />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
