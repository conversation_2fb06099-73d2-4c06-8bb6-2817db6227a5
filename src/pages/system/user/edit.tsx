import Upload, { BizUploadFile } from '@/components/upload';
import * as rules from '@/config/rule';
import { loginPublicKey as loginPublicKeyApi } from '@/services/base/authLogin';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';
import { orgUserInfoAdd, orgUserInfoUpdate } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import { arr2ValueEnum, encrypt } from '@/utils';
import {
  DrawerForm,
  FormInstance,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Alert, Form, message } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import * as Enum from './enum';
import type { BizObject } from './index';

export type EditRef = {
  open: (arg: {
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = (props, ref) => {
  // 传输密码都需要加密,加密要用到的公钥需要接口获取
  const loginPublicKey = useRequest(loginPublicKeyApi, {
    manual: true,
  });
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);
        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              ...arg.rowData,
              // photoUrl: arg.rowData?.photoUrl ? formatUploadFile([arg.rowData.photoUrl]) : [],
            });
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              state: Enum.UserStatus.启用,
            });
          });
        }
      },
    };
  });

  return (
    <DrawerForm
      data-key="role-edit"
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async ({ password, photoUrl, ...query }) => {
        const baseQuery = {
          ...query,
        };

        if (photoUrl?.[0]) {
          if ((photoUrl[0] as unknown as BizUploadFile).status === 'uploading') {
            message.info('请等待图片上传完成');

            return false;
          }

          baseQuery.photoUrl = (photoUrl[0] as unknown as BizUploadFile).response?.data?.fileUrl;
        }

        // 新增时需要传递密码
        if (!editable) {
          const publicKey = await loginPublicKey.run();
          // 获取公钥后RSA算法加密密码
          const encryptPassword = await encrypt(password, async () => publicKey!);

          baseQuery.passWord = encryptPassword;
        }

        await (editable
          ? orgUserInfoUpdate({
              ...baseQuery,
              id: rowData?.id as number,
            })
          : orgUserInfoAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <Alert
        showIcon
        type="warning"
        message="默认密码是: 这里根据项目的实际情况来填写"
        style={{ marginBottom: 24 }}
      />

      <ProFormText
        label="用户名"
        name="userName"
        readonly={editable}
        required={!editable}
        rules={[
          {
            required: true,
            message: '请填写用户名',
          },
          {
            validator: rules.userNameValidator,
          },
        ]}
      />

      {
        // 编辑时不需要填写密码
        !editable && (
          <ProFormDependency name={['password', 'rePassword']}>
            {({ password, rePassword }) => {
              return (
                <>
                  <ProFormText.Password
                    label="密码"
                    name="password"
                    rules={[
                      {
                        required: true,
                        message: '请输入密码',
                      },
                      {
                        validator: rules.passwordValidator,
                      },
                      {
                        validator: async (_, value) => {
                          // 不对空值校验
                          if (
                            password === '' ||
                            password === null ||
                            typeof password === 'undefined'
                          ) {
                            return;
                          }

                          if (
                            rePassword === '' ||
                            rePassword === null ||
                            typeof rePassword === 'undefined'
                          ) {
                            return;
                          }

                          if (rePassword === password) {
                            return;
                          }

                          throw new Error('两次输入的密码不一致');
                        },
                      },
                    ]}
                  />

                  <ProFormText.Password
                    label="确认密码"
                    name="rePassword"
                    rules={[
                      {
                        required: true,
                        message: '请输入密码',
                      },
                      {
                        validator: async (_, value) => {
                          // 不对空值校验
                          if (
                            password === '' ||
                            password === null ||
                            typeof password === 'undefined'
                          ) {
                            return;
                          }

                          if (
                            rePassword === '' ||
                            rePassword === null ||
                            typeof rePassword === 'undefined'
                          ) {
                            return;
                          }

                          if (rePassword === password) {
                            return;
                          }

                          throw new Error('两次输入的密码不一致');
                        },
                      },
                    ]}
                  />
                </>
              );
            }}
          </ProFormDependency>
        )
      }

      <ProFormRadio.Group
        label="状态"
        name="enable"
        valueEnum={Enum.UserStatusObj}
        rules={[{ required: true, message: '请选择用户状态' }]}
      />

      <ProFormText
        label="真实姓名"
        name="fullName"
        rules={[{ required: true, message: '请填写真实姓名' }]}
      />

      <Form.Item label="头像" name="photoUrl">
        <Upload onlyImg />
      </Form.Item>

      <ProFormTreeSelect
        label="所属组织"
        name="deptNames"
        fieldProps={{
          multiple: true,
          fieldNames: {
            label: 'deptName',
            value: 'deptName',
            children: 'child',
          },
        }}
        request={async () => {
          let data = await orgDeptInfoTree();
          return data.data || [];
        }}
      />

      <ProFormTreeSelect
        label="所属专业"
        name="major"
        // 变压器”、“开关”、“材料”、“线圈”
        valueEnum={arr2ValueEnum(['变压器', '开关', '材料', '线圈'])}
      />

      <ProFormText
        label="电话号码"
        name="phone"
        rules={[
          // { required: true, message: '请填写电话号码' },
          {
            validator: rules.mobileValidator,
          },
        ]}
      />

      <ProFormText
        label="电子邮箱"
        name="email"
        rules={[
          // { required: true, message: '请填写电话号码' },
          {
            validator: rules.emailValidator,
          },
        ]}
      />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
