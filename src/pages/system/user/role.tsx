import { NumBool } from '@/enums';
import { roleAddByUser, roleGetByUser, roleTree } from '@/services/base/authRole';
import { DrawerForm, FormInstance, ProFormTreeSelect } from '@ant-design/pro-components';
import { message, TreeSelect } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { BizObject } from './index';

export type RoleRef = {
  open: (arg: {
    /** 当前编辑的数据 */
    rowData: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type RoleProps = {};

const Edit: React.ForwardRefRenderFunction<RoleRef, RoleProps> = (props, ref) => {
  const form = useRef<FormInstance>(null);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);

        // 回填数据
        setTimeout(async () => {
          // 回填数据
          const res = await roleGetByUser({
            id: arg.rowData.id as unknown as string,
          });

          if (Array.isArray(res.data)) {
            form.current?.setFieldsValue({
              roleIds: res.data.map((item) => item.id),
            });
          }
        });
      },
    };
  });

  return (
    <DrawerForm<{
      roleIds: any[];
    }>
      isKeyPressSubmit
      title="绑定用户角色"
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async ({ roleIds, ...query }) => {
        await roleAddByUser({
          userId: rowData?.id as number,
          roleIds: roleIds.map((v) => v.value),
        });

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormTreeSelect
        label="角色"
        name="roleIds"
        request={async () => {
          const res = await roleTree({
            all: NumBool.否,
          });

          return res.data || [];
        }}
        fieldProps={{
          multiple: true,
          treeDefaultExpandAll: true,
          treeCheckable: true,
          treeCheckStrictly: true,
          showCheckedStrategy: TreeSelect.SHOW_ALL,
          fieldNames: {
            label: 'roleName',
            value: 'id',
            children: 'child',
          },
        }}
        // rules={[
        //   {
        //     required: true,
        //     message: '请选择角色',
        //   },
        // ]}
      />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
