import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import { TimeSort } from '@/enums';
import { roleTree } from '@/services/base/authRole';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';
import {
  orgUserInfoDelete,
  orgUserInfoPage,
  orgUserInfoResetPassword,
} from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import { arr2ValueEnum, parseSrc } from '@/utils';
import { RedoOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { Image, message, Space, Tag, Tooltip } from 'antd';
import { useRef } from 'react';
import Edit, { EditRef } from './edit';
import * as Enum from './enum';
import Role, { RoleRef } from './role';

/** 该模块增删改查对象 */
export type BizObject = BASE.OrgUserInfo;

const Page: React.FC = () => {
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);
  const role = useRef<RoleRef>(null);
  const token = localStorage.getItem('token');
  const ellipsisStyle = {
    display: 'inline-block',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  };

  const myEllipsis = (text: string[]) => {
    return <Tooltip title={
      <div>
        {text.map((item: any) => {
          return <Tag key={item} style={{ marginBottom: 4 }}>{item}</Tag>;
        })}
      </div>
    }>
      <span style={ellipsisStyle}>{text.join(',')}</span>
    </Tooltip>;
  };

  return (
    <PageContainer>
      <ProTable<BizObject, {}, GlobalValueType>
        actionRef={table}
        rowKey="id"
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        toolBarRender={(_) => {
          const nodes: React.ReactElement[] = [];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                edit.current?.open({
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await orgUserInfoPage({
            page: current,
            size: pageSize,
            timeSort: TimeSort.倒序,
            ...otherQuery,
          });

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        scroll={{ x: 'fit-content', y: 500 }}
        columns={[
          {
            title: '用户名',
            dataIndex: 'userName',
            ellipsis: true,
            width: 120,
          },
          {
            title: '真实姓名',
            dataIndex: 'fullName',
            ellipsis: true,
            width: 120,
          },
          {
            title: '所属组织',
            dataIndex: 'deptNames',
            ellipsis: true,
            hideInSearch: true,
            width: 200,
            render: (_, record) => {
              return myEllipsis(record?.deptNames || []);
            },
          },
          {
            title: '所属组织',
            dataIndex: 'deptNewId',
            hideInForm: true,
            hideInTable: true,
            hideInDescriptions: true,
            request: async () => {
              return orgDeptInfoTree().then((res) => res?.data || []);
            },
            valueType: 'treeSelect',
            fieldProps: {
              fieldNames: {
                label: 'deptName',
                value: 'id',
                children: 'child',
              },
            },
          },
          {
            title: '所属专业',
            dataIndex: 'major',
            valueType: 'select',
            ellipsis: true,
            width: 120,
            // 变压器"、"开关"、"材料"、"线圈"
            valueEnum: arr2ValueEnum(['变压器', '开关', '材料', '线圈']),
          },
          {
            title: '角色',
            dataIndex: 'roleNames',
            ellipsis: true,
            width: 200,
            render: (_, record) => {
              return myEllipsis(record?.roleNames || []);
            },
            hideInSearch: true,
          },
          {
            title: '角色',
            dataIndex: 'roleNewId',
            hideInForm: true,
            hideInTable: true,
            hideInDescriptions: true,
            request: async () => {
              return roleTree({ all: 0 }).then((res) => res?.data?.[0]?.child || []);
            },
            valueType: 'select',
            fieldProps: {
              fieldNames: {
                label: 'roleName',
                value: 'id',
              },
            },
          },
          {
            title: '状态',
            dataIndex: 'enable',
            valueType: 'enum',
            width: 100,
            valueEnum: Enum.UserStatusObj,
            hideInSearch: true,
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            width: 200,
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="配置角色"
                    icon={<UsergroupAddOutlined />}
                    onClick={() => {
                      role.current?.open({
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        editable: true,
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />
                  <IconAction
                    tooltip="重置密码"
                    icon={<RedoOutlined />}
                    confirm="popconfirm"
                    confirmText={`确定重置 ${row?.userName} 的密码吗?`}
                    request={{
                      service: () => orgUserInfoResetPassword({ userId: row.id }),
                    }}
                    onSuccess={() => {
                      message.success('重置密码成功');
                      table.current?.reload();
                    }}
                  />
                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    confirmText={`确定删除 ${row?.userName} 吗?`}
                    request={{
                      service: () =>
                        orgUserInfoDelete({ id: row.id } as unknown as ArgumentsType<
                          typeof orgUserInfoDelete
                        >[0]),
                    }}
                    onSuccess={() => {
                      message.success('删除成功');
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit ref={edit} />
      <Role ref={role} />
    </PageContainer>
  );
};

export default Page;
