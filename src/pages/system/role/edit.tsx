import * as rules from '@/config/rule';
import * as Enums from '@/enums/auth';
import {
  permissionFrontGetByRoleId as apiPermissionFrontGetByRoleId,
  permissionFrontGetTreeByRoleId as apiPermissionFrontGetTreeByRoleId,
} from '@/services/base/authFe';
import { roleAdd, roleUpdate } from '@/services/base/authRole';
import { useMergedState } from '@/utils/hooks';
import { renderEnum } from '@/valueType/valueType/enum';
import {
  DrawerForm,
  FormInstance,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Alert, Button, Card, Empty, Form, message, Spin, Tree, TreeProps } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import keyBy from 'lodash/keyBy';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import * as Enum from './enum';
import type { BizObject } from './index';

type BizDataNode = DataNode & {
  /** 前端权限对象 */
  data: BASE.PagePermissionVO;
  /** 当前节点下的所有子节点的key */
  childAllKeys: string[];
};

// 获取当前节点下的所有子节点的key
function getChildAllKeys(data: BASE.PagePermissionVO): string[] {
  let res: string[] = [];

  res.push(data.permissionCode as string);

  if (data.child) {
    data.child.forEach((item) => {
      res = res.concat(getChildAllKeys(item));
    });
  }

  return res;
}

// 格式化树
const formatTree = (data: BASE.PagePermissionVO[]): BizDataNode[] => {
  return data.map((item) => {
    return {
      title: item.permissionName,
      key: item.permissionCode,
      children: item.child ? formatTree(item.child) : undefined,

      data: item,
      childAllKeys: getChildAllKeys(item),
    };
  });
};

// 树转数组
const flatTree = (data: BizDataNode[]): BizDataNode[] => {
  return data.reduce<BizDataNode[]>((prev, cur) => {
    if (cur.children) {
      return prev.concat(cur, flatTree(cur.children as BizDataNode[]));
    }

    return prev.concat(cur);
  }, []);
};

const AuthTree: React.FC<
  TreeProps & {
    /** 角色id */
    roleId: undefined | string;
    value?: string[];
    onChange?: (v: string[]) => void;
  }
> = ({ value, onChange, roleId, ...rest }) => {
  const [internalValue, setInternalValue] = useMergedState<string[]>(value || [], {
    value,
    onChange,
  });
  // 树的所有key
  const [allKeys, setAllKeys] = useState<string[]>([]);
  // 树的map,方便查找
  const keyByTree = useRef<Record<string, BizDataNode>>({});
  // 展开的key
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  // 获取角色下面的权限权限
  const permissionFrontGetTreeByRoleId = useRequest(
    async (...args: ArgumentsType<typeof apiPermissionFrontGetTreeByRoleId>) => {
      // 权限树
      const res = await apiPermissionFrontGetTreeByRoleId(...args);
      const data = res.data || [];
      const format = formatTree(data);
      // 树转列表
      const list = flatTree(format);
      // 树的所有key
      const keys = list.map((item) => item.data.permissionCode as unknown as string);

      keyByTree.current = keyBy(list, 'key');

      setAllKeys(keys);
      setExpandedKeys(keys);

      return {
        ...res,
        data: format,
      };
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (typeof roleId === 'string') {
      permissionFrontGetTreeByRoleId.run({
        id: roleId,
      });
    } else {
      permissionFrontGetTreeByRoleId.reset();
    }
  }, [roleId]);

  const checkedKeys = useMemo(() => {
    // 判断当前节点下的所有子节点是否都被勾选
    return internalValue.filter((v) => {
      const child = keyByTree.current[v];

      // 当前节点下没有子节点
      if (!child || child.childAllKeys.length <= 1) {
        return true;
      }

      return child.childAllKeys.every((key) => {
        return internalValue.includes(key);
      });
    });
  }, [internalValue, allKeys]);

  if (typeof roleId !== 'string') {
    return <Alert type="warning" message="请选择角色" />;
  }

  return (
    <Card size="small">
      {(() => {
        if (permissionFrontGetTreeByRoleId.loading) {
          return (
            <div style={{ textAlign: 'center' }}>
              <Spin />
            </div>
          );
        }

        if (permissionFrontGetTreeByRoleId.error) {
          return <Alert type="error" message="获取权限失败" />;
        }

        const list = permissionFrontGetTreeByRoleId.data || [];

        if (list.length <= 0) {
          return <Empty description="当前角色下无权限数据" />;
        }

        const titleRender: TreeProps['titleRender'] = (_data) => {
          const data = _data as DataNode & {
            data: BASE.PagePermissionVO;
          };

          return (
            <>
              {renderEnum(data.data.type, Enums.FeAuthTypeObj)}
              {data.data.permissionName}({data.data.permissionCode})
            </>
          );
        };

        return (
          <>
            <div style={{ marginBottom: 10 }}>
              <Button.Group size="small">
                <Button
                  onClick={() => {
                    setExpandedKeys(allKeys);
                  }}
                >
                  展开所有
                </Button>
                <Button
                  onClick={() => {
                    setExpandedKeys([]);
                  }}
                >
                  折叠所有
                </Button>
              </Button.Group>
            </div>

            <Tree
              checkedKeys={checkedKeys}
              onCheck={(keys, info) => {
                const checkedKeys = (keys as string[]).concat(info.halfCheckedKeys as string[]);
                setInternalValue(checkedKeys);
                onChange?.(checkedKeys);
              }}
              expandedKeys={expandedKeys}
              onExpand={(e) => {
                setExpandedKeys(e as string[]);
              }}
              disabled={permissionFrontGetTreeByRoleId.loading}
              treeData={permissionFrontGetTreeByRoleId.data || []}
              titleRender={titleRender}
              blockNode
              checkable
              height={Math.max(300, document.documentElement.clientHeight - 771)}
              {...rest}
            />
          </>
        );
      })()}
    </Card>
  );
};

export type EditRef = {
  open: (arg: {
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {
  /** 角色树 */
  roleTreeData: BizObject[];
};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = ({ roleTreeData }, ref) => {
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();
  // 获取角色权限
  const permissionFrontGetByRoleId = useRequest(apiPermissionFrontGetByRoleId, {
    manual: true,
  });

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        successCb.current = arg.onSuccess;
        setOpen(true);

        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue(arg.rowData);
          });

          // 回填角色权限树数据
          permissionFrontGetByRoleId
            .run({
              id: arg.rowData.id,
            } as unknown as ArgumentsType<typeof apiPermissionFrontGetByRoleId>[0])
            .then((data) => {
              if (Array.isArray(data)) {
                form.current?.setFieldsValue({
                  permissionCodes: data.map((v) => v.permissionCode),
                });
              }
            });
        } else {
          // 新增回填数据
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({
              roleType: Enum.RoleType.普通角色,
              state: Enum.RoleStatus.启用,
              permissionCodes: [],
            });
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BizObject>
      isKeyPressSubmit
      title={editable ? '编辑' : '新增'}
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
        };

        await (editable
          ? roleUpdate({
              ...baseQuery,
              id: rowData?.id as number,
            })
          : roleAdd(baseQuery));

        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormText
        label="名称"
        name="roleName"
        rules={[{ required: true, message: '请填写名称' }]}
      />

      <ProFormText
        label="标识"
        name="roleCode"
        rules={[
          { required: true, message: '请填写标识' },
          {
            validator: rules.craeteValidator(/^[a-zA-Z0-9]{1,16}$/, '只能输入1-16位字母'),
          },
        ]}
      />

      <ProFormTreeSelect
        label="上级角色"
        name="pid"
        disabled={editable}
        hidden={editable}
        rules={[{ required: !editable, message: '请选择上级角色' }]}
        fieldProps={{
          treeData: roleTreeData,
          treeDefaultExpandAll: true,
          fieldNames: {
            label: 'roleName',
            value: 'id',
            children: 'child',
          },
        }}
      />

      <ProFormDependency name={['pid']}>
        {({ pid }) => {
          return (
            <Form.Item label="权限" name="permissionCodes">
              <AuthTree roleId={pid} />
            </Form.Item>
          );
        }}
      </ProFormDependency>

      <ProFormRadio.Group
        label="类型"
        name="roleType"
        tooltip="只能新增/修改普通角色"
        valueEnum={
          editable && rowData
            ? new Map([[rowData.roleType, Enum.RoleTypeObj.get(rowData.roleType)]])
            : new Map([[Enum.RoleType.普通角色, Enum.RoleTypeObj.get(Enum.RoleType.普通角色)]])
        }
        rules={[{ required: true, message: '请选择类型' }]}
      />

      <ProFormRadio.Group
        label="状态"
        name="state"
        valueEnum={Enum.RoleStatusObj}
        rules={[{ required: true, message: '请选择状态' }]}
      />

      <ProFormTextArea label="说明" name="roleExplain" />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
