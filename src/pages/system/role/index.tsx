import Button from '@/components/button';
import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import { NumBool } from '@/enums';
import { roleDelete, roleTree } from '@/services/base/authRole';
import { DatabaseOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { message, Space } from 'antd';
import { useRef, useState } from 'react';
import DataAuth, { DataAuthRef } from './dataAuth';
import Edit, { EditRef } from './edit';
import * as Enum from './enum';

// 树转数组
const flatten = (arr: BASE.RoleVO[]): BASE.RoleVO[] => {
  return arr.reduce((prev, item) => {
    if (item.child) {
      return prev.concat(item, flatten(item.child));
    }

    return prev.concat(item);
  }, [] as BASE.RoleVO[]);
};

/** 该模块增删改查对象 */
export type BizObject = BASE.RoleVO;

const Page: React.FC = () => {
  const table = useRef<ActionType>();
  const [ids, setIds] = useState<number[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [roleTreeData, setRoleTreeData] = useState<BizObject[]>([]);
  const edit = useRef<EditRef>(null);
  const dataAuth = useRef<DataAuthRef>(null);

  return (
    <PageContainer>
      <ProTable<BizObject, {}, GlobalValueType>
        search={false}
        pagination={false}
        childrenColumnName="child"
        actionRef={table}
        rowKey="id"
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        expandable={{
          expandedRowKeys,
          onExpandedRowsChange: (keys) => {
            setExpandedRowKeys(keys as number[]);
          },
        }}
        toolBarRender={(_, rows) => {
          const nodes: React.ReactElement[] = [
            <IconAction
              color="#40485b"
              tooltip="刷新"
              icon="refresh"
              key="refresh"
              onClick={() => {
                table.current?.reload();
              }}
              size="middle"
            />,
            <Button onClick={setExpandedRowKeys.bind({}, [])} key="fold">
              收缩所有
            </Button>,
            <Button onClick={setExpandedRowKeys.bind({}, ids)} key="unfold">
              展开所有
            </Button>,
          ];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                edit.current?.open({
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await roleTree({
            all: NumBool.否,
          });

          const list = res.data || [];
          // @ts-ignore
          const addIds = flatten(list).map((item) => item.id);
          // @ts-ignore
          setRoleTreeData(list);
          setIds(addIds);
          setExpandedRowKeys(addIds);

          return {
            data: list,
            success: res.success,
            total: list.length,
          };
        }}
        columns={[
          {
            title: '名称',
            dataIndex: 'roleName',
          },
          {
            title: '标识',
            dataIndex: 'roleCode',
          },
          {
            title: '类型',
            dataIndex: 'roleType',
            valueType: 'enum',
            valueEnum: Enum.RoleTypeObj,
          },
          {
            title: '状态',
            dataIndex: 'state',
            valueType: 'enum',
            valueEnum: Enum.RoleStatusObj,
          },
          {
            title: '拥有权限',
            dataIndex: 'permissions',
            ellipsis: true,
            hideInSearch: true,
          },
          {
            title: '说明',
            dataIndex: 'roleExplain',
            ellipsis: true,
            hideInSearch: true,
          },
          {
            title: '修改时间',
            dataIndex: 'updateTime',
            valueType: 'dateTime',
            hideInSearch: true,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            valueType: 'date',
            hideInSearch: true,
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        editable: true,
                        rowData: row,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="数据权限"
                    icon={<DatabaseOutlined />}
                    onClick={() => {
                      dataAuth.current?.open({
                        rowData: row,
                        onChange: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  {row.roleType === Enum.RoleType.普通角色 && (
                    <IconAction
                      tooltip="删除"
                      color="red"
                      icon="delete"
                      confirm="popconfirm"
                      confirmText={`确定删除 ${row.roleName} 吗?`}
                      request={{
                        service: () =>
                          roleDelete({ id: row.id } as unknown as ArgumentsType<
                            typeof roleDelete
                          >[0]),
                      }}
                      onSuccess={() => {
                        message.success('删除成功');
                        table.current?.reload();
                      }}
                    />
                  )}
                </Space>
              );
            },
          },
        ]}
      />

      <Edit roleTreeData={roleTreeData} ref={edit} />
      <DataAuth ref={dataAuth} />
    </PageContainer>
  );
};

export default Page;
