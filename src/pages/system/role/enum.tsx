/** 角色类型 */
export enum RoleType {
  /** 系统角色 */
  系统角色 = 1,
  /** 普通角色 */
  普通角色 = 2,
}

/** 角色类型 */
export const RoleTypeObj: ValueEnumMap<number> = new Map([
  [
    RoleType.系统角色,
    {
      text: '系统角色',
      color: 'red',
    },
  ],
  [
    RoleType.普通角色,
    {
      text: '普通角色',
      color: 'blue',
    },
  ],
]);

/** 角色状态 */
export enum RoleStatus {
  /** 启用 */
  启用 = 1,
  /** 禁用 */
  禁用 = 0,
}

/** 角色类型 */
export const RoleStatusObj: ValueEnumMap<number> = new Map([
  [
    RoleStatus.启用,
    {
      text: '启用',
      color: 'blue',
    },
  ],
  [
    RoleStatus.禁用,
    {
      text: '禁用',
      color: 'red',
    },
  ],
]);
