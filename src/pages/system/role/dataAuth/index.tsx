import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import Text from '@/components/text';
import * as Enum from '@/enums/auth';
import * as BaseEnum from '@/enums/base';
import { ActionType } from '@ant-design/pro-components';
import { Drawer, Space } from 'antd';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { BizObject as RoleBizObject } from '../index';
import Edit, { EditRef } from './edit';

export type BizObject = any;

export type DataAuthRef = {
  open: (arg: {
    /** 主字典对象 */
    rowData: RoleBizObject;
    /** 当关闭弹窗时如果有修改 */
    onChange?: () => void;
  }) => void;
};

export type DataAuthProps = {};

const DataAuth: React.ForwardRefRenderFunction<DataAuthRef, DataAuthProps> = (props, ref) => {
  const table = useRef<ActionType>();
  const edit = useRef<EditRef>(null);
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<RoleBizObject>();
  const onChangeCb = useRef<() => void>();
  // 是否修改过
  const [isChange, setIsChange] = useState(false);

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        setIsChange(false);
        setOpen(true);
        setData(arg.rowData);
        onChangeCb.current = arg.onChange;
      },
    };
  });

  return (
    <Drawer
      open={open}
      onClose={() => {
        setOpen(false);

        if (isChange) {
          onChangeCb.current?.();
        }
      }}
      title={
        <span>
          编辑 <Text>{data?.roleName}</Text> 数据权限
        </span>
      }
      width="50%"
    >
      <ProTable<any, Record<string, any>, GlobalValueType>
        actionRef={table}
        size="small"
        params={{
          roleId: data?.id,
        }}
        toolBarRender={(_, rows) => {
          const nodes: React.ReactElement[] = [
            <IconAction
              color="#40485b"
              tooltip="刷新"
              icon="refresh"
              key="refresh"
              onClick={() => {
                table.current?.reload();
              }}
              size="middle"
            />,
          ];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              onClick={() => {
                edit.current?.open({
                  roleId: data?.id as number,
                  editable: false,
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = {
            data: {
              records: [],
              total: 0,
            },
            success: true,
          };

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          {
            title: '表名',
            dataIndex: 'tableName',
          },
          {
            title: '字段',
            dataIndex: 'whereField',
          },
          {
            title: '类型',
            dataIndex: 'whereType',
          },
          {
            title: '值',
            dataIndex: 'whereValue',
            width: 300,
          },
          {
            title: '值类型',
            dataIndex: 'whereValueType',
            valueEnum: Enum.DataAuthWhereTypeObj,
          },
          {
            title: 'join达式',
            dataIndex: 'expression',
            hideInSearch: true,
          },
          {
            title: 'bean名称',
            dataIndex: 'implBeanName',
          },
          {
            title: '关联关系',
            dataIndex: 'incidenceRelation',
          },
          {
            title: '操作',
            valueType: 'option',
            fixed: 'right',
            align: 'center',
            width: 100,
            render: (text, r, _, action) => {
              return (
                <Space>
                  <IconAction
                    tooltip="编辑"
                    icon="edit"
                    onClick={() => {
                      edit.current?.open({
                        roleId: data?.id as number,
                        editable: true,
                        rowData: r,
                        onSuccess: () => {
                          table.current?.reload();
                        },
                      });
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    request={{
                      service: () =>
                        Promise.resolve({
                          success: true,
                        }),
                    }}
                    onSuccess={() => {
                      setIsChange(true);
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
        ]}
      />

      <Edit ref={edit} />
    </Drawer>
  );
};

export default forwardRef(DataAuth);
