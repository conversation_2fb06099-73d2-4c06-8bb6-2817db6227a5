import * as Enum from '@/enums/auth';
import { TimeSort } from '@/enums/base';
import { permissionApiGetTableName } from '@/services/base/authBe';

import {
  DrawerForm,
  ProForm,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import {
  AutoComplete,
  AutoCompleteProps,
  Button,
  FormInstance,
  message,
  Modal,
  Select,
} from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import type { BizObject } from './index';

export type EditRef = {
  open: (arg: {
    /** 角色id */
    roleId: number;
    /** 是否是编辑 */
    editable: boolean;
    /** 当前编辑的数据 */
    rowData?: BizObject;
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type EditProps = {};

const Edit: React.ForwardRefRenderFunction<EditRef, EditProps> = (props, ref) => {
  // 模板列表
  const tplList = useRequest(async () => {
    const res = {
      data: {
        records: [],
        total: 0,
      },
      success: true,
    };

    return {
      data: (res.data?.records || []).map((item: any) => {
        return {
          label: item.name,
          value: item.id,
          data: item,
        };
      }),
    };
  });
  const selectedTpl = useRef<number>();
  const form = useRef<FormInstance>(null);
  // 是否是编辑
  const [editable, setEditable] = useState(false);
  // 当前选中行数据
  const [rowData, setRowData] = useState<BizObject>();
  const [roleId, setRoleId] = useState<number>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  // 成功回调函数
  const successCb = useRef<() => void>();
  const [whereFieldList, setWhereFieldList] = useState<AutoCompleteProps['options']>([]);

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        // 清空表单
        form.current?.resetFields();
        setEditable(arg.editable);
        setRowData(arg.rowData);
        setRoleId(arg.roleId);
        successCb.current = arg.onSuccess;
        setOpen(true);

        // 编辑回填数据
        if (arg.editable && arg.rowData) {
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue(arg.rowData);
          });
        } else {
          // 新增回填数据
          setTimeout(() => {
            // 回填数据
            form.current?.setFieldsValue({});
          });
        }
      },
    };
  });

  return (
    <DrawerForm<BizObject>
      isKeyPressSubmit
      preserve={false}
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span>设置角色数据权限</span>

          <span>
            <Button
              onClick={() => {
                Modal.confirm({
                  title: '选择模板',
                  content: (
                    <Select
                      onChange={(e) => {
                        selectedTpl.current = e;
                      }}
                      style={{
                        width: '100%',
                      }}
                      placeholder="选择数据模板"
                      options={tplList.data}
                    />
                  ),
                  onOk: () => {
                    if (typeof selectedTpl !== 'undefined') {
                      const find = (tplList.data || []).find(
                        (item) => item.value === selectedTpl.current,
                      );

                      if (find) {
                        form.current?.setFieldsValue({
                          ...find.data,
                        });
                      }
                    }

                    // 清空选择
                    selectedTpl.current = undefined;
                  },
                });
              }}
            >
              选择模板
            </Button>
          </span>
        </div>
      }
      width="50%"
      open={open}
      onOpenChange={setOpen}
      formRef={form}
      onFinish={async (query) => {
        const baseQuery = {
          ...query,
          roleId,
        };
        successCb.current?.();
        message.success('保存成功');
        setOpen(false);
      }}
    >
      <ProFormSelect
        label="表名"
        name="tableName"
        rules={[{ required: true, message: '此项必填' }]}
        request={async () => {
          const res = await permissionApiGetTableName();

          return (res.data || [])?.map((name) => {
            return {
              label: name,
              value: name,
            };
          });
        }}
        fieldProps={{
          fetchDataOnSearch: false,
          showSearch: true,
          filterOption: (input, option) => {
            return ((option?.label as string) ?? '').toLowerCase().includes(input.toLowerCase());
          },
          onChange: async (value) => {
            form.current?.resetFields(['whereField']);

            const res = {
              data: [],
              success: true,
            };

            const list = (res.data || [])?.map((name: any) => {
              return {
                label: name,
                value: name,
              };
            });

            setWhereFieldList(list);
          },
        }}
      />

      <ProFormDependency name={['tableName']}>
        {({ tableName }) => {
          if (tableName) {
            return (
              <ProForm.Item preserve={false} label="字段" name="whereField">
                <AutoComplete options={whereFieldList} />
              </ProForm.Item>
              // <ProFormAutoComplete
              //   preserve={false}
              //   label="字段"
              //   name="whereField"
              //   rules={[{ required: true, message: '此项必填' }]}
              //   params={{
              //     tableName,
              //   }}
              //   mode="edit"
              //   request={async () => {
              //     const res = await roleDataPermissionGetColumnName({
              //       tableName,
              //     });

              //     return (res.data || [])?.map((name) => {
              //       return {
              //         label: name,
              //         value: name,
              //       };
              //     });
              //   }}
              //   fieldProps={{
              //     fetchDataOnSearch: false,
              //   }}
              // />
            );
          }

          return null;
        }}
      </ProFormDependency>

      <ProFormRadio.Group
        label="类型"
        name="whereType"
        request={async () => {
          const res = {
            data: [],
            success: true,
          };
          const list = (res.data || []).map((item: any) => {
            return {
              label: item.operateName,
              value: item.operator,
            };
          });

          // 默认填充
          if (list.length && !editable) {
            form.current?.setFieldsValue({
              whereType: list[0].value,
            });
          }

          return list;
        }}
        rules={[{ required: true, message: '请选择' }]}
      />

      <ProFormTextArea
        label="值"
        name="whereValue"
        rules={[{ required: true, message: '此项必填' }]}
      />

      <ProFormRadio.Group
        label="值类型"
        name="whereValueType"
        valueEnum={Enum.DataAuthWhereTypeObj}
        rules={[{ required: true, message: '请选择' }]}
      />

      <ProFormTextArea label="join表达式" name="expression" />
      <ProFormText label="bean名称" name="implBeanName" />

      <ProFormRadio.Group
        label="关联关系"
        name="incidenceRelation"
        request={async () => {
          const res = {
            data: [],
            success: true,
          };

          const list = (res.data || []).map((item) => {
            return {
              label: item.operateName,
              value: item.operator,
            };
          });

          // 默认填充
          if (list.length) {
            form.current?.setFieldsValue({
              incidenceRelation: list[0].value,
            });
          }

          return list;
        }}
      />
    </DrawerForm>
  );
};

export default forwardRef(Edit);
