import { color } from '@/config/color';

// 显示权限
export enum ShowState {
  已删除 = 0,
  不限制 = 1,
  本人 = 2,
  登录 = 3,
  其他 = 4,
}
export const ShowStateObj: ValueEnumMap<number> = new Map([
  [
    ShowState.已删除,
    {
      text: '已删除',
      color: color.blue,
    },
  ],
  [
    ShowState.不限制,
    {
      text: '不限制',
      color: color.cyan,
    },
  ],
  [
    ShowState.本人,
    {
      text: '本人',
      color: color.red,
    },
  ],
  [
    ShowState.登录,
    {
      text: '登录',
      color: color.purple,
    },
  ],
  [
    ShowState.其他,
    {
      text: '其他',
      color: color.cyan,
    },
  ],
]);
/** end 显示权限 */
