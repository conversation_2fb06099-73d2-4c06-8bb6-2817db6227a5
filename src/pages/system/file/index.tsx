import IconAction from '@/components/iconAction';
import ProTable from '@/components/proTable';
import { TimeSort } from '@/enums';
import { fileDelete, filePage } from '@/services/base/extFile';
import { parseSrc } from '@/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Image, message, Space } from 'antd';
import Saver from 'file-saver';
import { useRef } from 'react';
import * as Enums from './enum';
import Upload, { UploadModalRef } from './upload';

/** 该模块增删改查对象 */
export type BizObject = BASE.FileInfo;

const Page: React.FC = () => {
  const table = useRef<ActionType>();
  const upload = useRef<UploadModalRef>(null);

  return (
    <PageContainer>
      <ProTable<BizObject>
        rowSelection={{}}
        actionRef={table}
        hiddenBtns={['add', 'edit', 'delete', 'export', 'detail']}
        toolBarRender={(action, rows) => {
          const disabled = (rows.selectedRowKeys || []).length === 0;
          const nodes: React.ReactElement[] = [];

          nodes.push(
            <IconAction
              tooltip="新增"
              icon="add"
              key="add"
              onClick={() => {
                upload.current?.open({
                  onSuccess: () => {
                    table.current?.reload();
                  },
                });
              }}
              size="middle"
            />,
            <IconAction
              tooltip="批量删除"
              icon="delete"
              key="del"
              confirm="popconfirm"
              confirmText="确定要删除吗？"
              request={{
                service: () => {
                  return fileDelete({
                    id: (rows.selectedRowKeys || []).join(','),
                  });
                },
                format: (_:any, props:any) => {
                  return {
                    ...props,
                    loading: _.loading,
                    disabled: _.loading || disabled,
                  };
                },
              }}
              onSuccess={() => {
                action?.clearSelected?.();
                table.current?.reload();
              }}
              size="middle"
            />,
          );

          return nodes;
        }}
        request={async ({ current = 0, pageSize = 0, ...otherQuery }) => {
          const res = await filePage({
            page: current,
            size: pageSize,
            timeSort: TimeSort.倒序,
            ...otherQuery,
          });

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          // 列表项
          {
            title: '文件名称',
            dataIndex: 'fileName',
            hideInSearch: true,
          },
          {
            title: '大小',
            dataIndex: 'fileSize',
            hideInSearch: true,
          },
          {
            title: '预览',
            dataIndex: 'fileUrl',
            hideInSearch: true,
            render: (v, row) => {
              const suffix = row.fileName?.split('.').pop();
              const isImg = ['jpg', 'jpeg', 'png', 'gif'].includes(suffix?.toLowerCase() || '');

              if (isImg) {
                return <Image src={parseSrc(v as string)} width={80} />;
              }

              return null;
            },
          },
          {
            title: '文件类型',
            dataIndex: 'fileType',
            hideInSearch: true,
          },
          {
            title: '文件用途',
            dataIndex: 'filePurpose',
            valueType: 'dict',
            hideInSearch: true,
            fieldProps: {
              dicCode: 'filePurpose',
            },
          },
          {
            title: '权限',
            dataIndex: 'showState',
            hideInSearch: true,
            valueType: 'enum',
            valueEnum: Enums.ShowStateObj,
          },
          {
            title: '文件地址',
            dataIndex: 'fileUrl',
            hideInSearch: true,
          },
          {
            title: '上传用户姓名',
            dataIndex: 'fullName',
            hideInSearch: true,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            valueType: 'date',
            hideInSearch: true,
          },
          {
            title: '操作',
            fixed: 'right',
            align: 'center',
            valueType: 'option',
            render: (_, row) => {
              return (
                <Space>
                  <IconAction
                    tooltip="下载"
                    icon={<DownloadOutlined />}
                    onClick={() => {
                      // 下载文件
                      const downloadUrl = parseSrc(row.fileUrl as string);

                      Saver.saveAs(downloadUrl as string, row.fileName as string);
                    }}
                  />

                  <IconAction
                    tooltip="删除"
                    color="red"
                    icon="delete"
                    confirm="popconfirm"
                    confirmText={`确定删除 ${row.fileName} 吗?`}
                    request={{
                      service: () =>
                        fileDelete({ id: row.id } as unknown as ArgumentsType<
                          typeof fileDelete
                        >[0]),
                    }}
                    onSuccess={() => {
                      message.success('删除成功');
                      table.current?.reload();
                    }}
                  />
                </Space>
              );
            },
          },
          // end 列表项

          // 筛选项
          {
            title: '文件名称',
            dataIndex: 'fileName',
            hideInTable: true,
            hideInSetting: true,
          },
          {
            title: '文件用途',
            dataIndex: 'filePurpose',
            valueType: 'dict',
            hideInTable: true,
            hideInSetting: true,
            fieldProps: {
              dicCode: 'filePurpose',
            },
          },
          {
            title: '权限',
            dataIndex: 'showState',
            hideInTable: true,
            hideInSetting: true,
            valueType: 'enum',
            valueEnum: Enums.ShowStateObj,
          },
          {
            title: '创建时间',
            dataIndex: '创建时间',
            valueType: 'dateTimeRange',
            hideInTable: true,
            hideInSetting: true,
            fieldProps: {
              showTime: true,
            },
            search: {
              transform: (res: string[]) => {
                return {
                  beginTime: `${res[0].slice(0, 10)} 00:00:00`,
                  endTime: `${res[1].slice(0, 10)} 23:59:59`,
                };
              },
            },
          },
          // end 筛选项
        ]}
      />

      <Upload ref={upload} />
    </PageContainer>
  );
};

export default Page;
