import Upload, { UploadRef } from '@/components/upload';
import { InboxOutlined } from '@ant-design/icons';
import { Drawer } from 'antd';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

export type UploadModalRef = {
  open: (arg: {
    /** 成功回调 */
    onSuccess?: () => void;
  }) => void;
};

export type UploadModalProps = {};

const UploadModal: React.ForwardRefRenderFunction<UploadModalRef, UploadModalProps> = (
  props,
  ref,
) => {
  // 成功回调函数
  const successCb = useRef<() => void>();
  // 弹窗开启状态
  const [open, setOpen] = useState(false);
  const upload = useRef<UploadRef>(null);

  useImperativeHandle(ref, () => {
    return {
      open: (arg) => {
        upload.current?.clear();
        successCb.current = arg.onSuccess;
        setOpen(true);
      },
    };
  });

  return (
    <Drawer title={'上传文件'} width={300} open={open} onClose={setOpen.bind({}, false)}>
      <div>
        <Upload
          ref={upload}
          draggable
          onSuccess={() => {
            successCb.current?.();
          }}
          multiple
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单个或批量上传。</p>
        </Upload>
      </div>
    </Drawer>
  );
};

export default forwardRef(UploadModal);
