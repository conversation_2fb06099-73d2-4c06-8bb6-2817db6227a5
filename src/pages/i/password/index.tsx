import Button from '@/components/button';
import { passwordValidator } from '@/config/rule';
import { loginPublicKey } from '@/services/base/authLogin';
import { orgUserInfoSelfPassword } from '@/services/base/userPassword';
import { encrypt } from '@/utils';
import {
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormProps,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel, useRequest } from '@umijs/max';
import { Card, message } from 'antd';
import { useRef } from 'react';

const Page: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { initialState, setInitialState, refresh } = useModel('@@initialState');
  const updatePassword = useRequest(
    async ({ rePassword, password, checkPassword, ...values }) => {
      const publicKey = await loginPublicKey({
        errorMessage: '获取token数据失败',
      });

      // 获取公钥后RSA算法加密密码
      const encryptPassword = await encrypt(password, async () => publicKey.data!);
      const encryptCheckPassword = await encrypt(checkPassword, async () => publicKey.data!);

      const res = await orgUserInfoSelfPassword({
        ...values,
        checkPassword: encryptCheckPassword,
        password: encryptPassword,
      });

      await initialState?.logout();
      message.success('修改密码成功,即将重新登录');

      return res;
    },
    {
      manual: true,
    },
  );

  const onFinish: ProFormProps['onFinish'] = async (values) => {
    updatePassword.run(values);
  };

  return (
    <Card>
      <ProForm
        layout="horizontal"
        style={{
          maxWidth: 600,
        }}
        disabled={updatePassword.loading}
        isKeyPressSubmit
        formRef={formRef}
        onFinish={onFinish}
        submitter={{
          render: (_, dom) => {
            return (
              <>
                <div>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={updatePassword.loading}
                    disabled={updatePassword.loading}
                  >
                    提交
                  </Button>
                </div>
              </>
            );
          },
        }}
      >
        <ProFormText.Password
          label="旧密码"
          name="checkPassword"
          rules={[
            {
              required: true,
              message: '请输入旧密码',
            },
          ]}
        />

        <ProFormDependency name={['password', 'rePassword']}>
          {({ password, rePassword }) => {
            return (
              <>
                <ProFormText.Password
                  label="新密码"
                  name="password"
                  rules={[
                    {
                      required: true,
                      message: '请输入新密码',
                    },
                    {
                      validator: passwordValidator,
                    },
                    {
                      validator: async (_, value) => {
                        // 不对空值校验
                        if (
                          password === '' ||
                          password === null ||
                          typeof password === 'undefined'
                        ) {
                          return;
                        }

                        if (
                          rePassword === '' ||
                          rePassword === null ||
                          typeof rePassword === 'undefined'
                        ) {
                          return;
                        }

                        if (rePassword === password) {
                          return;
                        }

                        throw new Error('两次输入的密码不一致');
                      },
                    },
                  ]}
                />

                <ProFormText.Password
                  label="确认新密码"
                  name="rePassword"
                  rules={[
                    {
                      required: true,
                      message: '请输入新密码',
                    },
                    {
                      validator: async (_, value) => {
                        // 不对空值校验
                        if (
                          password === '' ||
                          password === null ||
                          typeof password === 'undefined'
                        ) {
                          return;
                        }

                        if (
                          rePassword === '' ||
                          rePassword === null ||
                          typeof rePassword === 'undefined'
                        ) {
                          return;
                        }

                        if (rePassword === password) {
                          return;
                        }

                        throw new Error('两次输入的密码不一致');
                      },
                    },
                  ]}
                />
              </>
            );
          }}
        </ProFormDependency>
      </ProForm>
    </Card>
  );
};

export default Page;
