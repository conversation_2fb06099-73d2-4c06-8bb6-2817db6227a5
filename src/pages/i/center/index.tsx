// import { orgUserInfoUpdateBySelf } from '@/services/base/userSys';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import { useEffect, useRef } from 'react';

const Page: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const { initialState, setInitialState, refresh } = useModel('@@initialState');

  useEffect(() => {
    if (formRef.current && initialState && initialState.currentUser) {
      formRef.current.setFieldsValue({
        ...initialState?.currentUser?.extendData?.user,
        // photoUrl: initialState?.currentUser?.extendData?.user?.userHead
        //   ? formatUploadFile([initialState?.currentUser?.extendData?.user?.userHead])
        //   : [],
      });
    }
  }, []);

  return (
    <Row gutter={[24, 24]}>
      <Col md={12} xs={24}>
        <Card title="个人信息">
          <ProForm
            layout="horizontal"
            style={{ overflow: 'hidden' }}
            isKeyPressSubmit
            disabled
            // submitter={{
            //   render(props, dom) {
            //     return dom.pop();
            //   },
            // }}

            submitter={false}
            initialValues={{
              username: initialState?.currentUser?.username,
              fullName: initialState?.currentUser?.fullName,
              photoUrl: (initialState?.currentUser as any)?.photoUrl,
            }}
            formRef={formRef}
            // onFinish={async ({ photoUrl, ...values }) => {
            //   const baseQuery = {
            //     ...values,
            //     id: initialState?.currentUser?.id,
            //   };

            //   if (photoUrl?.[0]) {
            //     if ((photoUrl[0] as unknown as BizUploadFile).status === 'uploading') {
            //       message.info('请等待图片上传完成');

            //       return false;
            //     }

            //     baseQuery.photoUrl = (
            //       photoUrl[0] as unknown as BizUploadFile
            //     ).response?.data?.fileUrl;
            //   }

            //   await orgUserInfoUpdateBySelf(baseQuery);

            //   await refresh();

            //   message.success('修改成功');
            // }}
          >
            <ProFormText label="用户名" name="username" readonly />
            <ProFormText label="用户昵称" name="fullName" />

            {/* <Form.Item label="头像" name="photoUrl">
              <Upload onlyImg />
            </Form.Item> */}

            {/* <ProFormRadio.Group label="性别" name="sex" valueEnum={SexObj} />

            <ProFormDatePicker label="生日" name="birthday" />

            <ProFormText
              label="电话号码"
              name="phone"
              rules={[
                {
                  validator: rules.mobileValidator,
                },
              ]}
            />

            <ProFormText
              label="电子邮箱"
              name="email"
              rules={[
                {
                  validator: rules.emailValidator,
                },
              ]}
            /> */}
          </ProForm>
        </Card>
      </Col>
    </Row>
  );
};

export default Page;
