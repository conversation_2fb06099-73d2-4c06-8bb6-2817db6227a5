import { arr2ValueEnum } from '@/utils';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import { useModel, } from '@umijs/max';
import { Tag } from 'antd';
import { useRef } from 'react';

/** 该模块增删改查对象 */
export type BizObject = BASE.NoticeInfoVO;

const Start: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const table = useRef<ActionType>();

  return (
    <PageContainer>
      <ProTable<
        any,
        {}
      >
        actionRef={table}
        rowKey="procInsId"
        size="small"
        pagination={{
          showSizeChanger: true,
        }}
        bordered
        request={async ({ current, pageSize, ...otherQuery }) => {

          const res = {
            data: {
              records: [],
              total: 0,
            },
            success: true,
          };

          return {
            data: res.data?.records || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          {
            title: '序号',
            valueType: 'index',
            align: 'center'
          },
          {
            title: '标题',
            dataIndex: 'taskName',
            align: 'center'
          },
          {
            title: '状态',
            dataIndex: 'status',
            hideInSearch: true,
            renderText(text, record, index, action) {
                return record?.processStatus === 'running' ? '进行中' : '已完成'
            },
            align: 'center'
          },
          {
            title: '申请时间',
            valueType: 'date',
            dataIndex: 'startDate',
            align: 'center',
            hideInTable: true,
          },
          {
            title: '申请时间',
            valueType: 'date',
            dataIndex: 'createTime',
            align: 'center',
            hideInSearch: true,
            fieldProps: {
              format: 'YYYY-MM-DD HH:mm',
            },
          },
          {
            title: '当前待办人',
            dataIndex: '',
            align: 'center',
            hideInSearch: true,
            render(text, record, index, action) {
              return record?.nowRunFullName ? record?.nowRunFullName?.map((item) => {
                return  <div
                key={index}
                style={{
                  marginBottom: 5,
                }}
              >
                <Tag>{item}</Tag>
              </div>
              }) : '-'
          },
          },
          {
            title: '处理时长',
            dataIndex: 'duration',
            align: 'center',
            hideInSearch: true,
          },
          {
            title: '结束时间',
            valueType: 'date',
            dataIndex: 'finishTime',
            align: 'center',
            hideInSearch: true,
            fieldProps: {
              format: 'YYYY-MM-DD HH:mm',
            },
          },
          {
            title: '模块来源',
            dataIndex: 'moduleName',
            align: 'center',
            valueType: 'select',
            hideInTable: true,
            valueEnum: arr2ValueEnum(['审核原始记录', '下发委托书','审核报告', '测试任务反馈', '委托书修改', '报告修改'])
          },
          {
            title: '模块来源',
            dataIndex: 'procDefName',
            renderText(text, record, index, action) {
                return  <Tag>{record?.procDefName || '-'}</Tag>
            },
            align: 'center',
            hideInSearch: true,
          },
          
        ]}
      />
    </PageContainer>
  );
};

export { Start };
