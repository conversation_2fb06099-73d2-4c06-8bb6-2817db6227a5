
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import { useModel, Access,useAccess, history } from '@umijs/max';
import { TypeUrl } from '@/pages/home/<USER>/todo';
import { Tag } from 'antd';
import { useRef } from 'react';
import Action from '@/components/action';
import Text from '@/components/text';
import ProFormSelect from '@/components/proFormSelect';
import { arr2ValueEnum } from '@/utils';

/** 该模块增删改查对象 */
export type BizObject = BASE.NoticeInfoVO;

const Done: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const table = useRef<ActionType>();
  const { commonAccess } = useAccess();

  return (
    <PageContainer>
      <ProTable<
        any,
        {}
      >
        actionRef={table}
        rowKey="indexKey"
        size="small"
        pagination={{
          showSizeChanger: true,
        }}
        bordered
        request={async ({ current, pageSize, ...otherQuery }) => {

          const res = {
            data: {
              records: [],
              total: 0,
            },
            success: true,
          };

          return {
            data: res.data?.records?.map((item: any, index: number) => {
              return {
                ...item,
                indexKey: index
              }
          }) || [],
            success: res.success,
            total: res.data?.total || 0,
          };
        }}
        columns={[
          {
            title: '序号',
            valueType: 'index',
            align: 'center'
          },
          {
            title: '标题',
            dataIndex: 'taskName',
            align: 'center'
          },
          {
            title: '状态',
            dataIndex: 'noticeType',
            hideInSearch: true,
            renderText(text, record, index, action) {
                return record?.processStatus === 'running' ? '进行中' : '已完成'
            },
            align: 'center'
          },
          {
            title: '申请人',
            dataIndex: 'startUserId',
            align: 'center',
            hideInTable: true,
            renderFormItem: (_, props) => {
              return (
                <ProFormSelect
                  type="user"
                  colProps={{ span: 24 }}
                  ignoreFormItem={true}
                  query={{
                  }}
                />
              );
            },
          },
          {
            title: '申请人',
            dataIndex: 'startUserName',
            align: 'center',
            hideInSearch: true
          },
          {
            title: '申请时间',
            valueType: 'date',
            dataIndex: 'startDate',
            align: 'center',
            hideInTable: true,
          },
          {
            title: '申请时间',
            valueType: 'date',
            dataIndex: 'startTime',
            align: 'center',
            hideInSearch: true,
            fieldProps: {
              format: 'YYYY-MM-DD HH:mm',
            },
          },
          {
            title: '上一处理人',
            dataIndex: 'previousHandler',
            align: 'center',
            hideInSearch: true,
          },
          {
            title: '处理时间',
            valueType: 'date',
            dataIndex: 'previousHandlerDate',
            align: 'center',
            hideInSearch: true,
            fieldProps: {
              format: 'YYYY-MM-DD HH:mm',
            },
          },
          {
            title: '当前待办人',
            dataIndex: 'startUserName',
            align: 'center',
            hideInSearch: true,
            render(text, record, index, action) {
                return record?.nowRunFullName ? record?.nowRunFullName?.map((item) => {
                  return  <div
                  key={index}
                  style={{
                    marginBottom: 5,
                  }}
                >
                  <Tag>{item}</Tag>
                </div>
                }) : '-'
            },
          },
          {
            title: '模块来源',
            dataIndex: 'moduleName',
            align: 'center',
            valueType: 'select',
            hideInTable: true,
            valueEnum: arr2ValueEnum(['审核原始记录', '下发委托书','审核报告', '测试任务反馈', '委托书修改', '报告修改'])
          },
          {
            title: '模块来源',
            dataIndex: ['procVars', 'bus_type'],
            renderText(text, record, index, action) {
                return  <Tag>{record?.procVars?.bus_type || '-'}</Tag>
            },
            align: 'center',
            hideInSearch: true,
          },
          {
            valueType: 'option',
            title: '操作',
            fixed: 'right',
            align: 'center',
            render(dom, entity, index, action, schema) {
              return (
                <Action>
                  <Access accessible={commonAccess('admin:/task/plan@testManage')}>
                    <Text
                      onClick={() => {
                        if (entity?.procVars && TypeUrl?.[entity?.procVars?.bus_type]) {
                          history.push(TypeUrl[entity?.procVars?.bus_type].url, {
                            id: entity?.procVars?.record_task_id || entity?.procVars?.bus_id,
                            activeKey: entity?.procVars?.bus_type, // 处理跳转到页面定位到某个tab页签的情况
                          });
                        }
                      }}
                    >
                      查看
                    </Text>
                  </Access>
                </Action>
              );
            },
          },
        ]}
      />
    </PageContainer>
  );
};

export { Done };
