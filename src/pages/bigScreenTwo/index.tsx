import { screenNewScreenDataCount } from '@/services/base/dapingjiekou';
import { useEffect, useState } from 'react';
import Header from './components/header/index';
import TopTotal from './components/topTotal/index';
import './index.less';
import ReportInfo from './components/reportInfo';
import SampleDetect from './components/sampleDetect';
import AlarmInfo from './components/alarmInfo';
import TaskInfo from './components/taskInfo';

function BigScreen() {
  // 标题
  // 监控 翻译为英文   Monitor
  // 任务汇总 翻译为英文  Task Summary
  // 报告信息 翻译为英文  Report Information

  const [bigScreenData, setBigScreenData] = useState<any>({});

  const getBigScreenData = async () => {
    const res = await screenNewScreenDataCount();
    if (res?.success) {
      console.log('res.data==', res.data);
      setBigScreenData(res?.data);
    }
  };

  useEffect(() => {
    getBigScreenData();
  }, []);

  const openHK = () => {
    // Remove console.log since it's not needed
    if (window && 'openOrFocusTab' in window) {
      (window as any).openOrFocusTab(HIK_VIDEO_URL, '海康');
    }
  };

  return (
    <div className="big-screen-two">
      <Header />
      <div className="big-screen-content">
        <TopTotal data={bigScreenData.top} />
        {/* 样品检测信息，告警信息，报告信息 翻译英文
          样品检测信息：Sample Detection Information
          告警信息：Alarm Information
          报告信息：Report Information
        */}
        <div className="big-screen-content-middle">
          <SampleDetect data={bigScreenData.left} />
          <AlarmInfo data={bigScreenData.center} />
          <ReportInfo data={bigScreenData.right} />
        </div>
        <TaskInfo data={bigScreenData.bottom} />
        <a className='big-screen-content-bottom-button' onClick={openHK}>
          打开监控
        </a>
      </div>
    </div>
  );
}

export default BigScreen;
