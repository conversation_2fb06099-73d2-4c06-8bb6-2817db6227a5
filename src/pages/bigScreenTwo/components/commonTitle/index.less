.big-screen-common-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  background-image: url('@/assets/bigscreen/total_bg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: left;

  .big-screen-common-title-text {
    font-size: 20px;
    font-weight: 600;
    color: rgba(255,255,255,0.8);
    padding-left: 80px;
    position: relative;
    letter-spacing: 2px;
    -webkit-text-stroke: 0.2px #f1f1f1; /* 外描边 */
    text-shadow: 0 0 2px #f1f1f1;
    transform: skew(-2deg); /* 左斜10度 */
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 16px;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

