import * as echarts from 'echarts';
import ReactECharts from 'echarts-for-react';
import { useEffect, useState } from 'react';
import CommonTitle from '../commonTitle';
import './index.less';

function AlarmInfo(Props: any) {
  const { data: alarmInfoData } = Props;
  // 样品超期,任务超期,报告超期
  const xAxis = ['样品超期', '任务超期', '报告超期'];

  // 超期总数 翻译英文
  const [overTotal, setOverTotal] = useState(0);
  const [data, setData] = useState<any[]>([
    {
      name: '样品超期',
      overFiveDay: 0,
      threeBeforeFiveDay: 0,
    },
    {
      name: '任务超期',
      overFiveDay: 0,
      threeBeforeFiveDay: 0,
    },
    {
      name: '报告超期',
      overFiveDay: 0,
      threeBeforeFiveDay: 0,
    },
  ]);

  useEffect(() => {
    if (alarmInfoData) {
      let total = 0;
      let result = data.map((item) => {
        let target = alarmInfoData[item.name as keyof typeof alarmInfoData];
        if (target) {
          item.overFiveDay = target.overFiveDay;
          item.threeBeforeFiveDay = target.threeBeforeFiveDay;
          total += item.overFiveDay + item.threeBeforeFiveDay;
        }
        return item;
      });
      setData(result);
      setOverTotal(total);
    }
  }, [alarmInfoData]);
  return (
    <div className="big-screen-alarm-info">
      <CommonTitle title="告警信息" />
      <div className="big-screen-alarm-info-content">
        <div className="big-screen-alarm-info-content-totle">
          <div className="big-screen-alarm-info-content-totle-item">
            超期总数
            <span className="big-screen-alarm-info-content-totle-item-box-total">{overTotal}</span>
            <span>个</span>
          </div>
          <div className="big-screen-alarm-info-content-totle-item-box">
            <div className="big-screen-alarm-info-content-totle-three-day">超期3天不到5天</div>
            <div className="big-screen-alarm-info-content-totle-five-day">超期大于5天</div>
          </div>
        </div>
        <div id="alarm-info-chart">
          <ReactECharts
            option={{
              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '10%',
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                data: xAxis,
                axisLine: { show: false },
                axisTick: { show: false },
              },
              yAxis: {
                type: 'value',
                minInterval: 1,
                min: 0,
                // 强制显示0刻度
                axisLabel: {
                  formatter: (value: any) => {
                    return value === 0 ? '0' : value;
                  },
                },
                splitLine: {
                  lineStyle: {
                    type: 'dashed',
                    color: 'rgba(181, 197, 212, 0.15)',
                  },
                },
              },
              series: [
                {
                  type: 'bar',
                  data: data.map((item) => item.overFiveDay),
                  barMaxWidth: 38,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#EBF8FD' },
                      { offset: 1, color: '#EAB966' },
                    ]),
                  },
                },
                {
                  type: 'bar',
                  data: data.map((item) => item.threeBeforeFiveDay),
                  barMaxWidth: 38,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: '#EBF8FD' },
                      { offset: 0.6, color: 'rgba(255, 77, 79, 1)' },
                    ]),
                  },
                },
              ],
            }}
            style={{
              height: '100%',
              overflow: 'hidden',
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default AlarmInfo;
