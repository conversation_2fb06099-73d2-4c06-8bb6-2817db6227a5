.big-screen-task-info {
  height: 330px;
  color: #fff;
  .big-screen-task-content {
    height: calc(100% - 50px);
    margin-top: 16px;
    overflow: hidden;
    .ant-pro-table, .ant-table-wrapper,
    .ant-spin-nested-loading,
    .ant-spin-container,
    .ant-table,
    .ant-table-container {
      height: 100%;
    }
    .ant-table-thead {
      height: 48px;
      .ant-table-cell {
        height: 48px;
        color: rgba(255,255,255,0.4);
        background-color: transparent;
      }
    }
  }
  .big-screen-task-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    min-width: 260px;
    span {
      position: relative;
      color: rgba(178, 187, 215, 1);
      padding-left: 16px;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-radius: 50%;
      }
    }
    .big-screen-task-status-wating {
      &::before {
        background: #FADB14;;
      }
    }
    .big-screen-task-status-checking {
      &::before {
        background: rgba(43, 164, 113, 1);
      }
    }
    .big-screen-task-status-finished {
      &::before {
        background: rgba(153, 153, 153, 1);
      }
    }   
  }
}
