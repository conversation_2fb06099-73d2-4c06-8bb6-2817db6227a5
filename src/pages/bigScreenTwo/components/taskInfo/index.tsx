import ModalViewLive from '@/pages/config/monitor/modalLive';
import ProTable, { ProColumns } from '@ant-design/pro-table';
import { useEffect, useState } from 'react';
import CommonTitle from '../commonTitle';
import './index.less';

function TaskInfo(Props: any) {
  const { data: taskInfoData } = Props;
  const [open, setOpen] = useState(false);
  const [itemTar, setItemTar] = useState({});
  const [reportDataSource, setReportDataSource] = useState<any[]>([]);

  useEffect(() => {
    if (taskInfoData) {
      let newTaskInfoData = taskInfoData.map((item: any, index: number) => {
        return {
          ...item,
          id: item.camera,
          index
        };
      });
      // 任务时间降序
      newTaskInfoData.sort((a: any, b: any) => {
        return new Date(b.taskStartTime).getTime() - new Date(a.taskStartTime).getTime();
      });
      setReportDataSource(newTaskInfoData);
    }
  }, [taskInfoData]);
  const columns: any[] = [
    {
      title: '任务编号',
      dataIndex: 'taskNumber',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '任务开始时间',
      dataIndex: 'taskStartTime',
      valueType: 'dateTime',
      fieldProps: {
        format: 'YYYY-MM-DD HH:mm',
      },
      width: 138,
      align: 'center',
    },
    {
      title: '工位',
      dataIndex: 'workStationName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '实验项目',
      dataIndex: 'projectName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '检测人员',
      dataIndex: 'testPersonName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '工单状态',
      dataIndex: 'workdOrderStatus',
      align: 'center',
      render: (text: string, record: any) => {
        return (
          <span className="big-screen-task-status">
            <span className="big-screen-task-status-wating">
              待检 {record.workdOrderStatus['待检'] ?? 0}
            </span>
            <span className="big-screen-task-status-checking">
              在检 {record.workdOrderStatus['在检'] ?? 0}
            </span>
            <span className="big-screen-task-status-finished">
              检毕 {record.workdOrderStatus['检毕'] ?? 0}
            </span>
          </span>
        );
      },
    },
    {
      title: '监控操作',
      dataIndex: 'option',
      valueType: 'option',
      align: 'center',
      render: (text: string, record: any) => {
        return (
          <a onClick={() => {
            setOpen(true);
            setItemTar({
              ...record,
            });
          }}>播放</a>
        );
      },
    },
  ];
  return (
    <div className="big-screen-task-info">
      <CommonTitle title="检测任务实时信息" />

      <div className="big-screen-task-content">
        <ProTable
          search={false}
          toolBarRender={false}
          bordered={false}
          columns={columns}
          dataSource={reportDataSource}
          pagination={false}
          rowKey="index"
          scroll={{ y: 'calc(100% - 68px)' }}
          rowClassName={() => 'custom-row'}
          className="big-screen-task-list-table"
        />
      </div>

      {/* </Carousel> */}
      <ModalViewLive
        open={open}
        row={{ ...itemTar }}
        footer={false}
        width={600}
        maskClosable={false}
        destroyOnClose={true}
        onCancel={() => {
          setOpen(false);
        }}
        className="big-screen-monitor-modal"
      />
    </div>
  );
}

export default TaskInfo;
