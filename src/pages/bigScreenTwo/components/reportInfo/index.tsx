import ProTable from '@ant-design/pro-table';
import { Select } from 'antd';
import CommonTitle from '../commonTitle';
import './index.less';
import { useEffect, useState } from 'react';
function ReportInfo(Props: any) {
  const { data: reportInfoData } = Props;
  const [reportDataSource, setReportDataSource] = useState<any[]>([]);
  let colorObj = {
    '合格': 'rgba(43, 164, 113, 1)',
    '不合格': 'rgba(255, 77, 79, 1)',
  }
  const columns: any[] = [
    {
      title: '编号',
      dataIndex: 'reportNumber',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '报告名称',
      dataIndex: ['reportFileInfo', 'fileName'],
      ellipsis: true,
      align: 'center',
    },
    // {
    //   title: '生成时间',
    //   dataIndex: 'createTime',
    //   valueType: 'dateTime',
    //   fieldProps: {
    //     format: 'YYYY-MM-DD HH:mm',
    //   },
    //   width: 138,
    //   align: 'center',
    // },
    {
      title: '合格状态',
      dataIndex: 'tfQualified',
      ellipsis: true,
      align: 'center',
      width: 100,
      render: (text: string, record: any) => {
        let color = colorObj[record.tfQualified as keyof typeof colorObj] ?? 'rgba(255, 255, 255, 0.85)';
        return (
          <span
            style={{
              color,
            }}
          >
            {record.tfQualified ?? '-'}
          </span>
        );
      },
    },
  ];
  useEffect(() => {
    if (reportInfoData) {
      setReportDataSource(reportInfoData);
    }
  }, [reportInfoData]);
  return (
    <div className="big-screen-report-info">
      <CommonTitle
        title="报告信息"
        // extra={
        //   <Select
        //     size="small"
        //     style={{ width: 60 }}
        //     defaultValue="周"
        //     className="big-screen-select"
        //   >
        //     <Select.Option value="周">周</Select.Option>
        //     <Select.Option value="月">月</Select.Option>
        //     <Select.Option value="年">年</Select.Option>
        //   </Select>
        // }
      />

      <div className="big-screen-report-content">
        <ProTable
          search={false}
          toolBarRender={false}
          bordered={false}
          columns={columns}
          dataSource={reportDataSource}
          pagination={false}
          rowKey="id"
          scroll={{ y: 'calc(100% - 48px)' }}
          rowClassName={() => 'custom-row'}
          className="big-screen-task-list-table"
        />
      </div>
    </div>
  );
}

export default ReportInfo;
