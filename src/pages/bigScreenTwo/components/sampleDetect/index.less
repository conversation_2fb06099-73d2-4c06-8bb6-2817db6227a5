.big-screen-sample-detect {
  width: 598px;
  height: 330px;
  padding: 10px 20px;
  color: #fff;
  background: url('@/assets/bigscreenTwo/middle-bg.png') no-repeat center center;
  background-size: 100% 100%;
  .big-screen-sample-detect-content {
    width: 100%;
    margin-top: 20px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 105px;
      width: 1px;
      height: calc(100% - 20px);
      margin-top: 20px;
      background: url('@/assets/bigscreenTwo/split_line.png') no-repeat center center;
      background-size: 100% 100%;
    }
    .big-screen-sample-detect-header-row {
      display: flex;
      align-items: center;
      text-align: center;
      .big-screen-sample-detect-header-row-item-title {
        position: relative;
        font-size: 18px;
        letter-spacing: 2px;
        -webkit-text-stroke: 0.2px #f1f1f1; /* 外描边 */
        text-shadow: 0 0 2px #f1f1f1;
        &::before {
          position: absolute;
          top: 50%;
          left: 0;
          width: 21px;
          height: 2px;
          background: linear-gradient(
            -90deg,
            rgba(95, 147, 195, 0.83) 0%,
            rgba(91, 142, 193, 0) 100%
          );
          transform: translateY(-50%);
          content: '';
        }
        &:after {
          position: absolute;
          top: 50%;
          right: 0;
          width: 21px;
          height: 2px;
          background: linear-gradient(
            90deg,
            rgba(95, 147, 195, 0.83) 0%,
            rgba(91, 142, 193, 0) 100%
          );
          transform: translateY(-50%);
          content: '';
        }
      }
      .big-screen-sample-detect-header-row-item-title {
        width: 110px;
      }
      .big-screen-sample-detect-header-row-item-title-other {
        width: 35px;
      }
    }
    .big-screen-sample-detect-content-row {
      height: 83px;
      margin: 20px 0;
      display: flex;
      align-items: center;
      text-align: center;
      .big-screen-sample-detect-content-row-item {
        height: 100%;
        background: url('@/assets/bigscreenTwo/list-item_bg.png') no-repeat center center;
        background-size: 100% 100%;
        margin: 0 10px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        .big-screen-sample-detect-content-row-item-title {
          line-height: 1;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.85);
        }
        .big-screen-sample-detect-content-row-item-value {
          line-height: 1;
          font-size: 18px;
          color: rgba(255, 255, 255, 1);
        }
        #big-screen-sample-detect-content-echarts {
          width: 61px;
          height: 36px;
        }
      }
      .big-screen-sample-detect-content-row-item-title {
        width: 35px;
      }
      .big-screen-sample-detect-content-row-item-title-other {
        width: 110px;
      }
    }
    .big-screen-sample-detect-header-row-content {
      display: flex;
      margin-top: 12px;
      .big-screen-sample-detect-header-row-content-item-title {
        color: rgba(255, 255, 255, 0.85);
        text-align: center;
        width: 110px;
      }
      .big-screen-sample-detect-header-row-content-item-title-other {
        white-space: nowrap;
        width: 35px;
      }
    }
  }
}
