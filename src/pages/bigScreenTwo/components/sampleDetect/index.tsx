import ReactECharts from 'echarts-for-react';
import { useEffect, useState } from 'react';
import CommonTitle from '../commonTitle';
import './index.less';

function SampleDetect(Props: any) {
  const { data: sampleDetectData } = Props;
  //变压器, 开关，线缆/线圈，材料，台台检
  const [data, setData] = useState<any[]>([
    {
      idx: 0,
      title: '',
      value: 100,
    },
    {
      idx: 1,
      title: '变压器',
      key: '变压器',
      value: 0,
      waiting: 0,
      checking: 0,
      checked: 0,
    },
    {
      idx: 2,
      title: '开关',
      key: '开关',
      value: 0,
      waiting: 0,
      checking: 0,
      checked: 0,
    },
    {
      idx: 3,
      title: '线缆',
      key: '线圈',
      value: 0,
      waiting: 0,
      checking: 0,
      checked: 0,
    },
    {
      idx: 4,
      title: '材料',
      key: '材料',
      value: 0,
      waiting: 0,
      checking: 0,
      checked: 0,
    },
    {
      idx: 5,
      title: '台台检',
      key: '台台检',
      value: 0,
      waiting: 0,
      checking: 0,
      checked: 0,
    },
  ]);

  useEffect(() => {
    if (sampleDetectData) {
      let result = data.map((item) => {
        let target = sampleDetectData[item.key as keyof typeof sampleDetectData];
        if (target) {
          item.value = target.allOfSample;
          item.waiting = target.waitingToCheck;
          item.checking = target.checking;
          item.checked = target.checkOver;
        }

        return item;
      });
      setData(result);
    }
  }, [sampleDetectData]);

  const [type, setType] = useState(['待检', '在检', '检毕']);
  let obj = {
    在检: 'checking',
    检毕: 'checked',
    待检: 'waiting',
  };
  let objColor = {
    在检: 'rgba(61, 230, 255, 1)',
    检毕: 'rgba(153, 153, 153, 1)',
    待检: 'rgba(250, 219, 20, 1)',
  };
  return (
    <div className="big-screen-sample-detect">
      <CommonTitle title="样品检测信息" />

      <div className="big-screen-sample-detect-content">
        <div className="big-screen-sample-detect-header-row">
          {data?.map((item) => (
            <div
              className={
                item.idx !== 0
                  ? 'big-screen-sample-detect-header-row-item-title'
                  : 'big-screen-sample-detect-header-row-item-title-other'
              }
              key={item.idx}
              style={{
              }}
            >
              {item.title}
            </div>
          ))}
        </div>
        <div className="big-screen-sample-detect-content-row">
          {data?.map((item) => (
            <div
              key={item.idx}
              style={{
                height: '100%',
              }}
              className={item.idx === 0 ? 'big-screen-sample-detect-content-row-item-title' : 'big-screen-sample-detect-content-row-item-title-other'}
            >
              {item.idx !== 0 && (
                <div className="big-screen-sample-detect-content-row-item">
                  <span className="big-screen-sample-detect-content-row-item-title">收样</span>
                  <span className="big-screen-sample-detect-content-row-item-value">
                    {item?.value ?? 0}
                  </span>
                  <div id="big-screen-sample-detect-content-echarts">
                    <ReactECharts
                      option={{
                        grid: {
                          left: '3%',
                          right: '4%',
                          bottom: '0%',
                          top: '0%',
                          containLabel: true,
                        },
                        tooltip: {
                          trigger: 'item'
                        },
                        series: [
                          {
                            type: 'pie',
                            avoidLabelOverlap: false,
                            radius: ['40%', '70%'],
                            center: ['50%', '45%'],
                            emphasis: {
                              scale: false
                            },
                            data: [
                              {
                                value: item?.waiting ?? 0,
                                name: '待检',
                                itemStyle: { color: objColor['待检'] },
                              },
                              {
                                value: item?.checking ?? 0,
                                name: '在检',
                                itemStyle: { color: objColor['在检'] },
                              },
                              {
                                value: item?.checked ?? 0,
                                name: '检毕',
                                itemStyle: { color: objColor['检毕'] },
                              },
                            ],
                            label: {
                              show: false,
                            },
                            labelLine: {
                              show: false,
                            },
                          },
                        ],
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        overflow: 'hidden',
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
        {type.map((item, index) => {
          let key: any = obj[item as keyof typeof obj];

          return (
            <div className="big-screen-sample-detect-header-row-content" key={index}>
              {data?.map((itemData, idx) => {
                let value = itemData[key as keyof typeof itemData];
                return (
                  <div
                    className={
                      idx !== 0
                        ? 'big-screen-sample-detect-header-row-content-item-title'
                        : 'big-screen-sample-detect-header-row-content-item-title-other'
                    }
                    key={idx}
                    style={{
                      color:
                        idx === 0
                          ? 'rgba(255, 255, 255, 0.85)'
                          : objColor[item as keyof typeof objColor],
                    }}
                  >
                    {idx === 0 ? item : value}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default SampleDetect;
