.big-screen-top-total {
  display: flex;
  gap: 24px;
  color: #fff;
  .big-screen-top-total-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 596px;
    height: 210px;
    background: url('@/assets/bigscreenTwo/title-bg.png') no-repeat center center;
    background-size: 100% 100%;
  }
  .big-screen-top-total-item-noBg,
  .big-screen-top-total-item {
    .big-screen-top-total-item-title {
      position: relative;
      width: 370px;
      margin-top: 8px;
      font-weight: 600;
      font-size: 24px;
      text-align: center;
      transform: skew(-6deg); /* 左斜10度 */
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        display: block;
        width: 127px;
        height: 2px;
        background: linear-gradient(
          -90deg,
          rgba(95, 147, 195, 0.83) 0%,
          rgba(91, 142, 193, 0.1) 100%
        );
        transform: translateY(-50%);
        content: '';
      }
      &::after {
        position: absolute;
        top: 50%;
        right: 0;
        display: block;
        width: 127px;
        height: 2px;
        background: linear-gradient(
          90deg,
          rgba(95, 147, 195, 0.83) 0%,
          rgba(91, 142, 193, 0.1) 100%
        );
        transform: translateY(-50%);
        content: '';
      }
    }
    .big-screen-top-total-item-value {
      margin-top: 24px;

      .big-screen-top-total-item-value-num {
        display: inline-block;
        font-weight: 600;
        color: #fff;
        font-size: 64px;
        width: 84px;
        height: 90px;
        line-height: 90px;
        text-align: center;
        background: linear-gradient(180deg, rgba(86, 136, 226, 0) 27%, rgba(86, 136, 226, 0.35) 87%),
          rgba(18, 93, 180, 0.16);
        border: 1px solid;
        border-radius: 4px 4px 4px 4px;
        border-image: linear-gradient(84deg, rgba(160, 195, 230, 0.35), rgba(54, 208, 242, 1)) 1 1;
        &:not(:last-child) {
          margin-right: 16px;
        }
      }
    }
  }
  .big-screen-top-total-two-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    justify-content: center;
    width: 596px;
    padding: 0 72px;
    background: url('@/assets/bigscreenTwo/title-bg.png') no-repeat center center;
    background-size: 100% 100%;
    .big-screen-top-total-item-noBg {
      width: fit-content;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .big-screen-top-total-item-title {
        width: 330px;
        font-size: 20px;
        &::before {
          width: 90px;
        }
        &::after {
          width: 90px;
        }
      }
      .big-screen-top-total-item-value {
        margin-top: 8px;
        .big-screen-top-total-item-value-num {
          width: 32px;
          height: 36px;
          font-size: 22px;
          line-height: 36px;
        }
      }
    }
  }
}
