import { useEffect } from 'react';

import { useState } from 'react';

import { history } from '@umijs/max';

import './index.less';

interface TopTotalProps {
  name: string;
  value: string;
  url?: string;
}

interface BigScreenDataProps {
  [str: string]: number;
}

function TopTotal({ data: bigScreenData }: BigScreenDataProps) {
  const [data, setData] = useState<TopTotalProps[]>([
    {
      name: '样品',
      value: '000',
      url: '/taskManage/sample',
    },
    {
      name: '任务',
      value: '000',
      url: '/task/listData',
    },
    {
      name: '报告',
      value: '000',
      url: '/report',
    },
  ]);

  useEffect(() => {
    if (bigScreenData) {
      let keys: string[] = ['样品', '任务', '今日报告', '累计报告'];
      let url: string[] = ['/taskManage/sample', '/task/listData', '/report', '/report'];
      let data: TopTotalProps[] = [];
      for (let i = 0; i < keys.length; i++) {
        data.push({
          name: keys[i],
          value: (bigScreenData[keys[i] as keyof typeof bigScreenData] ?? 0)
            .toString()
            .padStart(3, '0'),
          url: url[i],
        });
      }
      setData(data);
    }
  }, [bigScreenData]);

  return (
    <div className="big-screen-top-total">
      {data.map((item, index) => {
        if (index > 1) return null;
        const nums = item.value.toString().split('');
        return (
          <div
            className="big-screen-top-total-item"
            key={item.name}
            onClick={() => {
              if (item.url) {
                history.push(item.url);
              }
            }}
          >
            <div className="big-screen-top-total-item-title">{item.name}</div>
            <div className="big-screen-top-total-item-value">
              {nums.map((num, index) => (
                <span className="big-screen-top-total-item-value-num" key={index}>
                  {num}
                </span>
              ))}
            </div>
          </div>
        );
      })}
      <div className="big-screen-top-total-two-item">
        {data.map((item, index) => {
          if (index < 2) return null;
          const nums = item.value.toString().split('');
          return (
            <div
              className="big-screen-top-total-item-noBg"
              key={item.name}
              onClick={() => {
                if (item.url) {
                  history.push(item.url);
                }
              }}
            >
              <div className="big-screen-top-total-item-title">{item.name}</div>
              <div className="big-screen-top-total-item-value">
                {nums.map((num, index) => (
                  <span className="big-screen-top-total-item-value-num" key={index}>
                    {num}
                  </span>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default TopTotal;
