.big-screen-two {
  width: 1920px;
  height: 1080px;
  font-size: 16px;
  background-color: #f0f2f5;
  background-image: url('@/assets/bigscreenTwo/bg_full.png');
  background-repeat: no-repeat;
  background-size: contain;
  position: relative;
  .line-ellipsis {
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  .big-screen-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
    padding: 0px 40px 32px 40px;
    background-color: transparent;
    .big-screen-content-middle {
      display: flex;
      gap: 24px;
      width: 100%;
      margin-top: 48px;
    }
  }
  .big-screen-task-list-table {
    background-color: transparent;
    .ant-table {
      color: rgba(255, 255, 255, 0.85);
      background-color: transparent;
    }
    .ant-table-thead > tr {
      background: rgba(30, 70, 134, 0.14);
      > th {
        background-color: transparent;
        border: none;
        border-radius: 0 !important;
        box-shadow: none;
        &::before {
          background-color: transparent !important;
          border-bottom: none;
        }
      }
    }
    .ant-table-cell-fix-left {
      color: rgba(255, 255, 255, 0.85);
      background-color: transparent;
    }
    .ant-typography {
      color: rgba(255, 255, 255, 0.85);
    }
    .ant-table-tbody {
      .ant-table-row > .ant-table-cell-row-hover {
        background: transparent;
      }
      > tr > td {
        border: none;
      }
      tr.ant-table-placeholder {
        background: transparent;
      }
      .ant-empty-description {
        color: rgba(255, 255, 255, 0.85);
      }
      >tr.ant-table-placeholder:hover>td {
        background: transparent;
      }
    }
  }
  .big-screen-select {
    color: rgba(255, 255, 255, 0.85);
    background-color: transparent;
    .ant-select-selector {
      color: rgba(255, 255, 255, 0.85);
      background-color: transparent;
      border-image: linear-gradient(84deg, rgba(160, 195, 230, 0.35), rgba(54, 208, 242, 1)) 1 1;
    }
    .ant-select-arrow {
      color: rgba(255, 255, 255, 0.85);
    }
    &.ant-select-single.ant-select-open .ant-select-selection-item {
      color: rgba(255, 255, 255, 0.85);
    }
  }
  .big-screen-content-bottom-button {
    position: absolute;
    top: 1050px;
    right: 10px;
    color: #fff;
    cursor: pointer;
  }
}
