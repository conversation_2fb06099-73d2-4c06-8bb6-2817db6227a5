import ProTable, { ProTableProps } from '@/components/proTable';
import { orgUserInfoVoPage } from '@/services/base/zuzhijiagourenyuanbiao<PERSON>ekou';
import FileDownload from '@/components/fileDownload';
import { PageContainer } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { OnlyInSearch } from 'antd-pro-crud';
import { useAccess } from '@umijs/max';

function CheckItem() {
  const { commonAccess } = useAccess();
  
  const columns: ProTableProps<BASE.JzTaskInspectionItemInfoVO>['columns'] = [
    //检项编号（任务编号-1-1、任务编号-1-2、任务编号-2-x形式）、检毕时间、检测人员、检测标准、检测结果、是否合格
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 80,
    },
    {
      title: '检项编号',
      dataIndex: 'inspectionItemNumber',
      ellipsis: true,
      width: 200,
    },
    {
      title: '检毕时间',
      dataIndex: 'inspectionCompletionTime',
      valueType: 'dateRange',
      ...OnlyInSearch,
      transform: (value) => {
        return {
          inspectionCompletionTimeStart: dayjs(value[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          inspectionCompletionTimeEnd: dayjs(value[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        };
      },
    },
    {
      title: '检毕时间',
      dataIndex: 'inspectionCompletionTime',
      valueType: 'dateTime',
      hideInSearch: true,
      ellipsis: true,
      fieldProps: {
        style: {
          width: '100%',
        },
      },
      width: 130,
      render: (_, record) => {
        let time = record?.jzTaskWorkOrderInfo?.endDate;
        return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '检测人员',
      dataIndex: 'userId',
      ellipsis: true,
      width: 130,
      valueType: 'select',
      fieldProps: {
        style: { width: '100%' },
        showSearch: true,
        fieldNames: {
          label: 'fullName',
          value: 'id',
        },
      },
      request: async () => {
        const res = await orgUserInfoVoPage({
          page: 1,
          size: 2000,
        });
        return res.data?.records || [];
      },
      render: (_, record) => {
        return record?.userName || '-';
      },
    },
    {
      title: '检测标准',
      dataIndex: 'testStandard',
      ellipsis: true,
      hideInSearch: true,
      width: 130,
    }, {
      title: '实验项目',
      dataIndex: 'experimentalProject',
      ellipsis: true,
      width: 130,
    },
    {
      title: '检测结果',
      dataIndex: 'testResults',
      hideInSearch: true,
      ellipsis: true,
      width: 130,
    },
    {
      title: '是否合格',
      dataIndex: 'tfQualified',
      ellipsis: true,
      width: 130,
      valueType: 'select',
      fieldProps: {
        options: [{label: '合格', value: '合格'}, {label: '不合格', value: '不合格'}],
      },
    },
    {
      title: '关联附件',
      dataIndex: 'fileInfo',
      ellipsis: true,
      hideInSearch: true,
      width: 130,
      render: (_, record) => {
        return <FileDownload item={record?.fileInfo} preview={false} isDowmload={true} key={record?.fileInfo?.id} />;
      },  
    },
    // 操作
    {
      title: '操作',
      valueType: 'option',
      width: 160,
      fixed: 'right',
      render: (_, record) => {
        return <></>;
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.JzTaskInspectionItemInfoVO>
        crudKey="jzTaskInspectionItemInfo"
        hiddenBtns={['add', 'edit', 'delete']}
        scroll={{
          scrollToFirstRowOnChange: true,
          x: 'max-content',
          y: 500,
        }}
        pagination={{
          pageSizeOptions: [10, 15, 20, 50, 100, 1000],
        }}
        toolBarRender={() => {
          return [];
        }}
        crud={{
          detail: {
            visible: (row) => commonAccess('admin:/task/checkItem@detail'),
          },
        }}
        columns={columns}
      />
    </PageContainer>
  );
}

export default CheckItem;
