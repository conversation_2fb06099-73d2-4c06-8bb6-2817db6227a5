import { buInstrumentWorkstationInfoVoPage } from '@/services/base/gongweixinxiguanlijiekou';
import { jzTaskInfoUpdate } from '@/services/base/jingzhourenwuliebiaobiaojiekou';
import { instrumentNewInfoVoPage } from '@/services/base/yiqiyibiaojiekou';
import { orgUserInfoVoPage } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import { ProDescriptions, EditableProTable } from '@ant-design/pro-components';
import { Button, Form, message, Modal, ModalProps } from 'antd';
import { Card, OnlyInSearch  } from 'antd-pro-crud';
import { useState } from 'react';

type SampleDetailProps = {
  row: BASE.JzTaskInfoVO;
  onFinish: () => void;
} & ModalProps;

const SampleDetail: React.FC<SampleDetailProps> = ({ row, onFinish, ...rest }) => {
  const [form] = Form.useForm(); 
  const [editableKeys, setEditableKeys] = useState<string[]>([]);
  const [data, setData] = useState<BASE.JzTaskArrangementInfoVO[]>(row?.jzTaskArrangementInfoLists || []);

  return (
    <Modal
      title="任务编辑"
      width={1000}
      footer={(originNode) => {
        return (
          <>
          <Button onClick={(e: any) => rest?.onCancel?.(e)}>取消</Button>
          <Button
            type="primary"
            onClick={async (e) => {
              if (editableKeys.length > 0) {
                message.error('请先保存编辑的数据');
                return;
              }
              const res = await jzTaskInfoUpdate({
                id: row.id as unknown as number,
                jzTaskArrangementInfoList: data,
              });
              if (res.success) {
                message.success('更新成功');
                onFinish();
              } else {
                message.error('更新失败');
              }
            }}
          >
            确定
          </Button>
          </>
        );
      }}
      {...rest}
    >
      <Card title="样品信息" extra={false}>
        <ProDescriptions<BASE.SampleInfoVO>
          bordered
          column={2}
          columns={[
            {
              title: '任务编号',
              dataIndex: 'taskNumber',
            },
            {
              title: '物资类别',
              dataIndex: 'sampleName',
            },
            {
              title: '检测级别',
              dataIndex: 'testLevel',
            },
            {
              title: '任务状态',
              dataIndex: 'taskStatus',
            },
            {
              title: '任务生成时间',
              dataIndex: 'taskCreationTime',
              valueType: 'dateTime',
            },
            {
              title: '开始时间',
              dataIndex: 'startDate',
              valueType: 'dateTime',
            },
            {
              title: '结束时间',
              dataIndex: 'endDate',
              valueType: 'dateTime',
            },
          ]}
          dataSource={row}
          labelStyle={{ width: 200, whiteSpace: 'nowrap' }}
          className="modal-pro-descriptions-item-wrap"
        />
      </Card>
      <Card title="任务编排" extra={false}>
        <EditableProTable<BASE.JzTaskArrangementInfoVO>
          columns={[
            {
              title: '实验项目',
              dataIndex: 'gwBzName',
              width: 200,
              ellipsis: true,
              editable: false,
            },
            {
              title: '工位',
              dataIndex: 'workstationId',
              width: 200,
              ellipsis: true,
              valueType: 'select',
              fieldProps: {
                fieldNames: {
                  label: 'workstationName',
                  value: 'id',
                },
                // 搜索不调用request
                showSearch: true,
                fetchDataOnSearch: false,
                filterOption: (input: string, option: any) => {
                  return (
                    option?.workstationName?.indexOf(input.toLowerCase()) >= 0
                  );
                },
                onChange: (value: string, record: BASE.JzTaskArrangementInfoVO) => {
                  console.log(value, record, 'value');
                },
              },
              request: async () => {
                const res = await buInstrumentWorkstationInfoVoPage({
                  page: 1,
                  size: 1999,
                });
                const list = res.data?.records || [];
                return list;
              },
            },
            {
              title: '设备名称',
              dataIndex: 'equipmentName',
              ellipsis: true,
              valueType: 'select',
              fieldProps: {
                fieldNames: {
                  label: 'sbmc',
                  value: 'id',
                },
                // 搜索不调用request
                showSearch: true,
                fetchDataOnSearch: false,
                filterOption: (input: string, option: any) => {
                  return (
                    option?.sbmc?.indexOf(input.toLowerCase()) >= 0 ||
                    option?.sbbh?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  );
                },
                onChange: (value: string, record: BASE.JzTaskArrangementInfoVO) => {
                  form.setFieldsValue({
                    [editableKeys[0]]: {
                      testUserId: record.testUserId,
                      testUserName: record.testUserFullName,
                      workstationId: record.workstationId,
                      workstationName: record.workstationName,
                    },
                  });
                },
              },
              request: async () => {
                const res = await instrumentNewInfoVoPage({
                  page: 1,
                  size: 1999,
                });
                const list = res.data?.records || [];
                return list;
              },
            },
            {
              title: '测试人员姓名',
              dataIndex: 'testUserId',
              width: 200,
              ellipsis: true,
              valueType: 'select',
              fieldProps: {
                fieldNames: {
                  label: 'fullName',
                  value: 'id',
                },
                // 搜索不调用request
                showSearch: true,
                fetchDataOnSearch: false,
                filterOption: (input: string, option: any) => {
                  return (
                    option?.fullName?.indexOf(input.toLowerCase()) >= 0
                  );
                },
                onChange: (value: string, record: BASE.JzTaskArrangementInfoVO) => {
                  console.log(value, record, 'value');
                },
              },
              request: async () => {
                const res = await orgUserInfoVoPage({
                  page: 1,
                  size: 1999,
                });
                const list = res.data?.records || [];
                return list;
              },
            },
            // 编辑
            {
              title: '操作',
              valueType: 'option',
              render: (_: any, record: any, index: any, action: any) => [
                <a key="editable" onClick={() => {
                  action?.startEditable?.(record.id);
                }}>编辑</a>,
              ],
            },
          ]}
          value={data}
          recordCreatorProps={false}
          editable={{
            type: 'single',
            form: form,
            editableKeys,
            onChange: (keys: any[]) => {
              setEditableKeys(keys);
            },
            onSave: async (record: any, row: BASE.JzTaskArrangementInfoVO) => {
              console.log(record, row, 'record');
              const newData = data.map((item: any) => {
                if (item.id === record) {
                  return row;
                }
                return item;
              });
              setData(newData);
            },
            actionRender: (row: any, config: any, dom: any) => [dom.save, dom.cancel],
          }}
          rowKey="id"
          pagination={false}
          search={false}
          toolBarRender={false}
        />
      </Card>
    </Modal>
  );
};
export default SampleDetail;
