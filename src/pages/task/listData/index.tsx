import ProTable, { ProTableProps } from '@/components/proTable';
import {
  jzTaskInfoEndTask,
  jzTaskInfoGetVo,
  jzTaskInfoStartTask,
} from '@/services/base/jingzhourenwuliebiaobiaojiekou';
import { buSampleBaseInfoVoPage } from '@/services/base/yangpinjibenxinxiguanlianbiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Access, useAccess, useSearchParams, history } from '@umijs/max';
import { Form, message, Modal } from 'antd';
import { launch } from 'antd-pro-crud';
import { useEffect, useRef } from 'react';
import Detail from './detail';
import Edit from './edit';
const alert_detail = launch(Detail);
const alert_edit = launch(Edit);
interface LocationState {
  id: string; // 或 number，取决于你的实际类型
}
function ListData() {
  const tableRef = useRef<ActionType>(null);
  const [form] = Form.useForm();
  const { commonAccess } = useAccess();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (searchParams.get('id')) {
      history.replace('/task/listData');
      form.setFieldsValue({
        taskNumber: searchParams.get('id'),
      });
      // 触发查询
      form.submit();
    }
  }, [searchParams]);

  const columns: ProTableProps<BASE.JzTaskInfoVO>['columns'] = [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 80,
    },
    {
      title: '任务编号',
      dataIndex: 'taskNumber',
      width: 200,
      ellipsis: true,
      fieldProps: {
        style: {
          width: '100%',
        },
      },
    },
    {
      title: '物资类别',
      dataIndex: 'sampleName',
      valueType: 'select',
      fieldProps: {
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
      request: async () => {
        const res = await buSampleBaseInfoVoPage({
          page: 1,
          size: 1000,
        });
        // 数据去重
        const uniqueData =
          res.data?.records?.filter(
            (item, index, self) => index === self.findIndex((t) => t.name === item.name),
          ) || [];
        return uniqueData;
      },
    },
    {
      title: '检测级别',
      dataIndex: 'testLevel',
      valueType: 'select',
      valueEnum: arr2ValueEnum(['A', 'B', 'C']),
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      // 任务待检、任务在检、任务检毕
      valueType: 'select',
      valueEnum: arr2ValueEnum(['任务待检', '任务在检', '任务检毕', '工单检毕']),
      fieldProps: {},
    },
    {
      title: '任务生成时间',
      dataIndex: 'taskCreationTime',
      valueType: 'dateTime',
      width: 150,
      hideInSearch: true,
      fieldProps: {
        style: {
          width: '100%',
        },
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 150,
      fieldProps: {
        style: {
          width: '100%',
        },
        showTime: { format: 'HH:mm' },
      },
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 150,
      fieldProps: {
        style: {
          width: '100%',
        },
        showTime: { format: 'HH:mm' },
      },
    },
    // 操作
    {
      title: '操作',
      valueType: 'option',
      width: 220,
      render: (_, record) => {
        return (
          <>
            {record.taskStatus === '任务待检' && (
              <Access accessible={commonAccess('admin:/task/listData@edit')}>
                <a
                  onClick={async () => {
                    let modal = alert_edit({
                      row: record,
                      maskClosable: false,
                      onFinish: () => {
                        tableRef.current?.reload();
                        modal.close();
                      },
                    });
                  }}
                >
                  编辑
                </a>
              </Access>
            )}
            <Access accessible={commonAccess('admin:/task/listData@detail')}>
              <a
                onClick={async () => {
                  const res = await jzTaskInfoGetVo({
                    id: record.id as unknown as string,
                  });
                  if (res.data) {
                    alert_detail({
                      row: res.data,
                    });
                  } else {
                    message.error('获取详情失败');
                  }
                }}
              >
                详情
              </a>
            </Access>
            {record.taskStatus === '任务待检' && (
              <Access accessible={commonAccess('admin:/task/listData@startTask')}>
                <a
                  onClick={() => {
                    jzTaskInfoStartTask({
                      id: record.id as unknown as string,
                    })
                      .then((res) => {
                        if (res.success) {
                          message.success('开始任务成功');
                          tableRef.current?.reload();
                        } else {
                          message.error('开始任务失败');
                        }
                      })
                      .catch((err) => {
                        message.error('开始任务失败');
                      });
                  }}
                >
                  开始任务
                </a>
              </Access>
            )}
            {record.taskStatus !== '任务检毕' && record.startDate && (
              <Access accessible={commonAccess('admin:/task/listData@endTask')}>
                <a
                  onClick={() => {
                    // 弹窗确定后，调用任务检毕接口
                    Modal.confirm({
                      title: '确定任务检毕吗？',
                      onOk: async () => {
                        const res = await jzTaskInfoEndTask({
                          id: record.id as unknown as string,
                        });
                        if (res.success) {
                          message.success('任务检毕成功');
                          tableRef.current?.reload();
                        } else {
                          message.error('任务检毕失败');
                        }
                      },
                    });
                  }}
                >
                  任务检毕
                </a>
              </Access>
            )}
          </>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.JzTaskInfoVO>
        crudKey="jzTaskInfo"
        hiddenBtns={['detail', 'delete', 'add', 'edit']}
        scroll={{
          scrollToFirstRowOnChange: true,
          x: 'fit-content',
          y: 500,
        }}
        actionRef={tableRef}
        search={{
          form,
        }}
        pagination={{
          pageSizeOptions: [10, 15, 20, 50, 100, 1000],
        }}
        toolBarRender={(action, { selectedRowKeys, selectedRows }) => {
          return [];
        }}
        columns={columns}
      />
    </PageContainer>
  );
}

export default ListData;
