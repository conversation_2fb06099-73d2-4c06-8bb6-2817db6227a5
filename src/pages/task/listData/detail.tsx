import { ProDescriptions } from '@ant-design/pro-components';
import { Button, Modal, ModalProps, Tag, Tooltip } from 'antd';
import { Card, OnlyInSearch  } from 'antd-pro-crud';
import ProTable from '@ant-design/pro-table';
import { orgUserInfoVoPage } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import { standardBasicInstrumentInfoVoPage } from '@/services/base/biaozhunjianceyiqibiaozhunxinxibiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { buInstrumentWorkstationInfoPage } from '@/services/base/gongweixinxiguanlijiekou';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';

type SampleDetailProps = {
  row: BASE.JzTaskInfoVO;
} & ModalProps;

const SampleDetail: React.FC<SampleDetailProps> = ({ row, ...rest }) => {

  const ellipsisStyle = {
    display: 'inline-block',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  };

  const myEllipsis = (text: string[]) => {
    return <Tooltip title={
      <div>
        {text.map((item: any) => {
          return <Tag key={item} style={{ marginBottom: 4 }}>{item}</Tag>;
        })}
      </div>
    }>
      <span style={ellipsisStyle}>{text.join(',')}</span>
    </Tooltip>;
  };

  return (
    <Modal
      title="任务详情"
      width={1000}
      footer={(originNode) => {
        return (
          <Button
            type="primary"
            onClick={(e) => rest?.onCancel?.(e as React.MouseEvent<HTMLButtonElement>)}
          >
            确定
          </Button>
        );
      }}
      {...rest}
    >
      <Card title="样品信息" extra={false}>
        <ProDescriptions<BASE.SampleInfoVO>
          bordered
          column={2}
          columns={[
            {
              title: '任务编号',
              dataIndex: 'taskNumber',
            },
            {
              title: '物资类别',
              dataIndex: 'sampleName',
            },
            // 详情start
            {
              title: '规格',
              dataIndex: ['sampleInfo', 'sampleModel'],
              ...OnlyInSearch,
              hideInSearch: true,
              hideInDescriptions: false,
            },
            {
              title: '盲样编号',
              dataIndex: ['sampleInfo', 'blindSampleNumber'],
              ...OnlyInSearch,
              hideInSearch: true, 
              hideInDescriptions: false,
            },
            {
              title: '二次盲样编号',
              dataIndex: ['sampleInfo', 'secondaryBlindSampleNumber'],
              ...OnlyInSearch,
              hideInSearch: true,
              hideInDescriptions: false,
            },
            {
              title: '检测类型',
              dataIndex: ['sampleInfo', 'testType'],
              ...OnlyInSearch,
              hideInSearch: true,
              hideInDescriptions: false,
            },
            {
              title: '收样时间',
              dataIndex: ['sampleInfo', 'sampleReceiptTime'],
              ...OnlyInSearch,
              hideInSearch: true,
              hideInDescriptions: false,
              valueType: 'dateTime',
              fieldProps: {
                format: 'YYYY-MM-DD HH:mm',
              },
            },
            // 详情end
          ]}
          dataSource={row}
          labelStyle={{ width: 200, whiteSpace: 'nowrap' }}
          className="modal-pro-descriptions-item-wrap"
        />
      </Card>
      <Card title="工位信息" extra={false}>
        <ProTable<BASE.BuInstrumentWorkstationInfo>
          columns={[
            {
              title: '工位名称',
              dataIndex: 'workstationName',
            },
            {
              title: '工位编号',
              dataIndex: 'workstationCode',
            },
            {
              title: '工位位置',
              dataIndex: 'location',
            },
            {
              title: '负责人',
              dataIndex: 'responsiblePerson',
            },
            {
              title: '工位描述',
              dataIndex: 'description',
            },
          ]}
          dataSource={row.buInstrumentWorkstationInfoLists}
          pagination={false}
          search={false}
          toolBarRender={false}
        />
      </Card>
      <Card title="设备信息" extra={false}>
        <ProTable<BASE.InstrumentNewInfo>
          columns={[
            {
              title: '设备名称',
              dataIndex: 'sbmc',
              width: 100,
              ellipsis: true,
            },
            {
              title: '专业',
              dataIndex: 'professionalType',
              width: 100,
              ellipsis: true,
            },
            {
              dataIndex: 'collectionMethod',
              title: '采集方式',
              valueType: 'select',
              ellipsis: true,
              width: 100,
              valueEnum: arr2ValueEnum(['直采', '填报']),
              formItemProps: { rules: [{ required: true }] },
            },
            {
              title: '关联实验',
              dataIndex: 'gwBzId',
              ellipsis: true,
              fieldProps: {
                style: { width: '100%' },
                showSearch: true,
                mode: 'multiple',
                fieldNames: {
                  label: 'projectName',
                  value: 'id',
                },
              },
              request: async () => {
                const res = await standardBasicInstrumentInfoVoPage({
                  page: 1,
                  size: 2000,
                });
                return res.data?.records || [];
              },
              render: (_, record: any) => {
                if (record?.proName) {
                  let names: string[] = record?.proName;
                  return myEllipsis(names);
                }
                return '-';
              },
            },
            {
              title: '所属检测工位',
              dataIndex: 'workstationId',
              ellipsis: true,
              valueType: 'select',
              fieldProps: {
                style: { width: '100%' },
                showSearch: true,
                fieldNames: {
                  label: 'workstationName',
                  value: 'id',
                },
              },
              request: async () => {
                const res = await buInstrumentWorkstationInfoPage({
                  page: 1,
                  size: 2000,
                });
                return res.data?.records || [];
              },
            },
            {
              title: '设备管理员',
              dataIndex: 'testUserId',
              ellipsis: true,
              valueType: 'select',
              width: 160,
              fieldProps: {
                style: { width: '100%' },
                showSearch: true,
                fieldNames: {
                  label: 'fullName',
                  value: 'id',
                },
              },
              request: async () => {
                const res = await orgUserInfoVoPage({
                  page: 1,
                  size: 2000,
                });
                return res.data?.records || [];
              },
            },
          ]}
          dataSource={row.instrumentNewInfoLists}
          pagination={false}
          search={false}
          toolBarRender={false}
        />
      </Card>
      <Card title="人员信息" extra={false}>
        <ProTable<BASE.OrgUserInfo>
          columns={[
            {
              title: '人员姓名',
              dataIndex: 'fullName',
              width: 100,
            },
            {
              title: '人员电话',
              dataIndex: 'phone',
              width: 100,
            },
            {
              title: '人员邮箱',
              dataIndex: 'email',
              width: 100,
            },
            {
              title: '所属组织',
              dataIndex: 'deptNames',
              render: (_, record) => {
                return <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                  {
                    record?.deptNames?.map((item: any) => {
                      return <Tag key={item} style={{ marginBottom: 4 }}>{item}</Tag>;
                    })
                  }
                </div>;
              },
            },
            {
              title: '所属专业',
              dataIndex: 'major',
              valueType: 'select',
              width: 100,
              // 变压器”、“开关”、“材料”、“线圈”
              valueEnum: arr2ValueEnum(['变压器', '开关', '材料', '线圈']),
            },
          ]}
          dataSource={row.orgUserInfoLists}
          pagination={false}
          search={false}
          toolBarRender={false}
        />
      </Card>
      <Card title="工单信息" extra={false}>
        <ProTable<BASE.JzTaskWorkOrderInfo>
          columns={[
            {
              title: '工单编号',
              dataIndex: 'workOrderNumber',
            },
            {
              title: '工单状态',
              dataIndex: 'statusInfo',
            },
            {
              title: '开始时间',
              dataIndex: 'startDate',
              valueType: 'dateTime',
            },
            {
              title: '结束时间',
              dataIndex: 'endDate',
              valueType: 'dateTime',
            },
          ]}
          dataSource={row.jzTaskWorkOrderInfoLists}
          pagination={false}
          search={false}
          toolBarRender={false}
        />
      </Card>
    </Modal>
  );
};
export default SampleDetail;
