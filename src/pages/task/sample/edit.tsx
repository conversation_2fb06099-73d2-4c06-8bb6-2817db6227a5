import Upload from '@/components/upload';
import { sampleInfoAdd, sampleInfoUpdate } from '@/services/base/yangpinjibenxinxibiaojiekou';
import { buSampleBaseInfoVoPage } from '@/services/base/yangpinjibenxinxiguanlianbiaojiekou';
import { arr2ValueEnum } from '@/utils';
import {
  ModalForm,
  ModalFormProps,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormItem,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Col, Form, message, Row } from 'antd';

type EditProps = {
  isEdit?: boolean;
  onSuccess?: () => void;
  formatParams?: (values: Record<string, any>) => Record<string, any>;
} & ModalFormProps;

const Edit: React.FC<EditProps> = (props: EditProps) => {
  const { isEdit, onSuccess, formatParams, ...rest } = props;

  const [form] = Form.useForm();
  const onFinish = async (values: any) => {
    const submitValues = {
      ...values,
      sampleNumber: props?.initialValues?.sampleNumber,
      sampleSerialNumber: values?.sampleSerialNumber,
      nameplateFileId: Array.isArray(values?.nameplateFileId)
        ? values?.nameplateFileId?.[0]?.response?.data?.id
        : values?.nameplateFileId,
      samplePicId: Array.isArray(values?.samplePicId)
        ? values?.samplePicId?.[0]?.response?.data?.id
        : values?.samplePicId,
    };
    const res = isEdit
      ? await sampleInfoUpdate({ ...submitValues, id: props?.initialValues?.id })
      : await sampleInfoAdd(formatParams ? formatParams?.(submitValues) : submitValues);
    if (res?.success) {
      message.success(res?.message);
      props?.modalProps?.onCancel?.(submitValues);
      onSuccess?.();
    }
  };

  const token = localStorage.getItem('token');
  return (
    <ModalForm
      title={isEdit ? '编辑' : '新增'}
      width="800px"
      grid
      colProps={{ span: 12 }}
      {...rest}
      onFinish={onFinish}
      form={form}
    >
      {/* 联动样品规格，样品型号 */}

      {
        <ProFormDependency name={['jzSampleModel', 'sampleModel']}>
          {({ jzSampleModel, sampleModel }) => {
            return (
              <ProFormSelect
                label={'物资类别'}
                name={'sampleName'}
                formItemProps={{ rules: [{ required: true }] }}
                fieldProps={{
                  allowClear: true,
                  fieldNames: {
                    label: 'name',
                    value: 'id',
                  },
                  fetchDataOnSearch: false,
                  showSearch: true,
                  onChange: (value, option: any) => {
                    form.setFieldsValue({
                      sampleName: option?.name,
                      jzSampleModel: undefined,
                      sampleModel: undefined,
                    });
                  },
                }}
                params={() => {
                  return {
                    type: jzSampleModel,
                    model: sampleModel,
                  };
                }}
                request={async () => {
                  const res = await buSampleBaseInfoVoPage({
                    size: 2000,
                    page: 1,
                  });
                  // 筛选出物资类别重复的
                  const unique = res?.data?.records?.filter(
                    (item: any, index: number, self: any) =>
                      index === self.findIndex((t: any) => t.name === item.name),
                  );
                  return unique || [];
                }}
              />
            );
          }}
        </ProFormDependency>
      }

      {/* 联动样品型号，物资规格 */}
      <ProFormDependency name={['sampleName', 'sampleModel']}>
        {({ sampleName, sampleModel }) => {
          return (
            <ProFormSelect
              label={'物资型号'}
              name={'jzSampleModel'}
              formItemProps={{ rules: [{ required: sampleName }] }}
              fieldProps={{
                allowClear: true,
                fieldNames: {
                  label: 'type',
                  value: 'id',
                },
                showSearch: true,
                fetchDataOnSearch: false,
                onChange: (value, option: any) => {
                  form.setFieldsValue({
                    jzSampleModel: option?.type,
                    sampleModel: undefined,
                  });
                },
              }}
              disabled={!sampleName}
              params={() => {
                return {
                  name: sampleName,
                  model: sampleModel,
                };
              }}
              request={async () => {
                const res = await buSampleBaseInfoVoPage({
                  size: 2000,
                  page: 1,
                  name: sampleName,
                });
                // 去重
                const unique = res?.data?.records?.filter(
                  (item: any, index: number, self: any) =>
                    index === self.findIndex((t: any) => t.type === item.type),
                );
                return unique || [];
              }}
            />
          );
        }}
      </ProFormDependency>

      {/* 联动样品规格 */}
      <ProFormDependency name={['sampleName', 'jzSampleModel']}>
        {({ sampleName, jzSampleModel }) => {
          // 变动触发request
          return (
            <ProFormSelect
              label={'物资规格'}
              name={'sampleModel'}
              formItemProps={{ rules: [{ required: sampleName }] }}
              fieldProps={{
                allowClear: true,
                fieldNames: {
                  label: 'model',
                  value: 'id',
                },
                showSearch: true,
                fetchDataOnSearch: false,
                onChange: (value, option: any) => {
                  form.setFieldsValue({
                    sampleModel: option?.model,
                  });
                },
              }}
              disabled={!sampleName}
              params={() => {
                return {
                  name: sampleName,
                  type: jzSampleModel,
                };
              }}
              request={async () => {
                const res = await buSampleBaseInfoVoPage({
                  size: 2000,
                  page: 1,
                  name: sampleName,
                });
                // 过滤调jzSampleModel
                let list = res?.data?.records || [];
                if (jzSampleModel) {
                  list = list?.filter((item: any) => {
                    return item.type === jzSampleModel;
                  });
                }
                return list || [];
              }}
            />
          );
        }}
      </ProFormDependency>
      <ProFormSelect
        label={'检测类型'}
        name={'testType'}
        valueEnum={arr2ValueEnum(['A', 'B', 'C'])}
        formItemProps={{ rules: [{ required: true }] }}
      />
      <ProFormText
        label={'盲样编号'}
        name={'blindSampleNumber'}
        formItemProps={{ rules: [{ required: true }] }}
      />

      <ProFormText
        label={'二次盲样编号'}
        name={'secondaryBlindSampleNumber'}
        placeholder="请输入样品序号"
        fieldProps={{
          disabled: true,
        }}
      />

       <ProFormSelect
        label={'样品所属专业'}
        name={'sampleMajor'}
        // 变压器、开关、材料、线圈（线缆）
        valueEnum={arr2ValueEnum(['变压器', '开关', '材料', '线圈（线缆）'])}
        formItemProps={{ rules: [{ required: true }] }}
      />
      <ProFormDateTimePicker
        label={'收样时间'}
        name={'sampleReceiptTime'}
        fieldProps={{
          showTime: true,
          style: { width: '100%' },
          format: 'YYYY-MM-DD HH:mm',
        }}
      />

      <Row style={{ width: '100%' }}>
        <Col span={12} style={{ padding: '0 4px' }}>
          <ProFormItem label={'样品铭牌'} name={'nameplateFileId'}>
            <Upload maxCount={1} accept={'image/*'} />
          </ProFormItem>
        </Col>
        <Col span={12} style={{ padding: '0 4px' }}>
          <ProFormItem label={'样品照片'} name={'samplePicId'}>
            <Upload maxCount={1} accept={'image/*'} />
          </ProFormItem>
        </Col>
      </Row>

      <ProFormTextArea colProps={{ span: 24 }} label={'样品备注'} name={'sampleRemark'} />
    </ModalForm>
  );
};
export default Edit;
