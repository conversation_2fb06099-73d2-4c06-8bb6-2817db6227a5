import Button from '@/components/button';
import ProTable, { ProTableProps } from '@/components/proTable';
import { color } from '@/config/color';
import { sampleInfoGenerateSampleNumber } from '@/services/base/yangpinjibenxinxibiaojiekou';

import { buSampleBaseInfoVoPage } from '@/services/base/yangpinjibenxinxiguanlianbiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { useAccess, useSearchParams, Access, history } from '@umijs/max';
import { Dropdown, Form, Tag } from 'antd';
import { launch, launchSchema, OnlyInSearch } from 'antd-pro-crud';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
import SampleDetail from './detail';
import Edit from './edit';
import UpdateState from './updateState';

const alert_edit = launchSchema(Edit);
const alert_detail = launch(SampleDetail);
const alert_updateState = launchSchema(UpdateState);

export const PlanStatusColor: { [k: string]: string } = {
  任务待检: color.orange,
  任务在检: color.blue,
  任务检毕: color.green,
};

// 状态生命周期检测、归还、完结
export const statusLifecycleMap: { [k: string]: string } = {
  检测: color.orange,
  归还: color.blue,
  完结: color.green,
};

const Page: React.FC = () => {
  const [form] = Form.useForm();
  const { commonAccess } = useAccess();
  const actionRef = useRef<ActionType>();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (searchParams.get('id')) {
      history.replace('/taskManage/sample');
      form.setFieldsValue({
        sampleNumber: searchParams.get('id'),
      });
      // 触发查询
      form.submit();
    }
  }, [searchParams]);
  

  const columns: ProTableProps<BASE.SampleInfoVO>['columns'] = [
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 80,
      fixed: 'left',
    },
    {
      dataIndex: 'sampleName',
      title: '物资类别',
      width: 200,
      ellipsis: true,
      fieldProps: {
        allowClear: true,
        fieldNames: {
          label: 'name',
          value: 'name',
        },
      },
      request: async () => {
        const res = await buSampleBaseInfoVoPage({
          size: 1000,
          page: 1,
        });
        // 数据去重
        const uniqueData =
          res.data?.records?.filter(
            (item: any, index: number, self: any) => index === self.findIndex((t: any) => t.name === item.name),
          ) || [];
        return uniqueData;
      },
    },
    {
      title: '物资型号',
      dataIndex: 'jzSampleModel',
      hideInSearch: true,
      ellipsis: true,
      width: 150,
    },
    {
      dataIndex: 'sampleModel',
      title: '物资规格',
      hideInSearch: true,
      width: 200,
      ellipsis: true,
    },
    // 检测类型
    {
      dataIndex: 'testType',
      title: '检测类型',
      width: 100,
      valueType: 'select',
      valueEnum: arr2ValueEnum(['A', 'B', 'C']),
    },
    // 样品编号
    {
      dataIndex: 'sampleNumber',
      title: '样品编号',
      width: 120,
      ellipsis: true,
    },
    // 盲样编号
    {
      dataIndex: 'blindSampleNumber',
      title: '盲样编号',
      width: 120,
      ellipsis: true,
    },
    // 二次盲样编号
    {
      dataIndex: 'secondaryBlindSampleNumber',
      title: '二次盲样编号',
      hideInSearch: true,
      ellipsis: true,
      width: 150,
    },
    // 样品所属专业 线圈、线缆、开关、材料
    {
      dataIndex: 'sampleMajor',
      title: '样品所属专业',
      hideInSearch: true,
      width: 150,
      valueEnum: arr2ValueEnum(['变压器', '开关', '材料', '线圈（线缆）']),
    },
    // 收样时间
    {
      dataIndex: 'sampleReceiptTime',
      title: '收样时间',
      ...OnlyInSearch,
      transform: (value) => {
        return {
          sampleReceiptTimeStart: dayjs(value[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          sampleReceiptTimeEnd: dayjs(value[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        };
      },
      valueType: 'dateRange',
    },
    {
      dataIndex: 'taskStatus',
      title: '任务状态',
      width: 90,
      hideInSearch: true,
      // ...OnlyInTable,
      render: (_, entity: any) => {
        return (
          (entity?.taskStatus && (
            <Tag color={PlanStatusColor[entity?.taskStatus || '进行中']}>{entity?.taskStatus}</Tag>
          )) ||
          '--'
        );
      },
    },
    {
      dataIndex: 'backStatus',
      title: '返样状态',
      width: 90,
      hideInSearch: true,
    },
    // 报告状态
    {
      dataIndex: 'jzReportStatus',
      title: '报告状态',
      width: 90,
      hideInSearch: true,
    },
    {
      dataIndex: 'sampleRemark',
      title: '样品备注',
      width: 200,
      hideInSearch: true,
    },
    {
      valueType: 'option',
      title: '操作',
      fixed: 'right',
      width: 200,
      maxWidth: 200,
      render(dom, entity: any, index, action, schema) {
        return (
          <>
            {/* 状态为检毕，增加返样操作，返样操作包含“有样返样”、“无样返样”、“部分返样” */}
            {entity?.jzTaskStatus === '任务检毕' && (
              <Access accessible={commonAccess('admin:/taskManage/sample@backSample')}>
                <a
                  onClick={() => {
                    alert_updateState({
                      title: '返样',
                      width: '600px',
                      row: entity,
                      initialValues: {
                        backStatus: entity?.backStatus,
                      },
                      onSuccess: () => action?.reload(),
                    });
                  }}
                >
                  返样
                </a>
              </Access>
            )}
            <Access accessible={commonAccess('admin:/taskManage/sample@detail')}>
              <a
                onClick={() => {
                  alert_detail({
                    row: entity,
                  });
                }}
              >
                详情
              </a>
            </Access>
            {!entity?.jzTaskStatus && (
              <Access accessible={commonAccess('admin:/taskManage/sample@edit')}>
                <a
                  onClick={() =>
                    alert_edit({
                      isEdit: true,
                      initialValues: {
                        ...entity,
                        sampleSerialNumber: entity?.sampleSerialNumber,
                        nameplateFileId: [
                          {
                            ...entity?.nameplateFileInfo,
                            fileUrl: entity?.nameplateFileInfo?.fileUrl,
                          },
                        ],
                        samplePicId: [
                          {
                            ...entity?.samplePicInfo,
                            fileUrl: entity?.samplePicInfo?.fileUrl,
                          },
                        ],
                      },
                      onSuccess: () => action?.reload(),
                    })
                  }
                >
                  编辑
                </a>
              </Access>
            )}
          </>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.SampleInfoVO>
        crudKey="sampleInfo"
        hiddenBtns={['add', 'edit', 'detail', 'delete']}
        scroll={{
          scrollToFirstRowOnChange: true,
          x: 'fit-content',
          y: 500,
        }}
        pagination={{
          pageSizeOptions: [10, 15, 20, 50, 100, 1000],
        }}
        actionRef={actionRef}
        toolBarRender={(action: any, { selectedRowKeys, selectedRows }: any) => {
          return [
            <Access key={'add'} accessible={commonAccess('admin:/taskManage/sample@add')}>
              <Button
                type="primary"
                key={'add'}
                onClick={async () => {
                  let res = await sampleInfoGenerateSampleNumber();
                  alert_edit({
                    initialValues: res?.data
                      ? {
                          sampleNumber: res.data.sampleNumber,
                          secondaryBlindSampleNumber: res.data.secondaryBlindSampleNumber,
                        }
                      : undefined,
                    onSuccess: () => action?.reload(),
                  });
                }}
              >
                新增
              </Button>
            </Access>,
          ];
        }}
        search={{
          form,
        }}
        columns={columns}
      />
    </PageContainer>
  );
};

export default Page;
