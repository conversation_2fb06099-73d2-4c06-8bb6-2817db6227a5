import { PlanStatusColor } from '@/pages/home/<USER>/plan';
import { arr2ValueEnum, parseSrc } from '@/utils';
import { ProDescriptions, ProDescriptionsProps } from '@ant-design/pro-components';
import { Button, Modal, ModalProps, Tag } from 'antd';
import { Card } from 'antd-pro-crud';
import Img from '@/components/img';

type SampleDetailProps = {
  row: BASE.SampleInfoVO;
} & ModalProps;

const SampleDetail: React.FC<SampleDetailProps> = ({ row, ...rest }) => {
  return (
    <Modal
      title="样品详情"
      width={1000}
      footer={(originNode) => {
        return (
          <Button
            type="primary"
            onClick={(e) => rest?.onCancel?.(e as React.MouseEvent<HTMLButtonElement>)}
          >
            确定
          </Button>
        );
      }}
      {...rest}
    >
      <Card title="样品信息" extra={false}>
        <ProDescriptions<BASE.SampleInfoVO>
          bordered
          column={2}
          columns={[
            // {
            //   dataIndex: 'number',
            //   title: '任务号',
            //   hideInTable: true,
            //   hideInDescriptions: true,
            //   hideInForm: true,
            // },
            {
              dataIndex: 'sampleName',
              title: '物资类别',
              span: 2,
            },
            {
              dataIndex: 'jzSampleModel',
              title: '物资型号',
            },
            {
              title: '物资规格',
              dataIndex: 'sampleModel',
            },
            // 检测类型
            {
              dataIndex: 'testType',
              title: '检测类型',
            },
            // 样品编号
            {
              dataIndex: 'sampleNumber',
              title: '样品编号',
            },
            {
              title: '盲样编号',
              dataIndex: 'blindSampleNumber',
            },
            {
              title: '二次盲样编号',
              dataIndex: 'secondaryBlindSampleNumber',
            },
            {
              dataIndex: 'sampleMajor',
              title: '样品所属专业',
              valueEnum: arr2ValueEnum(['变压器', '开关', '材料', '线圈（线缆）']),
            },
            // 收样时间
            {
              dataIndex: 'sampleReceiptTime',
              title: '收样时间',
              valueType: 'dateTime',
              fieldProps: {
                format: 'YYYY-MM-DD HH:mm',
              },
            },
            {
              dataIndex: 'taskStatus',
              title: '任务状态',
              valueEnum: (entity) =>
                Object.keys(PlanStatusColor).reduce((acc, cur) => {
                  return {
                    ...acc,
                    [cur]: <Tag color={PlanStatusColor?.[cur || '待分配']}>{cur}</Tag>,
                  };
                }, {}),
              hideInTable: true,
            },
            // 报告状态
            {
              dataIndex: 'jzReportStatus',
              title: '报告状态',
            },
            {
              dataIndex: 'nameplateFileInfo',
              title: '样品铭牌',
              render(dom, entity, index, action, schema) {
                return <Img style={{ width: 100, height: 60 }} src={entity?.nameplateFileInfo?.fileUrl?.replace('/', '')} />;
              },
            },
            {
              dataIndex: 'samplePicInfo',
              title: '样品照片',
              render(dom, entity, index, action, schema) {
                return <Img style={{ width: 100, height: 60 }} src={entity?.samplePicInfo?.fileUrl?.replace('/', '')} />;
              },
            },
            {
              dataIndex: 'sampleRemark',
              title: '样品备注',
            },
          ]}
          dataSource={row}
        />
      </Card>
    </Modal>
  );
};
export default SampleDetail;
