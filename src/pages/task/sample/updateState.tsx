import { sampleInfoBackSampleInfo } from '@/services/base/yangpinjibenxinxibiaojiekou';
import { ModalForm, ModalFormProps, ProFormSelect } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { useState } from 'react';

type UpdateStateProps = {
  row: BASE.SampleInfoVO;
  onSuccess?: () => void;
} & ModalFormProps;

const UpdateState: React.FC<UpdateStateProps> = (props: UpdateStateProps) => {
  const { row, onSuccess, ...rest } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const onFinish = async (values: any) => {
    setLoading(true);
    const res = await sampleInfoBackSampleInfo({
      ...row,
      ...values,
    });
    setLoading(false);
    if (res?.success) {
      message.success(res?.message);
      props?.modalProps?.onCancel?.(values);
      onSuccess?.();
    }
  };
  return (
    <ModalForm
      form={form}
      {...rest}
      onFinish={onFinish}
    >
      <ProFormSelect
        name="backStatus"
        label="返样类型"
        options={[
          {
            label: '有样返样',
            value: '有样返样',
          },
          {
            label: '无样返样',
            value: '无样返样',
          },
          {
            label: '部分返样',
            value: '部分返样',
          },
        ]}
      />
    </ModalForm>
  );
};

export default UpdateState;
