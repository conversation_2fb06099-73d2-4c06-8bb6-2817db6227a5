// import { ProTable, ProTableProps } from '@ant-design/pro-components';
// import { Access, useAccess } from '@umijs/max';
// import { Button, DatePicker, message, Modal, ModalProps, Tag } from 'antd';
// import { alert_edit } from 'antd-pro-crud';
// import dayjs from 'dayjs';


// export const range = (start: number, end: number) => {
//   const result = [];
//   for (let i = start; i < end; i++) {
//     result.push(i);
//   }
//   return result;
// };

// const SampleRecord: React.FC<{ row: BASE.SampleInfoVO } & ModalProps> = ({ row, ...rest }) => {
//   const { commonAccess } = useAccess();

  

//   const columns: ProTableProps<any, Record<string, any>>['columns'] = [
//     {
//       title: '样品编号',
//       dataIndex: ['sampleInfo', 'sampleNumber'],
//       render(dom, entity, index, action, schema) {
//         return entity?.taskInfo?.number && `${entity?.taskInfo?.number}#${entity?.sampleInfo?.sampleNumber}`;
//       },
//       width: 150,
//     },
//     {
//       title: '样品',
//       render: (_, record) => {
//         return record?.sampleInfo?.sampleName || '-';
//       },
//     },
//     {
//       title: '配件',
//       render: (_, record) => {
//         return (
//           <div>
//             {record?.sampleFittingsOperationLogs?.map((i) => (
//               <Tag
//                 key={i.id}
//               >{`${i.sampleFittingsInfo?.fittingsName}${i?.sampleFittingsInfo?.fittingsNumber}`}</Tag>
//             ))}
//           </div>
//         );
//       },
//     },
//     {
//       title: '领取/归还人',
//       render: (_, record) => {
//         return record?.orgUserInfo?.fullName || '-';
//       },
//     },
//     {
//       title: '领取部门',
//       dataIndex: 'deptName',
//     },
//     {
//       dataIndex: 'recordMethod',
//       title: '类型',
//     },
//     {
//       dataIndex: 'remarks',
//       title: '备注',
//     },
//     {
//       dataIndex: 'createTime',
//       title: '领取/归还时间',
//       valueType: 'dateTime',
//     },
//     {
//       title: '操作',
//       valueType: 'option',
//       render: (text, record, index, action) => {
//         return [
//           <Access key="editable" accessible={commonAccess('admin:/task/sample@editTime')}>
//             <a
//               onClick={() => {
//                 alert_edit({
//                   title: '编辑',
//                   columns: [
//                     {
//                       dataIndex: 'newDate',
//                       title: '领取/归还时间',
//                       renderFormItem: (scheam, config, form) => {
//                         return (
//                           <DatePicker
//                             showNow={false}
//                             showTime={{ defaultValue: dayjs(record?.createTime) }}
//                             disabledDate={(date) => {
//                               if (
//                                 !row?.sampleOperationInfoList ||
//                                 row?.sampleOperationInfoList?.length === 0
//                               ) {
//                                 return false;
//                               } else if (row?.sampleOperationInfoList?.length === 1) {
//                                 //只有一条记录，限制修改时间为到样时间
//                                 return date.isBefore(
//                                   dayjs(
//                                     row?.taskInfo?.sampleArrivalDate ||
//                                       row?.entrustInfo?.sampleArrivalDate,
//                                   ),
//                                 );
//                               } else {
//                                 const newIndex = index;
//                                 const sampleOperationList = row.sampleOperationInfoList;
//                                 if (newIndex === 0) {
//                                   return date.isBefore(
//                                     dayjs(sampleOperationList?.[newIndex + 1]?.createTime),
//                                     'day',
//                                   );
//                                 } else if (newIndex === sampleOperationList?.length - 1) {
//                                   return (
//                                     date.isBefore(
//                                       dayjs(
//                                         row?.taskInfo?.sampleArrivalDate ||
//                                           row?.entrustInfo?.sampleArrivalDate,
//                                         'day',
//                                       ),
//                                     ) ||
//                                     date.isAfter(
//                                       dayjs(sampleOperationList?.[newIndex - 1]?.createTime),
//                                       'day',
//                                     )
//                                   );
//                                 } else {
//                                   return (
//                                     date.isBefore(
//                                       dayjs(sampleOperationList?.[newIndex + 1]?.createTime),
//                                       'day',
//                                     ) ||
//                                     date.isAfter(
//                                       dayjs(sampleOperationList?.[newIndex - 1]?.createTime),
//                                       'day',
//                                     )
//                                   );
//                                 }
//                               }
//                             }}
//                             disabledTime={(date) => {
//                               if (
//                                 !row?.sampleOperationInfoList ||
//                                 row?.sampleOperationInfoList?.length === 0
//                               ) {
//                                 return {};
//                               } else if (row?.sampleOperationInfoList?.length === 1) {
//                                 //只有一条记录，限制修改时间为到样时间
//                                 return {};
//                               } else {
//                                 const newIndex = index;
//                                 const sampleOperationList = row.sampleOperationInfoList;
//                                 let startTime: any;
//                                 let endTime: any;
//                                 if (newIndex === 0) {
//                                   startTime = dayjs(
//                                     sampleOperationList?.[newIndex + 1]?.createTime,
//                                   );
//                                 } else if (newIndex === sampleOperationList?.length - 1) {
//                                  startTime = dayjs(
//                                     row?.taskInfo?.sampleArrivalDate ||
//                                       row?.entrustInfo?.sampleArrivalDate,
//                                   );
//                                   endTime = dayjs(
//                                     sampleOperationList?.[newIndex - 1]?.createTime,
//                                   );
//                                 } else {
//                                   startTime = dayjs(
//                                     sampleOperationList?.[newIndex + 1]?.createTime,
//                                   );
//                                   endTime = dayjs(
//                                     sampleOperationList?.[newIndex - 1]?.createTime,
//                                   );
//                                 }
//                                 if (date?.date() === startTime?.date()) {
//                                   return {
//                                     disabledHours: () => range(0, startTime?.hour()),
//                                     disabledMinutes: (startTime?.hour() === date?.hour() ? () => range(0, startTime?.minute()) : null) as any,
//                                     disabledSeconds: (startTime?.minute() === date?.minute() ? () => range(0, startTime?.second()) : null) as any,
//                                   }
//                                   // return {
//                                   //   disabledHours: () => range(0, startTime?.hour()),
//                                   //   disabledMinutes: () => range(0, startTime?.minute()),
//                                   //   disabledSeconds: () => range(0, startTime?.second()),
//                                   // };
//                                 } else if (date?.date() === endTime?.date()) {
//                                   return {
//                                     disabledHours: () => range(endTime?.hour(), 24),
//                                     disabledMinutes: (endTime?.hour() === date?.hour() ? () => range(endTime?.minute(), 60) : null)  as any,
//                                     disabledSeconds: (endTime?.minute() === date?.minute() ? () => range(endTime?.second(), 60) : null) as any,
//                                   };
//                                 } else {
//                                   return {};
//                                 }
//                               }
//                             }}
//                           />
//                         );
//                       },
//                     },
//                   ],
//                   onFinish: async (value) => {
//                     const success = await sampleInfoUpdateClaimReturnTime({
//                       ...value,
//                       sampleId: row.id,
//                       sampleOperationId: record.id,
//                     });
//                     if (success) {
//                       action?.reload();
//                     }
//                   },
//                 });
//               }}
//             >
//               编辑
//             </a>
//           </Access>,
//         ];
//       },
//     },
//   ];
//   return (
//     <Modal
//       title="领取记录"
//       width={950}
//       footer={
//         <>
//           <Button
//             type="primary"
//             onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
//               rest?.onCancel?.(e);
//             }}
//           >
//             确定
//           </Button>
//         </>
//       }
//       {...rest}
//     >
//       <ProTable<any>
//         search={false}
//         options={false}
//         pagination={false}
//         rowKey="id"
//         // dataSource={row?.sampleOperationInfoList || []}
//         request={async () => {
//           return {
//             success: true,
//             data: [],
//             message: '获取成功',
//           }
//         }}
//         columns={columns}
//       />
//     </Modal>
//   );
// };
// export default SampleRecord;
