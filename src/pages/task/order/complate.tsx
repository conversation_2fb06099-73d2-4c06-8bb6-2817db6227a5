import FileDownload from '@/components/fileDownload';
import {
  jzTaskWorkOrderInfoAutoResult,
  jzTaskWorkOrderInfoEndWorkOrder,
} from '@/services/base/jingzhougongdanliebiaobiaojiekou';
import ProTable from '@ant-design/pro-table';
import { Button, message, Modal, ModalProps, Select } from 'antd';
import { useEffect, useState } from 'react';

type SampleDetailProps = {
  row?: any;
  onFinish?: () => void;
} & ModalProps;

const SampleDetail: React.FC<SampleDetailProps> = ({ row, onFinish, ...rest }) => {
  const [data, setData] = useState<any[]>([]);

  useEffect(() => {
    setData(row?.jzTaskInspectionItemInfos || []);
  }, [row]);

  const getResult = async () => {
    try {
      const res = await jzTaskWorkOrderInfoAutoResult(data.map((item) => item.id));
      if (res.success) {
        let arr: any[] = [];
        data.map((item: any, index) => {
          let itemData = res.data?.find((sub: any) => sub.id === item.id);
          arr.push({
            ...item,
            testResults: itemData?.result || '',
            fileId: itemData?.fileId,
            fileInfo: itemData?.fileInfo,
            tfQualified: itemData?.tfQualified,
          });
          return null;
        });
        setData(arr);
        message.success('获取实验数据成功');
        onFinish?.();
      } else {
        message.error('获取实验数据失败');
      }
    } catch (error) {
      message.error('获取实验数据失败');
    }
  };

  useEffect(() => {
    if (rest.open && row.equipmentName === '游标卡尺') {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          getResult();
        }
      };
      // 监听键盘事件
      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [rest.open]);

  return (
    <Modal
      title="检项列表"
      width={1000}
      footer={(originNode) => {
        return (
          <>
            {data.length > 0 && (
              <Button
                type="primary"
                onClick={async (e) => {
                  getResult();
                }}
              >
                获取实验数据
              </Button>
            )}
            <Button
              type="primary"
              onClick={async (e) => {
                try {
                  const res = await jzTaskWorkOrderInfoEndWorkOrder({
                    id: row?.id,
                    jzTaskInspectionItemInfosUpdate: data,
                  });
                  if (res.success) {
                    message.success('检毕成功');
                    onFinish?.();
                    rest?.onCancel?.(e as React.MouseEvent<HTMLButtonElement>);
                  } else {
                    message.error('检毕失败');
                  }
                } catch (error) {
                  message.error('检毕失败');
                }
              }}
            >
              检毕
            </Button>
          </>
        );
      }}
      {...rest}
    >
      <ProTable<any>
        columns={[
          {
            title: '检项',
            dataIndex: 'testName',
          },
          {
            title: '数据采集方式',
            dataIndex: 'dataCollectionMethod',
          },
          {
            title: '单位',
            dataIndex: 'unit',
          },
          {
            title: '结果',
            dataIndex: 'testResults',
          },
          {
            title: '关联附件',
            dataIndex: 'fileInfo',
            render: (_, record) => {
              return (
                <FileDownload
                  item={record?.fileInfo}
                  preview={false}
                  isDowmload={true}
                  key={record?.fileInfo?.id}
                />
              );
            },
          },
          {
            title: '是否合格',
            dataIndex: 'tfQualified',
            width: 180,
            render: (_, record) => {
              return (
                <Select
                  placeholder="请选择"
                  style={{ width: '100%' }}
                  options={[
                    { label: '合格', value: '合格' },
                    { label: '不合格', value: '不合格' },
                  ]}
                  allowClear
                  value={record.tfQualified}
                  onChange={(value) => {
                    const newData: any[] = [];
                    data.map((item: any) => {
                      newData.push({
                        ...item,
                        tfQualified: item.id === record.id ? value : item.tfQualified,
                      });
                      return null;
                    });
                    setData(newData);
                  }}
                />
              );
            },
          },
        ]}
        rowKey="id"
        dataSource={data}
        pagination={false}
        search={false}
        toolBarRender={false}
      />
    </Modal>
  );
};
export default SampleDetail;
