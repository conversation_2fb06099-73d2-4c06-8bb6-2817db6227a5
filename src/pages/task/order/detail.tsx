import Title from '@/components/title';
import FileDownload from '@/components/fileDownload';
import ProDescriptions from '@/pages/attorney/components/proDescriptions';
import ProTable from '@ant-design/pro-table';
import { Button, Divider, message, Modal, ModalProps } from 'antd';
import { useState, useEffect } from 'react';

type SampleDetailProps = {
  row?: BASE.JzTaskWorkOrderInfoVO;
  onFinish?: () => void;
} & ModalProps;

const SampleDetail: React.FC<SampleDetailProps> = ({ row, onFinish, ...rest }) => {
  const [data, setData] = useState<BASE.JzTaskInspectionItemInfo[]>([]);

  useEffect(() => {
    setData(row?.jzTaskInspectionItemInfos || []);
  }, [row]);

  return (
    <Modal
      title="检项列表"
      width={1000}
      footer={(originNode) => {
        return (
          <>
            <Button
              type="primary"
              onClick={async (e) => {
                rest?.onCancel?.(e as React.MouseEvent<HTMLButtonElement>);
              }}
            >
              确定
            </Button>
          </>
        );
      }}
      {...rest}
    >
      <ProDescriptions<any>
        title="基本信息"
        dataSource={row}
        column={2}
        bordered
        columns={[
          {
            dataIndex: 'workOrderNumber',
            title: '工单编号',
          },
          {
            dataIndex: 'equipmentName',
            title: '设备名称',
          },
          {
            dataIndex: 'gwBzName',
            title: '关联工位',
          },
          {
            dataIndex: 'testUserFullName',
            title: '检测人员',
          },
          {
            dataIndex: 'statusInfo',
            title: '状态',
          },
          {
            dataIndex: 'startDate',
            title: '开始时间',
            valueType: 'dateTime',
          },
          {
            dataIndex: 'inspectionCompletionTime',
            title: '结束时间',
            valueType: 'dateTime',
          },
        ]}
      />
      <Title title="检项列表" />
      <ProTable<BASE.JzTaskWorkOrderInfo>
        columns={[
          {
            title: '检项',
            dataIndex: 'testName',
          },
          {
            title: '数据采集方式',
            dataIndex: 'dataCollectionMethod',
          },
          {
            title: '单位',
            dataIndex: 'unit',
          },
          {
            title: '结果',
            dataIndex: 'testResults',
          },
          {
            title: '关联附件',
            dataIndex: 'fileInfo',
            render: (_, record) => {
              return <FileDownload item={record?.fileInfo} preview={false} isDowmload={true} key={record?.fileInfo?.id} />;
            },  
          },
        ]}
        dataSource={data}
        pagination={false}
        search={false}
        toolBarRender={false}
      />
    </Modal>
  );
};
export default SampleDetail;
