import ProTable, { ProTableProps } from '@/components/proTable';
import { jzTaskWorkOrderInfoStartWorkOrder } from '@/services/base/jingzhougongdanliebiaobiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Form, message,  } from 'antd';
import { launch } from 'antd-pro-crud';
import dayjs from 'dayjs';
import { useEffect, useRef } from 'react';
import Complate from './complate';
import Detail from './detail';
const alert_detail = launch(Detail);
const alert_Complate = launch(Complate);

import { Access, useAccess, useSearchParams, history } from '@umijs/max';
import './index.less';

function Order() {
  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();

  const { commonAccess } = useAccess();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (searchParams.get('id')) {
      history.replace('/task/order');
      form.setFieldsValue({
        workOrderNumber: searchParams.get('id'),
      });
      // 触发查询
      form.submit();
    }
  }, [searchParams]);

  const columns: ProTableProps<BASE.JzTaskWorkOrderInfoVO>['columns'] = [
    //工单编号（任务编号-1、任务编号-2形式）、检测设备、实验项目、检测人员、工单状态（在检、检毕）、工单开始时间、工单检毕时间
    {
      dataIndex: 'index',
      valueType: 'index',
      title: '序号',
      width: 80,
    },
    {
      title: '工单编号',
      dataIndex: 'workOrderNumber',
      ellipsis: true,
    },
    {
      title: '检测设备',
      dataIndex: 'equipmentName',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '实验项目',
      dataIndex: 'gwBzName',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '检测人员',
      dataIndex: 'testUserFullName',
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '工单状态',
      dataIndex: 'statusInfo',
      valueType: 'select',
      valueEnum: arr2ValueEnum(['在检', '检毕']),
    },
    {
      title: '工单开始时间',
      dataIndex: 'startDate',
      valueType: 'dateTime',
      hideInSearch: true,
      ellipsis: true,
      fieldProps: {
        style: {
          width: '100%',
        },
      },
      render: (_, record) => {
        let time = record.startDate;
        return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '工单检毕时间',
      dataIndex: 'inspectionCompletionTime',
      valueType: 'dateTime',
      hideInSearch: true,
      ellipsis: true,
      fieldProps: {
        style: {
          width: '100%',
        },
      },
      render: (_, record) => {
        let time = record.inspectionCompletionTime;
        return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    // 操作
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => {
        let arr = [
          <Access key="detail" accessible={commonAccess('admin:/task/order@detail')}>
            <a onClick={() => alert_detail({ row: record })}>详情</a>
          </Access>,
        ];
        if (record.statusInfo === '检毕') return arr;
        if (record.statusInfo === '待检') {
          arr.push(
            <Access key="start" accessible={commonAccess('admin:/task/order@start')}>
              <a
                onClick={async () => {
                  try {
                    const res = await jzTaskWorkOrderInfoStartWorkOrder({
                      id: record.id as unknown as string,
                    });
                    if (res.success) {
                      message.success('开始任务成功');
                      actionRef.current?.reload();
                    } else {
                      message.error('开始任务失败');
                    }
                  } catch (error) {
                    message.error('开始任务失败');
                  }
                }}
              >
                开始
              </a>
            </Access>,
          );
          return arr;
        }
        arr.push(
          <Access key="end" accessible={commonAccess('admin:/task/order@end')}>
            <a
              onClick={async () => {
                alert_Complate({
                row: record,
                onFinish: async () => {
                  actionRef.current?.reload();
                },
              });
            }}
          >
              工单检毕
            </a>
          </Access>,
        );
        return arr;
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.JzTaskWorkOrderInfoVO>
        crudKey="jzTaskWorkOrderInfo"
        hiddenBtns={['edit', 'delete', 'add', 'detail']}
        actionRef={actionRef}
        scroll={{
          scrollToFirstRowOnChange: true,
          x: 'fit-content',
          y: 500,
        }}
        pagination={{
          pageSizeOptions: [10, 15, 20, 50, 100, 1000],
        }}
        toolBarRender={(action, { selectedRowKeys, selectedRows }) => {
          return [];
        }}
        rowKey="id"
        crud={{}}
        search={{
          form,
        }}
        columns={columns}
      />
    </PageContainer>
  );
}

export default Order;
