import CrudProTable, { ProTableProps as CrudProTableProps } from '@/components/proTable';
import { Typography } from 'antd';
import { get, isEqual, PropertyPath } from 'lodash';
import React, { useMemo } from 'react';
import { formatValue } from '../proDescriptions';
const { Text, Link } = Typography;

export type ProTableProps<T = Record<string, any>> = CrudProTableProps<T> & {
  newdataSource?: Record<string, any>[];
};

/**
 *
 * @param dataSource 旧数据
 * @param newdataSource 新数据
 * @returns 返回比较后的数据
 */
export const compareList = (
  dataSource?: readonly Record<string, any>[],
  newdataSource?: readonly Record<string, any>[],
) => {
  console.log(dataSource, newdataSource, 'compareList');
  /**
   * 以dataSource为主，newdataSource为辅，如果dataSource中的有值，newdataSource中没有，表示该数据被删除；
   * 如果dataSource中的值和newdataSource中的值不一样，表示该数据被修改
   * 如果newdataSource中有值，dataSource中没有，表示该数据被新增
   **/
  if (!newdataSource?.length) {
    return dataSource?.map((i) => ({ ...i, type: 'delete' }));
  }
  if (!dataSource?.length) {
    return newdataSource?.map((i) => ({ ...i, type: 'success' }));
  }
  const dataSourceKeyItem = dataSource.reduce((acc, cur) => {
    acc[cur.id] = cur;
    return acc;
  }, {});

  const newdataSourceKeyItem = newdataSource.reduce((acc, cur) => {
    acc[cur.id] = cur;
    return acc;
  }, {});
  const keys = new Set([...Object.keys(dataSourceKeyItem), ...Object.keys(newdataSourceKeyItem)]);
  const result = Array.from(keys).reduce((acc: Record<string, any>[], cur: string) => {
    // 新增
    if (newdataSourceKeyItem?.[cur] && !dataSourceKeyItem[cur]) {
      return [
        ...acc,
        {
          ...newdataSourceKeyItem[cur],
          type: 'success',
        },
      ];
      // 修改
    } else if (
      newdataSourceKeyItem[cur] &&
      dataSourceKeyItem[cur] &&
      !isEqual(newdataSourceKeyItem[cur], dataSourceKeyItem[cur])
    ) {
      return [
        ...acc,
        {
          ...dataSourceKeyItem[cur],
          type: 'danger',
        },
      ];
      // 删除
    } else if (!newdataSourceKeyItem[cur] && dataSourceKeyItem[cur]) {
      return [
        ...acc,
        {
          ...dataSourceKeyItem[cur],
          type: 'delete',
        },
      ];
    } else {
      return [...acc, dataSourceKeyItem[cur]];
    }
  }, []);

  return result;
};

const ProTable: React.FC<ProTableProps> = (props) => {
  const { newdataSource, columns, dataSource, ...rest } = props;

  const _dataSource = useMemo(() => {
    return compareList(dataSource, newdataSource);
  }, [dataSource, newdataSource]);

  const _columns: ProTableProps['columns'] = columns?.map((item: any, index) => {
    if (['option'].includes(item.valueType || '') || !newdataSource) return item;
    return {
      ...item,
      render(dom, entity, index, action, schema) {
        return (
          <>
            <Text
              delete={entity?.type === 'delete'}
              type={entity?.type !== 'danger' && entity?.type}
            >
              {item?.render
                ? item.render(dom, entity, index, action, schema)
                : item.valueType === 'index' ? dom : formatValue(item, entity)}
            </Text>

            {entity.type === 'danger' &&
              item.dataIndex &&
              newdataSource &&
              !isEqual(
                get(entity, item.dataIndex as PropertyPath),
                get?.(newdataSource, `${index}.${item.dataIndex}`),
              ) && (
                <Text type={entity?.type}>
                  {`-> `}
                  {item?.render
                    ? item.render(
                        dom,
                        newdataSource?.find((i) => i.id === entity.id),
                        index,
                        action,
                        schema,
                      )
                    : formatValue(item, newdataSource?.find((i) => i.id === entity.id) || {})}
                </Text>
              )}
          </>
        );
      },
    };
  });

  return (
    <CrudProTable
      toolbar={{
        actions: [],
      }}
      needSelfColumns={false}
      title={
        newdataSource
          ? () => {
              return (
                <div>
                  <Text type="success">绿色表示新增数据；</Text>
                  <Text type="danger">红色表示数据修改；</Text>
                  <Text delete>横线表示数据被删除</Text>
                </div>
              );
            }
          : undefined
      }
      hiddenBtns={'all'}
      dataSource={_dataSource}
      columns={_columns}
      {...rest}
    />
  );
};

export default ProTable;
