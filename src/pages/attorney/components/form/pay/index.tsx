import BillForm from './bill';
import { BaseFormProps } from '../base';
import ContractForm from './contract';
import PayInfo from './payInfo';

type PayFormProps = BaseFormProps & {};

const PayForm: React.FC<PayFormProps> = ({ isDetail, dependency, initRowData, formRefs }) => {
  return (
    <>
      {/* <PayInfo disabled={isDetail} /> */}
      <ContractForm required={!dependency?.isDraft} formRefs={formRefs} isDisPrice={initRowData?.taskInfos?.taskStatus ? ['待受理', '待分配']?.includes(initRowData?.taskInfos?.taskStatus) : true}/>
      {/* <BillForm /> */}
    </>
  );
};

export default PayForm;
