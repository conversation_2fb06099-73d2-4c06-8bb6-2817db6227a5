import Title from '@/components/title';
import {
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
} from '@ant-design/pro-components';
type BaseFormProps = {
  formRefs?:React.MutableRefObject<Record<string, ProFormInstance | null>>;
  extraColumns?: React.ReactNode;
  required?: boolean;
  isEdit?: boolean;
  isDisPrice?: boolean; // 任务分配后不能编辑预估单价
};

const ContractForm: React.FC<BaseFormProps> = (props) => {
  const { formRefs, extraColumns, required, isEdit, isDisPrice = true } = props;
  return (
    <>
      <Title title="合同信息" />

      <ProFormCheckbox
        label="是否计时"
        name={'tfTiming'}
        disabled={isEdit && !isDisPrice}
        transform={(value) => {
          return {
            tfTiming: Number(value),
          };
        }}
      />

      <ProFormDependency name={['tfTiming']}>
        {({ tfTiming }) => {
          return (
            <ProFormDigit
              label={'合同金额'}
              name={'contractAmount'}
              formItemProps={{ rules: [{ required: required && !tfTiming }] }}
              fieldProps={{
                style: { width: '100%' },
                addonAfter: '元',
                stringMode: true,
                onBlur: (value) => {
                  const contractAmountReceive = formRefs?.current?.['payForm']?.getFieldValue('contractAmountReceive');
                  const contractBalancePay = formRefs?.current?.['payForm']?.getFieldValue('contractBalancePay');

                  if (!value) {
                    formRefs?.current?.['payForm']?.resetFields(['contractAmountReceive', 'contractBalancePay']);
                  } else if (contractAmountReceive) {
                    if (contractAmountReceive > Number(value)) {
                      formRefs?.current?.['payForm']?.setFieldsValue({
                        contractBalancePay: undefined,
                        contractAmountReceive: Number(value),
                      });
                    } else {
                      formRefs?.current?.['payForm']?.setFieldsValue({
                        contractBalancePay: Number(value) - contractAmountReceive,
                      });
                    }
                  } else if (contractBalancePay) {
                    if (contractBalancePay > Number(value)) {
                      formRefs?.current?.['payForm']?.setFieldsValue({
                        contractAmountReceive: undefined,
                        contractBalancePay: Number(value),
                      });
                    } else {
                      formRefs?.current?.['payForm']?.setFieldsValue({
                        contractAmountReceive: Number(value) - contractAmountReceive,
                      });
                    }
                  }
                },
              }}
            />
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['tfTiming']}>
        {({ tfTiming }) => {
          return <ProFormDigit
          label={'预估单价'}
          name={'price'}
          disabled={isEdit && !isDisPrice}
          formItemProps={{ rules: [{ required: required && tfTiming }] }}
          fieldProps={{ style: { width: '100%' }, addonAfter: '元/小时', stringMode: true }}
        />
        }}
      </ProFormDependency>

      {/* <ProFormDependency name={['contractAmount']}>
        {({ contractAmount }) => {
          return (
            <>
              <ProFormDigit
                label={'合同到账金额'}
                name={'contractAmountReceive'}
                fieldProps={{
                  style: { width: '100%' },
                  addonAfter: '元',
                  onBlur: (value) => {
                    if (value) {
                      if (contractAmount) {
                        formRefs?.current?.['payForm']?.setFieldsValue({
                          contractBalancePay: contractAmount - Number(value),
                        });
                      } else if (formRefs?.current?.['payForm']?.getFieldValue('contractBalancePay')) {
                        formRefs?.current?.['payForm']?.setFieldsValue({
                          contractAmount:
                            Number(value) + formRefs?.current?.['payForm']?.getFieldValue('contractBalancePay'),
                        });
                      }
                    } else {
                      if (contractAmount) {
                        formRefs?.current?.['payForm']?.setFieldsValue({
                          contractBalancePay: contractAmount,
                        });
                      }
                    }
                  },
                }}
              />

              <ProFormDigit
                label={'合同尾款'}
                name={'contractBalancePay'}
                fieldProps={{
                  style: { width: '100%' },
                  addonAfter: '元',
                  onBlur: (value) => {
                    if (value) {
                      if (contractAmount) {
                        formRefs?.current?.['payForm']?.setFieldsValue({
                          contractAmountReceive: contractAmount - Number(value),
                        });
                      } else if (formRefs?.current?.['payForm']?.getFieldValue('contractAmountReceive')) {
                        formRefs?.current?.['payForm']?.setFieldsValue({
                          contractAmount:
                            Number(value) +
                            formRefs?.current?.['payForm']?.getFieldValue('contractAmountReceive'),
                        });
                      }
                    } else {
                      if (contractAmount) {
                        formRefs?.current?.['payForm']?.setFieldsValue({
                          contractAmountReceive: contractAmount,
                        });
                      }
                    }
                  },
                }}
              />
            </>
          );
        }}
      </ProFormDependency> */}

      {extraColumns}
    </>
  );
};
export default ContractForm;
