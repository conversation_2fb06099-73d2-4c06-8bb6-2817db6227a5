import Action from '@/components/action';
import EditTable from '@/components/editTable';
import { DictSelect } from '@/components/select/dict';
import Text from '@/components/text';
import Title from '@/components/title';
import { genUUID } from '@/components/verify/util';
import SampleEdit from '@/pages/task/sample/edit';
import { sampleInfoVoPage } from '@/services/base/yangpinjibenxinxibiaojiekou';
import { importbasicCompany } from '@/services/base/yangpinpiliangdaorujiekou';
import { arr2ValueEnum } from '@/utils';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import {
  EditableFormInstance,
  nanoid,
  ProForm,
  ProFormInstance,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Form, message, Modal, ModalProps, Space, Upload } from 'antd';
import { launch, launchSchema } from 'antd-pro-crud';
import lodash from 'lodash';
import { useMemo, useRef, useState } from 'react';

type BaseFormProps = {
  disabled?: boolean;
  extraColumns?: React.ReactNode;
  name?:string;
};

type FittingObject = Partial<BASE.SampleFittingsInfo> & {
  rowId: string;
};

const PayInfo: React.FC<BaseFormProps> = (props) => {
  const {
    disabled = false,
    extraColumns,
    name = 'buEntrustPayInfos'
  } = props;
  const editableFormRef = useRef<EditableFormInstance>();
  const { initialState } = useModel('@@initialState');
  const payInfoFormRef = useRef<ProFormInstance>();

  return (
    <>
      <Title
        title="已支付信息"
      />
      <Form.Item
        name={name}
        style={{ padding: '5px' }}
      >
        <EditTable<Partial<BASE.SampleInfoVO> & { rowId: number }>
          hidenEdit={disabled}
          editableFormRef={editableFormRef}
          formRef={payInfoFormRef}
          columns={[
            {
              title: '支付对象',
              dataIndex: 'payObj',
              fieldProps: { disabled, style: { width: '100%' } },
            },
            {
              title: '支付金额（元）',
              dataIndex: 'payAmount',
              valueType: 'digit',
              fieldProps: { disabled: disabled, style: { width: '100%' } },
            },
            {
              title: '支付时间',
              dataIndex: 'payTime',
              valueType: 'date',
              fieldProps: { disabled: disabled, style: { width: '100%' } },
            },
            {
              title: '支付原因',
              dataIndex: 'payReson',
              fieldProps: { disabled, style: { width: '100%' } },
            },
          ]}
        />
      </Form.Item>
      {extraColumns}
    </>
  );
};
export default PayInfo;
