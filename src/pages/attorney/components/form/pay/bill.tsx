import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
type BaseFormProps = {
  formRef?:
    | React.MutableRefObject<ProFormInstance | undefined>
    | React.RefObject<ProFormInstance | undefined>;
};

const BillForm: React.FC<BaseFormProps> = (props) => {
  return (
    <>
      <Title title="开票信息" />
      <ProFormDigit
        label={'开票金额'}
        name={'billPayAmount'}
        fieldProps={{ style: { width: '100%' },addonAfter:'元' }}
      />

      <ProFormDatePicker
        label={'开票日期'}
        name={'billPayDate'}
        fieldProps={{ style: { width: '100%' } }}
      />

      <ProFormSelect
        label={'开票类型'}
        name={'billPayType'}
        valueEnum={arr2ValueEnum(['普票', '专票'])}
      />
      <ProFormText
        label={'票据接收单位'}
        name={'billAcceptUnit'}
      />
    </>
  );
};
export default BillForm;
