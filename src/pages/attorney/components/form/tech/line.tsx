import EditTable from '@/components/editTable';
import Title from '@/components/title';
import { EditableFormInstance, ProFormInstance } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Form } from 'antd';
import { useRef } from 'react';

type BaseFormProps = {
  disabled?: boolean;
  extraColumns?: React.ReactNode;
  name?: string;
};

const LineLossInfo: React.FC<BaseFormProps> = (props) => {
  const { disabled = false, extraColumns } = props;
  const editableFormRef = useRef<EditableFormInstance>();
  const { initialState } = useModel('@@initialState');
  const payInfoFormRef = useRef<ProFormInstance>();

  return (
    <>
      <Title title="线损信息" />
      <Form.Item name={'abc'} style={{ padding: '5px' }}>
        <EditTable<Partial<BASE.SampleInfoVO> & { rowId: number }>
          hidenEdit={disabled}
          editableFormRef={editableFormRef}
          formRef={payInfoFormRef}
          columns={[
            {
              title: '支付对象',
              dataIndex: 'payObj',
              fieldProps: { disabled, style: { width: '100%' } },
            },
            {
              title: '支付金额（元）',
              dataIndex: 'payAmount',
              valueType: 'digit',
              fieldProps: { disabled: disabled, style: { width: '100%' } },
            },
            {
              title: '支付时间',
              dataIndex: 'payTime',
              valueType: 'date',
              fieldProps: { disabled: disabled, style: { width: '100%' } },
            },
            {
              title: '支付原因',
              dataIndex: 'payReson',
              fieldProps: { disabled, style: { width: '100%' } },
            },
          ]}
        />
      </Form.Item>
      {extraColumns}
    </>
  );
};
export default LineLossInfo;
