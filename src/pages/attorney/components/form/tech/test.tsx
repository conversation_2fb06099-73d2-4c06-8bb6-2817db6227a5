import Title from '@/components/title';
import { NumBool, NumBoolObj } from '@/enums';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { standardInfoVoPage } from '@/services/base/jichuxinxibiaozhunxinxibiaojiekou';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Col } from 'antd';
import { useEffect } from 'react';
import AntennaForm from './antenna';
type BaseFormProps = {
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  disabled?: boolean;
  required?: boolean;
  /** 额外的表单项 */
  extraColumns?: React.ReactNode;
  type?: ATTORNEY_TYPE;
  isEdit: boolean;
};

const TestForm: React.FC<BaseFormProps> = (props) => {
  const { formRefs, disabled = false, required = true, isEdit, type, extraColumns } = props;

  useEffect(() => {
    if (type === ATTORNEY_TYPE.enter && formRefs?.current?.['techForm'] && !isEdit) {
      formRefs?.current?.['techForm']?.setFieldsValue({
        certificaMark: ['CMA', 'CNAS'],
        tfAccordStatement: NumBool.是,
        tfReport: NumBool.是,
      });
    }
  }, [type, formRefs?.current?.['techForm'], isEdit]);

  return (
    <>
      <Title title="检测信息" />
      <Col
        span={24}
        style={{
          backgroundColor: 'rgb(239, 239, 239)',
          padding: '10px',
          display: 'flex',
          flexWrap: 'wrap',
        }}
      >
        {type !== ATTORNEY_TYPE.model && (
          <>
            {type === ATTORNEY_TYPE.antenna && (
              <>
                <AntennaForm required={required} disabled={disabled} />
              </>
            )}

            {type !== ATTORNEY_TYPE.antenna && (
              <ProFormText
                label={'检测项目'}
                name={'inspectionItem'}
                fieldProps={{ disabled }}
                labelCol={{ span: 3 }}
                colProps={{ span: 12 }}
                formItemProps={{ rules: [{ required: !!(type !== ATTORNEY_TYPE.enter) }] }}
              />
            )}
            <ProFormSelect
              label={'标准编号'}
              name={'standardInfo'}
              // formItemProps={{ rules: [{ required }] }}
              labelCol={{ span: 3 }}
              colProps={{ span: 12 }}
              request={async () =>
                standardInfoVoPage({
                  page: 1,
                  size: 9999,
                }).then((res) => {
                  return (
                    res?.data?.records?.map((i) => ({
                      label: `${i?.standardNum}: ${i?.standardName}`,
                      value: `${i.standardNum}: ${i.standardName}`,
                    })) || []
                  );
                })
              }
              fieldProps={{
                mode: 'tags',
                disabled,
                onChange(value, option) {
                  const preArr =
                    formRefs?.current?.['techForm']?.getFieldValue('testMethod')?.split('\r') || [];
                  formRefs?.current?.['techForm']?.setFieldValue(
                    'testMethod',
                    (value &&
                      Array.isArray(value) &&
                      Array.from(new Set([...preArr, ...value]))?.join('\r')) ||
                      '',
                  );
                },
              }}
            />

            <ProFormTextArea
              label={'检测方法'}
              labelCol={{ span: 3 }}
              colProps={{ span: 12 }}
              name={'testMethod'}
              fieldProps={{ disabled }}
              formItemProps={{ rules: [{ required: !!(type !== ATTORNEY_TYPE.enter) }] }}
            />
            <ProFormTextArea
              label={'判定依据'}
              labelCol={{ span: 3 }}
              colProps={{ span: 12 }}
              name={'judgBasis'}
              formItemProps={{ rules: [{ required: !!(type !== ATTORNEY_TYPE.enter) }] }}
              fieldProps={{ disabled }}
            />
            {type !== ATTORNEY_TYPE.enter && (
              <ProFormRadio.Group
                label={'是否分包'}
                name={'tfSubcontract'}
                labelCol={{ span: 3 }}
                colProps={{ span: 12 }}
                fieldProps={{ disabled }}
                formItemProps={{ rules: [{ required }] }}
                valueEnum={NumBoolObj}
              />
            )}
          </>
        )}

        <ProFormDependency name={['tfSubcontract']}>
          {({ tfSubcontract }) => {
            if (tfSubcontract === NumBool.是) {
              return (
                <>
                  <ProFormText
                    label={'分包信息'}
                    name={'subcontractInfo'}
                    fieldProps={{ disabled }}
                    labelCol={{ span: 3 }}
                    colProps={{ span: 12 }}
                    formItemProps={{ rules: [{ required }] }}
                  />
                  <ProFormText
                    label={'分包方'}
                    name={'subcontractor'}
                    fieldProps={{ disabled }}
                    labelCol={{ span: 3 }}
                    colProps={{ span: 12 }}
                    formItemProps={{ rules: [{ required }] }}
                  />
                </>
              );
            }

            return null;
          }}
        </ProFormDependency>

        <ProFormSelect
          label={'认证标识'}
          name={'certificaMark'}
          labelCol={{ span: 3 }}
          colProps={{ span: 12 }}
          formItemProps={{ rules: [{ required }] }}
          fieldProps={{
            disabled,
            mode: 'multiple',
            onSelect: (val: string, ...rest) => {
              let checked = formRefs?.current?.['techForm']?.getFieldValue('certificaMark');
              let checkedIdx = checked?.indexOf('无');
              if (val === '无') {
                formRefs?.current?.['techForm']?.setFieldValue('certificaMark', ['无']);
              } else if (checkedIdx > -1) {
                checked.splice(checkedIdx, 1);
                formRefs?.current?.['techForm']?.setFieldValue('certificaMark', checked);
              }
            },
          }}
          valueEnum={arr2ValueEnum(['无', 'CMA', 'CNAS', 'ILAC_CNAS', 'ILAC_A2LA', 'A2LA'])}
        />

        <ProFormRadio.Group
          label={'符合性声明'}
          // labelCol={{ span: 3 }}
          colProps={{ span: 12 }}
          name={'tfAccordStatement'}
          fieldProps={{ disabled }}
          valueEnum={NumBoolObj}
        />

        <ProFormDependency name={['tfAccordStatement']}>
          {({ tfAccordStatement }) => {
            if (tfAccordStatement === NumBool.是) {
              return (
                <>
                  <ProFormRadio.Group
                    label={'符合性声明内容'}
                    // labelCol={{ span: 3 }}
                    colProps={{ span: 12 }}
                    name={'accordStatementInfo'}
                    fieldProps={{ disabled }}
                    valueEnum={{
                      0: '判定符合性时不考虑测量不确定度，直接以测量结果值（或测量结果的平均值）作为符合性声明的判定规则依据；',
                      1: '判定符合性时考虑测量不确定度，当考虑测量不确定度的测量结果值（或测量结果的平均值）无法判定其符合性时，统一判定为符合；',
                      2: '判定符合性时考虑测量不确定度，当考虑测量不确定度的测量结果值（或测量结果的平均值）无法判定其符合性时，统一判定为不符合；',
                    }}
                    formItemProps={{ rules: [{ required }] }}
                  />
                </>
              );
            }

            return null;
          }}
        </ProFormDependency>

        {extraColumns}

        {/* <ProFormText name={'frequencyBand'} label="频段(MHz)" /> */}
      </Col>
    </>
  );
};
export default TestForm;
