import { ATTORNEY_STATUS, ATTORNEY_TYPE } from '@/enums/attorney';
import { arr2ValueEnum } from '@/utils';
import { ProFormSelect } from '@ant-design/pro-components';
import { BaseFormProps } from '../base';
import AntennaForm from './antenna';
import EnterForm from './enter';
import ModeForm, { NormalTechForm } from './mode';
import ReportForm from './report';
import TecOtherForm from './tecOther';
import TestForm from './test';


type TechFormProps = BaseFormProps & {};

const TechForm: React.FC<TechFormProps> = ({
  initRowData,
  isDetail,
  formRefs,
  dependency,
  isEdit,
}) => {
  return (
    <> 
    {/* 进网 */}
    {dependency?.type === ATTORNEY_TYPE.enter && (
        <EnterForm formRefs={formRefs}/>
      )}
      {/* 检测信息 */}
      <TestForm
        type={dependency?.type}
        isEdit={isEdit}
        required={!dependency?.isDraft}
        disabled={isDetail}
        formRefs={formRefs}
      />
      {/* 检测报告 */}
      <ReportForm required={!dependency?.isDraft} disabled={isDetail} isShowTfGenerateData={dependency?.type !== ATTORNEY_TYPE.enter}/>
       {/* 技术参数， 核准信息 */}
      {dependency?.type === ATTORNEY_TYPE.model && (
        <ModeForm
          isApproval={isEdit && initRowData.status === ATTORNEY_STATUS.approval}
          required={!dependency?.isDraft}
          formRefs={formRefs}
        />
      )}
      {/* 通用委托书-技术参数 */}
      {dependency?.type === ATTORNEY_TYPE.normal && (
        <NormalTechForm
          formRefs={formRefs}
        />
      )}
      {/* {dependency?.type === ATTORNEY_TYPE.enter && (
        <EnterForm required={!dependency?.isDraft} disabled={isDetail} />
      )} */}
      

      <TecOtherForm disabled={isDetail} type={dependency?.type} />
    </>
  );
};

export default TechForm;
