import { NumBoolObj } from '@/enums';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormCheckbox,
  ProFormDependency,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
};

const AntennaForm: React.FC<BaseFormProps> = ({ disabled }) => {
  return (
    <>
      <ProFormCheckbox.Group
        label={'检测项目'}
        colProps={{ span: 12 }}
        disabled={disabled}
        name={['buEntrustAntennaInfo', 'testItem']}
        valueEnum={arr2ValueEnum(['驻波', '隔离', '互调', '方向图', '合成波束', '其他'])}
      />

      <ProFormDependency name={[['buEntrustAntennaInfo', 'testItem']]}>
        {(depValues) => {
          const testItem = depValues?.buEntrustAntennaInfo?.testItem;
          if (testItem && testItem?.length) {
            return (
              <>
                {testItem?.includes('驻波') && (
                  <ProFormTextArea
                    name={['buEntrustAntennaInfo', 'testItems', '驻波']}
                    disabled={disabled}
                    label={`驻波测试频段`}
                    colProps={{ span: 12 }}
                  />
                )}
                {testItem?.includes('隔离') && (
                  <ProFormTextArea
                    name={['buEntrustAntennaInfo', 'testItems', '隔离']}
                    disabled={disabled}
                    label={`隔离测试频段`}
                    colProps={{ span: 12 }}
                  />
                )}
                {testItem?.includes('互调') && (
                  <ProFormTextArea
                    name={['buEntrustAntennaInfo', 'testItems', '互调']}
                    disabled={disabled}
                    label={`互调测试频段`}
                    colProps={{ span: 12 }}
                  />
                )}
                {testItem?.includes('方向图') && (
                  <>
                    <ProFormTextArea
                      name={['buEntrustAntennaInfo', 'testItems', '方向图', '测试频点角度']}
                      disabled={disabled}
                      colProps={{ span: 12 }}
                      label={`方向图测试频点角度`}
                    />
                    <ProFormTextArea
                      name={['buEntrustAntennaInfo', 'testItems', '方向图', '需计算的参数']}
                      disabled={disabled}
                      colProps={{ span: 12 }}
                      label={`方向图需计算的参数`}
                    />
                  </>
                )}
                {testItem?.includes('合成波束') && (
                  <ProFormRadio.Group
                    name={['buEntrustAntennaInfo', 'testItems', '合成波束']}
                    label={`合成波束是否提供功分板`}
                    disabled={disabled}
                    colProps={{ span: 12 }}
                    valueEnum={NumBoolObj}
                  />
                )}
                {testItem?.includes('其他') && (
                  <ProFormTextArea
                    label={'其他测试项'}
                    colProps={{ span: 12 }}
                    disabled={disabled}
                    name={['buEntrustAntennaInfo', 'testItems', '其他']}
                  />
                )}
              </>
            );
          }
          return null;
        }}
      </ProFormDependency>
    </>
  );
};

export default AntennaForm;
