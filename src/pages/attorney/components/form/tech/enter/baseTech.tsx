import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import ProFormCheckbox from '../ProFormCheckbox';
import { Col } from 'antd';
import style from './index.less';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  isVerify?: boolean;
};

const BaseTechForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, formRefs,isVerify } = props;
  return (
    <>
      <Title title="技术规格" />
      <Col span="24" className={style['enter-group']}>
        <ProFormCheckbox
          label={'GSM：'}
          name={['buEntrustIntoNetworkInfo', 'bandGsm']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['900MHz', '1800MHz', '850MHz', '1900MHz'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'CDMA：'}
          name={['buEntrustIntoNetworkInfo', 'bandCdma']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['800MHz', '1900MHz'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormSelect
          label={'WCDMA：   Band：'}
          name={['buEntrustIntoNetworkInfo', 'bandWcdma']}
          colProps={{
            span: 12,
          }}
          disabled={disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          valueEnum={arr2ValueEnum(['Ⅰ', 'Ⅱ', 'Ⅲ', 'Ⅳ', 'Ⅴ', 'Ⅵ', 'Ⅶ', 'Ⅷ', 'Ⅸ'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={''}
          name={['buEntrustIntoNetworkInfo', 'bandWcdmaOtherOne']}
          colProps={{
            span: 1,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'bandWcdmaOther'],
                  null,
                );
              }
            },
          }}
          disabled={disabled}
          valueEnum={arr2ValueEnum(['其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'bandWcdmaOtherOne']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'bandWcdmaOther']}
                  colProps={{
                    span: 10,
                  }}
                  disabled={
                    disabled || !buEntrustIntoNetworkInfo?.bandWcdmaOtherOne?.split(',')?.includes('其他')
                  }
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormSelect
          label={'cdma2000：Band Class：'}
          name={['buEntrustIntoNetworkInfo', 'bandCdma2000']}
          colProps={{
            span: 12,
          }}
          disabled={disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          valueEnum={arr2ValueEnum(['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={''}
          name={['buEntrustIntoNetworkInfo', 'bandCdma2000OtherOne']}
          colProps={{
            span: 1,
          }}
          disabled={disabled}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'bandCdma2000Other'],
                  null,
                );
              }
            },
          }}
          valueEnum={arr2ValueEnum(['其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'bandCdma2000OtherOne']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'bandCdma2000Other']}
                  colProps={{
                    span: 10,
                  }}
                  disabled={
                    disabled ||
                    !buEntrustIntoNetworkInfo?.bandCdma2000OtherOne?.split(',')?.includes('其他')
                  }
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'TD-SCDMA：'}
          name={['buEntrustIntoNetworkInfo', 'bandTdScdma']}
          colProps={{
            span: 12,
          }}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          disabled={disabled}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['2GHz'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={''}
          name={['buEntrustIntoNetworkInfo', 'bandTdScdmaOtherOne']}
          colProps={{
            span: 1,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'bandTdScdmaOther'],
                  null,
                );
              }
            },
          }}
          disabled={disabled}
          valueEnum={arr2ValueEnum(['其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'bandTdScdmaOtherOne']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'bandTdScdmaOther']}
                  colProps={{
                    span: 10,
                  }}
                  disabled={
                    disabled ||
                    !buEntrustIntoNetworkInfo?.bandTdScdmaOtherOne?.split(',')?.includes('其他')
                  }
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'LTE FDD：'}
          name={['buEntrustIntoNetworkInfo', 'bandLtefdd']}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          disabled={disabled}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['Band 1', 'Band 3', 'Band 5', 'Band 8'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'TD-LTE：'}
          name={['buEntrustIntoNetworkInfo', 'bandTdLte']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            'Band 38',
            'Band 39',
            'Band 40',
            'Band 41',
            '2555-2575 MHz',
            '2575-2635 MHz',
            '2575-2635 MHz',
            '2635-2655 MHz',
            '2500-2690 MHz',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'NR NSA：'}
          name={['buEntrustIntoNetworkInfo', 'bandNrNsa']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['Band 41', 'Band 78', 'Band 79'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'NR SA：'}
          name={['buEntrustIntoNetworkInfo', 'bandNrSa']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            'Band 41',
            'Band 78',
            'Band 79',
            'Band 28',
            'N8',
            'N5',
            'N1',
            '2110-2130MHz',
            '2130-2155MHz',
            '2110-2155MHz',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'SIM/UIM卡槽和待机方式：'}
          name={['buEntrustIntoNetworkInfo', 'simUimSlotStandbyMode']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 8,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            '单卡',
            '双卡单待机',
            '双卡双待机',
            '支持独立eUICC芯片的eSIM',
            '其他',
          ])}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'simUimCardSlotStandbyModeOther'],
                  null,
                );
              }
            },
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'simUimSlotStandbyMode']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'simUimCardSlotStandbyModeOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.simUimSlotStandbyMode?.split(',')?.includes('其他')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'eSIM支持的电信运营商：'}
          name={['buEntrustIntoNetworkInfo', 'esimSupportedCarriers']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['中国移动', '中国联通', '中国电信'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDigit
          label={'本设备预置的eSIM证书共：'}
          name={['buEntrustIntoNetworkInfo', 'preInstalledEsimCertificates']}
          colProps={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 16,
          }}
          wrapperCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
          fieldProps={{
            addonAfter: '张',
          }}
        />
        <ProFormCheckbox
          label={'，证书机构分别为：'}
          name={['buEntrustIntoNetworkInfo', 'preInstalledEsimCertifyingAuthority']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 6,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其它')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'esimCertificatePreinstalledDeviceOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['中国移动', '中国联通', '中国电信', '中国信通院', '其它'])}
          // // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency
          name={[['buEntrustIntoNetworkInfo', 'preInstalledEsimCertifyingAuthority']]}
        >
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'esimCertificatePreinstalledDeviceOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.preInstalledEsimCertifyingAuthority?.split(',')?.includes('其它')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        {/* 一起的 */}
        <ProFormText
          label={'支持eSIM： 本设备预置的eSIM证书中，用于产品在境内支持eSIM技术应用的证书分别为：'}
          name={''}
          colProps={{
            span: 20,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 13,
          }}
          wrapperCol={{ span: 0 }}
        />
        <ProFormCheckbox
          label={' '}
          name={['buEntrustIntoNetworkInfo', 'supportedEsimAuthorities']}
          // mode={'multiple'}
          colProps={{
            span: 14,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 7,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其它')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'supportEsimOther'],
                  '',
                );
              }
            },
          }}
          valueEnum={arr2ValueEnum([
            '中国移动商用证书',
            '中国联通商用证书',
            '中国电信商用证书',
            '中国信通院商用证书',
            '其它',
          ])}
          // // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'supportedEsimAuthorities']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'supportEsimOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.supportedEsimAuthorities?.split(',')?.includes('其它')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <Col
          span={16}
          style={{
            marginLeft: '17%',
            marginBottom: 20,
          }}
        >
          我公司承诺，在中国境内支持eSIM技术应用的证书应为境内证书机构发放的具有通用性的证书，不得违反电信管理相关要求在境内提供境外运营商卡文件的激活和开通功能。
          我公司严格遵守码号管理相关要求，仅使用13位物联网码号开展物联网领域eSIM技术应用服务，使用eSIM技术时仅支持数据业务和相关定向语音、定向短信业务
        </Col>
        {/* 一起的 */}
        <ProFormCheckbox
          label={'核心芯片组数量：'}
          name={['buEntrustIntoNetworkInfo', 'coreChipsetQuantity']}
          colProps={{
            span: 10,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 9,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['单芯片组', '双芯片组'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />

        <ProFormRadio.Group
          label={'双芯片组是否支持同时发射：'}
          name={['buEntrustIntoNetworkInfo', 'dualChipsetSimultaneousTransmission']}
          colProps={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否双通：'}
          name={['buEntrustIntoNetworkInfo', 'dualTransmissionSupported']}
          colProps={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum(['单通', '双通'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'核心芯片（芯片组）供应商：'}
          name={['buEntrustIntoNetworkInfo', 'esimCoreChipSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'核心芯片（芯片组）型号：'}
          name={['buEntrustIntoNetworkInfo', 'esimCoreChipModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'基带芯片供应商：'}
          name={['buEntrustIntoNetworkInfo', 'esimBasebandChipSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'基带芯片型号：'}
          name={['buEntrustIntoNetworkInfo', 'esimBasebandChipModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'射频芯片供应商：'}
          name={['buEntrustIntoNetworkInfo', 'esimRfChipSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'射频芯片型号：'}
          name={['buEntrustIntoNetworkInfo', 'esimRfChipModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'软件版本：'}
          name={['buEntrustIntoNetworkInfo', 'softwareVersion']}
          colProps={{
            span: 8,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 12,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'查询指令：'}
          name={['buEntrustIntoNetworkInfo', 'softwareQueryCommand']}
          colProps={{
            span: 7,
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'软件开发商：'}
          name={['buEntrustIntoNetworkInfo', 'softwareDeveloper']}
          colProps={{
            span: 7,
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'硬件版本：'}
          name={['buEntrustIntoNetworkInfo', 'hardwareVersion']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'查询指令：'}
          name={['buEntrustIntoNetworkInfo', 'hardwareQueryCommand']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'终端款式：'}
          name={['buEntrustIntoNetworkInfo', 'terminalStyle']}
          colProps={{
            span: 10,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 10,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其它')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'terminalStyleOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['直板', '翻盖', '推拉', '折叠', '旋转', '其它'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'terminalStyle']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'terminalStyleOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) || !buEntrustIntoNetworkInfo?.terminalStyle?.split(',')?.includes('其它')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'天线：'}
          name={['buEntrustIntoNetworkInfo', 'antenna']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['外置', '内置', '拉杆天线', '该设备为LTE单天线终端设备'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'预留位芯片位置：'}
          name={['buEntrustIntoNetworkInfo', 'reservedChipPosition']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '没有') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'reservedChipModel'],
                  null,
                );
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'reservedChipFunction'],
                  null,
                );
              }
            },
          }}
          valueEnum={arr2ValueEnum(['有', '没有'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'reservedChipPosition']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={'预留位芯片型号：'}
                  name={['buEntrustIntoNetworkInfo', 'reservedChipModel']}
                  colProps={{
                    span: 24,
                  }}
                  labelCol={{
                    span: 4,
                  }}
                  wrapperCol={{
                    span: 8,
                  }}
                  disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.reservedChipPosition !== '有'}
                  // formItemProps={{ rules: [{ required: true }] }}
                />
                <ProFormText
                  label={'预留位芯片功能：'}
                  name={['buEntrustIntoNetworkInfo', 'reservedChipFunction']}
                  colProps={{
                    span: 24,
                  }}
                  labelCol={{
                    span: 4,
                  }}
                  wrapperCol={{
                    span: 8,
                  }}
                  disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.reservedChipPosition !== '有'}
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'CDMA版本：'}
          name={['buEntrustIntoNetworkInfo', 'cdmaVersion']}
          colProps={{
            span: 8,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 12,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'cdmaVersionOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['Rel.0', 'Rel.A', 'Rel.B', '其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'cdmaVersion']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'cdmaVersionOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) || !buEntrustIntoNetworkInfo?.cdmaVersion?.split(',')?.includes('其他')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <Col span={8}></Col>
        <ProFormCheckbox
          label={'cdma2000版本：'}
          name={['buEntrustIntoNetworkInfo', 'cdma2000Version']}
          colProps={{
            span: 8,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 12,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'cdma2000VersionOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['Rev.0', 'Rev.A', '其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'cdma2000Version']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'cdma2000VersionOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) || !buEntrustIntoNetworkInfo?.cdma2000Version?.split(',')?.includes('其他')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'WCDMA版本：'}
          name={['buEntrustIntoNetworkInfo', 'wcdmaVersion']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['R99', 'R5', 'R6', 'R7', 'R8', 'R9', 'R10', 'R11', 'R12'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'TD-SCDMA版本：'}
          name={['buEntrustIntoNetworkInfo', 'tdScdmaVersion']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['R4', 'R5', 'R6', 'R7', 'R8', 'R9'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'LTE FDD版本：'}
          name={['buEntrustIntoNetworkInfo', 'ltefddVersion']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            'R8',
            'R9',
            'R10',
            'R11',
            'R12',
            'R13',
            'R14',
            'R15',
            'R15及以上',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'TD-LTE版本：'}
          name={['buEntrustIntoNetworkInfo', 'tdLteVersion']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            'R8',
            'R9',
            'R10',
            'R11',
            'R12',
            'R13',
            'R14',
            'R15',
            'R15及以上',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'NR NSA版本：'}
          name={['buEntrustIntoNetworkInfo', 'nrNsaVersion']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['R15', 'R16', 'R17'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'NR SA版本：'}
          name={['buEntrustIntoNetworkInfo', 'nrSaVersion']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 8,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['R15', 'R16', 'R17'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否默认开启SA模式：'}
          name={['buEntrustIntoNetworkInfo', 'saModeDefaultEnabled']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum(['是', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={' '}
          name={['buEntrustIntoNetworkInfo', 'nrElb']}
          // mode={'multiple'}
          colProps={{
            span: 8,
          }}
          labelCol={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum(['该设备支持5G-增强移动宽带(eMBB)技术'])}
          // // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={' '}
          name={['buEntrustIntoNetworkInfo', 'nrRedcap']}
          // mode={'multiple'}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 0,
          }}
          valueEnum={arr2ValueEnum(['该设备支持5G-轻量化(RedCap)技术'])}
          // // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否支持国内5G异网漫游：核心网漫游：'}
          name={['buEntrustIntoNetworkInfo', 'domestic5gNetworkRoamingSupported']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 12,
          }}
          labelCol={{
            span: 11,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'核心网漫游频段：'}
          name={['buEntrustIntoNetworkInfo', 'coreNetworkRoamingBand']}
          colProps={{
            span: 4,
          }}
          disabled={isVerify ? !isVerify : disabled}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'coreNetworkRoamingBandOther'],
                  '',
                );
              }
            },
          }}
          // mode="multiple"
          valueEnum={arr2ValueEnum(['N28', '其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'coreNetworkRoamingBand']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'coreNetworkRoamingBandOther']}
                  colProps={{
                    span: 4,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.coreNetworkRoamingBand?.split(',')?.includes('其他')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'是否智能终端：'}
          name={['buEntrustIntoNetworkInfo', 'smartTerminal']}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum(['是 (需填写《智能终端软件配置信息表》)', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'smartTerminal']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormCheckbox
                label={'操作系统：'}
                name={['buEntrustIntoNetworkInfo', 'operatingSystem']}
                colProps={{
                  span: 24,
                }}
                labelCol={{
                  span: 4,
                }}
                disabled={isVerify ? !isVerify : disabled}
                valueEnum={arr2ValueEnum([
                  'Windows',
                  'Symbian',
                  'OMS',
                  'android',
                  'iOS',
                  'BB OS',
                  'X software platform',
                  'Tizen',
                  '960 OS',
                  'Yun OS',
                  'AliOS',
                  'WatchOS',
                  'KaiOS',
                  'HarmonyOS',
                  'XTCWear',
                  'Ubuntu',
                ])}
                formItemProps={{
                  rules: [
                    {
                      required:
                        buEntrustIntoNetworkInfo?.smartTerminal ===
                        '是 (需填写《智能终端软件配置信息表》)',
                      message: '必填项',
                    },
                  ],
                }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormText
          label={'版本号：'}
          name={['buEntrustIntoNetworkInfo', 'versionNumber']}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'商用密码：'}
          name={['buEntrustIntoNetworkInfo', 'commercialEncryption']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 8,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'commercialPasswordOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['AES', 'SNOW3G', 'ZUC', 'SM2', 'SM3', 'SM4', 'SM9', '其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'commercialEncryption']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'commercialPasswordOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.commercialEncryption?.split(',')?.includes('其他')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>
      </Col>
    </>
  );
};
export default BaseTechForm;
