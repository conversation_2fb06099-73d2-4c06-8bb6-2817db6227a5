import NbltTechForm from './nbltTech';
import WxTechForm from './wxTech';
import BaseTechForm from './baseTech';
import BaseBusinessTechForm from './baseBusinessTech';
import ExtendTechForm from './extendTech';
import FittingsTechForm from './fittingsTech';
import { ProForm, ProFormInstance, ProFormItem } from '@ant-design/pro-components';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  isVerify?: boolean;
};

const EnterForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, formRefs, isVerify } = props;
  return (
    <>
    {/* 进网-NB-IoT终端技术规格 */}
    <NbltTechForm  disabled={disabled} isVerify={isVerify}/>
    {/* 进网-卫星移动终端技术规格 */}
    <WxTechForm formRefs={formRefs} disabled={disabled}/>
    {/* 进网-技术规格 */}
    <BaseTechForm formRefs={formRefs} disabled={disabled} isVerify={isVerify}/>
    {/* 进网-基本业务和功能 */}
    <BaseBusinessTechForm formRefs={formRefs} disabled={disabled} isVerify={isVerify}/>
     {/* 进网-扩展功能 */}
     <ExtendTechForm formRefs={formRefs} disabled={disabled} isVerify={isVerify}/>
      {/* 进网-配件 */}
      <FittingsTechForm disabled={disabled} isVerify={isVerify}/>
    </>
  );
};
export default EnterForm;
