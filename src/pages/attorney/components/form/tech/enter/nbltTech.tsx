import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {  ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { Col } from 'antd';
import ProFormCheckbox from '../ProFormCheckbox';
import style from './index.less';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  isVerify?: boolean;
};

const NbltTechForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, isVerify } = props;
  return (
    <>
      <Title title="NB-IoT终端技术规格" />
      <Col span="24" className={style['enter-group']}>
        <ProFormCheckbox
          label={'NB-IoT版本：'}
          disabled={disabled}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          name={['buEntrustIntoNetworkInfo', 'nbIotVersion']}
          valueEnum={arr2ValueEnum(['R13', 'R14'])}
        />
        <ProFormCheckbox
          label={'NB-IoT频段：'}
          name={['buEntrustIntoNetworkInfo', 'nbIotBand']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['Band3', 'Band5', 'Band8'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'NB-IoT传输能力等级：'}
          name={['buEntrustIntoNetworkInfo', 'nbIotTransmissionAbility']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['NB1', 'NB2'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'物联网模组供应商：'}
          name={['buEntrustIntoNetworkInfo', 'iotModuleSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'物联网模组型号：'}
          name={['buEntrustIntoNetworkInfo', 'iotModuleModel']}
          colProps={{
            span: 12,
          }}
          wrapperCol={{
            span: 14,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />

        <ProFormText
          label={'核心芯片（芯片组）供应商：'}
          name={['buEntrustIntoNetworkInfo', 'coreChipSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'核心芯片（芯片组）型号：'}
          name={['buEntrustIntoNetworkInfo', 'coreChipModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'基带芯片供应商：'}
          name={['buEntrustIntoNetworkInfo', 'basebandChipSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'基带芯片型号：'}
          name={['buEntrustIntoNetworkInfo', 'basebandChipModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'射频芯片供应商：'}
          name={['buEntrustIntoNetworkInfo', 'rfChipSupplier']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'射频芯片型号：'}
          name={['buEntrustIntoNetworkInfo', 'rfChipModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 6,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'NB-IoT业务：'}
          name={['buEntrustIntoNetworkInfo', 'nbIotBusiness']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['短消息业务', '数据业务'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否已承载业务应用：'}
          colProps={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 16,
          }}
          name={['buEntrustIntoNetworkInfo', 'businessApplicationSupported']}
          valueEnum={arr2ValueEnum(['是', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否承载可安装的业务应用程序：'}
          colProps={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          name={['buEntrustIntoNetworkInfo', 'installableBusinessApplicationSupported']}
          valueEnum={arr2ValueEnum(['是', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否传输用户信息：'}
          colProps={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          name={['buEntrustIntoNetworkInfo', 'userInformationTransmissionSupported']}
          valueEnum={arr2ValueEnum(['是', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否支持其他蜂窝功能：'}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          name={['buEntrustIntoNetworkInfo', 'otherCellularFunctionSupported']}
          valueEnum={arr2ValueEnum(['支持其他蜂窝通信功能', '不支持其他蜂窝通信功能'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <Col
          span={16}
          style={{
            marginLeft: '17%',
            marginBottom: 20,
            color: 'red',
            marginTop: '-25px',
          }}
        >
          注：若不支持其他蜂窝功能，以下内容均不需填写
        </Col>
      </Col>
    </>
  );
};
export default NbltTechForm;
