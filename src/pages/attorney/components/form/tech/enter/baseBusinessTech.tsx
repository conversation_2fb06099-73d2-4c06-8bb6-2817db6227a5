// import ProFormCheckbox.Group from '@/components/ProFormCheckbox.Group';
import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { Col } from 'antd';
import style from './index.less';
import ProFormCheckbox from '../ProFormCheckbox';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  isVerify?: boolean;
};

const BaseBusinessTechForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, formRefs, isVerify } = props;
  return (
    <>
      <Title title="基本业务和功能" />
      <Col span="24" className={style['enter-group']}>
        <ProFormRadio.Group
          label={'GPRS功能：'}
          name={['buEntrustIntoNetworkInfo', 'gprsFunction']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 11,
          }}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '不支持') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'class0'],
                  null,
                );
              }
            },
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          // // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'gprsFunction']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormText
                label={'CLASS：'}
                name={['buEntrustIntoNetworkInfo', 'class0']}
                colProps={{
                  span: 15,
                }}
                disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.gprsFunction !== '支持'}
                labelCol={{
                  span: 5,
                }}
                wrapperCol={{
                  span: 12,
                }}
                // // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'HSDPA： WCDMA：'}
          name={['buEntrustIntoNetworkInfo', 'hsdpaWcdmaSupported']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 14,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '不支持') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'hsdpaWcdmaAccessCategory'],
                  null,
                );
              }
            },
          }}
          // // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'hsdpaWcdmaSupported']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormText
                label={'HSDPA接入类别：'}
                name={['buEntrustIntoNetworkInfo', 'hsdpaWcdmaAccessCategory']}
                colProps={{
                  span: 15,
                }}
                style={{
                  position: 'relative',
                  left: '-20px',
                }}
                disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.hsdpaWcdmaSupported !== '支持'}
                labelCol={{
                  span: 5,
                }}
                wrapperCol={{
                  span: 12,
                }}
                // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'TD-SCDMA：'}
          name={['buEntrustIntoNetworkInfo', 'hsdpaTdscdmaSupported']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 14,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '不支持') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'hsdpaTdscdmaAccessCategory'],
                  null,
                );
              }
            },
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'hsdpaTdscdmaSupported']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormText
                label={'HSDPA接入类别：'}
                name={['buEntrustIntoNetworkInfo', 'hsdpaTdscdmaAccessCategory']}
                colProps={{
                  span: 15,
                }}
                disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.hsdpaTdscdmaSupported !== '支持'}
                labelCol={{
                  span: 5,
                }}
                wrapperCol={{
                  span: 12,
                }}
                // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'HSUPA：WCDMA：'}
          name={['buEntrustIntoNetworkInfo', 'hsupaWcdmaSupported']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 14,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '不支持') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'hsupaWcdmaAccessCategory'],
                  null,
                );
              }
            },
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={['buEntrustIntoNetworkInfo', 'hsupaWcdmaSupported']}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormText
                label={'HSUPA接入类别：'}
                name={['buEntrustIntoNetworkInfo', 'hsupaWcdmaAccessCategory']}
                colProps={{
                  span: 15,
                }}
                disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.hsupaWcdmaSupported !== '支持'}
                labelCol={{
                  span: 5,
                }}
                wrapperCol={{
                  span: 12,
                }}
                // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'TD-SCDMA：'}
          name={['buEntrustIntoNetworkInfo', 'hsupaTdscdmaSupported']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 14,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        {/* <ProFormDependency name={['buEntrustIntoNetworkInfo', 'hsupaTdscdmaSupported']}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormText
                label={'TDSCDMA接入类别：'}
                name={['buEntrustIntoNetworkInfo', 'hsupaTdscdmaNoSupportedIllustrate']}
                colProps={{
                  span: 15,
                }}
                disabled={buEntrustIntoNetworkInfo?.hsupaTdscdmaSupported !== '支持'}
                labelCol={{
                  span: 5,
                }}
                wrapperCol={{
                  span: 12,
                }}
                // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency> */}

        <ProFormCheckbox
          label={'LTE传输能力：'}
          name={['buEntrustIntoNetworkInfo', 'lteTransmissionAbility']}
          // mode={'multiple'}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum([
            'Cat1',
            'Cat3',
            'Cat4',
            'Cat5',
            'Cat6',
            'Cat7',
            'Cat8',
            'Cat9',
            'Cat10',
            'Cat11',
            'Cat12',
            'Cat13',
            'Cat14',
            'Cat15',
            'Cat16',
            'Cat17',
            'Cat18',
            'Cat19',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'多天线传输模式：'}
          name={['buEntrustIntoNetworkInfo', 'multiAntennaTransmissionMode']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['是否支持TM5', '是否支持TM8双流beanforming'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'LTE终端语音模式：'}
          name={['buEntrustIntoNetworkInfo', 'lteTerminalVoiceMode']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            '是否支持语音回落到GSM',
            '是否支持语音回落到TD-SCDMA',
            '是否支持语音回落到WCDMA',
            '是否支持VoLTE',
            '是否支持VoNR',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'CDMA1X是否支持补充信道：'}
          name={['buEntrustIntoNetworkInfo', 'cdma1xSupplementaryChannelSupported']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          valueEnum={arr2ValueEnum(['是', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'CDMA数据编码方式：'}
          name={['buEntrustIntoNetworkInfo', 'cdmaDataEncoding']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['卷积码', 'Turbo码'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'短信：'}
          name={['buEntrustIntoNetworkInfo', 'sms']}
          colProps={{
            span: 18,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 5,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'smsOther'],
                  '',
                );
              }
            },
          }}
          // formItemProps={{ rules: [{ required: true }] }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            '普通文字短信',
            'MMS多媒体短信',
            'EMS增强型短信',
            '中国移动（5G信息）',
            '中国联通（5G信息）',
            '中国电信（5G信息）',
            '其他',
          ])}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'sms']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'smsOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={(isVerify ? !isVerify : disabled) || !buEntrustIntoNetworkInfo?.sms?.split(',')?.includes('其他')}
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        {/* <ProFormText
        label={'5G消息'}
        name={'fivegMessage'}
        mode={'multiple'}
        valueEnum={arr2ValueEnum([
          '普通文字短信',
          'MS多媒体短信',
          'EMS增强型短信',
          '中国移动（5G信息）',
          '中国联通（5G信息）',
          '中国电信（5G信息）',
          '其他',
        ])}
        // formItemProps={{ rules: [{ required: true }] }}
      /> */}
        <ProFormCheckbox
          label={'支持的补充业务：'}
          name={['buEntrustIntoNetworkInfo', 'supportedSupplementaryServices']}
          disabled={isVerify ? !isVerify : disabled}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['号码识别', '呼叫转移', '呼叫等待', '呼叫保持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'支持的增值业务：WCDMA及TD-SCDMA手机：'}
          name={['buEntrustIntoNetworkInfo', 'supportedValueAddedServices']}
          colProps={{
            span: 16,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 10,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={' '}
          disabled={isVerify ? !isVerify : disabled}
          name={['buEntrustIntoNetworkInfo', 'videoPhone']}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['可视电话'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'TD-SCDMA：'}
          name={['buEntrustIntoNetworkInfo', 'supportedValueAddedServicesTdscdma']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 7,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['WAP', '流媒体'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />

        <ProFormCheckbox
          label={'紧急呼叫：'}
          name={['buEntrustIntoNetworkInfo', 'emergencyCall']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 8,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'emergencyCallOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['110', '119', '112', '120', '其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'emergencyCall']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={' '}
                  fieldProps={{
                    style: {
                      left: '-38%',
                      position: 'relative',
                    },
                  }}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) || !buEntrustIntoNetworkInfo?.emergencyCall?.split(',')?.includes('其他')
                  }
                  name={['buEntrustIntoNetworkInfo', 'emergencyCallOther']}

                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'IP协议：'}
          name={['buEntrustIntoNetworkInfo', 'ipProtocol']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['IPv4/IPv6', 'IPv6', 'IPv4', '不支持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
      </Col>
    </>
  );
};
export default BaseBusinessTechForm;
