import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { Col } from 'antd';
import style from './index.less';
import ProFormCheckbox from '../ProFormCheckbox';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
};

const WxTechForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, formRefs } = props;
  return (
    <>
      <Title title="卫星移动终端技术规格" />
      <Col span="24" className={style['enter-group']}>
        <ProFormCheckbox
          label={'卫星频段：'}
          name={['buEntrustIntoNetworkInfo', 'satelliteBand']}
          colProps={{
            span: 12,
          }}
          disabled={disabled}
          labelCol={{
            span: 8,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'satelliteFrequencyBandOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            'S波段(下行2170MHz-2200MHz上行1980-2010MHz)',
            'L波段',
            'C波段',
            '其他',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'satelliteBand']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'satelliteFrequencyBandOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    disabled || !buEntrustIntoNetworkInfo?.satelliteBand?.split(',')?.includes('其他')
                  }
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'卫星业务：'}
          name={['buEntrustIntoNetworkInfo', 'satelliteBusiness']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['短信业务', '语音业务', '数据业务'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'卫星移动终端类别：'}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          name={['buEntrustIntoNetworkInfo', 'satelliteMobileTerminalCategory']}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['手持终端', '非手持终端'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'卫星通信制式：'}
          name={['buEntrustIntoNetworkInfo', 'satelliteCommunicationSystem']}
          colProps={{
            span: 9,
          }}
          disabled={disabled}
          labelCol={{
            span: 11,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'satelliteCommunicationStandardOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['天通一号卫星', '海事卫星', '其他卫星'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'satelliteCommunicationSystem']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'satelliteCommunicationStandardOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    disabled ||
                    !buEntrustIntoNetworkInfo?.satelliteCommunicationSystem?.split(',')?.includes('其他卫星')
                  }
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'应用场景：'}
          name={['buEntrustIntoNetworkInfo', 'applicationScenario']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['行业应用型', '公众消费型'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'是否支持蜂窝功能：'}
          name={['buEntrustIntoNetworkInfo', 'cellularFunctionSupported']}
          colProps={{
            span: 24,
          }}
          disabled={disabled}
          labelCol={{
            span: 4,
          }}
          valueEnum={arr2ValueEnum(['支持蜂窝通信功能', '不支持蜂窝通信功能'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <Col
          span={16}
          style={{
            marginLeft: '17%',
            marginBottom: 20,
            color: 'red',
            marginTop: '-25px',
          }}
        >
          注：若不支持蜂窝通信功能，以下内容均不需填写
        </Col>
      </Col>
    </>
  );
};
export default WxTechForm;
