import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import { Col } from 'antd';
import style from './index.less';
import ProFormCheckbox from '../ProFormCheckbox';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  isVerify?: boolean;
};

const ExtendTechForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, formRefs, isVerify } = props;
  return (
    <>
      <Title title="扩展功能" />
      <Col span="24" className={style['enter-group']}>
        <ProFormRadio.Group
          label={'CMMB广播式手机电视：'}
          name={['buEntrustIntoNetworkInfo', 'cmmbMobileTv']}
          colProps={{
            span: 22,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'本地连接方式：'}
          name={['buEntrustIntoNetworkInfo', 'localConnectionMethods']}
          colProps={{
            span: 10,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 9,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其他')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'localConnectionMethodsOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['红外', '蓝牙', 'USB', 'RS232', 'PCMCIA', '其他'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'localConnectionMethods']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'localConnectionMethodsOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.localConnectionMethods?.split(',')?.includes('其他')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'照相/摄像功能：'}
          name={['buEntrustIntoNetworkInfo', 'cameraFunction']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 10,
          }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '不支持') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'cameraQuantity'],
                  null,
                );
              }
            },
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'cameraFunction']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormDigit
                label={'摄像头数量：'}
                name={['buEntrustIntoNetworkInfo', 'cameraQuantity']}
                colProps={{
                  span: 15,
                }}
                disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.cameraFunction !== '支持'}
                labelCol={{
                  span: 3,
                }}
                wrapperCol={{
                  span: 12,
                }}
                fieldProps={{
                  addonAfter: '个',
                }}
                // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormRadio.Group
          label={'EDGE：'}
          name={['buEntrustIntoNetworkInfo', 'edge']}
          colProps={{
            span: 9,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 10,
          }}
          fieldProps={{
            onChange(e) {
              if (e?.target?.value === '不支持') {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'edgeClass'],
                  null,
                );
              }
            },
          }}
          // formItemProps={{ rules: [{ required: true }] }}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'edge']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <ProFormText
                label={'CLASS：'}
                name={['buEntrustIntoNetworkInfo', 'edgeClass']}
                colProps={{
                  span: 15,
                }}
                disabled={(isVerify ? !isVerify : disabled) || buEntrustIntoNetworkInfo?.edge !== '支持'}
                labelCol={{
                  span: 3,
                }}
                wrapperCol={{
                  span: 12,
                }}
                // formItemProps={{ rules: [{ required: true }] }}
              />
            );
          }}
        </ProFormDependency>

        <ProFormCheckbox
          label={'卫星导航定位系统：'}
          name={['buEntrustIntoNetworkInfo', 'satelliteNavigationSystem']}
          colProps={{
            span: 15,
          }}
          labelCol={{
            span: 6,
          }}
          disabled={isVerify ? !isVerify : disabled}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其它')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'satelliteNavigationSystemOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum([
            'GPS',
            'AGPS',
            '北斗卫星导航系统',
            'GLONASS',
            '北斗优先定位',
            '北斗独立定位',
            '单北斗定位',
            '其它',
          ])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'satelliteNavigationSystem']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'satelliteNavigationSystemOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) ||
                    !buEntrustIntoNetworkInfo?.satelliteNavigationSystem?.split(',')?.includes('其它')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>

        <Col
          span={16}
          style={{
            marginLeft: '17%',
            marginBottom: 20,
            marginTop: '-20px',
          }}
        >
          <div>注: 北斗优先定位：在融合卫星信号场景下，优先用北斗卫星信号进行定位解算。</div>
          <div>
            &nbsp;&nbsp;&nbsp;&nbsp;北斗独立定位：在仅有北斗卫星信号场景下，使用北斗卫星信号进行搜星、捕获、定位解算和输出。
          </div>
          <div>
            &nbsp;&nbsp;&nbsp;&nbsp;单北斗定位：在融合卫星信号场景下，用且仅用北斗卫星信号进行搜星、捕获、定位解算和输出。
          </div>
        </Col>
        <ProFormCheckbox
          label={'其它功能：'}
          name={['buEntrustIntoNetworkInfo', 'otherFunctions']}
          colProps={{
            span: 22,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['WLAN', 'NFC', 'WI-FI 5及以下', 'WI-FI 6', 'WI-FI 7'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormCheckbox
          label={'电视功能：'}
          name={['buEntrustIntoNetworkInfo', 'tvFunction']}
          colProps={{
            span: 10,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 9,
          }}
          fieldProps={{
            onChange(value) {
              if (!value?.includes('其它')) {
                formRefs?.current?.['techForm']?.setFieldValue(
                  ['buEntrustIntoNetworkInfo', 'tvFunctionOther'],
                  '',
                );
              }
            },
          }}
          // mode={'multiple'}
          valueEnum={arr2ValueEnum(['数字信号', '卫星信号', '模拟信号', '流媒体方式', '其它'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormDependency name={[['buEntrustIntoNetworkInfo', 'tvFunction']]}>
          {({ buEntrustIntoNetworkInfo }) => {
            return (
              <>
                <ProFormText
                  label={''}
                  name={['buEntrustIntoNetworkInfo', 'tvFunctionOther']}
                  colProps={{
                    span: 6,
                  }}
                  disabled={
                    (isVerify ? !isVerify : disabled) || !buEntrustIntoNetworkInfo?.tvFunction?.split(',')?.includes('其它')
                  }
                  // formItemProps={{ rules: [{ required: true }] }}
                />
              </>
            );
          }}
        </ProFormDependency>
      </Col>
    </>
  );
};
export default ExtendTechForm;
