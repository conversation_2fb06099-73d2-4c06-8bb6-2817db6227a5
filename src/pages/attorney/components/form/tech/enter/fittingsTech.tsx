// import Title from '@/components/title';
import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import {  ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { Col } from 'antd';
import style from './index.less';
import ProFormCheckbox from '../ProFormCheckbox';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  isVerify?: boolean;
};

const FittingsTechForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, isVerify } = props;
  return (
    <>
      <Title title="配件" />
      <Col span="24" className={style['enter-group']}>
        <ProFormRadio.Group
          label={'是否支持配件：'}
          name={['buEntrustIntoNetworkInfo', 'supportAccessoriesInfo']}
          colProps={{
            span: 24,
          }}
          labelCol={{
            span: 4,
          }}
          disabled={isVerify ? !isVerify : disabled}
          valueEnum={arr2ValueEnum(['支持', '不支持'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'电池类型：'}
          name={['buEntrustIntoNetworkInfo', 'batteryType']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'电池型号：'}
          name={['buEntrustIntoNetworkInfo', 'batteryModel']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'电池额定容量：'}
          name={['buEntrustIntoNetworkInfo', 'batteryRatedCapacity']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'电池标称电压：'}
          name={['buEntrustIntoNetworkInfo', 'batteryNominalVoltage']}
          colProps={{
            span: 12,
          }}
          disabled={isVerify ? !isVerify : disabled}
          wrapperCol={{
            span: 14,
          }}
          labelCol={{
            span: 8,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'电池生产厂家：'}
          name={['buEntrustIntoNetworkInfo', 'batteryManufacturer']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'电池制造商：'}
          name={['buEntrustIntoNetworkInfo', 'batteryProducer']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'其他配件：'}
          name={['buEntrustIntoNetworkInfo', 'otherAccessories']}
          colProps={{
            span: 24,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 4,
          }}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormRadio.Group
          label={'电源适配器/充电器：适配器是否标配：'}
          name={['buEntrustIntoNetworkInfo', 'adapterIncluded']}
          colProps={{
            span: 10,
          }}
          disabled={isVerify ? !isVerify : disabled}
          labelCol={{
            span: 14,
          }}
          valueEnum={arr2ValueEnum(['是', '否'])}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'型号：'}
          name={['buEntrustIntoNetworkInfo', 'model']}
          colProps={{
            span: 7,
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'产地：'}
          name={['buEntrustIntoNetworkInfo', 'origin']}
          colProps={{
            span: 7,
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'生产厂：'}
          name={['buEntrustIntoNetworkInfo', 'manufacturer']}
          colProps={{
            span: 24
          }}
          labelCol={{
            span: 6
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'制造商：'}
          name={['buEntrustIntoNetworkInfo', 'producer']}
          colProps={{
            span: 24
          }}
          labelCol={{
            span: 6
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'CCC证书号：'}
          name={['buEntrustIntoNetworkInfo', 'cccCertificateNumber']}
          colProps={{
            span: 24
          }}
          labelCol={{
            span: 6
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
        <ProFormText
          label={'备注：'}
          name={['buEntrustIntoNetworkInfo', 'remarks']}
          colProps={{
            span: 24
          }}
          labelCol={{
            span: 4
          }}
          disabled={isVerify ? !isVerify : disabled}
          // formItemProps={{ rules: [{ required: true }] }}
        />
      </Col>
    </>
  );
};
export default FittingsTechForm;
