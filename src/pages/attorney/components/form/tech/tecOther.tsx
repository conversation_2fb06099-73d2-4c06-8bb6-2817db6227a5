import EditTable from '@/components/editTable';
import Title from '@/components/title';
import Upload from '@/components/upload';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { UploadOutlined } from '@ant-design/icons';
import { nanoid, ProFormItem } from '@ant-design/pro-components';
import { Button, Col, Form } from 'antd';
import style from './enter/index.less';
type BaseFormProps = {
  disabled?: boolean;
  type?: ATTORNEY_TYPE;
};

const TecOtherForm: React.FC<BaseFormProps> = (props) => {
  const { disabled = false, type } = props;
  return (
    <>
      <Title title="其他信息" />
      <Col span="24"  style={{
        backgroundColor: 'rgb(239, 239, 239)',
        padding: '10px' 
      }}>
        <Col span={24}>
          <ProFormItem
            // labelCol={{ span: 1 }}
            label="技术文件"
            name="fileInfos"
            style={{ padding: '0 5px' }}
          >
            <Upload disabled={disabled} multiple>
              <Button disabled={disabled} icon={<UploadOutlined />}>
                上传
              </Button>
            </Upload>
          </ProFormItem>
        </Col>

        {(type === ATTORNEY_TYPE.enter || type === ATTORNEY_TYPE.normal) && (
          <>
            <Form.Item name={'lineloss'} wrapperCol={{span: 24}} style={{ padding: '5px' }}>
              <EditTable<{ rowId: number; smaIpexKey: string; smaIpexValue: string } | any >
                hidenEdit={disabled}
                hidenDelete={disabled}
                recordCreatorProps={disabled ? false : {
                  position: 'bottom',
                  // 每次新增的时候需要Key
                  record: () => ({ rowId: nanoid() }),
                }}
                columns={[
                  {
                    title: 'SMA转ipex线损须率',
                    dataIndex: 'smaIpexKey',
                    fieldProps: { disabled, style: { width: '100%' } },
                  },
                  {
                    title: 'SMA转pex线损须值',
                    dataIndex: 'smaIpexValue',
                    fieldProps: { disabled: disabled, style: { width: '100%' } },
                  },
                ]}
              />
            </Form.Item>
          </>
        )}
      </Col>
    </>
  );
};
export default TecOtherForm;
