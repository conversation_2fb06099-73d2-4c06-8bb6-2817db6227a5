import {
  ProFormCheckbox as AntProFormCheckbox,
  ProFormCheckboxGroupProps as AntProFormCheckboxGroupProps,
} from '@ant-design/pro-components';
import _ from 'lodash';

export type ProFormCheckboxProps = AntProFormCheckboxGroupProps & {
  prePath?: string[]
}
const ProFormCheckbox: React.FC<ProFormCheckboxProps> = ({ ...rest }) => {

 console.log(rest.name)

  return (
    <AntProFormCheckbox.Group
      {...rest}
      convertValue={(value) => {
        return typeof value === 'string' ? value?.split(',')  : value;
      }}
      transform={(value, namePath) => {
        return _.set({}, rest.name?.join('.'), Array.isArray(value)
          ? value.join(',')
          : value)
      }}
    />
  );
};

export default ProFormCheckbox;
