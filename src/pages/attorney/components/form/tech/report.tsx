import Title from '@/components/title';
import { NumBool, NumBoolObj } from '@/enums';
import { ProFormDependency, ProFormDigit, ProFormRadio } from '@ant-design/pro-components';
import { Col } from 'antd';
type BaseFormProps = {
  disabled?: boolean;
  required?: boolean;
  isShow?: boolean;
  isShowTfGenerateData?: boolean;
};

const ReportForm: React.FC<BaseFormProps> = (props) => {
  const { disabled = false, required = true, isShow = true, isShowTfGenerateData } = props;
  return (
    <>
      <Title title="检测报告" />
      <Col
        span={24}
        style={{
          backgroundColor: 'rgb(239, 239, 239)',
          padding: '10px',
          display: 'flex',
          flexWrap: 'wrap',
        }}
      >
        {isShow && (
          <>
            <ProFormRadio.Group
              label={'是否生成报告'}
              // labelCol={{ span: 8 }}
              name={'tfReport'}
              fieldProps={{ disabled }}
              valueEnum={NumBoolObj}
            />

            <ProFormDependency name={['tfReport']}>
              {({ tfReport }) => {
                if (tfReport === NumBool.是) {
                  return (
                    <>
                      <ProFormDigit
                        label={'报告份数'}
                        name={'reportNumber'}
                        formItemProps={{ rules: [{ required }] }}
                        fieldProps={{ style: { width: '100%' }, addonAfter: '份', disabled }}
                      />
                    </>
                  );
                }

                return null;
              }}
            </ProFormDependency>
          </>
        )}

        {
          isShowTfGenerateData && (
            <ProFormRadio.Group
          label={'是否生成数据'}
          // labelCol={{ span: 8 }}
          name={'tfGenerateData'}
          fieldProps={{ disabled }}
          valueEnum={NumBoolObj}
        />
          )
        }

        
      </Col>
    </>
  );
};
export default ReportForm;
