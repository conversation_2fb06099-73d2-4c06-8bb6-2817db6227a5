import Action from '@/components/action';
import EditTable from '@/components/editTable';
import { DictSelect } from '@/components/select/dict';
import Text from '@/components/text';
import Title from '@/components/title';
import Upload from '@/components/upload';
import {
  entrustInfoQueryExecutionStandard,
  entrustInfoSubmitValidationRecord,
  entrustInfoUpdate,
} from '@/services/base/weituoshujichuxinxibiaojiekou';
import { arr2ValueEnum } from '@/utils';
import {
  ModalForm,
  ModalFormProps,
  nanoid,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormInstance,
  ProFormItem,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Button, Col, message, Row, Space } from 'antd';
import _ from 'lodash';
import { useRef, useState } from 'react';

export const ResultsOptions = [
  {
    label: '样品外观是否一致',
    value: 'tfAppearanceSampleConsistent',
  },
  {
    label: '样品最大输出功率是否一致',
    value: 'tfWhetherMaximumOutputSampleConsistent',
  },
  {
    label: '样品频率范围是否一致',
    value: 'tfHetherSampleFrequencyRangeConsistent',
  },
  {
    label: '样品ACLR测试结果是否符合要求',
    value: 'tfWhetherSampleAclrResultsMeetRequirement',
  },
];

type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  /**是否测试人员核准 */
  isApproval?: boolean;
  formRefs: React.MutableRefObject<Record<string, ProFormInstance | null>>;
};

const ModeForm: React.FC<BaseFormProps> = ({ isApproval, disabled, formRefs }) => {
  return (
    <>
      {/* 技术参数 */}
      <Title title="技术参数" />
     
        <ProFormItem
          name={['buEntrustApproveInfo', 'verifiRecordVOList']}
          style={{ padding: '5px' }}
        >
          <EditTable<{ rowId: string; frequency?: number[] }>
            rowKey={'rowId'}
            recordCreatorProps={{
              position: 'bottom',
              // 每次新增的时候需要Key
              record: () => ({ rowId: nanoid() }),
            }}
            formatDataSource={(dataSource) => {
              return dataSource.map((item) => {
                return {
                  ...item,
                  frequencyOne: item?.frequency?.[0],
                  frequencyTwo: item?.frequency?.[1],
                };
              });
            }}
            onChange={async (value) => {
              const standardList: string[] = [];
              const initValue =
                formRefs?.current?.['techForm']?.getFieldValue('buEntrustApproveInfo') || {};
              (value as unknown as { standard: string }[]).forEach((item) => {
                if (!standardList.includes(item.standard)) {
                  standardList.push(item.standard);
                }
              });

              if (!standardList.length) {
                // 无内容
                formRefs?.current?.['techForm']?.setFieldsValue({
                  buEntrustApproveInfo: {
                    ...initValue,
                    implementationStandard: '',
                  },
                });
              } else {
                const detectionType =
                  formRefs?.current?.['baseForm']?.getFieldValue('testType') || '';

                if (!detectionType) {
                  message.warning('请选择检测类型');
                  return;
                }

                const res = await entrustInfoQueryExecutionStandard({
                  detectionType: detectionType,
                  standardList: standardList,
                });

                formRefs?.current?.['techForm']?.setFieldsValue({
                  buEntrustApproveInfo: {
                    ...initValue,
                    implementationStandard: _.uniq(
                      (res?.data?.judgeStandard || '')?.split(';'),
                    )?.join(';'),
                    testStandard: _.uniq((res?.data?.testStandard || '')?.split(';'))?.join(';'),
                  },
                });
              }
            }}
            columns={[
              {
                title: '制式',
                dataIndex: 'standard',
                formItemProps: { rules: [{ required: true }] },
                renderFormItem: () => (
                  <DictSelect
                    dicCode="modeProductSystem"
                    showSearch
                    filterOption={(inputValue, option) =>
                      option?.value?.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                    }
                  />
                ),
              },
              {
                title: '频段(MHz)',
                dataIndex: 'frequency',
                valueType: 'digitRange',

                render(dom, entity, index, action, schema) {
                  return (
                    <span>
                      {entity?.frequency?.[0]} ~ {entity?.frequency?.[1]}
                    </span>
                  );
                },
                fieldProps: {
                  precision: 0,
                },
                formItemProps: {
                  rules: [
                    {
                      validator: (_, v: string | undefined) => {
                        if (v?.length === 2 && v?.[0] && v?.[1]) {
                          return Promise.resolve();
                        }
                        return Promise.reject('请输入正确格式数据');
                      },
                    },
                    {
                      required: true,
                    },
                  ],
                },
              },
              {
                title: '上行/下行',
                dataIndex: 'upDown',
                valueType: 'select',
                formItemProps: { rules: [{ required: true }] },
                valueEnum: arr2ValueEnum(['上行', '下行']),
              },
              {
                title: '带宽',
                formItemProps: { rules: [{ required: true }] },
                dataIndex: 'bandwidth',
              },
              {
                title: '限值要求',
                formItemProps: { rules: [{ required: true }] },
                dataIndex: 'limitRequire',
              },
              {
                title: '操作',
                valueType: 'option',
                fixed: 'right',
                width: 'auto',
                render(dom, entity, index, action, schema) {
                  return (
                    <Action>
                      <Text
                        onClick={() => {
                          action?.startEditable?.(entity?.rowId);
                        }}
                      >
                        编辑
                      </Text>
                      <Text
                        onClick={() => {
                          const initValue =
                            formRefs?.current?.['techForm']?.getFieldValue(
                              'buEntrustApproveInfo',
                            ) || {};

                          const voList = initValue?.verifiRecordVOList || [];
                          voList?.push({
                            ...entity,
                            rowId: nanoid(),
                          });

                          formRefs?.current?.['techForm']?.setFieldsValue({
                            buEntrustApproveInfo: {
                              ...initValue,
                              verifiRecordVOList: voList,
                            },
                          });
                        }}
                      >
                        复制
                      </Text>
                      <Text
                        type="danger"
                        onClick={() => {
                          const initValue =
                            formRefs?.current?.['techForm']?.getFieldValue(
                              'buEntrustApproveInfo',
                            ) || {};

                          formRefs?.current?.['techForm']?.setFieldsValue({
                            buEntrustApproveInfo: {
                              ...initValue,
                              verifiRecordVOList: (
                                (initValue?.verifiRecordVOList || []) as any[]
                              ).filter((item) => item.rowId !== entity.rowId),
                            },
                          });
                        }}
                      >
                        删除
                      </Text>
                    </Action>
                  );
                },
              },
            ]}
          />
        </ProFormItem>
      

      <Title title="核准信息" />
      {/* 项目经理填写的表单 */}
     
        <Row style={{ width: '100%' }}>
          <Col span={24}>
            <ProFormItem
              style={{ paddingLeft: 5 }}
              label="核准文件"
              rules={[{ required: true }]}
              name={['buEntrustApproveInfo', 'fileId']}
              labelCol={{
                span: 2,
              }}
            >
              <Upload maxCount={1} multiple accept=".doc,.docx" />
            </ProFormItem>
          </Col>
        </Row>

        <ProFormTextArea
          name={['buEntrustApproveInfo', 'testStandard']}
          label="标准编号"
          disabled
          placeholder={'根据制式自动生成'}
          rules={[{ required: true }]}
        />
        <ProFormTextArea
          name={['buEntrustApproveInfo', 'implementationStandard']}
          label="判定依据"
          disabled
          placeholder={'根据制式自动生成'}
          rules={[{ required: true }]}
        />

        {/* 回显补充验证记录信息 */}
        {isApproval && (
          <>
            <Title title="补充验证记录" />
           
              <ProFormDatePicker
                name={['buEntrustApproveInfo', 'verificationDate']}
                label="验证日期"
                rules={[
                  {
                    required: true,
                    message: '此项必选',
                  },
                ]}
              />
              <ProFormCheckbox.Group name="checkbox" options={ResultsOptions} />
              <ProFormTextArea name={['buEntrustApproveInfo', 'remark']} label="备注" />
           

            <Title title="验证记录" />
            
               <ProFormItem name={['buEntrustApproveInfo', 'verifiRecordVOListT']}>
              <EditTable<BASE.VerifiRecordVO>
                rowKey={'rowId'}
                recordCreatorProps={false}
                columns={[
                  {
                    title: '序号',
                    valueType: 'index',
                    editable: false,
                  },
                  {
                    title: '制式',
                    dataIndex: 'standard',
                    editable: false,
                  },
                  {
                    title: '频段(MHz)',
                    editable: false,
                    render(dom, entity, index, action, schema) {
                      return entity?.frequencyOne + '-' + entity?.frequencyTwo;
                    },
                  },
                  {
                    title: '带宽',
                    dataIndex: 'bandwidth',
                    editable: false,
                  },
                  {
                    title: '限值要求',
                    dataIndex: 'limitRequire',
                    editable: false,
                  },
                  {
                    title: '样品编号',
                    dataIndex: 'sampleNumber',
                    editable: false,
                  },
                  {
                    title: '上下行',
                    dataIndex: 'upDown',
                    editable: false,
                  },
                  {
                    title: '高频点（MHz）',
                    dataIndex: 'frequencyPointHight',
                    valueType: 'digit',
                    formItemProps(form, config) {
                      const row = config?.entity || {};
                      return {
                        rules: [
                          {
                            validator(_, value) {
                              if (
                                row?.frequencyOne &&
                                row?.frequencyTwo &&
                                value >= row?.frequencyOne &&
                                value <= row?.frequencyTwo
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                `有效值为${`${row?.frequencyOne}-${row?.frequencyTwo}` || '--'}`,
                              );
                            },
                          },
                        ],
                      };
                    },
                  },
                  {
                    title: '到达天线端口高电平值(dBm)',
                    dataIndex: 'antennaLevelHight',
                  },
                  {
                    title: '低频点（MHz）',
                    dataIndex: 'frequencyPointLow',
                    valueType: 'digit',
                    formItemProps(form, config) {
                      const row = config?.entity || {};
                      return {
                        rules: [
                          {
                            validator(_, value) {
                              if (
                                row?.frequencyOne &&
                                row?.frequencyTwo &&
                                value >= row?.frequencyOne &&
                                value <= row?.frequencyTwo
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                `有效值为${`${row?.frequencyOne}-${row?.frequencyTwo}` || '--'}`,
                              );
                            },
                          },
                        ],
                      };
                    },
                  },
                  {
                    title: '到达天线端口低电平值(dBm)',
                    dataIndex: 'antennaLevelLow',
                  },
                ]}
              />
            </ProFormItem>
           
           
          </>
        )}
      
    </>
  );
};

type SupplementProps = {
  row: BASE.EntrustInfoVO;
  onSuccess?: () => void;
  // 详情页的节点
  detailNode?: React.ReactNode;
} & ModalFormProps;

/** 提交验证记录 */
export const Supplement: React.FC<SupplementProps> = ({
  row,
  children,
  onSuccess,
  detailNode,
  ...rest
}) => {
  const formRef = useRef<ProFormInstance>(null);
  // 请求中
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const onFinish = async (data: Record<string, any>, tfSubmit?: boolean) => {
    setLoading(true);
    if (data?.verifiRecordVOListT && data?.verifiRecordVOListT?.length && tfSubmit) {
      const result = data?.verifiRecordVOListT?.every(
        (i: Record<string, any>) =>
          i?.upDown &&
          i?.frequencyPointHight &&
          i?.antennaLevelHight &&
          i?.frequencyPointLow &&
          i?.antennaLevelLow,
      );
      if (!result) {
        message.error('请补充验证记录里面的所有数据');
        setLoading(false);
        return false;
      }
    }

    if (data?.checkbox) {
      ResultsOptions.forEach((item) => {
        data[item.value] = (data?.checkbox as string[]).includes(item.value);
      });
      data.checkbox = void 0;
    }

    if (data?.list) {
      data['antennaPortLevelValueJson'] = JSON.stringify(data.list);
      data.list = void 0;
    }

    console.log(data, 'data------');

    try {
      const res = await entrustInfoSubmitValidationRecord({
        // ...row,
        id: row.id,
        status: row.status,
        buEntrustApproveInfo: {
          // ...(row?.buEntrustApproveInfo || {}),
          id: row?.buEntrustApproveInfo?.id as number,
          entrustId: row.buEntrustApproveInfo?.entrustId as number,
          ...data,
          tfSubmit: !!tfSubmit,
        },
      });

      if (res.success) {
        onSuccess?.();
        setLoading(false);
        setOpen(false);
        return true;
      }
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <ModalForm
      title="提交验证记录"
      width={1200}
      open={open}
      grid
      formRef={formRef}
      style={{
        padding: '10px',
      }}
      scrollToFirstError={true}
      modalProps={{
        onCancel(e) {
          setOpen(false);
        },
      }}
      onFinish={(values) => onFinish(values, true)}
      submitter={{
        searchConfig: {
          submitText: '提交',
        },
        render: ({ onSubmit, onReset, submit, reset }, doms) => {
          console.log(doms, 'doms');
          return (
            <Row>
              <Col span={24}>
                <Space>
                  {doms}
                  <Button
                    type="primary"
                    loading={loading}
                    onClick={async () => {
                      const values = await formRef?.current?.validateFieldsReturnFormatValue?.();
                      await onFinish(values, false);
                    }}
                  >
                    保存
                  </Button>
                </Space>
              </Col>
            </Row>
          );
        },
      }}
      trigger={<Text onClick={() => setOpen(true)}>提交验证记录</Text>}
      onOpenChange={(v) => {
        if (v && row) {
          setTimeout(() => {
            formRef.current?.setFieldsValue({
              ...row.buEntrustApproveInfo,
              handlOpinion: '满足条件，可下达测试任务',
              checkbox:
                ResultsOptions.filter(
                  // @ts-ignore
                  (item) => !!row?.buEntrustApproveInfo?.[item.value],
                ).map((v) => v.value) || [],
              list: row.buEntrustApproveInfo?.antennaPortLevelValueJson
                ? JSON.parse(row.buEntrustApproveInfo?.antennaPortLevelValueJson)
                : [],
              verifiRecordVOListT: (row.buEntrustApproveInfo?.verifiRecordVOListT || []).map(
                (i) => {
                  return {
                    ...i,
                    rowId: nanoid(),
                  };
                },
              ),
            });
          });
        }
      }}
      {...rest}
    >
      <div style={{ width: '100%' }}>
        {!!detailNode && (
          <Row>
            <Col span={24}>{detailNode}</Col>
          </Row>
        )}
      </div>
      <Title title="基本信息" />
      <ProFormDatePicker
        name={'verificationDate'}
        label="验证日期"
        colProps={{
          span: 11,
        }}
        rules={[
          {
            required: true,
            message: '此项必选',
          },
        ]}
      />
      <ProFormCheckbox.Group name="checkbox" options={ResultsOptions} />

      <Title title="验证记录" />
      <ProFormItem name={'verifiRecordVOListT'}>
        <EditTable<BASE.VerifiRecordVO>
          rowKey={'rowId'}
          recordCreatorProps={false}
          hidenDelete
          columns={[
            {
              title: '序号',
              valueType: 'index',
              width: 'auto',
              editable: false,
            },
            {
              title: '制式',
              dataIndex: 'standard',
              editable: false,
            },
            {
              title: '频段(MHz)',
              render(dom, entity, index, action, schema) {
                return (
                  <span>
                    {entity?.frequencyOne}-{entity?.frequencyTwo}
                  </span>
                );
              },
              editable: false,
            },
            {
              title: '带宽',
              dataIndex: 'bandwidth',
              editable: false,
            },
            {
              title: '限值要求',
              dataIndex: 'limitRequire',
              editable: false,
            },
            {
              title: '样品编号',
              dataIndex: 'sampleNumber',
              editable: false,
            },
            {
              title: '上下行',
              dataIndex: 'upDown',
              editable: false,
            },
            {
              title: '高频点（MHz）',
              dataIndex: 'frequencyPointHight',
              valueType: 'digit',
              formItemProps(form, config) {
                const row = config?.entity || {};
                return {
                  rules: [
                    {
                      validator(_, value) {
                        if (
                          row?.frequencyOne &&
                          row?.frequencyTwo &&
                          value >= row?.frequencyOne &&
                          value <= row?.frequencyTwo
                        ) {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          `有效值为${`${row?.frequencyOne}-${row?.frequencyTwo}` || '--'}`,
                        );
                      },
                    },
                  ],
                };
              },
            },
            {
              title: '到达天线端口高电平值(dBm)',
              dataIndex: 'antennaLevelHight',
              valueType: 'digit',
              fieldProps: {
                min: -Infinity,
                max: Infinity,
              },
            },
            {
              title: '低频点（MHz）',
              dataIndex: 'frequencyPointLow',
              valueType: 'digit',
              formItemProps(form, config) {
                const row = config?.entity || {};
                return {
                  rules: [
                    {
                      validator(_, value) {
                        if (
                          row?.frequencyOne &&
                          row?.frequencyTwo &&
                          value >= row?.frequencyOne &&
                          value <= row?.frequencyTwo
                        ) {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          `有效值为${`${row?.frequencyOne}-${row?.frequencyTwo}` || '--'}`,
                        );
                      },
                    },
                  ],
                };
              },
            },
            {
              title: '到达天线端口低电平值(dBm)',
              dataIndex: 'antennaLevelLow',
              valueType: 'digit',
              fieldProps: {
                min: -Infinity,
                max: Infinity,
              },
            },
          ]}
        />
      </ProFormItem>

      <Title title="处理意见" />
      <ProFormRadio.Group
        name={'handlOpinion'}
        options={[
          {
            label: '满足条件，可下达测试任务',
            value: '满足条件，可下达测试任务',
          },
          {
            label: '联系企业调试',
            value: '联系企业调试',
          },
        ]}
      />
      <ProFormTextArea name={'remark'} label="备注" />
    </ModalForm>
  );
};

export default ModeForm;

{
  /* 通用委托书-技术参数 */
}
export const NormalTechForm: React.FC<BaseFormProps> = ({ formRefs, disabled }) => {
  return (
    <>
      {/* 技术参数 */}
      <Title title="技术参数" />
      
        <ProFormItem name={'entrustTechnicalParametersintoInfos'} style={{ padding: '5px' }}>
          <EditTable<{ rowId: string; frequency?: number[] }>
            rowKey={'rowId'}
            recordCreatorProps={disabled ? false : {
              position: 'bottom',
              // 每次新增的时候需要Key
              record: () => ({ rowId: nanoid() }),
            }}
            // onChange={async (value) => {
            //   const standardList: string[] = [];
            //   const initValue = formRefs?.current?.['techForm']?.getFieldValue('buEntrustApproveInfo') || {};
            //   (value as unknown as { standard: string }[]).forEach((item) => {
            //     if (!standardList.includes(item.standard)) {
            //       standardList.push(item.standard);
            //     }
            //   });

            //   if (!standardList.length) {
            //     // 无内容
            //     formRefs?.current?.['techForm']?.setFieldsValue({
            //       buEntrustApproveInfo: {
            //         ...initValue,
            //         implementationStandard: '',
            //       },
            //     });
            //   } else {
            //     const detectionType = formRefs?.current?.['baseForm']?.getFieldValue('testType') || '';

            //     if (!detectionType) {
            //       message.warning('请选择检测类型');
            //       return;
            //     }

            //     const res = await entrustInfoQueryExecutionStandard({
            //       detectionType: detectionType,
            //       standardList: standardList,
            //     });

            //     formRefs?.current?.['techForm']?.setFieldsValue({
            //       buEntrustApproveInfo: {
            //         ...initValue,
            //         implementationStandard: _.uniq((res?.data?.judgeStandard || '')?.split(';'))?.join(';'),
            //         testStandard: _.uniq((res?.data?.testStandard || '')?.split(';'))?.join(';'),
            //       },
            //     });
            //   }
            // }}
            columns={[
              {
                title: '制式',
                dataIndex: 'standardInfo',
                formItemProps: { rules: [{ required: true }] },
                renderFormItem: () => (
                  <DictSelect
                    dicCode="modeProductSystem"
                    // showSearch
                    mode="tags"
                    maxCount={1}
                    filterOption={(inputValue, option) =>
                      option?.value?.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                    }
                  />
                ),
                render(dom, entity, index, action, schema) {
                  return <span>{entity?.standardInfo?.[0]}</span>;
                },
              },
              {
                title: '频段(MHz)',
                dataIndex: 'frequencyBand',
                valueType: 'digitRange',
                render(dom, entity, index, action, schema) {
                  return (
                    <span>
                      {entity?.frequencyBand?.[0]} ~ {entity?.frequencyBand?.[1]}
                    </span>
                  );
                },
                fieldProps: {
                  precision: 0,
                },
                formItemProps: {
                  rules: [
                    {
                      validator: (_, v: string | undefined) => {
                        if (v?.length === 2 && v?.[0] && v?.[1]) {
                          return Promise.resolve();
                        }
                        return Promise.reject('请输入正确格式数据');
                      },
                    },
                    {
                      required: true,
                    },
                  ],
                },
              },
              {
                title: '操作',
                valueType: 'option',
                fixed: 'right',
                width: 'auto',
                hideInTable: disabled,
                render(dom, entity, index, action, schema) {
                  return (
                    <Action>
                      <Text
                        onClick={() => {
                          action?.startEditable?.(entity?.rowId);
                        }}
                      >
                        编辑
                      </Text>
                      <Text
                        onClick={() => {
                          const initValue =
                            formRefs?.current?.['techForm']?.getFieldValue(
                              'entrustTechnicalParametersintoInfos',
                            ) || [];

                          initValue?.push({
                            ...entity,
                            rowId: nanoid(),
                          });

                          formRefs?.current?.['techForm']?.setFieldsValue({
                            entrustTechnicalParametersintoInfos: initValue,
                          });
                        }}
                      >
                        复制
                      </Text>
                      <Text
                        type="danger"
                        onClick={() => {
                          const initValue =
                            formRefs?.current?.['techForm']?.getFieldValue(
                              'entrustTechnicalParametersintoInfos',
                            ) || [];

                          formRefs?.current?.['techForm']?.setFieldsValue({
                            entrustTechnicalParametersintoInfos: (
                              (initValue || []) as any[]
                            ).filter((item) => item.rowId !== entity.rowId),
                          });
                        }}
                      >
                        删除
                      </Text>
                    </Action>
                  );
                },
              },
            ]}
          />
        </ProFormItem>
      
    </>
  );
};
