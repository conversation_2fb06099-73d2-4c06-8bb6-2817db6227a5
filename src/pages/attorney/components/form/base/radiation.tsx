import Title from '@/components/title';
import { ProFormDigit, ProFormText } from '@ant-design/pro-components';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
};

const RadiationForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false } = props;
  return (
    <>
      <Title title="电磁辐射信息" />
      <Title
        title={<span style={{ fontSize: '13px' }}>移动通信基站项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <ProFormText
        label={'基站名称'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'baseStationName']}
      />
      <ProFormText
        label={'基站地址'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'baseStationAddress']}
      />

      <ProFormText
        label={'运营单位'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'operator']}
      />

      <ProFormText
        label={'基站编号'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'baseStationNumber']}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>WGS84</span>}
        style={{ marginLeft: '15px' }}
      />
      <ProFormText
        label={'东经'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'wgEastLongitude']}
        fieldProps={{ style: { width: '100%' } }}
      />

      <ProFormText
        label={'北纬'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'wgNorthLatitude']}
        fieldProps={{ style: { width: '100%' } }}
      />

      <ProFormDigit
        label={'海拔'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'wgAltitude']}
        fieldProps={{ style: { width: '100%' }, addonAfter: 'm' }}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>输变线项目</span>}
        style={{ marginLeft: '5px' }}
      />

      <ProFormText
        label={'项目名称'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'transformationLineProjectName']}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>变电站项目</span>}
        style={{ marginLeft: '5px' }}
      />

      <ProFormText
        label={'项目名称'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'transformerSubstationProjectName']}
      />

      <Title title={<span style={{ fontSize: '13px' }}>其他</span>} style={{ marginLeft: '5px' }} />
      <ProFormText
        label={'其他'}
        disabled={disabled}
        name={['buEntrustElectroRadiaInfo', 'otherProjectName']}
      />
    </>
  );
};
export default RadiationForm;
