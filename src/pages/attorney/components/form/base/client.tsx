import Title from '@/components/title';
import { emailValidator } from '@/config/rule';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { ProFormInstance, ProFormText, ProFormTextArea } from '@ant-design/pro-components';

type BaseFormProps = {
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  disabled?: boolean;
  required?: boolean;
  type?: ATTORNEY_TYPE;
  extraColumns?: React.ReactNode;
};

const ClentForm: React.FC<BaseFormProps> = (props) => {
  const { disabled = false, required = true, type, extraColumns, formRefs } = props;

  return (
    <>
      <Title title="委托方信息" />
      <ProFormText
        label={'单位名称'}
        name={'entrustEntName'}
        formItemProps={{ rules: [{ required }] }}
        fieldProps={{
          disabled,
          onChange: (e) => {
            formRefs?.current?.['payForm']?.setFieldValue('billAcceptUnit', e.target.value);
          },
        }}
      />

      <ProFormText
        label={'联系人'}
        name={'entrustContact'}
        fieldProps={{ disabled }}
        formItemProps={{ rules: [{ required }] }}
      />

      <ProFormText
        label={'手机号'}
        name={'entrustPhone'}
        fieldProps={{ disabled }}
        formItemProps={{ rules: [{ required: type === ATTORNEY_TYPE.model }] }}
      />
      <ProFormText
        label={'邮箱'}
        name={'entrustEmail'}
        fieldProps={{ disabled }}
        formItemProps={{
          rules: [{ required: type === ATTORNEY_TYPE.model }, { validator: emailValidator }],
        }}
      />

      <ProFormText
        label={'单位地址'}
        name={'companyAddress'}
        fieldProps={{ disabled }}
        formItemProps={{ rules: [{ required }] }}
      />
      <ProFormText
        label={'客户代码'}
        name={'customerCode'}
        fieldProps={{ disabled }}
      />
      <ProFormText
        label={'项目代码'}
        name={'projectCode'}
        fieldProps={{ disabled }}
      />

      {type === ATTORNEY_TYPE.model && <>
        <ProFormText
          name={['buEntrustApproveInfo', 'postalCode']}
          label="邮政编码"
          rules={[{ required: true }]}
        />
        <ProFormTextArea
          name={['buEntrustApproveInfo', 'mainFunction']}
          label="主要功能"
          rules={[{ required: true }]}
        />
      </>}

      {extraColumns}
    </>
  );
};
export default ClentForm;
