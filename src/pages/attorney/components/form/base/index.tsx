import ProFormSelect from '@/components/proFormSelect';
import Upload from '@/components/upload';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { arr2ValueEnum } from '@/utils';
import { UploadOutlined } from '@ant-design/icons';
import {
  ProFormDependency,
  ProFormInstance,
  ProFormItem,
  ProFormText,
} from '@ant-design/pro-components';
import { Button, Col } from 'antd';
import AntennaBaseForm from './antenna';
import BusinessForm from './bussiness';
import ClientForm from './client';
import EnvironmentForm from './environment';
import OtherForm from './other';
import RadiationForm from './radiation';
import SampleForm from './sample';
import SoftwareForm from './software';

export type BaseFormProps = {
  isDetail: boolean;
  initRowData: any;
  formRefs: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  dependency?: Record<string, any>;
  isEdit: boolean;
};

const BaseForm: React.FC<BaseFormProps> = ({ isDetail, initRowData, formRefs, dependency }) => {
  return (
    <>
      <BusinessForm
        disabled={isDetail}
        outRow={initRowData}
        formRefs={formRefs}
        dependency={dependency}
      />
      <ProFormDependency name={['isDraft', 'type']}>
        {({ isDraft, type }) => {
          return (
            <>
              {/* 委托方信息 */}
              <ClientForm
                required={!isDraft || type === ATTORNEY_TYPE.model}
                disabled={isDetail}
                formRefs={formRefs}
                type={type}
                extraColumns={
                  type === ATTORNEY_TYPE.enter ? (
                    <>
                      <ProFormText
                        label={'产地'}
                        name={'originAddress'}
                        fieldProps={{ disabled: isDetail }}
                        formItemProps={{ rules: [{ required: true }] }}
                      />
                      <ProFormSelect
                        name={'inspectionType'}
                        label="检验类别"
                        dicCode="jwtestType"
                        initialValue={'进网'}
                      />
                      <ProFormSelect
                        valueEnum={arr2ValueEnum([
                          '进网检测',
                          '办新证',
                          '备案',
                          '到期换证',
                          '扩容换证',
                          '委托',
                          '--'
                        ])}
                        label={'检验类别详细'}
                        name={'testTypeInfo'}
                        fieldProps={{ disabled: isDetail }}
                        rules={[
                          {
                            required: true,
                            message: '此项必选',
                          },
                        ]}
                      />
                      <Col span={6}>
                        <ProFormItem
                          label="设备简介表"
                          name="equipmentFiles"
                        >
                          <Upload multiple>
                            <Button icon={<UploadOutlined />}>上传</Button>
                          </Upload>
                        </ProFormItem>
                      </Col>
                      <Col span={6}>
                        <ProFormItem
                          label="实验室选择"
                          name="laboratoryFiles"
                          rules={[
                            {
                              required: true,
                              message: '此项必选',
                            },
                          ]}
                        >
                          <Upload multiple>
                            <Button icon={<UploadOutlined />}>上传</Button>
                          </Upload>
                        </ProFormItem>
                      </Col>
                    </>
                  ) : null
                }
              />
              {/* 样品信息 */}
              <SampleForm
                required={!isDraft}
                formRefs={formRefs}
                disabled={isDetail}
                initRowData={initRowData}
                isMode={type === ATTORNEY_TYPE.model}
              />
              {type === ATTORNEY_TYPE.antenna && (
                <AntennaBaseForm required={!isDraft} disabled={isDetail} />
              )}
              {type === ATTORNEY_TYPE.radiation && <RadiationForm required={!isDraft} />}
              {type === ATTORNEY_TYPE.environment && (
                <EnvironmentForm required={!dependency?.isDraft} />
              )}
              {type === ATTORNEY_TYPE.softtest && <SoftwareForm required={!dependency?.isDraft} />}
              {
                <OtherForm required={!isDraft} disabled={isDetail} />
              }
            </>
          );
        }}
      </ProFormDependency>
    </>
  );
};

export default BaseForm;
