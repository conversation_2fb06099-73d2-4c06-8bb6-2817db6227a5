import Title from '@/components/title';
import { ProFormDigit, ProFormText } from '@ant-design/pro-components';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
};

const EnvironmentForm: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false } = props;
  return (
    <>
      <Title title="电磁环境信息" />
      <Title
        title={<span style={{ fontSize: '13px' }}>机场项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <ProFormText
        label={'机场项目'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'airProjectName']}
      />
      <ProFormText
        label={'机场地址'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'airAddress']}
      />

      <ProFormText
        label={'东经'}
        name={['buEntrustElectroEnvInfo', 'airWgEastLongitude']}
        disabled={disabled}
      />

      <ProFormText
        label={'北纬'}
        name={['buEntrustElectroEnvInfo', 'airWgNorthLatitude']}
        disabled={disabled}
      />
      <ProFormText
        label={'海拔'}
        name={['buEntrustElectroEnvInfo', 'airWgAltitude']}
        disabled={disabled}
      />
      <Title
        title={<span style={{ fontSize: '13px' }}>台站项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <ProFormText
        label={'建设单位'}
        // colProps={{ span: 12 }}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationConstrucName']}
      />
      <ProFormText
        label={'建设单位地址'}
        // colProps={{ span: 12 }}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationConstrucAddress']}
      />

      <ProFormText
        label={'台站名称'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationName']}
      />

      <ProFormText
        label={'台站类型'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationType']}
      />

      <ProFormText
        label={'台站地址'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationAddress']}
      />

      <ProFormText
        label={'台站东经'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationEastLongitude']}
      />

      <ProFormText
        label={'台站北纬'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationNorthLatitude']}
      />

      <ProFormText
        label={'台站海拔'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationAltitude']}
      />

      <ProFormDigit
        label={'台站数量'}
        name={['buEntrustElectroEnvInfo', 'stationCount']}
        disabled={disabled}
        fieldProps={{ style: { width: '100%' } }}
      />

      <ProFormDigit
        label={'台站高度'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'stationHeight']}
        fieldProps={{ style: { width: '100%' } }}
      />

      <ProFormText
        label={'接收灵敏度'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'receivSensitivit']}
      />

      <ProFormText
        label={'天线增益'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'antennaGain']}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>输变线项目</span>}
        style={{ marginLeft: '5px' }}
      />

      <ProFormText
        label={'项目名称'}
        disabled={disabled}
        name={['buEntrustElectroEnvInfo', 'tranProjectName']}
      />
    </>
  );
};
export default EnvironmentForm;
