import { templateInfoGetTemplateDownLoadList } from '@/services/base/jichuxinximobanguanlibiaojiekou';
import { entrustInfoImportTemplate } from '@/services/base/weituoshujichuxinxibiaojiekou';
import { downloadFile } from '@/utils';
import { UploadOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, Divider, message, Modal, ModalProps, Upload } from 'antd';
import { alert_loading } from 'antd-pro-crud';

const ImportModal: React.FC<
  ModalProps & { onSuccess?: (data?: BASE.EntrustInfoVO) => void; type: string }
> = ({ onSuccess, type, ...rest }) => {
  const templeReuqest = useRequest(() => {
    return templateInfoGetTemplateDownLoadList({ type: '委托书' });
  });
  return (
    <Modal title="导入数据" footer={false} {...rest}>
      {templeReuqest?.data?.map((name) => {
        return (
          <div key={name}>
            <a
              // href={`${PROXY_KEY}test/templateInfo/downTemplate?fileName=${name}&type=委托书`}
              // download={name}
              onClick={() =>
                downloadFile(`/templateInfo/downTemplate?fileName=${name}&type=委托书`)
              }
            >
              {name}
            </a>
          </div>
        );
      })}
      <p style={{ color: 'red', marginTop: 20 }}>
        ⚠️「进网委托」模版类型为xls或者xlsx，其他模板都为doc或者docx格式
      </p>
      <Divider />
      <Upload
        showUploadList={false}
        maxCount={1}
        // 只能上传word文件
        accept=".doc,.docx,.xls,.xlsx"
        // beforeUpload={()=>false}
        customRequest={async (info) => {
          console.log(info, 'info--1-1-1-');
          const form = new FormData();
          form.append('file', info.file as any);
          form.append('uploadType', type);
          if(!type) {
            message.error('请先选择委托书类型！')
            return ;
          }
          const { close } = alert_loading({
            maskClosable: false,
          });
          entrustInfoImportTemplate(
            form as unknown as BASE.entrustInfoImportTemplateParams,
            form,
          ).then((res) => {
            if (res.success) {
              close();
              onSuccess?.(res?.data);
              message.success(`导入数据成功`);
              rest?.onOk?.({} as React.MouseEvent<HTMLButtonElement>);
            }
          }).catch((error) => {
            close();
          })
          
        }}
      >
        <Button icon={<UploadOutlined />} type="primary">
          导入模板
        </Button>
      </Upload>
    </Modal>
  );
};

export default ImportModal;
