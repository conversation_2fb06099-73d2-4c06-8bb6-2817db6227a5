import Title from '@/components/title';
import { NumBoolObj } from '@/enums';
import { arr2ValueEnum } from '@/utils';
import {
  ProFormCheckbox,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useUpdate } from 'ahooks';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
};

const AntennaBaseForm: React.FC<BaseFormProps> = (props) => {
  const { required = true, disabled = false } = props;
  
  return (
    <>
      <Title title="天线信息" />
      <ProFormRadio.Group
        label={'大尺寸天线'}
        name={['buEntrustAntennaInfo', 'tfLargeSize']}
        valueEnum={NumBoolObj}
        disabled={disabled}
      />
      <ProFormDependency name={[['buEntrustAntennaInfo', 'tfLargeSize']]}>
        {(depValues) => {
          if (depValues?.buEntrustAntennaInfo?.tfLargeSize) {
            return (
              <>
                <ProFormDigit
                  label={'长'}
                  name={['buEntrustAntennaInfo', 'buLength']}
                  fieldProps={{ style: { width: '100%' }, addonAfter: 'm' }}
                  disabled={disabled}
                />
                <ProFormDigit
                  label={'宽'}
                  name={['buEntrustAntennaInfo', 'buWideth']}
                  fieldProps={{ style: { width: '100%' }, addonAfter: 'm' }}
                  disabled={disabled}
                />
                <ProFormDigit
                  label={'高'}
                  fieldProps={{ style: { width: '100%' }, addonAfter: 'm' }}
                  name={['buEntrustAntennaInfo', 'buThick']}
                  disabled={disabled}
                />
                <ProFormDigit
                  label={'重量'}
                  name={['buEntrustAntennaInfo', 'buWeight']}
                  fieldProps={{ style: { width: '100%' }, addonAfter: 'Kg' }}
                  disabled={disabled}
                />
              </>
            );
          } else {
            return null;
          }
        }}
      </ProFormDependency>

      <ProFormRadio.Group
        label={'多频段天线'}
        name={['buEntrustAntennaInfo', 'tfMultifrequency']}
        disabled={disabled}
        valueEnum={NumBoolObj}
      />
      <ProFormDependency name={[['buEntrustAntennaInfo', 'tfMultifrequency']]}>
        {(depValues) => {
          if (depValues?.buEntrustAntennaInfo?.tfMultifrequency) {
            return (
              <ProFormSelect
                label={'中心位置方式'}
                disabled={disabled}
                name={['buEntrustAntennaInfo', 'centerPositionMode']}
                valueEnum={arr2ValueEnum(['样品上标示', '提供样品示意图'])}
              />
            );
          }
          return null;
        }}
      </ProFormDependency>

      <ProFormSelect
        label={'天线类型'}
        name={['buEntrustAntennaInfo', 'antennaType']}
        disabled={disabled}
        valueEnum={arr2ValueEnum(['室分', '基站', '美化', '其他'])}
      />
      <ProFormDigit
        label={'端口数量'}
        disabled={disabled}
        name={['buEntrustAntennaInfo', 'portNum']}
      />

    </>
  );
};
export default AntennaBaseForm;
