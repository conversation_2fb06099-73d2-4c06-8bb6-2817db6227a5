import ProFormSelect from '@/components/proFormSelect';
import Title from '@/components/title';
import { NumBool, NumBoolObj, ROLE_TYPE } from '@/enums';
import { ATTORNEY_STATUS, ATTORNEY_TYPE, ATTORNEY_TYPE_MAP } from '@/enums/attorney';
import { disabledParent } from '@/pages/attorney/edit';
import { taskTypeInfoGetTree } from '@/services/base/jichuxinxirenwuleixingdingyibiaojiekou';
import { orgUserInfoGetUserInfoByRole } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormRadio,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { useLocation, useModel } from '@umijs/max';
import { Button } from 'antd';
import { launch } from 'antd-pro-crud';
import BasicForm from './base';
import ImportModal from './import';

const alert_import = launch(ImportModal);

type BaseFormProps = {
  formRefs: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  required?: boolean;
  disabled?: boolean;
  isEdit?: boolean;
  outRow?: BASE.EntrustInfoVO;
  dependency?: Record<string, any>;
};

type UserInfo = {
  deptId: string;
  deptName: string;
  id: string;
  userId: string;
  userInfo: BASE.IUser;
};

type DeptUserTreeType = {
  label: string;
  value: string;
  children?: DeptUserTreeType[];
  /** 是否是部门 */
  disabled: boolean;
};

/** 格式化用户列表 */
const formatUserTree = (data: Record<string, UserInfo[]>) => {
  if (!data) {
    return [];
  }

  const deptUserTree: DeptUserTreeType[] = [];
  for (let item in data) {
    if (data?.[item]) {
      deptUserTree.push({
        label: item,
        value: item,
        disabled: true,
        children: (data?.[item] || []).map((itex) => {
          return {
            label: itex.userInfo.fullName || '',
            value: `${itex.userId}-${itex.deptId}`,
            disabled: false,
          };
        }),
      });
    }
  }

  return deptUserTree;
};

const BusinessForm: React.FC<BaseFormProps> = (props) => {
  const { initialState } = useModel('@@initialState');
  const { formRefs, required, disabled = false, isEdit, outRow, dependency } = props;
  const location = useLocation();

  // 是否发起修改流程
  const isApproveEdit = (location?.state as Record<string, any>)?.isApproveEdit;

  console.log(formRefs, 'formRefs----');

  return (
    <>
      <ProFormRadio.Group
        label={'是否占位'}
        name={'isDraft'}
        initialValue={NumBool.否}
        fieldProps={{ disabled: outRow?.status === ATTORNEY_STATUS.back ? false : !!isEdit }}
        formItemProps={{ rules: [{ required: true }] }}
        valueEnum={NumBoolObj}
      />

      <ProFormSelect
        label={'委托书类型'}
        name={'type'}
        formItemProps={{ rules: [{ required: true }] }}
        valueEnum={ATTORNEY_TYPE_MAP}
        fieldProps={{ disabled: !!isEdit }}
        addonAfter={
          <Button
            type="primary"
            disabled={!!isEdit}
            
            onClick={() => {
              alert_import({
                type: formRefs?.current?.['baseForm']?.getFieldValue('type'),
                onSuccess(data) {
                  Object.keys(formRefs?.current)?.forEach((formKey) => {
                    formRefs?.current?.[formKey]?.setFieldsValue({
                      ...data,
                      billAcceptUnit: data?.entrustEntName,
                      buEntrustAntennaInfo: {
                        ...data?.buEntrustAntennaInfo,
                        testItem:
                          data?.buEntrustAntennaInfo?.testItem &&
                          Object.keys(JSON.parse(data?.buEntrustAntennaInfo?.testItem)),
                        testItems:
                          data?.buEntrustAntennaInfo?.testItem &&
                          JSON.parse(data?.buEntrustAntennaInfo?.testItem),
                      },
                      sampleInfos: data?.sampleInfos?.map((item, index) => {
                        return {
                          ...item,
                          index,
                          id: undefined,
                          rowId: Date.now() + 1000 * (index + 1),
                        };
                      }),
                      sampleFittingsInfos: data?.sampleFittingsInfos?.map((item, index) => {
                        return {
                          ...item,
                          index,
                          id: undefined,
                          rowId: Date.now() + 1000 * (index + 1),
                        };
                      }),
                      buEntrustPayInfos: data?.buEntrustPayInfos?.map((item, index) => {
                        return {
                          ...item,
                          index,
                          id: undefined,
                          rowId: Date.now() + 1000 * (index + 1),
                        };
                      }),
                      inspectionItem: data?.testProjectInfo || data?.inspectionItem,
                      standardInfo: data?.standardInfo?.split('\n'),
                    });
                  });
                },
              });
            }}
          >
            导入数据
          </Button>
        }
      />
      <ProFormDependency name={['type']}>
        {({ type }) => {
          if (type === ATTORNEY_TYPE.model || type === ATTORNEY_TYPE.antenna || type === ATTORNEY_TYPE.radiation) {
            return (
              <ProFormSelect
                name={'testType'}
                label="检测类型"
                dicCode={type === ATTORNEY_TYPE.antenna ? 'txProductType' : (type === ATTORNEY_TYPE.model ? 'modeProductType' : 'radProductType') }
                rules={[
                  {
                    required: type === ATTORNEY_TYPE.model,
                    message: '此项必选',
                  },
                ]}
              />
            );
          }
        }}
      </ProFormDependency>
      <Title title="业务信息" />
      <ProFormTreeSelect
        label={'任务类型'}
        name={'taskTypeId'}
        formItemProps={{ rules: [{ required: true }] }}
        request={async () => {
          return taskTypeInfoGetTree({ displayTrue: true } as ArgumentsType<
            typeof taskTypeInfoGetTree
          >[0]).then((res) => disabledParent(res?.data) || []);
        }}
        fieldProps={{
          treeDefaultExpandAll: true,
          fieldNames: {
            label: 'name',
            value: 'id',
            children: 'child',
          },
        }}
      />

      <ProFormSelect
        type="user"
        label={'项目经理'}
        initialValue={initialState?.currentUser?.id}
        name={'projectManagerUserId'}
        formItemProps={{ rules: [{ required: true }] }}
        fieldProps={{ style: { width: '100%' }, disabled: true }}
      />

      <ProFormDependency name={['isDraft', 'type']}>
        {({ isDraft, type }) => {
          return (
            <>
              {type === ATTORNEY_TYPE.model ? (
                <ProFormTreeSelect
                  name={['buEntrustApproveInfo', 'auditorUserId']}
                  label="测试人员"
                  fieldProps={{
                    treeDefaultExpandAll: true,
                  }}
                  rules={[
                    {
                      required: true,
                      message: '请选择测试人员',
                    },
                  ]}
                  request={async () => {
                    const res = await orgUserInfoGetUserInfoByRole({
                      id: ROLE_TYPE.testPerson,
                    });

                    const treeList = formatUserTree(res.data || {});

                    return treeList;
                  }}
                />
              ) : (
                <ProFormSelect
                  type="user"
                  query={{
                    roleId: ROLE_TYPE.testManager,
                  }}
                  label={'测试主管'}
                  name={'testSupervisorUserIds'}
                  fieldProps={{ style: { width: '100%' }, mode: 'multiple' }}
                  tooltip='必选条件：认证标识选择了CMA、CNAS、ILAC_CNAS其中任意一个并且选择了分包'
                  rules={
                    !!['CMA', 'CNAS', 'ILAC_CNAS'].filter((i) =>
                      ((dependency?.certificaMark || []) as string[]).includes(i),
                    ).length && !!dependency?.tfSubcontract
                      ? [
                          {
                            required: true,
                            message: '此项必选',
                          },
                        ]
                      : undefined
                  }
                />
              )}
              <BasicForm
                formRefs={formRefs}
                required={!isDraft}
                disabled={disabled}
                isMode
                extraColumns={
                  type === ATTORNEY_TYPE.model && (
                    <>
                      <ProFormDatePicker
                        name={'sampleArrivalDate'}
                        label="收样时间"
                        fieldProps={{
                          style: { width: '100%' },
                        }}
                        rules={[
                          {
                            required: true,
                            message: '此项必选',
                          },
                        ]}
                      />
                      <ProFormDatePicker
                        name={'pushDate'}
                        label="推送时间"
                        fieldProps={{
                          style: { width: '100%' },
                        }}
                        rules={[
                          {
                            required: true,
                            message: '此项必选',
                          },
                        ]}
                      />
                    </>
                  )
                }
              />
            </>
          );
        }}
      </ProFormDependency>
    </>
  );
};
export default BusinessForm;
