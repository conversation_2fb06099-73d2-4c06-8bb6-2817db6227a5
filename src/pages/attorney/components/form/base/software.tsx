import ProFormSelect from '@/components/proFormSelect';
import Title from '@/components/title';
import { arr2ValueEnum } from '@/utils';
import { ProFormInstance, ProFormText } from '@ant-design/pro-components';
type BaseFormProps = {
  required?: boolean;
  disabled?:boolean;
};

const SoftwareForm: React.FC<BaseFormProps> = (props) => {
  const { formRef, required,disabled = false } = props;
  return (
    <>
      <Title title="软件测试信息" />

      <ProFormText disabled={disabled} label={'软件名称'} name={['buEntrustSoftwareInfo','swName']}  />
      <ProFormText
        label={'软件版本'}
        disabled={disabled}
        name={['buEntrustSoftwareInfo','swVersion']}
      />

      <ProFormText
        label={'文档资料'}
        disabled={disabled}
        name={['buEntrustSoftwareInfo','swDocument']}
      />

      <ProFormText
        label={'测试环境信息'}
        disabled={disabled}
        name={['buEntrustSoftwareInfo','testEnvInfo']}
      />

      <ProFormSelect
        label={'软件处理意见'}
        disabled={disabled}
        name={['buEntrustSoftwareInfo','swOpinion']}
        valueEnum={arr2ValueEnum(['删除', '清除历史记录', '其他'])}
      />
    </>
  );
};
export default SoftwareForm;
