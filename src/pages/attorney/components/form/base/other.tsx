import Title from '@/components/title';
import Upload from '@/components/upload';
import { BoolObj, NumBool, NumBoolObj } from '@/enums';
import { UploadOutlined } from '@ant-design/icons';
import { ProFormInstance, ProFormItem, ProFormRadio, ProFormTextArea } from '@ant-design/pro-components';
import { Button, Col } from 'antd';
type BaseFormProps = {
  formRef?:
    | React.MutableRefObject<ProFormInstance | undefined>
    | React.RefObject<ProFormInstance | undefined>;
  disabled?: boolean;
  required?: boolean;
};

const OtherForm: React.FC<BaseFormProps> = (props) => {
  const { formRef, disabled = false, required = true } = props;
  return (
    <>
      <Title title="其他信息" />
      {/* <Col span={12}>
        <ProFormItem
          labelCol={{ span: 3 }}
          label="委托书附件"
          name="fileInfos"
          style={{ padding: '0 5px' }}
        >
          <Upload disabled={disabled} multiple>
            <Button disabled={disabled} icon={<UploadOutlined />}>
              上传
            </Button>
          </Upload>
        </ProFormItem>
      </Col> */}

      
      <Col span={12}>
        <ProFormItem
          labelCol={{ span: 3 }}
          label="客户确认文件"
          name="confirmedFileIds"
        >
          <Upload disabled={disabled} multiple>
            <Button disabled={disabled} icon={<UploadOutlined />}>
              上传
            </Button>
          </Upload>
        </ProFormItem>
      </Col>

      <ProFormRadio.Group
        label={'是否鲜章'}
        name={'tfFreshChapter'}
        disabled={disabled}
        colProps={{ span: 12 }}
        // formItemProps={{ rules: [{ required: true }] }}
        valueEnum={BoolObj}
      />

      <ProFormTextArea
        labelCol={{ span: 3 }}
        colProps={{ span: 12 }}
        label={'委托书备注'}
        name={'remark'}
        fieldProps={{ disabled }}
      />

      {/* <Col span={12}>
        <ProFormItem
          labelCol={{ span: 3 }}
          label="客户签字文件"
          name="signedFileIds"
          style={{ padding: '0 5px' }}
        >
          <Upload disabled={disabled} multiple>
            <Button disabled={disabled} icon={<UploadOutlined />}>
              上传
            </Button>
          </Upload>
        </ProFormItem>
      </Col> */}
    </>
  );
};
export default OtherForm;
