import React from 'react';
import type { ReactNode } from 'react';
import type { UploadProps } from 'antd';
import Action from '@/components/action';
import EditTable from '@/components/editTable';
import { DictSelect } from '@/components/select/dict';
import Text from '@/components/text';
import Title from '@/components/title';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { TASK_TYPE } from '@/enums/task';
import SampleEdit from '@/pages/task/sample/edit';
import { arrivalInspectionBaseInfoPage } from '@/services/base/daohuojianbiaozhunkujiekou';
import { sampleInfoVoPage } from '@/services/base/yangpinjibenxinxibiaojiekou';
import { importbasicCompany } from '@/services/base/yangpinpiliangdaorujiekou';
import { arr2ValueEnum, downloadFile } from '@/utils';
import { ImportOutlined, PlusOutlined } from '@ant-design/icons';
import {
  EditableFormInstance,
  nanoid,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Dropdown, message, Modal, ModalProps, Upload } from 'antd';
import { launch, launchSchema } from 'antd-pro-crud';
import lodash from 'lodash';
import { useMemo, useRef, useState } from 'react';

type BaseFormProps = {
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  disabled?: boolean;
  required?: boolean;
  extraColumns?: React.ReactNode;
  /** 是否显示选择样品按钮 */
  showChoose?: boolean;
  entrustId?: string;
  /** 是否是型号核准类型委托书 */
  isMode?: boolean;
  initRowData: any;
};

const alert_editSample = launchSchema(SampleEdit);

const alert_sample = launch(({ entrustId, ...rest }: { entrustId?: string } & ModalProps) => {
  const [selectedRows, setSelectedRow] = useState<BASE.SampleInfoVO[]>([]);
  return (
    <Modal
      title="样品选择"
      width={1200}
      {...rest}
      onOk={() => {
        rest.onOk?.(selectedRows as any);
      }}
      okButtonProps={{
        disabled: selectedRows.length === 0,
      }}
    >
      <ProTable
        options={false}
        rowKey="id"
        request={async ({ pageSize, current, ...rest }: { pageSize?: number; current?: number; [key: string]: any }) => {
          return sampleInfoVoPage({
            page: current!,
            size: pageSize!,
            timeSort: 1,
            entrustId: (entrustId && (entrustId as unknown as number)) || undefined,
            tfentrustIdIsNull: !entrustId,
            queryType: 2,
            ...rest,
          }).then((res) => {
            return {
              success: res?.success,
              data: res?.data?.records,
              total: res?.data?.total,
            };
          });
        }}
        rowSelection={{
          onChange: (selectedRowKeys: React.Key[], selectedRows: BASE.SampleInfoVO[]) => {
            setSelectedRow(selectedRows);
          },
        }}
        toolBarRender={(action: any) => [
          !entrustId && (
            <Button
              type="primary"
              key={'add'}
              icon={<PlusOutlined />}
              onClick={() =>
                alert_editSample({
                  onSuccess: () => action?.reload(),
                  formatParams(values: any) {
                    return {
                      ...values,
                      entrustId,
                    };
                  },
                })
              }
            >
              新增
            </Button>
          ),
        ]}
        columns={[
          {
            dataIndex: 'sampleNumber',
            title: '样品编号',
          },
          {
            dataIndex: 'taskCode',
            title: '任务编号',
          },
          {
            dataIndex: 'sampleName',
            title: '样品名称',
          },
          {
            dataIndex: 'sampleModel',
            title: '样品型号',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleStatus',
            title: '样品状态',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleSerialNumber',
            title: '样品序列号',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleVersionNumber',
            title: '样品版本号',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleType',
            title: '样品类型',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleNum',
            title: '样品数量',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleProcessStatus',
            title: '样品处理状态',
            hideInSearch: true,
          },
          {
            dataIndex: 'sampleRemark',
            title: '样品备注',
            hideInSearch: true,
          },
          {
            dataIndex: 'statusInfo',
            title: '状态',
            hideInSearch: true,
          },
        ]}
      />
    </Modal>
  );
});

type FittingObject = Partial<BASE.SampleFittingsInfo> & {
  rowId: string;
};

const SampleForm: React.FC<BaseFormProps> = (props) => {
  const {
    formRefs,
    disabled = false,
    required = true,
    extraColumns,
    entrustId,
    showChoose,
    initRowData,
  } = props;

  const editableFormRef = useRef<EditableFormInstance>();
  const { initialState } = useModel('@@initialState');
  const sampleFormRef = useRef<ProFormInstance>();
  const [prodUnit, setProdUnit] = useState<any[]>([]);
  const [currentName, setCurrentName] = useState<string>('');
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  // 标记 样品类型
  const fittingCount = useMemo(() => {
    return (initialState?.dict['fittingType'] || []).map((i: any) => ({
      ...i,
      count: 0,
    }));
  }, [initialState]);

  return (
    <>
      <Title
        title="样品信息"
        extra={
          <>
            {showChoose ? (
              <a
                onClick={() => {
                  alert_sample({
                    entrustId: entrustId,
                    async onOk(selectedRows) {
                      const oldSampleInfos =
                        formRefs?.current?.['baseForm']?.getFieldValue('sampleInfos') || [];
                      formRefs?.current?.['baseForm']?.setFieldsValue({
                        // 合并oldSampleInfos和selectedRows
                        sampleInfos: lodash.uniqBy(
                          [
                            ...oldSampleInfos,
                            ...(selectedRows as unknown as Record<string, any>[]),
                          ],
                          'id',
                        ),
                      });
                    },
                  });
                }}
              >
                选择样品
              </a>
            ) : null}
          </>
        }
      />
      <ProFormDependency name={['type', 'testType']}>
        {({ type, testType }: { type: any; testType: any }) => {
          return (
            <ProForm.Item
              name={'sampleInfos'}
              rules={[{ required: true, message: '请至少添加一个样品' }]}
              style={{ padding: '5px' }}
            >
              <EditTable<Partial<BASE.SampleInfoVO> & { rowId: number }>
                hidenEdit={disabled}
                editableFormRef={editableFormRef}
                formRef={sampleFormRef}
                style={{ padding: 0 }}
                editable={{
                  editableKeys,
                  onChange: setEditableRowKeys,
                }}
                recordCreatorProps={
                  disabled && (type === TASK_TYPE.检测任务 || type === TASK_TYPE.内部任务)
                    ? false
                    : ({
                        position: 'bottom',
                        record: () => ({
                          rowId: Date.now(),
                          sampleStatus: '完好',
                          sampleProcessStatus: '检毕取回',
                        }),
                      } as any)
                }
                toolBarRender={
                  !disabled
                    ? () => {
                        return [
                          <Dropdown
                            key="import"
                            menu={{
                              items: [
                                {
                                  key: 'downloadTemp',
                                  label: (
                                    <a
                                      // href={`${PROXY_KEY}test/templateInfo/downTemplate?fileName=测试子项导入模板.xlsx&type=测试子项导入模板`}
                                      // download={'测试子项导入模板.xlsx'}
                                      onClick={() =>
                                        downloadFile(
                                          `/templateInfo/downTemplate?fileName=样品导入.xlsx&type=样品批量导入模板`,
                                        )
                                      }
                                    >
                                      {'下载导入模板'}
                                    </a>
                                  ),
                                },
                                {
                                  key: 'uploadTemp',
                                  label: (
                                    <Upload
                                      accept=".xls,.xlsx"
                                      showUploadList={false}
                                      customRequest={async (options) => {
                                        if (!options.file) {
                                          message.error('请选择要上传的文件');
                                          return;
                                        }

                                        const form = new FormData();
                                        form.append('file', options.file);

                                        try {
                                          const res = await importbasicCompany(form);
                                          const originData =
                                            formRefs?.current?.['baseForm']?.getFieldValue?.(
                                              'sampleInfos',
                                            ) || [];
                                          formRefs?.current?.['baseForm']?.setFieldsValue?.({
                                            sampleInfos: [...originData, ...(res?.data || [])]?.map(
                                              (i: Record<string, any>, index: number) => {
                                                console.log(index, 'index----');
                                                return {
                                                  ...i,
                                                  sampleNumber: i?.id
                                                    ? i?.sampleNumber
                                                    : `S${index + 1}`,
                                                  rowId: nanoid(),
                                                };
                                              },
                                            ),
                                          });

                                          // table.current?.reload();
                                          message.success(`导入数据成功`);
                                        } catch (e) {
                                          message.error(
                                            `${(options.file as File)?.name || ''}上传错误`,
                                          );
                                        }
                                      }}
                                    >
                                      <div>导入</div>
                                    </Upload>
                                  ),
                                },
                              ],
                            }}
                          >
                            <Button type="primary" icon={<ImportOutlined />}>
                              导入样品
                            </Button>
                          </Dropdown>,
                        ];
                      }
                    : false
                }
                onChange={(value) => {
                  // let newList: any = [];
                  const newValue = [...(value || [])];
                  const lastItem = newValue[newValue?.length - 1];
                  // if(newItem && Number(newItem?.sampleNum) === 1) {
                  //   newValue?.push({
                  //     ...newItem,
                  //     sampleNumber: `S${newValue?.length + 1}`,
                  //   });
                  // }

                  // 编辑
                  for (let i = 0; i < newValue?.length; i++) {
                    const newItem: any = newValue?.splice(i, 1) || {};
                    // if(newItem?.[0]?.sampleNum > 1) {
                      if(newItem[0]?.sampleNumber) {
                        newValue?.splice(i, 0 , {
                          ...newItem[0],
                          id: newItem[0]?.id || undefined,
                          sampleNum: 1,
                          sampleNumber: newItem[0]?.sampleNumber || `S${i + 1}`,
                          rowId: Date.now() + 1000 * (i + 1),
                          statusInfo: '待领取',
                        })
                      }
                      
                      for (let j = 0; j < (newItem[0]?.sampleNumber ? newItem?.[0]?.sampleNum - 1 : newItem?.[0]?.sampleNum); j++) {
                        const insertIndex = newValue?.findIndex((item, index) => {
                          return Number(item?.sampleNumber?.replace('S', '')) > index + 1;
                        });
  
                        if (insertIndex !== -1) {
                          newValue?.splice(insertIndex, 0, {
                            ...newItem[0],
                            // index: index + i + 1,
                            id: undefined,
                            rowId: Date.now() + 1000 * (i + 1),
                            sampleNum: 1,
                            sampleNumber:`S${insertIndex + 1}`,
                            statusInfo: '待领取',
                          });
                        } else {
                          newValue.push({
                            ...newItem[0],
                            // index: index + i + 1,
                            id: undefined,
                            rowId: Date.now() + 1000 * (i + 1),
                            sampleNum: 1,
                            sampleNumber: `S${newValue?.length + 1}`,
                            statusInfo: '待领取',
                          });
                        }
                      }
                    // } else {
                    //   newValue?.splice(i, 0 , {
                    //     ...newItem[0],
                    //     id: newItem[0]?.id || undefined,
                    //     sampleNum: 1,
                    //     sampleNumber: newItem[0]?.sampleNumber || `S${i + 1}`,
                    //     rowId: Date.now() + 1000 * (i + 1),
                    //     statusInfo: '待领取',
                    //   })
                    // }
                  }

                  // const result = [...value?.map((i) => ({ ...i, sampleNum: 1 })), ...newList];

                  formRefs?.current?.['baseForm']?.resetFields(['sampleInfos']);

                  formRefs?.current?.['baseForm']?.setFieldsValue?.({
                    sampleInfos: newValue,
                  });
                }}
                columns={[
                  type === ATTORNEY_TYPE.antenna && testType === '到货检'
                    ? {
                        title: '样品名称',
                        dataIndex: 'sampleName',
                        formItemProps: { rules: [{ required: true }] },
                        valueType: 'select',
                        width: 250,
                        fieldProps: {
                          // multiple: true,
                          treeDefaultExpandAll: true,
                          showSearch: true,

                          onChange: (value: any) => {
                            setCurrentName(value as unknown as string);
                          },
                          disabled,
                          fieldNames: {
                            label: 'productName',
                            value: 'productName',
                          },
                        },
                        // render(dom, entity, index, action, schema) {
                        //     if(entity?.sampleName === 'null') {
                        //       return '-'
                        //     }
                        //     return entity?.sampleName
                        // },
                        request: () => {
                          return arrivalInspectionBaseInfoPage({
                            page: 1,
                            size: 9999,
                          }).then((res) => {
                            setProdUnit(res?.data?.records || []);
                            return lodash.uniqBy(res?.data?.records || [], 'productName');
                          });
                        },
                      }
                    : {
                        title: '样品名称',
                        dataIndex: 'sampleName',
                        fieldProps: { disabled, style: { width: '100%' } },
                        formItemProps: { rules: [{ required: true }] },
                      },
                  {
                    title: '样品编号',
                    dataIndex: 'sampleNumber',
                    fieldProps: { disabled: true, style: { width: '100%' } },
                    // formItemProps: { rules: [{ required: true }] },
                  },
                  {
                    title: '样品序号',
                    dataIndex: 'sampleSerialNumber',
                    formItemProps: { rules: [{ required: true }] },
                  },
                  {
                    title: '样品版本号',
                    dataIndex: 'sampleVersionNumber',
                    fieldProps: { disabled, style: { width: '100%' } },
                  },
                  {
                    title: '样品型号',
                    dataIndex: 'sampleModel',
                    formItemProps: { rules: [{ required: true }] },
                    fieldProps: { disabled, style: { width: '100%' } },
                  },
                  {
                    title: '样品数量',
                    dataIndex: 'sampleNum',
                    formItemProps: { rules: [{ required: true }] },
                    fieldProps: { disabled, style: { width: '100%' } },
                  },
                  {
                    title: '样品状态',
                    dataIndex: 'sampleStatus',
                    fieldProps: { disabled, style: { width: '100%' } },
                  },
                  {
                    title: '样品处理意见',
                    dataIndex: 'sampleProcessStatus',
                    fieldProps: { disabled, style: { width: '100%' } },
                    valueEnum: arr2ValueEnum(['检毕取回', '受委托方处理', '报损']),
                  },
                  type === ATTORNEY_TYPE.antenna && testType === '到货检'
                    ? {
                        title: '生产单位',
                        dataIndex: 'productCompany',
                        formItemProps: { rules: [{ required: true }] },
                        valueType: 'select',
                        fieldProps: {
                          disabled,
                          showSearch: true,
                          onChange: (value: any) => {
                            const filterValue = prodUnit
                              .filter((item: any) => {
                                return item.productName === currentName;
                              })
                              .filter((item: any) => {
                                return item.productUnit === value;
                              });
                            formRefs?.current?.['baseForm']?.setFieldValue(
                              'judgBasis',
                              filterValue
                                .map((item) => {
                                  return item.judgBasis;
                                })
                                .join('\r') || '',
                            );
                            const preArr =
                              formRefs?.current?.['baseForm']
                                ?.getFieldValue('testMethod')
                                ?.split('\r') || [];
                            const testValue = Array.from(
                              new Set([
                                ...preArr,
                                ...filterValue.map((item) => {
                                  return item.testMethod;
                                }),
                              ]),
                            );
                            formRefs?.current?.['baseForm']?.setFieldValue(
                              'testMethod',
                              testValue.join('\r') || '',
                            );
                          },
                        },
                        valueEnum: () => {
                          return arr2ValueEnum(
                            prodUnit
                              .filter((item: any) => {
                                return item.productName === currentName;
                              })
                              .map((item: any) => {
                                return item.productUnit;
                              }) || [],
                          );
                        },
                      }
                    : {
                        title: '生产单位',
                        dataIndex: 'productCompany',
                        fieldProps: { disabled },
                        formItemProps: { rules: [{ required: true }] },
                      },
                  {
                    title: '生产单位地址',
                    dataIndex: 'productCompanyAddress',
                    fieldProps: { disabled },
                  },
                  {
                    title: '备注',
                    dataIndex: 'sampleRemark',
                    fieldProps: { disabled, style: { width: '100%' } },
                  },
                  {
                    title: '操作',
                    valueType: 'option',
                    fixed: 'right',
                    hideInTable:
                      disabled && (type === TASK_TYPE.检测任务 || type === TASK_TYPE.内部任务),
                    render(dom: any, entity: any, index: any, action: any, schema: any) {
                      return (
                        <Action>
                          <Text
                            onClick={() => {
                              action?.startEditable?.(entity.rowId);
                            }}
                          >
                            编辑
                          </Text>
                          <Text
                            type="danger"
                            onClick={() => {
                              const list = (formRefs?.current?.['baseForm']?.getFieldValue(
                                'sampleInfos',
                              ) || []) as {
                                id: number;
                                rowId: number;
                                sampleNumber: string;
                              }[];
                              const filterList = list
                                ?.filter((v) => {
                                  const vId = v.id || v.rowId;
                                  const entityId = entity.id || entity.rowId;
                                  return vId !== entityId;
                                })

                                filterList.forEach((i, index) => {
                                  if(!i?.id){
                                    i.sampleNumber = `S${(Number(filterList[index - 1]?.sampleNumber?.replace('S', '')) || 0) + 1}`
                                  }
                                })

                              formRefs?.current?.['baseForm']?.setFieldsValue({
                                
                                sampleInfos: filterList,
                              });
                            }}
                          >
                            删除
                          </Text>
                        </Action>
                      );
                    },
                  },
                ]}
              />
            </ProForm.Item>
          );
        }}
      </ProFormDependency>

      <Title title="配件信息" />
      <ProForm.Item name={'sampleFittingsInfos'}>
        <EditTable<FittingObject>
          rowKey={'rowId'}
          recordCreatorProps={
            !!entrustId
              ? false
              : {
                  position: 'bottom',
                  record: () => ({
                    rowId: nanoid(),
                    fittingsCount: 1,
                  }),
                }
          }
          hidenDelete={!!entrustId}
          hidenEdit={!!entrustId}
          onChange={(values) => {
            const v: FittingObject[] = [];
            let temp = fittingCount?.map((item: any) => ({
              name: item.dicName,
              key: item.dicExplain,
              count: item.count,
            }));

            if (values?.length > 0) {
              for (let i = 0; i < values.length; i++) {
                if ((values[i]?.fittingsCount || 0) > 1) {
                  for (let n = 0; n < (values[i]?.fittingsCount || 0); n++) {
                    const _fittingType = temp?.find((item: any) => item.name === values[i].fittingsType);
                    v.push({
                      ...values[i],
                      fittingsCount: 1,
                      rowId: `${values[i].rowId}-${n}`,
                      fittingsNumber: `${_fittingType?.key}${(_fittingType?.count || 0) + 1}`,
                    });

                    temp = temp?.map((item: any) => {
                      if (item.name === values[i].fittingsType) {
                        return {
                          ...item,
                          count: item.count + 1,
                        };
                      }

                      return item;
                    });
                  }
                } else {
                  const _fittingType = temp?.find((item: any) => item.name === values[i].fittingsType);
                  v.push({
                    ...values[i],
                    fittingsNumber: `${_fittingType?.key}${(_fittingType?.count || 0) + 1}`,
                  });
                  temp = temp?.map((item: any) => {
                    if (item.name === values[i].fittingsType) {
                      return {
                        ...item,
                        count: item.count + 1,
                      };
                    }

                    return item;
                  });
                }
              }
            }

            formRefs?.current?.['baseForm']?.setFieldValue('sampleFittingsInfos', v);
          }}
          columns={[
            {
              title: '配件名称',
              dataIndex: 'fittingsName',
            },
            {
              title: '编号',
              dataIndex: 'fittingsNumber',
              editable: false,
            },
            {
              title: '配件类型',
              dataIndex: 'fittingsType',
              formItemProps: { rules: [{ required: true, message: '此项必选' }] },
              renderFormItem: () => (
                <DictSelect dicCode="fittingType" formatOptions={(v) => v?.reverse()} />
              ),
            },
            {
              title: '数量',
              dataIndex: 'fittingsCount',
              valueType: 'digit',
            },
            {
              title: '备注',
              dataIndex: 'fittingsRemark',
            },
          ]}
        />
      </ProForm.Item>

      {initRowData?.type !== ATTORNEY_TYPE.model && (
        <>
          <ProFormTextArea
            label="检验样品功能描述"
            name={'sampleFunctionDescription'}
            colProps={{ span: 24 }}
            disabled={disabled}
          />
          <ProFormTextArea
            label="检验样品规格及参数"
            name={'sampleSpecificationParam'}
            colProps={{ span: 24 }}
            disabled={disabled}
          />
          <ProFormTextArea
            label="样品的配置、工作状态、软硬件版本信息"
            name={'sampleConfigInfo'}
            colProps={{ span: 24 }}
            disabled={disabled}
          />
          <ProFormTextArea
            label="配合被测样品工作且影响样品工作状态的设备信息"
            name={'sampleImpactStatusInfo'}
            colProps={{ span: 24 }}
            disabled={disabled}
          />
        </>
      )}
      {extraColumns}
    </>
  );
};
export default SampleForm;
