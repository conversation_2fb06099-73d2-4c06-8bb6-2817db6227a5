import ProFormSelect from '@/components/proFormSelect';
import { ROLE_TYPE } from '@/enums';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { TASK_TYPE } from '@/enums/task';
import { entrustInfoGetEndDateInfo } from '@/services/base/weituoshujichuxinxibiaojiekou';
import {
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import dayjs from 'dayjs';

type BaseFormProps = {
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  // 最前面添加的表单项
  prefixColumns?: React.ReactNode;
  // 最后面添加的表单项
  extraColumns?: React.ReactNode;
  // 是否禁用不会禁用prefixColumns和extraColumns
  disabled?: boolean;
  // 是否必填
  required?: boolean;
  /** 是否是核准类型委托书 */
  isMode?: boolean;
  isBand?: boolean;
};

const BasicForm: React.FC<BaseFormProps> = (props) => {
  const {
    formRefs,
    extraColumns,
    prefixColumns,
    disabled = false,
    required = true,
    isMode = false,
    isBand,
  } = props;

  const getReviseData = useRequest(
    (entrustDate, detecCycle) => {
      return entrustInfoGetEndDateInfo({
        startDate: dayjs(entrustDate).format('YYYY-MM-DD'),
        workDays: detecCycle,
      });
    },
    {
      manual: true,
      onSuccess(data, params) {
        formRefs?.current?.['baseForm']?.setFieldValue('deadLineDate', dayjs(data));
      },
    },
  );

  return (
    <>
      {prefixColumns}

      <ProFormText
        label={'项目名称'}
        required={required}
        name={'entrustName'}
        disabled={isBand || disabled}
      />

      <ProFormDatePicker
        label={'委托日期'}
        name={'entrustDate'}
        formItemProps={{ rules: [{ required }] }}
        fieldProps={{
          style: { width: '100%' },
          disabled: disabled,
          picker: 'date',
          onChange(value, dateString) {
            const _acceptDate = formRefs?.current?.['baseForm']?.getFieldValue('acceptDate');
            // 如果受理日期有值，并且委托日期大于受理日期，清空受理日期
            if (_acceptDate && dayjs(dateString as string).isAfter(dayjs(_acceptDate))) {
              formRefs?.current?.['baseForm']?.setFieldsValue({ acceptDate: undefined });
            }
          },
          disabledDate(current) {
            return current && current > dayjs().endOf('day');
          },
        }}
      />

      <ProFormDependency name={['type']}>
        {({ type }) => {
          if (type === ATTORNEY_TYPE.model || type === TASK_TYPE.检测记录) {
            return null;
          } else {
            return (
              <ProFormDatePicker
                label={'到样日期'}
                name={'sampleArrivalDate'}
                formItemProps={{
                  rules: [
                    {
                      required:
                        type === ATTORNEY_TYPE.normal ||
                        type === ATTORNEY_TYPE.antenna ||
                        type === ATTORNEY_TYPE.enter
                          ? true
                          : false,
                    },
                  ],
                }}
                fieldProps={{
                  style: { width: '100%' },
                  picker: 'date',
                }}
              />
            );
          }
        }}
      </ProFormDependency>

      <ProFormDigit
        label={'检测周期'}
        name={'detecCycle'}
        fieldProps={{
          style: { width: '100%' },
          disabled,
          precision: 0,
          addonAfter: '工作日',
          onBlur(value) {
            if (formRefs?.current?.['baseForm']?.getFieldValue('acceptDate') && value) {
              getReviseData.run(
                formRefs?.current?.['baseForm']?.getFieldValue('acceptDate'),
                value,
              );
            }
          },
        }}
        formItemProps={{ rules: [{ required }] }}
      />

      <ProFormSelect
        type="user"
        query={{
          roleId: ROLE_TYPE.salesPerson,
        }}
        label={'销售人员'}
        name={'salesManId'}
        fieldProps={{ disabled }}
      />

      {extraColumns}
    </>
  );
};
export default BasicForm;
