import Action from '@/components/action';
import EditTable from '@/components/editTable';
import FileDownload from '@/components/fileDownload';
import ProFormSelect from '@/components/proFormSelect';
import ProTable from '@/components/proTable';
import Text from '@/components/text';
import Title from '@/components/title';
import { NumBoolObj, ROLE_TYPE } from '@/enums';
import testTimeDetail from '@/pages/task/plan/detail/testTimeDetail';
import { sumTestDuration } from '@/pages/task/plan/form/testTimeEdit';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';
import { taskDeptResultInfoUpdate } from '@/services/base/renwujianceshizirenwubiaojiekou';
import {
  taskInfoGetVo,
  taskInfoModifyContractAmountAndUnitPrice,
} from '@/services/base/renwujichuxinxibiaojiekou';
import {
  taskDeptKeepTimeInfoAccount,
  taskDeptKeepTimeInfoBatchPrice,
  taskDeptKeepTimeInfoUpdate,
  taskDeptKeepTimeInfoVoPage,
} from '@/services/base/renwujishirenwujiekou';
import { arr2ValueEnum } from '@/utils';
import {
  ProDescriptions,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import {
  Button,
  Col,
  Form,
  FormInstance,
  message,
  Modal,
  ModalProps,
  Row,
  Tabs,
  TabsProps,
  Tag,
} from 'antd';
import { alert_edit, launch, OnlyInSearch } from 'antd-pro-crud';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import PayInfo from '../pay/payInfo';

const alert_detail = launch(testTimeDetail);
export type BizObject = BASE.TaskDeptKeepTimeInfoVO;

export type DetailProps = ModalProps & {
  row?: BASE.TaskInfoVO;
  onFinish?: (params: Record<string, any>) => void;
};

const AttorneyDetail = (props: DetailProps) => {
  const { row, ...rest } = props;

  const basicInfoForm = useRef<ProFormInstance>();
  const editForm = useRef<FormInstance>();
  const { commonAccess } = useAccess();
  const [activeKey, setActiveKey] = useState<string>('收支情况');

  const taskInfoReq = useRequest(() => {
    return taskInfoGetVo({ id: row?.id as unknown as string });
  });

  // 是否核算
  const isAccount = useRequest(() => {
    return taskDeptKeepTimeInfoVoPage({
      page: 1,
      size: 999999,
      timeSort: 1,
      taskId: row?.id,
    });
  }, {
    manual: false,
    formatResult(res) {
      return res?.data?.records?.some((item) => item?.statusInfo === '已核算');
    },
  });

  useEffect(() => {
    if (activeKey === '收支情况') {
      isAccount.run();
    }
  }, [activeKey]);

  const { initialState, setInitialState, refresh } = useModel('@@initialState');

  useEffect(() => {
    if (taskInfoReq?.data) {
      basicInfoForm?.current?.setFieldsValue(taskInfoReq?.data);
    }
  }, [taskInfoReq?.data]);

  const edit = (entity: any, onSuccess?: () => void, selectedRowKeys?: string[]) => {
    return alert_edit({
      title: '测试单价编辑',
      initialValues: entity,
      columns: [
        {
          title: '测试时长',
          name: 'testDurationSum',
          valueType: 'digit',
          hideInForm: !entity,
          fieldProps: {
            disabled: true,
            style: {
              width: '100%',
            },
            addonAfter: '小时',
          },
        },
        {
          title: '测试单价',
          name: 'testUnitPrice',
          valueType: 'digit',
          renderFormItem(schema, config, form, action) {
            return (
              <ProFormDigit
                name="testUnitPrice"
                fieldProps={{
                  precision: 2,
                  onChange(value) {
                    if (entity) {
                      form?.setFieldValue(
                        'testUnitPriceSum',
                        (Number(value || 0) * Number(entity?.testDurationSum || 0))?.toFixed(2),
                      );
                    }
                  },
                  addonAfter: '元',
                }}
              />
            );
          },
        },
        {
          title: '共计',
          name: 'testUnitPriceSum',
          valueType: 'digit',
          hideInForm: !entity,
          fieldProps: {
            disabled: true,
            style: {
              width: '100%',
            },
            precision: 2,
            addonAfter: '元',
            placeholder: '测试时长*测试单价',
          },
        },
      ],
      onFinish: async (values) => {
        const params = {
          ...values,
          id: entity?.id || null,
          deptId: entity?.deptId,
          taskId: entity?.taskId,
          taskDeptId: entity?.taskDeptId,
        };
        const res = entity
          ? await taskDeptKeepTimeInfoUpdate(
              params as unknown as ArgumentsType<typeof taskDeptResultInfoUpdate>[0],
            )
          : await taskDeptKeepTimeInfoBatchPrice(
              selectedRowKeys?.map((id) => {
                return {
                  id: id as unknown as number,
                  testUnitPrice: values?.testUnitPrice || null,
                };
              }) || [],
            );
        if (res.success) {
          onSuccess?.();
          message?.success('操作成功！');
          return true;
        }
      },
    });
  };

  const columns = [
    {
      dataIndex: 'number',
      title: '任务编号',
    },
    {
      dataIndex: 'name',
      title: '项目名称',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.entrustName || entity?.name;
      },
    },
    {
      dataIndex: 'taskTypeName',
      title: '任务类型',
    },
    {
      dataIndex: 'businessAreaTypeCn',
      title: '业务领域',
    },
    {
      dataIndex: 'deadLineDate',
      title: '截止时间',
      valueType: 'date',
    },
    {
      dataIndex: 'managerName',
      title: '项目经理',
    },
    {
      dataIndex: 'contractAmount',
      title: '合同金额（元）',
    },
    {
      dataIndex: 'tfTiming',
      title: '是否计时',
      valueEnum: NumBoolObj,
    },
    {
      dataIndex: 'price',
      title: '计时单价（元/小时）',
    },
    {
      dataIndex: 'invoicTotal',
      title: '开票总额（元）',
    },
    {
      dataIndex: 'receiptTotal',
      title: '到账总额（元）',
    },
    {
      dataIndex: 'contractBalancePay',
      title: '合同尾款（元）',
    },
    {
      dataIndex: 'foreignTotal',
      title: '支付总额（元）',
    },
  ];

  const items: TabsProps['items'] = [
    {
      key: '收支情况',
      label: '收支情况',
      children: (
        <>
          {/* 当前登录人是当前任务的项目经理，并且（如果是计时任务要已核算或者非计时任务） */}
          {row?.projectMangeUserId === initialState?.currentUser?.id && ((!!row?.tfTiming && !isAccount.data) || !!!row?.tfTiming) && (
            <Row>
              <Col span={24} style={{ textAlign: 'right', paddingRight: 20 }}>
                <Button
                  type="primary"
                  onClick={() =>
                    alert_edit({
                      title: '编辑',
                      initialValues: taskInfoReq?.data,
                      columns: [
                        {
                          title: '合同金额',
                          name: 'contractAmount',
                          valueType: 'digit',
                          hideInForm: !!row?.tfTiming,
                          fieldProps: {
                            style: {
                              width: '100%',
                            },
                            addonAfter: '元',
                          },
                        },
                        {
                          title: '计时单价',
                          name: 'price',
                          valueType: 'digit',
                          hideInForm: !!!row?.tfTiming,
                          fieldProps: {
                            style: {
                              width: '100%',
                            },
                            addonAfter: '元/小时',
                          },
                        },
                      ],
                      onFinish: async (params) => {
                        const res = await taskInfoModifyContractAmountAndUnitPrice({
                          ...params,
                          id: row?.id,
                        });
                        if (res?.success) {
                          message.success('操作成功');
                          taskInfoReq?.run();
                          return true;
                        }
                      },
                    })
                  }
                >
                  编辑
                </Button>
              </Col>
            </Row>
          )}

          <Title title="基本信息" />
          <ProDescriptions
            labelStyle={{
              width: 177,
            }}
            contentStyle={{
              width: 390,
            }}
            dataSource={taskInfoReq?.data}
            // labelWidth={177}
            // contentWidth={390}
            layout="horizontal"
            column={2}
            columns={columns}
            bordered
          />
          <Title title="已开票信息" />
          <Form.Item name={'payInvoicInfos'} style={{ padding: '5px' }}>
            <EditTable<Partial<BASE.SampleInfoVO> & { rowId: number }>
              //   hidenEdit={disabled}
              //   editableFormRef={editableFormRef}
              //   formRef={payInfoFormRef}
              columns={[
                {
                  title: '开票金额（元）',
                  dataIndex: 'billPayAmount',
                  valueType: 'digit',
                  fieldProps: { style: { width: '100%' } },
                },
                {
                  title: '开票日期',
                  dataIndex: 'billPayDate',
                  valueType: 'date',
                  fieldProps: { style: { width: '100%' } },
                },
                {
                  title: '开票类型',
                  dataIndex: 'billPayType',
                  valueType: 'select',
                  valueEnum: arr2ValueEnum(['普票', '专票']),
                  fieldProps: { style: { width: '100%' } },
                },
                {
                  title: '票据接收单位',
                  dataIndex: 'billAcceptUnit',
                  fieldProps: { style: { width: '100%' } },
                },
              ]}
            />
          </Form.Item>
          <Title title="已到账信息" />
          <Form.Item name={'payReceiptInfos'} style={{ padding: '5px' }}>
            <EditTable<Partial<BASE.SampleInfoVO> & { rowId: number }>
              columns={[
                {
                  title: '付款单位',
                  dataIndex: 'paymentEnt',
                  initialValue: row?.entrustEntName,
                  fieldProps: { style: { width: '100%' } },
                },
                {
                  title: '付款金额（元）',
                  dataIndex: 'paymentAmount',
                  valueType: 'digit',
                  fieldProps: { style: { width: '100%' } },
                },
                {
                  title: '付款时间',
                  dataIndex: 'paymentDate',
                  valueType: 'date',
                  fieldProps: { style: { width: '100%' } },
                },
              ]}
            />
          </Form.Item>
          <PayInfo name="payForeignInfos" />
          <ProFormTextArea
            label={'备注'}
            name={'payRemark'}
            colProps={{ span: 12 }}
            labelCol={{ span: 1 }}
          />
        </>
      ),
    },
    {
      key: '计时管理',
      label: !!row?.tfTiming && commonAccess('admin:/task/list@testTimeConfirm') ? '计时管理' : '',
      disabled: !!!row?.tfTiming && !commonAccess('admin:/task/list@testTimeConfirm'),
      children: (
        <>
          <ProTable<BizObject, {}, GlobalValueType>
            rowKey="id"
            needSelfColumns={false}
            hiddenBtns="all"
            defaultSize="small"
            bordered
            rowSelection={{
              getCheckboxProps: (record: any) => {
                return {
                  disabled: record?.deptName === '合计' || record?.statusInfo === '已核算',
                };
              },
            }}
            // search={false}
            expandable={{
              expandIcon: () => {
                return null;
              },
            }}
            style={{
              marginTop: 24,
              marginLeft: '-24px',
            }}
            options={false}
            request={async ({ current = 1, pageSize = 999, ...otherQuery }) => {
              const res = await taskDeptKeepTimeInfoVoPage({
                page: current,
                size: pageSize,
                timeSort: 1,
                ...otherQuery,
                taskId: row?.id,
              });
              const sumTime = res.data?.records?.reduce((pre, next) => {
                return pre + (next?.testDurationSum || 0);
              }, 0);
              const sumPrice = res.data?.records?.reduce((pre, next) => {
                return pre + (next?.testUnitPriceSum || 0);
              }, 0);
              return {
                data: (res.data?.records && res.data?.records?.length > 0
                  ? [
                      ...(res.data?.records || []),
                      {
                        deptName: '合计',
                        testDurationSum: sumTime,
                        testUnitPrice: sumTime ? ((sumPrice || 0) / sumTime)?.toFixed(2) : '-',
                        testUnitPriceSum: Number(sumPrice).toFixed(2),
                      },
                    ]
                  : []) as any,
                success: res.success,
                total: Number(res.data?.total || 0),
              };
            }}
            toolBarRender={(action, { selectedRowKeys }) => [
              <Button
                type="primary"
                key={'confirmFinish'}
                icon={''}
                disabled={!selectedRowKeys?.length}
                onClick={async () => {
                  const res = await taskDeptKeepTimeInfoAccount(selectedRowKeys as number[]);
                  if (res?.success) {
                    message.success('操作成功');
                    action?.reload();
                  }
                }}
              >
                批量核算
              </Button>,
              <Button
                type="primary"
                key={'confirmFinish'}
                icon={''}
                disabled={!selectedRowKeys?.length}
                onClick={async () => {
                  edit(undefined, () => action?.reload(), selectedRowKeys as string[]);
                }}
              >
                批量编辑
              </Button>,
            ]}
            columns={[
              {
                valueType: 'index',
                title: '序号',
                width: 50,
                fixed: 'left',
              },
              {
                title: '设备名称',
                dataIndex: 'deviceName',
                width: 200,
                fixed: 'left',
                render(dom, entity, index, action, schema) {
                  return entity?.taskDeptKeepTimeSysInstrumentInfoListVo
                    ?.reduce((pre, next) => {
                      return [...pre, ...(next?.instrumentNewInfos || [])];
                    }, [])
                    ?.map((item, index) => {
                      return (
                        <div
                          key={index}
                          style={{
                            marginBottom: 5,
                          }}
                        >
                          <Tag>{item?.sbmc || '-'}</Tag>
                        </div>
                      );
                    });
                },
              },
              {
                title: '设备型号',
                dataIndex: '22',
                width: 120,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity?.taskDeptKeepTimeSysInstrumentInfoListVo
                    ?.reduce((pre, next) => {
                      return [...pre, ...(next?.instrumentNewInfos || [])];
                    }, [])
                    ?.map((item, index) => {
                      return (
                        <div
                          key={index}
                          style={{
                            marginBottom: 5,
                          }}
                        >
                          <Tag>{item?.sbxh || '-'}</Tag>
                        </div>
                      );
                    });
                },
              },
              {
                title: '序列号',
                width: 120,
                dataIndex: '33',
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity?.taskDeptKeepTimeSysInstrumentInfoListVo
                    ?.reduce((pre, next) => {
                      return [...pre, ...(next?.instrumentNewInfos || [])];
                    }, [])
                    ?.map((item, index) => {
                      return (
                        <div
                          key={index}
                          style={{
                            marginBottom: 5,
                          }}
                        >
                          <Tag>{item?.ccbh || '-'}</Tag>
                        </div>
                      );
                    });
                },
              },
              {
                title: '检测室',
                dataIndex: 'deptId',
                ...OnlyInSearch,
                valueType: 'treeSelect',
                fieldProps: {
                  multiple: false,
                  treeDefaultExpandAll: true,
                  fieldNames: {
                    label: 'deptName',
                    value: 'id',
                    children: 'child',
                  },
                },
                request: () => {
                  return orgDeptInfoTree().then((res) => {
                    return res?.data || [];
                  });
                },
              },
              {
                title: '检测室',
                dataIndex: 'deptName',
                align: 'center',
                hideInSearch: true,
              },
              {
                title: '检测内容',
                dataIndex: 'testItem',
                width: 300,
                hideInSearch: true,
              },
              // {
              //   title: '检测室',
              //   dataIndex: 'testProject',
              //   width: 300,
              //   hideInSearch: true,
              // },
              {
                title: '测试人员',
                width: 120,
                dataIndex: 'testUserInfo',
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity?.testUserInfo?.fullName || '-';
                },
              },
              {
                title: '测试人员',
                dataIndex: 'testUserId',
                valueType: 'select',
                hideInTable: true,
                renderFormItem(schema, config, form, action) {
                  return (
                    <ProFormSelect
                      noStyle
                      fieldProps={{
                        style: { width: '100%' },
                      }}
                      type="user"
                      query={{
                        roleId: ROLE_TYPE.testPerson,
                      }}
                    />
                  );
                },
              },
              {
                title: '开始日期',
                dataIndex: 'testStartTime',
                width: 150,
                valueType: 'date',
                hideInSearch: true,
                render(text, record, index, action) {
                  return record?.testStartTime
                    ? dayjs(record?.testStartTime)?.format('YYYY年 MM月 DD日')
                    : '-';
                },
              },
              {
                title: '结束日期',
                dataIndex: 'testEndTime',
                width: 150,
                valueType: 'date',
                hideInSearch: true,
                render(text, record, index, action) {
                  return record?.testEndTime
                    ? dayjs(record?.testEndTime)?.format('YYYY年 MM月 DD日')
                    : '-';
                },
              },
              {
                title: '测试时长',
                dataIndex: 'testDurationSum',
                width: 150,
                hideInSearch: true,
                render(text, record, index, action) {
                  return record?.testDurationSum ? record?.testDurationSum + '小时' : '-';
                },
              },
              {
                title: '测试单价（元）',
                dataIndex: 'testUnitPrice',
                width: 150,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return Number(entity?.testUnitPrice)?.toFixed(2);
                },
              },
              {
                title: '测试总价（元）',
                dataIndex: 'testUnitPriceSum',
                width: 150,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return Number(entity?.testUnitPriceSum)?.toFixed(2);
                },
              },
              {
                title: '附件',
                dataIndex: 'fileInfos',
                width: 320,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity.fileInfos?.map((item: BASE.FileInfo) => {
                    return <FileDownload key={item.id} item={item} />;
                  });
                },
              },
              {
                title: '备注',
                dataIndex: 'remark',
                width: 320,
                hideInSearch: true,
              },
              {
                title: '状态',
                dataIndex: 'statusInfo',
                width: 120,
                hideInSearch: true,
              },
              {
                title: '操作',
                valueType: 'option',
                fixed: 'right',
                render(_, entity, index, action) {
                  return (
                    <Action>
                      {(entity?.statusInfo === '待核算' || entity?.statusInfo === '已核算') && (
                        <Text
                          onClick={() => {
                            alert_detail({
                              row: {
                                ...entity,
                                testAllTime: sumTestDuration(
                                  entity?.taskDeptKeepTimeChildInfoListVo || [],
                                ),
                                instrumentList:
                                  entity?.taskDeptKeepTimeSysInstrumentInfoListVo?.reduce(
                                    (pre, next) => {
                                      const list = next?.instrumentNewInfos?.map((k) => {
                                        return {
                                          sysName: next?.sysName,
                                          fieldName: next?.fieldName,
                                          ...k,
                                        };
                                      });
                                      return [...(pre || []), ...(list || [])];
                                    },
                                    [],
                                  ),
                              },
                            });
                          }}
                        >
                          详情
                        </Text>
                      )}

                      {entity?.statusInfo === '待核算' && (
                        <Text onClick={() => edit(entity, () => action?.reload())}>编辑</Text>
                      )}
                      {entity?.statusInfo === '待核算' && (
                        <Text.Action
                          type="danger"
                          confirm
                          confirmText={`确定核算吗？`}
                          request={{
                            service: () =>
                              taskDeptKeepTimeInfoAccount([entity.id as unknown as number]),
                          }}
                          onSuccess={() => {
                            message.success('操作成功');
                            action?.reload();
                          }}
                        >
                          核算
                        </Text.Action>
                      )}
                    </Action>
                  );
                },
              },
            ]}
          />
        </>
      ),
    },
  ];

  return (
    <Modal
      title="收支管理"
      width={'80%'}
      footer={
        <Button
          type="primary"
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            rest?.onFinish?.(basicInfoForm?.current?.getFieldsValue());
            rest?.onOk?.(e);
          }}
        >
          确定
        </Button>
      }
      {...rest}
    >
      <ProForm formRef={basicInfoForm} submitter={false} layout="horizontal">
        <Tabs defaultActiveKey="1"  activeKey={activeKey} onChange={setActiveKey} items={items} destroyInactiveTabPane={true}/>
      </ProForm>
    </Modal>
  );
};

const alert_IncomeAndExpend = launch(AttorneyDetail);

export default alert_IncomeAndExpend;
