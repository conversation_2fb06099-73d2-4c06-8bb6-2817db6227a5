import {
  ProDescriptions as AntdProDescriptions,
  ProDescriptionsItemProps,
  ProDescriptionsProps as AntdProDescriptionsProps,
  ProSchemaValueEnumMap,
  ProSchemaValueEnumObj,
} from '@ant-design/pro-components';
import dayjs from 'dayjs';
import lodash, { get, isEqual, PropertyPath } from 'lodash';
import { compareList } from '../proTable';

export type ProDescriptionsProps<T = Record<string, any>> = AntdProDescriptionsProps<
  T,
  GlobalValueType
> & {
  newdataSource?: Record<string, any>;
  labelWidth?: number;
  contentWidth?: number;
};

export const formatValue = (
  item: ProDescriptionsItemProps<Record<string, any>, 'text'>,
  entity: Record<string, any>,
) => {
  const { dataIndex, title, valueEnum, valueType } = item;
  const value = get(entity, dataIndex as PropertyPath);

  if (value === null || value === undefined) {
    return '-';
  }
  if (valueEnum) {
    const _valueEnum =
      Object.prototype.toString.call(valueEnum) === '[object Function]'
        ? (
            valueEnum as (row: Record<string, any>) => ProSchemaValueEnumObj | ProSchemaValueEnumMap
          )?.(value)
        : valueEnum;

    if (Object.prototype.toString.call(_valueEnum) === '[object Object]') {
      return (valueEnum as ProSchemaValueEnumObj)[value as string];
    } else {
      const _valueEnum = valueEnum as ProSchemaValueEnumMap;

      const _value = _valueEnum.get(value);

      if (typeof _value === 'object' && _value !== null && 'text' in _value) {
        return _value.text;
      } else {
        return _value;
      }
    }
  }
  if (!valueType) {
    return value;
  }

  switch (valueType) {
    case 'date':
      return dayjs(value).format('YYYY-MM-DD');
    case 'dateTime':
      return dayjs(value).format('YYYY-MM-DD HH:mm:ss');
    case 'dateRange':
      return value.map((item: any) => dayjs(item).format('YYYY-MM-DD')).join(' ~ ');
    case 'dateTimeRange':
      return value.map((item: any) => dayjs(item).format('YYYY-MM-DD HH:mm:ss')).join(' ~ ');
    case 'time':
      return dayjs(value).format('HH:mm:ss');
    case 'money':
      return `￥${value}`;
    case 'progress':
      return `${value}%`;
    case 'percent':
      return `${value}%`;
    default:
      return value;
  }
};

function ProDescriptions<T extends Record<string, any>, ValueType = GlobalValueType>(
  props: ProDescriptionsProps,
) {
  const { newdataSource, labelWidth = 140, contentWidth = 310, columns, ...rest } = props;

  console.log(newdataSource, 'newdataSource')
  console.log(props?.dataSource, 'dataSource')
  console.log(get(null, 'oopp'), '=====')

  const _columns: ProDescriptionsItemProps[] = (columns || [])?.map((item: any, index) => {
  
    if (!newdataSource) return item;
    return {
      ...item,
      render(dom, entity, index, action, schema) {
        const renderText = item?.render
          ? item.render(dom, entity, index, action, schema)
          : formatValue(item, entity);

        if (item.dataIndex && Array.isArray(entity?.[item.dataIndex as string])) {
          const result = compareList(
            props?.dataSource?.[item.dataIndex as string],
            newdataSource[item.dataIndex as string],
          );
          return (
            <>
              {result?.map((itm: any, index) => {
                return item?.render
                  ? item?.render(dom, { [item.dataIndex as string]: [itm] }, index, action, schema)
                  : formatValue(item, itm);
              })}
            </>
          );
        } else {
          return (
            <>
              {renderText}
              {item.dataIndex &&
                newdataSource &&
                lodash.has(newdataSource, item.dataIndex) &&
                !isEqual(
                  get(entity, item.dataIndex as PropertyPath) || null,
                  get?.(newdataSource, item.dataIndex as PropertyPath) || null,
                ) && (
                  <span style={{ color: 'red', marginLeft: 5 }}>
                    {`-> `}
                    {item?.render
                      ? item.render(
                          get(newdataSource, item.dataIndex),
                          newdataSource,
                          index,
                          action,
                          schema,
                        )
                      : formatValue(item, newdataSource)}
                  </span>
                )}
            </>
          );
        }
      },
      labelStyle: {
        width: labelWidth,
        ...item.labelStyle,
      },
      contentStyle: {
        width: contentWidth,
        ...item.contentStyle,
      },
    };
  });

  // @ts-ignore
  return (
    <AntdProDescriptions<T, GlobalValueType>
      columns={_columns}
      column={2}
      labelStyle={{
        width: 177,
      }}
      contentStyle={{
        width: 390,
      }}
      {...rest}
    />
  );
}

export default ProDescriptions;
