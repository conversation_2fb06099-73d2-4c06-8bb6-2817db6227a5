import { Tabs } from 'antd';
import useDetail from './useDetail';

export type CommonType = BASE.EntrustInfoVO &
  BASE.TaskInfoVO & {
    taskType: string;
  };

export type DetailCommonProps = {
  attorneyId?: string; // 委托书id
  taskId?: string; // 任务id
  isExamine?: boolean;  // 是否审核界面展示的委托书详情
  isNetworkTaskExamine?: boolean; // 是否审核界面展示的进网技术信息详情
  formatResult?: (row: CommonType) => CommonType & { [k: string]: any };
};

const DetailCommon: React.FC<DetailCommonProps> = ({ attorneyId, taskId, isExamine = false, isNetworkTaskExamine = false,  formatResult }) => {
  const items = useDetail({
    attorneyId,
    taskId,
    isExamine,
    isNetworkTaskExamine,
    formatResult,
  });

  return <Tabs defaultActiveKey="基本信息" items={items} />;
};
export default DetailCommon;
