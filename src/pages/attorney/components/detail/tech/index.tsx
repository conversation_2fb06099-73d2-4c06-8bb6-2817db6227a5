import { ATTORNEY_TYPE } from '@/enums/attorney';
import { useModel } from '@umijs/max';
import ModeDetail, {NormalTechDetail} from './mode';
import ReportDetail from './report';
import TestDetail from './test';
import { CommonType } from '..';
import TecOtherDetail from './tecOther';
import EnterDetail from './enter';
import { TASK_TYPE } from '@/enums/task';

type BaseDetailProps = {
  row?: CommonType;
  isNetworkTaskExamine?: boolean; // 进网审核时展示
};

const TechDetail: React.FC<BaseDetailProps> = ({ row, isNetworkTaskExamine = false }) => {
  const { initialState } = useModel('@@initialState');

  return (
    <>
       {/* 进网-详情 */}
       {row?.type === ATTORNEY_TYPE.enter  && <EnterDetail row={row as BASE.EntrustInfoVO} />}
      {isNetworkTaskExamine ? null : <TestDetail row={row} />}
      {isNetworkTaskExamine ? null : (row?.taskType !== TASK_TYPE.记录任务 && <ReportDetail row={row} />)}
      {/* {row?.type === ATTORNEY_TYPE.antenna && <AntennaTechDetail row={row as BASE.EntrustInfoVO} />} */}
      {isNetworkTaskExamine ? null : (row?.type === ATTORNEY_TYPE.model && <ModeDetail row={row as BASE.EntrustInfoVO} />)}
      {isNetworkTaskExamine ? null : (row?.type === ATTORNEY_TYPE.normal && <NormalTechDetail row={row as BASE.EntrustInfoVO} />)}

      {isNetworkTaskExamine ? null :  <TecOtherDetail row={row}  />}
    </>
  );
};

export default TechDetail;
