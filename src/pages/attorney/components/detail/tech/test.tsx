import Title from '@/components/title';
import { NumBool, NumBoolObj } from '@/enums';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { Tag } from 'antd';
import { chunk, get } from 'lodash';
import { useMemo } from 'react';
import { CommonType } from '..';
import MyProDescriptions from '../../proDescriptions';

type BaseDetailProps = {
  row?: CommonType;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str || "{}"), key)) ?? '--';
};

const TestDetail: React.FC<BaseDetailProps> = ({ row, isMode = false }) => {
  const columns: ProDescriptionsProps['columns'] = useMemo(() => {
    if (row?.type === ATTORNEY_TYPE.model) {
      return [
        {
          title: '认证标识',
          dataIndex: 'certificaMark',
          span: 3,
        },
        {
          title: '符合性声明',
          dataIndex: 'tfAccordStatement',
          valueEnum: NumBoolObj,
          span: 3,
        },
        {
          title: '符合性声明内容',
          dataIndex: 'accordStatementInfo',
          valueEnum: {
            0: '判定符合性时不考虑测量不确定度，直接以测量结果值（或测量结果的平均值）作为符合性声明的判定规则依据；',
            1: '判定符合性时考虑测量不确定度，当考虑测量不确定度的测量结果值（或测量结果的平均值）无法判定其符合性时，统一判定为符合；',
            2: '判定符合性时考虑测量不确定度，当考虑测量不确定度的测量结果值（或测量结果的平均值）无法判定其符合性时，统一判定为不符合；',
          },
          span: 3,
        },
      ];
    }

    const _tempColumns: ProDescriptionsProps['columns'] = [
      {
        title: '检测项目',
        dataIndex: 'inspectionItem',
        span: 2,
      },
      {
        title: '标准编号',
        dataIndex: 'standardInfo',
        span: 2,
      },
      {
        title: '检测方法',
        dataIndex: 'testMethod',
        span: 2,
      },
      {
        title: '认证标识',
        dataIndex: 'certificaMark',
        span: 2,
      },
      {
        title: '符合性声明',
        dataIndex: 'tfAccordStatement',
        valueEnum: NumBoolObj,
        span: 2,
      },
      {
        title: '符合性声明内容',
        dataIndex: 'accordStatementInfo',
        valueEnum: {
          0: '判定符合性时不考虑测量不确定度，直接以测量结果值（或测量结果的平均值）作为符合性声明的判定规则依据；',
          1: '判定符合性时考虑测量不确定度，当考虑测量不确定度的测量结果值（或测量结果的平均值）无法判定其符合性时，统一判定为符合；',
          2: '判定符合性时考虑测量不确定度，当考虑测量不确定度的测量结果值（或测量结果的平均值）无法判定其符合性时，统一判定为不符合；',
        },
        span: 2,
      },
      {
        title: '判定依据',
        dataIndex: 'judgBasis',
        span: 2,
      },
      {
        title: '是否分包',
        dataIndex: 'tfSubcontract',
        valueEnum: NumBoolObj,
      },
      {
        title: '分包信息',
        dataIndex: 'subcontractInfo',
      },
      {
        title: '分包方',
        dataIndex: 'subcontractor',
      },
      
    ];

    if (row?.type === ATTORNEY_TYPE.antenna) {
      // 去掉_tempColumns中第1列，并追加两列

      _tempColumns.splice(
        0,
        1,
        {
          title: '检测项目',
          ellipsis: true,
          dataIndex: ['buEntrustAntennaInfo', 'Items'],
          render(dom, entity, index, action, schema) {
            // keys每三个换行
            return (
              (entity.buEntrustAntennaInfo?.Items && 
                (entity?.buEntrustAntennaInfo?.Items).map(
                  (item: string[], index: number) => {
                    return <Tag key={index}>{item}</Tag>;
                  },
                )
              ) ||
              '--'
            );
          },
        },
        {
          title: '驻波测试频段',
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '驻波'],
          render(dom, entity, index, action, schema) {
            return getStrValue(entity.buEntrustAntennaInfo?.testItem, '驻波');
          },
        },
        {
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '隔离'],
          title: '隔离测试频段',
          render(dom, entity, index, action, schema) {
            return getStrValue(entity.buEntrustAntennaInfo?.testItem, '隔离');
          },
        },
        {
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '互调'],
          title: '互调测试频段',
          render(dom, entity, index, action, schema) {
            return getStrValue(entity.buEntrustAntennaInfo?.testItem, '互调');
          },
        },
        {
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '方向图', '测试频点角度'],
          title: '方向图测试频点角度',
          render(dom, entity, index, action, schema) {
            return getStrValue(entity.buEntrustAntennaInfo?.testItem, '方向图.测试频点角度');
          },
        },
        {
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '方向图', '需计算的参数'],
          title: '方向图需计算的参数',
          render(dom, entity, index, action, schema) {
            return getStrValue(entity.buEntrustAntennaInfo?.testItem, '方向图.需计算的参数');
          },
        },
        {
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '合成波束'],
          title: '合成波束是否提供功分板',
          render(dom, entity, index, action, schema) {
            return NumBool[getStrValue(entity.buEntrustAntennaInfo?.testItem, '合成波束')];
          },
        },
        {
          dataIndex: ['buEntrustAntennaInfo', 'testItems', '其他'],
          title: '其他测试项',
          render(dom, entity, index, action, schema) {
            return getStrValue(entity.buEntrustAntennaInfo?.testItem, '其他');
          },
        },
      );
    }

    return _tempColumns;
  }, [row]);

  const newdataSource = {
    ...JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}'),
    buEntrustAntennaInfo: {
      ...JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustAntennaInfo,
      testItems: JSON.parse(JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustAntennaInfo?.testItem || '{}'),
      Items: Object.keys(JSON.parse(JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustAntennaInfo?.testItem || '{}'))
    },
  }

  return (
    <>
      <Title title="检测信息" />
      <MyProDescriptions
        column={1}
        columns={columns}
        dataSource={{
          ...row,
          buEntrustAntennaInfo: {
            ...row?.buEntrustAntennaInfo,
            Items: Object.keys(JSON.parse(row?.buEntrustAntennaInfo?.testItem || '{}'))
          },
        }}
        newdataSource={row?.entrustApplyInfo ? newdataSource : null}
        labelStyle={{
          width: 177,
        }}
        contentStyle={{
          width: 390,
        }}
        bordered
        layout="horizontal"
      />
    </>
  );
};

export default TestDetail;
