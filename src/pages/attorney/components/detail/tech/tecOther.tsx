import FileDownload from '@/components/fileDownload';
import Title from '@/components/title';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { Table } from 'antd';
import { ProDescriptionsProps } from '../../proDescriptions';
import MyProDescriptions from '../ProDescription';
import ProTable from '../../proTable';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

type LinelossType = {
  smaIpexKey: string;
  smaIpexValue: string;
};

const TecOtherDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  
  let smaIpexValues: string[] = [];
  let smaIpexKeys: string[] = [];
  let smaIpexValues2: string[] = [];
  let smaIpexKey2: string[] = [];
  if(row?.type === ATTORNEY_TYPE.enter) {
    smaIpexValues = row?.buEntrustIntoNetworkInfo?.smaIpexValues?.split(',') || [];
    smaIpexKeys = row?.buEntrustIntoNetworkInfo?.smaIpexKeys?.split(',') || [];
    smaIpexValues2 =  row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo?.smaIpexValues?.split(',');
    smaIpexKey2 = row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo?.smaIpexKeys?.split(',');
  }
  if(row?.type === ATTORNEY_TYPE.normal) {
    smaIpexValues = row?.smaIpexValues?.split(',') || [];
    smaIpexKeys = row?.smaIpexKeys?.split(',') || [];
    smaIpexValues2 =  row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.smaIpexValues?.split(',');
    smaIpexKey2 = row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.smaIpexKeys?.split(',');
  }
  // 原始数据处理 
  const lineloss: Array<LinelossType> = smaIpexKeys?.reduce((acc: LinelossType[], cur, index) => {
        return [
          ...acc,
          {
            smaIpexKey: cur,
            smaIpexValue: smaIpexValues?.[index] as string,
            id: index + 1
          },
        ];
      }, [] as LinelossType[]) || [];
  // 修订数据
  const newLineloss: Array<LinelossType> = smaIpexKey2?.reduce((acc: LinelossType[], cur, index) => {
        return [
          ...acc,
          {
            smaIpexKey: cur,
            smaIpexValue: smaIpexValues2?.[index] as string,
            id: index + 1
          },
        ];
      }, [] as LinelossType[]) || [];


  const columns: ProDescriptionsProps['columns'] = [
    {
      title: '技术文件',
      span: 24,
      dataIndex: 'fileInfos',
      // valueType:'upload',
      render(dom, entity, index, action, schema) {
        return entity.fileInfos?.map((item: BASE.FileInfo) => {
          return <FileDownload key={item.id} item={item} />;
        });
      },
    },
  ];

  return (
    <>
      <Title title="其他信息" />
      <MyProDescriptions columns={columns} dataSource={row} />

      {(row?.type === ATTORNEY_TYPE.enter || row?.type === ATTORNEY_TYPE.normal) && (
        <ProTable
          // dataSource={lineloss || []}
          search={false}
        options={false}
        style={{
          marginTop: 10
        }}
        pagination={false}
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        dataSource={(lineloss || [])}
        newdataSource = { row?.entrustApplyInfo?.entrustJson ? newLineloss : undefined }
        rowKey="rowId"
          columns={[
            {
              dataIndex: 'smaIpexKey',
              title: 'SMA转ipex线损须率',
            },
            {
              dataIndex: 'smaIpexValue',
              title: 'SMA转pex线损须值',
            },
          ]}
        />
      )}
    </>
  );
};

export default TecOtherDetail;
