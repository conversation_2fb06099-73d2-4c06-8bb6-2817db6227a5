import FileDownload from '@/components/fileDownload';
import Tag from '@/components/tag';
import Title from '@/components/title';
import { nanoid, ProDescriptions } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import MyProDescriptions from '../ProDescription';
import ProTable from '../../proTable';
import { noticeInfoCount } from '@/services/base/msgNotice';

const ModeDetail: React.FC<{ row: BASE.EntrustInfoVO }> = ({ row }) => {
  console.log(row,'row-1-1-技术参数')
  return (
    <>
      <Title title="验证记录" />
      <MyProDescriptions
        columns={[
          {
            title: '邮政编码',
            dataIndex: 'postalCode',
            span:2
          },
          {
            title: '检测标准',
            dataIndex: 'testStandard',
            span:2
          },
          {
            title: '主要功能',
            dataIndex: 'mainFunction',
          },
        ]}
        dataSource={row?.buEntrustApproveInfo || {}}
        newdataSource={
          row?.entrustApplyInfo?.entrustJson &&
          JSON.parse(row?.entrustApplyInfo?.entrustJson)?.buEntrustApproveInfo
        }
      />

      <Title title="技术参数" />
      <ProTable
        search={false}
        options={false}
        pagination={false}
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        dataSource={(row?.buEntrustApproveInfo?.verifiRecordVOList || []).map((i, index) => ({
          ...i,
          id: index
        }))}
        newdataSource={row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson)?.buEntrustApproveInfo?.verifiRecordVOList?.map((i:any, index)=>({...i, id: index}))}

        rowKey="rowId"
        columns={[
          {
            title: '序号',
            valueType: 'index',
          },
          {
            title: '制式',
            dataIndex: 'standard',
          },
          {
            title: '频段(MHz)',
            render(dom, entity, index, action, schema) {
              return entity?.frequencyOne + '-' + entity?.frequencyTwo;
            },
          },
          {
            title: '上下行',
            dataIndex: 'upDown',
          },
          {
            title: '带宽',
            dataIndex: 'bandwidth',
          },
          {
            title: '限值要求',
            dataIndex: 'limitRequire',
          },
        ]}
      />

      {/* 核准信息 */}
      <Title title="核准信息" />
      <MyProDescriptions
        dataSource={row?.buEntrustApproveInfo || {}}
        newdataSource={
          (row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson))?.buEntrustApproveInfo
        }
        columns={[
          {
            title: '核准文件',
            dataIndex: 'fileInfo',
            span: 2,
            render(dom, entity, index, action, schema) {
              return (
                <FileDownload
                item={entity?.fileInfo}
                  key={entity?.fileInfo?.id}
                />
              );
            },
          },
          {
            title: '检测标准',
            dataIndex: 'testStandard',
            span:2
          },
          {
            title: '判定依据',
            dataIndex: 'implementationStandard',
            span:2
          },
        ]}
      />

      {/* 验证记录 */}
      <Title title="补充验证记录" />
      <MyProDescriptions
        columns={[
          {
            title: '验证日期',
            dataIndex: 'verificationDate',
            valueType: 'date',
            span: 24,
          },
          {
            title: '样品外观是否一致',
            dataIndex: 'tfAppearanceSampleConsistent',
            render(dom, entity, index, action, schema) {
              return <Tag>{entity?.tfAppearanceSampleConsistent ? '是' : '否'}</Tag>;
            },
          },
          {
            title: '样品最大输出功率是否一致',
            dataIndex: 'tfWhetherMaximumOutputSampleConsistent',
            render(dom, entity, index, action, schema) {
              return <Tag>{entity?.tfWhetherMaximumOutputSampleConsistent ? '是' : '否'}</Tag>;
            },
          },
          {
            title: '样品频率范围是否一致',
            dataIndex: 'tfHetherSampleFrequencyRangeConsistent',
            render(dom, entity, index, action, schema) {
              return <Tag>{entity?.tfHetherSampleFrequencyRangeConsistent ? '是' : '否'}</Tag>;
            },
          },
          {
            title: '样品ACLR测试结果是否符合要求',
            dataIndex: 'tfWhetherSampleAclrResultsMeetRequirement',
            render(dom, entity, index, action, schema) {
              return <Tag>{entity?.tfWhetherSampleAclrResultsMeetRequirement ? '是' : '否'}</Tag>;
            },
          },
          {
            title: '处理意见',
            dataIndex: 'handlOpinion',
          },
          {
            title: '备注',
            dataIndex: 'remark',
          },
        ]}
        dataSource={row?.buEntrustApproveInfo || {}}

        newdataSource={row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson).buEntrustApproveInfo}

      />

      <ProTable
        search={false}
        options={false}
        pagination={false}
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        style={{
          marginTop: '10px',
        }}
        newdataSource={row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson)?.buEntrustApproveInfo?.verifiRecordVOListT?.map((i:any, index: number)=>({...i, id: index,}))}

        dataSource={(row?.buEntrustApproveInfo?.verifiRecordVOListT || []).map((i, index) => ({
          ...i,
          id: index,
          rowId: nanoid(),
        }))}
        rowKey="rowId"
        columns={[
          {
            title: '序号',
            valueType: 'index',
            editable: false,
          },
          {
            title: '制式',
            dataIndex: 'standard',
            editable: false,
          },
          {
            title: '频段(MHz)',
            editable: false,
            render(dom, entity, index, action, schema) {
              return (
                (entity?.frequencyOne &&
                  entity?.frequencyTwo &&
                  entity?.frequencyOne + '-' + entity?.frequencyTwo) ||
                '-'
              );
            },
          },
          {
            title: '带宽',
            dataIndex: 'bandwidth',
            editable: false,
          },
          {
            title: '限值要求',
            dataIndex: 'limitRequire',
            editable: false,
          },
          {
            title: '样品编号',
            dataIndex: 'sampleNumber',
            editable: false,
          },
          {
            title: '上下行',
            dataIndex: 'upDown',
            editable: false,
          },
          {
            title: '高频点（MHz）',
            dataIndex: 'frequencyPointHight',
          },
          {
            title: '到达天线端口高电平值(dBm)',
            dataIndex: 'antennaLevelHight',
          },
          {
            title: '低频点（MHz）',
            dataIndex: 'frequencyPointLow',
          },
          {
            title: '到达天线端口低电平值(dBm)',
            dataIndex: 'antennaLevelLow',
          },
        ]}
      />

      {/* TODO:待后台返回后完善 */}
      {!!row?.buEntrustApproveInfo?.materialFileInfos?.length && <>
      <Title title="项目资料" />
      <MyProDescriptions
        column={1}
        columns={[
          {
            title: '项目资料',
            dataIndex: 'materialFileInfos',
            render(dom, entity, index, action, schema) {
              return entity.buEntrustApproveInfo?.materialFileInfos?.map((item: BASE.FileInfo) => {
                return (
                  <FileDownload
                    key={item.id}
                    item={item}
                    preview
                  />
                );
              });
            },
          },
        ]}
        dataSource={row}
      />
      </>}
    </>
  );
};

export default ModeDetail;


export const NormalTechDetail: React.FC<{ row: BASE.EntrustInfoVO }> = ({ row }) => {
  console.log(row,'row-1-1-技术参数')
  return (
    <>
      <Title title="技术参数" />
      <ProTable
        search={false}
        options={false}
        pagination={false}
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        dataSource={(row?.entrustTechnicalParametersintoInfoVos
          || [])}
        newdataSource={row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson)?.entrustTechnicalParametersintoInfoVos?.map((item) => {
          return {
            ...item,
            id: item?.id || nanoid
          }
        })}

        rowKey="rowId"
        columns={[
          {
            title: '序号',
            valueType: 'index',
          },
          {
            title: '制式',
            dataIndex: 'standardInfo',
          },
          {
            title: '频段(MHz)',
            dataIndex: 'frequencyBand',
          },
        ]}
      />
    </>
  );
};
