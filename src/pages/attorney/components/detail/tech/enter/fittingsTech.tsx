import Title from '@/components/title';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { get } from 'lodash';
import MyProDescriptions from '../../../proDescriptions';

type BaseDetailProps = {
  row?: any;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const FittingsTechDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: '是否支持配件',
      dataIndex: 'supportAccessoriesInfo',
      span: 3,
    },
    {
      title: '电池类型',
      dataIndex: 'batteryType',
      span: 1.5,
    },
    {
      title: '电池型号',
      dataIndex: 'batteryModel',
      span: 1.5,
    },
    {
      title: '电池额定容量',
      dataIndex: 'batteryRatedCapacity',
      span: 1.5,
    },
    {
      title: '电池标称电压',
      dataIndex: 'batteryNominalVoltage',
      span: 1.5,
    },
    {
      title: '电池生产厂家',
      dataIndex: 'batteryManufacturer',
      span: 3,
    },
    {
      title: '电池制造商',
      dataIndex: 'batteryProducer',
      span: 3,
    },
    {
      title: '其他配件',
      dataIndex: 'otherAccessories',
      span: 3,
    },
    {
      title: '适配器是否标配',
      dataIndex: 'adapterIncluded',
      span: 1,
    },
    {
      title: '型号',
      dataIndex: 'model',
      span: 1,
    },
    {
      title: '产地',
      dataIndex: 'origin',
      span: 1,
    },
    {
      title: '生产厂',
      dataIndex: 'manufacturer',
      span: 3,
    },
    {
      title: '制造商',
      dataIndex: 'producer',
      span: 3,
    },
    {
      title: 'CCC证书号',
      dataIndex: 'cccCertificateNumber',
      span: 3,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      span: 3,
    },
  ];

  return (
    <>
      <Title title="配件" />
      <MyProDescriptions
        column={3}
        columns={columns}
        labelStyle={{
          width: 177,
        }}
        contentStyle={{
          width: 390,
        }}
        bordered
        layout="horizontal"
        dataSource={row?.buEntrustIntoNetworkInfo }
        newdataSource={
          JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo || []
        }
      />
    </>
  );
};

export default FittingsTechDetail;
