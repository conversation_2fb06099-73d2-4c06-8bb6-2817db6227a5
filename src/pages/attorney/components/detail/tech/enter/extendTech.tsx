import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import Title from '@/components/title';
import { NumBool, NumBoolObj } from '@/enums';
import MyProDescriptions from '../../../proDescriptions';
import { chunk, get } from 'lodash';
import { Tag } from 'antd';

type BaseDetailProps = {
  row?: any;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const ExtendTechDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const columns:ProDescriptionsProps['columns'] =  [
    {
      title: "CMMB广播式手机电视",
      dataIndex: "cmmbMobileTv",
      span: 2
    },
    {
      title: "本地连接方式",
      dataIndex: "localConnectionMethods",
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.localConnectionMethods?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: "其他",
      dataIndex: "localConnectionMethodsOther",
      span: 1
    },
    {
      title: "照相/摄像功能",
      dataIndex: "cameraFunction",
      span: 1
    },
    {
      title: "摄像头数量",
      dataIndex: "cameraQuantity",
      span: 1
    },
    {
      title: "EDGE",
      dataIndex: "edge",
      span: 1
    },
    {
      title: "EDGE-CLASS",
      dataIndex: "edgeClass",
      span: 1
    },
    {
      title: "卫星导航定位系统",
      dataIndex: "satelliteNavigationSystem",
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.satelliteNavigationSystem?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: "其它",
      dataIndex: "satelliteNavigationSystemOther",
      span: 1
    },
    {
      title: "其它功能",
      dataIndex: "otherFunctions",
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.otherFunctions?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: "电视功能",
      dataIndex: "tvFunction",
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.tvFunction?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: "其它",
      dataIndex: "tvFunctionOther",
      span: 1
    },
    
  ];

  return (
    <>
      <Title title="扩展功能" />
      <MyProDescriptions column={2} columns={columns} 
      labelStyle={{
        width: 177,
      }}
      contentStyle={{
        width: 390,
      }}
      bordered
      layout="horizontal"
      dataSource={row?.buEntrustIntoNetworkInfo }
      newdataSource={
        JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo || []
      }
       />
    </>
  );
};

export default ExtendTechDetail;
