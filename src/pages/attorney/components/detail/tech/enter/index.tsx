import NbltTechDetail from './nbltTech';
import WxTechDetail from './wxTech';
import BaseTechDetail from './baseTech';
import BaseBusinessTechDetail from './baseBusinessTech';
import ExtendTechDetail from './extendTech';
import FittingsTechDetail from './fittingsTech';
import { ProForm, ProFormInstance, ProFormItem } from '@ant-design/pro-components';
type BaseFormProps = {
  required?: boolean;
  disabled?: boolean;
  formRefs?: React.MutableRefObject<Record<string, ProFormInstance | null>>;
  row?: BASE.EntrustInfoVO
};

const EnterDetail: React.FC<BaseFormProps> = (props) => {
  const { required, disabled = false, formRefs, row } = props;
  return (
    <>
   
    {/* 进网-NB-IoT终端技术规格 */}
    <NbltTechDetail row={row as BASE.EntrustInfoVO}/>
    {/* 进网-卫星移动终端技术规格 */}
    <WxTechDetail row={row as BASE.EntrustInfoVO}/>
    {/* 进网-技术规格 */}
    <BaseTechDetail row={row as BASE.EntrustInfoVO}/>
    {/* 进网-基本业务和功能 */}
    <BaseBusinessTechDetail row={row as BASE.EntrustInfoVO}/>
     {/* 进网-扩展功能 */}
     <ExtendTechDetail row={row as BASE.EntrustInfoVO}/>
      {/* 进网-配件 */}
      <FittingsTechDetail row={row as BASE.EntrustInfoVO}/>
    
    </>
  );
};
export default EnterDetail;
