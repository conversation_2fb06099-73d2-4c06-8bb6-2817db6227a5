import Title from '@/components/title';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { Tag } from 'antd';
import { get } from 'lodash';
import MyProDescriptions from '../../../proDescriptions';

type BaseDetailProps = {
  row?: any;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const NbltTechDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: 'NB-IoT版本',
      dataIndex: 'nbIotVersion',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.nbIotVersion?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'NB-IoT频段',
      dataIndex: 'nbIotBand',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.nbIotBand?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'NB-IoT传输能力等级',
      dataIndex: 'nbIotTransmissionAbility',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.nbIotTransmissionAbility?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '物联网模组供应商',
      dataIndex: 'iotModuleSupplier',
      span: 1.5,
    },
    {
      title: '物联网模组型号',
      dataIndex: 'iotModuleModel',
      span: 1.5,
    },
    {
      title: '核心芯片（芯片组）供应商',
      dataIndex: 'coreChipSupplier',
      span: 1.5,
    },
    {
      title: '核心芯片（芯片组）型号',
      dataIndex: 'coreChipModel',
      span: 1.5,
    },
    {
      title: '基带芯片供应商',
      dataIndex: 'basebandChipSupplier',
      span: 1.5,
    },
    {
      title: '基带芯片型号',
      dataIndex: 'basebandChipModel',
      span: 1.5,
    },
    {
      title: '射频芯片供应商',
      dataIndex: 'rfChipSupplier',
      span: 1.5,
    },
    {
      title: '射频芯片型号',
      dataIndex: 'rfChipModel',
      span: 1.5,
    },
    {
      title: 'NB-IoT业务',
      dataIndex: 'nbIotBusiness',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.nbIotBusiness?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '是否已承载业务应用',
      dataIndex: 'businessApplicationSupported',
      span: 1,
    },
    {
      title: '是否承载可安装的业务应用程序',
      dataIndex: 'installableBusinessApplicationSupported',
      span: 1,
    },
    {
      title: '是否传输用户信息',
      dataIndex: 'userInformationTransmissionSupported',
      span: 1,
    },
    {
      title: '是否支持蜂窝通信功能',
      dataIndex: 'otherCellularFunctionSupported',
      span: 3,
    },
  ];

  return (
    <>
      <Title title="NB-IoT终端技术规格" />
      <MyProDescriptions
        column={3}
        columns={columns}
        labelStyle={{
          width: 177,
        }}
        contentStyle={{
          width: 390,
        }}
        bordered
        layout="horizontal"
        dataSource={row?.buEntrustIntoNetworkInfo}
        newdataSource={
          JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo || []
        }
      />
    </>
  );
};

export default NbltTechDetail;
