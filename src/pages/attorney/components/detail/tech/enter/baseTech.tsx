import Title from '@/components/title';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { Tag } from 'antd';
import { get } from 'lodash';
import MyProDescriptions from '../../../proDescriptions';

type BaseDetailProps = {
  row?: any;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const BaseTechDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: 'GSM',
      dataIndex: 'bandGsm',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.bandGsm?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'CDMA',
      dataIndex: 'bandCdma',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.bandCdma?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'WCDMA',
      dataIndex: 'bandWcdma',
      span: 1.5,
    },
    {
      title: 'WCDMA其他',
      dataIndex: 'bandWcdmaOther',
      span: 1.5,
    },
    {
      title: 'cdma2000',
      dataIndex: 'bandCdma2000',
      span: 1.5,
    },
    {
      title: 'cdma2000其他',
      dataIndex: 'bandCdma2000Other',
      span: 1.5,
    },
    {
      title: 'TD-SCDMA',
      dataIndex: 'bandTdScdma',
      span: 1.5,
    },
    {
      title: 'TD-SCDMA其他',
      dataIndex: 'bandTdScdmaOther',
      span: 1.5,
    },
    {
      title: 'LTE FDD',
      dataIndex: 'bandLtefdd',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.bandLtefdd?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'TD-LTE',
      dataIndex: 'bandTdLte',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.bandTdLte?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'NR NSA',
      dataIndex: 'bandNrNsa',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.bandNrNsa?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'NR SA',
      dataIndex: 'bandNrSa',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.bandNrSa?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: 'SIM/UIM卡槽和待机方式',
      dataIndex: 'simUimSlotStandbyMode',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.simUimSlotStandbyMode?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'simUimCardSlotStandbyModeOther',
      span: 1.5,
    },
    {
      title: 'eSIM支持的电信运营商',
      dataIndex: 'esimSupportedCarriers',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.esimSupportedCarriers?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '本设备预置的eSIM证书张数',
      dataIndex: 'preInstalledEsimCertificates',
      span: 1,
    },
    {
      title: '本设备预置的eSIM证书机构',
      dataIndex: 'preInstalledEsimCertifyingAuthority',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.preInstalledEsimCertifyingAuthority?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其它',
      dataIndex: 'esimCertificatePreinstalledDeviceOther',
      span: 1,
    },
    {
      title: '支持eSIM机构',
      dataIndex: 'supportedEsimAuthorities',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.supportedEsimAuthorities?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其它',
      dataIndex: 'supportEsimOther',
      span: 1.5,
    },
    {
      title: '核心芯片组数量',
      dataIndex: 'coreChipsetQuantity',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.coreChipsetQuantity?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '双芯片组是否支持同时发射',
      dataIndex: 'dualChipsetSimultaneousTransmission',
      span: 1,
    },
    {
      title: '是否双通',
      dataIndex: 'dualTransmissionSupported',
      span: 1,
    },
    {
      title: '核心芯片（芯片组）供应商',
      dataIndex: 'esimCoreChipSupplier',
      span: 1.5,
    },
    {
      title: '核心芯片（芯片组）型号',
      dataIndex: 'esimCoreChipModel',
      span: 1.5,
    },
    {
      title: '基带芯片供应商',
      dataIndex: 'esimBasebandChipSupplier',
      span: 1.5,
    },
    {
      title: '基带芯片型号',
      dataIndex: 'esimBasebandChipModel',
      span: 1.5,
    },
    {
      title: '射频芯片供应商',
      dataIndex: 'esimRfChipSupplier',
      span: 1.5,
    },
    {
      title: '射频芯片型号',
      dataIndex: 'esimRfChipModel',
      span: 1.5,
    },
    {
      title: '软件版本',
      dataIndex: 'softwareVersion',
      span: 1,
    },
    {
      title: '软件查询指令',
      dataIndex: 'softwareQueryCommand',
      span: 1,
    },
    {
      title: '软件开发商',
      dataIndex: 'softwareDeveloper',
      span: 1,
    },
    {
      title: '硬件版本',
      dataIndex: 'hardwareVersion',
      span: 1.5,
    },
    {
      title: '硬件查询指令',
      dataIndex: 'hardwareQueryCommand',
      span: 1.5,
    },
    {
      title: '终端款式',
      dataIndex: 'terminalStyle',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.terminalStyle?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其它',
      dataIndex: 'terminalStyleOther',
      span: 1.5,
    },
    {
      title: '天线',
      dataIndex: 'antenna',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.antenna?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '预留芯片位置',
      dataIndex: 'reservedChipPosition',
      span: 3,
    },
    {
      title: '预留芯片型号',
      dataIndex: 'reservedChipModel',
      span: 3,
    },
    {
      title: '预留芯片功能',
      dataIndex: 'reservedChipFunction',
      span: 3,
    },
    {
      title: 'CDMA版本',
      dataIndex: 'cdmaVersion',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.cdmaVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'cdmaVersionOther',
      span: 1.5,
    },
    {
      title: 'cdma2000版本',
      dataIndex: 'cdma2000Version',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.cdma2000Version?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'cdma2000VersionOther',
      span: 1.5,
    },
    {
      title: 'WCDMA版本',
      dataIndex: 'wcdmaVersion',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.wcdmaVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: 'TD-SCDMA版本',
      dataIndex: 'tdScdmaVersion',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.tdScdmaVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: 'LTE FDD版本',
      dataIndex: 'ltefddVersion',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.ltefddVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: 'TD-LTE版本',
      dataIndex: 'tdLteVersion',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.tdLteVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: 'NR NSA版本',
      dataIndex: 'nrNsaVersion',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.nrNsaVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: 'NR SA版本',
      dataIndex: 'nrSaVersion',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.nrSaVersion?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '是否默认开启SA模式',
      dataIndex: 'saModeDefaultEnabled',
      span: 1.5,
    },
    {
      title: '该设备支持5G-增强移动宽带(eMBB)技术',
      dataIndex: 'nrElb',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.nrElb?.length > 0 ? '是' : '否';
      },
    },
    {
      title: '该设备支持5G-轻量化(RedCap)技术',
      dataIndex: 'nrElb',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.nrRedcap?.length > 0 ? '是' : '否';
      },
    },
    {
      title: '是否支持国内5G异网漫游核心网漫游',
      dataIndex: 'domestic5gNetworkRoamingSupported',
      span: 1,
    },
    {
      title: '核心网漫游频段',
      dataIndex: 'coreNetworkRoamingBand',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.coreNetworkRoamingBand?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '核心网漫游频段其他',
      dataIndex: 'coreNetworkRoamingBandOther',
      span: 1,
    },
    {
      title: '是否智能终端',
      dataIndex: 'smartTerminal',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.smartTerminal?.substring(0, 1)
      },
    },
    {
      title: '操作系统',
      dataIndex: 'operatingSystem',
      span: 3,
      render(dom, entity, index, action, schema) {
        return entity?.operatingSystem?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '版本号',
      dataIndex: 'versionNumber',
      span: 3,
    },
    {
      title: '商用密码',
      dataIndex: 'commercialEncryption',
      span: 1.5,
      render(dom, entity, index, action, schema) {
        return entity?.commercialEncryption?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'commercialPasswordOther',
      span: 1.5,
    },
  ];

  return (
    <>
      <Title title="技术规格" />
      <MyProDescriptions
        column={3}
        columns={columns}
        labelStyle={{
          width: 177,
        }}
        contentStyle={{
          width: 390,
        }}
        bordered
        layout="horizontal"
        dataSource={row?.buEntrustIntoNetworkInfo }
        newdataSource={
          JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo || []
        }
      />
    </>
  );
};

export default BaseTechDetail;
