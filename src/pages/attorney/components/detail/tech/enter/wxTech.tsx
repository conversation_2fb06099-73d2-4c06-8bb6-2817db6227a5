import Title from '@/components/title';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { Tag } from 'antd';
import { get } from 'lodash';
import MyProDescriptions from '../../../proDescriptions';

type BaseDetailProps = {
  row?: any;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const WxTechDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: '卫星频段',
      dataIndex: 'satelliteBand',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.satelliteBand?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'satelliteFrequencyBandOther',
      span: 1,
    },
    {
      title: '卫星业务',
      dataIndex: 'satelliteBusiness',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.satelliteBusiness?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '卫星移动终端类别',
      dataIndex: 'satelliteMobileTerminalCategory',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.satelliteMobileTerminalCategory?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '卫星通信制式',
      dataIndex: 'satelliteCommunicationSystem',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.satelliteCommunicationSystem?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'satelliteCommunicationStandardOther',
      span: 1,
    },
    {
      title: '应用场景',
      dataIndex: 'applicationScenario',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.applicationScenario?.split(',')?.map((item) => {
          return <Tag>{item}</Tag>;
        });
      },
    },
    {
      title: '是否支持蜂窝功能',
      dataIndex: 'cellularFunctionSupported',
      span: 2,
    },
  ];

  return (
    <>
      <Title title="卫星移动终端技术规格" />
      <MyProDescriptions
        column={2}
        columns={columns}
        labelStyle={{
          width: 177,
        }}
        contentStyle={{
          width: 390,
        }}
        bordered
        layout="horizontal"
        dataSource={row?.buEntrustIntoNetworkInfo }
        newdataSource={
          JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo || []
        }
      />
    </>
  );
};

export default WxTechDetail;
