import Title from '@/components/title';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { Tag } from 'antd';
import { get } from 'lodash';
import MyProDescriptions from '../../../proDescriptions';

type BaseDetailProps = {
  row?: any;
};
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const BaseBusinessTechDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: 'GPRS功能',
      dataIndex: 'gprsFunction',
      span: 1,
    },
    {
      title: 'CLASS',
      dataIndex: 'class0',
      span: 1,
    },
    {
      title: 'HSDPA-WCDMA是否支持',
      dataIndex: 'hsdpaWcdmaSupported',
      span: 1,
    },
    {
      title: 'HSDPA接入类别',
      dataIndex: 'hsdpaWcdmaAccessCategory',
      span: 1,
    },
    {
      title: 'HSDPA-TDSCDMA是否支持',
      dataIndex: 'hsdpaTdscdmaSupported',
      span: 1,
    },
    {
      title: 'HSDPA接入类别',
      dataIndex: 'hsdpaTdscdmaAccessCategory',
      span: 1,
    },
    {
      title: 'HSUPA-WCDMA是否支持',
      dataIndex: 'hsupaWcdmaSupported',
      span: 1,
    },
    {
      title: 'HSUPA接入类别',
      dataIndex: 'hsupaWcdmaAccessCategory',
      span: 1,
    },
    {
      title: 'HSUPA-TDSCDMA是否支持',
      dataIndex: 'hsupaTdscdmaSupported',
      span: 2,
    },
    // {
    //   title: "HSUPA-TDSCDMA接入类别",
    //   dataIndex: "hsupaTdscdmaNoSupportedIllustrate"
    // },
    {
      title: 'LTE传输能力',
      dataIndex: 'lteTransmissionAbility',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.lteTransmissionAbility?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '多天线传输模式',
      dataIndex: 'multiAntennaTransmissionMode',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.multiAntennaTransmissionMode?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item?.substring(2) || '-'}</Tag>;
        });
      },
    },
    {
      title: 'LTE终端语音模式',
      dataIndex: 'lteTerminalVoiceMode',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.lteTerminalVoiceMode?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item?.substring(2) || '-'}</Tag>;
        });
      },
    },
    {
      title: 'CDMA1X是否支持补充信道',
      dataIndex: 'cdma1xSupplementaryChannelSupported',
      span: 1,
    },
    {
      title: 'CDMA数据编码方式',
      dataIndex: 'cdmaDataEncoding',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.cdmaDataEncoding?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '短信',
      dataIndex: 'sms',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.sms?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'smsOther',
      span: 1,
    },
    // {
    //   title: "5G消息",
    //   dataIndex: "fivegMessage"
    // },
    {
      title: '支持的补充业务',
      dataIndex: 'supportedSupplementaryServices',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.supportedSupplementaryServices?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '支持的增值业务',
      dataIndex: 'supportedValueAddedServices',
      span: 1,
    },
    {
      title: '可视电话',
      dataIndex: 'videoPhone',
      span: 1,
      renderText(text, record, index, action) {
        return record?.videoPhone === '可视电话' ? '是' : '否';
      },
    },
    {
      title: 'TD-SCDMA',
      dataIndex: 'supportedValueAddedServicesTdscdma',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.supportedValueAddedServicesTdscdma?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '紧急呼叫',
      dataIndex: 'emergencyCall',
      span: 1,
      render(dom, entity, index, action, schema) {
        return entity?.emergencyCall?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
    {
      title: '其他',
      dataIndex: 'emergencyCallOther',
      span: 1,
    },
    {
      title: 'IP协议',
      dataIndex: 'ipProtocol',
      span: 2,
      render(dom, entity, index, action, schema) {
        return entity?.ipProtocol?.split(',')?.map((item) => {
          return <Tag style={{marginBottom: 5}}>{item}</Tag>;
        });
      },
    },
  ];

  return (
    <>
      <Title title="基本业务和功能" />
      <MyProDescriptions
        column={2}
        columns={columns}
        labelStyle={{
          width: 177,
        }}
        contentStyle={{
          width: 390,
        }}
        bordered
        layout="horizontal"
        dataSource={row?.buEntrustIntoNetworkInfo }
        newdataSource={
          JSON.parse(row?.entrustApplyInfo?.entrustJson || '{}')?.buEntrustIntoNetworkInfo || []
        }
      />
    </>
  );
};

export default BaseBusinessTechDetail;
