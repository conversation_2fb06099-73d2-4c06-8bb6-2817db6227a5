import Title from '@/components/title';
import { NumBoolObj } from '@/enums';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import MyProDescriptions from '../ProDescription';
import { CommonType } from '..';
import { TASK_TYPE } from '@/enums/task';
import { ATTORNEY_TYPE } from '@/enums/attorney';

type BaseDetailProps = {
  row?: CommonType;
};

const ReportDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;

  const columns: ProDescriptionsProps['columns'] = [
    {
      title: '是否生成报告',
      dataIndex: 'tfReport',
      valueEnum: NumBoolObj,
      hideInDescriptions: row?.taskType === TASK_TYPE.检测记录
    },
    {
      title: '报告份数',
      dataIndex: 'reportNumber',
      hideInDescriptions: row?.taskType === TASK_TYPE.检测记录
    },
    {
      title: '是否生成数据',
      dataIndex: 'tfGenerateData',
      valueEnum: NumBoolObj,
      hideInDescriptions: row?.type === ATTORNEY_TYPE.enter
    },
  ];

  return (
    <>
      <Title title="检测报告" />
      <MyProDescriptions
        columns={columns}
        dataSource={row}
      />
    </>
  );
};

export default ReportDetail;
