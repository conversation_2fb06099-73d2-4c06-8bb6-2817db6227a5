import { FC } from 'react';
import ProDescriptions, { ProDescriptionsProps } from '../proDescriptions';

const MyProDescriptions: FC<ProDescriptionsProps> = (props) => {
 
  return (
    <ProDescriptions
      newdataSource={
        props?.dataSource?.entrustApplyInfo?.entrustJson &&
        JSON.parse(props?.dataSource?.entrustApplyInfo?.entrustJson)
      }
      labelStyle={
        {
          width: 177
        }
      }
      contentStyle={
        {
          width: 390
        }
      }
      // labelWidth={177}
      // contentWidth={390}
      layout="horizontal"
      column={2}
      bordered
      {...props}
    />
  );
};

export default MyProDescriptions;
