import { ATTORNEY_TYPE } from '@/enums/attorney';
import { entrustInfoGetVo } from '@/services/base/weituoshujichuxinxibiaojiekou';
import { useModel, useRequest } from '@umijs/max';
import BaseDetail from './base';
import ClientDetail from './client';
import SampleDetail from './sample';
import { CommonType } from '..';
import AntennaBaseDetail from './antenna';
import EnvironmentDetail from './environment';
import RadiationDetail from './radiation';
import SoftwareDetail from './software';

type BaseDetailProps = {
  row?: CommonType;
};

const BaseInfo: React.FC<BaseDetailProps> = ({ row }) => {
  const { initialState } = useModel('@@initialState');

  return (
    <>
      <BaseDetail row={row} />
      <ClientDetail row={row} />
      <SampleDetail row={row} />
      {row?.type === ATTORNEY_TYPE.antenna && <AntennaBaseDetail row={row} />}
      {row?.type === ATTORNEY_TYPE.environment && <EnvironmentDetail row={row as BASE.EntrustInfoVO} />}
      {row?.type === ATTORNEY_TYPE.radiation && <RadiationDetail row={row as BASE.EntrustInfoVO} />}
      {row?.type === ATTORNEY_TYPE.softtest && <SoftwareDetail row={row as BASE.EntrustInfoVO} />}
     
    </>
  );
};

export default BaseInfo;
