import Title from '@/components/title';
import { NumBoolObj } from '@/enums';
import ProDescriptions, { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { get } from 'lodash';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

/**
 * 获取检测项目具体的值
 * @param str 检测项目字符串
 * @param key 需要获取的key
 */
const getStrValue = (str: string, key: string | string[]) => {
  return (str && get(JSON.parse(str), key)) || '--';
};

const AntennaBaseDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  const columns: ProDescriptionsProps['columns'] = [
    {
      dataIndex: ['buEntrustAntennaInfo', 'tfLargeSize'],
      title: '大尺寸天线',
      valueEnum: NumBoolObj,
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'buLength'],
      title: '长',
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'buWideth'],
      title: '宽',
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'buThick'],
      title: '高',
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'buWeight'],
      title: '重量',
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'tfMultifrequency'],
      title: '多频段天线',
      valueEnum: NumBoolObj,
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'centerPositionMode'],
      title: '中心位置方式',
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'antennaType'],
      title: '天线类型',
    },
    {
      dataIndex: ['buEntrustAntennaInfo', 'portNum'],
      title: '端口数量',
    },
  ];

  return (
    <>
      <Title title="天线信息" />
      <ProDescriptions
        newdataSource={
          (row?.entrustApplyInfo?.entrustJson && JSON.parse(row?.entrustApplyInfo?.entrustJson))}
        layout="horizontal"
        bordered
        columns={columns}
        dataSource={row}
      />
    </>
  );
};

export default AntennaBaseDetail;
