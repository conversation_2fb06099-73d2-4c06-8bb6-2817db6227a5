import Title from '@/components/title';
import ProTable from '../../proTable';
import MyProDescriptions from '../ProDescription';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import { CommonType } from '..';

type BaseDetailProps = {
  row?: CommonType;
};

const SampleDetail: React.FC<BaseDetailProps> = ({ row,  }) => {
  const isMode = row?.type === ATTORNEY_TYPE.model;
  return (
    <>
      <Title title="样品信息" />
      <ProTable
        search={false}
        options={false}
        pagination={false}
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        rowKey="id"
        newdataSource={
          (row as BASE.EntrustInfoVO)?.entrustApplyInfo?.entrustJson &&
          JSON.parse((row as BASE.EntrustInfoVO)?.entrustApplyInfo?.entrustJson || '{}')?.sampleInfos
        }
        dataSource={row?.sampleInfos || []}
        columns={[
          {
            title: '序号',
            valueType: 'index',
          },
          {
            dataIndex: 'sampleName',
            title: '样品名称',
          },
          {
            title: '样品编号',
            dataIndex: 'sampleNumber',
          },
          {
            title: '样品序号',
            dataIndex: 'sampleSerialNumber',
          },
          {
            title: '样品版本号',
            dataIndex: 'sampleVersionNumber',
          },
          {
            title: '样品型号',
            dataIndex: 'sampleModel',
          },
          {
            title: '样品数量',
            dataIndex: 'sampleNum',
          },
          {
            title: '样品状态',
            dataIndex: 'sampleStatus',
          },

          {
            title: '样品处理意见',
            dataIndex: 'sampleProcessStatus',
          },
          {
            title: '生产单位',
            dataIndex: 'productCompany',
          },
          {
            title: '生产单位地址',
            dataIndex: 'productCompanyAddress',
          },
          {
            title: '备注',
            dataIndex: 'sampleRemark',
          },
          {
            title: '状态',
            dataIndex: 'statusInfo',
          },
        ]}
      />

      <Title title="配件信息" />
      <ProTable
        search={false}
        options={false}
        pagination={false}
        newdataSource={
          (row as BASE.EntrustInfoVO)?.entrustApplyInfo?.entrustJson &&
          JSON.parse((row as BASE.EntrustInfoVO)?.entrustApplyInfo?.entrustJson || '{}')?.sampleFittingsInfos
        }
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        rowKey="id"
        dataSource={row?.sampleFittingsInfos || []}
        columns={[
          {
            title: '序号',
            valueType: 'index',
          },
          {
            title: '配件名称',
            dataIndex: 'fittingsName',
          },
          {
            title: '编号',
            dataIndex: 'fittingsNumber',
          },
          {
            title: '配件类型',
            dataIndex: 'fittingsType',
          },
          {
            title: '数量',
            dataIndex: 'fittingsCount',
            valueType: 'digit',
          },
          {
            title: '备注',
            dataIndex: 'fittingsRemark',
          },
        ]}
      />

      {!isMode && (
        <MyProDescriptions
          style={{ marginTop: '20px' }}
          column={1}
          // labelWidth={10}
          labelStyle={{
            width: 200,
          }}
          bordered
          layout="horizontal"
          dataSource={row}
          columns={[
            {
              title: '检验样品功能描述',
              dataIndex: 'sampleFunctionDescription',
            },
            {
              title: '检验样品规格及参数',
              dataIndex: 'sampleSpecificationParam',
            },
            {
              title: '样品的配置、工作状态、软硬件版本信息',
              dataIndex: 'sampleConfigInfo',
            },
            {
              title: '配合被测样品工作且影响样品工作状态的设备信息',
              dataIndex: 'sampleImpactStatusInfo',
            },
          ]}
        />
      )}
    </>
  );
};

export default SampleDetail;
