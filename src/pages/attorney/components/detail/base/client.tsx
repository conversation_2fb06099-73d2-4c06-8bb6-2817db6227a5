import FileDownload from '@/components/fileDownload';
import ProDescriptions, { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import Title from '@/components/title';
import { ATTORNEY_TYPE } from '@/enums/attorney';
import dayjs from 'dayjs';
import MyProDescriptions from '../ProDescription';
import { CommonType } from '..';

type BaseDetailProps = {
  row?: CommonType;
};

const ClientDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  console.log(row, 'bbbbb')
  const columns: ProDescriptionsProps['columns'] = [
    {
      dataIndex: 'entrustEntName',
      title: '委托单位',
    },
    {
      dataIndex: 'entrustContact',
      title: '联系人',
    },
    {
      dataIndex: 'entrustPhone',
      title: '手机号',
    },
    {
      dataIndex: 'entrustEmail',
      title: '邮箱',
    },
    {
      dataIndex: 'companyAddress',
      title: '委托单位地址',
    },
    {
      dataIndex: 'customerCode',
      title: '客户代码',
    },
    {
      dataIndex: 'projectCode',
      title: '项目代码',
    },
    {
      dataIndex: 'originAddress',
      title: '产地',
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.enter,
    },
    {
      dataIndex: 'inspectionType',
      title: '检验类别',
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.enter,
      // span:24
    },
    {
      dataIndex: 'testTypeInfo',
      title: '检验类别详细',
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.enter,
    },
    {
      title: '邮政编码',
      dataIndex: ['buEntrustApproveInfo', 'postalCode'],
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.model,

    },
    {
      title: '主要功能',
      dataIndex: ['buEntrustApproveInfo', 'mainFunction'],
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.model,

    },
    // {
    //   dataIndex: 'inspectionDetailType',
    //   title: '检验类型详细',
    //   hideInDescriptions: row?.type !== ATTORNEY_TYPE.enter,
    // },
    {
      title: '设备简介表',
      span: 24,
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.enter,
      dataIndex: 'equipmentFiles',
      render(dom, entity, index, action, schema) {
        return entity.equipmentFiles?.map((item: BASE.FileInfo) => {
          return (
            <FileDownload
            item={item}
            key={item.id}
            preview={true}
            />
          );
        });
      },
    },
    {
      title: '实验室选择',
      span: 24,
      dataIndex: 'laboratoryFiles',
      hideInDescriptions: row?.type !== ATTORNEY_TYPE.enter,
      render(dom, entity, index, action, schema) {
        return entity.laboratoryFiles?.map((item: BASE.FileInfo) => {
          return (
            <FileDownload
            item={item}
            key={item.id}
            preview={true}
            />
          );
        });
      },
    },
    
  ];

  return (
    <>
      <Title title="委托方信息" />
      <MyProDescriptions  columns={columns} dataSource={row} />
    </>
  );
};

export default ClientDetail;
