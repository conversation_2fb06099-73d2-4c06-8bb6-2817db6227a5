import Title from '@/components/title';
import { getDataIndexFn } from '@/utils';
import { ProDescriptions, ProDescriptionsProps } from '@ant-design/pro-components';
import MyProDescriptions from '../ProDescription';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

const getDataIndex = getDataIndexFn('buEntrustElectroRadiaInfo');

const RadiationDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: '基站名称',
      dataIndex: getDataIndex('baseStationName'),
    },
    {
      title: '基站地址',
      dataIndex: getDataIndex('baseStationAddress'),
    },
    {
      title: '东经',
      dataIndex: getDataIndex('wgEastLongitude'),
    },
    {
      title: '北纬',
      dataIndex: getDataIndex('wgNorthLatitude'),
    },
    {
      title: '海拔',
      dataIndex: getDataIndex('wgAltitude'),
      render(dom, entity, index, action, schema) {
        return dom && `${dom}m` || '--';
      },
    },
    {
      title: '运营单位',
      dataIndex: getDataIndex('operator'),
    },
    {
      title: '基站编号',
      dataIndex: getDataIndex('baseStationNumber'),
    },
    {
      title: '其他',
      dataIndex: getDataIndex('otherProjectName'),
    },
  ];

  return (
    <>
      <Title title="电磁辐射信息" />
      <Title
        title={<span style={{ fontSize: '13px' }}>移动通信基站项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <ProDescriptions column={3} bordered layout="horizontal" columns={columns} dataSource={row} />
      <Title
        title={<span style={{ fontSize: '13px' }}>WGS84</span>}
        style={{ marginLeft: '15px' }}
      />
      <MyProDescriptions
        columns={[
          {
            dataIndex: getDataIndex('wgEastLongitude'),
            title: '东经',
          },
          {
            dataIndex: getDataIndex('wgNorthLatitude'),
            title: '北纬',
          },
          {
            dataIndex: getDataIndex('wgAltitude'),
            title: '海拔',
            render(dom, entity, index, action, schema) {
              return dom && `${dom}m` || '--';
            },
          },
        ]}
        dataSource={row}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>输变线项目</span>}
        style={{ marginLeft: '15px' }}
      />
      <MyProDescriptions
        columns={[
          {
            dataIndex: getDataIndex('transformationLineProjectName'),
            title: '项目名称',
          },
        ]}
        dataSource={row}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>变电站项目</span>}
        style={{ marginLeft: '15px' }}
      />
      <MyProDescriptions
        columns={[
          {
            dataIndex: getDataIndex('transformerSubstationProjectName'),
            title: '项目名称',
          },
        ]}
        dataSource={row}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>其他</span>}
        style={{ marginLeft: '15px' }}
      />
      <MyProDescriptions
        columns={[
          {
            dataIndex: getDataIndex('otherProjectName'),
            title: '项目名称',
          },
        ]}
        dataSource={row}
      />
    </>
  );
};

export default RadiationDetail;
