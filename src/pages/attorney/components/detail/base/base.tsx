import FileDownload from '@/components/fileDownload';
import Title from '@/components/title';
import { ATTORNEY_TYPE, ATTORNEY_TYPE_MAP } from '@/enums/attorney';
import { TASK_TYPE, TASK_TYPE_MAP } from '@/enums/task';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';
import { useMemo } from 'react';
import { CommonType } from '..';
import MyProDescriptions from '../ProDescription';
import { ProTable } from '@ant-design/pro-components';
import { NumBool } from '@/enums/base';

type BaseDetailProps = {
  row?: CommonType;
  title?: string;
};

const BaseDetail: React.FC<BaseDetailProps> = (props) => {
  const { row, title = '业务信息' } = props;
  console.log(row,'base----')

  const columns: ProDescriptionsProps['columns'] = useMemo(() => {
    const _columns: ProDescriptionsProps['columns'] = [
      {
        dataIndex: 'entrustName',
        title: '项目名称',
        span: 3,
        render(dom, entity, index, action, schema) {
          return entity?.entrustName || entity?.name;
        },
      },
      {
        dataIndex: 'number',
        title: '任务编号',
      },
      {
        dataIndex: 'entrustDate',
        title: '委托日期',
        valueType: 'date',
      },
      {
        dataIndex: 'sampleArrivalDate',
        title: '到样日期',
        valueType: 'date',
      },
      {
        dataIndex: 'detecCycle',
        title: '检测周期',
      },
      {
        dataIndex: 'type',
        title: '委托类型',
        valueEnum: ATTORNEY_TYPE_MAP,
      },
      {
        dataIndex: 'taskTypeName',
        title: '任务类型',
      },
      {
        dataIndex: 'managerName',
        title: '项目经理',
      },
      {
        dataIndex: 'salerName',
        title: '销售人员',
      },
      {
        dataIndex: 'businessAreaTypeCn',
        title: '业务领域',
      },
      {
        dataIndex: 'taskType',
        title: '任务书类型',
        valueEnum: TASK_TYPE_MAP,
      },
      {
        dataIndex: 'acceptDate',
        title: '受理日期',
        valueType: 'date',
      },
      {
        dataIndex: ['taskInfos', 'deadLineDate'],
        title: '截止时间',
        valueType: 'date',
        span: 24,
      },
      {
        title: '是否鲜章',
        dataIndex: 'tfFreshChapter',
        span: 24,
        render(dom, entity, index, action, schema) {
          if(entity?.tfFreshChapter === true) {
            return '是'
          } else if(entity?.tfFreshChapter === false){
            return '否'
          } else {
            return '-'
          }
        },
      },
      {
        title: '委托书备注',
        dataIndex: 'remark',
        valueType: 'textarea',
        span: 24,
      },
      {
        title: '客户确认文件',
        span: 24,
        dataIndex: 'confirmedFileInfos',
        render(dom, entity, index, action, schema) {
          return entity.confirmedFileInfos?.map((item: BASE.FileInfo) => {
            return <FileDownload key={item.id} item={item} />;
          });
        },
      },
      {
        dataIndex: 'remarks',
        title: '任务备注',
        span: 24,
      },
      {
        dataIndex: 'taskFileInfos',
        title: '任务附件',
        span: 24,
        render(dom, entity, index, action, schema) {
          return entity?.taskFileInfos?.map((i) => <FileDownload key={i.id} item={i} />);
        },
      },
      {
        dataIndex: 'closInfoFiles',
        title: '项目结项资料',
        span: 24,
        render(dom, entity, index, action, schema) {
          return entity?.closInfoFiles?.map((i) => <FileDownload key={i.id} item={i} />) || '-';
        },
      },
      // {
      //   dataIndex: 'tfGenerateData',
      //   title: '是否生成数据',
      //   hideInDescriptions: row?.type === ATTORNEY_TYPE.enter,
      //   render(dom, entity, index, action, schema) {
      //     return (
      //       NumBool[entity?.tfGenerateData] || '-'
      //     );
      //   },
      // },
    ];
    if (row?.type === ATTORNEY_TYPE.model) {
      // 型号核准去掉任务类型
      return _columns
        .filter((i) => i.dataIndex !== 'taskTypeName')
        .concat([
          {
            dataIndex: ['buEntrustApproveInfo', 'orgUserInfo', 'fullName'],
            title: '测试人员',
          },
          {
            dataIndex: 'receiveSampleDate',
            title: '收样时间',
            valueType: 'date',
          },
          {
            dataIndex: 'pushDate',
            title: '推送时间',
            valueType: 'date',
          },
        ]);
    } else if (String(row?.taskType) === TASK_TYPE.检测记录) {
      // 检测记录去掉委托类型、任务类型、项目经理
      return _columns.filter(
        (i) => !['type', 'taskTypeName', 'managerName'].includes(i.dataIndex as string),
      );
    }
    return _columns;
  }, [row?.type, row?.taskType]);

  return (
    <>
      <Title title={title} />
      <MyProDescriptions columns={columns} dataSource={row} />

      {((String(row?.taskType) === TASK_TYPE.检测任务 || String(row?.taskType) === TASK_TYPE.内部任务)) && (
        <>
          <ProTable
            search={false}
            options={false}
            pagination={false}
            cardProps={{
              bodyStyle: {
                padding: 0,
                marginTop: 10,
              },
            }}
            
            rowKey="id"
            dataSource={(row?.addTaskDeptIds as unknown as Record<string, any>[]) || []}
            columns={[
              {
                title: '序号',
                valueType: 'index',
              },
              {
                title: '检测部门',
                dataIndex: 'deptId',
                valueType: 'treeSelect',
                formItemProps: { rules: [{ required: true }] },
                request: () => {
                  return orgDeptInfoTree().then((res) => {
                    return res?.data || [];
                  });
                },
                fieldProps: {
                  showSearch: true,
                  treeDefaultExpandAll: true,
                  fieldNames: {
                    label: 'deptName',
                    value: 'id',
                    children: 'child',
                  },
                },
              },
              {
                title: '标准编号',
                dataIndex: 'standardInfo',
              },
              {
                title: '检测方法',
                dataIndex: row?.type === ATTORNEY_TYPE.model ? 'standardInfo' : 'testMethod',
                hideInTable: row?.type === ATTORNEY_TYPE.model,
              },
            ]}
          />
        </>
      )}
    </>
  );
};

export default BaseDetail;
