import Title from '@/components/title';
import { getDataIndexFn } from '@/utils';
import { Row } from 'antd';
import MyProDescriptions from '../ProDescription';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { ProDescriptions } from '@ant-design/pro-components';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

const getDataIndex = getDataIndexFn('buEntrustElectroEnvInfo');

const EnvironmentDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  const columns: ProDescriptionsProps['columns'] = [
    {
      dataIndex: getDataIndex('airProjectName'),
      title: '机场项目',
    },
    {
      dataIndex: getDataIndex('airAddress'),
      title: '机场地址',
    },
    {
      dataIndex: getDataIndex('airWgEastLongitude'),
      title: '东经',
    },
    {
      dataIndex: getDataIndex('airWgNorthLatitude'),
      title: '北纬',
    },
    {
      dataIndex: getDataIndex('airWgAltitude'),
      title: '海拔',
      span: 2,
      render(dom, entity, index, action, schema) {
        console.log(dom,'dom--1-1-')
        return (dom && `${dom}m`) || '--';
      },
    },
  ];

  return (
    <>
      <Title title="电磁环境信息" />
      <Title
        title={<span style={{ fontSize: '13px' }}>机场项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <MyProDescriptions  columns={columns} dataSource={row} />
      <Title
        title={<span style={{ fontSize: '13px' }}>台站项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <MyProDescriptions
        columns={[
          {
            dataIndex: getDataIndex('stationConstrucName'),
            title: '建设单位',
          },
          {
            dataIndex: getDataIndex('stationConstrucAddress'),
            title: '建设单位地址',
          },
          {
            dataIndex: getDataIndex('stationName'),
            title: '台站名称',
          },
          {
            dataIndex: getDataIndex('stationType'),
            title: '台站类型',
          },
          {
            dataIndex: getDataIndex('stationAddress'),
            title: '台站地址',
          },
          {
            dataIndex: getDataIndex('stationEastLongitude'),
            title: '台站东经',
          },
          {
            dataIndex: getDataIndex('stationNorthLatitude'),
            title: '台站北纬',
          },
          {
            dataIndex: getDataIndex('stationAltitude'),
            title: '台站海拔',
            render(dom, entity, index, action, schema) {
              return (dom && `${dom}m`) || '--';
            },
          },
          {
            dataIndex: getDataIndex('stationCount'),
            title: '台站数量',
          },
          {
            dataIndex: getDataIndex('stationHeight'),
            title: '台站高度',
          },
          {
            dataIndex: getDataIndex('receivSensitivit'),
            title: '接收灵敏度',
          },
          {
            dataIndex: getDataIndex('antennaGain'),
            title: '天线增益',
          },
        ]}
        dataSource={row}
      />

      <Title
        title={<span style={{ fontSize: '13px' }}>输变线项目</span>}
        style={{ marginLeft: '5px' }}
      />
      <MyProDescriptions
        columns={[
          {
            dataIndex: getDataIndex('tranProjectName'),
            title: '项目名称',
          },
        ]}
        dataSource={row}
      />
    </>
  );
};

export default EnvironmentDetail;
