import Title from '@/components/title';
import { getDataIndexFn } from '@/utils';
import MyProDescriptions from '../ProDescription';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

const getDataIndex = getDataIndexFn('buEntrustSoftwareInfo');

const SoftwareDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  const columns: ProDescriptionsProps['columns'] = [
    {
      title: '软件名称',
      dataIndex: getDataIndex('swName'),
    },
    {
      title: '软件版本',
      dataIndex: getDataIndex('swVersion'),
    },
    {
      title: '文档资料',
      dataIndex: getDataIndex('swDocument'),
    },
    {
      title: '测试环境信息',
      dataIndex: getDataIndex('testEnvInfo'),
    },
    {
      title: '软件处理意见',
      dataIndex: getDataIndex('swOpinion'),
    },
  ];

  return (
    <>
      <Title title="软件测试信息" />
      <MyProDescriptions columns={columns} dataSource={row} />
    </>
  );
};

export default SoftwareDetail;
