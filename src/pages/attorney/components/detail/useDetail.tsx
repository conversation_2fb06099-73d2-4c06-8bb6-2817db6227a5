import { taskInfoGetVo } from '@/services/base/renwujichuxinxibiaojiekou';
import { entrustInfoGetVo } from '@/services/base/weituoshujichuxinxibiaojiekou';

import BaseDetail from '@/pages/attorney/components/detail/base';
import PayInfoDetail from '@/pages/attorney/components/detail/pay';
import TechDetail from '@/pages/attorney/components/detail/tech';
import { useModel, useRequest } from '@umijs/max';
import { TabsProps } from 'antd';
import { useMemo } from 'react';
import { CommonType, DetailCommonProps } from '.';

const useDetail = ({ attorneyId, taskId, isExamine, isNetworkTaskExamine, formatResult }: DetailCommonProps) => {
  const { initialState } = useModel('@@initialState');

  const attorneyRequest = useRequest(
    () => {
      return entrustInfoGetVo({
        id: attorneyId,
        tfExamine: isExamine
      });
    },
    {
      manual: !attorneyId,
    },
  );
  const taskReqeust = useRequest(
    () => {
      return taskInfoGetVo({
        id: taskId,
        tfExamine: isExamine
      });
    },
    {
      manual: !taskId,
    },
  );
  const _row = useMemo(() => {
    if (attorneyId) {
      const taskInfos = isExamine
        ? attorneyRequest?.data?.entrustHisJson
          ? JSON.parse(attorneyRequest?.data?.entrustHisJson)?.taskInfos
          : attorneyRequest?.data?.taskInfos
        : attorneyRequest?.data?.entrustHisJson
        ? JSON.parse(attorneyRequest?.data?.entrustHisJson)?.taskInfos
        : attorneyRequest?.data?.taskInfos;
      const modifyJSON = JSON.parse(attorneyRequest?.data?.entrustNewJson || '{}');
      const _data = {
        ...taskInfos,
        ...(attorneyRequest?.data?.entrustHisJson
          ? JSON.parse(attorneyRequest?.data?.entrustHisJson)
          : attorneyRequest?.data),
        // 任务书字段，需要从任务书取
        taskType: taskInfos?.type,
        taskFileInfos: taskInfos?.taskFileInfos,
        acceptDate: taskInfos?.acceptDate,
        businessAreaTypeCn: taskInfos?.businessAreaTypeCn, // 业务领域
        remarks: taskInfos?.remarks,
        entrustApplyInfo: isExamine
          ? attorneyRequest?.data?.entrustNewJson
            ? {
              entrustJson: JSON.stringify({
                ...modifyJSON,
                taskFileInfos: modifyJSON?.taskInfos?.taskFileInfos,
                closInfoFiles: modifyJSON?.taskInfos?.closInfoFiles,
              }),
            }
            : null
          : (attorneyRequest?.data?.entrustHisJson ? {
            entrustJson: JSON.stringify({...attorneyRequest?.data, taskFileInfos: taskInfos?.taskFileInfos,})
          } : null)
      };
      return formatResult ? formatResult(_data as unknown as CommonType) : _data;
    } else if (taskId) {
      // 处理委托书修改审核通过后也可以查看上一次的修改记录
      const entrustInfo = isExamine ? (taskReqeust?.data?.entrustInfo?.entrustHisJson
        ? JSON.parse(taskReqeust?.data?.entrustInfo?.entrustHisJson)
        : taskReqeust?.data?.entrustInfo) : (taskReqeust?.data?.entrustInfo?.entrustHisJson
          ? JSON.parse(taskReqeust?.data?.entrustInfo?.entrustHisJson)
          : taskReqeust?.data?.entrustInfo);
      const modifyJSON = JSON.parse(taskReqeust?.data?.entrustInfo?.entrustNewJson || '{}');
      const _data = {
        ...taskReqeust?.data,
        ...entrustInfo,
        taskFileInfos: taskReqeust?.data?.taskFileInfos,
        // remark: entrustInfo?.remark,
        type: entrustInfo?.type, // 默认type为委托书的type
        taskType: taskReqeust?.data?.entrustInfo?.entrustHisJson
          ? JSON.parse(taskReqeust?.data?.entrustInfo?.entrustHisJson)?.taskInfos?.type
          : taskReqeust?.data?.type, // 任务类型
        number: taskReqeust?.data?.entrustInfo?.entrustHisJson
          ? JSON.parse(taskReqeust?.data?.entrustInfo?.entrustHisJson)?.taskInfos?.number
          : taskReqeust?.data?.number, // 任务编号
        acceptDate: taskReqeust?.data?.entrustInfo?.entrustHisJson
          ? JSON.parse(taskReqeust?.data?.entrustInfo?.entrustHisJson)?.taskInfos?.acceptDate
          : taskReqeust?.data?.acceptDate,
        buEntrustApproveInfo: entrustInfo?.buEntrustApproveInfo,
        entrustApplyInfo: isExamine
          ? taskReqeust?.data?.entrustInfo?.entrustNewJson
            ? {
                entrustJson: JSON.stringify({
                  ...modifyJSON,
                  taskFileInfos: modifyJSON?.taskInfos?.taskFileInfos,
                  closInfoFiles: modifyJSON?.taskInfos?.closInfoFiles,
                }),
              }
            : null
          : taskReqeust?.data?.entrustInfo?.entrustHisJson ? {
            entrustJson: JSON.stringify({...taskReqeust?.data, ...taskReqeust?.data?.entrustInfo, taskFileInfos: taskReqeust?.data?.taskFileInfos})
          } : null
      };
      return formatResult ? formatResult(_data as unknown as CommonType) : _data;
    }
  }, [attorneyId, taskId, attorneyRequest?.data, taskReqeust?.data]);

  // 仅市场部人员可见
  const onlyMenmber = initialState?.currentUser?.roles?.some((role) =>
    ['superAdmin', 'acceptPerson', 'proManage', 'marketDirector', 'salesMan'].includes(role),
  );

  const items: TabsProps['items'] = useMemo(() => {
    return [
      {
        key: '基本信息',
        label: '基本信息',
        children: <BaseDetail row={_row as CommonType} />,
      },
      {
        key: '技术信息',
        label: '技术信息',
        children: <TechDetail row={_row as CommonType} isNetworkTaskExamine={isNetworkTaskExamine}/>,
      },
      // {
      //   key: '合同信息',
      //   label: '合同信息',
      //   hide: true,
      //   children: <PayInfoDetail row={_row as CommonType} />,
      // },
    ].filter((k) => {
      if(isNetworkTaskExamine) {
        return k.label === '技术信息';
      }
      if (onlyMenmber) {
        return k;
      } else {
        return k.label !== '合同信息';
      }
    });
  }, [_row]);

  return items;
};

export default useDetail;
