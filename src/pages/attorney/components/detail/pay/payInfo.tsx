import Title from '@/components/title';
import ProTable from '@/pages/attorney/components/proTable';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

const PayInfoDetail: React.FC<BaseDetailProps> = ({ row }) => {
  return (
    <>
      <Title title="已支付信息" />
      <ProTable
        search={false}
        options={false}
        pagination={false}
        newdataSource={
          row?.entrustApplyInfo?.entrustJson &&
          JSON.parse(row?.entrustApplyInfo?.entrustJson).payForeignInfos
        }
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        rowKey="id"
        dataSource={row?.payForeignInfos || []}
        columns={[
          {
            title: '序号',
            valueType: 'index',
          },
          {
            dataIndex: 'payObj',
            title: '支付对象',
          },
          {
            title: '支付金额（元）',
            dataIndex: 'payAmount',
          },
          {
            title: '支付时间',
            dataIndex: 'payTime',
            valueType: 'date',
          },
          {
            title: '支付原因',
            dataIndex: 'payReson',
          },
        ]}
      />
    </>
  );
};

export default PayInfoDetail;
