import Title from '@/components/title';
import { sumTestDuration } from '@/pages/task/plan/form/testTimeEdit';
import { taskDeptKeepTimeInfoAccount, taskDeptKeepTimeInfoVoPage } from '@/services/base/renwujishirenwujiekou';
import { Button, message, Tag } from 'antd';
import { launch, OnlyInSearch } from 'antd-pro-crud';
import testTimeDetail from '@/pages/task/plan/detail/testTimeDetail';
import FileDownload from '@/components/fileDownload';
import { orgDeptInfoTree } from '@/services/base/bumenxinxibiaojiekou';
import ProFormSelect from '@/components/proFormSelect';
import { ROLE_TYPE } from '@/enums';
import dayjs from 'dayjs';
import Action from '@/components/action';
import ProTable from '@/components/proTable';
import Text from '@/components/text';


const alert_detail = launch(testTimeDetail);

type testRecordDetailProps = {
  row?: BASE.EntrustInfoVO;
};

export type BizObject = BASE.TaskDeptKeepTimeInfoVO;

const TestRecordDetail: React.FC<testRecordDetailProps> = ({ row }) => {
  return (
    <>
      <Title title="测试记录" />
      <ProTable<BizObject, {}, GlobalValueType>
            rowKey="id"
            hiddenBtns="all"
            defaultSize="small"
            bordered
            rowSelection={false}
            search={false}
            expandable={{
              expandIcon: () => {
                return null;
              },
            }}
            toolbar={{
              actions: [],
            }}
            style={{
              marginTop: 24,
              marginLeft: '-24px',
            }}
            options={false}
            request={async ({ current = 1, pageSize = 999, ...otherQuery }) => {
              const res = await taskDeptKeepTimeInfoVoPage({
                page: current,
                size: pageSize,
                timeSort: 1,
                ...otherQuery,
                taskId: row?.taskInfos?.id || row?.id
              });
              const sumTime = res.data?.records?.reduce((pre, next) => {
                return pre + (next?.testDurationSum || 0)
              }, 0)
              const sumPrice = res.data?.records?.reduce((pre, next) => {
                return pre + (next?.testUnitPriceSum || 0)
              }, 0)
              return {
                data: (res.data?.records && res.data?.records?.length > 0) ? [...(res.data?.records || []), {
                  deptName: '合计',
                  testDurationSum: sumTime,
                  testUnitPrice: sumTime ? ((sumPrice || 0) / sumTime)?.toFixed(2) : '-',
                  testUnitPriceSum: Number(sumPrice).toFixed(2)
                }] : [],
                success: res.success,
                total: Number(res.data?.total || 0),
              };
            }}
            columns={[
              {
                valueType: 'index',
                title: '序号',
                width: 50,
                fixed: 'left',
              },
              {
                title: '设备名称',
                dataIndex: 'deviceName',
                width: 200,
                fixed: 'left',
                render(dom, entity, index, action, schema) {
                  return entity?.taskDeptKeepTimeSysInstrumentInfoListVo
                    ?.reduce((pre, next) => {
                      return [...pre, ...(next?.instrumentNewInfos || [])];
                    }, [])
                    ?.map((item, index) => {
                      return (
                        <div
                          key={index}
                          style={{
                            marginBottom: 5,
                          }}
                        >
                          <Tag>{item?.sbmc || '-'}</Tag>
                        </div>
                      );
                    });
                },
              },
              {
                title: '设备型号',
                dataIndex: '22',
                width: 120,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity?.taskDeptKeepTimeSysInstrumentInfoListVo
                    ?.reduce((pre, next) => {
                      return [...pre, ...(next?.instrumentNewInfos || [])];
                    }, [])
                    ?.map((item, index) => {
                      return (
                        <div
                          key={index}
                          style={{
                            marginBottom: 5,
                          }}
                        >
                          <Tag>{item?.sbxh || '-'}</Tag>
                        </div>
                      );
                    });
                },
              },
              {
                title: '序列号',
                width: 120,
                dataIndex: '33',
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity?.taskDeptKeepTimeSysInstrumentInfoListVo
                    ?.reduce((pre, next) => {
                      return [...pre, ...(next?.instrumentNewInfos || [])];
                    }, [])
                    ?.map((item, index) => {
                      return (
                        <div
                          key={index}
                          style={{
                            marginBottom: 5,
                          }}
                        >
                          <Tag>{item?.ccbh || '-'}</Tag>
                        </div>
                      );
                    });
                },
              },
              {
                title: '检测室',
                dataIndex: 'deptId',
                ...OnlyInSearch,
                valueType: 'treeSelect',
                fieldProps: {
                  multiple: false,
                  treeDefaultExpandAll: true,
                  fieldNames: {
                    label: 'deptName',
                    value: 'id',
                    children: 'child',
                  },
                },
                request: () => {
                  return orgDeptInfoTree().then((res) => {
                    return res?.data || [];
                  });
                },
              },
              {
                title: '检测室',
                dataIndex: 'deptName',
                align: 'center',
                hideInSearch: true,
              },
              {
                title: '检测内容',
                dataIndex: 'testItem',
                width: 300,
                hideInSearch: true,
              },
              // {
              //   title: '检测室',
              //   dataIndex: 'testProject',
              //   width: 300,
              //   hideInSearch: true,
              // },
              {
                title: '测试人员',
                width: 120,
                dataIndex: 'testUserInfo',
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity?.testUserInfo?.fullName || '-';
                },
              },
              {
                title: '测试人员',
                dataIndex: 'testUserId',
                valueType: 'select',
                hideInTable: true,
                renderFormItem(schema, config, form, action) {
                  return (
                    <ProFormSelect
                      noStyle
                      fieldProps={{
                        style: { width: '100%' },
                      }}
                      type="user"
                      query={{
                        roleId: ROLE_TYPE.testPerson,
                      }}
                    />
                  );
                },
              },
              {
                title: '开始日期',
                dataIndex: 'testStartTime',
                width: 150,
                valueType: 'date',
                hideInSearch: true,
                render(text, record, index, action) {
                  return record?.testStartTime
                    ? dayjs(record?.testStartTime)?.format('YYYY年 MM月 DD日')
                    : '-';
                },
              },
              {
                title: '结束日期',
                dataIndex: 'testEndTime',
                width: 150,
                valueType: 'date',
                hideInSearch: true,
                render(text, record, index, action) {
                  return record?.testEndTime
                    ? dayjs(record?.testEndTime)?.format('YYYY年 MM月 DD日')
                    : '-';
                },
              },
              {
                title: '测试时长',
                dataIndex: 'testDurationSum',
                width: 150,
                hideInSearch: true,
                render(text, record, index, action) {
                  return record?.testDurationSum + '小时';
                },
              },
              {
                title: '测试单价（元）',
                dataIndex: 'testUnitPrice',
                width: 150,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                    return Number(entity?.testUnitPrice)?.toFixed(2)
                },
              },
              {
                title: '测试总价（元）',
                dataIndex: 'testUnitPriceSum',
                width: 150,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return Number(entity?.testUnitPriceSum)?.toFixed(2)
              },
              },
              {
                title: '附件',
                dataIndex: 'fileInfos',
                width: 320,
                hideInSearch: true,
                render(dom, entity, index, action, schema) {
                  return entity.fileInfos?.map((item: BASE.FileInfo) => {
                    return <FileDownload key={item.id} item={item} />;
                  });
                },
              },
              {
                title: '备注',
                dataIndex: 'remark',
                width: 320,
                hideInSearch: true,
              },
              {
                title: '状态',
                dataIndex: 'statusInfo',
                width: 120,
                hideInSearch: true,
              },
              {
                title: '操作',
                valueType: 'option',
                fixed: 'right',
                render(_, entity, index, action) {
                  return (
                    <Action>
                      {(entity?.statusInfo === '待核算' || entity?.statusInfo === '已核算') && (
                        <Text
                        onClick={() => {
                          alert_detail({
                            row: {
                              ...entity,
                              testAllTime: sumTestDuration(
                                entity?.taskDeptKeepTimeChildInfoListVo || [],
                              ),
                              instrumentList:
                                entity?.taskDeptKeepTimeSysInstrumentInfoListVo?.reduce(
                                  (pre, next) => {
                                    const list = next?.instrumentNewInfos?.map((k) => {
                                      return {
                                        sysName: next?.sysName,
                                        fieldName: next?.fieldName,
                                        ...k,
                                      };
                                    });
                                    return [...(pre || []), ...(list || [])];
                                  },
                                  [],
                                ),
                            },
                          });
                        }}
                      >
                        详情
                      </Text>
                      )}
                    </Action>
                  );
                },
              },
            ]}
          />
    </>
  );
};

export default TestRecordDetail;
