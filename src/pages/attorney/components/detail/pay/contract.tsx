import Title from '@/components/title';
import MyProDescriptions from '../ProDescription';
import { ProDescriptionsProps } from '@/pages/attorney/components/proDescriptions';
import { CommonType } from '..';

type BaseDetailProps = {
  row?: CommonType;
};

const ContractDetail: React.FC<BaseDetailProps> = (props) => {
  const { row } = props;
  const columns: ProDescriptionsProps['columns'] = [
    {
      dataIndex: 'contractAmount',
      title: '合同金额（元）',
      render(dom, entity, index, action, schema) {
        return (entity?.contractAmount && entity?.contractAmount + '元') || '-';
      },
    },
    {
      title: '是否计时',
      render(dom, entity, index, action, schema) {
        return !!entity?.tfTiming ? '是' : '否';
      },
    },
    {
      title: '预估单价（元/小时）',
     dataIndex: 'price'
    },
    {
      dataIndex: 'invoicTotal',
      title: '开票总额（元）',
    },
    {
      dataIndex: 'receiptTotal',
      title: '到账总额（元）',
    },
    {
      dataIndex: 'contractBalancePay',
      title: '合同尾款（元）',
    },
    {
      dataIndex: 'foreignTotal',
      title: '支付总额（元）',
    },
    {
      dataIndex: 'payRemark',
      title: '备注',
    },
  ];

  return (
    <>
      <Title title="合同信息" />
      <MyProDescriptions
        columns={columns}
        column={3}
        dataSource={row}
      />
    </>
  );
};

export default ContractDetail;
