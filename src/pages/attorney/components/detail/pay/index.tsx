import BillDetail from '@/pages/task/list/detail/bill';
import { useModel } from '@umijs/max';
import ContractDetail from './contract';
import { CommonType } from '..';
import PayInfo from './payInfo';
import ReceivedInfoDetail from './received';
import TestRecordDetail from './testRecord';

type BaseDetailProps = {
  row?: CommonType;
};

const PayInfoDetail: React.FC<BaseDetailProps> = ({ row }) => {
  const { initialState } = useModel('@@initialState');

  return (
    <>
       {/* 受理人员，项目经理，市场主管， 销售人员可以看合同信息 */}
      
        <>
          <ContractDetail row={row} />
          <BillDetail row={row} />
          <ReceivedInfoDetail row={row}/>
          <PayInfo row={row} />
          {
            !!row?.taskInfos?.tfTiming && (
              <TestRecordDetail row={row}/>
            )
          }
        </>
      
      
    </>
  );
};

export default PayInfoDetail;
