import Title from '@/components/title';
import ProTable from '@/pages/attorney/components/proTable';

type BaseDetailProps = {
  row?: BASE.EntrustInfoVO;
};

const ReceivedInfoDetail: React.FC<BaseDetailProps> = ({ row }) => {
  return (
    <>
      <Title title="已到账信息" />
      <ProTable
        search={false}
        options={false}
        pagination={false}
        newdataSource={
          row?.entrustApplyInfo?.entrustJson &&
          JSON.parse(row?.entrustApplyInfo?.entrustJson).payReceiptInfos
        }
        cardProps={{
          bodyStyle: {
            padding: 0,
          },
        }}
        rowKey="id"
        dataSource={row?.payReceiptInfos || []}
        columns={[
          {
            title: '序号',
            valueType: 'index',
          },
          {
            title: '付款单位',
            dataIndex: 'paymentEnt',
            initialValue: row?.entrustEntName,
            fieldProps: { style: { width: '100%' } },
          },
          {
            title: '付款金额（元）',
            dataIndex: 'paymentAmount',
            valueType: 'digit',
            fieldProps: { style: { width: '100%' } },
          },
          {
            title: '付款时间',
            dataIndex: 'paymentDate',
            valueType: 'date',
            fieldProps: { style: { width: '100%' } },
          },
        ]}
      />
    </>
  );
};

export default ReceivedInfoDetail;
