import Empty from '@/components/empty';
import InfiniteList, { InfiniteListRef } from '@/components/InfiniteList';
import color from '@/config/color';

import { history, useModel, useRequest } from '@umijs/max';
import { Badge, Tabs, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useRef } from 'react';

import styles from './index.less';

export const TypeUrl: { [k: string]: { url: string; color: string } } = {
  下发委托书: {
    url: '/attorney',
    color: color[0],
  },
  审核报告: {
    url: '/report',
    color: color[1],
  },
  审核原始记录: {
    url: '/task/plan',
    color: color[2],
  },
  报告修改: {
    url: '/report',
    color: color[3],
  },
  委托书修改: {
    url: '/attorney',
    color: color[4],
  },
  测试任务反馈: {
    url: '/task/plan',
    color: color[5],
  },
  进网委托书验证: {
    url: '/task/list',
    color: color[6],
  },
};

const TodoItem: React.FC<{ item: any }> = ({ item }) => {
  return (
    <Badge.Ribbon
      text={item?.procVars?.bus_type}
      color={item?.procVars && TypeUrl[item?.procVars?.bus_type]?.color}
    >
      <div className={styles['list_item']}>
        <div
          className={styles['list_item_title']}
          onClick={() => {
            if (item?.procVars && TypeUrl?.[item?.procVars?.bus_type]) {
              history.push(TypeUrl[item?.procVars?.bus_type].url, {
                id: item?.procVars?.record_task_id || item?.procVars?.bus_id,
                activeKey: item?.procVars?.bus_type, // 处理跳转到页面定位到某个tab页签的情况
              });
            }
          }}
        >
          <Tooltip title={item?.taskName}>{item?.taskName}</Tooltip>
        </div>
        <div className={styles['list_item_info']}>
          <span>{`申请人：${item?.startUserName || '-'}`}</span>
          <span>{`时间：${dayjs(item?.createTime).format('YYYY-MM-DD')}`}</span>
        </div>
      </div>
    </Badge.Ribbon>
  );
};

const TodoList = ({ getCurrentKey }: { getCurrentKey?: (key: string) => void }) => {
  const { initialState } = useModel('@@initialState');
  const listRef = useRef<InfiniteListRef | null>(null);


  return (
    <div className={styles['container']}>
      <Tabs
        defaultActiveKey="1"
        size="small"
        style={{ width: '100%' }}
        onChange={(key) => {
          getCurrentKey?.(key);
        }}
        items={[
          {
            key: 'todo',
            label: `待办`,
            children: (
              <div className={styles['list']}>
                <InfiniteList
                  listHeight={500}
                  // request={todoReuqest as any}
                  query={{ userId: String(initialState?.currentUser?.id) }}
                >
                  {(data) => {
                    return (
                      (data?.length &&
                        data?.map((item, index) => {
                          return <TodoItem key={index} item={item} />;
                        })) || <Empty />
                    );
                  }}
                </InfiniteList>
              </div>
            ),
          },
          {
            key: 'done',
            label: `已办`,
            children: (
              // <Skeleton loading={doneReuqest?.loading} active>
              //   <div className={styles['list']}>
              //     {(doneReuqest?.data?.records?.length &&
              //       doneReuqest?.data?.records?.map((item, index) => {
              //         return <TodoItem key={index} item={item} />;
              //       })) || <Empty />}
              //   </div>
              // </Skeleton>
              <div className={styles['list']}>
                <InfiniteList
                  listHeight={500}
                  // request={doneReuqest as any}
                  query={{ userId: String(initialState?.currentUser?.id) }}
                >
                  {(data) => {
                    return (
                      (data?.length &&
                        data?.map((item, index) => {
                          return <TodoItem key={index} item={item} />;
                        })) || <Empty />
                    );
                  }}
                </InfiniteList>
              </div>
            ),
          },
          {
            key: 'start',
            label: `我发起的`,
            children: (
              <div className={styles['list']}>
                <InfiniteList
                  listHeight={500}
                  // request={startRequest as any}
                  query={{ userId: String(initialState?.currentUser?.id) }}
                >
                  {(data) => {
                    return (
                      (data?.length &&
                        data?.map((item: any, index) => {
                          return (
                            <Badge.Ribbon
                              key={index}
                              text={
                                (item?.processStatus === 'running' && item?.procDefName) || '已完成'
                              }
                              color={
                                (item?.processStatus === 'running' && color[4]) ||
                                'geekblue-inverse'
                              }
                            >
                              <div className={styles['list_item']}>
                                <div
                                  className={styles['list_item_title']}
                                  onClick={() => {
                                    // if (item?.procVars && TypeUrl?.[item?.procVars?.bus_type]) {
                                    //   history.push(TypeUrl[item?.procVars?.bus_type].url, {
                                    //     id: item?.procVars?.bus_id,
                                    //   });
                                    // }
                                  }}
                                >
                                  {item?.taskName}
                                </div>
                                <div className={styles['list_item_info']}>
                                  {/* <span>{`申请人：${item?.startUserName || '-'}`}</span> */}
                                  <span>{`时长：${item?.duration}`}</span>
                                  {item?.finishTime && (
                                    <span>{`结束时间：${
                                      dayjs(item?.finishTime).format('YYYY-MM-DD HH:mm:ss') || '--'
                                    }`}</span>
                                  )}
                                </div>
                              </div>
                            </Badge.Ribbon>
                          );
                        })) || <Empty />
                    );
                  }}
                </InfiniteList>
              </div>
            ),
          },
        ]}
      />
    </div>
  );
};
export default TodoList;
