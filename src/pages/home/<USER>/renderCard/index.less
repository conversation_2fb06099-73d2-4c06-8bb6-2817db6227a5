.home-common-card {
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.06);
  border-radius: 2px;
  padding: 16px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .home-common-card-icon {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 164px;
    height: 164px;
    background-image: url('@/assets/home/<USER>');
    background-size: 100% 100%;
  }
  .home-common-card-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .home-common-card-title {
      font-size: 20px;
      font-weight: 600;
      color: #333333;
      position: relative;
      padding-left: 10px;
      &::before {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        content: '';
        display: inline-block;
        width: 6px;
        height: 18px;
        background: #006569;
      }
    }
  }
  .home-common-card-content {
    margin-top: 20px;
    display: flex;
    gap: 80px;
    .home-common-card-content-item {
      display: flex;
      flex-direction: column;
      gap: 15px;
      position: relative;
      &:not(:last-child)::before{
        position: absolute;
        right: -40px;
        top: 50%;
        transform: translateY(-50%);
        content: '';
        display: inline-block;
        width: 1px;
        height: 46px;
        background: #E6E6E6;;
      }
      .home-common-card-content-item-title {
        font-size: 14px;
        font-weight: 600;
        color: #666666;
      }
      .home-common-card-content-item-value {
        line-height: 1;
        font-size: 44px;
        font-weight: 600;
        color: rgba(10, 48, 32, 1);
      }
    }
  }
}