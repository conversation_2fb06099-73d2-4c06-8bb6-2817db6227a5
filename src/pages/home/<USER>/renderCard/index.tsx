import './index.less';
interface RenderCardProps {
  boxStyle: React.CSSProperties;
  title: string;
  items: any[];
}

function RenderCard({ boxStyle, title, items }: RenderCardProps) {
  return (
    <div
      style={{
        ...boxStyle,
      }}
      className="home-common-card"
    >
      <div className="home-common-card-icon"></div>
      <div className="home-common-card-header">
        <div className="home-common-card-title">{title}</div>
      </div>
      <div className="home-common-card-content">{items?.map((item, index) => {
        return (
          <div key={index} className="home-common-card-content-item">
            <div className="home-common-card-content-item-title">{item.title}</div>
            <div className="home-common-card-content-item-value">{item.value}</div>
          </div>
        )
      })}</div>
    </div>
  );
}

export default RenderCard;
