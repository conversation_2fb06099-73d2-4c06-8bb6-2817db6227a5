import color from '@/config/color';
import { ROLENAMEobj } from '@/enums';
import { environmentInfoGetWeather } from '@/services/base/tianqixinxiguanlijiekou';
import { Access, useAccess, useModel, useRequest } from '@umijs/max';
import { Avatar, Divider, Skeleton, Space, Tag } from 'antd';
import styles from './index.less';
// 通过时间获取不同的问候语
const getWelcome = () => {
  const hour = new Date().getHours();
  if (hour >= 0 && hour < 6) {
    return '凌晨好';
  } else if (hour >= 6 && hour < 12) {
    return '上午好';
  } else if (hour >= 12 && hour < 14) {
    return '中午好';
  } else if (hour >= 14 && hour < 18) {
    return '下午好';
  } else {
    return '晚上好';
  }
};

const Welcome = () => {
  const { initialState } = useModel('@@initialState');
  const { commonAccess } = useAccess();

  // const weatherRequest = useRequest(() => {
  //   return environmentInfoGetWeather({}); 
  // });

  return (
    <div className={styles['container']}>
      <div className={styles['container_left']}>
        <div className={styles['avater']}>
          <Avatar size={65} src={require('../../assets/avatar.png')} />
        </div>
        <div className={styles['user_info']}>
          <div className={styles['welcome']}>{`${getWelcome()}, 「${
            initialState?.currentUser?.fullName
          }」，祝你开心每一天！`}</div>
          <div className={styles['role']}>
            <Space split={<Divider type="vertical" />}>
              <Space>
                {initialState?.currentUser?.extendData?.orgUserRoleFullNames?.map((item, index) => {
                  return <Tag key={index} color={color[index]}>{item}</Tag>;
                })}
              </Space>
              <Space>
                {initialState?.currentUser?.extendData?.orgDeptInfos?.map((item) => {
                  return <div key={item.id}>{item.deptName}</div>;
                })}
              </Space>
            </Space>
            {/* {`项目经理 | (重庆信息通信研究院-信通院（西安）科技创新中心有限公司-综合服务部)`} */}
          </div>
        </div>
      </div>
      <div className={styles['container_right']}>
        <Access accessible={commonAccess('admin:/bigScreenTwo')}>
          <a href="#/bigScreenTwo">监控中心</a>
        </Access>
        {/* <Skeleton loading={weatherRequest?.loading} active>
          <>
            <img src={require('../../assets/sun.png')} />
            <div className={styles['right_item']}>
              <div className={styles['title']}>温度</div>
              <div className={styles['num']}>{weatherRequest?.data?.temperature}°</div>
            </div>
          </>
          <div className={styles['right_item']}>
            <div className={styles['title']}>相对湿度</div>
            <div className={styles['num']}>
              {weatherRequest?.data?.humidity?.split('%')?.[0]}
              <span className={styles['unit']}>%</span>
            </div>
          </div>
          <div className={styles['right_item']}>
            <div className={styles['title']}>大气压</div>
            <div className={styles['num']}>
              {weatherRequest?.data?.pressure}
              <span className={styles['unit']}>KPa</span>
            </div>
          </div>
        </Skeleton> */}
      </div>
    </div>
  );
};

export default Welcome;
