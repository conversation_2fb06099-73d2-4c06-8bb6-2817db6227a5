@marginPx: 15px;

.container {
    display: flex;
    flex: 0 0 120px;
    align-items: center;
    justify-content: space-between;
    margin-bottom: @marginPx;
    padding: 15px 30px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 18px 0 rgba(33, 85, 181, 0.1);
    &_left {
      // flex:  0 0 300px;
      display: flex;
      .user_info {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        margin-left: 20px;
        .welcome {
          color: #383838;
          font-size: 20px;
        }
        .role {
          color: #a6a6a6;
          font-size: 14px;
        }
      }
    }
    &_right {
      display: flex;
      align-items: center;
      justify-self: flex-end;
      .right_item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 135px;
        .title {
          color: #979797;
          font-size: 14px;
        }
        .num {
          color: #2b2b2b;
          font-size: 30px;
        }
        .unit {
          color: #bbb;
          font-size: 15px;
        }
        &:last-child {
          &::after {
            display: none;
          }
        }
        &:not(:last-child)::after {
          position: absolute;
          top: 50%;
          right: 0;
          width: 1px;
          height: 40px;
          background: #e9e9e9;
          transform: translateY(-50%);
          content: '';
        }
      }
    }
  }