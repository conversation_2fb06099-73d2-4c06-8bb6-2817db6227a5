import Empty from '@/components/empty';
import { taskInfoGetTask } from '@/services/base/renwujichuxinxibiaojiekou';
import { history, useModel, useRequest } from '@umijs/max';
import { Badge, Skeleton } from 'antd';
import dayjs from 'dayjs';
import styles from './index.less';

const TaskList = () => {
  const { initialState } = useModel('@@initialState');

  const todoReuqest = useRequest(() => {
    // return taskInfoGetTask({
    //   id: String(initialState?.currentUser?.id),
    // });
  });

  return (
    <div className={styles['container']}>
      <Skeleton loading={todoReuqest?.loading} active>
        <div className={styles['list']}>
          {(todoReuqest?.data?.length &&
            todoReuqest?.data?.map((item, index) => {
              return (
                <Badge.Ribbon text={item?.taskStatus} key={index}>
                  <div className={styles['list_item']} onClick={()=>{
                    history.push('/task/list', {
                      id: item?.taskId,
                    })
                  }}>
                    <div
                      className={styles['list_item_title']}
                    >{`任务编号：${item.number} | ${item?.name}`}</div>
                    <div className={styles['list_item_info']}>
                      <span>{`委托方：${
                        item?.entrustEntNameFromTask || item?.entrustEntNameFromEntrust || '-'
                      }`}</span>
                      <div>
                        <span style={{ marginRight: 20 }}>{`受理人：${
                        item?.officer || '-'
                      }`}</span>
                        <span>{`受理时间：${
                          (item?.acceptDate && dayjs(item?.acceptDate).format('YYYY-MM-DD')) || '-'
                        }`}</span>
                      </div>
                    </div>
                  </div>
                </Badge.Ribbon>
              );
            })) || <Empty />}
        </div>
      </Skeleton>
    </div>
  );
};
export default TaskList;
