import { useAccess, Access } from '@umijs/max';
import HomeCard from '../components/renderCard';
import RenderTable from '../components/renderTable';
import './index.less';
import { history } from 'umi';
function CheckedCenterDirector({ userInfo, topData, bottomData }: { userInfo: any, topData: any, bottomData: any }) {
  const { commonAccess } = useAccess();
  const items = [
    {
      title: '工单',
      items: [
        {
          title: '累计工单',
          value: topData?.dataInfoDto?.all || 0,
        },
        {
          title: '今日工单',
          value: topData?.dataInfoDto?.today || 0,
        },
      ],
    },
    {
      title: '待检',
      items: [
        {
          title: '待检中',
          value: topData?.waitToCheckDto?.all || 0,
        },
        {
          title: '今日新增待检',
          value: topData?.waitToCheckDto?.today || 0,
        },
      ],
    },
    {
      title: '在检',
      items: [
        {
          title: '在检中',
          value: topData?.checkingDto?.all || 0,
        },
        {
          title: '今日新增在检',
          value: topData?.checkingDto?.today || 0,
        },
      ],
    },
    {
      title: '检毕',
      items: [
        {
          title: '累计检毕',
          value: topData?.checkOverDto?.all || 0,
        },
        {
          title: '今日检毕',
          value: topData?.checkOverDto?.today || 0,
        },
      ],
    },
  ];
  const handleColumnClick = (id: any) => {
    history.push('/task/order?id=' + id);
  };
  return (
    <div className="checked-center-director-container">
      <div className="checked-center-director-welcome">
        <div>检测人员 {userInfo?.fullName} 您好！</div>
        <Access accessible={commonAccess('admin:/bigScreenTwo')}>
          <a href="#/bigScreenTwo">监控中心</a>
        </Access>
      </div>
      <div
        style={{
          display: 'flex',
          gap: '14px',
        }}
      >
        {items.map((item) => {
          return (
            <HomeCard
              key={item.title}
              title={item.title}
              items={item.items}
              boxStyle={{
                width: `calc(100% / ${items.length} - ${items.length - 1} * 14px / ${
                  items.length
                })`,
                height: 220,
              }}
            />
          );
        })}
      </div>
      <div className="checked-center-director-table-container">
        <div>
          <div className="checked-center-director-table-title">待办</div>
          <RenderTable
            title="开始工单"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={bottomData?.waittingDto?.topNumbers?.map((item: any) => ({
              name: item,
            })) || []}
          />
          <RenderTable
            title="检毕工单"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={bottomData?.waittingDto?.bottomNumbers?.map((item: any) => ({
              name: item,
            })) || []}
          />
        </div>
        <div>
          <div className="checked-center-director-table-title">已办</div>
          <RenderTable
            title="开始工单"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={bottomData?.doneDto?.topNumbers?.map((item: any) => ({
              name: item,
            })) || []}
          />
          <RenderTable
            title="检毕工单"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={bottomData?.doneDto?.topNumbers?.map((item: any) => ({
              name: item,
            })) || []}
          />
        </div>
        <div>
          <div className="checked-center-director-table-title">今日发起</div>
          <RenderTable
            title="开始工单"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={bottomData?.todayGitDto?.topNumbers?.map((item: any) => ({
              name: item,
            })) || []}
          />
          <RenderTable
            title="检毕工单"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={bottomData?.todayGitDto?.bottomNumbers?.map((item: any) => ({
              name: item,
            })) || []}
          />
        </div>
      </div>
    </div>
  );
}

export default CheckedCenterDirector;
