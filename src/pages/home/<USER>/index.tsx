import HomeCard from '../components/renderCard';
import RenderTable from '../components/renderTable';
import './index.less';
import { useAccess, Access } from '@umijs/max';
interface TestingCenterDirectorProps {
  userInfo: any;
  topData: any;
  bottomData: any;
}

function TestingCenterDirector({ userInfo, topData, bottomData }: TestingCenterDirectorProps) {
  const { commonAccess } = useAccess();
  const items = [
    {
      title: '样品',
      items: [
        {
          title: '累计样品',
          value: topData?.sampleInfoDto?.all || 0,
        },
        {
          title: '今日收样',
          value: topData?.sampleInfoDto?.todayGetSample || 0,
        },
        {
          title: '今日返样',
          value: topData?.sampleInfoDto?.todayBackSample || 0,
        },
      ],
    },
    {
      title: '任务',
      items: [
        {
          title: '累计任务',
          value: topData?.taskInfoDto?.all || 0,
        },
        {
          title: '今日新增待检',
          value: topData?.taskInfoDto?.todayWaitToCheck || 0,
        },
        {
          title: '今日新增在检',
          value: topData?.taskInfoDto?.todayChecking || 0,
        },
        {
          title: '今日检毕',
          value: topData?.taskInfoDto?.todayOverCheck || 0,
        },
      ],
    },
    {
      title: '报告',
      items: [
        {
          title: '累计报告',
          value: topData?.reportInfoDto?.all || 0,
        },
        {
          title: '今日报告',
          value: topData?.reportInfoDto?.today || 0,
        },
      ],
    },
  ];
  
  return (
    <div className="testing-center-director-container">
      <div className="testing-center-director-welcome">
        <div>检测中心主任 {userInfo?.fullName} 您好！</div>
        <Access accessible={commonAccess('admin:/bigScreenTwo')}>
          <a href="#/bigScreenTwo">监控中心</a>
        </Access>
      </div>
      <div
        style={{
          display: 'flex',
          gap: '14px',
        }}
      >
        {items.map((item) => {
          return (
            <HomeCard
              key={item.title}
              title={item.title}
              items={item.items}
              boxStyle={{
                width: `calc(100% / ${items.length} - ${items.length - 1} * 14px / ${
                  items.length
                })`,
                height: 220,
              }}
            />
          );
        })}
      </div>
      <div className="testing-center-director-table-container">
        <div>
          <div className="testing-center-director-table-title">设备信息汇总</div>
          <RenderTable
            title="设备信息汇总"
            hasMore={true}
            columns={[
              {
                title: '设备名',
                dataIndex: 'name',
              },
              {
                title: '工作状态',
                dataIndex: 'status',
              },
              {
                title: '距下次年检时间',
                dataIndex: 'nextYearDay',
              },
            ]}
            data={bottomData?.instrumentInfoDtos || []}
            tableScroll={320}
          />
        </div>
        <div>
          <div className="testing-center-director-table-title">人员信息汇总</div>
          <RenderTable
            title="人员信息汇总"
            hasMore={true}
            columns={[
              {
                title: '姓名',
                dataIndex: 'name',
              },
              {
                title: '工作状态',
                dataIndex: 'status',
              },
            ]}
            data={bottomData?.userInfoDtos || []}
            tableScroll={320}
          />
        </div>
      </div>
    </div>
  );
}

export default TestingCenterDirector;
