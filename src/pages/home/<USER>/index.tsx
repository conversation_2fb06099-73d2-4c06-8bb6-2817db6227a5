import { Access, useAccess } from '@umijs/max';
import { history } from 'umi';
import HomeCard from '../components/renderCard';
import RenderTable from '../components/renderTable';
import './index.less';
function SampleAdministrator({
  userInfo,
  topData,
  bottomData,
}: {
  userInfo: any;
  topData: any;
  bottomData: any;
}) {
  const { commonAccess } = useAccess();
  const items = [
    {
      title: '收样',
      items: [
        {
          title: '累计收样',
          value: topData?.getSampleDto?.all || 0,
        },
        {
          title: '今日收样',
          value: topData?.getSampleDto?.today || 0,
        },
      ],
    },
    {
      title: '返样',
      items: [
        {
          title: '累计返样',
          value: topData?.backSampleDto?.all || 0,
        },
        {
          title: '今日返样',
          value: topData?.backSampleDto?.today || 0,
        },
      ],
    },
    {
      title: '任务',
      items: [
        {
          title: '累计任务',
          value: topData?.taskInfoDto?.all || 0,
        },
        {
          title: '今日任务',
          value: topData?.taskInfoDto?.today || 0,
        },
      ],
    },
  ];
  const handleMore = () => {
    history.push('/taskManage/sample');
  };
  const handleColumnClick = (id: any) => {
    history.push('/taskManage/sample?id=' + id);
  };
  return (
    <div className="sample-administrator-container">
      <div className="sample-administrator-welcome">
        <div>样品管理员 {userInfo?.fullName} 您好！</div>
        <Access accessible={commonAccess('admin:/bigScreenTwo')}>
          <a href="#/bigScreenTwo">监控中心</a>
        </Access>
      </div>
      <div
        style={{
          display: 'flex',
          gap: '14px',
        }}
      >
        {items.map((item) => {
          return (
            <HomeCard
              key={item.title}
              title={item.title}
              items={item.items}
              boxStyle={{
                width: `calc(100% / ${items.length} - ${items.length - 1} * 14px / ${
                  items.length
                })`,
                height: 220,
              }}
            />
          );
        })}
      </div>
      <div className="sample-administrator-table-container">
        <div>
          <div className="sample-administrator-table-title">待办</div>
          <RenderTable
            title="返样"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any, row: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>
                      {params}
                    </span>
                  );
                },
              },
            ]}
            data={
              bottomData?.waittingDto?.topNumbers?.map((item: any) => ({
                name: item,
              })) || []
            }
          />

          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 0',
            }}
          >
            <div style={{ fontSize: 14, fontWeight: 600 }}>收样</div>
            <div>
              {
                <div
                  style={{ color: 'rgba(96, 94, 92, 1)', cursor: 'pointer' }}
                  onClick={handleMore}
                >
                  更多 {'>'}{' '}
                </div>
              }
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              background: '#F9FAFA',
              padding: '16px',
              borderRadius: 8,
              height: '100%',
            }}
          >
            <span
              style={{
                fontSize: 14,
                fontWeight: 600,
                cursor: 'pointer',
                color: 'rgba(0, 101, 105, 1)',
              }}
              onClick={handleMore}
            >
              样品收样
            </span>
          </div>
        </div>
        <div>
          <div className="sample-administrator-table-title">已办</div>
          <RenderTable
            title="收样"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={
              bottomData?.doneDto?.topNumbers?.map((item: any) => ({
                name: item,
              })) || []
            }
          />
          <RenderTable
            title="返样"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={
              bottomData?.doneDto?.bottomNumbers?.map((item: any) => ({
                name: item,
              })) || []
            }
          />
        </div>
        <div>
          <div className="sample-administrator-table-title">今日发起</div>
          <RenderTable
            title="收样"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={
              bottomData?.todayGitDto?.topNumbers?.map((item: any) => ({
                name: item,
              })) || []
            }
          />
          <RenderTable
            title="返样"
            hasMore={true}
            columns={[
              {
                title: '样品编号',
                dataIndex: 'name',
                render: (params: any) => {
                  return (
                    <span style={{ cursor: 'pointer' }} onClick={() => handleColumnClick(params)}>{params}</span>
                  );
                },
              },
            ]}
            data={
              bottomData?.todayGitDto?.bottomNumbers?.map((item: any) => ({
                name: item,
              })) || []
            }
          />
        </div>
      </div>
    </div>
  );
}

export default SampleAdministrator;
