import { Table } from "antd";
import { history } from 'umi';
interface RenderTableProps {
  columns: any[];
  data: any[];
  title: string;
  hasMore: boolean;
  tableScroll?: number;
}

const urlMap = {
  '任务': '/task/listData',
  '返样': '/taskManage/sample',
  '收样': '/taskManage/sample',
  '设备': '/device/newInstrument',
  '人员': '/userManage/user',
  '工单': '/task/order',
}
function RenderTable({ columns, data, title, hasMore, tableScroll = 180 }: RenderTableProps) {
  let url = '';
  Object.keys(urlMap).forEach((key) => {
    if (title.includes(key)) {
      url = urlMap[key as keyof typeof urlMap];
    }
  });
  const handleMore = () => {
    history.push(url);
  }

  return (
    <div style={{ width: '100%', height: '50%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px 0' }}>
        <div style={{ fontSize: 14, fontWeight: 600 }}>{title}</div>
        <div>{hasMore && <div style={{ color: 'rgba(96, 94, 92, 1)', cursor: 'pointer' }} onClick={handleMore}>更多 {'>'} </div>}</div>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey={(record) => record.id?.toString() || Math.random().toString()}
        pagination={false}
        scroll={{ y: tableScroll }}
        style={{ minHeight: tableScroll, flex: 1 }}
      />
    </div>
  );
}

export default RenderTable;
