import Empty from '@/components/empty';
import { ATTORNEY_TYPE_MAP } from '@/enums/attorney';
import { entrustInfoGetEntrust, entrustInfoVoPage } from '@/services/base/weituoshujichuxinxibiaojiekou';
import { useModel,history } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Avatar, Skeleton } from 'antd';
import dayjs from 'dayjs';
import styles from './index.less';

// 使用dayjs计算创建时间距离现在时间，如果小于1小时，显示分钟，小于1天，显示小时，大于1天，显示天小时
const getCreateTimeBefore = (createTime: string) => {
  const minute = dayjs().diff(dayjs(createTime), 'minute');
  if (minute < 60) {
    return `${minute}分钟前`;
  } else if (minute < 60 * 24) {
    return `${Math.floor(minute / 60)}小时前`;
  } else {
    return `${Math.floor(minute / 60 / 24)}天前`;
  }
};

const AttoryneyList: React.FC = () => {

  const { initialState } = useModel('@@initialState');

  const atteroneyRequest = useRequest(() => {
    return entrustInfoGetEntrust({ id: String(initialState?.currentUser?.id) });
  });
  
  return (
    <Skeleton loading={atteroneyRequest.loading} active paragraph={{ rows: 6 }} style={{padding:10}}>
      <div className={styles['attorney_item_container']}>
      {atteroneyRequest?.data?.data?.length &&  atteroneyRequest?.data?.data?.slice(0, 5)?.map((item, index) => {
        return (
          <div key={index} className={styles['attorney_item']} onClick={()=>{
            history.push(`/attorney`,{
              id:item.entrustId
            })
          }}>
            <div className={styles['attorney_item_type']}>
              <Avatar
                size={24}
                style={{ marginRight: 8 }}
                src={'https://img.xiaopiu.com/static/ant_design_pro/20.png'}
              />
              {ATTORNEY_TYPE_MAP.get(item.type!)}
            </div>
            <div className={styles['attorney_item_title']}>{item?.entrustName}</div>
            <div className={styles['attorney_item_info']}>
              <span className={styles['attorney_item_info_company']}>{item?.entrustEntName}</span>
              <span>{getCreateTimeBefore(item.createTime!)}</span>
            </div>
          </div>
        );
      }) || <Empty  />}
      </div>
      
    </Skeleton>
  );
};

export default AttoryneyList;
