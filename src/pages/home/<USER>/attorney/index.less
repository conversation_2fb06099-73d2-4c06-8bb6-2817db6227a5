.attorney_item_container {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  width: 100%;
  .attorney_item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 33%;
    height: 117px;
    padding: 10px;
    font-size: 14px;
    border-bottom: 1px solid #e9e9e9;
    &:not(:nth-child(3n)) {
      border-right: 1px solid #e9e9e9;
    }
  
    &_type {
      display: flex;
      align-items: center;
      color: @primary;
      font-weight: bold;
    }
    &_title {
      display: -webkit-box;
      overflow: hidden;
      color: rgba(0, 0, 0, 0.45);
      font-weight: bold;
      text-overflow: ellipsis;
      cursor: pointer;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      flex: 1;
      margin: 10px 0;
      &:hover {
        color: @primary;
      }
    }
    &_info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      &_company {
        flex: 1;
        overflow: hidden;
        color: @primary;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &_time {
        flex: 0 0 80px;
      }
    }
  }
}

