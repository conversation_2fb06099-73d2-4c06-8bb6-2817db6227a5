import Empty from '@/components/empty';
import { color } from '@/config/color';
// import { taskDeptInfoGetTestTask } from '@/services/base/renwujianceshirenwubiaojiekou';
import { history, useModel, useRequest } from '@umijs/max';
import { Badge, Skeleton } from 'antd';
import dayjs from 'dayjs';
import styles from './index.less';

export const PlanStatusColor: { [k: string]: string } = {
  待受理:color.orange,
  待分配: color.cyan,
  测试中: color.blue,
  任务完成: color.green,
  已结项:color.primary,
  已撤销:'volcano-inverse',
  委托书修改审核中: color.cyan,
};



const PlanList = () => {
  const { initialState } = useModel('@@initialState');

  const planReuqest = useRequest(() => {
    // return taskDeptInfoGetTestTask();
  });
  return (
    <div className={styles['container']}>
      <Skeleton loading={planReuqest?.loading} active>
        <div className={styles['list']}>
          {(planReuqest?.data?.length &&
            planReuqest?.data?.map((item, index) => {
              return (
                <Badge.Ribbon
                  text={item?.taskStatus}
                  color={PlanStatusColor?.[item.taskStatus || '待分配']}
                  key={index}
                >
                  <div
                    className={styles['list_item']}
                    onClick={() => {
                      history.push('/task/plan', {
                        id: item?.id,
                      });
                    }}
                  >
                    <div
                      className={styles['list_item_title']}
                    >{`任务名称：${item?.name}`}</div>
                    <div className={styles['list_item_info']}>
                      <span>{`委托单位：${item?.entrustEntName || '-'}`}</span>
                      <div>
                        {/* <span style={{ marginRight: 20 }}>{`检测员：${item?.testers?.map(i=>i.fullName).join('、')}`}</span> */}
                        <span>{`受理时间：${dayjs(item?.acceptanceDate).format('YYYY-MM-DD')}`}</span>
                      </div>
                    </div>
                  </div>
                </Badge.Ribbon>
              );
            })) || <Empty />}
        </div>
      </Skeleton>
    </div>
  );
};
export default PlanList;
