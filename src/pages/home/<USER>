import { getInitialState } from '@/app';
import {
  workStationBottomDataCount,
  workStationTopDataCount,
} from '@/services/base/gongzuotaiguanlijiekou';
import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import Checked from './checked';
import ProductionScheduler from './productionScheduler';
import SampleAdministrator from './sampleAdministrator';
import TestingCenterDirector from './testingCenterDirector';

const roleMap = {
  detectAdmin: TestingCenterDirector, // 检测中心主任
  detectPerson: Checked, // 检测员
  sampleManager: SampleAdministrator, // 样品管理员
  prodScheduler: ProductionScheduler, // 生产调度员
};
const defaultRole = 'detectAdmin';
const Home: React.FC = () => {
  const [userInfo, setUserInfo] = useState<any>({});

  const { data: topData } = useRequest(
    async () => {
      if (userInfo?.roles?.length > 0) {
        const role = userInfo?.roles?.find((role: string) => roleMap.hasOwnProperty(role)) || defaultRole;
        const res = await workStationTopDataCount({
          roles: [role],
        });
        return res.data;
      }
      return null;
    },
    {
      refreshDeps: [userInfo],
    },
  );

  const { data: bottomData } = useRequest(
    async () => {
      if (userInfo?.roles?.length > 0) {
        const role = userInfo?.roles?.find((role: string) => roleMap.hasOwnProperty(role)) || defaultRole;
        const res = await workStationBottomDataCount({
          roles: [role],
        });
        return res.data;
      }
      return null;
    },
    {
      refreshDeps: [userInfo],
    },
  );

  useEffect(() => {
    getInitialState().then((res) => {
      setUserInfo(res.currentUser);
    });
  }, []);
  // 根据角色判断是否显示不同的组件
  const getComponent = () => {
    const role = userInfo?.roles?.find((role: string) => roleMap.hasOwnProperty(role)) || defaultRole;
    if (role) {
      const Component = roleMap[role as keyof typeof roleMap];
      return <Component userInfo={userInfo} topData={topData} bottomData={bottomData} />;
    }
    return <></>;
  };
  return getComponent();
};

export default Home;
