@marginPx: 15px;

.home {
  display: flex;
  flex-direction: column;
  height: 100%;

  &_bottom {
    display: flex;
    flex: 1;
    &_left {
      display: flex;
      flex: 1;
      // flex-direction: column;
      height: 100%;
      padding-bottom: @marginPx;
      .left_item {
        display: flex;
        flex: 1;
        height: 100%;
        margin-right: @marginPx;
        & > div {
          flex: 1;
          width: 100%;
        }
        &_card {
          &_body {
            display: flex;
            flex-wrap: wrap;
            padding: unset;
            overflow: hidden;
          }
        }
      }
      .left_item-left {
        .left_item_card {
          height: 100%;
        }
      }
      .left_item-right {
        display: flex;
        flex-direction: column;
        & > div {
          &:first-child {
            // margin-bottom: @marginPx;
          }
        }
        &-top {
          height: 50%;
        }
        &-bottom {
          height: 100%;
        }
      }
    }
    &_right {
      display: flex;
      flex: 0 0 350px;
      flex-direction: column;
      .right_item {
        margin-bottom: @marginPx;
        &_one {
          .total {
            display: flex;
            align-items: center;
            justify-content: space-around;
            &_item {
              position: relative;
              display: flex;
              flex: 1;
              flex-direction: column;
              align-items: center;
              .title {
                color: #979797;
                font-size: 14px;
                .title_tag {
                  position: relative;
                  top: 0;
                  display: inline-block;
                  width: 6px;
                  height: 6px;
                  margin-right: 6px;
                  border-radius: 6px;
                }
              }
              .num {
                color: #979797;
                font-weight: 600;
                font-size: 26px;
              }
              &:not(:last-child)::after {
                position: absolute;
                top: 50%;
                right: 0;
                width: 1px;
                height: 40px;
                background: #e9e9e9;
                transform: translateY(-50%);
                content: '';
              }
            }
          }
        }

        .link {
          display: inline-block;
          min-width: 25%;
          line-height: 45px;
          text-align: center;
        }
      }
    }
  }
}
