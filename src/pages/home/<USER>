import Card from '@/components/card';
import { entrustInfoGetDetectionStatistics } from '@/services/base/weituoshujichuxinxibiaojiekou';
import { history } from '@umijs/max';
import { useRequest } from 'ahooks';
import { default as classNames, default as cs } from 'classnames';
import ReactECharts from 'echarts-for-react';
import { useEffect, useMemo, useState } from 'react';
import AttoryneyList from './components/attorney';
import TaskList from './components/task';
import TodoList from './components/todo';
import Welcome from './components/welcome';
import styles from './index.less';
import qs from 'qs';
import { jzTaskInfoGetHomepage } from '@/services/base/jingzhourenwuliebiaobiaojiekou';
import ProductionScheduler from './productionScheduler';
const ShortLinks = [
  // {
  //   title: '委托书',
  //   link: '/attorney',
  // },
  {
    title: '任务列表',
    link: '/task/listData',
  },
  {
    title: '报告管理',
    link: '/report',
  },
  {
    title: '样品管理',
    link: '/taskManage/sample',
  },
];

const Home: React.FC = () => {
  
  if (true) {
    return <ProductionScheduler />
  }
  // 我的待办当前tab
  const [currentKey, setCurrentKey] = useState<string>('todo')

  const [homepageData, setHomepageData] = useState<any>({});
  const getHomepageData = async () => {
    const res = await jzTaskInfoGetHomepage();
    console.log(res);
    if (res?.success) {
      setHomepageData(res?.data);
    }
  };

  useEffect(() => {
    getHomepageData();
  }, []);

  return (
    <div className={styles['home']}>
      <Welcome />
      <div className={styles['home_bottom']}>
        <div className={styles['home_bottom_left']}>
          <div className={cs(styles['left_item'], styles['left_item-left'])}>
            <Card
              title={'我的流程'}
              className={styles['left_item_card']}
              bodyClassName={styles['left_item_card_body']}
              onClick={() => {
                history.push({
                  pathname: '/msg',
                  search: qs.stringify({
                    activeKey: currentKey
                  })
                });
              }}
            >
              <TodoList getCurrentKey={(key) => {setCurrentKey(key)}}/>
            </Card>
          </div>
          <div className={cs(styles['left_item'], styles['left_item-right'])}>
            {/* <div className={cs(styles['left_item-right-top'])}>
              <Card
                title="我的委托"
                className={styles['left_item_card']}
                bodyClassName={styles['left_item_card_body']}
                onClick={() => {
                  history.push('/attorney');
                }}
              >
                <AttoryneyList />
              </Card>
            </div> */}
            <div className={cs(styles['left_item-right-bottom'])}> 
              <Card
                title="任务管理"
                className={styles['left_item_card']}
                onClick={() => {
                  history.push('/task/list');
                }}
              >
                <TaskList />
              </Card>
            </div>

            {/* <Card
              title="测试任务"
              className={styles['left_item_card']}
              onClick={() => {
                history.push('/task/plan');
              }}
            >
              <PlanList />
            </Card> */}
          </div>
        </div>
        <div className={styles['home_bottom_right']}>
          <Card
            title="检测统计"
            className={classNames(styles['right_item'])}
            bodyClassName={styles['right_item_one']}
            extra={false}
          >
            {/* <ReactECharts
              option={{
                radar: {
                  indicator: [
                    { name: '通用', max },
                    { name: '天线', max },
                    { name: '电磁辐射', max },
                    { name: '电磁环境', max },
                    { name: '软测', max },
                    { name: '型号核准', max },
                  ],
                },
                series: [
                  {
                    name: 'Budget vs spending',
                    type: 'radar',
                    label: {
                      normal: {
                        show: true,
                        formatter: function (params: { value: string }) {
                          return params.value;
                        },
                      },
                    },
                    data: [
                      {
                        value: statisData.data?.find((i) => i.taskType === '委托')?.detail || [],
                        name: '委托',
                      },
                      {
                        value: statisData.data?.find((i) => i.taskType === '任务')?.detail || [],
                        name: '任务',
                      },
                      {
                        value: statisData.data?.find((i) => i.taskType === '样品')?.detail || [],
                        name: '样品',
                      },
                    ],
                  },
                ],
              }}
            /> */}
            <div className={styles['total']}>
              <ReactECharts
                style={{ width: '100%', height: 160 }}
                option={{
                  grid: { 
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '10%',
                    containLabel: true 
                  },
                  xAxis: {
                    type: 'category',
                    data: ['任务', '样品'],
                    axisLine: {
                      show: false,
                    },
                    axisTick: {
                      show: false,
                    },
                  },
                  yAxis: {
                    type: 'value',
                  },
                  series: [
                    {
                      type: 'bar',
                      label: {
                        show: true,
                      },
                      data: [
                        homepageData?.taskNumber || 0,
                        homepageData?.sampleNumber || 0,
                      ],
                    },
                  ],
                }}
              />
              {/* <div className={styles['total_item']}>
                <div className={styles['title']}>
                  <span className={styles['title_tag']} style={{ background: '#fac858' }} />
                  委托
                </div>
                <div className={styles['num']}>
                  {statisData.data?.find((i) => i.taskType === '委托')?.detectionNum || 0}
                </div>
              </div> */}
              {/* <div className={styles['total_item']}>
                <div className={styles['title']}>
                  <span className={styles['title_tag']} style={{ background: '#91cc75' }} />
                  任务
                </div>
                <div className={styles['num']}>
                  {statisData.data?.find((i) => i.taskType === '任务')?.detectionNum || 0}
                </div>
              </div>
              <div className={styles['total_item']}>
                <div className={styles['title']}>
                  <span className={styles['title_tag']} style={{ background: '#5470c6' }} />
                  样品
                </div>
                <div className={styles['num']}>
                  {statisData.data?.find((i) => i.taskType === '样品')?.detectionNum || 0}
                </div>
              </div> */}
            </div>
          </Card>
          <Card title="便捷导航" className={styles['right_item']} extra={false}>
            {ShortLinks.map((item, index) => {
              return (
                <a
                  key={index}
                  className={styles['link']}
                  onClick={() => {
                    history.push(item.link);
                  }}
                >
                  {item.title}
                </a>
              );
            })}
          </Card>
          {/* <Card title="我的样品" className={styles['right_item']} extra={false}>123123</Card> */}
        </div>
      </div>
    </div>
  );
};

export default Home;
