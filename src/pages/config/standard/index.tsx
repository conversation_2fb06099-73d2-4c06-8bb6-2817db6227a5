import ProTable, { ProTableProps } from '@/components/proTable';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const columns: ProTableProps<BASE.StandardInfoVO>['columns'] = [
    {
      dataIndex: 'index',
      title: '序号',
      valueType: 'index',
      width: 80,
    },
    {
      dataIndex: 'standardNum',
      title: '标准编号',
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      align:'left',
      ellipsis: true,
      width: 200,
    },
    {
      dataIndex: 'standardName',
      title: '标准名称',
      hideInSearch: true,
      ellipsis: true,
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      align:'left'
    },

    {
      dataIndex: 'createTime',
      title: '创建时间',
      hideInSearch: true,
      hideInForm: true,
      valueType: 'date',
      width: 150,
      fieldProps: { style: { width: '100%' } },
    },
    // 操作
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 150,
      render: (_, record) => [],
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.StandardInfoVO> scroll={{ x: 'fit-content' }} crudKey="standardInfo" columns={columns} />
    </PageContainer>
  );
};

export default Page;
