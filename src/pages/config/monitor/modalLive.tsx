import LivePlayer from '@/components/videoPlayer';
import { buInstrumentCameraInfoStream } from '@/services/base/haikangweishishexiangtouguanli<PERSON><PERSON>ou';
import { Modal } from 'antd';
import { useEffect, useState } from 'react';

function ModalViewLive({ row, ...rest }: any) {

  const [url, setUrl] = useState('https://l3g8l8wn-8082.asse.devtunnels.ms/live/1liy60bt8ADSGTpv2UrplfJ52aDjKSWuxRTJkFauXOMMd2C8D71bvWpAXQGHAejdg64CrUhdcO0RsD53ft2vpUqf.flv')
  const getUrl = async () => {
    console.log(row)
    const res = await buInstrumentCameraInfoStream({
      id: row.id,
    })
    if (res.data) {
      setUrl(res.data)
    }
  }

  useEffect(() => {
    if (row.id) {
      getUrl()
    }
  }, [row])

  return (
    <Modal title="测试现场" {...rest}>
      <div style={{ margin: '0 auto', marginTop: 16, width: 450, height: 300 }}>
        <LivePlayer
          url={url}
          autoplay={true}
        />
      </div>
    </Modal>
  );
}

export default ModalViewLive;
