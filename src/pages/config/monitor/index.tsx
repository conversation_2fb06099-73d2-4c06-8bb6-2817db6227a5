import Button from '@/components/button';
import ProTable, { ProTableProps } from '@/components/proTable';
import { buInstrumentWorkstationInfoVoPage } from '@/services/base/gongweixinxiguanlijiekou';
import { arr2ValueEnum } from '@/utils';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Access, useAccess, useModel } from '@umijs/max';
import { launch, launchSchema } from 'antd-pro-crud';
import { useRef } from 'react';
import Detail from './detail';
import Edit from './edit';
import ModalLive from './modalLive';

const alert_edit = launchSchema(Edit);
const alert_detail1 = launch(Detail);
const alert_ModalLive = launch(ModalLive);
const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { commonAccess } = useAccess();
  const actionRef = useRef<ActionType>();

  const columns: ProTableProps<BASE.BuInstrumentCameraInfoVO>['columns'] = [
    {
      title: '序号',
      valueType: 'index',
      hideInDescriptions: true,
      width: 80,
    },
    {
      dataIndex: 'deviceCode',
      title: '监控设备编号',
    },
    {
      dataIndex: 'deviceModel',
      title: '监控设备型号',
    },
    {
      dataIndex: 'deviceName',
      title: '监控设备名称',
    },
    /** 工位id */
    {
      dataIndex: 'workstationId',
      title: '工位',
      hideInSearch: true,
      hiddenInAdd: true,
      valueType: 'select',
      fieldProps: {
        fieldNames: {
          label: 'workstationName',
          value: 'id',
        },
      },
      request: async () => {
        let res = await buInstrumentWorkstationInfoVoPage({
          page: 1,
          size: 1000,
        });
        const list = res.data?.records || [];
        console.log(list, 'list');
        return list;
      },
    },
    {
      dataIndex: 'deviceStatus',
      title: '设备状态',
      valueType: 'select',
      valueEnum: arr2ValueEnum(['正常', '异常']),
      hiddenInAdd: true,
    },
    {
      dataIndex: 'cameraType',
      title: '摄像头类型',
      hideInSearch: true,
      valueType: 'select',
      valueEnum: arr2ValueEnum(['球机', '枪机', '半球', '其他']),
    },
    {
      dataIndex: 'channelNum',
      title: '通道号',
      hideInSearch: true,
    },
    {
      dataIndex: 'installationLocation',
      title: '安装位置',
      hideInSearch: true,
    },
    {
      dataIndex: 'ipAddress',
      title: 'IP地址',
      hideInSearch: true,
    },
    {
      valueType: 'option',
      title: '操作',
      width: 200,
      fixed: 'right',
      render(dom, entity, index, action, schema) {
        return (
          <>
            {/* 编辑 */}
            <Access accessible={commonAccess('admin:/device/monitor@edit')}>
              <a
                onClick={() =>
                  alert_edit({
                    initialValues: entity,
                    onSuccess: () => action?.reload(),
                  })
                }
              >
                编辑
              </a>
            </Access>
            {/* 播放 */}
            <Access accessible={commonAccess('admin:/device/monitor@play')}>
              <a
                onClick={() => {
                  alert_ModalLive({
                    row: {
                      ...entity,
                      szIP: entity.ipAddress,
                      szPort: entity.port,
                      szUser: entity.username,
                      szPass: entity.password,
                    },
                    footer: false,
                    width: 600,
                    maskClosable: false,
                  });
                }}
              >
                播放
              </a>
            </Access>
            {/* 详情 */}
            <Access accessible={commonAccess('admin:/device/monitor@detail')}>
              <a
                onClick={() =>
                  alert_detail1({
                    row: entity,
                  })
                }
              >
                详情
              </a>
            </Access>
          </>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.BuInstrumentCameraInfoVO>
        crudKey="buInstrumentCameraInfo"
        columns={columns}
        search={{
          labelWidth: 120,
        }}
        actionRef={actionRef}
        toolBarRender={(action: any, { selectedRowKeys }: any) => {
          return [
            <Access key="add" accessible={ false && commonAccess('admin:/device/monitor@add')}>
              <Button
                type="primary"
                key={'add'}
                onClick={() =>
                  alert_edit({
                    onSuccess: () => action?.reload(),
                  })
                }
              >
                新增
              </Button>
            </Access>,
          ];
        }}
        crud={{
          delete: {
            visible: (row) => {
              return commonAccess('admin:/device/monitor@delete');
            },
          },
          edit: {
            visible: (row) => {
              return commonAccess('admin:/device/monitor@edit');
            },
          },
        }}
        hiddenBtns={['add', 'detail', 'edit']}
      />
    </PageContainer>
  );
};

export default Page;
