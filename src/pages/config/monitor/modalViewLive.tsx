import { message, Modal } from 'antd';
import { useEffect, useState } from 'react';

let g_iWndIndex = 0;
interface LiveProps {
  szIP: string;
  szPort: string;
  szUser: string;
  szPass: string;
}

function ModalViewLive({ row, ...rest }: any) {
  const { open } = rest;

  const [szDeviceIdentify, setSzDeviceIdentify] = useState<string>('');
  const [iRtspPort, setIRtspPort] = useState<string>('');
  const [bZeroChannel, setBZeroChannel] = useState<any>(false);
  const [iChannelID, setIChannelID] = useState<string>('');

  const getChannelInfo = (szDeviceIdentify: string) => {
    return new Promise((resolve, reject) => {
      let arr: any = [];
      const isFn = () => {
        if (arr.length === 3) {
          arr.map((item: any) => {
            if (Object.keys(item).length > 0) {
              setBZeroChannel(item.type === '3');
              setIChannelID(item.id);
            }
            return null;
          });
          resolve(arr);
        }
      };
      // 模拟通道
      WebVideoCtrl.I_GetAnalogChannelInfo(szDeviceIdentify, {
        async: false,
        success: (xmlDoc: any) => {
          console.log(xmlDoc);
          const books = xmlDoc.getElementsByTagName('VideoInputChannel')[0];
          if (books) {
            let ids = books.getElementsByTagName('id'),
              names = books.getElementsByTagName('name'),
              online = books.getElementsByTagName('online');
            console.log(ids[0], names[0], online[0]);
            arr.push({
              id: ids[0].innerHTML,
              name: names[0].innerHTML,
              online: online[0].innerHTML,
              type: '1',
            });
          } else {
            arr.push({});
            isFn();
          }
        },
        error: (status: number, xmlDoc: any) => {
          message.warning(szDeviceIdentify + ' 获取模拟通道失败！', status, xmlDoc);
          arr.push({
            type: '1',
            err: xmlDoc,
          });
          isFn();
        },
      });
      // 数字通道
      WebVideoCtrl.I_GetDigitalChannelInfo(szDeviceIdentify, {
        async: false,
        success: (xmlDoc: any) => {
          const books = xmlDoc.getElementsByTagName('InputProxyChannelStatus')[0];
          if (books) {
            let ids = books.getElementsByTagName('id'),
              names = books.getElementsByTagName('name'),
              online = books.getElementsByTagName('online');
            console.log(ids[0], names[0], online[0]);
            arr.push({
              id: ids[0].innerHTML,
              name: names[0].innerHTML,
              online: online[0].innerHTML,
              type: '2',
            });
          } else {
            arr.push({});
            isFn();
          }
        },
        error: (status: number, xmlDoc: any) => {
          message.warning(szDeviceIdentify + ' 获取数字通道失败！', status, xmlDoc);
          arr.push({
            type: '2',
            err: xmlDoc,
          });
          isFn();
        },
      });
      // 零通道
      WebVideoCtrl.I_GetZeroChannelInfo(szDeviceIdentify, {
        async: false,
        success: (xmlDoc: any) => {
          const books = xmlDoc.getElementsByTagName('ZeroVideoChannel')[0];
          if (books) {
            let ids = books.getElementsByTagName('id'),
              names = books.getElementsByTagName('name'),
              online = books.getElementsByTagName('online');
            console.log(ids[0], names[0], online[0]);
            arr.push({
              id: ids[0].innerHTML,
              name: names[0].innerHTML,
              online: online[0].innerHTML,
              type: '3',
            });
          } else {
            arr.push({});
            isFn();
          }
        },
        error: (status: number, xmlDoc: any) => {
          message.warning(szDeviceIdentify + ' 获取零通道失败！', status, xmlDoc);
          arr.push({
            type: '3',
            err: xmlDoc,
          });
          isFn();
        },
      });
    });
  };
  const getDevicePort = (szDeviceIdentify: string) => {
    return new Promise((resolve) => {
      let oPort = WebVideoCtrl.I_GetDevicePort(szDeviceIdentify);
      if (oPort !== null) {
        console.log(oPort.iDevicePort, oPort.iRtspPort);
        setIRtspPort(oPort.iRtspPort);
        message.success(szDeviceIdentify + ' 获取端口成功！');
      } else {
        message.warning(szDeviceIdentify + ' 获取端口失败！');
      }
      resolve(oPort);
    });
  };

  const reconnect = (szDeviceIdentify: string) => {
    WebVideoCtrl.I_Reconnect(szDeviceIdentify, {
      success() {},
      error(status: number, xmlDoc: any) {
        let szInfo = '';
        if (403 === status) {
          szInfo = '设备不支持Websocket取流！';
        } else {
          szInfo = '开始预览失败！';
        }
        console.log(szInfo);
        reconnect(szDeviceIdentify);
      },
    });
  };

  const startLive = (iStreamType: any = 1) => {
    let oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
      szInfo = '';

    if ('undefined' === typeof iStreamType) {
      // iStreamType = parseInt($("#streamtype").val(), 10);
    }

    if (null === szDeviceIdentify) {
      return;
    }
    let startRealPlay = () => {
      WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
        iRtspPort: iRtspPort,
        iStreamType: iStreamType,
        iChannelID: iChannelID,
        bZeroChannel: bZeroChannel,
        success: () => {
          szInfo = '开始预览成功！';
        },
        error: (status: number, xmlDoc: any) => {
          if (403 === status) {
            szInfo = '设备不支持Websocket取流！';
          } else {
            szInfo = '开始预览失败！';
          }
          reconnect(szDeviceIdentify);
          message.warning(szDeviceIdentify + ' ' + szInfo);
        },
      });
    };

    if (oWndInfo !== null) {
      // 已经在播放了，先停止
      WebVideoCtrl.I_Stop({
        success: () => {
          startRealPlay();
        },
      });
    } else {
      startRealPlay();
    }
  };

  const init = () => {
    return new Promise((resolve) => {
      // 检查插件是否已经安装过
      let iRet = WebVideoCtrl.I_CheckPluginInstall();
      if (-1 === iRet) {
        message.warning('您还未安装过插件，双击开发包目录里的WebComponentsKit.exe安装！');
        return;
      }
      // 初始化插件参数及插入插件
      WebVideoCtrl.I_InitPlugin(500, 300, {
        bWndFull: true, //是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
        iPackageType: 2,
        //szColorProperty:"plugin-background:0000ff; sub-background:0000ff; sub-border:00ffff; sub-border-select:0000ff",   //2:PS 11:MP4
        iWndowType: 1,
        bNoPlugin: true,
        cbSelWnd: (xmlDoc: any) => {
          const books = xmlDoc.getElementsByTagName('SelectWnd');
          g_iWndIndex = books[0].innerHTML;
          let szInfo = '当前选择的窗口编号：' + g_iWndIndex;
          console.log(szInfo);
        },
        cbDoubleClickWnd: (iWndIndex: number, bFullScreen: any) => {
          let szInfo = '当前放大的窗口编号：' + iWndIndex;
          if (!bFullScreen) {
            szInfo = '当前还原的窗口编号：' + iWndIndex;
          }
          message.warning(szInfo);
        },
        cbEvent: (iEventType: any, iParam1: any) => {
          if (2 === iEventType) {
            // 回放正常结束
            message.warning('窗口' + iParam1 + '回放结束！');
          } else if (-1 === iEventType) {
            message.warning('设备' + iParam1 + '网络错误！');
          } else if (3001 === iEventType) {
            message.warning('s', iParam1);
          }
        },
        cbRemoteConfig: () => {
          message.warning('关闭远程配置库！');
        },
        cbInitPluginComplete: () => {
          WebVideoCtrl.I_InsertOBJECTPlugin('divPlugin');
          console.log('插件初始化完成');
          // 检查插件是否最新
          if (-1 === WebVideoCtrl.I_CheckPluginVersion()) {
            message.warning('检测到新的插件版本，双击开发包目录里的WebComponentsKit.exe升级！');
            return;
          }
          resolve(true);
        },
      });
    });
  };

  const loginDevice = async (props: LiveProps) => {
    await init();
    const { szIP, szPort, szUser: szUsername, szPass: szPassword } = props;
    const szDeviceIdentify = szIP + '_' + szPort;
    WebVideoCtrl.I_Login(szIP, 1, szPort, szUsername, szPassword, {
      success: (xmlDoc: string) => {
        console.log('login success', xmlDoc);
        setTimeout(async () => {
          await getChannelInfo(szDeviceIdentify);
          await getDevicePort(szDeviceIdentify);
          startLive();
        }, 10);
      },
      error: (status: number, xmlDoc: any) => {
        message.warning(szDeviceIdentify + ' 登录失败！', status, xmlDoc);
      },
    });
  };

  useEffect(() => {
    if (open && row) {
      let info = {
        szIP: row.szIP,
        szPort: row.szPort,
        szUser: row.szUser,
        szPass: row.szPass,
        // szIP: '***************',
        // szPort: '80',
        // szUser: 'admin',
        // szPass: 'cqyj130326',
      }
      console.log(row, info);
      loginDevice(info);
    }
  }, [row, open]);

  return (
    <Modal title="查看直播" {...rest}>
      <div style={{ margin: '0 auto', marginTop: 16 }}>
        {open && (
          <div
            id="divPlugin"
            className="plugin"
            style={{ width: 500, height: 300, margin: '0 auto' }}
          ></div>
        )}
      </div>
    </Modal>
  );
}

export default ModalViewLive;
