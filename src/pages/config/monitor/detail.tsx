import { buInstrumentWorkstationInfoVoPage } from '@/services/base/gongweixinxiguanlijiekou';
import { instrumentNewInfoGet } from '@/services/base/yiqiyibiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { ProDescriptions, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Modal, Tag } from 'antd';
import React from 'react';

const Detail: React.FC<{ row: Record<string,any> }> = ({ row, ...rest }) => {
  // const detailRequest = useRequest(() => {
  //   return instrumentNewInfoGet({
  //     id,
  //   });
  // });

  return (
    <Modal {...rest} width={'80%'}>
      <ProDescriptions<any>
        title="基本信息"
        dataSource={row}
        column={2}
        bordered
        columns={[
          {
            dataIndex: 'deviceCode',
            title: '监控设备编号',
          },
          {
            dataIndex: 'deviceModel',
            title: '监控设备型号',
          },
          {
            dataIndex: 'deviceName',
            title: '监控设备名称',
          },
          {
            dataIndex: 'workstationId',
            title: '工位',
            hideInSearch: true,
            fieldProps: {
              fieldNames: {
                label: 'workstationName',
                value: 'id',
              },
            },
            request: async () => {
              let res = await buInstrumentWorkstationInfoVoPage({
                page: 1,
                size: 1000,
              });
              const list = res.data?.records || [];
              return list;
            }
          },
          {
            dataIndex: 'deviceStatus',
            title: '设备状态',
            valueType: 'select',
            valueEnum: arr2ValueEnum(['正常', '异常']),
          },
          {
            dataIndex: 'cameraType',
            title: '摄像头类型',
            hideInSearch: true,
            valueType: 'select',
            valueEnum: arr2ValueEnum(['球机', '枪机', '半球', '其他']),
          },
          {
            dataIndex: 'channelNum',
            title: '通道号',
            hideInSearch: true,
          },
          {
            dataIndex: 'installationLocation',
            title: '安装位置',
            hideInSearch: true,
          },
          {
            dataIndex: 'ipAddress',
            title: 'IP地址',
            hideInSearch: true,
            span: 24,
          },
          // {
          //   dataIndex: 'isOnline',
          //   title: '是否在线',
          //   valueType: 'select',
          //   valueEnum: {
          //     1: '在线',
          //     0: '离线',
          //   },
          // },
          // {
          //   dataIndex: 'rtspUrl',
          //   title: 'RTSP流地址',
          //   hideInSearch: true,
          // },
          {
            dataIndex: 'username',
            title: '账号',
            hideInSearch: true,
          },
          {
            dataIndex: 'password',
            title: '密码',
            hideInSearch: true,
          },
        ]}
      />
    </Modal>
  );
};

export default Detail;
