import { sampleInfoAdd, sampleInfoUpdate } from '@/services/base/yangpinjibenxinxibiaojiekou';
import { arr2ValueEnum } from '@/utils';
import {
  ModalForm,
  ModalFormProps,
  ProFormDigit,
  ProFormSelect,
  ProFormDatePicker,
  ProFormText,
  ProFormTextArea,
  ProFormDateTimePicker,
} from '@ant-design/pro-components';
import { message } from 'antd';

type EditProps = {
  isEdit?: boolean;
  onSuccess?: () => void;
  formatParams?: (values: Record<string, any>) => Record<string, any>;
} & ModalFormProps;

const Edit: React.FC<EditProps> = (props: EditProps) => {
  const { isEdit, onSuccess, formatParams, ...rest } = props;

  const onFinish = async (values: any) => {
    const submitValues = {
      ...values,
      sampleSerialNumber: values?.sampleSerialNumber,
    };
    const res = isEdit
      ? await sampleInfoUpdate({ ...submitValues, id: props?.initialValues?.id })
      : await sampleInfoAdd(formatParams ? formatParams?.(submitValues) : submitValues);
    if (res?.success) {
      message.success(res?.message);
      props?.modalProps?.onCancel?.(submitValues);
      onSuccess?.();
    }
  };
  return (
    <ModalForm
      title={isEdit ? '编辑' : '新增'}
      width="800px"
      grid
      colProps={{ span: 12 }}
      {...rest}
      onFinish={onFinish}
    >
      <ProFormText
        label={'监控设备编号'}
        name={'deviceCode'}
        formItemProps={{ rules: [{ required: true }] }}
      />
      <ProFormText label={'监控设备型号'} name={'deviceModel'} />

      <ProFormText label={'监控设备名称'} name={'deviceName'} />

      <ProFormSelect label={'摄像头类型'} name={'cameraType'} valueEnum={arr2ValueEnum(['球机', '枪机', '半球', '其他'])} />

      
      <ProFormText label={'通道号'} name={'channelNum'} />
      <ProFormText label={'安装位置'} name={'installationLocation'} />
      <ProFormText label={'IP地址'} name={'ipAddress'} />

      <ProFormText label={'账号'} name={'username'} />
      <ProFormText label={'密码'} name={'password'} />
      
    </ModalForm>
  );
};
export default Edit;
