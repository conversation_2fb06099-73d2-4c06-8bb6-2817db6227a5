import ProFormSelect from '@/components/proFormSelect';
import { buInstrumentWorkstationInfoVoPage } from '@/services/base/gongweixinxiguanlijiekou';
import { ProForm } from '@ant-design/pro-components';
import { Form, Modal } from 'antd';
import { useEffect } from 'react';

function LinkWorkStation({ row, ...rest }: any) {
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      workstationId: row?.buInstrumentWorkstationInfo?.id,
    });
  }, [row]);

  return (
    <Modal
      title="关联工位"
      {...rest}
      onOk={() => {
        rest?.onFinish?.(form.getFieldsValue(true));
        rest?.onOk?.();
      }}
    >
      <ProForm form={form} style={{ marginTop: 10 }} submitter={false}>
        <ProFormSelect
          label="关联工位"
          name="workstationId"
          fieldProps={{
            fieldNames: {
              label: 'workstationName',
              value: 'id',
            },
            // 搜索不调用request
            showSearch: true,
            fetchDataOnSearch: false,
            filterOption: (input: string, option: any) => {
              return option?.workstationName?.indexOf(input.toLowerCase()) >= 0;
            },
            onChange(value: any) {
              rest?.onSuccess?.(value);
            },
          }}
          request={async () => {
            let res = await buInstrumentWorkstationInfoVoPage({
              page: 1,
              size: 1000,
            });
            const list = res.data?.records || [];
            console.log(list, 'list');
            return list;
          }}
        />
      </ProForm>
    </Modal>
  );
}

export default LinkWorkStation;
