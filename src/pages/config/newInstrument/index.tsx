import React, { useState, useRef } from "react";
import { Button, message, Tag, Tooltip } from "antd";
import { ActionType, PageContainer } from '@ant-design/pro-components';
import ProTable, { ProTableProps } from '@/components/proTable';
import { Access, useAccess } from '@umijs/max';
import { arr2ValueEnum } from '@/utils';
import { launch, launchSchema } from 'antd-pro-crud';
import DeviceTestData from "./deviceTestData";
import Detail from "./detail";
import EditUnit, { UnitDataProps } from "./editUnit";
import { standardBasicInstrumentInfoVoPage } from '@/services/base/biaozhunjianceyiqibiaozhunxinxibiaojiekou';
import { orgUserInfoVoPage } from '@/services/base/zuzhijiagourenyuanbiaojiekou';

const alert_editUnit = launchSchema(EditUnit);
const alert_detail1 = launch(Detail);

const ellipsisStyle = {
  display: 'inline-block',
  width: '100%',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
};
const myEllipsis = (text: string[]) => {
  return (
    <Tooltip
      title={
        <div>
          {text.map((item: any) => {
            return (
              <Tag key={item} style={{ marginBottom: 4 }}>
                {item}
              </Tag>
            );
          })}
        </div>
      }
    >
      <span style={ellipsisStyle}>{text.join(',')}</span>
    </Tooltip>
  );
};

const Page: React.FC = () => {
  const { commonAccess } = useAccess();
  const actionRef = useRef<ActionType>();

  // 添加状态控制DeviceTestData弹窗
  const [deviceTestDataVisible, setDeviceTestDataVisible] = useState<boolean>(false);
  const [selectedDevice, setSelectedDevice] = useState<any>(null);

  const columns: ProTableProps<BASE.InstrumentNewInfoVO>['columns'] = [
    {
      title: '序号',
      valueType: 'index',
      width: 80,
      hideInDescriptions: true,
    },
    {
      dataIndex: 'sbmc',
      title: '设备名称',
      width: 180,
      ellipsis: true,
      formItemProps: { rules: [{ required: true }] },
      fieldProps: {
        style: { width: '100%' },
      },
    },
    {
      dataIndex: 'professionalType',
      title: '专业类型',
      width: 180,
      ellipsis: true,
      formItemProps: { rules: [{ required: true }] },
      fieldProps: {
        style: { width: '100%' },
      },
    },
    {
      dataIndex: 'collectionMethod',
      title: '采集方式',
      valueType: 'select',
      width: 120,
      ellipsis: true,
      valueEnum: arr2ValueEnum(['直采', '填报']),
      formItemProps: { rules: [{ required: true }] },
      fieldProps: {
        style: { width: '100%' },
      },
    },
    {
      dataIndex: 'collectionMethod1',
      title: '在线状态',
      width: 100,
      hideInForm: true,
    },
    // 其他列保持不变...
    {
      dataIndex: 'gwBzIds',
      title: '关联实验',
      valueType: 'select',
      width: 160,
      ellipsis: true,
      hideInSearch: true,
      hideInForm: true,
      fieldProps: {
        style: { width: '100%' },
        showSearch: true,
        mode: 'multiple',
        fieldNames: {
          label: 'projectName',
          value: 'id',
        },
        fetchDataOnSearch: false,
      },
      formItemProps: {
        rules: [{ required: true }],
      },
      request: async () => {
        const res = await standardBasicInstrumentInfoVoPage({
          page: 1,
          size: 2000,
        });
        return res.data?.records || [];
      },
      render: (_, record) => {
        if (record?.proName) {
          let names: string[] = record?.proName;
          return myEllipsis(names);
        }
        return '-';
      },
    },
    // 关联工位
    {
      dataIndex: 'workstationName',
      title: '所属检测工位',
      width: 140,
      ellipsis: true,
      hideInSearch: true,
      hideInForm: true,
    },
    {
      dataIndex: 'testUsers',
      title: '检测人员',
      width: 160,
      hideInSearch: true,
      hideInTable: true,
      valueType: 'select',
      ellipsis: true,
      fieldProps: {
        mode: 'multiple',
        style: { width: '100%' },
        showSearch: true,
        fetchDataOnSearch: false,
      },
      convertValue: (value: any) => {
        if (value) {
          return value?.map((item: any) => item?.userId ?? item);
        }
        return [];
      },
      transform: (value: any) => {
        return {
          testUserIds: value?.map((item: any) => item?.userId ?? item),
        };
      },
      request: async () => {
        const res = await orgUserInfoVoPage({
          page: 1,
          size: 2000,
        });
        if (res.data?.records) {
          return res.data?.records.map((item: any) => ({
            label: item?.fullName,
            value: item?.id,
          }));
        }
        return [];
      },
    },
    {
      dataIndex: 'testUserFullNames',
      title: '检测人员',
      width: 160,
      hideInSearch: true,
      hideInForm: true,
      ellipsis: true,
      render: (_, record) => {
        if (record?.testUserFullNames) {
          let names: string[] = record?.testUserFullNames;
          return myEllipsis(names);
        }
        return '-';
      },
    },
    {
      dataIndex: 'scc',
      title: '设备厂商',
      width: 160,
      hideInSearch: true,
      ellipsis: true,
      fieldProps: {
        style: { width: '100%' },
      },
    },
    {
      dataIndex: 'commissionDate',
      title: '设备投入使用时间',
      hideInSearch: true,
      ellipsis: true,
      valueType: 'date',
      width: 140,
      formItemProps: { rules: [{ required: true }] },
      fieldProps: {
        style: { width: '100%' },
      },
    },
    {
      dataIndex: 'parametersInspectedEquipment',
      title: '设备可检参数',
      hideInSearch: true,
      hideInForm: true,
      valueType: 'select',
      ellipsis: true,
      width: 140,
      fieldProps: {
        style: { width: '100%' },
        mode: 'tags',
      },
      render: (_, record) => {
        if (record?.parametersInspectedEquipment) {
          try {
            let names = JSON.parse(record?.parametersInspectedEquipment)?.map(
              (item: any) => item?.paramName,
            );
            return myEllipsis(names);
          } catch (error) {
            return '-';
          }
        }
        return '-';
      },
    },
    {
      valueType: 'option',
      title: '操作',
      width: 280,
      fixed: 'right',
      render(dom, entity, index, action, schema) {
        let status = (entity as any).collectionMethod1 ?? '-';
        return (
          <>
            {
              (status === '在线' || status === '-') && (
                <a onClick={() => {
                  setSelectedDevice(entity);
                  setDeviceTestDataVisible(true);
                }}>获取数据</a>
              )
            }
            <Access accessible={commonAccess('admin:/device/newInstrument@editCheckItem')}>
              <a
                onClick={() => {
                  let unitData: UnitDataProps[] = [];
                  try {
                    if (entity?.parametersInspectedEquipment) {
                      unitData = JSON.parse(entity?.parametersInspectedEquipment);
                    } else {
                      unitData = [];
                    }
                  } catch (error) {
                    unitData = [];
                  }
                  let modal = alert_editUnit({
                    unitData,
                    id: entity?.id.toString(),
                    onFinish: () => {
                      actionRef.current?.reload();
                      modal.close();
                    },
                  });
                }}
              >
                编辑可检参数
              </a>
            </Access>
            <Access accessible={commonAccess('admin:/device/newInstrument@detail')}>
              <a
                onClick={() =>
                  alert_detail1({
                    row: entity,
                  })
                }
              >
                详情
              </a>
            </Access>
          </>
        );
      }
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.InstrumentNewInfoVO>
        crudKey="instrumentNewInfo"
        columns={columns}
        search={{
          labelWidth: 120,
        }}
        rowKey="id"
        scroll={{ x: 'fit-content', y: 500 }}
        actionRef={actionRef}
        toolBarRender={(action: any, { selectedRowKeys }: any) => {
          return [];
        }}
        crud={{
          add: {
            visible: (row) => {
              return commonAccess('admin:/device/newInstrument@add');
            },
          },
          delete: {
            visible: (row) => {
              return commonAccess('admin:/device/newInstrument@delete');
            },
          },
        }}
        style={{ overflow: 'auto' }}
        hiddenBtns={['detail']}
      />

      {/* 添加测试数据弹窗 */}
      <DeviceTestData
        row={selectedDevice}
        open={deviceTestDataVisible}
        onCancel={() => setDeviceTestDataVisible(false)}
      />
    </PageContainer>
  );
};

export default Page;