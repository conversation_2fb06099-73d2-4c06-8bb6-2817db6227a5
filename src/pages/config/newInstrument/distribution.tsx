
import ProTable from '@/components/proTable';
import { fieldNumInfoVoPage } from '@/services/base/jichuxinxilingyubianhaoxinxibiaojiekou';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { Tag } from 'antd';
import _ from 'lodash';
import { useEffect, useMemo, useState } from 'react';

export interface DistributionProps{
    entity: Record<string, any>
}


const Distribution = (props: DistributionProps) => {
  const { entity } = props
  const { initialState } = useModel('@@initialState');
  const { commonAccess } = useAccess();
  const currentUserId = initialState?.currentUser?.id;
 

  

  const [fieldNumId, setFieldNumId] = useState<string>('')

  const [fieldData, setFieldData] = useState<Record<string, any>>({})

  const depId =
    initialState?.currentUser?.extendData?.orgDeptInfos
      ?.map((item) => {
        return {
          label: item?.deptName,
          value: item?.id,
        };
      })
      ?.reduce((obj, item) => ({ ...obj, [item.value]: item.label }), {}) || {};


 

  return (
    <ProTable
    crudKey="instrumentDeptSysInfo"
    search={false}
    hiddenBtns={['detail']}
    params={{
      instrumentationId: entity?.id,
    }}
    crud={{
      add: {
        formatQuery: (params) => {
          return {
            ...params,
            instrumentationId: entity?.id,
            deptName: depId[params?.deptId as number],
            belongSystem: params?.belongSystem?.join(',') || '',
          };
        },
      },
      edit: {
        visible: (row) => {
          if(initialState?.currentUser?.roles?.some((role) => {
            return  ['testSupervisor'].includes(role)
          }
         ) && (initialState?.currentUser?.extendData?.orgDeptInfos?.map((item) => {
          return item?.id
        }).includes(row?.deptId))){
            return true;
          } else {
            return row?.createUserId === currentUserId;
          }
          
        },
        formatBeforeOpen: (params) => {
          return {
            ...params,
            belongSystem:
              typeof params?.belongSystem === 'string'
                ? params?.belongSystem?.split(',')
                : params?.belongSystem,
          };
        },
        formatQuery: (params) => {
          return {
            ...params,
            instrumentationId: entity?.id,
            
            deptName: depId[params?.deptId as number],
            belongSystem: params?.belongSystem?.join(',') || '',
          };
        },
      },
      delete: {
        visible: (row) => {
          if(initialState?.currentUser?.roles?.some((role) => {
            return  ['testSupervisor'].includes(role)
          }
         ) && (initialState?.currentUser?.extendData?.orgDeptInfos?.map((item) => {
          return item?.id
        }).includes(row?.deptId))){
            return true;
          } else {
            return row?.createUserId === currentUserId;
          }
        },
      },
    }}
    columns={[
      {
        title: '所属检测室',
        dataIndex: 'deptId',
        valueType: 'select',
        valueEnum: depId,
        formItemProps: {
          rules: [{ required: true }],
        },
        fieldProps: {
          onChange(value, row){
            setFieldNumId(value)
          }
        },
        render(dom, entity) {
          console.log(entity, 'entity?.deptName')
          return entity?.deptName;
        },
      },
      {
        dataIndex: 'fieldChildName',
        title: '所属领域',
        hideInForm: true,
      },
      {
        valueType: 'dependency',
        name: ['deptId'],
        columns: ({deptId}) => {
          if(!deptId) {
            return []
          }else {
            return [
              {
                dataIndex: 'fieldChildName',
                title: '所属领域',
                valueType: 'select',
                formItemProps: {
                  rules: [{ required: true }],
                },
                params: {
                  id: deptId
                },
                request: async (params) => {
                 const res = await  fieldNumInfoVoPage({
                    page: 1,
                    size: 9999,
                    deptId: params?.id
                  })
                  return res?.data?.records?.[0]?.realmChildName?.split('、')?.map((item) => {
                    return {
                      label: item,
                      value: item
                    }
                  })
                }
              },
            ]
          }
        }
      },
      {
        dataIndex: 'belongSystem',
        title: '所属系统',
        valueType: 'select',
        formItemProps: {
          rules: [{ required: true }],
        },
        fieldProps: {
          mode: 'tags',
        },
        render(dom, entity) {
          return (
            <>
              {entity?.belongSystem
                ?.split(',')
                ?.map((item: string, index: number) => {
                  return (
                    <Tag color="blue" key={index}>
                      {item}
                    </Tag>
                  );
                })}
            </>
          );
        },
      },
      {
        dataIndex: 'createUserFullName',
        title: '修改人',
        hideInSearch: true,
        hideInForm: true,
      },
    ]}
  />
  );
};

export default Distribution;
