import { buInstrumentWorkstationInfoPage } from '@/services/base/gongweixinxiguanlijiekou';
import { orgUserInfoVoPage } from '@/services/base/zuzhijiagourenyuanbiaojiekou';
import { ProDescriptions, ProTable } from '@ant-design/pro-components';
import { Button, Modal, Tag } from 'antd';
import React from 'react';

const Detail: React.FC<{ row: Record<string, any>; onCancel?: (e: any) => {} }> = ({
  row,
  ...rest
}) => {
  return (
    <Modal
      footer={(originNode) => {
        return (
          <Button
            type="primary"
            onClick={(e) => rest?.onCancel?.(e as React.MouseEvent<HTMLButtonElement>)}
          >
            确定
          </Button>
        );
      }}
      {...rest}
      width={'80%'}
    >
      <ProDescriptions<any>
        title="基本信息"
        dataSource={row}
        column={2}
        bordered
        columns={[
          {
            dataIndex: 'sbmc',
            title: '设备名称',
          },
          {
            dataIndex: 'professionalType',
            title: '专业类型',
          },
          {
            dataIndex: 'collectionMethod',
            title: '采集方式',
          },
          {
            dataIndex: 'gwBzIds',
            title: '关联实验',
            ellipsis: true,
            fieldProps: {
              style: { width: '100%' },
              showSearch: true,
              mode: 'multiple',
            },
            render: (_, record) => {
              if (record?.proName) {
                let names: string[] = record?.proName;
                return (
                  <>
                    {names.map((item: string, index: number) => {
                      return (
                        <Tag key={item} style={{ marginBottom: 4 }}>
                          {item}
                        </Tag>
                      );
                    })}
                  </>
                );
              }
              return '-';
            },
          },
          // 关联工位
          {
            dataIndex: 'workstationId',
            title: '所属检测工位',
            render: (_, record) => {
              return record?.workstationName || '-';
            },
          },
          {
            dataIndex: 'testUserFullName',
            title: '检测人员',
          },
          {
            dataIndex: 'scc',
            title: '设备厂商',
          },
          {
            dataIndex: 'commissionDate',
            title: '设备投入使用时间',
            valueType: 'date',
          },
        ]}
      />
      <div style={{ marginTop: 15, fontSize: 16, fontWeight: 600 }}>检查项目信息</div>
      <ProTable<any>
        search={false}
        options={false}
        pagination={false}
        dataSource={row?.instrumentDeptSysInfoList}
        cardProps={{
          bodyStyle: {
            padding: 0,
            marginTop: 10,
          },
        }}
        bordered
        columns={[
          {
            title: '所属系统',
            dataIndex: 'belongSystem',
            render(dom, entity, index, action, schema) {
              return (
                <>
                  {entity?.belongSystem?.split(',')?.map((item: string, index: number) => {
                    return (
                      <Tag color="blue" key={index}>
                        {item}
                      </Tag>
                    );
                  })}
                </>
              );
            },
          },
          {
            title: '所属领域',
            dataIndex: 'fieldChildName',
          },
          {
            title: '所属检测室',
            dataIndex: 'deptName',
          },
        ]}
      />
    </Modal>
  );
};

export default Detail;
