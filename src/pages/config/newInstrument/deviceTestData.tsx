import React, { useEffect, useState } from "react";
import { Modal, Tabs, Table, Button, message, Spin, Alert } from "antd";
import { deviceIntegrationGetExperimentData, deviceDataQueryPost } from '@/services/base/shebeiji<PERSON>ekou';
import dayjs from 'dayjs';

interface DeviceTestDataProps {
    row: any; // 设备信息
    open: boolean;
    onCancel: () => void;
}
const middleIcon = '-middle-'

// 表格列定义
const columns = [
    {
        title: '参数原始key',
        dataIndex: 'paramKey',
        key: 'paramKey',
        width: 150
    },
    {
        title: '参数',
        dataIndex: 'paramName',
        key: 'paramName',
        width: 200
    },
    {
        title: '测试值',
        dataIndex: 'testValue',
        key: 'testValue',
        render: (value: any) => {
            return value !== undefined && value !== null && value !== '' ? value : '暂无数据';
        }
    }
];

const DeviceTestData: React.FC<DeviceTestDataProps> = ({ row, open, onCancel }) => {
    const [activeKey, setActiveKey] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [experimentData, setExperimentData] = useState<any[]>([]);
    const [testDataMap, setTestDataMap] = useState<Record<string, any>>({});
    const [configLoading, setConfigLoading] = useState<boolean>(false);
    const [currentDeviceId, setCurrentDeviceId] = useState<number | null>(null);
    const [dataTimeMap, setDataTimeMap] = useState<Record<string, string>>({});

    // 当设备变化时重置数据
    useEffect(() => {
        if (row?.id !== currentDeviceId) {
            // 清空之前的实验数据和测试数据
            setExperimentData([]);
            setTestDataMap({});
            setDataTimeMap({});
            setActiveKey('');
            setCurrentDeviceId(row?.id || null);
        }
    }, [row?.id]);

    // 获取设备实验配置
    useEffect(() => {
        if (!open || !row?.id) {
            return;
        }

        setConfigLoading(true);
        deviceIntegrationGetExperimentData({ equipmentId: row.id })
            .then(response => {
                if (response.success && response.data) {
                    // 解析实验数据
                    const experiments = response.data.experiments?.map((item: any, idx: number) => {
                        item.name = item.name + middleIcon + idx;
                        return item;
                    }) || [];

                    setExperimentData(experiments);

                    // 默认选中第一个tab
                    if (experiments.length > 0) {
                        setActiveKey(experiments[0].name);

                        // 🌟 在这里直接处理实验数据中的参数映射，不再等待useEffect调用fetchTestData
                        const newTestDataMap = { ...testDataMap };

                        // 遍历所有实验
                        experiments.forEach((experiment: any) => {
                            const mapping = experiment.mapping || {};
                            // 创建表格数据
                            const tableData = [];

                            // 从映射关系中获取所有参数
                            if (mapping && Object.keys(mapping).length > 0) {
                                for (const [key, paramKey] of Object.entries(mapping)) {
                                    tableData.push({
                                        key: key,
                                        paramName: key,
                                        paramKey: paramKey,
                                        testValue: '暂无数据'  // 默认显示暂无数据
                                    });
                                }
                            }

                            // 更新数据
                            newTestDataMap[experiment.name] = tableData;
                        });

                        setTestDataMap(newTestDataMap);
                    }
                } else {
                    message.error('获取设备配置失败');
                }
            })
            .catch(error => {
                console.error('获取设备配置异常', error);
                message.error('获取设备配置异常');
            })
            .finally(() => {
                setConfigLoading(false);
            });
    }, [open, row?.id]);

    // 🌟 修改这个useEffect，默认情况下不自动获取测试数据
    // 当实验数据或活动tab变化时，不再自动获取测试数据
    // useEffect(() => {
    //     if (experimentData.length > 0 && activeKey) {
    //         const currentExperiment = experimentData.find(exp => exp.name === activeKey);
    //         if (currentExperiment && !testDataMap[activeKey]) {
    //             fetchTestData(activeKey, currentExperiment.tableName, currentExperiment.mapping);
    //         }
    //     }
    // }, [experimentData, activeKey]);

    // 获取测试数据 - 使用设备对接数据管理API
    const fetchTestData = async (experimentName: string, _tableName?: string, mappingData?: any) => {
        if (!row?.id || !row?.deviceTypeCode) {
            message.error('设备信息不完整，无法获取测试数据');
            return;
        }

        setLoading(true);
        try {
            console.log(`🔍 开始获取测试数据，实验名称: ${experimentName}, 设备类型代码: ${row.deviceTypeCode}`);

            // 调用设备对接数据管理API获取测试数据（不传时间参数，获取最新数据）
            const response = await deviceDataQueryPost({
                deviceCode: row.deviceTypeCode  // 使用设备类型代码
            });

            console.log('📡 API响应数据:', response);

            if (response.success && response.data) {
                // API返回的data是List<DeviceDataResponse>格式
                const experiments = Array.isArray(response.data) ? response.data : [];
                console.log(`📊 解析到${experiments.length}个实验数据:`, experiments);

                const currentExperimentData = experiments.find((exp: any) => exp.name === experimentName);

                if (!currentExperimentData) {
                    console.warn(`⚠️ 未找到实验"${experimentName}"的数据，可用实验:`, experiments.map(exp => exp.name));
                    message.warning(`未找到实验"${experimentName}"的数据`);
                    return;
                }

                console.log(`✅ 找到实验数据:`, currentExperimentData);
                // 获取实验结果数据
                const testResults = currentExperimentData.result || [];

                // 获取数据时间
                const dataTime = currentExperimentData.dataTime;

                // 获取当前实验配置的映射关系
                const mapping = mappingData ||
                    experimentData.find(exp => exp.name === activeKey)?.mapping ||
                    {};

                // 创建表格数据
                const tableData = [];

                // 先从映射关系中获取所有参数
                if (mapping && Object.keys(mapping).length > 0) {
                    for (const [displayName, fieldName] of Object.entries(mapping)) {
                        // 从测试结果中查找对应的数据
                        const testResult = testResults.find((result: any) =>
                            result.name === displayName || result.code === fieldName
                        );

                        tableData.push({
                            key: displayName,
                            paramName: displayName,
                            paramKey: fieldName,
                            testValue: testResult ? testResult.value : '暂无数据'
                        });
                    }
                } else {
                    // 如果没有映射关系，则直接使用测试结果数据
                    testResults.forEach((result: any) => {
                        tableData.push({
                            key: result.name || result.code,
                            paramName: result.name || result.code,
                            paramKey: result.code || '',
                            testValue: result.value || '暂无数据'
                        });
                    });
                }

                // 更新数据 - 使用activeKey作为键，因为表格显示时使用的是experiment.name（带索引后缀）
                const newTestDataMap = { ...testDataMap };
                newTestDataMap[activeKey] = tableData;
                setTestDataMap(newTestDataMap);

                // 更新数据时间
                if (dataTime) {
                    const newDataTimeMap = { ...dataTimeMap };
                    newDataTimeMap[activeKey] = dayjs(dataTime).format('YYYY-MM-DD HH:mm:ss');
                    setDataTimeMap(newDataTimeMap);
                }

                message.success(`成功刷新${activeKey.split(middleIcon)[0]}的测试数据，共${tableData.length}条`);
                console.log(`成功加载${activeKey}的测试数据，共${tableData.length}条`);
            } else {
                message.error('获取测试数据失败：' + (response.message || '未知错误'));
            }
        } catch (error) {
            console.error('获取测试数据失败', error);
            message.error('获取测试数据失败，请检查网络连接');
        } finally {
            setLoading(false);
        }
    };

    // Tab切换处理
    const handleTabChange = (key: string) => {
        setActiveKey(key);
        // 🌟 不再自动加载数据
    };

    // 手动刷新数据
    const refreshData = () => {
        if (activeKey) {
            // 直接使用activeKey查找实验（因为activeKey就是完整的实验名称，包含索引后缀）
            const experiment = experimentData.find(exp => exp.name === activeKey);
            if (experiment) {
                // 从activeKey中提取原始实验名称（去掉-middle-索引后缀）
                const originalExperimentName = activeKey.replace(/-middle-\d+$/, '');
                fetchTestData(originalExperimentName, experiment.tableName, experiment.mapping);
            } else {
                console.log('调试信息 - activeKey:', activeKey);
                console.log('调试信息 - experimentData:', experimentData.map(exp => exp.name));
                message.warning('未找到当前实验配置');
            }
        } else {
            message.warning('请先选择一个实验标签');
        }
    };

    return (
        <Modal
            title={`${row?.sbmc || ''}设备测试数据`}
            open={open}
            onCancel={onCancel}
            width={800}
            footer={[
                <Button key="refresh" type="primary" loading={loading} onClick={refreshData}>
                    刷新数据
                </Button>,
                <Button key="close" onClick={onCancel}>
                    关闭
                </Button>
            ]}
        >
            {configLoading ? (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                    <Spin tip="加载设备配置中..." />
                </div>
            ) : experimentData.length > 0 ? (
                <Tabs
                    activeKey={activeKey}
                    onChange={handleTabChange}
                    type="card"
                    items={experimentData.map((experiment) => ({
                        key: experiment.name,
                        label: experiment.name?.split(middleIcon)[0],
                        children: (
                            <div>
                                {/* 数据时间信息 */}
                                {dataTimeMap[experiment.name] && (
                                    <Alert
                                        message={`数据时间：${dataTimeMap[experiment.name]}`}
                                        type="info"
                                        showIcon
                                        style={{ marginBottom: 16 }}
                                    />
                                )}
                                <Table
                                    columns={columns}
                                    dataSource={testDataMap[experiment.name] || []}
                                    pagination={false}
                                    loading={loading && activeKey === experiment.name}
                                    size="small"
                                    scroll={{ y: 400 }}
                                />
                            </div>
                        )
                    }))}
                />
            ) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                    没有可用的实验数据配置
                </div>
            )}
        </Modal>
    );
};

export default DeviceTestData;