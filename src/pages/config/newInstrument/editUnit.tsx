import { instrumentNewInfoUpdate } from '@/services/base/yiqiyibia<PERSON>ek<PERSON>';
import { arr2ValueEnum } from '@/utils';
import {
  ActionType,
  EditableProTable,
  ModalForm,
  ModalFormProps,
} from '@ant-design/pro-components';
import { Button, Form, message, Upload, Modal } from 'antd';
import { UploadOutlined, DownloadOutlined, ImportOutlined } from '@ant-design/icons';
import { request } from '@umijs/max';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import type { UploadProps } from 'antd';

export interface UnitDataProps {
  id: string;
  param: string;
  unit: string;
  collectionMethod: string;
  isNew?: boolean;
}
export interface EditUnitProps extends Omit<ModalFormProps, 'onFinish'> {
  unitData: UnitDataProps[];
  id: string;
  onFinish?: () => void;
}

const EditUnit = (props: EditUnitProps) => {
  const { unitData = [], onFinish, ...rest } = props;
  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();

  const [dataValue, setDataValue] = useState<UnitDataProps[]>(unitData);
  const [editableKeys, setEditableKeys] = useState<string[]>([]);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [uploading, setUploading] = useState(false);

  // 📤 导出模板
  const handleExportTemplate = async () => {
    try {
      const response = await request('/instrumentNewInfo/exportCheckParamsTemplate', {
        method: 'POST',
        responseType: 'blob',
        params: {
          equipmentId: props.id, // 🔧 传递设备ID，导出该设备的实际数据
        },
      });

      if (response) {
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `可检参数导入模板_${Date.now()}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        message.success('📤 模板导出成功！');
      } else {
        message.error('导出模板失败');
      }
    } catch (error) {
      console.error('导出模板失败:', error);
      message.error('导出模板失败');
    }
  };

  // 📥 刷新设备可检参数数据
  const refreshEquipmentData = async () => {
    try {
      // 🔍 重新获取设备信息
      const equipmentResponse = await request('/instrumentNewInfo/getVo', {
        method: 'POST',
        data: { id: props.id },
      });

      if (equipmentResponse.success && equipmentResponse.data) {
        const equipment = equipmentResponse.data;
        if (equipment.parametersInspectedEquipment) {
          try {
            // 📋 解析可检参数JSON数据并更新表格
            const parsedData = JSON.parse(equipment.parametersInspectedEquipment);
            setDataValue(parsedData);
          } catch (e) {
            console.warn('解析可检参数数据失败:', e);
          }
        }
      }
    } catch (error) {
      console.error('刷新设备数据失败:', error);
    }
  };

  // 📥 处理文件导入
  const handleImport: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file as File);
      formData.append('equipmentId', props.id);

      const result = await request('/instrumentNewInfo/importCheckParams', {
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (result.success) {
        message.success('📥 导入成功！');

        // 🔄 刷新当前组件的数据，而不是关闭弹窗
        await refreshEquipmentData();

        // 关闭导入弹窗
        setImportModalVisible(false);

        onSuccess?.(result);
      } else {
        message.error(`导入失败：${result.message}`);
        onError?.(new Error(result.message));
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败');
      onError?.(error as Error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <ModalForm
      title="编辑可检参数"
      width={800}
      {...rest}
      onFinish={async () => {
        if (editableKeys.length > 0) {
          message.error('请先保存当前编辑');
          return;
        }
        dataValue.forEach((item: any) => {
          if (typeof item.experimentalProject === 'object') {
            item.experimentalProject = item.experimentalProject.label;
          }
        });
        let res = await instrumentNewInfoUpdate({
          id: props.id as unknown as number,
          parametersInspectedEquipment: JSON.stringify(dataValue),
        });
        if (res.success) {
          message.success('保存成功');
          onFinish?.();
          return true;
        }
        message.error('保存失败');
        return false;
      }}
    >
      <EditableProTable
        columns={[
          {
            title: '可检参数名',
            dataIndex: 'paramName',
            ellipsis: true,
            formItemProps: {
              rules: [{ required: true, message: '请输入可检参数名' }],
            },
          },
          {
            title: '参数类型',
            dataIndex: 'paramType',
            ellipsis: true,
            valueType: 'select',
            // 比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型
            valueEnum: arr2ValueEnum([
              '比较型',
              '计算比较依据型',
              '计算比较结果型',
              '不做判定型',
              '人工判定型',
              '其他型',
            ]),
            formItemProps: {
              rules: [{ required: true, message: '请选择参数类型' }],
            },
          },
          {
            title: '可检参数单位',
            dataIndex: 'unit',
            ellipsis: true,
            formItemProps: {
              rules: [{ required: true, message: '请输入可检参数单位' }],
            },
          },
          {
            title: '采集方式',
            dataIndex: 'collectionMethod',
            valueType: 'select',
            fieldProps: {
              showSearch: true,
              allowClear: true,
              fetchDataOnSearch: false,
              optionFilterProp: 'label',
              labelInValue: true,
            },
            ellipsis: true,
            // 直采、填报、随机生成
            valueEnum: arr2ValueEnum(['直采', '填报', '随机生成']),
            formItemProps: {
              rules: [{ required: true, message: '请选择采集方式' }],
            },
          },
          {
            title: '操作',
            valueType: 'option',
            width: 120,
            render: (_: any, record: UnitDataProps, index: any, action: any) => {
              return [
                <a
                  key="editable"
                  onClick={() => {
                    if (editableKeys.length > 0) {
                      message.error('请先保存当前编辑');
                      return;
                    }
                    action?.startEditable?.(record.id);
                  }}
                >
                  编辑
                </a>,
                <a
                  key="delete"
                  onClick={() => {
                    setDataValue(dataValue.filter((item: UnitDataProps) => item.id !== record.id));
                  }}
                >
                  删除
                </a>,
              ];
            },
          },
        ]}
        value={dataValue}
        recordCreatorProps={false}
        className="scroll-table"
        editable={{
          type: 'single',
          form: form,
          editableKeys,
          onChange: (keys: any[]) => {
            setEditableKeys(keys);
          },
          onSave: async (record: any, row: UnitDataProps) => {
            if (row.isNew) {
              setDataValue([
                ...dataValue,
                {
                  ...row,
                  isNew: false,
                },
              ]);
            } else {
              setDataValue(
                dataValue.map((item: UnitDataProps) => {
                  if (item.id === record) {
                    return row;
                  }
                  return item;
                }),
              );
            }
          },
          actionRender: (row: any, config: any, dom: any) => [dom.save, dom.cancel],
        }}
        rowKey="id"
        pagination={false}
        search={false}
        actionRef={actionRef}
        toolBarRender={() => {
          return [
            <Button
              key="import"
              icon={<ImportOutlined />}
              onClick={() => setImportModalVisible(true)}
              style={{ marginRight: 8 }}
            >
              📥 导入参数
            </Button>,
            <Button
              key="add"
              type="primary"
              onClick={() => {
                if (editableKeys.length > 0) {
                  message.error('请先保存当前编辑');
                  return;
                }
                // 新增
                actionRef.current?.addEditRecord?.({
                  id: dayjs().valueOf(),
                  title: '新的一行',
                  isNew: true,
                });
                setTimeout(() => {
                  const tableWrapper = document.querySelector('.scroll-table');
                  if (tableWrapper) {
                    let node: any = tableWrapper.parentNode?.parentNode;
                    if (node) {
                      node.scrollTo({
                        top: node.scrollHeight,
                        behavior: 'smooth',
                      });
                    }
                  }
                }, 100);
              }}
            >
              新增
            </Button>,
          ];
        }}
      />

      {/* 📥 导入模态框 */}
      <Modal
        title="📥 导入可检参数"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: 16, color: '#666' }}>
            <p>📋 <strong>导入说明：</strong></p>
            <ul style={{ paddingLeft: 20 }}>
              <li>请先下载模板文件，按照模板格式填写数据</li>
              <li>支持 .xlsx 和 .xls 格式的Excel文件</li>
              <li>如果参数名已存在，将跳过导入</li>
              <li>请确保所有必填字段都已填写</li>
            </ul>
          </div>

          <Upload
            accept=".xlsx,.xls"
            showUploadList={false}
            customRequest={handleImport}
            disabled={uploading}
          >
            <Button
              icon={<UploadOutlined />}
              loading={uploading}
              size="large"
              type="primary"
              style={{ width: '100%', height: 50 }}
            >
              {uploading ? '📤 导入中...' : '📁 选择Excel文件'}
            </Button>
          </Upload>

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={handleExportTemplate}
            >
              📤 下载导入模板
            </Button>
          </div>
        </div>
      </Modal>
    </ModalForm>
  );
};

export default EditUnit;
