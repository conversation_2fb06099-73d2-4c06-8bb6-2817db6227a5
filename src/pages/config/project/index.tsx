import ProTable, { ProTableProps } from '@/components/proTable';
import { buInstrumentWorkstationInfoGetWorkstationDeviceTree } from '@/services/base/gongweixinxiguanlijiekou';
import { instrumentNewInfoGet } from '@/services/base/yiqiyibiaojiekou';
import { PageContainer } from '@ant-design/pro-components';
import { Access, useAccess } from '@umijs/max';
import { Button, Tag, Tooltip } from 'antd';
import { launchSchema, OnlyInSearch } from 'antd-pro-crud';

import Edit from './edit';

const alert_editProject = launchSchema(Edit);

const Page: React.FC = () => {
  const { commonAccess } = useAccess();
  const columns: ProTableProps<BASE.StandardBasicProjectInstrumentVO>['columns'] = [
    // 实验项目、检测仪器（可多选）、检项、创建时间、修改时间
    {
      dataIndex: 'index',
      title: '序号',
      valueType: 'index',
      width: 80,
    },
    {
      dataIndex: 'projectName',
      title: '实验项目',
      formItemProps: { rules: [{ required: true }] },
      fieldProps: {
        allowClear: true,
        style: { width: '100%' },
      },
      colProps: { span: 24 },
      align: 'left',
      ellipsis: true,
      width: 200,
    },
    {
      dataIndex: 'instrumentId',
      title: '检测仪器',
      hideInDescriptions: true,
      ellipsis: true,
      fieldProps: {
        fieldNames: {
          label: 'label',
          value: 'id',
          children: 'children',
        },
        showSearch: true,
        treeNodeFilterProp: 'label',
        onChange: async (value: string) => {
          if (!value) return;
          // 根据value获取对应的设备
        },
      },
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      align: 'left',
      valueType: 'treeSelect',
      request: async () => {
        const res = await buInstrumentWorkstationInfoGetWorkstationDeviceTree();
        // 处理数据，type为workstation时id加上'workstation-'前缀
        const treeData = res.data?.map(
          (item: BASE.WorkstationDeviceTreeDTO & { selectable?: boolean }) => {
            if (item.type === 'workstation') {
              item.id = `workstation-${item.id}` as unknown as number;
              item.selectable = false;
            }
            return item;
          },
        );
        // 不显示没有子节点的节点
        return treeData || [];
      },
      render: (value: any, record: any) => {
        return record?.instrumentNewInfo?.sbmc || '-';
      },
    },
    {
      dataIndex: 'instrumentNewInfo',
      title: '检测仪器',
      ...OnlyInSearch,
      hideInSearch: true,
      hideInDescriptions: false,
      render: (value: any, record: any) => {
        return <>{record.instrumentNewInfo?.sbmc}</>;
      },
    },
    {
      dataIndex: 'projectParams',
      title: '检项',
      hideInSearch: true,
      ellipsis: true,
      colProps: { span: 24 },
      dependencies: ['instrumentId'],
      request: async (params: any) => {
        if (!params.instrumentId) return [];
        const res = await instrumentNewInfoGet({
          id: params.instrumentId,
        });
        if (res.status === 200) {
          let instrumentNameString = res.data?.parametersInspectedEquipment ?? '[]';

          try {
            let instrumentNameList = JSON.parse(instrumentNameString) ?? [];
            let options = instrumentNameList?.map((item: any) => ({
              value: String(item.id),
              label: item.paramName,
              paramType: item.paramType,
            }));
            console.log(options, 'options');
            return options;
          } catch (error) {
            console.log(error);
            return [];
          }
        }
      },
      render: (value: any, record: any) => {
        let tags = record.projectParams?.map((item: any) => item.paramName);
        return (
          <Tooltip
            title={
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                {tags?.map((item: any, index: number) => (
                  <Tag key={index}>{item}</Tag>
                ))}
              </div>
            }
          >
            {tags?.map((item: any, index: number) => (
              <Tag key={index}>{item}</Tag>
            ))}
          </Tooltip>
        );
      },
    },
    {
      dataIndex: 'createTime',
      title: '创建时间',
      hideInSearch: true,
      hideInForm: true,
      valueType: 'date',
      width: 150,
    },
    {
      dataIndex: 'updateTime',
      title: '修改时间',
      hideInSearch: true,
      hideInForm: true,
      valueType: 'date',
      width: 150,
    },
    // 详情应包含创建人、最后操作人信息
    {
      dataIndex: 'createUserFullName',
      title: '创建人',
      hideInSearch: true,
      hideInForm: true,
      hideInTable: true,
      width: 150,
    },
    {
      dataIndex: 'updateUserFullName',
      title: '最后操作人',
      hideInSearch: true,
      hideInForm: true,
      hideInTable: true,
      width: 150,
    },
    // 操作
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 150,
      render: (_, record, index, action, schema) => [
        <Access key={record.id} accessible={commonAccess('admin:/standardManage/project@edit')}>
          <a
            type="link"
            key={record.id}
            onClick={() =>
              alert_editProject({
                row: record,
                isEdit: true,
                initialValues: record,
                onFinish: () => action?.reload(),
              })
            }
          >
            编辑
          </a>
        </Access>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.StandardBasicProjectInstrumentVO>
        scroll={{ x: '1000', y: 500 }}
        crudKey="standardBasicProjectInstrument"
        columns={columns}
        hiddenBtns={['add', 'edit']}
        toolBarRender={(action: any) => [
          <Access key="add" accessible={commonAccess('admin:/standardManage/project@add')}>
            <Button
              type="primary"
              key={'add'}
              onClick={() =>
                alert_editProject({
                  isEdit: false,
                  onFinish: () => action?.reload(),
                })
              }
            >
              新增
            </Button>
          </Access>,
        ]}
      />
    </PageContainer>
  );
};

export default Page;
