import { buInstrumentWorkstationInfoGetWorkstationDeviceTree } from '@/services/base/gongweixinxiguanlijiekou';
import { arr2ValueEnum } from '@/utils';
import {
  ActionType,
  EditableProTable,
  ModalForm,
  ModalFormProps,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { useEffect, useRef, useState } from 'react';

import {
  standardBasicProjectInstrumentAdd,
  standardBasicProjectInstrumentUpdate,
} from '@/services/base/biaozhunshiyanxiangmujianceyiqixinxibiaojiekou';
import { instrumentNewInfoGet } from '@/services/base/yiqiyibiaojiekou';
import { Button } from 'antd';
import dayjs from 'dayjs';
interface UnitDataProps {
  id?: string;
  paramName: string;
  paramType: string;
  unit: string;
  collectionMethod: string;
  isNew?: boolean;
}

interface EditProps extends Omit<ModalFormProps, 'onFinish'> {
  onFinish?: () => void;
  row?: any;
  isEdit?: boolean;
}

function Edit(props: EditProps) {
  const { onFinish, isEdit, ...rest } = props;
  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableKeys] = useState<string[]>([]);
  const [dataValue, setDataValue] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (props?.isEdit) {
      setDataValue(props?.initialValues?.projectParams);
    }
  }, [props?.isEdit]);
  return (
    <ModalForm
      title={props?.isEdit ? '编辑实验项目' : '新增实验项目'}
      width={800}
      grid
      {...rest}
      onFinish={async (values: any) => {
        if (editableKeys.length > 0) {
          message.error('请先保存当前编辑');
          return;
        }
        const submitValues = {
          ...values,
        };
        const res = props?.isEdit
          ? await standardBasicProjectInstrumentUpdate({
              ...submitValues,
              id: props?.initialValues?.id,
              projectParams: dataValue.map((item: UnitDataProps) => ({
                paramName: item.paramName,
                paramType: item.paramType,
                unit: item.unit,
                collectionMethod:
                  typeof item.collectionMethod === 'string'
                    ? item.collectionMethod
                    : (item.collectionMethod as { value: string })?.value ?? '',
              })),
            })
          : await standardBasicProjectInstrumentAdd({
              ...submitValues,
              projectParams: dataValue.map((item: UnitDataProps) => ({
                paramName: item.paramName,
                paramType: item.paramType,
                unit: item.unit,
                collectionMethod:
                  typeof item.collectionMethod === 'string'
                    ? item.collectionMethod
                    : (item.collectionMethod as { value: string })?.value ?? '',
              })),
            });
        if (res?.success) {
          message.success(res?.message);
          props?.modalProps?.onCancel?.(submitValues);
          onFinish?.();
        }
      }}
    >
      <ProFormText
        name="projectName"
        label="实验项目名称"
        formItemProps={{ rules: [{ required: true }] }}
      />
      {/* 检测仪器 */}
      <ProFormTreeSelect
        name="instrumentId"
        label="检测仪器"
        formItemProps={{ rules: [{ required: true }] }}
        fieldProps={{
          fieldNames: {
            label: 'label',
            value: 'id',
            children: 'children',
          },
          showSearch: true,
          treeNodeFilterProp: 'label',
          onChange: async (value: string) => {
            if (!value) {
              setDataValue([]);
              return;
            }
            // 根据value获取对应的设备
            setLoading(true);
            const res = await instrumentNewInfoGet({
              id: value,
            });
            if (res?.status === 200) {
              let instrumentNameString = res.data?.parametersInspectedEquipment ?? '[]';

              try {
                let instrumentNameList = JSON.parse(instrumentNameString) ?? [];
                instrumentNameList = instrumentNameList.map((item: any) => ({
                  paramName: item.paramName,
                  paramType: item.paramType,
                  unit: item.unit,
                  collectionMethod: item.collectionMethod ?? item.collectionMethod?.value,
                  id: item.id,
                }));
                setDataValue(instrumentNameList);
              } catch (error) {
                console.log(error);
              }
            }
            setLoading(false);
          },
        }}
        convertValue={(value: string) => {
          let name = props?.initialValues?.instrumentNewInfo?.sbmc;
          return name;
        }}
        transform={(value: string) => {
          console.log(value, 'value');
          return {
            instrumentId: value,
          };
        }}
        request={async () => {
          const res = await buInstrumentWorkstationInfoGetWorkstationDeviceTree();
          // 处理数据，type为workstation时id加上'workstation-'前缀
          const treeData = res.data?.map(
            (item: BASE.WorkstationDeviceTreeDTO & { selectable?: boolean }) => {
              if (item.type === 'workstation') {
                item.id = `workstation-${item.id}` as unknown as number;
                item.selectable = false;
              }
              return item;
            },
          );
          console.log(treeData, 'treeData');
          // 不显示没有子节点的节点
          const newTreeData = treeData?.filter((item: any) => item.children?.length > 0) || [];
          return newTreeData || [];
        }}
      />
      {/* 检项 */}
      <EditableProTable
        columns={[
          {
            dataIndex: 'paramName',
            title: '可检参数名',
            formItemProps: {
              rules: [{ required: true, message: '请输入可检参数名' }],
            },
          },
          {
            dataIndex: 'paramType',
            title: '参数类型',
            valueType: 'select',
            // 比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型
            valueEnum: arr2ValueEnum([
              '比较型',
              '计算比较依据型',
              '计算比较结果型',
              '不做判定型',
              '人工判定型',
              '其他型',
            ]),
            formItemProps: {
              rules: [{ required: true, message: '请选择参数类型' }],
            },  
          },
          // 可检参数单位
          {
            dataIndex: 'unit',
            title: '可检参数单位',
            formItemProps: {
              rules: [{ required: true, message: '请输入可检参数单位' }],
            },
          },
          // 采集方式
          {
            dataIndex: 'collectionMethod',
            title: '采集方式',
            valueType: 'select',
            fieldProps: {
              showSearch: true,
              allowClear: true,
              fetchDataOnSearch: false,
              optionFilterProp: 'label',
              labelInValue: true,
              convertValue: (value: string) => {
                return {
                  label: value,
                  value: value,
                };
              },
            },
            formItemProps: {
              rules: [{ required: true, message: '请选择采集方式' }],
            },
            ellipsis: true,
            // 直采、填报、随机生成
            valueEnum: arr2ValueEnum(['直采', '填报', '随机生成']),
          },
          // 操作
          {
            title: '操作',
            valueType: 'option',
            render: (_, record, index, action) => [
              <a
                key="editable"
                onClick={() => {
                  if (editableKeys.length > 0) {
                    message.error('请先保存当前编辑');
                    return;
                  }
                  action?.startEditable?.(record.id);
                }}
              >
                编辑
              </a>,
              <a
                key="delete"
                onClick={() => {
                  setDataValue(dataValue.filter((item: UnitDataProps) => item.id !== record.id));
                }}
              >
                删除
              </a>,
            ],
          },
        ]}
        value={dataValue}
        recordCreatorProps={false}
        loading={loading}
        editable={{
          type: 'single',
          form: form,
          editableKeys,
          onChange: (keys: any[]) => {
            setEditableKeys(keys);
          },
          onSave: async (record: any, row: UnitDataProps) => {
            if (row.isNew) {
              setDataValue([
                ...dataValue,
                {
                  ...row,
                  isNew: false,
                },
              ]);
            } else {
              setDataValue(
                dataValue.map((item: UnitDataProps) => {
                  if (item.id === record) {
                    return row;
                  }
                  return item;
                }),
              );
            }
          },
          actionRender: (row: any, config: any, dom: any) => [dom.save, dom.cancel],
        }}
        rowKey="id"
        pagination={false}
        search={false}
        actionRef={actionRef}
        toolBarRender={() => {
          return [
            <Button
              key="add"
              type="primary"
              onClick={() => {
                if (editableKeys.length > 0) {
                  message.error('请先保存当前编辑');
                  return;
                }
                // 新增
                actionRef.current?.addEditRecord?.({
                  id: dayjs().valueOf(),
                  title: '新的一行',
                  isNew: true,
                });
              }}
            >
              新增
            </Button>,
          ];
        }}
      />
    </ModalForm>
  );
}

export default Edit;
