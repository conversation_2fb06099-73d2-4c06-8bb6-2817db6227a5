import ProTable, { ProTableProps } from '@/components/proTable';
import { standardBasicInstrumentInfoGetVo } from '@/services/base/biaozhunjianceyiqibiaozhunxinxibiaojiekou';
import { standardBasicInfoVoPage } from '@/services/base/biaozhunjichengzhuangzhishebeixinxibiaojiekou';
import { buSampleBaseInfoVoPage } from '@/services/base/yangpinjibenxinxiguanlianbiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import { ActionType } from '@ant-design/pro-table';
import { Access, useAccess } from '@umijs/max';
import { Button, message, Space, Tag, Tooltip } from 'antd';
import { launch, launchSchema } from 'antd-pro-crud';
import { useRef } from 'react';
import FileDownload from '@/components/fileDownload';
import Detail from './detail';
import Edit from './edit';
import TemplateValuesEdit from './templateValuesEdit';

const alert_detail = launch(Detail);
const alert_edit = launchSchema(Edit);
const editTemplateValues = launchSchema(TemplateValuesEdit);

const Page: React.FC = () => {
  const { commonAccess } = useAccess();
  const actionRef = useRef<ActionType>();
  const columns: ProTableProps<BASE.StandardBasicInstrumentInfoVO>['columns'] = [
    {
      dataIndex: 'index',
      title: '序号',
      width: 80,
      valueType: 'index',
    },
    {
      dataIndex: 'sampleName',
      title: '物资类别',
      ellipsis: true,
      colProps: { span: 24 },
      valueType: 'select',
      width: 160,
      align: 'left',
      fieldProps: {
        allowClear: true,
        showSearch: true,
        fieldNames: {
          label: 'name',
          value: 'name',
        },
      },
      request: async () => {
        const res = await buSampleBaseInfoVoPage({ page: 1, size: 2000 });
        // 筛选出物资类别重复的
        const unique = res?.data?.records?.filter(
          (item: any, index: number, self: any) =>
            index === self.findIndex((t: any) => t.name === item.name),
        );

        return unique || [];
      },
    },
    {
      dataIndex: 'sampleModel',
      title: '物资型号',
      width: 160,
      ellipsis: true,
      hideInSearch: true,
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      fieldProps: {
        style: { width: '100%' },
      },
      align: 'left',
    },
    {
      dataIndex: 'sampleSpecifications',
      title: '物资规格',
      width: 160,
      ellipsis: true,
      colProps: { span: 24 },
      align: 'left',
      valueType: 'select',
      request: async (params) => {
        const res = await buSampleBaseInfoVoPage({
          size: 2000,
          page: 1,
        });
        return res?.data?.records || [];
      },
    },
    {
      dataIndex: 'testCapabilityLevel',
      title: '检测能力级别',
      width: 120,
      hideInSearch: true,
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      convertValue: (v) => (Array.isArray(v) ? v : v?.split(',')),
      transform: (v) => (Array.isArray(v) ? v?.join(',') : v),
      fieldProps: {
        mode: 'multiple',
        style: { width: '100%' },
      },
      // A,B,C
      valueEnum: arr2ValueEnum(['A', 'B', 'C']),
      align: 'left',
    },
    {
      dataIndex: 'standardBasicProjectInstrumentList',
      title: '实验项目',
      width: 180,
      ellipsis: true,
      hideInForm: true,
      hideInSearch: true,
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      align: 'left',
      render: (_, record) => {
        return (
          <Tooltip
            title={record?.standardBasicProjectInstrumentList?.map((item) => (
              <Tag key={item.id}>
                {item.projectName}
              </Tag>
            ))}
          >
            {record?.standardBasicProjectInstrumentList?.map((item) => item.projectName).join(',') || '-'}
          </Tooltip>
        );
      },
    },
    {
      dataIndex: 'standardRequirement',
      title: '标准要求',
      width: 160,
      ellipsis: true,
      hideInSearch: true,
      formItemProps: { rules: [{ required: true }] },
      colProps: { span: 24 },
      align: 'left',
    },    // 报告模板文件列
    {
      dataIndex: 'reportTemplateFileId',
      title: '报告模板',
      width: 160,
      ellipsis: true,
      hideInSearch: true,
      hideInForm: true,
      align: 'left',
      render: (_, record: any) => {
        // 处理后端返回的报告模板文件信息
        // 后端返回的数据结构是 reportTemplateFileInfo，而不是 reportTemplateFile
        const fileInfo = record?.reportTemplateFileInfo || record?.reportTemplateFile;

        if (fileInfo && typeof fileInfo === 'object') {
          return (
            <FileDownload
              item={fileInfo}
              preview={false}
              isDowmload={true}
              key={fileInfo.id || record.reportTemplateFileId}
            />
          );
        } else if (record.reportTemplateFileId) {
          // 如果只有ID，则构造一个简单的文件信息对象
          const simpleFileInfo = {
            id: record.reportTemplateFileId,
            fileName: '查看报告模板',
            fileUrl: `/api/test/file/download/${record.reportTemplateFileId}`
          };
          return (
            <FileDownload
              item={simpleFileInfo}
              preview={false}
              isDowmload={true}
              key={record.reportTemplateFileId}
            />
          );
        }
        return '-';
      },
    },    // 移除报告模板自定义值编辑按钮列，已移至操作列
    // 原始记录模板文件列
    {
      dataIndex: 'originalRecordTemplateFileId',
      title: '原始记录模板',
      width: 160,
      ellipsis: true,
      hideInSearch: true,
      hideInForm: true,
      align: 'left',
      render: (_, record: any) => {
        // 处理后端返回的原始记录模板文件信息
        // 后端返回的数据结构是 originalRecordTemplateFileInfo，而不是 originalRecordTemplateFile
        const fileInfo = record?.originalRecordTemplateFileInfo || record?.originalRecordTemplateFile;

        if (fileInfo && typeof fileInfo === 'object') {
          return (
            <FileDownload
              item={fileInfo}
              preview={false}
              isDowmload={true}
              key={fileInfo.id || record.originalRecordTemplateFileId}
            />
          );
        } else if (record.originalRecordTemplateFileId) {
          // 如果只有ID，则构造一个简单的文件信息对象
          const simpleFileInfo = {
            id: record.originalRecordTemplateFileId,
            fileName: '查看原始记录模板',
            fileUrl: `/api/test/file/download/${record.originalRecordTemplateFileId}`
          };
          return (
            <FileDownload
              item={simpleFileInfo}
              preview={false}
              isDowmload={true}
              key={record.originalRecordTemplateFileId}
            />
          );
        }
        return '-';
      },
    },    // 移除原始记录模板自定义值编辑按钮列，已移至操作列
    {
      dataIndex: 'createTime',
      title: '创建时间',
      hideInSearch: true,
      hideInForm: true,
      width: 120,
      valueType: 'date',
      fieldProps: { style: { width: '100%' } },
    },
    // 修改时间
    {
      dataIndex: 'updateTime',
      title: '修改时间',
      hideInSearch: true,
      hideInForm: true,
      width: 120,
      valueType: 'date',
      fieldProps: { style: { width: '100%' } },
    },
    {      // 操作
      title: '操作',
      valueType: 'option',
      width: 400, // 增加宽度以适应更多操作
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            {/* 编辑报告模板值 */}
            <a
              onClick={() => {
                editTemplateValues({
                  row: record,
                  templateType: 'report',
                  onFinish: async () => {
                    actionRef.current?.reload();
                    return true;
                  }
                });
              }}
            >
              编辑报告模板值
            </a>

            {/* 编辑原始记录模板值 */}
            <a
              onClick={() => {
                editTemplateValues({
                  row: record,
                  templateType: 'originalRecord',
                  onFinish: async () => {
                    actionRef.current?.reload();
                    return true;
                  }
                });
              }}
            >
              编辑原始记录模板值
            </a>

            {/*  编辑 */}
            <Access accessible={commonAccess('admin:/standardManage/standard@edit')}>
              <a
                onClick={async () => {
                  let modal = alert_edit({
                    row: {
                      ...record,
                      projectIds: record?.standardBasicProjectInstrumentList?.map(
                        (item) => item.id,
                      ),
                    },
                    onFinish: async () => {
                      actionRef.current?.reload();
                      modal.close();
                    },
                  });
                }}
              >
                编辑
              </a>
            </Access>

            {/*  详情 */}
            <Access accessible={commonAccess('admin:/standardManage/standard@detail')}>
              <a
                onClick={async () => {
                  const res = await standardBasicInstrumentInfoGetVo({
                    id: record.id as unknown as string,
                  });
                  if (res.data) {
                    alert_detail({
                      row: res.data,
                    });
                  } else {
                    message.error('获取详情失败');
                  }
                }}
              >
                详情
              </a>
            </Access>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.StandardBasicInstrumentInfoVO>
        hiddenBtns={['detail', 'add', 'edit']}
        scroll={{ x: 'fit-content', y: 500 }}
        crudKey="standardBasicInstrumentInfo"
        columns={columns}
        rowKey="id"
        actionRef={actionRef}
        crud={{
          delete: {
            visible: (row) => commonAccess('admin:/standardManage/standard@delete'),
          },
        }}
        toolBarRender={() => {
          return [
            <Access key="add" accessible={commonAccess('admin:/standardManage/standard@add')}>
              <Button
                type="primary"
                key="add"
                onClick={() => {
                  let modal = alert_edit({
                    onFinish: async () => {
                      actionRef.current?.reload();
                      modal.close();
                    },
                  });
                }}
              >
                新增
              </Button>
            </Access>,
          ];
        }}
      />
    </PageContainer>
  );
};

export default Page;
