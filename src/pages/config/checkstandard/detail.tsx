import { standardBasicInfoVoPage } from '@/services/base/biaozhunjichengzhuangzhishebeixinxibiaojiekou';
import { ProDescriptions } from '@ant-design/pro-components';
import { Button, Modal, ModalProps } from 'antd';
import { Card, OnlyInSearch  } from 'antd-pro-crud';

type SampleDetailProps = {
  row: BASE.StandardBasicInstrumentInfoVO;
} & ModalProps;

const SampleDetail: React.FC<SampleDetailProps> = ({ row, ...rest }) => {
  console.log(row)
  return (
    <Modal
      title="任务详情"
      width={1000}
      footer={(originNode) => {
        return (
          <Button
            type="primary"
            onClick={(e) => rest?.onCancel?.(e as React.MouseEvent<HTMLButtonElement>)}
          >
            确定
          </Button>
        );
      }}
      {...rest}
    >
      <ProDescriptions<BASE.StandardBasicInstrumentInfoVO>
        bordered
        column={2}
        columns={[
          {
            dataIndex: 'sampleName',
            title: '物资类别',
          },
          {
            dataIndex: 'sampleModel',
            title: '物资型号',
          },
          {
            dataIndex: 'sampleSpecifications',
            title: '物资规格',
          },
          {
            dataIndex: 'testCapabilityLevel',
            title: '检测能力级别',
          },
          {
            dataIndex: 'proName',
            title: '实验项目',
            render: (_, record) => {
              return record?.standardBasicProjectInstrumentList?.map((item: any) => item.projectName).join(',');
            },
          },
          {
            dataIndex: 'standardRequirement',
            title: '标准要求',
          },
          // {
          //   dataIndex: 'standardRequirement',
          //   title: '规则模型',
          // },
          {
            dataIndex: 'createTime',
            title: '创建时间',
            valueType: 'dateTime',
          },
          {
            dataIndex: 'updateTime',
            title: '修改时间',
            valueType: 'dateTime',
          },
          // 创建人
          {
            dataIndex: 'createUserFullName',
            title: '创建人',
          },
          // 修改人
          {
            dataIndex: 'updateUserFullName',
            title: '修改人',
          },
        ]}
        dataSource={row}
        labelStyle={{ width: 200, whiteSpace: 'nowrap' }}
        className="modal-pro-descriptions-item-wrap"
      />
    </Modal>
  );
};
export default SampleDetail;
