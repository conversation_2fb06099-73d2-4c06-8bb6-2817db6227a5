import { ModalForm, ModalFormProps } from '@ant-design/pro-components';
import { Form, message, Button, Input, Space, Divider, Table, Modal, Tooltip, Card } from 'antd';
import { useState, useEffect, useRef } from 'react';
import { PlusOutlined, DeleteOutlined, UploadOutlined, EditOutlined, PictureOutlined, FileTextOutlined } from '@ant-design/icons';
import { standardBasicInstrumentInfoUpdate } from '@/services/base/biaozhunjianceyiqibiaozhunxinxibiaojiekou';
import Upload, { BizUploadFile, UploadRef } from '@/components/upload';

type TemplateValuesEditProps = {
    row: any;
    templateType: 'report' | 'originalRecord';
    onFinish?: () => Promise<boolean | void>;
} & ModalFormProps;

const TemplateValuesEdit: React.FC<TemplateValuesEditProps> = ({
    row,
    templateType,
    onFinish,
    ...rest
}) => {
    const [form] = Form.useForm();
    const [templateValues, setTemplateValues] = useState<any[]>([]);

    // 图片上传组件引用
    const uploadRefs = useRef<Record<string, UploadRef>>({});

    const fieldName = templateType === 'report' ? 'reportTemplateValues' : 'originalRecordTemplateValues';
    const titleText = templateType === 'report' ? '报告模板自定义值' : '原始记录模板自定义值';

    useEffect(() => {
        if (row && row[fieldName]) {
            try {
                const values = JSON.parse(row[fieldName]);
                setTemplateValues(values);
            } catch (error) {
                setTemplateValues([]);
            }
        } else {
            setTemplateValues([]);
        }
    }, [row, fieldName]);

    const handleAddField = () => {
        setTemplateValues([...templateValues, { key: '', value: '', type: 'text' }]);
    };

    const handleRemoveField = (index: number) => {
        const newValues = [...templateValues];
        newValues.splice(index, 1);
        setTemplateValues(newValues);
    };

    const handleFieldChange = (index: number, field: string, value: any) => {
        const newValues = [...templateValues];
        newValues[index] = { ...newValues[index], [field]: value };
        setTemplateValues(newValues);
    };

    // 处理文件上传成功后的回调
    const handleFileSuccess = (index: number, fileInfo: any) => {
        if (fileInfo && fileInfo.data) {
            // 存储文件URL而不是base64数据
            const fileUrl = fileInfo.data.fileUrl; // 文件URL (相对路径)
            const fileId = fileInfo.data.id; // 文件ID

            // 创建一个新的值对象以立即更新UI
            const newValues = [...templateValues];
            newValues[index] = {
                ...newValues[index],
                type: 'image',
                value: fileUrl,
                fileId: fileId,
                fileName: fileInfo.data.fileName || ''
            };

            // 一次性更新所有值，避免多次更新导致的渲染问题
            setTemplateValues(newValues);
            message.success('图片上传成功');
        }
    };

    const handleSubmit = async (): Promise<boolean> => {
        const fieldValue = JSON.stringify(templateValues);

        const updateData = {
            id: row.id,
            [fieldName]: fieldValue
        };

        const res = await standardBasicInstrumentInfoUpdate(updateData);

        if (res?.success) {
            message.success('保存成功');
            if (onFinish) {
                await onFinish();
            }
            return true;
        } else {
            message.error(res?.message || '保存失败');
            return false;
        }
    };

    // 添加图片预览弹窗相关状态
    const [previewVisible, setPreviewVisible] = useState<boolean>(false);
    const [previewImage, setPreviewImage] = useState<string>('');

    // 图片预览处理函数
    const handlePreview = (imageUrl: string) => {
        // 确保URL以 /api 开头
        let fullUrl = imageUrl;
        if (!imageUrl.startsWith('/api') && !imageUrl.startsWith('http')) {
            fullUrl = `/api${imageUrl}`;
        }
        setPreviewImage(fullUrl);
        setPreviewVisible(true);
    };

    // 定义表格列
    const columns = [
        {
            title: '键名',
            dataIndex: 'key',
            key: 'key',
            width: '30%',
            render: (text: string, record: any, index: number) => (
                <Input
                    placeholder="变量名称 (例如: company_name, logo)"
                    value={text}
                    onChange={(e) => handleFieldChange(index, 'key', e.target.value)}
                />
            ),
        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            width: '15%',
            render: (text: string, record: any, index: number) => (
                <Space size="small" style={{ display: 'flex', justifyContent: 'center' }}>
                    <Button
                        type={text === 'text' ? 'primary' : 'default'}
                        size="small"
                        onClick={() => handleFieldChange(index, 'type', 'text')}
                        icon={<FileTextOutlined />}
                    >
                        文本
                    </Button>
                    <Button
                        type={text === 'image' ? 'primary' : 'default'}
                        size="small"
                        onClick={() => {
                            handleFieldChange(index, 'type', 'image');
                            // 自动触发上传图片点击
                            setTimeout(() => {
                                const uploadButton = document.querySelector(`#upload_button_${index}`) as HTMLButtonElement;
                                if (uploadButton) {
                                    uploadButton.click();
                                }
                            }, 100);
                        }}
                        icon={<PictureOutlined />}
                    >
                        图片
                    </Button>
                </Space>
            ),
        },
        {
            title: '值',
            dataIndex: 'value',
            key: 'value',
            width: '45%',
            render: (text: string, record: any, index: number) => {
                if (record.type === 'image') {
                    return (
                        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                            <Upload
                                ref={(ref) => {
                                    if (ref) {
                                        uploadRefs.current[`upload_${index}`] = ref;
                                    }
                                }}
                                onlyImg
                                maxCount={1}
                                onSuccess={(res) => {
                                    console.log('Upload success:', res);
                                    handleFileSuccess(index, res);
                                }}
                                value={record.value ? [record.value] : []}
                            >
                                <Button
                                    icon={<UploadOutlined />}
                                    id={`upload_button_${index}`}
                                >
                                    {text ? '更换图片' : '选择图片'}
                                </Button>
                            </Upload>
                            {text && (
                                <div
                                    style={{
                                        cursor: 'pointer',
                                        display: 'flex',
                                        alignItems: 'center',
                                        marginLeft: 8,
                                        marginTop: 8
                                    }}
                                    onClick={() => handlePreview(text)}
                                >
                                    <img
                                        src={text.startsWith('/api') || text.startsWith('http') ? text : `/api${text}`}
                                        alt="预览"
                                        style={{
                                            maxWidth: 60,
                                            maxHeight: 60,
                                            border: '1px solid #d9d9d9',
                                            padding: 2,
                                            borderRadius: 4,
                                            objectFit: 'contain'
                                        }}
                                    />
                                    <Tooltip title="点击查看大图">
                                        <Button
                                            type="link"
                                            size="small"
                                            icon={<PictureOutlined />}
                                            style={{ marginLeft: 4 }}
                                        >
                                            查看
                                        </Button>
                                    </Tooltip>
                                </div>
                            )}
                        </div>
                    );
                }
                return (
                    <Input.TextArea
                        placeholder="变量值"
                        value={text}
                        onChange={(e) => handleFieldChange(index, 'value', e.target.value)}
                        autoSize={{ minRows: 1, maxRows: 3 }}
                    />
                );
            },
        },
        {
            title: '操作',
            key: 'action',
            width: '10%',
            render: (text: string, record: any, index: number) => (
                <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveField(index)}
                />
            ),
        },
    ];

    return (
        <ModalForm
            title={`编辑${titleText}`}
            width={900}
            form={form}
            {...rest}
            onFinish={handleSubmit}
        >
            <p style={{ marginBottom: 16 }}>您可以添加自定义的键值对，在使用POI-TL生成报告时会自动替换。支持文本和图片类型。<strong>图片类型将上传并存储为文件URL，而不是base64编码。</strong></p>

            <Table
                dataSource={templateValues}
                columns={columns}
                rowKey={(record, index) => index?.toString() || '0'}
                pagination={false}
                size="small"
                bordered
                locale={{
                    emptyText: (
                        <div style={{ padding: '16px 0' }}>
                            <p>还没有添加任何自定义变量，点击下方"添加变量"按钮开始添加。</p>
                            <p>添加后可以选择文本或图片类型，并设置对应的值。图片将作为文件上传到系统，并自动生成访问链接。</p>
                        </div>
                    )
                }}
            />

            <Button
                type="dashed"
                onClick={handleAddField}
                icon={<PlusOutlined />}
                style={{ marginTop: 16 }}
            >
                添加变量
            </Button>

            {/* 图片预览弹窗 */}
            <Modal
                open={previewVisible}
                footer={null}
                onCancel={() => setPreviewVisible(false)}
                title="图片预览"
                width={800}
                bodyStyle={{ textAlign: 'center' }}
            >
                <img alt="预览" src={previewImage} style={{ maxWidth: '100%' }} />
            </Modal>
        </ModalForm>
    );
};

export default TemplateValuesEdit;
