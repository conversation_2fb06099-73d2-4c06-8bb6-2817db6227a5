import EditTable from '@/components/editTable';
import TemplateUpload from '@/components/templateUpload';
import {
  standardBasicInstrumentInfoAdd,
  standardBasicInstrumentInfoUpdate,
  standardBasicInstrumentInfoUploadReportTemplate,
  standardBasicInstrumentInfoUploadOriginalRecordTemplate,
} from '@/services/base/biaozhunjianceyiqibiaozhunxinxibiaojiekou';
import { standardBasicProjectInstrumentVoPage } from '@/services/base/biaozhunshiyanxiangmujianceyiqixinxibiaojiekou';
import { buSampleBaseInfoVoPage } from '@/services/base/yangpinjibenxinxiguanlianbiaojiekou';
import { arr2ValueEnum } from '@/utils';
import { ModalForm, ModalFormProps, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Form, message, Row, Col, Divider } from 'antd';
import { parse } from 'mathjs';
import { useEffect, useState } from 'react';
type SampleEditProps = {
  row?: BASE.StandardBasicInstrumentInfoVO;
  onFinish?: () => void;
} & ModalFormProps;

const SampleEdit: React.FC<SampleEditProps> = ({ row, onFinish, ...rest }) => {
  const [form] = Form.useForm();
  const [editTableForm] = Form.useForm();
  const [dataSource, setDataSource] = useState<any[]>(row?.standardBasicModelParamList || []);
  const [reportFileList, setReportFileList] = useState<any[]>([]);
  const [originalRecordFileList, setOriginalRecordFileList] = useState<any[]>([]);

  // 检查并初始化模板文件列表
  useEffect(() => {
    if (row?.id) {
      // 检查报告模板文件
      if (row.reportTemplateFileId && row.reportTemplateFile) {
        setReportFileList([
          {
            uid: `-${row.reportTemplateFileId}`,
            name: row.reportTemplateFile.fileName || '报告模板文件',
            status: 'done',
            url: `/api/test/file/download/${row.reportTemplateFileId}`,
            response: row.reportTemplateFile
          }
        ]);
      } else {
        setReportFileList([]);
      }

      // 检查原始记录模板文件
      if (row.originalRecordTemplateFileId && row.originalRecordTemplateFile) {
        setOriginalRecordFileList([
          {
            uid: `-${row.originalRecordTemplateFileId}`,
            name: row.originalRecordTemplateFile.fileName || '原始记录模板文件',
            status: 'done',
            url: `/api/test/file/download/${row.originalRecordTemplateFileId}`,
            response: row.originalRecordTemplateFile
          }
        ]);
      } else {
        setOriginalRecordFileList([]);
      }
    }
  }, [row]);

  // 处理报告模板文件上传成功
  const handleReportTemplateSuccess = (file: any) => {
    if (file) {
      setReportFileList([
        {
          uid: `-${file.id}`,
          name: file.fileName || '报告模板文件',
          status: 'done',
          url: `/api/test/file/download/${file.id}`,
          response: file
        }
      ]);
    }
  };

  // 处理原始记录模板文件上传成功
  const handleOriginalRecordTemplateSuccess = (file: any) => {
    if (file) {
      setOriginalRecordFileList([
        {
          uid: `-${file.id}`,
          name: file.fileName || '原始记录模板文件',
          status: 'done',
          url: `/api/test/file/download/${file.id}`,
          response: file
        }
      ]);
    }
  };

  return (
    <ModalForm
      title={row ? '编辑' : '新增'}
      width={1000}
      form={form}
      initialValues={row}
      {...rest}
      onFinish={async (values: any) => {
        // 检测table是否报错
        try {
          await editTableForm.validateFields();
        } catch (error: any) {
          // message.error(error);
          return;
        }

        let res = null;
        let arr = dataSource.map((item: any) => ({
          ...item,
          id: undefined as unknown as number,
        }));
        if (row) {
          res = await standardBasicInstrumentInfoUpdate({
            ...values,
            id: row?.id,
            standardBasicModelParams: arr,
          });
        } else {
          res = await standardBasicInstrumentInfoAdd({
            ...values,
            id: undefined as unknown as number,
            standardBasicModelParams: arr,
          });
        }
        if (res?.success) {
          message.success(res?.message);
          rest?.modalProps?.onCancel?.(values);
          onFinish?.();
        }
      }}
    >
      <ProFormSelect
        label={'物资类别'}
        name={'sampleName'}
        rules={[{ required: true, message: '请选择物资类别' }]}
        fieldProps={{
          style: { width: '100%' },
          placeholder: '请选择',
          fieldNames: {
            label: 'name',
            value: 'name',
          },
          onChange: (value: any, option: any) => {
            form.setFieldsValue({
              sampleName: option?.name,
              sampleSpecifications: undefined,
              sampleModel: undefined,
            });
          },
          getPopupContainer: (e) => e,
        }}
        request={async () => {
          const res = await buSampleBaseInfoVoPage({ page: 1, size: 2000 });
          // 筛选出物资类别重复的
          const unique = res?.data?.records?.filter(
            (item: any, index: number, self: any) =>
              index === self.findIndex((t: any) => t.name === item.name),
          );

          return unique || [];
        }}
      />
      <ProFormSelect
        label={'物资型号'}
        name={'sampleModel'}
        rules={[{ required: true, message: '请选择物资型号' }]}
        fieldProps={{
          style: { width: '100%' },
          placeholder: '请选择',
          fieldNames: {
            label: 'type',
            value: 'id',
          },
          labelInValue: true,
          showSearch: true,
          optionFilterProp: 'label',
          optionLabelProp: 'label',
          // 搜索不调用接口
          fetchDataOnSearch: false,
          onChange: (value: any) => {
            form.setFieldsValue({
              sampleModel: value?.label,
              sampleSpecifications: undefined,
            });
          },
          getPopupContainer: (e) => e,
        }}
        transform={(value) => {
          if (value?.label) {
            return {
              sampleModel: value?.label,
            };
          }
          return { sampleModel: value };
        }}
        dependencies={['sampleName']}
        request={async () => {
          const res = await buSampleBaseInfoVoPage({
            page: 1,
            size: 2000,
            name: form.getFieldValue('sampleName') ?? row?.sampleName,
          });
          // 去重
          const unique = res?.data?.records?.filter(
            (item: any, index: number, self: any) =>
              index === self.findIndex((t: any) => t.type === item.type),
          );
          return unique || [];
        }}
      />
      <ProFormSelect
        label={'物资规格'}
        name={'sampleSpecifications'}
        rules={[{ required: true, message: '请输入物资规格' }]}
        fieldProps={{
          style: { width: '100%' },
          placeholder: '请选择',
          fieldNames: {
            label: 'model',
            value: 'id',
          },
          labelInValue: true,
          showSearch: true,
          optionFilterProp: 'label',
          optionLabelProp: 'label',
          // 搜索不调用接口
          fetchDataOnSearch: false,
          getPopupContainer: (e) => e,
        }}
        transform={(value) => {
          if (value?.label) {
            return {
              sampleSpecifications: value?.label,
            };
          }
          return { sampleSpecifications: value };
        }}
        dependencies={['sampleName', 'sampleModel']}
        request={async () => {
          const res = await buSampleBaseInfoVoPage({
            page: 1,
            size: 2000,
            name: form.getFieldValue('sampleName') ?? row?.sampleName,
          });
          // 过滤掉sampleModel
          const list = res?.data?.records || [];
          const sampleModel = form.getFieldValue('sampleModel') ?? row?.sampleModel;
          if (sampleModel) {
            return list?.filter((item: any) => item.type === sampleModel) || [];
          }
          return list;
        }}
      />
      <ProFormSelect
        label={'检测能力级别'}
        name={'testCapabilityLevel'}
        valueEnum={arr2ValueEnum(['A', 'B', 'C'])}
        rules={[{ required: true, message: '请选择检测能力级别' }]}
        fieldProps={{
          getPopupContainer: (e) => e,
        }}
      />
      {/* 实验项目 */}
      <ProFormSelect
        label={'实验项目'}
        name={'projectIds'}
        rules={[{ required: true, message: '请输入实验项目' }]}
        fieldProps={{
          mode: 'multiple',
          style: { width: '100%' },
          placeholder: '请选择',
          fieldNames: {
            label: 'projectName',
            value: 'id',
          },
          getPopupContainer: (e) => e,
          labelInValue: true,
          onChange: (value: any) => {
            let arr: any[] = [];
            let obj: any = {};
            row?.standardBasicModelParamList?.forEach((item: any) => {
              obj[item.standardBasicProjectParamId] = item;
            });
            value.map((item: any) => {
              let items = item.projectParams.map((item: any, index: number) => ({
                // 生成唯一id
                id: item.id,
                testItem: item.paramName,
                judgeType: item.paramType,
                standardBasicProjectParamId: item.id,
                unit: item.unit ?? obj[item.id]?.unit,
                paramKey: obj[item.id]?.paramKey,
                qualifiedStandard: obj[item.id]?.qualifiedStandard,
                judgeFormula: obj[item.id]?.judgeFormula,
              }));
              arr = arr.concat(items);
              return null;
            });
            setDataSource(arr);
          },
        }}
        transform={(value) => {
          return {
            projectIds: value.map((item: any) => item.id ?? item),
          };
        }}
        request={async () => {
          const res = await standardBasicProjectInstrumentVoPage({ page: 1, size: 2000 });
          return res?.data?.records || [];
        }}
      />

      <ProFormText
        label={'标准要求'}
        name={'standardRequirement'}
        rules={[{ required: true, message: '请输入标准要求' }]}
      />

      {/* 上传模板文件区域 */}
      <Divider orientation="left">模板文件</Divider>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="报告模板文件"
            name="reportTemplateFile"
            extra="支持格式：docx、doc（仅在编辑模式下可上传）"
          >
            <TemplateUpload
              disabled={!row?.id}
              standardId={row?.id}
              fileList={reportFileList}
              uploadAction={standardBasicInstrumentInfoUploadReportTemplate}
              buttonText="上传报告模板"
              onSuccess={handleReportTemplateSuccess}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="原始记录模板文件"
            name="originalRecordTemplateFile"
            extra="支持格式：docx、doc（仅在编辑模式下可上传）"
          >
            <TemplateUpload
              disabled={!row?.id}
              standardId={row?.id}
              fileList={originalRecordFileList}
              uploadAction={standardBasicInstrumentInfoUploadOriginalRecordTemplate}
              buttonText="上传原始记录模板"
              onSuccess={handleOriginalRecordTemplateSuccess}
            />
          </Form.Item>
        </Col>
      </Row>

      {/* 规则模型 */}
      <Divider orientation="left">规则模型</Divider>
      <EditTable
        columns={[
          // 检项,检项参数,单位,判别类型,合格标准,判定公式
          {
            dataIndex: 'testItem',
            title: '检项',
            editable: false,
          },
          {
            dataIndex: 'paramKey',
            title: '检项参数',
            valueType: 'text',
            formItemProps: {
              rules: [{ required: true, message: '请输入检项参数' }],
            },
          },
          {
            dataIndex: 'unit',
            title: '单位',
            valueType: 'text',
            formItemProps: {
              rules: [{ required: true, message: '请输入单位' }],
            },
          },
          {
            dataIndex: 'judgeType',
            title: '判别类型',
            valueType: 'select',
            editable: false,
            width: 'auto',
            valueEnum: arr2ValueEnum([
              '比较型',
              '计算比较依据型',
              '计算比较结果型',
              '不做判定型',
              '人工判定型',
              '其他型',
            ]),
          },
          {
            dataIndex: 'qualifiedStandard',
            title: '合格标准',
            valueType: 'textarea',
            formItemProps: {
              rules: [{ required: true, message: '请输入合格标准' }],
            },
          },
          {
            dataIndex: 'judgeFormula',
            title: '判定公式',
            valueType: 'textarea',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '请输入判定公式',
                },
                {
                  required: false,
                  validator: (rule, value, callback) => {
                    if (!value) {
                      callback();
                      return;
                    }

                    // 允许的操作符（注意 math.js 中的符号用对应写法）
                    const allowedOps = [
                      '+',
                      '-',
                      '*',
                      '/',
                      '(',
                      ')',
                      '<',
                      '<=',
                      '>',
                      '>=',
                      '=',
                      '∪',
                      '∩',
                    ];

                    let paramKeys: any[] = [];
                    dataSource.forEach((item: any) => {
                      paramKeys.push(item.paramKey);
                    });

                    try {
                      // 检查括号前后的乘号
                      const formula = value.toString().replace(/\s+/g, '');
                      const parenthesesPattern = /[a-zA-Z0-9]\(|\)[a-zA-Z0-9]/g;
                      const missingMultSigns = formula.match(parenthesesPattern);

                      if (missingMultSigns) {
                        callback('表达式语法错误');
                        return;
                      }

                      const node = parse(value);

                      // 提取所有变量（SymbolNode）和操作符
                      const usedVars: any[] = [];
                      let hasInvalidOp = false;
                      let invalidOp = '';

                      node.traverse(function (node: any) {
                        if (node.isSymbolNode) {
                          usedVars.push(node.name);
                        }
                        // 检查操作符

                        if (node.isOperatorNode) {
                          const op = node.op;
                          if (!allowedOps.includes(op)) {
                            hasInvalidOp = true;
                            invalidOp = op;
                          }
                        }
                        if (
                          node.isArrayNode ||
                          node.isAccessorNode ||
                          node.isFunctionNode ||
                          node.isBlockNode
                        ) {
                          callback(`包含非法操作符`)
                        }
                      });

                      if (hasInvalidOp) {
                        callback('包含非法操作符:' + invalidOp);
                        return;
                      }

                      // 检查是否只包含允许的变量
                      const illegalVars = usedVars.filter((v) => !paramKeys.includes(v));
                      if (illegalVars.length > 0) {
                        callback('包含非法变量:' + illegalVars.join(', '));
                      } else {
                        callback();
                      }
                    } catch (err: any) {
                      callback('表达式语法错误');
                    }
                  },
                },
              ],
            },
          },
          {
            dataIndex: 'action',
            title: ' ',
            valueType: 'option',
            width: '0',
          },
        ]}
        editable={{
          type: 'multiple',
          form: editTableForm,
          editableKeys: dataSource.map((item: any) => item.id),
          actionRender: (row, _, dom) => {
            return [];
          },
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
          },
        }}
        rowKey={(record) => record.id}
        value={dataSource}
        pagination={false}
        recordCreatorProps={false}
      />
    </ModalForm>
  );
};

export default SampleEdit;
