import { instrumentNewInfoGet } from '@/services/base/yiqiyibia<PERSON><PERSON><PERSON><PERSON>';
import { ProDescriptions, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Modal, Tag } from 'antd';
import React from 'react';

const Detail: React.FC<{ row: Record<string,any> }> = ({ row, ...rest }) => {
  // const detailRequest = useRequest(() => {
  //   return instrumentNewInfoGet({
  //     id,
  //   });
  // });

  return (
    <Modal {...rest} width={'80%'}>
      <ProDescriptions<any>
        title="基本信息"
        dataSource={row}
        column={2}
        bordered
        columns={[
          {
            title: '工位编码',
            dataIndex: 'workstationCode',
          },
          {
            title: '工位名称',
            dataIndex: 'workstationName',
          },
          {
            title: '工位状态',
            dataIndex: 'status',
          },
          {
            title: '工位位置',
            dataIndex: 'location',
          },
          {
            title: '负责人',
            dataIndex: 'responsiblePerson',
          },
          {
            title: '工位描述',
            dataIndex: 'description',
          },
        ]}
      />
    </Modal>
  );
};

export default Detail;
