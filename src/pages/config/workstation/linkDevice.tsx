import ProFormSelect from '@/components/proFormSelect';
import { instrumentNewInfoVoPage } from '@/services/base/yiqiyibiaojiekou';
import { ProForm } from '@ant-design/pro-components';
import { Button, Form, Modal } from 'antd';
import { useEffect } from 'react';

function LinkDevice({ row, ...rest }: any) {
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      equipmentIds: row?.instrumentNewInfos?.map((item: any) => item?.id),
    });
  }, [row])

  return (
    <Modal
      title="关联设备"
      {...rest}
      onOk={() => {
        rest?.onFinish?.(form.getFieldsValue(true));
        rest?.onOk?.();
      }}
    >
      <ProForm form={form} style={{ marginTop: 10 }} submitter={false}>
        <ProFormSelect
          label="关联设备"
          name="equipmentIds"
          mode="multiple"
          fieldProps={{
            fieldNames: {
              label: 'sbmc',
              value: 'id',
            },
            // 搜索不调用request
            showSearch: true,
            fetchDataOnSearch: false,
            filterOption: (input: string, option: any) => {
              return (
                option?.sbmc?.indexOf(input.toLowerCase()) >= 0 ||
                option?.sbbh?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              );
            },
          }}
          request={async () => {
            const res = await instrumentNewInfoVoPage({
              page: 1,
              size: 1999,
            });
            const list = res.data?.records || [];
            return list;
          }}
        />
        {/* {
          row?.instrumentNewInfo?.sbmc ? <a style={{ color: 'red' }}
          onClick={() => {

          }}>解绑</a> : null
        } */}
      </ProForm>
    </Modal>
  );
}

export default LinkDevice;
