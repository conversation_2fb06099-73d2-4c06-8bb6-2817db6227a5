import { buInstrumentWorkstationInfoUpdate } from '@/services/base/gongweixinxiguanlijiekou';
import { buInstrumentWorkstationInfoAdd } from '@/services/base/gongweixinxiguanlijiekou';
import {
  ModalForm,
  ModalFormProps,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';

type EditProps = {
  isEdit?: boolean;
  onSuccess?: () => void;
  formatParams?: (values: Record<string, any>) => Record<string, any>;
} & ModalFormProps;

const Edit: React.FC<EditProps> = (props: EditProps) => {
  const { isEdit, onSuccess, formatParams, ...rest } = props;

  const onFinish = async (values: any) => {
    const submitValues = {
      ...values,
      sampleSerialNumber: values?.sampleSerialNumber,
    };
    const res = isEdit
      ? await buInstrumentWorkstationInfoUpdate({ ...submitValues, id: props?.initialValues?.id })
      : await buInstrumentWorkstationInfoAdd(formatParams ? formatParams?.(submitValues) : submitValues);
    if (res?.success) {
      message.success(res?.message);
      props?.modalProps?.onCancel?.(submitValues);
      onSuccess?.();
    }
  };
  return (
    <ModalForm
      title={isEdit ? '编辑' : '新增'}
      width="800px"
      grid
      colProps={{ span: 12 }}
      {...rest}
      onFinish={onFinish}
    >
      <ProFormText
        label={'工位编码'}
        name={'workstationCode'}
        formItemProps={{ rules: [{ required: true }] }}
      />
      <ProFormText label={'工位名称'} name={'workstationName'} />

      <ProFormText label={'工位位置'} name={'location'} />

      <ProFormText label={'负责人'} name={'responsiblePerson'} />
      <ProFormText label={'工位描述'} name={'description'} />
      
    </ModalForm>
  );
};
export default Edit;
