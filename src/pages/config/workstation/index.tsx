import ProTable, { ProTableProps } from '@/components/proTable';
import {
  buInstrumentWorkstationInfoSaveEntityWithCamera,
  buInstrumentWorkstationInfoSaveEntityWithDevice,
  buInstrumentWorkstationInfoUpdate,
} from '@/services/base/gongweixinxiguanlijiekou';
import { arr2ValueEnum } from '@/utils';
import { ActionType, PageContainer } from '@ant-design/pro-components';
import { Access, useAccess } from '@umijs/max';
import { Button } from 'antd';
import { launch, launchSchema } from 'antd-pro-crud';
import { useRef } from 'react';
import Detail from './detail';
import Edit from './edit';
import LinkDevice from './linkDevice';
import MonitorDevice from './monitorDevice';

const alert_edit = launchSchema(Edit);

const alert_detail1 = launch(Detail);
const alert_linkDevice = launch(LinkDevice);
const alert_monitorDevice = launch(MonitorDevice);

const Page: React.FC = () => {
  const { commonAccess } = useAccess();
  const actionRef = useRef<ActionType>();

  const columns: ProTableProps<BASE.BuInstrumentWorkstationInfoVO>['columns'] = [
    {
      title: '序号',
      valueType: 'index',
      hideInDescriptions: true,
      width: 80,
    },
    {
      dataIndex: 'workstationCode',
      title: '工位编码',
      ellipsis: true,
    },
    {
      dataIndex: 'workstationName',
      title: '工位名称',
      ellipsis: true,
    },
    {
      dataIndex: 'location',
      title: '工位位置',
      ellipsis: true,
    },
    {
      dataIndex: 'status',
      title: '工位状态',
      valueType: 'select',
      valueEnum: arr2ValueEnum(['空闲', '使用中', '维护中']),
    },
    {
      dataIndex: ['instrumentNewInfo', 'sbmc'],
      title: '关联设备',
      width: 200,
      ellipsis: true,
      hideInForm: true,
      fieldProps: {
        style: { width: '100%' },
      },
      hideInSearch: true,
      render: (_, record) => {
        return record?.instrumentNewInfos?.map((item: any) => item?.sbmc).join(',');
      },
    },
    {
      dataIndex: ['buInstrumentCameraInfo', 'deviceName'],
      title: '关联监控设备',
      hiddenInAdd: true,
      ellipsis: true,
      hideInForm: true,
      hideInSearch: true,
    },
    {
      valueType: 'option',
      title: '操作',
      fixed: 'right',
      width: 280,
      render(dom, entity, index, action, schema) {
        return (
          <>
            {/* 编辑 */}
            <Access accessible={commonAccess('admin:/device/workstation@edit')}>  
              <a
                onClick={() =>
                  alert_edit({
                    isEdit: true,
                    initialValues: entity,
                    onSuccess: () => action?.reload(),
                  })
                }
              >
                编辑
              </a>
            </Access>
            {/* 详情 */}
            <Access accessible={commonAccess('admin:/device/workstation@detail')}>
              <a
                onClick={() =>
                  alert_detail1({
                    row: entity,
                  })
                }
              >
                详情
              </a>
            </Access>
            <Access accessible={commonAccess('admin:/device/workstation@linkDevice')}>
              <a
                onClick={() =>
                  alert_linkDevice({
                    row: entity,
                    onFinish: async (values: any) => {
                      if (values.equipmentIds) {
                        await buInstrumentWorkstationInfoSaveEntityWithDevice({
                          id: entity.id,
                          equipmentIds: values.equipmentIds,
                          operationType: '关联',
                        });
                      }
                      actionRef?.current?.reload();
                    },
                    width: 460,
                  })
                }
              >
                关联设备
              </a>
            </Access>
            <Access accessible={commonAccess('admin:/device/workstation@monitorDevice')}>
              <a
                onClick={() =>
                  alert_monitorDevice({
                    row: entity,
                    onFinish: async (values: any) => {
                      if (values.cameraId) {
                        await buInstrumentWorkstationInfoSaveEntityWithCamera({
                          id: entity.id,
                          cameraId: values.cameraId,
                          operationType: '关联',
                        });
                      }
                      actionRef?.current?.reload();
                    },
                    width: 460,
                  })
                }
              >
                关联监控设备
              </a>
            </Access>
          </>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.BuInstrumentWorkstationInfoVO>
        crudKey="buInstrumentWorkstationInfo"
        columns={columns}
        search={{
          labelWidth: 120,
        }}
        actionRef={actionRef}
        scroll={{ x: 'fit-content', y: 500 }}
        toolBarRender={(action: any) => [
          <Access key="add" accessible={commonAccess('admin:/device/workstation@add')}>
            <Button
              type="primary"
              key={'add'}
              onClick={() =>
                alert_edit({
                  onSuccess: () => action?.reload(),
                })
              }
            >
              新增
            </Button>
          </Access>,
        ]}
        crud={{
          delete: {
            visible: (row) => {
              return commonAccess('admin:/device/workstation@delete');
            },
          },
          edit: {
            visible: (row) => {
              return commonAccess('admin:/device/workstation@edit');
            },
          },
        }}
        hiddenBtns={['add', 'detail', 'edit']}
      />
    </PageContainer>
  );
};

export default Page;
