import ProFormSelect from "@/components/proFormSelect";
import { ModalForm, ProForm } from "@ant-design/pro-components";
import { buInstrumentCameraInfoVoPage } from "@/services/base/haikangweishishexiangtouguanlijiekou";
import { Form, Modal } from "antd";
import { useEffect } from 'react';

function MonitorDevice({ row, ...rest }: any) {
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      cameraId: row?.buInstrumentCameraInfo?.deviceName,
    });
  }, [row])

  return <Modal
    title="关联监控设备"
    {...rest}
    onOk={async (values: any) => {
      rest?.onFinish?.(form.getFieldsValue(true));
      rest?.onOk?.();
    }}
  >
    <ProForm form={form} style={{ marginTop: 10 }} submitter={false}>
      <ProFormSelect
        label="关联监控设备"
        name="cameraId"
        fieldProps={{
          fieldNames: {
            label: 'deviceName',
            value: 'id',
          },
          // 搜索不调用request
          showSearch: true,
          fetchDataOnSearch: false,
          filterOption: (input: string, option: any) => {
            return (
              option?.deviceName?.indexOf(input.toLowerCase()) >= 0 ||
              option?.deviceCode?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            );
          },
        }}
        request={async () => {
          let res = await buInstrumentCameraInfoVoPage({
            page: 1,
            size: 1000,
          });
          const list = res.data?.records || [];
          console.log(list, 'list');
          return list;
        }}
      />
    </ProForm>
  </Modal>;
}

export default MonitorDevice;

