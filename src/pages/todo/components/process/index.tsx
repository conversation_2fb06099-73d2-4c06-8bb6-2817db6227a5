import Title from '@/components/title';
import { genUUID } from '@/components/verify/util';
import { ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Image, Modal, ModalProps, Tabs, TabsProps } from 'antd';
import dayjs from 'dayjs';

type ProcessItemProps = {
  // 流程id
  processId: string;
  /**业务的id，例如委托书就是委托书的ID，报告就是报告的ID */
  busId: string;
  // 流程名称
  processName?: string;
};

type ProcessProps = {
  process: Partial<ProcessItemProps>[];
} & ModalProps;

const ProcessItem = (props: ProcessItemProps) => {
  const { processId, busId, processName } = props;

  // 流程图请求接口
  const imgRequest = useRequest(
    () => {
      // return flowTaskInfoDiagram(
      //   { processId },
      //   {
      //     responseType: 'blob',
      //   },
      // );
    },
    {
      formatResult(res) {
        return URL.createObjectURL(res);
      },
    },
  );

  // 流程历史的接口
  const tableRequest = useRequest(
    () => {
    },
  );

  return (
    <>
      <Image src={imgRequest?.data} preview={false} />

      <Title title="流转历史" />
      <ProTable<Record<string, any>>
        options={false}
        rowKey="id"
        search={false}
        loading={tableRequest?.loading}
        pagination={false}
        dataSource={tableRequest?.data || []}
        columns={[
          {
            dataIndex: 'taskName',
            title: '任务名称',
          },
          {
            dataIndex: 'assigneeName',
            title: '处理人',
          },
          {
            dataIndex: 'createTime',
            title: '开始时间',
            valueType: 'dateTime',
            render(dom, entity, index, action, schema) {
              return entity?.createTime && dayjs(entity?.createTime).format('YYYY-MM-DD HH:mm');
            },
          },
          {
            dataIndex: 'finishTime',
            title: '结束时间',
            valueType: 'dateTime',
            render(dom, entity, index, action, schema) {
              return entity?.finishTime && dayjs(entity?.finishTime).format('YYYY-MM-DD HH:mm');
            },
          },
          {
            title: '处理时长',
            render(dom, entity, index, action, schema) {
              // 使用dayjs计算时间差
              const start = dayjs(entity.createTime);
              const end = dayjs(entity.finishTime || new Date());
              const duration = dayjs.duration(end.diff(start));
              // 这里优化下，如果分钟小于60，就不显示小时了，如果小时小于24，就不显示天了
              const days = duration.days();
              const hours = duration.hours();
              const minutes = duration.minutes();
              const seconds = duration.seconds();
              return (
                <>
                  {days > 0 && `${days}天`}
                  {hours > 0 && `${hours}小时`}
                  {minutes > 0 && `${minutes}分钟`}
                  {seconds > 0 && `${seconds}秒`}
                </>
              );
            },
          },
          {
            title: '备注',
            render(dom, entity, index, action, schema) {
              return entity?.commentList?.map((i: any) => (
                <div key={i?.time}>{`${i?.message}`}</div>
              ));
            },
          },
        ]}
      />
    </>
  );
};

const Process: React.FC<ProcessProps> = (props) => {
  const { process, ...rest } = props;

  // 所有的流转历史
  // const tabList = useRequest(()=>{
  //   return taskDeptRecordInfoGetRecordHistoricalProcess({
  //     id:busId
  //   })
  // },{
  //   onSuccess(data,params){
  //     console.log(data,'dat111')
  //     imgRequest?.run(data?.[0]?.flowId)
  //     tableRequest?.run(data?.[0]?.flowId)
  //   }
  // })

  const _items: TabsProps['items'] = process
    ?.filter((i) => !!i.processId)
    .map((i) => ({
      key: i.processId,
      label: i.processName || '流程图',
      children: <ProcessItem {...i} />,
    }));

  return (
    <Modal
      title="流程图"
      width={1000}
      {...rest}
      footer={
        <Button type="primary" onClick={(e: any) => rest?.onCancel?.(e as any)}>
          确定
        </Button>
      }
    >
      <Tabs defaultActiveKey="1" items={_items} />
    </Modal>
  );
};

export default Process;
