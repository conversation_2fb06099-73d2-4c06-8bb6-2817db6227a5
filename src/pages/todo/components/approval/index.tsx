import Title from '@/components/title';
import { BoolObj } from '@/enums';
import {
  ModalForm,
  ModalFormProps,
  ProFormDependency,
  ProFormRadio,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { alert_loading } from 'antd-pro-crud';

type ApprovalProps = {
  // 需要审核的任务id
  flowId: string;
  beforSubmit?: (data: Record<string, any>) => Record<string, any>;
  beforOpen?: (data: Record<string, any>) => Record<string, any>;
  // 成功后的回调
  onSuccess?: () => void;
  // 详情页的节点
  detailNode?: React.ReactNode;
  extraProFormItems?: React.ReactNode;
  /** 提交的接口 */
  submitInterface?: (body: any, options?: { [key: string]: any }) => Promise<BASE.IResult>;
} & ModalFormProps;

const Approval = (props: ApprovalProps) => {
  const {
    beforOpen,
    beforSubmit,
    flowId,
    onSuccess,
    detailNode,
    extraProFormItems,
    submitInterface = () => Promise.resolve({ success: true, message: '审核成功' }),
    ...rest
  } = props;
  const onFinish = async (values: Record<string, any>) => {
    const { close } = alert_loading({
      maskClosable: false,
    });
    try {
      const _submitValus = beforSubmit
        ? beforSubmit({ ...values, taskId: flowId })
        : { ...values, taskId: flowId };
      const res = await submitInterface(_submitValus);
      close();
      if (res.success) {
        message.success('审核成功');
        if (onSuccess) onSuccess?.();
        props?.modalProps?.onCancel?.({} as any);
        return true;
      }
    } catch (error) {
      close();
    }
  };
  return (
    <ModalForm {...rest} onFinish={onFinish}>
      {detailNode}
      {detailNode && <Title title="审核信息" />}
      <ProFormRadio.Group
        name="isPass"
        label="是否通过"
        formItemProps={{ rules: [{ required: true }] }}
        valueEnum={BoolObj}
      />
      {extraProFormItems}
      <ProFormDependency name={['isPass']}>
        {({ isPass }) => {
          return (
            <ProFormTextArea
              name="comment"
              label="备注"
              placeholder="请输入备注"
              formItemProps={{
                rules: [
                  {
                    required: !isPass,
                  },
                ],
              }}
            />
          );
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default Approval;
