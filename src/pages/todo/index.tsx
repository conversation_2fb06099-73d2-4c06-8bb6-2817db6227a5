import ProTable, { ProTableProps } from '@/components/proTable';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { launch, launchSchema } from 'antd-pro-crud';
import Approval from './components/approval';
import Process from './components/process';

const alert_approval = launchSchema(Approval);
const alert_process = launch(Process);

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  const columns: ProTableProps<BASE.MyTaskVo>['columns'] = [
    
    {
      dataIndex: 'taskName',
      title: '任务名称',
    },
    {
      dataIndex: 'deptName',
      title: '部门名称',
      hideInSearch: true,
    },
    {
      dataIndex: 'startUserName',
      title: '流程发起人',
      hideInSearch: true,
    },
    {
      dataIndex: 'category',
      title: '流程类型',
      hideInSearch: true,
    },
    {
      valueType: 'option',
      title: '操作',
      render(dom, entity, index, action, schema) {
        return (
          <>
            <a
              onClick={() =>
                alert_approval({
                  title: '审核',
                  flowId: entity.taskId!,
                })
              }
            >
              审核
            </a>
            <a
              onClick={() =>
                alert_process({
                  process:[{
                    processId: entity.procInsId!,
                    busId:''
                  }]
                })
              }
            >
              查看流程
            </a>
          </>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.MyTaskVo>
        crudKey="flowTaskInfo"
        rowKey={'taskId'}
        hiddenBtns={['add', 'edit', 'delete']}
        crud={{
          list: {
            api: `/flowTaskInfo/todo?userId=${initialState?.currentUser?.id}`,
            formatResult: (res) => {
              return {
                data: res.data?.records || [],
                success: res?.success,
                total: res.data?.length || 0,
              };
            },
          },
        }}
        columns={columns}
      />
    </PageContainer>
  );
};

export default Page;
