import FileDownload from '@/components/fileDownload';
import ProTable, { ProTableProps } from '@/components/proTable';
import { arr2ValueEnum } from '@/utils';
import { PageContainer } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { useAccess } from '@umijs/max';
function ReportNew() {
  const { commonAccess } = useAccess();
  const columns: ProTableProps<BASE.JzReportInfoVO>['columns'] = [
    // 报告编号、样品名称、检测级别、任务编号、是否合格、原始记录（一个文件）、报告（一个文件）、生成时间
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      width: 80,
    },
    {
      title: '报告编号',
      dataIndex: 'reportNumber',
      ellipsis: true,
    },
    {
      title: '物资类别',
      dataIndex: 'sampleName',
      ellipsis: true,
    },
    {
      title: '检测级别',
      dataIndex: 'testLevel',
      width: 100,
      ellipsis: true,
      valueEnum: arr2ValueEnum(['A', 'B', 'C']),
    },
    {
      title: '任务编号',
      dataIndex: 'taskNumber',
      ellipsis: true,
    },
    {
      title: '是否合格',
      dataIndex: 'tfQualified',
      width: 100,
      ellipsis: true,
      valueEnum: arr2ValueEnum(['合格', '不合格']),
    },
    {
      title: '原始记录',
      dataIndex: ['originalRecordFileInfo', 'fileName'],
      ellipsis: true,
      hideInSearch: true,
      render: (_, record) => {
        return (
          <FileDownload
            item={record?.originalRecordFileInfo}
            preview={false}
            isDowmload={true}
            key={record?.originalRecordFileInfo?.id}
          />
        );
      },
    },
    {
      title: '实验报告',
      dataIndex: ['reportFileInfo', 'fileName'],
      ellipsis: true,
      hideInSearch: true,
      render: (_, record) => {
        return (
          <FileDownload
            item={record?.reportFileInfo}
            preview={false}
            isDowmload={true}
            key={record?.reportFileInfo?.id}
          />
        );
      },
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      ellipsis: true,
      hideInSearch: true,
      valueType: 'dateTime',
      render(dom, entity, index, action, schema) {
        return <span>{dayjs(entity.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    // 操作
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => {
        return <></>;
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<BASE.JzReportInfoVO>
        crudKey="jzReportInfo"
        hiddenBtns={['add', 'edit', 'delete']}
        scroll={{
          scrollToFirstRowOnChange: true,
          x: 'max-content',
          y: 500,
        }}
        pagination={{
          pageSizeOptions: [10, 15, 20, 50, 100, 1000],
        }}
        toolBarRender={() => {
          return [];
        }}
        crud={{
          detail: {
            visible: (row) => commonAccess('admin:/report@detail'),
          },
        }}
        columns={columns}
      />
    </PageContainer>
  );
}

export default ReportNew;
