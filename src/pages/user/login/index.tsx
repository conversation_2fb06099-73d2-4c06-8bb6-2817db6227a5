import { VerifySlideModal, VerifySlideModalRef } from '@/components/verify/modal';
import { login as apiLogin, loginPublicKey } from '@/services/base/authLogin';
import { encrypt } from '@/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { history, useModel, useRequest, useSearchParams } from '@umijs/max';
import { Button, Checkbox, Form, Input, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import styles from './index.less';
import {event} from '../layout'

type FormQuery = {
  username: string;
  password: string;
  dynamicPassword: string;
};

const Page: React.FC = () => {
  const [form] = Form.useForm();
  const verify = useRef<VerifySlideModalRef>(null);
  const { initialState, setInitialState, refresh } = useModel('@@initialState');

  const [searchParams, setSearchParams] = useSearchParams();
  const token = searchParams.get('token'); // b

  const getInfo = async (token: string) => {
    // 缓存token
    localStorage.setItem('token', token);

    const userInfo = await initialState?.fetchUserInfo?.();

    if (!userInfo) {
      event.emit('setLoading',false)
      throw new Error('未获取到用户信息!');
    }

    await flushSync(() => {
      setInitialState((s) => ({
        ...(s as GlobalStore),
        currentUser: userInfo,
      }));
    });
    // 必须执行refresh，否则会出现菜单权限不刷新的问题
    await refresh();

    // 重定向
    const urlParams = new URL(window.location.href).searchParams;
    history.push(urlParams.get('redirect') || '/');
    location.reload();
  };

  useEffect(() => {
    if (token) {
      event.emit('setLoading',true)
      getInfo(token);
    } else {
      event.emit('setLoading',false)
    }
  }, [token]);

  const login = useRequest(
    async (query: FormQuery, captchaVerification: string) => {
      // 获取公钥后RSA算法加密密码
      const password = await encrypt(query.password, async () => {
        const res = await loginPublicKey({
          errorMessage: '获取token数据失败',
        });

        return res.data!;
      });

      const token = await apiLogin({
        ...query,
        password,
        captchaVerification,
      });

      if (!token.data) {
        throw Error('获取token失败');
      }

      getInfo(token.data);
    },
    {
      formatResult: (res) => res,
      manual: true,
    },
  );

  const handleFinish: React.ComponentProps<typeof Form<FormQuery>>['onFinish'] = async (values) => {
    verify.current?.open({
      onSuccess: (token) => {
        login.reset();
        login.run(
          {
            ...values,
          },
          token,
        );
      },
    });
  };

  return (
    <>
      {/* <h1 className={styles.welcome}>欢迎登录 {APP_TITLE} ! 👋🏻</h1> */}

      <Form<FormQuery> form={form} onFinish={handleFinish} disabled={login.loading}>
        {/* {login.error ? (
          <Alert showIcon type="error" message={login.error.message} className={styles['alert']} />
        ) : null} */}

        <Form.Item
          name="username"
          rules={[
            {
              required: true,
              message: '请输入用户名',
            },
          ]}
        >
          <Input allowClear placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[
            {
              required: true,
              message: '请输入密码',
            },
          ]}
        >
          <Input.Password allowClear placeholder="请输入密码" />
        </Form.Item>

        <div className={styles['toolbar']}>
          <Form.Item noStyle name="saveLogin" valuePropName="checked">
            <Checkbox>
              保持登录{' '}
              <Tooltip title="登录状态默认保存2小时,勾选后保存7天">
                <QuestionCircleOutlined />
              </Tooltip>
            </Checkbox>
          </Form.Item>
        </div>

        <Button htmlType="submit"  block loading={login.loading} className={styles['login-button']}>
          提交
        </Button>
        <div className={styles['toolbar']} style={{ marginTop: 8, fontSize: 12, color: 'red' }}>
        严禁传输、存储国家秘密、内部文件、敏感信息、工作秘密！
        </div>
      </Form>

      <VerifySlideModal ref={verify} />

      
    </>
  );
};

export default Page;
