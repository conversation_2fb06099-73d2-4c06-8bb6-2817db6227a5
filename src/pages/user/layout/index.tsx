import { Outlet } from '@umijs/max';
import { ConfigProvider, Spin } from 'antd';
import EventEmitter from 'eventemitter3';
import { useEffect, useState } from 'react';
import styles from './index.less';
export const event = new EventEmitter();

function Layout() {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    event.on('setLoading', setLoading);
  }, []);

  return (
    <ConfigProvider
      theme={{
        components: {
          Input: {
            controlHeight: 56,
            fontSize: 16,
          },
          Button: {
            controlHeight: 56,
            fontSize: 16,
          },
        },
      }}
    >
      <div className={styles['layout']}>
        <div className={styles['container']}>
          <div className={styles['container-banner']}>
            <div className={styles['container-banner-logo']} />
            <div className={styles['container-banner-img']}>
              <img
                style={{ width: '100%', }}
                src={require('./img/logo.png')}
                alt="banner"
              />
              <div className={styles['container-banner-title']}>
                <span>{APP_TITLE}</span>
              </div>
            </div>
          </div>
          <div className={styles['container-content']}>
            <Outlet />
          </div>
        </div>
      </div>
      <Spin spinning={loading} tip="正在努力登录中，请稍等..." fullscreen />
    </ConfigProvider>
  );
}

export default Layout;
