.layout {
  min-height: 100vh;
  background: radial-gradient(circle at center, #ffffff, #f2f3f5);
}

.container {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  background-color: transparent;
  border-radius: 10px;
  // box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);
  transform: translate(-50%) translateY(-50%);

  @media @phone {
    position: relative;
    top: inherit;
    left: inherit;
    flex-direction: column;
    justify-content: start;
    width: 100%;
    height: 100%;
    border-radius: 0;
    box-shadow: none;
    transform: translate(0) translateY(0);
  }

  &-banner {
    position: relative;
    width: 450px;
    overflow: hidden;
    background-color: transparent;

    @media @phone {
      width: 100%;
      padding: 20px 0;
    }

    &-title {
      margin-top: 16px;
      font-size: 42px;
      color: #025752;
      line-height: 42px;
      font-weight: 600;
    }
    &-logo {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 50px;
      height: 50px;
      // background: url('~@/assets/img/logo.png') no-repeat;
      background-size: contain;
    }

    &-img {
      position: absolute;
      top: 42%;
      width: 100%;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      @media @phone {
        position: relative;
        top: inherit;
        right: inherit;
        display: inherit;
        width: 65%;
        max-width: 375px;
        margin: 0 auto;
        transform: translateY(0);
      }
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 530px;
    padding: 50px;
    margin: 24px 24px 24px 200px;
    overflow: hidden;
    box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.08);


    @media @phone {
      width: 100%;
      min-height: auto;
      padding: 30px;
    }

    > h1 {
      width: 100%;
      margin: 0 auto 30px;
      color: @color-base;
      font-weight: 700;
      font-size: 22px;
    }
  }
}
