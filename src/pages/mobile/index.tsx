// import { orgUserInfoSupplierRegistration } from '@/services/biz/zuzhijiagourenyuanbiaojiekou';
import { useRequest } from '@umijs/max';
import { ConfigProvider, FormInstance, Spin } from 'antd';
import { useRef, useState } from 'react';
import noData from './img/noData.png';
import styles from './index.less';

function Layout() {
  const searchParams = location.hash.replace('#/mobile?', '');
  const urlParams = new URLSearchParams(searchParams);
  const reportId = urlParams.get('reportId');
  const reportInfo = useRequest(() => {
    return {
      data: {},
      success: true,
      total: 0,
    };
  });
  return (
    <ConfigProvider
      theme={{
        components: {
          Input: {
            controlHeight: 56,
            fontSize: 16,
          },
          Button: {
            controlHeight: 56,
            fontSize: 16,
          },
        },
      }}
    >
      <div className={styles['layout']}>
        <div className={styles['header']}>
          <div className={styles['titleimg']}></div>
        </div>
        <div className={styles['header']}>
          <div className={styles['title']}> 重庆信息通信研究院</div>
        </div>
        <div className={styles['container']}>
          <div className={styles['content']}>
            <div className={styles['header2']}>报告信息</div>
            {
              reportInfo?.loading && (
                <Spin />
              )
            }
            {reportInfo?.data?.reportStatus === '有效' && (
              <div className={styles['contentList']}>
                <div className={styles['list']}>
                  <div className={styles['key']}>报告编号：</div>
                  <div className={styles['value']}>
                    {reportInfo?.data?.reportNumber}
                  </div>
                </div>
                <div className={styles['list']}>
                  <div className={styles['key']}>报告签发人：</div>
                  <div className={styles['value']}>
                  {reportInfo?.data?.reportIssuer}
                  </div>
                </div>
                <div className={styles['list']}>
                  <div className={styles['key']}>报告签发时间：</div>
                  <div className={styles['value']}>{reportInfo?.data?.reportIssueTime}</div>
                </div>
                <div className={styles['list']}>
                  <div className={styles['key']}>委托单位：</div>
                  <div className={styles['value']}>{reportInfo?.data?.reportClient}</div>
                </div>
                <div className={styles['list']}>
                  <div className={styles['key']}>样品名称：</div>
                  <div className={styles['value']}>
                    {reportInfo?.data?.sampleNames?.map((k, index) => {
                      return <div key={index}>{k}；</div>
                    })}</div>
                </div>
                <div className={styles['list']}>
                  <div className={styles['key']}>测试结论：</div>
                  <div className={styles['value']}>{reportInfo?.data?.testConclusion}</div>
                </div>
                <div className={styles['list']}>
                  <div className={styles['key']}>报告状态：</div>
                  <div className={styles['value']}>
                  {reportInfo?.data?.reportStatus}
                  </div>
                </div>
              </div>
            )}
            {reportInfo?.data?.reportStatus === '报告，已失效' && (
              <div className={styles['noContent']}>
                <img src={noData} alt="" />
              </div>
            )}
            {reportInfo?.data?.reportStatus === '报告，已失效' && (
              <div className={styles['reportNumber']}>{reportInfo?.data?.reportNumber}</div>
            )}
            {reportInfo?.data?.reportStatus === '报告，已失效' && (
              <div className={styles['header4']}>报告，已失效</div>
            )}
          </div>
        </div>
        <div className={styles['bottom']}>
          <div className={styles['tip']}>
            <div className={styles['titlePre']}></div>
            <div className={styles['title2']}>温馨提示</div>
            <div className={styles['titlenext']}></div>
          </div>
          <div className={styles['thanks']}>感谢您的支持！</div>
          <div className={styles['thanks']}>若有异议，欢迎您致电：023-88068315</div>
        </div>
      </div>
    </ConfigProvider>
  );
}

export default Layout;
