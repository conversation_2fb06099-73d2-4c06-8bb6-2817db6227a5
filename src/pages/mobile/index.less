.layout {
  min-height: 100vh;
  background: #f0f2f5;
  background-image: url('./img/bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: auto;
  .header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
  }
  .titleimg {
    width: 32vw;
    height: 9.2vw;
    margin: 5vw 0 4vw;
    background-image: url('./img/titleimg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .title {
    // width: 64vw;
    height: 9.2vw;
    margin-bottom: 5vw;
    color: #ffffff;
    font-weight: 700;
    font-size: 7vw;
    font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>;
    font-style: normal;
    line-height: 9.2vw;
    text-align: center;
    text-transform: none;
  }
}

.container {
  width: 100vw;
  min-height: 114vw;
  padding: 0 2vw;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    min-height: 114vw;
    background-image: url('./img/content.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .header2 {
      // width: 21vw;
      height: 7vw;
      margin-top: 12vw;
      color: #0e1f50;
      font-weight: 500;
      font-size: 5.1vw;
      font-family: PingFang SC, PingFang SC;
      font-style: normal;
      line-height: 5.1vw;
      text-align: center;
      text-transform: none;
    }
    .contentList {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: 0 5vw 12vw;
      .list {
        display: flex;
        margin: 3.5vw 0 0;
        .key {
          width: 29vw;
          color: #0e1f50;
          font-size: 4vw;
          font-family: PingFang SC, PingFang SC;
          text-align: right;
        }
        .value {
          flex: 1;
          color: #0e1f50;
          font-size: 4vw;
          font-family: PingFang SC, PingFang SC;
          text-align: left;
        }
      }
    }
    .noContent {
      width: 54vw;
      height: 54vw;
      margin-top: 5.4vw;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .reportNumber {
      font-size: 4.1vw;
      color: #0e1f50;
      margin-top: 8.5vw;
    }
    .header4 {
      font-size: 4.1vw;
      line-height: 8vw;
      height: 6vw;
      color: red;
    }
  }
}

.bottom {
  width: 91vw;
  height: 33vw;
  margin: auto;
  margin-top: 2.3vw;
  background: #001d57;
  border-radius: 1vw;
  margin-bottom: 2vw;
  .tip {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
    margin-bottom: 8vw;
  }
  .titlePre {
    width: 21.5vw;
    height: 6px;
    background: linear-gradient(90deg, rgba(211, 231, 255, 0) 0%, #d3e7ff 100%);
  }
  .title2 {
    margin: 0 2.5vw;
    color: #d3e7ff;
    font-size: 4.1vw;
  }
  .titlenext {
    width: 21.5vw;
    height: 6px;
    background: linear-gradient(90deg, #d3e7ff 0%, rgba(211, 231, 255, 0) 100%);
  }
  .thanks {
    color: #d3e7ff;
    padding-left: 2.5vw;
    font-weight: 400;
    font-size: 3.8vw;
    font-family: PingFang SC, PingFang SC;
    font-style: normal;
    line-height: 8vw;
    text-align: left;
  }
}
