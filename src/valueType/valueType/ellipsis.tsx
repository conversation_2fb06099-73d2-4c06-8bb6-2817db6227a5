import type { ProRenderFieldPropsType } from '@ant-design/pro-components';
import { Input, Tooltip } from 'antd';

const DEFAULT_WIDTH = '10em';

const ValueType: ProRenderFieldPropsType = {
  render: (text, props) => {
    const width = props.fieldProps.width ?? DEFAULT_WIDTH;

    if (typeof text !== 'string') {
      return text;
    }

    return (
      <Tooltip title={text}>
        <div
          style={{
            width,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {text}
        </div>
      </Tooltip>
    );
  },
  renderFormItem: (_, props) => {
    return <Input {...props.fieldProps} />;
  },
};

export default ValueType;
