import Dict from '@/components/dict';
import { DictSelect } from '@/components/select/dict';
import type { ProRenderFieldPropsType } from '@ant-design/pro-components';

const ValueType: ProRenderFieldPropsType = {
  render: (text, props) => {
    return <Dict {...props.fieldProps} value={text} />;
  },
  renderFormItem: (_, props) => {
    return <DictSelect {...props.fieldProps} />;
  },
};

export default ValueType;
