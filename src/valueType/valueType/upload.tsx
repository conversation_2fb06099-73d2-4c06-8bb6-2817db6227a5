import FileDownload from '@/components/fileDownload';
import Upload from '@/components/upload';
import { UploadOutlined } from '@ant-design/icons';
import type { ProRenderFieldPropsType } from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';

const ValueType: ProRenderFieldPropsType = {
  render: (text, record, dom) => {
    console.log(text, record, dom, 'text, record, dom');
    return (
      <>
        {text?.map((fileInfo: any, index: number) => {
          return (
            <FileDownload
              key={index}
              item={fileInfo}
            />
          );
        })}
      </>
    );
  },
  renderFormItem: () => {
    return (
      <Upload>
        <Button icon={<UploadOutlined />}>上传</Button>
      </Upload>
    );
  },
};

export default ValueType;
