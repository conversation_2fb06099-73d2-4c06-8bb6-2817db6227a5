import Select from '@/components/select';
import { formatValue } from '@/utils';
import type {
  ProRenderFieldPropsType,
  ProSchemaValueEnumMap,
  ProSchemaValueEnumObj,
  ProSchemaValueEnumType,
} from '@ant-design/pro-components';

import { Tag } from 'antd';
import isPlainObject from 'lodash/isPlainObject';
import React from 'react';

export function renderEnum(text: any, valueEnum?: ProSchemaValueEnumObj | ProSchemaValueEnumMap) {
  if (valueEnum instanceof Map) {
    const map = valueEnum.get(Number(text)) || valueEnum.get(String(text));

    if (!map) {
      return formatValue(text);
    }

    if (React.isValidElement(map)) {
      return map;
    }

    if (isPlainObject(map)) {
      const valueEnumObj = map as ProSchemaValueEnumType;

      return <Tag color={valueEnumObj.color ?? valueEnumObj.status}>{valueEnumObj.text}</Tag>;
    }

    return map;
  }

  if (isPlainObject(valueEnum)) {
    const map = (valueEnum as ProSchemaValueEnumObj)[text];

    if (!map) {
      return formatValue(text);
    }

    if (React.isValidElement(map)) {
      return map;
    }

    if (isPlainObject(map)) {
      const valueEnumObj = map as ProSchemaValueEnumType;

      return <Tag color={valueEnumObj.color ?? valueEnumObj.status}>{valueEnumObj.text}</Tag>;
    }

    return map;
  }

  return formatValue(text);
}

const ValueType: ProRenderFieldPropsType = {
  render: (text, record) => {
    return renderEnum(text, record.valueEnum);
  },
  renderFormItem: (_, props) => {
    const valueEnum = props.valueEnum;

    const options: React.ComponentProps<typeof Select>['options'] = [];

    if (valueEnum) {
      if (valueEnum instanceof Map) {
        valueEnum.forEach((item, key) => {
          if (isPlainObject(item)) {
            options.push({
              value: key,
              label: (item as ProSchemaValueEnumType).text,
            });
          } else {
            options.push({
              value: key,
              label: item,
            });
          }
        });
      } else {
        Object.keys(valueEnum).forEach((key) => {
          const item = valueEnum[key];

          if (isPlainObject(item)) {
            options.push({
              value: key,
              label: (item as ProSchemaValueEnumType).text,
            });
          } else {
            options.push({
              value: key,
              label: item,
            });
          }
        });
      }
    }

    return <Select {...props.fieldProps} options={options} />;
  },
};

export default ValueType;
