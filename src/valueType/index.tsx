import { ProProvider } from '@ant-design/pro-components';
import React, { useContext } from 'react';

import color from './valueType/color';
import dict from './valueType/dict';
import ellipsis from './valueType/ellipsis';
import enumValueType from './valueType/enum';
import iconAnt from './valueType/iconAnt';
import upload from './valueType/upload';

const valueTypeMap = {
  enum: enumValueType,
  dict,
  iconAnt,
  color,
  ellipsis,
  upload,
};

/** 自定义valueType */
export type ValueType = keyof typeof valueTypeMap;

const ValueTypeProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const values = useContext(ProProvider);

  return (
    <ProProvider.Provider
      value={{
        ...values,
        valueTypeMap: {
          ...values.valueTypeMap,
          ...valueTypeMap,
        },
      }}
    >
      {children}
    </ProProvider.Provider>
  );
};

export { ValueTypeProvider };
