import { request } from '@umijs/max';

import { message } from 'antd';

import type { ConfigType } from 'dayjs';

import dayjs from 'dayjs';

import Decimal from 'decimal.js-light';

import Saver from 'file-saver';

import lodash from 'lodash';

import RSA from 'node-rsa';

/** 空值显示 */

export const emptyText = '--';

/** 格式化值 */

export function formatValue<T = string | number | null | undefined>(
  /** 要渲染的值 */

  v?: T,

  /** 换行个数 */

  max?: number,
): string | T {
  if (v === null || typeof v === 'undefined') {
    return emptyText;
  }

  if (typeof v === 'string' && !v) {
    return emptyText;
  }

  if (typeof v === 'number' && isNaN(v)) {
    return emptyText;
  }

  const s = String(v);

  if (max && s.length > max) {
    return s.replace(new RegExp(`(?![^\\n]{1,${max}}$)([^\\n]{1,${max}})\\s`, 'g'), '$1\n');
  }

  return v;
}

/** 格式化日期 */

export function formatDate(v: ConfigType, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!v) {
    return emptyText;
  }

  const m = dayjs(v);

  if (!m.isValid()) {
    return emptyText;
  }

  return m.format(format);
}

/**

 * 格式化数值

 */

export const formatNumber = (
  v: string | number | null | undefined,

  /** 小数位数 */

  digit = 0,

  /** 回调 */

  cb?: (v: Decimal) => Decimal,
): string => {
  if (v === null || typeof v === 'undefined') {
    return emptyText;
  }

  const isDecimal = /^(-?)(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;

  if (typeof v === 'string' && !isDecimal.test(v)) {
    return emptyText;
  }

  const p = new Decimal(v);

  return (cb ? cb(p) : p).toFixed(digit, Decimal.ROUND_DOWN);
};

/**

 * 加密字符串

 * 用于输入密码的地方,比如登录/修改密码

 */

export async function encrypt(
  /** 要加密的字符串 */

  value: string,

  /** 公钥Key,一般通过接口获取 */

  getKeyFunc: () => Promise<string>,
): Promise<string> {
  const SCRECT_KEY = await getKeyFunc();

  if (typeof SCRECT_KEY !== 'string') {
    throw Error('获取验证码失败');
  }

  const r = new RSA(SCRECT_KEY, 'public', {
    encryptionScheme: 'pkcs1',
  });

  return r.encrypt(Buffer.from(encodeURI(value), 'utf8'), 'base64', 'utf8');
}

/** 获取浏览器滚动条宽度 */

export function getBrowserScrollWidth(): number {
  // Add temporary box to wrapper

  const scrollbox = document.createElement('div');

  // Make box scrollable

  scrollbox.style.overflow = 'scroll';

  // Append box to document

  document.body.appendChild(scrollbox);

  // Measure inner width of box

  const scrollBarWidth = scrollbox.offsetWidth - scrollbox.clientWidth;

  // Remove box

  document.body.removeChild(scrollbox);

  return scrollBarWidth;
}

/** 将proTable的columns参数转化为ProDescriptions的columns */

export function transforTCol2DCols(tColumns: any[]) {
  return tColumns.map((i) => ({
    label: i.title,

    ...i,
  }));
}

export function replaceMultiText(text: string, replaceText: string): string {
  return text.replace(new RegExp(`(${replaceText}){1,}`, 'g'), '$1');
}

/** 将数组转为valueEnum ['男','女'] 转为{男:'男',女:'女'} */

export const arr2ValueEnum = (arr: string[]) =>
  arr?.reduce((acc, cur) => ({ ...acc, [cur]: cur }), {});

/** 将字典数据转为valueEnum */

export const dict2ValueEnum = (
  arr: BASE.DicInfoVO[] = [],

  config: { labelKey: string; valueKey: string } = { labelKey: 'dicName', valueKey: 'dicValue' },
) =>
  arr?.reduce(
    (acc, cur) => ({
      ...acc,

      [lodash.get(cur, config?.labelKey)]: lodash.get(cur, config?.valueKey),
    }),

    {},
  );

// 解析图片地址

export const parseSrc = (src?: undefined | null | string) => {
  if (typeof src !== 'string') {
    return undefined;
  }

  if (src.startsWith('http')) {
    return src;
  }

  // base64

  if (/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(src)) {
    if (src.startsWith('/')) {
      return `data:image/png;base64,${src}`;
    }

    if (src.startsWith(',/')) {
      return `data:image/png;base64${src}`;
    }

    return `data:image/png;base64,${src}`;
  }

  if (src.startsWith('/')) {
    return replaceMultiText(`${PROXY_KEY}${src}`, '/');
  }

  return src;
};

/**

 * 对数字进行三位分割

 * @param {*} value  需要进行分割的数字

 * @returns  返回分割后的数字串

 */

// 小数部分只显示两位小数

export function numberFormat(value: number | string) {
  if (!value) return '0';

  // 获取整数部分的数字

  let intPart = Number(value).toFixed(0);

  // 将整数部分逢三一断

  let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');

  // 预定义小数部分

  let floatPart = '.00';

  let value2Array = value.toString().split('.');

  // =2表示数据有小数位

  if (value2Array.length === 2) {
    floatPart = value2Array[1].toString(); // 拿到小数部分

    if (floatPart.length === 1) {
      // 补0

      return intPartFormat + '.' + floatPart + '0';
    } else {
      return intPartFormat + '.' + floatPart;
    }
  } else {
    return intPartFormat;
  }
}

/**

 * 按照提供的排列顺序对columns进行排序

 * @param columns 需要排序的columns

 * @param order 排序的顺序

 */

export const orderColumns = (columns: any[], order: string[]) => {
  const titleColumnItem = lodash.keyBy(columns, 'title');

  const newColumns = order.map((item) => titleColumnItem[item]);

  const otherColumns = columns.filter((item) => !order.includes(item.title));

  newColumns.push(...otherColumns);

  return newColumns;
};

// 柯里化函数，创建一个获取dataIndex的函数

export const getDataIndexFn = (prefix: string) => {
  return (dataIndex: string) => [prefix, dataIndex];
};

// 下载保存文件

export const fileSave = (res) => {
  // 获取文件名

  const contentDisposition = res.headers['content-disposition'];

  let filename = '';

  if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
    let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;

    let matches = filenameRegex.exec(contentDisposition);

    if (matches !== null && matches[1]) {
      filename = decodeURIComponent(matches[1].replace(/['"]/g, ''));
    }
  }

  Saver.saveAs(res.data, filename);
};

// blob转json

function blobToJson(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = function (event) {
      const text = event?.target?.result;

      try {
        const json = JSON.parse(text as any);

        resolve(json);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = function (event) {
      reject(event?.target?.error);
    };

    reader.readAsText(blob);
  });
}

/** 下载文件 */

export const downloadFile = async (
  fileUrl?: string,

  callback?: ((param: boolean) => void) | null,
) => {
  if (!fileUrl) {
    return message.error('文件不存在');
  }

  console.log(fileUrl);

  if (callback) {
    callback(false);

    message.warning('文件下载中，请稍等！');
  }

  try {
    const res = await request(fileUrl, {
      responseType: 'blob',

      skipErrorHandler: true,

      getResponse: true,
    });

    if (callback) {
      callback(true);
    }

    fileSave(res);
  } catch (error) {
    if (callback) {
      callback(true);
    }

    blobToJson((error as any)?.response?.data)
      .then((json) => {
        setTimeout(() => {
          message.error((json as any)?.message); // 处理转换后的 JSON 对象
        }, 1000);
      })

      .catch((error) => {
        console.error('Error:', error);
      });
  }
};
