import { useEffect, useLayoutEffect, useRef, useState } from 'react';

type Updater<T> = (updater: T | ((origin: T) => T)) => void;

enum Source {
  INNER, // 组件内部
  PROP, // props
}

type ValueRecord<T> = [T, Source, T];

type ValueFunction<T> = (prev?: T) => T;

/** We only think `undefined` is empty */
function hasValue<T>(value: T) {
  return value !== undefined;
}

/**
 * useState和props值合并
 */
const useMergedState = <T, R = T>(
  defaultStateValue: T | ValueFunction<T>,
  option?: {
    defaultValue?: T | ValueFunction<T>;
    value?: T;
    onChange?: (value: T, prevValue: T) => void;
    postState?: (value: T) => T;
  },
): [R, Updater<T>] => {
  const { defaultValue, value, onChange, postState } = option || {};
  const firstMountRef = useRef(false);

  /** 初始化 */
  const [mergedValue, setMergedValue] = useState<ValueRecord<T>>(() => {
    let finalValue: T;
    let source: Source;

    if (hasValue(value)) {
      // 从option.value同步
      finalValue = value as T;
      source = Source.PROP;
    } else if (hasValue(defaultValue)) {
      // 从option.defaultValue同步
      finalValue =
        typeof defaultValue === 'function'
          ? (defaultValue as ValueFunction<T>)()
          : (defaultValue as T);
      source = Source.PROP;
    } else {
      // 从defaultStateValue同步
      finalValue =
        typeof defaultStateValue === 'function'
          ? (defaultStateValue as ValueFunction<T>)()
          : defaultStateValue;
      source = Source.INNER;
    }

    return [finalValue, source, finalValue];
  });
  /** end 初始化 */

  const chosenValue = hasValue(value) ? value : mergedValue[0];
  const postMergedValue = postState ? postState(chosenValue as T) : chosenValue;

  /** 同步props */
  useEffect(() => {
    // 过滤第一次更新, 因为初始化时已经同步了
    if (!firstMountRef.current) {
      firstMountRef.current = true;

      return;
    }

    setMergedValue(([prevValue]) => [value as T, Source.PROP, prevValue]);
  }, [value]);
  /** end 同步props */

  /** 更新 */
  const changeEventPrevRef = useRef<T>();

  const triggerChange: Updater<T> = (updater) => {
    setMergedValue((prev) => {
      const [prevValue, prevSource, prevPrevValue] = prev;

      const nextValue: T =
        typeof updater === 'function' ? (updater as ValueFunction<T>)(prevValue) : updater;

      if (nextValue === prevValue) {
        return prev;
      }

      // 如果在批量更新中，则使用 prev prev 值以避免丢失数据
      const overridePrevValue =
        prevSource === Source.INNER && changeEventPrevRef.current !== prevPrevValue
          ? prevPrevValue
          : prevValue;

      return [nextValue, Source.INNER, overridePrevValue];
    });
  };
  /** end 更新 */

  /** 改变 */
  useLayoutEffect(() => {
    const [current, source, prev] = mergedValue;

    if (current !== prev && source === Source.INNER) {
      onChange?.(current, prev);
      changeEventPrevRef.current = prev;
    }
  }, [mergedValue]);
  /** end 改变 */

  return [postMergedValue as R, triggerChange];
};

export { useMergedState };
