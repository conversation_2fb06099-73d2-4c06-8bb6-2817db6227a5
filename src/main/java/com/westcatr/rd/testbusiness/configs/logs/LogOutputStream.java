package com.westcatr.rd.testbusiness.configs.logs;

import org.apache.commons.io.output.TeeOutputStream;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

public class LogOutputStream extends ByteArrayOutputStream {
    private final PrintStream original;

    public LogOutputStream() {
        // 保存原始的 System.out
        this.original = System.out;

        // 创建 TeeOutputStream，将输出定向到控制台和 ByteArrayOutputStream
        TeeOutputStream teeOutputStream = new TeeOutputStream(this, original);
        System.setOut(new PrintStream(teeOutputStream));
    }

    // 获取日志内容
    public String getLogContent() {
        return this.toString();
    }

    // 释放资源
    public void resetOut() {
        System.setOut(original);
    }
}