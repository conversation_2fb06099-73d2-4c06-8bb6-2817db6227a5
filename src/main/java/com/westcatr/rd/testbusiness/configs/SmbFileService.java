package com.westcatr.rd.testbusiness.configs;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jcifs.CIFSContext;
import jcifs.context.SingletonContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbException;
import jcifs.smb.SmbFile;
import lombok.extern.slf4j.Slf4j;

/**
 * SMB文件服务
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Slf4j
@Service
public class SmbFileService {

    @Value("${smb.file.save.dir}")
    private String smbFileSaveDir;

    /**
     * 从 SMB 共享目录获取指定时间范围内的文件，并保存到本地临时目录。
     * 
     * @param smbUrl     SMB 共享目录 URL，例如 "smb://server/share/"
     * @param username   SMB 用户名
     * @param password   SMB 密码
     * @param startTime  开始时间（时间戳，毫秒）
     * @param endTime    结束时间（时间戳，毫秒）
     * @param fileSuffix 文件后缀名（例如 ".bmp", ".jpg"），不区分大小写；如果为空，则获取所有类型的文件
     * @param saveDir    本地保存目录（可选），如果为空，则使用系统临时目录
     * @return 返回一个包含文件信息的列表, 每个元素是一个 Map，包含 'file' (File 对象) 和 'originalFileName'
     *         (原始 SMB 文件名)
     * @throws IOException 如果发生 I/O 错误
     */
    public List<Map<String, Object>> getSmbFiles(String smbUrl, String username, String password,
            Long startTime, Long endTime, String fileSuffix, String saveDir) throws IOException {

        // 1. 参数校验和处理
        if (!StringUtils.hasText(smbUrl)) {
            throw new IllegalArgumentException("SMB URL 不能为空");
        }
        if (startTime != null && endTime != null && startTime > endTime) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }

        // 确保 smbUrl 格式正确
        if (!smbUrl.startsWith("smb://")) {
            // 如果不是以 smb:// 开头，添加前缀
            smbUrl = "smb://" + smbUrl;
        }
        // 确保 URL 以 / 结尾
        if (!smbUrl.endsWith("/")) {
            smbUrl = smbUrl + "/";
        }

        log.info("尝试连接 SMB 服务器: {}", smbUrl);

        // 2. 创建 CIFSContext (使用 NtlmPasswordAuthenticator)
        NtlmPasswordAuthenticator auth = new NtlmPasswordAuthenticator(null, username, password);

        // 设置JCIFS全局属性，增加超时时间和重试次数
        System.setProperty("jcifs.smb.client.responseTimeout", "60000"); // 响应超时时间增加到60秒
        System.setProperty("jcifs.smb.client.soTimeout", "60000"); // Socket超时时间增加到60秒
        System.setProperty("jcifs.resolveOrder", "DNS"); // 优先使用DNS解析
        System.setProperty("jcifs.smb.client.connTimeout", "60000"); // 连接超时时间60秒
        System.setProperty("jcifs.smb.client.retryCount", "3"); // 重试3次
        System.setProperty("jcifs.netbios.retryTimeout", "5000"); // NetBIOS重试超时5秒

        // 创建CIFSContext
        CIFSContext context = SingletonContext.getInstance().withCredentials(auth);

        // 3. 创建 SmbFile 对象和获取文件列表
        List<SmbFile> filteredFiles = new ArrayList<>();

        try {
            SmbFile smbDir = new SmbFile(smbUrl, context);

            // 尝试检查目录是否存在
            try {
                if (!smbDir.exists() || !smbDir.isDirectory()) {
                    log.warn("SMB 目录不存在或不是一个目录: {}", smbUrl);
                    return new ArrayList<>(); // 返回空列表而不是抛出异常
                }
            } catch (SmbException e) {
                log.error("检查 SMB 目录时出错: {}，错误信息: {}", smbUrl, e.getMessage());
                return new ArrayList<>(); // 返回空列表而不是抛出异常
            }

            // 修改一下 随机获取一个图。需要过滤文件后缀名
            SmbFile[] files = null;
            try {
                files = smbDir.listFiles();
            } catch (SmbException e) {
                log.error("列出 SMB 目录文件时出错: {}，错误信息: {}", smbUrl, e.getMessage());
                return new ArrayList<>(); // 返回空列表而不是抛出异常
            }

            if (files != null) {
                List<SmbFile> eligibleFiles = new ArrayList<>();
                for (SmbFile file : files) {
                    try {
                        if (file.isFile()) {
                            // 先过滤文件名后缀
                            if (fileSuffix == null || fileSuffix.isEmpty()
                                    || file.getName().toLowerCase().endsWith(fileSuffix.toLowerCase())) {
                                eligibleFiles.add(file);
                            }
                        }
                    } catch (SmbException e) {
                        log.warn("检查文件 {} 时出错: {}", file.getPath(), e.getMessage());
                        // 继续处理下一个文件
                    }
                }

                // 从符合条件的文件中随机选择一个
                if (!eligibleFiles.isEmpty()) {
                    int randomIndex = new Random().nextInt(eligibleFiles.size());
                    filteredFiles.add(eligibleFiles.get(randomIndex));
                    log.info("从 SMB 目录中选择了文件: {}", eligibleFiles.get(randomIndex).getName());
                } else {
                    log.warn("在 SMB 目录 {} 中没有找到符合条件的文件", smbUrl);
                }
            }
        } catch (IOException e) {
            log.error("连接 SMB 服务器失败: {}，错误信息: {}", smbUrl, e.getMessage());
            return new ArrayList<>(); // 返回空列表而不是抛出异常
        } catch (Exception e) {
            log.error("处理 SMB 连接时发生未预期的错误: {}", e.getMessage(), e);
            return new ArrayList<>(); // 返回空列表而不是抛出异常
        }

        // 如果没有找到文件，直接返回空列表
        if (filteredFiles.isEmpty()) {
            log.info("没有找到符合条件的文件，返回空列表");
            return new ArrayList<>();
        }

        // 5. 创建本地临时保存目录
        // 使用配置的保存目录
        File localSaveDir;
        try {
            if (StringUtils.hasText(saveDir)) {
                localSaveDir = new File(saveDir);
            } else if (StringUtils.hasText(smbFileSaveDir)) {
                // 使用配置的 smbFileSaveDir 作为基础目录
                String tempDirName = "smb_files_" + System.currentTimeMillis();
                localSaveDir = new File(smbFileSaveDir, tempDirName);
            } else {
                // 如果都没有配置，则使用系统临时目录
                localSaveDir = Files.createTempDirectory("smb_files_").toFile();
                localSaveDir.deleteOnExit();
            }

            // 确保目录存在
            if (!localSaveDir.exists()) {
                if (!localSaveDir.mkdirs()) {
                    log.error("无法创建本地保存目录: {}", localSaveDir.getAbsolutePath());
                    return new ArrayList<>(); // 返回空列表而不是抛出异常
                }
            }
        } catch (IOException e) {
            log.error("创建临时目录时出错: {}", e.getMessage(), e);
            return new ArrayList<>(); // 返回空列表而不是抛出异常
        } catch (Exception e) {
            log.error("创建本地保存目录时出错: {}", e.getMessage(), e);
            return new ArrayList<>(); // 返回空列表而不是抛出异常
        }

        // 6. 下载文件到本地
        List<Map<String, Object>> downloadedFiles = new ArrayList<>();
        for (SmbFile smbFile : filteredFiles) {
            File localFile = new File(localSaveDir, smbFile.getName()); // 使用原始文件名
            try (InputStream in = smbFile.getInputStream();
                    FileOutputStream out = new FileOutputStream(localFile)) {
                byte[] buffer = new byte[65536]; // 64KB 缓冲区
                int bytesRead;

                try {
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                    log.info("成功下载文件: {} 到 {}", smbFile.getName(), localFile.getAbsolutePath());
                } catch (jcifs.smb.SmbException e) {
                    // 忽略"End of file"异常
                    if (!e.getMessage().contains("End of file")) {
                        log.error("下载文件 {} 时出错: {}", smbFile.getName(), e.getMessage());
                        continue; // 跳过此文件，继续处理下一个
                    }
                    log.info("注意：SMB读取时遇到了预期的End of file异常，已正常处理");
                }

            } catch (Exception e) {
                log.error("处理文件 {} 时出错: {}", smbFile.getName(), e.getMessage(), e);
                continue; // 跳过此文件，继续处理下一个
            }

            // 构造返回信息
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("file", localFile);
            fileInfo.put("originalFileName", smbFile.getName()); // 原始文件名
            downloadedFiles.add(fileInfo);
        }

        return downloadedFiles;
    }

    /**
     * 将时间戳转换为 LocalDateTime
     * 
     * @param timestamp 时间戳 (毫秒)
     * @return
     */
    public static LocalDateTime timestampToLocalDateTime(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }
}
