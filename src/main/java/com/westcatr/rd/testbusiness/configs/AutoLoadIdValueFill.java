package com.westcatr.rd.testbusiness.configs;

import com.westcatr.rd.boot.orm.dto.table.ColumnInfo;
import com.westcatr.rd.boot.orm.dto.table.Table;
import com.westcatr.rd.boot.orm.service.DefaultValueFillService;
import com.westcatr.rd.boot.orm.service.TableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 加载id默认值填充处理器
 * <AUTHOR>
 * @date 2023/3/27 18:56
 */
@Component
public class AutoLoadIdValueFill implements CommandLineRunner {

    @Autowired
    private TableService tableService;
    @Autowired
    private DefaultValueFillService idValueFillServiceImpl;

    @Override
    public void run(String... args) {
        List<String> allTable = tableService.getAllTable();
        for (String s : allTable) {
            Table table = tableService.loadTable(s);
            ColumnInfo id = table.getColumnInfo("id");
            if (id != null){
                Map<String, DefaultValueFillService> createFillMap = tableService.getCreateFillMap(s);
                createFillMap.putIfAbsent("id", idValueFillServiceImpl);
            }
        }
    }
}
