package com.westcatr.rd.testbusiness.configs;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <p>
 * MQTT配置类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mqtt")
public class MqttConfig {

    /**
     * MQTT服务器地址
     */
    private String brokerUrl = "tcp://48kkod342786.vicp.fun:26600";

    /**
     * 客户端ID前缀
     */
    private String clientIdPrefix = "test_business_";

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 下行主题（发送指令）
     */
    private String downTopic = "/jcxt/link/down/edgeGateway/edgeTopic";

    /**
     * 上行主题（接收结果）
     */
    private String upTopic = "/jcxt/link/up/edgeGateway/edgeTopic";

    /**
     * QoS级别
     */
    private int qos = 1;

    /**
     * 请求超时时间（秒）
     */
    private int requestTimeout = 30;

    /**
     * 是否启用MQTT客户端
     */
    private boolean enabled = true;
}
