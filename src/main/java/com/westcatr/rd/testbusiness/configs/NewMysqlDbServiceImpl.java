package com.westcatr.rd.testbusiness.configs;

import cn.hutool.core.collection.CollUtil;
import com.westcatr.rd.boot.core.util.MapUtil;
import com.westcatr.rd.boot.orm.OrmProperties;
import com.westcatr.rd.boot.orm.dto.table.ColumnInfo;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.service.MysqlDbServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/27 19:04
 */
@Slf4j
@Primary
@Service("mysqlDbService")
public class NewMysqlDbServiceImpl extends MysqlDbServiceImpl {

    @Autowired
    private OrmProperties ormProperties;

    @Override
    public List<ColumnInfo> getAllColumns(String dataSource, BaseDao baseDao, String tableName) {
        String sql = "SELECT * FROM information_schema.COLUMNS WHERE TABLE_NAME = '" + tableName + "' AND TABLE_SCHEMA = '" + ormProperties.getDataSourceDbNameMap().get(dataSource) + "'";
        List<Map<String, Object>> maps = baseDao.selectListBySql(sql, null);
        return maps.stream().map(map -> {
            ColumnInfo columnInfo = new ColumnInfo();
            columnInfo.setColumnName(MapUtil.getStr(map, "COLUMN_NAME"));
            columnInfo.setIsNullable(MapUtil.getStr(map, "IS_NULLABLE"));
            columnInfo.setColumnDefault(MapUtil.getStr(map, "COLUMN_DEFAULT"));
            columnInfo.setComment(MapUtil.getStr(map, "COLUMN_COMMENT"));
            columnInfo.setSqlType(MapUtil.getStr(map, "DATA_TYPE"));
            columnInfo.setExtra(MapUtil.getStr(map, "EXTRA"));
            String columnType = MapUtil.getStr(map, "COLUMN_TYPE");
            if (columnType.contains("(")){
                columnInfo.setLength(columnType.substring(columnType.indexOf("(") + 1, columnType.indexOf(")")));
            }
            if ("int".equals(columnInfo.getSqlType())) {
                columnInfo.setClazz(Integer.class);
            } else if ("bigint".equals(columnInfo.getSqlType())) {
                columnInfo.setClazz(Long.class);
            } else if ("varchar".equals(columnInfo.getSqlType())) {
                columnInfo.setClazz(String.class);
            } else if ("datetime".equals(columnInfo.getSqlType())) {
                columnInfo.setClazz(Date.class);
            } else if ("decimal".equals(columnInfo.getSqlType())) {
                columnInfo.setClazz(BigDecimal.class);
            } else if ("tinyint".equals(columnInfo.getSqlType())) {
                columnInfo.setClazz(Boolean.class);
            }else if (("smallint").equals(columnInfo.getSqlType())){
                columnInfo.setClazz(Integer.class);
            }else if (("timestamp").equals(columnInfo.getSqlType())){
                columnInfo.setClazz(Date.class);
            }else if (("longtext").equals(columnInfo.getSqlType())){
                columnInfo.setClazz(String.class);
            }else if (("double").equals(columnInfo.getSqlType())){
                columnInfo.setClazz(Double.class);
            }else if (("float").equals(columnInfo.getSqlType())){
                columnInfo.setClazz(Float.class);
            }else if (("text").equals(columnInfo.getSqlType())){
                columnInfo.setClazz(String.class);
            }else {
                //throw new IRuntimeException("不支持的类型[" + columnInfo.getSqlType() + "]");
                columnInfo.setClazz(String.class);
            }
            return columnInfo;
        }).toList();
    }

    @Override
    public String getKey(String dataSource, BaseDao dao, String tableName) {
        try {
            String tableSchema = ormProperties.getDataSourceDbNameMap().get(dataSource);
            List<String> strings = dao.selectListBySql(String.class, "SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_NAME = '" + tableName + "' AND TABLE_SCHEMA = '" + tableSchema + "' AND COLUMN_KEY = 'PRI'", null);
            if (CollUtil.isNotEmpty(strings)){
                return CollUtil.join(strings, ",");
            }else {
                return "id";
            }
        }catch (Exception e){
            log.error("获取表{}主键失败", tableName, e);
            return "id";
        }
    }
}
