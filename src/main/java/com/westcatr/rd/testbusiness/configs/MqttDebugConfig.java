package com.westcatr.rd.testbusiness.configs;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <p>
 * MQTT调试配置类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mqtt.debug")
public class MqttDebugConfig {

    /**
     * 是否启用MQTT调试模式
     */
    private boolean enabled = false;

    /**
     * 调试响应文件存储路径
     */
    private String storagePath = "/data/mqtt_debug/responses/";

    /**
     * 响应文件保留天数
     */
    private int retentionDays = 30;

    /**
     * 是否自动保存设备响应
     */
    private boolean autoSaveResponses = true;

    /**
     * 默认响应文件名前缀
     */
    private String filePrefix = "mqtt_response";

    /**
     * 响应文件格式
     */
    private String fileExtension = ".json";
}
