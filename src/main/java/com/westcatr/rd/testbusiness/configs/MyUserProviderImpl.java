package com.westcatr.rd.testbusiness.configs;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fast.jackson.JSONObject;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.security.crud.entity.ApiPermission;
import com.westcatr.rd.boot.security.crud.entity.PagePermission;
import com.westcatr.rd.boot.security.crud.entity.Role;
import com.westcatr.rd.boot.security.crud.pojo.query.PagePermissionQuery;
import com.westcatr.rd.boot.security.domain.LoginDTO;
import com.westcatr.rd.boot.security.service.impl.AbstractUserProviderImpl;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.westcatr.rd.testbusiness.business.org.service.OrgDeptInfoService;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserDeptInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.westcatr.rd.boot.security.common.AuthConstant.SUPER_ADMIN;

@Service
public class MyUserProviderImpl extends AbstractUserProviderImpl {

    @Autowired
    private OrgUserDeptInfoService orgUserDeptInfoService;

    @Autowired
    private OrgDeptInfoService orgDeptInfoService;

    @Override
    public IUser getByLoginDTO(LoginDTO dto) {
        return getByUsernameAndOther(dto.getUsername(), dto.getOtherData());
    }

    @Override
    public IUser getByUsernameAndOther(String username, Map<String, Object> otherData) {
        QueryWrapper<Object> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(iSecurityProperties.getSelectUserSqlWhere(), username);
        return baseDao.selectOneBySql(IUser.class, iSecurityProperties.getSelectUserSql(), queryWrapper);
    }

    @Override
    public IUser getUserPermissions(IUser user) {
        List<Role> byUserId = userRoleService.getByUserId(user.getId());
        // 按照角色等级正向排序
        if (CollUtil.isNotEmpty(byUserId)) {
            byUserId.sort(Comparator.comparing(Role::getRoleSort));
        }
        List<String> userRoleNames = new ArrayList<>();
        if (CollUtil.isNotEmpty(byUserId)) {
            Set<String> strings = new HashSet<>();
            Set<Long> integerSet = new HashSet<>();
            for (Role role : byUserId) {
                strings.add(role.getRoleCode());
                integerSet.add(role.getId());
                userRoleNames.add(role.getRoleName());
            }
            user.setRoles(strings);
            user.setRoleIds(integerSet);
        } else {
            user.setRoles(user.getRoles() == null ? new HashSet<>() : user.getRoles());
            user.setRoleIds(user.getRoleIds() == null ? new HashSet<>() : user.getRoleIds());
        }
        Set<String> roles = user.getRoles();
        Set<String> pages = new HashSet<>();
        Set<Long> pageIds = new HashSet<>();
        Set<String> permissions = new HashSet<>();
        Set<Long> permissionIds = new HashSet<>();
        List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService
                .list(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getUserId, user.getId()));
        if (CollUtil.isNotEmpty(orgUserDeptInfos)) {
            // 获取orgUserDeptInfos所有id为一个List<Long>
            List<Long> deptIds = orgUserDeptInfos.stream().map(OrgUserDeptInfo::getDeptId).collect(Collectors.toList());
            List<OrgDeptInfo> orgDeptInfos = orgDeptInfoService
                    .list(new LambdaQueryWrapper<>(OrgDeptInfo.class).in(OrgDeptInfo::getId, deptIds));
            if (CollUtil.isNotEmpty(orgDeptInfos)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("orgDeptInfos", orgDeptInfos);
                jsonObject.put("orgUserRoleFullNames", userRoleNames);
                user.setExtendData(jsonObject);
            }
        }
        if (roles.contains(SUPER_ADMIN)) {
            /* PagePermissionQuery menuQuery = new PagePermissionQuery();
            menuQuery.setPage(1).setSize(10000); */
            List<PagePermission> records = pagePermissionService.list();
            user.setPages(records.stream().map(PagePermission::getPermissionCode).collect(Collectors.toSet()));
            user.setPageIds(records.stream().map(PagePermission::getId).collect(Collectors.toSet()));
            List<ApiPermission> list = apiPermissionService.listEntity();
            if (CollUtil.isNotEmpty(list)) {
                for (ApiPermission apiPermission : list) {
                    permissions.add(apiPermission.getRequestWay() + ":" + apiPermission.getUrl());
                    permissionIds.add(apiPermission.getId());
                }
            }
            user.setPermissions(permissions);
            user.setPermissionIds(permissionIds);
        } else {
            Set<PagePermission> pagePermissionSet = userPagePermissionService
                    .getAllPagePermissionByUserId(user.getId());
            if (CollUtil.isNotEmpty(pagePermissionSet)) {
                pagePermissionSet.forEach(pagePermission -> {
                    pages.add(pagePermission.getPermissionCode());
                    pageIds.add(pagePermission.getId());
                });
            }
            user.setPages(pages);
            user.setPageIds(pageIds);
            if (CollUtil.isNotEmpty(user.getPageIds())) {
                Set<ApiPermission> apiPermissionSet = pagePermissionApiService.getByPagePermissionIds(pages);
                if (CollUtil.isNotEmpty(apiPermissionSet)) {
                    for (ApiPermission apiPermission : apiPermissionSet) {
                        permissions.add(apiPermission.getRequestWay() + ":" + apiPermission.getUrl());
                        permissionIds.add(apiPermission.getId());
                    }
                }
            }
            user.setPermissions(permissions);
            user.setPermissionIds(permissionIds);
        }
        return user;
    }

    @Override
    public IUser getUserByDb(String token) {
        IUser iUser0 = getUserByCache(token);
        Map<String, Object> map = new HashMap<>();
        map.put("userType", iUser0.getUserType());
        IUser iUser = getByUsernameAndOther(iUser0.getUsername(), map);
        if (iUser == null) {
            throw new IRuntimeException("用户不存在");
        }
        iUser.setPassword(null);
        iUser.setPageIds(iUser0.getPageIds());
        iUser.setPages(iUser0.getPages());
        iUser.setPermissions(iUser0.getPermissions());
        iUser.setPermissionIds(iUser0.getPermissionIds());
        iUser.setRoles(iUser0.getRoles());
        iUser.setRoleIds(iUser0.getRoleIds());
        List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService
                .list(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getUserId, iUser0.getId()));
        JSONObject jsonObject = new JSONObject();
        if (CollUtil.isNotEmpty(orgUserDeptInfos)) {
            // 获取orgUserDeptInfos所有id为一个List<Long>
            List<Long> deptIds = orgUserDeptInfos.stream().map(OrgUserDeptInfo::getDeptId)
                    .collect(Collectors.toList());
            List<OrgDeptInfo> orgDeptInfos = orgDeptInfoService
                    .list(new LambdaQueryWrapper<>(OrgDeptInfo.class).in(OrgDeptInfo::getId, deptIds));
            List<Role> byUserId = userRoleService.getByUserId(iUser.getId());
            if (CollUtil.isNotEmpty(orgDeptInfos)) {
                jsonObject.put("orgDeptInfos", orgDeptInfos);
                jsonObject.put("orgUserRoleFullNames", Optional.ofNullable(byUserId)
                        .orElse(Collections.emptyList())
                        .parallelStream()
                        .map(Role::getRoleName)
                        .filter(Objects::nonNull)
                        .distinct()
                        .toList());
            }
        }
        jsonObject.put("photoUrl", null);
        if (iUser.getExtend1() != null && iUser.getExtend1().equals("true")) {
            jsonObject.put("photoUrl", "avatar/" + iUser.getId() + ".jpg");

        }
        iUser.setExtendData(jsonObject);
        return iUser;
    }

    @Override
    public IUser getUserByCache(String token) {
        IUser iUser = tokenManageProvider.getUserAndPermission(token);
        if (iUser != null) {
            return iUser;
        }
        IUser user = tokenManageProvider.getUser(token);
        if (user != null) {
            IUser userDetails = getUserPermissions(user);
            tokenManageProvider.setUserAndPermission(token, userDetails, tokenManageProvider.getExpire(token));
            return userDetails;
        }
        String offlineTokenMessage = tokenManageProvider.getOfflineTokenMessage(token);
        if (offlineTokenMessage != null) {
            throw new IRuntimeException(offlineTokenMessage, 401);
        }
        throw new IRuntimeException("获取用户信息失败", 401);
    }
}
