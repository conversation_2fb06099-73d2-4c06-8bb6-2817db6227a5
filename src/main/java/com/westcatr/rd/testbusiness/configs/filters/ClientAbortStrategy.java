package com.westcatr.rd.testbusiness.configs.filters;

import com.westcatr.rd.boot.web.exception.ErrorInfo;
import com.westcatr.rd.boot.web.exception.strategy.ExceptionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:33
 */
@Slf4j
@Component
public class ClientAbortStrategy implements ExceptionStrategy {

    @Override
    public void exceptionProcessing(ErrorInfo errorInfo, Throwable error) {
        // 直接忽略错误拦截
        errorInfo.setMessage("");
    }

    @Override
    public Class<? extends Throwable> getExceptionClass() {
        return ClientAbortException.class;
    }
}
