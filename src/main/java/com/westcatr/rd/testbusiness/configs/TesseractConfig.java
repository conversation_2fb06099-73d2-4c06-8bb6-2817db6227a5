package com.westcatr.rd.testbusiness.configs;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import net.sourceforge.tess4j.Tesseract;

@Configuration
public class TesseractConfig {

    @Value("${tesseract.install.readdes}")
    private String tesseractInstallReades;

    @Value("${tesseract.language.path}")
    private String tesseractLanguagePath;

    @Bean
    public Tesseract tesseract() {
        // 设置JNA库路径，确保能找到tesseract的动态库
        System.setProperty("jna.library.path", tesseractInstallReades);

        // 检测操作系统类型，为Mac OS设置额外的属性
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("mac")) {
            // Mac OS特定配置
            System.setProperty("jna.platform.library.path", tesseractInstallReades);
            // 设置Darwin特定库路径属性
            System.setProperty("jna.darwin.library.path", tesseractInstallReades);

            // 设置Leptonica库的路径（Mac OS特定）
            System.setProperty("lept.library.path", "/opt/homebrew/lib");
            // 设置Java临时目录，避免权限问题
            System.setProperty("java.io.tmpdir", "/tmp");
        }

        Tesseract tesseract = new Tesseract();
        // 设置语言包路径
        tesseract.setDatapath(tesseractLanguagePath);
        // 设置语言
        tesseract.setLanguage("chi_sim");
        // 可选：设置其他参数（如 OCR 模式）
        tesseract.setPageSegMode(6); // 假设单行文本
        return tesseract;
    }
}
