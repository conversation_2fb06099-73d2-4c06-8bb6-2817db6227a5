package com.westcatr.rd.testbusiness.configs;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.List;

@MapperScan("com.westcatr.rd.boot.orm.mapper.mapper")
@ComponentScan
@Configuration
@EnableTransactionManagement
public class LsMybatisPlusConfig {

    @Bean(name = "westcatrMybatisPlusInterceptor")
    @ConditionalOnMissingBean(name = "westcatrMybatisPlusInterceptor")
    public MybatisPlusInterceptor westcatrMybatisPlusInterceptor(List<InnerInterceptor> innerInterceptors) {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        if (CollUtil.isNotEmpty(innerInterceptors)) {
            innerInterceptors.sort(AnnotationAwareOrderComparator.INSTANCE);
            for (InnerInterceptor innerInterceptor : innerInterceptors) {
                interceptor.addInnerInterceptor(innerInterceptor);
            }
        }
        //这是分页拦截器
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        return interceptor;
    }
}
