package com.westcatr.rd.testbusiness.configs;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <p>
 * 数据同步配置类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "data.sync")
public class DataSyncConfig {

    /**
     * 是否启用数据同步
     */
    private boolean enabled = true;

    /**
     * 批量处理大小
     */
    private int batchSize = 1000;

    /**
     * 同步间隔（秒）
     */
    private int intervalSeconds = 2;

    /**
     * 最大重试次数
     */
    private int maxRetryTimes = 3;

    /**
     * 重试延迟（秒）
     */
    private int retryDelaySeconds = 5;

    /**
     * Redis缓存键前缀
     */
    private String redisKeyPrefix = "data:sync:";

    /**
     * 同步状态缓存键
     */
    private String syncStatusKey = "sync:status";

    /**
     * 最后同步时间缓存键
     */
    private String lastSyncTimeKey = "last:sync:time";

    /**
     * 同步统计缓存键
     */
    private String syncStatsKey = "sync:stats";

    /**
     * 时区转换配置
     */
    private TimezoneConversion timezoneConversion = new TimezoneConversion();

    /**
     * PostgreSQL时区设置
     */
    private String postgresqlTimezone = "Asia/Shanghai";

    /**
     * MySQL时区设置
     */
    private String mysqlTimezone = "Asia/Shanghai";

    @Data
    public static class TimezoneConversion {
        /**
         * 是否启用时区转换
         */
        private boolean enabled = false;
    }
}
