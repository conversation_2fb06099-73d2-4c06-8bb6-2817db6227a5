package com.westcatr.rd.testbusiness.configs;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.OrgUserInfoVO;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * ChangeUserAvatar
 */
@Log4j2
@Component
public class ChangeUserAvatar implements ApplicationRunner {

    @Value("${westcatr.boot.file.upload-folder}")
    private String uploadFolder;

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private BaseDao baseDao;

   /* @Lazy
    @Autowired
    private TestService userService;*/

    @Override
    public void run(ApplicationArguments args) throws Exception {
        QueryWrapper<OrgUserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id")
                .isNotNull("user_sign_info");
        List<OrgUserInfo> users = baseDao.selectList(OrgUserInfo.class, queryWrapper);
/*
        List<OrgUserInfo> users = orgUserInfoService.list(new LambdaQueryWrapper<>(OrgUserInfo.class).select(OrgUserInfo::getId, OrgUserInfo::getuser));*/

        List<String> resulta = new ArrayList<>();
        for (OrgUserInfo sync : users) {
            resulta.add(sync.getId() + ".jpg");
        }
        AssociationQuery<OrgUserInfoVO> associationQuery = new AssociationQuery<>(OrgUserInfoVO.class);
        List<OrgUserInfoVO> list = (associationQuery.voList(associationQuery));
        String path = uploadFolder + "avatar";
        for (String i : resulta) {
            // 创建文件对象
            File file1 = new File(path + "\\" + i);
            if (file1.exists()) {
                boolean isDeleted1 = file1.delete();
            }
        }


        // 判断文件夹是否存在，不存在则创建
        File folder = new File(path);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        for (OrgUserInfoVO orgUserInfo : list) {
            if (StrUtil.isNotBlank(orgUserInfo.getUserSignInfo())) {
                String filePath = path + "/" + orgUserInfo.getId() + ".jpg";
                if (!FileUtil.exist(filePath)) {
                    String result = orgUserInfo.getUserSignInfo();
                    int commaIndex = result.indexOf(",");
                    if (commaIndex != -1) {
                        result = result.substring(commaIndex + 1);
                    }

                    byte[] imageBytes = Base64.getDecoder().decode(result);
                    File output = new File(filePath);
                    try (FileOutputStream fos = new FileOutputStream(output)) {
                        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
                        ImageIO.write(image, "png", fos);
                        // 构建更新条件
                        UpdateWrapper<OrgUserInfo> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq("id", orgUserInfo.getId());
                        updateWrapper.set("tf_path_sign", true);
                        orgUserInfoService.update(updateWrapper);
                        List<Long> idList = users.stream()
                                .map(OrgUserInfo::getId)
                                .toList();
                        for (Long a : idList) {
                            LambdaUpdateWrapper<OrgUserInfo> lambdaUpdateWrappera = new LambdaUpdateWrapper<>();
                            lambdaUpdateWrappera
                                    .set(OrgUserInfo::getTfPathSign, 1)
                                    .eq(OrgUserInfo::getId, a);
                            orgUserInfoService.update(lambdaUpdateWrappera);
                        }
                    } catch (IOException e) {
                        log.error(e.getMessage());
                    }
                }
            }
        }
        Console.log("用户头像转换完成");
    }
}