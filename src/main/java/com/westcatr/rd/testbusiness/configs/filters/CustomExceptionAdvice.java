package com.westcatr.rd.testbusiness.configs.filters;

import com.westcatr.rd.boot.core.vo.IResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static org.springframework.core.Ordered.HIGHEST_PRECEDENCE;

/**
 * <AUTHOR>
 * @date 2024/10/16 11:45
 */
@ControllerAdvice
@Order(value = HIGHEST_PRECEDENCE)
@Slf4j
public class CustomExceptionAdvice {

    @ExceptionHandler(value = ClientAbortException.class)
    public IResult<Void> handleClientAbortException(HttpServletRequest request, HttpServletResponse response) {
        log.info("Client abort exception");
        return IResult.ok();
    }

    @ExceptionHandler(value = HttpMessageNotWritableException.class)
    public IResult<Void> handleHttpMessageNotWritableException(HttpServletRequest request, HttpServletResponse response) {
        log.info("HttpMessageNotWritableException");
        return IResult.ok();
    }


}
