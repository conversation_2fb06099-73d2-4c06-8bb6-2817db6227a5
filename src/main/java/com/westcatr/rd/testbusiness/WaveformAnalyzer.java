package com.westcatr.rd.testbusiness;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Point;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;

public class WaveformAnalyzer {
    static {
        // 静态块中设置库路径，确保在任何代码执行前就设置好
        // 为Mac OS设置相关JNA库路径
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("mac")) {
            // Tesseract库路径
            System.setProperty("jna.library.path", "/opt/homebrew/Cellar/tesseract/5.5.0_1/lib:/opt/homebrew/lib");
            System.setProperty("jna.platform.library.path",
                    "/opt/homebrew/Cellar/tesseract/5.5.0_1/lib:/opt/homebrew/lib");
            System.setProperty("jna.darwin.library.path",
                    "/opt/homebrew/Cellar/tesseract/5.5.0_1/lib:/opt/homebrew/lib");

            // 设置Leptonica库路径 - 很关键，解决leptonica库加载问题
            System.setProperty("lept.library.path", "/opt/homebrew/lib");
            // 设置临时目录
            System.setProperty("java.io.tmpdir", "/tmp");

            // 直接设置JNA库搜索路径，提高库的查找成功率
            System.setProperty("jna.library.path",
                    "/opt/homebrew/lib:" +
                            "/opt/homebrew/Cellar/leptonica/1.85.0/lib:" +
                            "/opt/homebrew/Cellar/tesseract/5.5.0_1/lib:");
        }
    }

    public static void main(String[] args) {
        try {
            // 加载图像
            File imageFile = new File("/Users/<USER>/Downloads/雷冲结果数据/20240611--150635.bmp");
            BufferedImage image = ImageIO.read(imageFile);

            // 添加调试信息：采样图像多个位置的颜色
            System.out.println("\n🔍 图像颜色采样:");
            // 采样图像中心位置
            int centerX = image.getWidth() / 2;
            int centerY = image.getHeight() / 2;
            sampleImageColors(image, centerX, centerY, "图像中心");

            // 采样黄色曲线可能的位置
            sampleImageColors(image, image.getWidth() / 4, centerY, "可能的曲线位置1");
            sampleImageColors(image, image.getWidth() / 3, image.getHeight() / 3, "可能的曲线位置2");

            // 检测白线位置
            int whiteLineX = findWhiteLine(image);
            if (whiteLineX > 0) {
                System.out.println("\n✅ 检测到白色分隔线位置: " + whiteLineX);
                // 采样白线附近的颜色
                sampleImageColors(image, whiteLineX, centerY, "白线位置");
            } else {
                System.out.println("\n⚠️ 未检测到白色分隔线");
            }

            // 1. 提取所有黄色曲线点
            List<Point> allCurvePoints = extractAllYellowPoints(image);
            System.out.println("找到黄色曲线点总数: " + allCurvePoints.size());

            // 调试信息：打印前10个曲线点的RGB值
            if (!allCurvePoints.isEmpty()) {
                System.out.println("\n🔍 前10个曲线点的RGB值:");
                int count = 0;
                for (Point p : allCurvePoints) {
                    if (count++ >= 10)
                        break;
                    Color color = new Color(image.getRGB(p.x, p.y));
                    System.out.printf("点(%d, %d): R=%d, G=%d, B=%d\n",
                            p.x, p.y, color.getRed(), color.getGreen(), color.getBlue());
                }
            } else {
                System.out.println("\n⚠️ 未找到任何曲线点！尝试进行图像扫描以查找可能的曲线颜色...");
                scanImageForPossibleCurveColors(image);
            }

            // 2. 通过点密度分析找到连续曲线
            List<Point> continuousCurvePoints = findContinuousCurve(allCurvePoints);
            System.out.println("连续曲线点数: " + continuousCurvePoints.size());

            // 3. 找到连续曲线的最右侧点
            Point lastPoint = findLastPointOfCurve(continuousCurvePoints);
            if (lastPoint == null) {
                System.out.println("未能找到有效的曲线点");
                return;
            }

            // 打印箭头指向的最右侧点信息
            Color pointColor = new Color(image.getRGB(lastPoint.x, lastPoint.y));
            System.out.println("\n🎯 波形最右侧点信息:");
            System.out.printf("坐标: (%d, %d), RGB值: R=%d, G=%d, B=%d\n",
                    lastPoint.x, lastPoint.y,
                    pointColor.getRed(), pointColor.getGreen(), pointColor.getBlue());

            // 计算坐标比值 y/x
            double coordinateRatio = (double) lastPoint.y / lastPoint.x;
            System.out.println("\n📊 坐标比值计算:");
            System.out.printf("y/x = %d/%d = %.6f\n", lastPoint.y, lastPoint.x, coordinateRatio);

            // 5. 确定基线位置（仅用于可视化）
            int baselineY = findBaselineY(image);

            // 7. 可视化结果
            BufferedImage resultImage = visualizeResult(image, allCurvePoints, continuousCurvePoints, lastPoint,
                    baselineY, coordinateRatio);
            File outputFile = new File("waveform_analysis_improved.png");
            ImageIO.write(resultImage, "png", outputFile);

            // 8. 输出详细结果
            System.out.println("\n🔍 波形分析结果:");
            System.out.println("图像尺寸: " + image.getWidth() + " x " + image.getHeight());
            System.out.println("曲线最右侧点坐标: (" + lastPoint.x + ", " + lastPoint.y + ")");
            System.out.println("坐标比值(y/x): " + String.format("%.6f", coordinateRatio));
            System.out.println("结果已保存到 " + outputFile.getAbsolutePath());

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 提取图像中所有的黄色像素点
     */
    private static List<Point> extractAllYellowPoints(BufferedImage image) {
        List<Point> points = new ArrayList<>();

        // 计算主网格区域的范围 - 曲线只出现在左侧部分
        // 右侧信息区域约占图像宽度的20%，进一步限制为75%以确保安全
        int gridAreaWidth = (int) (image.getWidth() * 0.75);

        // 确定右侧文字区域的边界位置 - 通过扫描右侧区域的白线
        int whiteLineX = findWhiteLine(image);
        if (whiteLineX > 0) {
            // 如果找到白线，使用白线位置作为边界
            gridAreaWidth = whiteLineX - 10; // 留出安全距离
            System.out.println("检测到白色边界线位置: " + whiteLineX);
        }

        System.out.println("识别限制在宽度 " + gridAreaWidth + " 像素以内的区域");

        // 扫描图像左侧区域
        for (int x = 0; x < gridAreaWidth; x++) { // 只扫描主网格区域
            for (int y = 0; y < image.getHeight(); y++) {
                // 跳过图像最上部的刻度显示区域
                if (y < image.getHeight() * 0.1)
                    continue;

                // 跳过图像最下部的刻度显示区域
                if (y > image.getHeight() * 0.9)
                    continue;

                Color color = new Color(image.getRGB(x, y));

                // 检测黄色曲线
                if (isYellowColor(color)) {
                    points.add(new Point(x, y));
                }
            }
        }

        return points;
    }

    /**
     * 查找图像中的白色垂直分隔线位置
     */
    private static int findWhiteLine(BufferedImage image) {
        // 从图像宽度的70%处开始向右扫描，查找白色垂直线
        int startX = (int) (image.getWidth() * 0.7);
        int endX = (int) (image.getWidth() * 0.85);
        int middleY = image.getHeight() / 2;

        for (int x = startX; x < endX; x++) {
            // 计算当前列的白色像素数量
            int whitePixelCount = 0;

            // 采样图像中部区域的像素
            for (int y = (int) (image.getHeight() * 0.3); y < image.getHeight() * 0.7; y++) {
                Color color = new Color(image.getRGB(x, y));
                // 检查是否是白色或近白色像素
                if (isWhiteOrBrightPixel(color)) {
                    whitePixelCount++;
                }
            }

            // 如果白色像素数量超过采样高度的60%，认为找到了白线
            if (whitePixelCount > (image.getHeight() * 0.4 * 0.6)) {
                return x;
            }
        }

        // 如果没找到，返回-1
        return -1;
    }

    /**
     * 判断像素是否为白色或亮色
     */
    private static boolean isWhiteOrBrightPixel(Color color) {
        // 白色或亮色的特征：所有通道都很高
        int threshold = 200;
        return color.getRed() > threshold &&
                color.getGreen() > threshold &&
                color.getBlue() > threshold;
    }

    /**
     * 判断像素是否为黄色（波形曲线的颜色）
     */
    private static boolean isYellowColor(Color color) {
        // 黄色特征：高红、高绿、低蓝
        int redThreshold = 120; // 红色通道需要较高
        int greenThreshold = 120; // 绿色通道需要较高
        int blueThreshold = 100; // 蓝色通道需要较低

        // 红绿通道的相对差值不应太大，因为黄色是红+绿
        int redGreenMaxDiff = 50;

        // 基本条件：红和绿都要高，蓝要低，红绿差不多
        return color.getRed() > redThreshold &&
                color.getGreen() > greenThreshold &&
                color.getBlue() < blueThreshold &&
                Math.abs(color.getRed() - color.getGreen()) < redGreenMaxDiff &&
                // 确保红色或绿色与蓝色有足够差距
                (color.getRed() - color.getBlue() > 50 ||
                        color.getGreen() - color.getBlue() > 50);
    }

    /**
     * 判断像素是否为曲线颜色（青绿色）
     * 注意：此方法已被isYellowColor替代，保留作为参考
     */
    private static boolean isCurveColor(Color color) {
        // 大幅放宽颜色条件以捕获更多可能的曲线点
        // 青绿色特征：中低红、中高绿、中等蓝
        int redThreshold = 150; // 红色可以更高
        int greenThreshold = 100; // 绿色要求降低
        int blueThreshold = 150; // 蓝色要求放宽

        // 降低绿色与其他颜色差值要求
        int greenRedDiff = 30; // 绿色比红色的差值降低

        // 基本条件：绿色要比红色高，并且绿色要足够明显
        return color.getGreen() > greenThreshold &&
                (color.getGreen() > color.getRed()) &&
                (color.getRed() < redThreshold) &&
                (color.getBlue() < blueThreshold);
    }

    /**
     * 找到连续的曲线点
     * 策略：基于点的密度和连续性来判断
     */
    private static List<Point> findContinuousCurve(List<Point> allPoints) {
        if (allPoints.isEmpty()) {
            return Collections.emptyList();
        }

        // 按x坐标排序
        List<Point> sortedPoints = new ArrayList<>(allPoints);
        Collections.sort(sortedPoints, Comparator.comparingInt(p -> p.x));

        // 对每个x坐标，找到对应的y值（可能有多个）
        Map<Integer, List<Integer>> xToYsMap = new HashMap<>();

        for (Point p : sortedPoints) {
            if (!xToYsMap.containsKey(p.x)) {
                xToYsMap.put(p.x, new ArrayList<>());
            }
            xToYsMap.get(p.x).add(p.y);
        }

        // 对于每个x坐标，选择y值的中位数作为曲线的代表点
        // 这有助于过滤掉噪点
        List<Point> curvePoints = new ArrayList<>();

        for (Map.Entry<Integer, List<Integer>> entry : xToYsMap.entrySet()) {
            int x = entry.getKey();
            List<Integer> yValues = entry.getValue();

            // 按y坐标排序
            Collections.sort(yValues);

            // 取中值作为该x坐标下的y值
            int medianY = yValues.get(yValues.size() / 2);

            curvePoints.add(new Point(x, medianY));
        }

        // 再次按x坐标排序
        Collections.sort(curvePoints, Comparator.comparingInt(p -> p.x));

        return curvePoints;
    }

    /**
     * 找到连续曲线的最后一个点
     * 策略：从最右边往左，找到第一个密度足够的点作为波形的终点
     */
    private static Point findLastPointOfCurve(List<Point> curvePoints) {
        if (curvePoints.isEmpty()) {
            return null;
        }

        // 已排序的点集，直接取最后一个点
        return curvePoints.get(curvePoints.size() - 1);
    }

    /**
     * 计算基线位置（示波器的零位线，仅用于可视化）
     */
    private static int findBaselineY(BufferedImage image) {
        // 对于示波器图像，基线通常是中间的网格线
        return image.getHeight() / 2;
    }

    /**
     * 可视化分析结果
     */
    private static BufferedImage visualizeResult(BufferedImage original,
            List<Point> allPoints,
            List<Point> curvePoints,
            Point lastPoint,
            int baselineY) {
        return visualizeResult(original, allPoints, curvePoints, lastPoint, baselineY, 0.0);
    }

    /**
     * 可视化分析结果（包含坐标比值）
     */
    private static BufferedImage visualizeResult(BufferedImage original,
            List<Point> allPoints,
            List<Point> curvePoints,
            Point lastPoint,
            int baselineY,
            double coordinateRatio) {
        // 创建副本
        BufferedImage result = new BufferedImage(
                original.getWidth(), original.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics2D g = result.createGraphics();
        g.drawImage(original, 0, 0, null);

        // 确定右侧文字区域的边界位置
        int whiteLineX = findWhiteLine(original);
        int gridAreaWidth = whiteLineX > 0 ? whiteLineX - 10 : (int) (original.getWidth() * 0.75);

        // 绘制主网格区域边界（白线）
        g.setColor(Color.RED);
        g.drawLine(gridAreaWidth, 0, gridAreaWidth, original.getHeight());
        g.drawString("识别区域边界", gridAreaWidth - 100, 20);

        // 绘制基线
        g.setColor(Color.BLUE);
        g.drawLine(0, baselineY, gridAreaWidth, baselineY);

        // 显示所有曲线点
        g.setColor(new Color(0, 255, 0, 100)); // 绿色，半透明
        for (Point p : allPoints) {
            g.fillOval(p.x - 1, p.y - 1, 3, 3);
        }

        // 显示所有连续曲线点
        g.setColor(new Color(0, 255, 255, 100)); // 淡青色，半透明
        for (Point p : curvePoints) {
            g.fillOval(p.x - 2, p.y - 2, 5, 5);
        }

        // 特别标记出最右侧点
        if (lastPoint != null) {
            g.setColor(Color.RED);
            // 使用更大更明显的圆点标记
            g.fillOval(lastPoint.x - 8, lastPoint.y - 8, 16, 16);

            // 添加箭头效果指向该点
            int arrowLength = 40;
            g.drawLine(lastPoint.x - arrowLength, lastPoint.y - arrowLength, lastPoint.x - 4, lastPoint.y - 4);
            // 绘制箭头尖端
            int[] xPoints = { lastPoint.x - 4, lastPoint.x - 14, lastPoint.x - 10 };
            int[] yPoints = { lastPoint.y - 4, lastPoint.y - 10, lastPoint.y - 14 };
            g.fillPolygon(xPoints, yPoints, 3);

            // 画一条从基线到最右侧点的垂直线
            g.setColor(Color.GREEN);
            g.drawLine(lastPoint.x, baselineY, lastPoint.x, lastPoint.y);

            // 添加坐标信息标签，使其更突出
            g.setColor(Color.WHITE);
            String coordText = String.format("点坐标: (%d, %d)", lastPoint.x, lastPoint.y);
            g.drawString(coordText, lastPoint.x - 120, lastPoint.y - 25);

            // 添加坐标比值信息
            String ratioText = String.format("坐标比值(y/x): %.6f", coordinateRatio);
            g.drawString(ratioText, lastPoint.x - 120, lastPoint.y - 5);

            // 添加计算公式
            String formulaText = String.format("%d/%d = %.6f", lastPoint.y, lastPoint.x, coordinateRatio);
            g.drawString(formulaText, lastPoint.x - 120, lastPoint.y + 15);

            // 添加波形终点标记
            g.setColor(new Color(255, 255, 0, 200)); // 半透明黄色
            g.drawString("⬅ 波形终点", lastPoint.x + 15, lastPoint.y);

            // 添加明显的红框
            g.setColor(new Color(255, 0, 0, 180));
            g.drawRect(lastPoint.x - 15, lastPoint.y - 15, 30, 30);
        }

        g.dispose();
        return result;
    }

    /**
     * 采样图像指定位置周围的颜色
     */
    private static void sampleImageColors(BufferedImage image, int centerX, int centerY, String location) {
        System.out.println("\n🎯 " + location + "周围的颜色样本:");

        // 在中心点周围采样5x5的区域
        for (int y = Math.max(0, centerY - 2); y <= Math.min(image.getHeight() - 1, centerY + 2); y++) {
            for (int x = Math.max(0, centerX - 2); x <= Math.min(image.getWidth() - 1, centerX + 2); x++) {
                Color color = new Color(image.getRGB(x, y));
                System.out.printf("位置(%d, %d): R=%d, G=%d, B=%d\n",
                        x, y, color.getRed(), color.getGreen(), color.getBlue());
            }
        }
    }

    /**
     * 扫描图像以寻找可能的曲线颜色
     */
    private static void scanImageForPossibleCurveColors(BufferedImage image) {
        // 计算主网格区域的范围
        int gridAreaWidth = (int) (image.getWidth() * 0.8);

        System.out.println("\n🔍 扫描图像中可能的曲线颜色...");

        // 用于记录不同颜色的计数
        Map<String, Integer> colorCounts = new HashMap<>();
        Map<String, Point> colorPositions = new HashMap<>();

        // 设置扫描间隔，避免扫描每个像素
        int scanInterval = 10;

        // 扫描图像主区域
        for (int x = 0; x < gridAreaWidth; x += scanInterval) {
            for (int y = (int) (image.getHeight() * 0.1); y < image.getHeight() * 0.9; y += scanInterval) {
                Color color = new Color(image.getRGB(x, y));

                // 关注绿色占比较高的颜色
                if (color.getGreen() > color.getRed() && color.getGreen() > color.getBlue()) {
                    // 创建颜色标识符
                    String colorId = String.format("R=%d,G=%d,B=%d",
                            color.getRed(), color.getGreen(), color.getBlue());

                    // 更新计数
                    colorCounts.put(colorId, colorCounts.getOrDefault(colorId, 0) + 1);

                    // 保存位置信息
                    if (!colorPositions.containsKey(colorId)) {
                        colorPositions.put(colorId, new Point(x, y));
                    }
                }
            }
        }

        // 根据频率排序并输出前10种最可能的曲线颜色
        List<Map.Entry<String, Integer>> sortedColors = new ArrayList<>(colorCounts.entrySet());
        sortedColors.sort((e1, e2) -> e2.getValue().compareTo(e1.getValue()));

        System.out.println("\n🌈 前10种最可能的曲线颜色:");
        int count = 0;
        for (Map.Entry<String, Integer> entry : sortedColors) {
            if (count++ >= 10)
                break;

            String colorId = entry.getKey();
            int frequency = entry.getValue();
            Point position = colorPositions.get(colorId);

            System.out.printf("颜色: %s, 频率: %d, 样本位置: (%d, %d)\n",
                    colorId, frequency, position.x, position.y);
        }
    }
}
