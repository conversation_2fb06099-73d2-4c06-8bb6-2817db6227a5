package com.westcatr.rd.testbusiness.example;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.westcatr.rd.testbusiness.poitl.MeasurementWindingResistanceTablePolicy;
import com.westcatr.rd.testbusiness.poitl.entity.MeasurementWindingResistanceData;
import com.westcatr.rd.testbusiness.utils.WindingResistanceTableHelper;

/**
 * 绕组电阻表格动态渲染示例
 */
public class WindingResistanceTableExample {

    public static void main(String[] args) throws IOException {
        // 配置模板策略
        Configure config = Configure.builder()
                .bind("winding_resistance_table", new MeasurementWindingResistanceTablePolicy())
                .build();

        // 构建数据模型
        Map<String, Object> dataModel = new HashMap<>();

        // 使用辅助类创建示例数据
        MeasurementWindingResistanceData resistanceData = WindingResistanceTableHelper.buildExampleData();
        dataModel.put("winding_resistance_table", resistanceData);

        // 加载Word模板并渲染
        XWPFTemplate template = XWPFTemplate.compile("templates/winding_resistance_template.docx", config)
                .render(dataModel);

        // 保存渲染后的文档
        try (FileOutputStream out = new FileOutputStream("output/winding_resistance_result.docx")) {
            template.write(out);
        }

        // 关闭模板
        template.close();

        System.out.println("✅ 绕组电阻表格渲染完成！文档已保存至: output/winding_resistance_result.docx");
    }

    /**
     * 下面是模板中表格的示例结构：
     * 
     * {{winding_resistance_table}}
     * +--------+--------+---------------------+----------+-------------------+
     * | 绕组类型 | 分接位置 | 测量电阻值 | 最大不平衡率 | 不平衡允许电阻(%) |
     * | | +-----+-----+-----+ | (%) | |
     * | | | RAB | Rec | Ra | | | |
     * +--------+--------+-----+-----+-----+--+----------+-------------------+
     * | 高压 | — | | | | | | |
     * +--------+--------+-----+-----+-----+--+----------+-------------------+
     * | 低压 | — | | | | | | |
     * +--------+--------+-----+-----+-----+--+----------+-------------------+
     * 
     * 模板中需要使用{{winding_resistance_table}}作为表格标签
     */
}