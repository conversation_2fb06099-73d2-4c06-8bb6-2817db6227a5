package com.westcatr.rd.testbusiness;

import static com.westcatr.rd.boot.web.WebBaseStarterConfig.PATH_LIST;

import java.security.KeyPair;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.westcatr.rd.boot.security.service.impl.AbstractPasswordHandlerImpl;
import com.westcatr.rd.boot.web.util.PackageUtil;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.asymmetric.SM2;

@EnableAsync
@EnableScheduling
@EnableCaching
@SpringBootApplication
@MapperScan(basePackages = { "com.westcatr.rd.testbusiness.business.sample.mapper",
        "com.westcatr.rd.testbusiness.business.gwstandard.mapper",
        "com.westcatr.rd.testbusiness.business.jztask.mapper",
        "com.westcatr.rd.testbusiness.business.jzreport.mapper",
        "com.westcatr.rd.testbusiness.business.datasync.mapper",
        "com.westcatr.rd.testbusiness.business.**.mapper" })
public class TestBusinessBackApplication {

    public static void main(String[] args) {
        // 解决 MyBatis-Plus Lambda 反射访问问题
        System.setProperty("java.lang.invoke.stringConcat", "BC_SB");

        // SM2 算法需要依赖 BouncyCastle 库，所以需要先加载 BouncyCastle 库
        KeyPair sm2 = SecureUtil.generateKeyPair("SM2");
        AbstractPasswordHandlerImpl.MY_SM2 = new SM2(sm2.getPrivate(), sm2.getPublic());
        // RSA 算法不需要依赖 BouncyCastle 库，禁用 BouncyCastle 库，否则会报错 JCE cannot authenticate
        // the provider BC 的问题
        SecureUtil.disableBouncyCastle();
        AbstractPasswordHandlerImpl.RSA_ = new RSA();
        PATH_LIST.addAll(PackageUtil.getAllController(TestBusinessBackApplication.class.getPackage().getName()));
        SpringApplication.run(TestBusinessBackApplication.class, args);
    }

}
