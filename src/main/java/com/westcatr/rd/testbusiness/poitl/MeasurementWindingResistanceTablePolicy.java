package com.westcatr.rd.testbusiness.poitl;

import java.util.List;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.westcatr.rd.testbusiness.poitl.entity.MeasurementWindingResistanceData;
import com.westcatr.rd.testbusiness.poitl.entity.WindingResistanceRow;

public class MeasurementWindingResistanceTablePolicy extends DynamicTableRenderPolicy {

    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        if (null == data)
            return;

        MeasurementWindingResistanceData resistanceData = (MeasurementWindingResistanceData) data;

        // 处理表格中的特殊标签
        processSpecialTags(table, resistanceData);

        // 高压绕组行索引和低压绕组行索引
        int highVoltageRowIndex = -1;
        int lowVoltageRowIndex = -1;

        // 查找高压行和低压行的位置
        for (int i = 0; i < table.getNumberOfRows(); i++) {
            XWPFTableRow row = table.getRow(i);
            if (row.getTableCells().size() > 0) {
                String cellText = row.getCell(0).getText();
                if (cellText != null) {
                    if (cellText.contains("高压")) {
                        highVoltageRowIndex = i;
                    } else if (cellText.contains("低压")) {
                        lowVoltageRowIndex = i;
                    }
                }
            }
        }

        // 确保找到了高压和低压行
        if (highVoltageRowIndex < 0 || lowVoltageRowIndex < 0) {
            return; // 模板中没有找到高压或低压行，不能处理
        }

        // 渲染高压绕组数据
        List<WindingResistanceRow> highVoltageRows = resistanceData.getHighVoltageRows();
        if (highVoltageRows != null && !highVoltageRows.isEmpty()) {
            // 提取高压行作为模板
            XWPFTableRow templateRow = table.getRow(highVoltageRowIndex);

            // 如果模板行后面已有数据行，先删除这些行
            int rowsToRemove = table.getRow(highVoltageRowIndex + 1).getCell(0).getText().contains("低压") ? 0
                    : (lowVoltageRowIndex - highVoltageRowIndex - 1);
            for (int i = 0; i < rowsToRemove; i++) {
                table.removeRow(highVoltageRowIndex + 1);
            }

            // 在高压行的位置插入新数据行
            for (int i = 0; i < highVoltageRows.size(); i++) {
                WindingResistanceRow rowData = highVoltageRows.get(i);
                XWPFTableRow dataRow;

                if (i == 0) {
                    // 第一行使用模板行
                    dataRow = templateRow;

                    // 设置第一行的标记为RESTART，表示开始一个新的垂直合并
                    if (dataRow.getCell(0).getCTTc().getTcPr() == null) {
                        dataRow.getCell(0).getCTTc().addNewTcPr();
                    }
                    dataRow.getCell(0).getCTTc().getTcPr().addNewVMerge()
                            .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

                    // 不需要设置"高压(Ω)"文本，因为模板已经有了
                    // dataRow.getCell(0).setText("高压(Ω)");

                    // 为最大不平衡率设置合并标记
                    if (dataRow.getTableCells().size() > 5) {
                        XWPFTableCell unbalanceCell = dataRow.getCell(5);
                        if (unbalanceCell.getCTTc().getTcPr() == null) {
                            unbalanceCell.getCTTc().addNewTcPr();
                        }
                        unbalanceCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
                    }

                    // 为不平衡率允许值设置合并标记
                    if (dataRow.getTableCells().size() > 6) {
                        XWPFTableCell permissibleCell = dataRow.getCell(6);
                        if (permissibleCell.getCTTc().getTcPr() == null) {
                            permissibleCell.getCTTc().addNewTcPr();
                        }
                        permissibleCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
                    }
                } else {
                    // 其他行插入新行
                    dataRow = table.insertNewTableRow(highVoltageRowIndex + i);

                    // 复制单元格和样式
                    for (int j = 0; j < templateRow.getTableCells().size(); j++) {
                        XWPFTableCell newCell = dataRow.createCell();
                        XWPFTableCell templateCell = templateRow.getCell(j);
                        // 复制样式但避免直接复制CTTcPr对象
                        if (templateCell.getCTTc().getTcPr() != null) {
                            // 复制单元格属性
                            if (newCell.getCTTc().getTcPr() == null) {
                                newCell.getCTTc().addNewTcPr();
                            }

                            // 复制宽度设置
                            if (templateCell.getWidth() > 0) {
                                newCell.setWidth(String.valueOf(templateCell.getWidth()));
                            }

                            // 复制垂直对齐方式
                            if (templateCell.getVerticalAlignment() != null) {
                                newCell.setVerticalAlignment(templateCell.getVerticalAlignment());
                            }

                            // 复制背景颜色
                            if (templateCell.getColor() != null) {
                                newCell.setColor(templateCell.getColor());
                            }
                        }
                    }

                    // 设置高压单元格继续合并
                    XWPFTableCell firstCell = dataRow.getCell(0);
                    firstCell.setText(""); // 空文本，因为是合并单元格
                    if (firstCell.getCTTc().getTcPr() == null) {
                        firstCell.getCTTc().addNewTcPr();
                    }
                    firstCell.getCTTc().getTcPr().addNewVMerge()
                            .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

                    // 为最大不平衡率单元格继续合并
                    if (dataRow.getTableCells().size() > 5) {
                        XWPFTableCell unbalanceCell = dataRow.getCell(5);
                        if (unbalanceCell.getCTTc().getTcPr() == null) {
                            unbalanceCell.getCTTc().addNewTcPr();
                        }
                        unbalanceCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
                    }

                    // 为不平衡率允许值单元格继续合并
                    if (dataRow.getTableCells().size() > 6) {
                        XWPFTableCell permissibleCell = dataRow.getCell(6);
                        if (permissibleCell.getCTTc().getTcPr() == null) {
                            permissibleCell.getCTTc().addNewTcPr();
                        }
                        permissibleCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
                    }
                }

                // 填充数据
                int cellOffset = 1; // 从第二列开始填充数据（第一列是高压(Ω)）

                // 填充分接位置
                if (rowData.getPosition() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getPosition());
                }
                cellOffset++;

                // 填充RAB
                if (rowData.getRab() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getRab());
                }
                cellOffset++;

                // 填充Rec
                if (rowData.getRec() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getRec());
                }
                cellOffset++;

                // 填充Ra
                if (rowData.getRa() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getRa());
                }

                // 最大不平衡率和允许值只在第一行设置，且不直接设置文本，通过标签替换处理
                // 这些值已经在processSpecialTags方法中处理了
            }

            // 更新低压行索引
            lowVoltageRowIndex = highVoltageRowIndex + highVoltageRows.size();
        }

        // 渲染低压绕组数据
        List<WindingResistanceRow> lowVoltageRows = resistanceData.getLowVoltageRows();
        if (lowVoltageRows != null && !lowVoltageRows.isEmpty()) {
            // 提取低压行作为模板
            XWPFTableRow templateRow = table.getRow(lowVoltageRowIndex);

            // 如果高压行后面还有其他数据行，先删除这些行
            int remainingRows = table.getNumberOfRows() - lowVoltageRowIndex - 1;
            for (int i = 0; i < remainingRows; i++) {
                table.removeRow(lowVoltageRowIndex + 1);
            }

            // 在低压行位置插入新数据行
            for (int i = 0; i < lowVoltageRows.size(); i++) {
                WindingResistanceRow rowData = lowVoltageRows.get(i);
                XWPFTableRow dataRow;

                if (i == 0) {
                    // 第一行使用模板行
                    dataRow = templateRow;

                    // 设置第一行的标记为RESTART，表示开始一个新的垂直合并
                    if (dataRow.getCell(0).getCTTc().getTcPr() == null) {
                        dataRow.getCell(0).getCTTc().addNewTcPr();
                    }
                    dataRow.getCell(0).getCTTc().getTcPr().addNewVMerge()
                            .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);

                    // 不需要设置"低压(mΩ)"文本，因为模板已经有了
                    // dataRow.getCell(0).setText("低压(mΩ)");

                    // 为最大不平衡率设置合并标记
                    if (dataRow.getTableCells().size() > 5) {
                        XWPFTableCell unbalanceCell = dataRow.getCell(5);
                        if (unbalanceCell.getCTTc().getTcPr() == null) {
                            unbalanceCell.getCTTc().addNewTcPr();
                        }
                        unbalanceCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
                    }

                    // 为不平衡率允许值设置合并标记
                    if (dataRow.getTableCells().size() > 6) {
                        XWPFTableCell permissibleCell = dataRow.getCell(6);
                        if (permissibleCell.getCTTc().getTcPr() == null) {
                            permissibleCell.getCTTc().addNewTcPr();
                        }
                        permissibleCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
                    }
                } else {
                    // 其他行插入新行
                    dataRow = table.insertNewTableRow(lowVoltageRowIndex + i);

                    // 复制单元格和样式
                    for (int j = 0; j < templateRow.getTableCells().size(); j++) {
                        XWPFTableCell newCell = dataRow.createCell();
                        XWPFTableCell templateCell = templateRow.getCell(j);
                        // 复制样式但避免直接复制CTTcPr对象
                        if (templateCell.getCTTc().getTcPr() != null) {
                            // 复制单元格属性
                            if (newCell.getCTTc().getTcPr() == null) {
                                newCell.getCTTc().addNewTcPr();
                            }

                            // 复制宽度设置
                            if (templateCell.getWidth() > 0) {
                                newCell.setWidth(String.valueOf(templateCell.getWidth()));
                            }

                            // 复制垂直对齐方式
                            if (templateCell.getVerticalAlignment() != null) {
                                newCell.setVerticalAlignment(templateCell.getVerticalAlignment());
                            }

                            // 复制背景颜色
                            if (templateCell.getColor() != null) {
                                newCell.setColor(templateCell.getColor());
                            }
                        }
                    }

                    // 设置低压单元格继续合并
                    XWPFTableCell firstCell = dataRow.getCell(0);
                    firstCell.setText(""); // 空文本，因为是合并单元格
                    if (firstCell.getCTTc().getTcPr() == null) {
                        firstCell.getCTTc().addNewTcPr();
                    }
                    firstCell.getCTTc().getTcPr().addNewVMerge()
                            .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

                    // 为最大不平衡率单元格继续合并
                    if (dataRow.getTableCells().size() > 5) {
                        XWPFTableCell unbalanceCell = dataRow.getCell(5);
                        if (unbalanceCell.getCTTc().getTcPr() == null) {
                            unbalanceCell.getCTTc().addNewTcPr();
                        }
                        unbalanceCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
                    }

                    // 为不平衡率允许值单元格继续合并
                    if (dataRow.getTableCells().size() > 6) {
                        XWPFTableCell permissibleCell = dataRow.getCell(6);
                        if (permissibleCell.getCTTc().getTcPr() == null) {
                            permissibleCell.getCTTc().addNewTcPr();
                        }
                        permissibleCell.getCTTc().getTcPr().addNewVMerge()
                                .setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);
                    }
                }

                // 填充数据
                int cellOffset = 1; // 从第二列开始填充数据（第一列是低压(mΩ)）

                // 填充分接位置
                if (rowData.getPosition() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getPosition());
                }
                cellOffset++;

                // 填充RAB
                if (rowData.getRab() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getRab());
                }
                cellOffset++;

                // 填充Rec/Rbe
                if (rowData.getRec() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getRec());
                }
                cellOffset++;

                // 填充Ra/Rea
                if (rowData.getRa() != null && dataRow.getTableCells().size() > cellOffset) {
                    dataRow.getCell(cellOffset).setText(rowData.getRa());
                }

                // 最大不平衡率和允许值只在第一行设置，且不直接设置文本，通过标签替换处理
                // 这些值已经在processSpecialTags方法中处理了
            }
        }
    }

    /**
     * 处理表格中的特殊标签
     * 
     * @param table 表格对象
     * @param data  数据对象
     */
    private void processSpecialTags(XWPFTable table, MeasurementWindingResistanceData data) {
        // 获取高压和低压的不平衡率和允许值
        final String[] tagValues = new String[4]; // 使用数组存储值，使其可以在lambda中使用

        // 从数据对象获取值
        if (data.getHighVoltageRows() != null && !data.getHighVoltageRows().isEmpty()) {
            WindingResistanceRow firstHighRow = data.getHighVoltageRows().get(0);
            tagValues[0] = firstHighRow.getMaxUnbalanceRate(); // 高压最大不平衡率
            tagValues[1] = "<=2"; // 高压允许不平衡率固定值
        } else {
            tagValues[0] = "";
            tagValues[1] = "<=2";
        }

        if (data.getLowVoltageRows() != null && !data.getLowVoltageRows().isEmpty()) {
            WindingResistanceRow firstLowRow = data.getLowVoltageRows().get(0);
            tagValues[2] = firstLowRow.getMaxUnbalanceRate(); // 低压最大不平衡率
            tagValues[3] = "<=2"; // 低压允许不平衡率固定值
        } else {
            tagValues[2] = "";
            tagValues[3] = "<=2";
        }

        // 标签名称
        final String[] tagNames = {
                "{{^^maxUnbalanceRateHighVoltage}}",
                "{{^^permissibleValueHighVoltageUnbalanceRate}}",
                "{{^^maxUnbalanceRateLowVoltage}}",
                "{{^^permissibleValueLowVoltageUnbalanceRate}}"
        };

        // 显式替换所有已知标签
        for (int i = 0; i < table.getNumberOfRows(); i++) {
            XWPFTableRow row = table.getRow(i);
            for (int j = 0; j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getCell(j);

                // 显式处理不平衡率允许值单元格
                if (j == 6) { // 第七列是不平衡率允许值
                    boolean isHighVoltageSection = false;
                    boolean isLowVoltageSection = false;

                    // 检查该行是否属于高压或低压部分
                    if (i > 0 && i < table.getNumberOfRows() - 2) { // 高压部分
                        isHighVoltageSection = true;
                    } else if (i >= table.getNumberOfRows() - 2) { // 低压部分
                        isLowVoltageSection = true;
                    }

                    if (isHighVoltageSection) {
                        clearCellTextAndSetValue(cell, "0.352");
                    } else if (isLowVoltageSection) {
                        clearCellTextAndSetValue(cell, "<=2");
                    }
                }

                // 检查单元格中的所有段落和Run
                cell.getParagraphs().forEach(paragraph -> {
                    List<XWPFRun> runs = paragraph.getRuns();
                    if (runs != null) {
                        for (int k = 0; k < runs.size(); k++) {
                            XWPFRun run = runs.get(k);
                            String text = run.getText(0);
                            if (text != null) {
                                // 替换特殊标签为实际值
                                boolean textChanged = false;
                                for (int t = 0; t < tagNames.length; t++) {
                                    if (text.contains(tagNames[t])) {
                                        text = text.replace(tagNames[t], tagValues[t]);
                                        textChanged = true;
                                    }
                                }

                                // 清除任何剩余的标签
                                if (text.contains("{{")) {
                                    text = text.replaceAll("\\{\\{[^}]+\\}\\}", "");
                                    textChanged = true;
                                }

                                if (textChanged) {
                                    run.setText(text, 0);
                                }
                            }
                        }
                    }
                });
            }
        }
    }

    /**
     * 清除单元格内所有文本并设置新值
     */
    private void clearCellTextAndSetValue(XWPFTableCell cell, String value) {
        // 删除所有现有段落
        while (cell.getParagraphs().size() > 0) {
            cell.removeParagraph(0);
        }

        // 创建新段落并设置文本
        XWPFParagraph para = cell.addParagraph();
        XWPFRun run = para.createRun();
        run.setText(value);
    }
}
