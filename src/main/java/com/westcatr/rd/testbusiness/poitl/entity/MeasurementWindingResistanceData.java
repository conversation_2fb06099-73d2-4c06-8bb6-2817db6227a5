package com.westcatr.rd.testbusiness.poitl.entity;

import java.util.List;

import lombok.Data;

/**
 * 绕组电阻测量数据模型
 */

@Data
public class MeasurementWindingResistanceData {

    // 高压绕组测量数据行
    private List<WindingResistanceRow> highVoltageRows;

    // 低压绕组测量数据行
    private List<WindingResistanceRow> lowVoltageRows;

    // maxUnbalanceRateHighVoltage
    private String maxUnbalanceRateHighVoltage;

    // permissibleValueHighVoltage
    private String permissibleValueHighVoltage;

    // maxUnbalanceRateLowVoltage
    private String maxUnbalanceRateLowVoltage;

    // permissibleValueLowVoltage
    private String permissibleValueLowVoltage;
}