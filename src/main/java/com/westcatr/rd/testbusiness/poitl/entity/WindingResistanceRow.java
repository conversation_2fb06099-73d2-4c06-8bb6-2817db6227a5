package com.westcatr.rd.testbusiness.poitl.entity;

import com.deepoove.poi.data.RowRenderData;

/**
 * 绕组电阻测量行数据模型
 */
public class WindingResistanceRow {

    // 分接位置
    private String position;

    // 测量电阻值RAB
    private String rab;

    // 测量电阻值Rec
    private String rec;

    // 测量电阻值Ra
    private String ra;

    // 最大不平衡率
    private String maxUnbalanceRate;

    // 不平衡允许电阻
    private String permissibleUnbalanceRate;

    // 行渲染数据（用于POI-TL渲染）
    private RowRenderData rowData;

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRab() {
        return rab;
    }

    public void setRab(String rab) {
        this.rab = rab;
    }

    public String getRec() {
        return rec;
    }

    public void setRec(String rec) {
        this.rec = rec;
    }

    public String getRa() {
        return ra;
    }

    public void setRa(String ra) {
        this.ra = ra;
    }

    public String getMaxUnbalanceRate() {
        return maxUnbalanceRate;
    }

    public void setMaxUnbalanceRate(String maxUnbalanceRate) {
        this.maxUnbalanceRate = maxUnbalanceRate;
    }

    public String getPermissibleUnbalanceRate() {
        return permissibleUnbalanceRate;
    }

    public void setPermissibleUnbalanceRate(String permissibleUnbalanceRate) {
        this.permissibleUnbalanceRate = permissibleUnbalanceRate;
    }

    public RowRenderData getRowData() {
        return rowData;
    }

    public void setRowData(RowRenderData rowData) {
        this.rowData = rowData;
    }
}