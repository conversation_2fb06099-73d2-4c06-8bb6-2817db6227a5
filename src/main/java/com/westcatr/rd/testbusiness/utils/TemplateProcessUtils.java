package com.westcatr.rd.testbusiness.utils;

import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.poitl.MeasurementWindingResistanceTablePolicy;

import cn.hutool.core.io.FileUtil;
import lombok.extern.log4j.Log4j2;

/**
 * 文档模板处理工具类，用于处理报告和原始记录模板的数据填充
 */
@Log4j2
public class TemplateProcessUtils {

    /**
     * 处理文档模板，应用数据并保存到目标路径
     * 
     * @param sourceFilePath   源模板文件路径
     * @param targetFilePath   目标文件保存路径
     * @param data             要应用到模板的数据
     * @param standardInfo     标准信息对象，可为null
     * @param isReportTemplate 是否为报告模板，true表示报告模板，false表示原始记录模板
     * @return 处理成功返回true，否则返回false
     */
    public static boolean processTemplate(
            String sourceFilePath,
            String targetFilePath,
            Map<String, Object> data,
            StandardBasicInstrumentInfo standardInfo,
            boolean isReportTemplate) {

        try {
            // 如果数据为空，初始化一个空Map
            if (data == null) {
                data = new HashMap<>();
            }

            // 如果提供了标准信息，处理自定义模板值
            if (standardInfo != null) {
                String templateValues = isReportTemplate
                        ? standardInfo.getReportTemplateValues()
                        : standardInfo.getOriginalRecordTemplateValues();

                if (StringUtils.isNotBlank(templateValues)) {
                    // 使用工具类解析JSON并添加到data
                    Map<String, Object> customValues = TemplateValuesUtils.parseTemplateValues(templateValues);
                    if (customValues != null && !customValues.isEmpty()) {
                        // 合并自定义值到数据中
                        data.putAll(customValues);
                        log.info("Added {} custom template values to {} data",
                                customValues.size(),
                                isReportTemplate ? "report" : "original record");
                    }
                }
            }

            // 确保目标目录存在
            FileUtil.mkParentDirs(targetFilePath);

            // 使用POI-TL处理模板
            Configure config = Configure.builder()
                    .bind("equipmentListTable", new LoopRowTableRenderPolicy())
                    .bind("measurementWindingResistanceTable", new MeasurementWindingResistanceTablePolicy())
                    .build();

            // 使用POI-TL处理模板
            XWPFTemplate template = XWPFTemplate.compile(sourceFilePath, config).render(data);

            // 保存处理后的文件
            try (OutputStream out = Files.newOutputStream(Paths.get(targetFilePath))) {
                template.write(out);
                template.close();
            }

            log.info("Template processed successfully: {}", targetFilePath);
            return true;
        } catch (Exception e) {
            log.error("Failed to process template: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理输入流模板，应用数据后保存到目标路径
     * 
     * @param inputStream      模板输入流
     * @param targetFilePath   目标文件保存路径
     * @param data             要应用到模板的数据
     * @param standardInfo     标准信息对象，可为null
     * @param isReportTemplate 是否为报告模板，true表示报告模板，false表示原始记录模板
     * @return 处理成功返回true，否则返回false
     */
    public static boolean processTemplate(
            InputStream inputStream,
            String targetFilePath,
            Map<String, Object> data,
            StandardBasicInstrumentInfo standardInfo,
            boolean isReportTemplate) {

        try {
            // 如果数据为空，初始化一个空Map
            if (data == null) {
                data = new HashMap<>();
            }

            // 如果提供了标准信息，处理自定义模板值
            if (standardInfo != null) {
                String templateValues = isReportTemplate
                        ? standardInfo.getReportTemplateValues()
                        : standardInfo.getOriginalRecordTemplateValues();

                if (StringUtils.isNotBlank(templateValues)) {
                    // 使用工具类解析JSON并添加到data
                    Map<String, Object> customValues = TemplateValuesUtils.parseTemplateValues(templateValues);
                    if (customValues != null && !customValues.isEmpty()) {
                        // 合并自定义值到数据中
                        data.putAll(customValues);
                        log.info("Added {} custom template values to {} data from input stream",
                                customValues.size(),
                                isReportTemplate ? "report" : "original record");
                    }
                }
            }

            // 确保目标目录存在
            FileUtil.mkParentDirs(targetFilePath);

            // 使用POI-TL处理模板
            XWPFTemplate template = XWPFTemplate.compile(inputStream).render(data);

            // 保存处理后的文件
            try (OutputStream out = Files.newOutputStream(Paths.get(targetFilePath))) {
                template.write(out);
                template.close();
            }

            log.info("Template processed successfully from input stream: {}", targetFilePath);
            return true;
        } catch (Exception e) {
            log.error("Failed to process template from input stream: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建报告模板数据的通用方法
     * 
     * @param baseData     基础数据Map，用于填充到模板
     * @param standardInfo 标准信息对象，可为null
     * @return 合并后的完整数据Map
     */
    public static Map<String, Object> buildReportTemplateData(
            Map<String, Object> baseData,
            StandardBasicInstrumentInfo standardInfo) {

        Map<String, Object> result = new HashMap<>();

        // 复制基础数据
        if (baseData != null) {
            result.putAll(baseData);
        }

        // 如果提供了标准信息，处理报告自定义模板值
        if (standardInfo != null && StringUtils.isNotBlank(standardInfo.getReportTemplateValues())) {
            Map<String, Object> customValues = TemplateValuesUtils
                    .parseTemplateValues(standardInfo.getReportTemplateValues());
            if (customValues != null && !customValues.isEmpty()) {
                result.putAll(customValues);
                log.info("Added {} custom report template values to data", customValues.size());
            }
        }

        return result;
    }

    /**
     * 构建原始记录模板数据的通用方法
     * 
     * @param baseData     基础数据Map，用于填充到模板
     * @param standardInfo 标准信息对象，可为null
     * @return 合并后的完整数据Map
     */
    public static Map<String, Object> buildOriginalRecordTemplateData(
            Map<String, Object> baseData,
            StandardBasicInstrumentInfo standardInfo) {

        Map<String, Object> result = new HashMap<>();

        // 复制基础数据
        if (baseData != null) {
            result.putAll(baseData);
        }

        // 如果提供了标准信息，处理原始记录自定义模板值
        if (standardInfo != null && StringUtils.isNotBlank(standardInfo.getOriginalRecordTemplateValues())) {
            Map<String, Object> customValues = TemplateValuesUtils
                    .parseTemplateValues(standardInfo.getOriginalRecordTemplateValues());
            if (customValues != null && !customValues.isEmpty()) {
                result.putAll(customValues);
                log.info("Added {} custom original record template values to data", customValues.size());
            }
        }

        return result;
    }
}
