package com.westcatr.rd.testbusiness.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OcrTextFixer {
    public static Map<String, String> fixOcrText(String ocrText) {

        // 打印原始文本
        System.out.println("原始文本: " + ocrText);
        Map<String, String> result = new HashMap<>();

        // 预处理：移除文本中的多余空格，替换常见OCR错误
        ocrText = preprocessText(ocrText);

        // 处理 CHL 和其后面的值 - 修改为能匹配多个词的模式
        Pattern chlPattern = Pattern.compile("CHL\\s*(?:--|—|－)*\\s*([^,、]+)"); // 匹配 CHL 后面的值，直到逗号或顿号
        Matcher chlMatcher = chlPattern.matcher(ocrText);
        if (chlMatcher.find()) {
            // 保留空格，确保"全 波"这样的值能完整保留
            result.put("CHL", chlMatcher.group(1));
        }

        // 处理 max 值
        Pattern maxPattern = Pattern.compile("[vV]?max\\s*[=＝]\\s*(\\d+[.,]?\\s*\\d*)\\s*[kK]?[vV]?");
        Matcher maxMatcher = maxPattern.matcher(ocrText);
        if (maxMatcher.find()) {
            String maxValue = maxMatcher.group(1).replaceAll("\\s+", "").replace(",", ".");
            result.put("Vmax", maxValue);
        }

        // 处理 T1 值 - 修改为专门处理带空格的小数
        // 首先查找完整的T1表达式
        Pattern t1FullPattern = Pattern.compile("T1\\s*[=＝]\\s*([^,、]+)");
        Matcher t1FullMatcher = t1FullPattern.matcher(ocrText);
        if (t1FullMatcher.find()) {
            String t1Full = t1FullMatcher.group(1).trim();

            // 检查是否有"L."开头的情况
            if (t1Full.matches("[Ll]\\s*\\.\\s*\\d+.*")) {
                // 将"L."替换为"1."
                String t1Value = t1Full.replaceAll("[Ll]\\s*\\.", "1.").replaceAll("\\s+", "");
                // 提取数字部分
                Pattern numPattern = Pattern.compile("(\\d+\\.\\d+)");
                Matcher numMatcher = numPattern.matcher(t1Value);
                if (numMatcher.find()) {
                    result.put("T1", numMatcher.group(1));
                }
            } else {
                // 处理形如"1. 18"的情况，确保能提取完整的小数
                Pattern decimalPattern = Pattern.compile("(\\d+)\\s*\\.\\s*(\\d+)");
                Matcher decimalMatcher = decimalPattern.matcher(t1Full);
                if (decimalMatcher.find()) {
                    // 组合整数部分和小数部分
                    String t1Value = decimalMatcher.group(1) + "." + decimalMatcher.group(2);
                    result.put("T1", t1Value);
                } else {
                    // 使用常规模式提取数字
                    Pattern numPattern = Pattern.compile("(\\d+\\.?\\d*)");
                    Matcher numMatcher = numPattern.matcher(t1Full);
                    if (numMatcher.find()) {
                        result.put("T1", numMatcher.group(1).replaceAll("\\s+", ""));
                    }
                }
            }
        }

        // 处理 T2 值 - 使用相同的逻辑处理T2
        Pattern t2FullPattern = Pattern.compile("T2\\s*[=＝]\\s*([^,、]+)");
        Matcher t2FullMatcher = t2FullPattern.matcher(ocrText);
        if (t2FullMatcher.find()) {
            String t2Full = t2FullMatcher.group(1).trim();
            
            // 处理形如"55. 34"的情况，确保能提取完整的小数
            Pattern decimalPattern = Pattern.compile("(\\d+)\\s*\\.\\s*(\\d+)");
            Matcher decimalMatcher = decimalPattern.matcher(t2Full);
            if (decimalMatcher.find()) {
                // 组合整数部分和小数部分
                String t2Value = decimalMatcher.group(1) + "." + decimalMatcher.group(2);
                result.put("T2", t2Value);
            } else {
                // 使用常规模式提取数字
                Pattern numPattern = Pattern.compile("(\\d+\\.?\\d*)");
                Matcher numMatcher = numPattern.matcher(t2Full);
                if (numMatcher.find()) {
                    result.put("T2", numMatcher.group(1).replaceAll("\\s+", ""));
                }
            }
        }

        // 如果结果不完整，尝试使用备用模式
        if (!result.containsKey("T1") || !result.containsKey("T2")) {
            tryAlternativePatterns(ocrText, result);
        }

        return result;
    }

    /**
     * 预处理OCR文本，修正常见OCR错误
     */
    private static String preprocessText(String text) {
        // 替换常见OCR错误
        text = text.replaceAll("(?i)CH[lI1]-*", "CHL");
        text = text.replaceAll("(?i)V ?max", "Vmax");
        text = text.replaceAll("(?i)T ?1", "T1");
        text = text.replaceAll("(?i)T ?2", "T2");

        // 替换全角字符为半角
        text = text.replace('，', ',').replace('：', ':').replace('＝', '=');

        // 替换错误识别的字母为数字
        text = text.replaceAll("(?<=\\d)[lI](?=\\d)", "1"); // 数字间的l或I替换为1
        text = text.replaceAll("(?<=\\D)[lI](?=\\d)", "1"); // 非数字后跟l或I再跟数字，替换l或I为1
        text = text.replaceAll("(?<=\\d)[oO](?=\\d)", "0"); // 数字间的o或O替换为0

        return text;
    }

    /**
     * 尝试使用备用模式匹配T1和T2值
     */
    private static void tryAlternativePatterns(String text, Map<String, String> result) {
        // 备用T1模式 - 专门处理"L. 数字"的情况
        if (!result.containsKey("T1")) {
            // 查找形如"T1 = L. 17"的模式
            Pattern altT1Pattern = Pattern.compile("T1\\s*[=＝]\\s*[Ll]\\s*\\.\\s*(\\d+)");
            Matcher altT1Matcher = altT1Pattern.matcher(text);
            if (altT1Matcher.find()) {
                // 提取数字并添加小数点
                String t1Value = "1." + altT1Matcher.group(1).trim();
                result.put("T1", t1Value);
            } else {
                // 查找形如"T1 = 1. 18"的模式
                Pattern decimalT1Pattern = Pattern.compile("T1\\s*[=＝]\\s*(\\d+)\\s*\\.\\s*(\\d+)");
                Matcher decimalT1Matcher = decimalT1Pattern.matcher(text);
                if (decimalT1Matcher.find()) {
                    String t1Value = decimalT1Matcher.group(1) + "." + decimalT1Matcher.group(2);
                    result.put("T1", t1Value);
                } else {
                    // 常规宽松匹配
                    Pattern regularT1Pattern = Pattern.compile("T1\\D*(\\d+[.,]?\\d*)");
                    Matcher regularT1Matcher = regularT1Pattern.matcher(text);
                    if (regularT1Matcher.find()) {
                        String t1Value = regularT1Matcher.group(1).replaceAll("\\s+", "").replace(",", ".");
                        result.put("T1", t1Value);
                    }
                }
            }
        }

        // 备用T2模式 - 处理带空格的小数
        if (!result.containsKey("T2")) {
            // 查找形如"T2 = 55. 34"的模式
            Pattern decimalT2Pattern = Pattern.compile("T2\\s*[=＝]\\s*(\\d+)\\s*\\.\\s*(\\d+)");
            Matcher decimalT2Matcher = decimalT2Pattern.matcher(text);
            if (decimalT2Matcher.find()) {
                String t2Value = decimalT2Matcher.group(1) + "." + decimalT2Matcher.group(2);
                result.put("T2", t2Value);
            } else {
                // 常规宽松匹配
                Pattern altT2Pattern = Pattern.compile("T2\\D*(\\d+[.,]?\\d*)");
                Matcher altT2Matcher = altT2Pattern.matcher(text);
                if (altT2Matcher.find()) {
                    String t2Value = altT2Matcher.group(1).replaceAll("\\s+", "").replace(",", ".");
                    result.put("T2", t2Value);
                }
            }
        }
    }
}
