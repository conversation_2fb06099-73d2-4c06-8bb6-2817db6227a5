package com.westcatr.rd.testbusiness.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepoove.poi.data.Pictures;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.westcatr.rd.boot.core.util.SpringUtil;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.file.properties.IFileStarterProperties;
import com.westcatr.rd.boot.file.service.FileInfoService;

import cn.hutool.core.io.FileUtil;
import lombok.extern.log4j.Log4j2;

/**
 * 模板值工具类，用于处理标准中定义的自定义模板值
 */
@Log4j2
public class TemplateValuesUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 解析模板自定义值JSON字符串并转换为POI-TL可用的数据Map
     *
     * @param templateValuesJson 模板自定义值的JSON字符串
     * @return 转换后的数据Map
     */
    public static Map<String, Object> parseTemplateValues(String templateValuesJson) {
        Map<String, Object> result = new HashMap<>();

        if (StringUtils.isBlank(templateValuesJson)) {
            return result;
        }

        try {
            List<Map<String, Object>> valuesList = objectMapper.readValue(
                    templateValuesJson, new TypeReference<List<Map<String, Object>>>() {
                    });

            if (valuesList != null) {
                for (Map<String, Object> item : valuesList) {
                    String key = (String) item.get("key");
                    String value = (String) item.get("value");
                    String type = (String) item.get("type");

                    if (StringUtils.isNotBlank(key)) {
                        if ("image".equals(type) && StringUtils.isNotBlank(value)) {
                            // 处理图片URL
                            try {
                                FileInfoService fileInfoService = SpringUtil.getBean(FileInfoService.class);
                                FileInfo fileInfo = fileInfoService.getOne(new LambdaQueryWrapper<FileInfo>()
                                        .eq(FileInfo::getFileUrl, value).last("limit 1"));
                                IFileStarterProperties fProperties = SpringUtil.getBean(IFileStarterProperties.class);
                                if (FileUtil.exist(fProperties.getUploadFolder() + fileInfo.getFilePath())) {
                                    result.put(key,
                                            Pictures.ofLocal(fProperties.getUploadFolder() + fileInfo.getFilePath())
                                                    .size(200, 200)
                                                    .create());
                                    continue;
                                }
                            } catch (Exception e) {
                                log.error("处理模板图片值失败: " + key, e);
                                try {
                                    // 如果本地文件不存在或处理失败，尝试使用URL
                                    result.put(key, Pictures.ofLocal(value).create());
                                } catch (Exception e2) {
                                    log.error("通过URL处理图片失败: " + key, e2);
                                    // 如果图片处理全部失败，使用空字符串
                                    result.put(key, "");
                                }
                            }
                        } else {
                            // 文本处理
                            result.put(key, value != null ? value : "");
                        }
                    }
                }
            }
        } catch (JsonProcessingException e) {
            log.error("解析模板自定义值JSON失败", e);
        }

        return result;
    }

    /**
     * 合并多个数据Map
     * 
     * @param maps 要合并的数据Map数组
     * @return 合并后的Map
     */
    @SafeVarargs
    public static Map<String, Object> mergeMaps(Map<String, Object>... maps) {
        Map<String, Object> result = new HashMap<>();

        for (Map<String, Object> map : maps) {
            if (map != null) {
                result.putAll(map);
            }
        }

        return result;
    }
}
