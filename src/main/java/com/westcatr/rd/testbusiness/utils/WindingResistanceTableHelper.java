package com.westcatr.rd.testbusiness.utils;

import java.util.ArrayList;
import java.util.List;

import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.TextRenderData;
import com.westcatr.rd.testbusiness.poitl.entity.MeasurementWindingResistanceData;
import com.westcatr.rd.testbusiness.poitl.entity.WindingResistanceRow;

/**
 * 绕组电阻表格数据构建辅助类
 */
public class WindingResistanceTableHelper {

    /**
     * 创建高压绕组行数据
     * 
     * @param position         分接位置
     * @param rab              RAB测量值
     * @param rec              Rec测量值
     * @param ra               Ra测量值
     * @param maxUnbalanceRate 最大不平衡率
     * @param permissibleRate  不平衡允许电阻
     * @return 绕组电阻行数据
     */
    public static WindingResistanceRow createHighVoltageRow(String position, String rab, String rec, String ra,
            String maxUnbalanceRate, String permissibleRate) {

        WindingResistanceRow row = new WindingResistanceRow();
        row.setPosition(position);
        row.setRab(rab);
        row.setRec(rec);
        row.setRa(ra);
        row.setMaxUnbalanceRate(maxUnbalanceRate);
        row.setPermissibleUnbalanceRate(permissibleRate);

        // 创建行渲染数据
        RowRenderData rowData = Rows.of(
                new TextRenderData("高压"),
                new TextRenderData(position),
                new TextRenderData(rab),
                new TextRenderData(rec),
                new TextRenderData(ra),
                new TextRenderData(maxUnbalanceRate),
                new TextRenderData(permissibleRate))
                .center().create();

        row.setRowData(rowData);
        return row;
    }

    /**
     * 创建低压绕组行数据
     * 
     * @param position         分接位置
     * @param rab              RAB测量值
     * @param rec              Rec测量值
     * @param ra               Ra测量值
     * @param maxUnbalanceRate 最大不平衡率
     * @param permissibleRate  不平衡允许电阻
     * @return 绕组电阻行数据
     */
    public static WindingResistanceRow createLowVoltageRow(String position, String rab, String rec, String ra,
            String maxUnbalanceRate, String permissibleRate) {

        WindingResistanceRow row = new WindingResistanceRow();
        row.setPosition(position);
        row.setRab(rab);
        row.setRec(rec);
        row.setRa(ra);
        row.setMaxUnbalanceRate(maxUnbalanceRate);
        row.setPermissibleUnbalanceRate(permissibleRate);

        // 创建行渲染数据
        RowRenderData rowData = Rows.of(
                new TextRenderData("低压"),
                new TextRenderData(position),
                new TextRenderData(rab),
                new TextRenderData(rec),
                new TextRenderData(ra),
                new TextRenderData(maxUnbalanceRate),
                new TextRenderData(permissibleRate))
                .center().create();

        row.setRowData(rowData);
        return row;
    }

    /**
     * 构建绕组电阻测量数据模型
     * 
     * @param highVoltageRows 高压绕组行数据列表
     * @param lowVoltageRows  低压绕组行数据列表
     * @return 绕组电阻测量数据模型
     */
    public static MeasurementWindingResistanceData buildResistanceData(List<WindingResistanceRow> highVoltageRows,
            List<WindingResistanceRow> lowVoltageRows) {

        MeasurementWindingResistanceData data = new MeasurementWindingResistanceData();
        data.setHighVoltageRows(highVoltageRows);
        data.setLowVoltageRows(lowVoltageRows);
        return data;
    }

    /**
     * 构建示例数据（用于测试）
     * 
     * @return 绕组电阻测量数据模型
     */
    public static MeasurementWindingResistanceData buildExampleData() {
        // 创建高压绕组数据
        List<WindingResistanceRow> highVoltageRows = new ArrayList<>();
        highVoltageRows.add(createHighVoltageRow("1", "0.512", "0.514", "0.513", "0.2%", "0.5%"));
        highVoltageRows.add(createHighVoltageRow("2", "0.523", "0.525", "0.526", "0.3%", "0.5%"));

        // 创建低压绕组数据
        List<WindingResistanceRow> lowVoltageRows = new ArrayList<>();
        lowVoltageRows.add(createLowVoltageRow("1", "0.015", "0.016", "0.015", "0.4%", "1.0%"));
        lowVoltageRows.add(createLowVoltageRow("2", "0.017", "0.017", "0.018", "0.5%", "1.0%"));

        return buildResistanceData(highVoltageRows, lowVoltageRows);
    }
}