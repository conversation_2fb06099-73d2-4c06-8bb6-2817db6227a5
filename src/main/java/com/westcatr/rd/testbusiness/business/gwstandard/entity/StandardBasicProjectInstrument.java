package com.westcatr.rd.testbusiness.business.gwstandard.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—实验项目检测仪器信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("standard_basic_project_instrument")
@Schema(description = "标准—实验项目检测仪器信息表")
public class StandardBasicProjectInstrument extends Model<StandardBasicProjectInstrument> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "实验项目名称")
    @Length(max = 255, message = "实验项目名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("project_name")
    private String projectName;

    // 仪器仪表id
    @TableField("instrument_id")
    private Long instrumentId;

    // 实验标准id
    @TableField("standard_basic_instrument_id")
    private Long standardBasicInstrumentId;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "创建人ID")
    @TableField("create_user_id")
    private Long createUserId;

    @Schema(description = "最后操作人ID")
    @TableField("update_user_id")
    private Long updateUserId;

    @Length(max = 255, message = "长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("create_user_full_name")
    private String createUserFullName;

    @Length(max = 255, message = "长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("update_user_full_name")
    private String updateUserFullName;

    // 实验项目参数
    @TableField(exist = false)
    private java.util.List<StandardBasicProjectParam> projectParams;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
