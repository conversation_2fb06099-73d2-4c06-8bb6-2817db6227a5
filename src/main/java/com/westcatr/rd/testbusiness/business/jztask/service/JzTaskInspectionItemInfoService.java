package com.westcatr.rd.testbusiness.business.jztask.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInspectionItemInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 荆州—检项列表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface JzTaskInspectionItemInfoService extends IService<JzTaskInspectionItemInfo> {

    IPage<JzTaskInspectionItemInfo> entityPage(JzTaskInspectionItemInfoQuery query);

    JzTaskInspectionItemInfo getEntityById(Long id);

    boolean saveEntity(JzTaskInspectionItemInfo param);

    boolean updateEntity(JzTaskInspectionItemInfo param);

    boolean removeEntityById(Long id);
}
