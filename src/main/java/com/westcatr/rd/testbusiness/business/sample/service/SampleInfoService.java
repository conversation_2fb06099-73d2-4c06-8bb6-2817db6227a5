package com.westcatr.rd.testbusiness.business.sample.service;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import com.westcatr.rd.testbusiness.business.sample.pojo.dto.AutoSampleNumberDto;
import com.westcatr.rd.testbusiness.business.sample.pojo.query.SampleInfoQuery;
import com.westcatr.rd.testbusiness.business.sample.pojo.vo.SampleInfoVO;

/**
 * <p>
 * 样品——基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
public interface SampleInfoService extends IService<SampleInfo> {

    IPage<SampleInfo> entityPage(SampleInfoQuery query);

    SampleInfo getEntityById(Long id);

    boolean saveEntity(SampleInfo param);

    boolean updateEntity(SampleInfo param);

    boolean removeEntityById(Long id);

    IPage<SampleInfoVO> entityVoPage(SampleInfoQuery query);


    List<SampleInfo> importData(MultipartFile file) throws IOException;


    /**
     * 导入excel
     *
     * @param file
     * @return
     * @throws IOException
     */
    List<SampleInfo> importExcel(MultipartFile file) throws IOException;

    /**
     * 导出模板
     *
     * @param response
     */
    void exportTemplate(HttpServletResponse response);

    /**
     * 随机生成样品编号和二次盲样编号
     */
    AutoSampleNumberDto generateSampleNumber();

    boolean backSampleInfo(SampleInfo param);


//    /**
//     * 生成任务
//     *
//     * @param id
//     * @return
//     */
//    boolean generationTask(Long id);
}
