package com.westcatr.rd.testbusiness.business.jztask.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准-模型参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="标准-模型参数表查询对象")
public class JzTaskWorkOrderModelParamQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "检项")
    @QueryCondition
    private String testItem;

    @Schema(description = "检项参数（英文，唯一）")
    @QueryCondition
    private String paramKey;

    @Schema(description = "单位")
    @QueryCondition
    private String unit;

    @Schema(description = "判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型）")
    @QueryCondition
    private String judgeType;

    @Schema(description = "合格标准（描述合格的参数）")
    @QueryCondition
    private String qualifiedStandard;

    @Schema(description = "判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =)")
    @QueryCondition
    private String judgeFormula;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "修改时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "采集方式")
    @QueryCondition
    private String collectionMethod;

    @QueryCondition
    private Long orderId;
}
