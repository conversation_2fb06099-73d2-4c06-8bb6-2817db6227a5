package com.westcatr.rd.testbusiness.business.org.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.org.entity.UserTableColumnInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.query.UserTableColumnInfoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.UserTableColumnInfoVO;
import com.westcatr.rd.testbusiness.business.org.service.UserTableColumnInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * UserTableColumnInfo 控制器
 * 
 * <AUTHOR>
 * @since 2024-05-17
 */
@Validated
@Tag(name = "自定义展示列表接口", description = "自定义展示列表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class UserTableColumnInfoController {

    @Autowired
    private UserTableColumnInfoService userTableColumnInfoService;

    @Operation(summary = "获取自定义展示列表分页数据")
    @PostMapping("/userTableColumnInfo/page")
    public IResult<IPage<UserTableColumnInfo>> getUserTableColumnInfoPage(@RequestBody UserTableColumnInfoQuery query) {
        return IResult.ok(userTableColumnInfoService.entityPage(query));
    }

    @Operation(summary = "获取自定义展示列表数据")
    @PostMapping("/userTableColumnInfo/get")
    public IResult<UserTableColumnInfo> getUserTableColumnInfoById(@RequestBody @Id ID id) {
        return IResult.ok(userTableColumnInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增自定义展示列表数据")
    @PostMapping("/userTableColumnInfo/add")
    public IResult addUserTableColumnInfo(@RequestBody @Validated(Insert.class) UserTableColumnInfo param) {
        return IResult.auto(userTableColumnInfoService.saveEntity(param));
    }

    @Operation(summary = "更新自定义展示列表数据")
    @PostMapping("/userTableColumnInfo/update")
    public IResult updateUserTableColumnInfoById(@RequestBody @Validated(Update.class) UserTableColumnInfo param) {
        return IResult.auto(userTableColumnInfoService.updateEntity(param));
    }

    @Operation(summary = "删除自定义展示列表数据")
    @PostMapping("/userTableColumnInfo/delete")
    public IResult deleteUserTableColumnInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            userTableColumnInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取自定义展示列表VO分页数据")
    @PostMapping("/userTableColumnInfo/voPage")
    public IResult<IPage<UserTableColumnInfoVO>> getUserTableColumnInfoVoPage(
            @RequestBody UserTableColumnInfoQuery query) {
        AssociationQuery<UserTableColumnInfoVO> associationQuery = new AssociationQuery<>(UserTableColumnInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取自定义展示列表VO数据")
    @PostMapping("/userTableColumnInfo/getVo")
    public IResult<UserTableColumnInfoVO> getUserTableColumnInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<UserTableColumnInfoVO> associationQuery = new AssociationQuery<>(UserTableColumnInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
