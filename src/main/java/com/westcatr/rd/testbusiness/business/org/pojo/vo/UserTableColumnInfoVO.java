package com.westcatr.rd.testbusiness.business.org.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.org.entity.UserTableColumnInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 自定义展示列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="自定义展示列表VO对象")
public class UserTableColumnInfoVO extends UserTableColumnInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
