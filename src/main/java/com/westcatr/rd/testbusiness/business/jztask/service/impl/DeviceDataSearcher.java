package com.westcatr.rd.testbusiness.business.jztask.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataResponse;

public class DeviceDataSearcher {
    /**
     * Finds the first TestResult from the first DeviceDataResponse that matches the
     * given deviceName.
     *
     * @param dataList   The list of DeviceDataResponse objects to search.
     * @param deviceName The name of the device to match (DeviceDataResponse.name).
     * @return The first TestResult of the matched device, or null if not found,
     *         or if the matched device has no results.
     */
    public DeviceDataResponse.TestResult findFirstTestResultByDeviceName(
            List<DeviceDataResponse> dataList,
            String deviceName) {

        if (dataList == null || dataList.isEmpty() || deviceName == null) {
            return null;
        }

        // Using a traditional for-each loop
        for (DeviceDataResponse deviceData : dataList) {
            if (deviceData != null) {
                List<DeviceDataResponse.TestResult> testResults = deviceData.getResult();
                for (DeviceDataResponse.TestResult testResult : testResults) {
                    if (deviceName.equals(testResult.getName())) { // Check if device name matches
                        // Found the first matching device, return its first TestResult
                        if (testResult != null) {
                            return testResult; // Return the first TestResult
                        }
                    }
                }
            }
        }
        return null; // No device with the target name was found
    }

    /**
     * Finds the first TestResult from the first DeviceDataResponse that matches the
     * given deviceName,
     * using Java Streams.
     *
     * @param dataList   The list of DeviceDataResponse objects to search.
     * @param deviceName The name of the device to match (DeviceDataResponse.name).
     * @return The first TestResult of the matched device, or null if not found,
     *         or if the matched device has no results.
     */
    public DeviceDataResponse.TestResult findFirstTestResultByDeviceNameStream(
            List<DeviceDataResponse> dataList,
            String deviceName) {

        if (dataList == null || deviceName == null) {
            return null;
        }

        return dataList.stream()
                .filter(deviceData -> deviceData != null && deviceName.equals(deviceData.getName())) // Find the
                                                                                                     // DeviceDataResponse
                .findFirst() // Get the first one that matches
                .flatMap(deviceData -> { // If found, try to get its first TestResult
                    List<DeviceDataResponse.TestResult> results = deviceData.getResult();
                    if (results != null && !results.isEmpty()) {
                        return java.util.Optional.of(results.get(0));
                    }
                    return java.util.Optional.empty(); // No results or empty list
                })
                .orElse(null); // If no device found, or device found but no results
    }

    public static void main(String[] args) {
        // Example Usage:
        DeviceDataSearcher searcher = new DeviceDataSearcher();

        // Create some sample data
        List<DeviceDataResponse.TestResult> results1 = new ArrayList<>();
        results1.add(new DeviceDataResponse.TestResult("ParamA1", "CodeA1", "ValueA1"));
        results1.add(new DeviceDataResponse.TestResult("ParamA2", "CodeA2", "ValueA2"));

        List<DeviceDataResponse.TestResult> results2 = new ArrayList<>();
        results2.add(new DeviceDataResponse.TestResult("ParamB1", "CodeB1", "ValueB1"));

        List<DeviceDataResponse.TestResult> results3_empty = new ArrayList<>();

        List<DeviceDataResponse> data = new ArrayList<>();
        data.add(new DeviceDataResponse("Device Alpha", "DVC001", new Date(), results1));
        data.add(new DeviceDataResponse("Device Beta", "DVC002", new Date(), results2));
        data.add(new DeviceDataResponse("Device Gamma", "DVC003", new Date(), null)); // No results list
        data.add(new DeviceDataResponse("Device Delta", "DVC004", new Date(), results3_empty)); // Empty results list

        // --- Test with traditional loop method ---
        System.out.println("--- Using traditional loop ---");
        DeviceDataResponse.TestResult foundResult1 = searcher.findFirstTestResultByDeviceName(data, "Device Alpha");
        if (foundResult1 != null) {
            System.out.println("Found for 'Device Alpha': " + foundResult1);
            System.out.println("  Name: " + foundResult1.getName());
            System.out.println("  Code: " + foundResult1.getCode());
            System.out.println("  Value: " + foundResult1.getValue());
        } else {
            System.out.println("No result found for 'Device Alpha'");
        }

        DeviceDataResponse.TestResult foundResult2 = searcher.findFirstTestResultByDeviceName(data, "Device Beta");
        if (foundResult2 != null) {
            System.out.println("Found for 'Device Beta': " + foundResult2);
        } else {
            System.out.println("No result found for 'Device Beta'");
        }

        DeviceDataResponse.TestResult foundResult3 = searcher.findFirstTestResultByDeviceName(data, "Device Gamma"); // Device
                                                                                                                     // with
                                                                                                                     // null
                                                                                                                     // results
        if (foundResult3 != null) {
            System.out.println("Found for 'Device Gamma': " + foundResult3);
        } else {
            System.out.println("No result found for 'Device Gamma'");
        }

        DeviceDataResponse.TestResult foundResult4 = searcher.findFirstTestResultByDeviceName(data, "Device Delta"); // Device
                                                                                                                     // with
                                                                                                                     // empty
                                                                                                                     // results
        if (foundResult4 != null) {
            System.out.println("Found for 'Device Delta': " + foundResult4);
        } else {
            System.out.println("No result found for 'Device Delta'");
        }

        DeviceDataResponse.TestResult foundResult5 = searcher.findFirstTestResultByDeviceName(data,
                "NonExistentDevice");
        if (foundResult5 != null) {
            System.out.println("Found for 'NonExistentDevice': " + foundResult5);
        } else {
            System.out.println("No result found for 'NonExistentDevice'");
        }

        // --- Test with Stream API method ---
        System.out.println("\n--- Using Stream API ---");
        DeviceDataResponse.TestResult foundResultStream1 = searcher.findFirstTestResultByDeviceNameStream(data,
                "Device Alpha");
        if (foundResultStream1 != null) {
            System.out.println("Found for 'Device Alpha' (Stream): " + foundResultStream1);
            System.out.println("  Name: " + foundResultStream1.getName());
            System.out.println("  Code: " + foundResultStream1.getCode());
            System.out.println("  Value: " + foundResultStream1.getValue());
        } else {
            System.out.println("No result found for 'Device Alpha' (Stream)");
        }

        DeviceDataResponse.TestResult foundResultStream2 = searcher.findFirstTestResultByDeviceNameStream(data,
                "Device Beta");
        if (foundResultStream2 != null) {
            System.out.println("Found for 'Device Beta' (Stream): " + foundResultStream2);
        } else {
            System.out.println("No result found for 'Device Beta' (Stream)");
        }

        DeviceDataResponse.TestResult foundResultStream3 = searcher.findFirstTestResultByDeviceNameStream(data,
                "Device Gamma");
        if (foundResultStream3 != null) {
            System.out.println("Found for 'Device Gamma' (Stream): " + foundResultStream3);
        } else {
            System.out.println("No result found for 'Device Gamma' (Stream)");
        }

        DeviceDataResponse.TestResult foundResultStream4 = searcher.findFirstTestResultByDeviceNameStream(data,
                "Device Delta");
        if (foundResultStream4 != null) {
            System.out.println("Found for 'Device Delta' (Stream): " + foundResultStream4);
        } else {
            System.out.println("No result found for 'Device Delta' (Stream)");
        }

        DeviceDataResponse.TestResult foundResultStream5 = searcher.findFirstTestResultByDeviceNameStream(data,
                "NonExistentDevice");
        if (foundResultStream5 != null) {
            System.out.println("Found for 'NonExistentDevice' (Stream): " + foundResultStream5);
        } else {
            System.out.println("No result found for 'NonExistentDevice' (Stream)");
        }
    }
}
