package com.westcatr.rd.testbusiness.business.workstation.pojo.vo;

import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工作台-历史数据统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="工作台-历史数据统计VO对象")
public class WorkstationHistoryCountVO extends WorkstationHistoryCount {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
