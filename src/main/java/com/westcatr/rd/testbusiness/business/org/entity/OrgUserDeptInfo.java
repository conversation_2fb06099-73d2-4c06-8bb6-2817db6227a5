package com.westcatr.rd.testbusiness.business.org.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 用户部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("org_user_dept_info")
@Schema(description="用户部门信息表")
public class OrgUserDeptInfo extends Model<OrgUserDeptInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("dept_id")
    private Long deptId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
