package com.westcatr.rd.testbusiness.business.jzreport.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 样品基本信息-关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="样品基本信息-关联表查询对象")
public class BuSampleBaseInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long id;

    @Schema(description = "样品名称")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE)
    private String name;

    @Schema(description = "样品型号")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE)
    private String type;

    @Schema(description = "样品规格")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE)
    private String model;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;
}
