package com.westcatr.rd.testbusiness.business.workstation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;

import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import com.westcatr.rd.testbusiness.business.workstation.pojo.query.WorkstationHistoryCountQuery;
import com.westcatr.rd.testbusiness.business.workstation.pojo.vo.WorkstationHistoryCountVO;
import com.westcatr.rd.testbusiness.business.workstation.service.WorkstationHistoryCountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 *  WorkstationHistoryCount 控制器
 *   <AUTHOR>
 *  @since 2025-05-29
 */
@Validated
@Tag(name = "工作台-历史数据统计接口", description = "工作台-历史数据统计接口")
@Slf4j
@RestController
public class WorkstationHistoryCountController {

    @Autowired
    private WorkstationHistoryCountService workstationHistoryCountService;

    @Operation(summary = "获取工作台-历史数据统计分页数据")
    @PostMapping("/workstationHistoryCount/page")
    public IResult<IPage<WorkstationHistoryCount>> getWorkstationHistoryCountPage(@RequestBody WorkstationHistoryCountQuery query) {
        return IResult.ok(workstationHistoryCountService.entityPage(query));
    }

    @Operation(summary = "获取工作台-历史数据统计数据")
    @PostMapping("/workstationHistoryCount/get")
    public IResult<WorkstationHistoryCount> getWorkstationHistoryCountById(@RequestBody @Id ID id) {
        return IResult.ok(workstationHistoryCountService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增工作台-历史数据统计数据")
    @PostMapping("/workstationHistoryCount/add")
    public IResult addWorkstationHistoryCount(@RequestBody @Validated(Insert.class) WorkstationHistoryCount param) {
        return IResult.auto(workstationHistoryCountService.saveEntity(param));
    }

    @Operation(summary = "更新工作台-历史数据统计数据")
    @PostMapping("/workstationHistoryCount/update")
    public IResult updateWorkstationHistoryCountById(@RequestBody @Validated(Update.class) WorkstationHistoryCount param) {
        return IResult.auto(workstationHistoryCountService.updateEntity(param));
    }

    @Operation(summary = "删除工作台-历史数据统计数据")
    @PostMapping("/workstationHistoryCount/delete")
    public IResult deleteWorkstationHistoryCountById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            workstationHistoryCountService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取工作台-历史数据统计VO分页数据")
    @PostMapping("/workstationHistoryCount/voPage")
    public IResult<IPage<WorkstationHistoryCountVO>> getWorkstationHistoryCountVoPage(@RequestBody WorkstationHistoryCountQuery query) {
        AssociationQuery<WorkstationHistoryCountVO> associationQuery = new AssociationQuery<>(WorkstationHistoryCountVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取工作台-历史数据统计VO数据")
    @PostMapping("/workstationHistoryCount/getVo")
    public IResult<WorkstationHistoryCountVO> getWorkstationHistoryCountVoById(@RequestBody @Id ID id) {
        AssociationQuery<WorkstationHistoryCountVO> associationQuery = new AssociationQuery<>(WorkstationHistoryCountVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }


}
