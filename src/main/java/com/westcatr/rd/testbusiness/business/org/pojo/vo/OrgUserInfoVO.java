package com.westcatr.rd.testbusiness.business.org.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "人员表VO对象")
public class OrgUserInfoVO extends OrgUserInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("user_sign_info")
    private String userSignInfo;
}
