package com.westcatr.rd.testbusiness.business.datasync.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.westcatr.rd.testbusiness.business.datasync.entity.LogDeviceFunctionCall;

/**
 * <p>
 * 设备功能调用记录表 Mapper 接口 (MySQL)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Mapper
public interface LogDeviceFunctionCallMapper extends BaseMapper<LogDeviceFunctionCall> {

    /**
     * 获取最后同步时间
     * 
     * @return 最后同步时间
     */
    @Select("SELECT MAX(create_time) FROM zzzzz_log_device_function_call")
    Date getLastSyncTime();

    /**
     * 根据ID列表查询记录是否存在
     * 
     * @param ids ID列表
     * @return 存在的ID列表
     */
    @Select("<script>" +
            "SELECT id FROM zzzzz_log_device_function_call WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<Long> selectExistingIds(@Param("ids") List<Long> ids);

    /**
     * 统计指定时间范围内的记录数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM zzzzz_log_device_function_call " +
            "WHERE create_time >= #{startTime} AND create_time <= #{endTime}")
    Long countByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
