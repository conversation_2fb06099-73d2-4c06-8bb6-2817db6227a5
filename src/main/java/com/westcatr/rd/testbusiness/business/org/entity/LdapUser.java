package com.westcatr.rd.testbusiness.business.org.entity;

import lombok.Data;

@Data
public class LdapUser {
    private String dn;

    /**
     * 用户名
     */
    private String cn;

    /**
     * 节点类型
     */
    private String[] objectClass;

    /**
     * 用户密码
     */
    private String userPassword;

    /**
     * 当前目录唯一id
     */
    private String uid;

    /**
     * 常指姓氏
     */
    private String sn;

    /**
     * 一级部门（公司主体：分院、公司等）
     */
    private String dept1;
    /**
     * 二级部门
     */
    private String dept2;
    /**
     * 三级级部门
     */
    private String dept3;

    /**
     * 电话
     */
    private String userMobile;

    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 密码MD5
     */
    private String passwordMD5;

    /**
     * 密码SHA1
     */
    private String passwordSHA1;

    /**
     * 员工编号
     */
    private String userNo;

    /**
     * 员工用户名，姓名全拼
     */
    private String userName;

    /**
     * 钉钉id
     */
    private String dingId;

    /**
     *员工性质，working-在职，leave-离职
     */
    private String userType;

    /**
     *员工id
     */
    private String empId;

    /**
     *部门祖级列表
     */
    private String ancestors;

    /**
     *部门名称
     */
    private String deptName;

    /**
     *部门Id
     */
    private String deptId;

    /**
     *是否主部门（1，主部门；2，兼职部门）
     */
    private String workType;

    /**
     *职位
     */
    private String position;

    /**
     *账号状态（0正常 1停用）
     */
    private String status;

    /**
     *删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     *ume系统LdapId
     */
    private String umeLdapId;

    /**
     *员工类型ID（1，正式职工；2，实习生；3，外聘专家；4，保洁阿姨；5，协会正式职工）
     */
    private String empTypeId;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 学历
     */
    private String degreeReceived;

    public String getDn() {
        return dn;
    }

    public void setDn(String dn) {
        this.dn = dn;
    }

    public String getCn() {
        return cn;
    }

    public void setCn(String cn) {
        this.cn = cn;
    }

    public String[] getObjectClass() {
        return objectClass;
    }

    public void setObjectClass(String[] objectClass) {
        this.objectClass = objectClass;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getDept1() {
        return dept1;
    }

    public void setDept1(String dept1) {
        this.dept1 = dept1;
    }

    public String getDept2() {
        return dept2;
    }

    public void setDept2(String dept2) {
        this.dept2 = dept2;
    }

    public String getDept3() {
        return dept3;
    }

    public void setDept3(String dept3) {
        this.dept3 = dept3;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getPasswordMD5() {
        return passwordMD5;
    }

    public void setPasswordMD5(String passwordMD5) {
        this.passwordMD5 = passwordMD5;
    }

    public String getPasswordSHA1() {
        return passwordSHA1;
    }

    public void setPasswordSHA1(String passwordSHA1) {
        this.passwordSHA1 = passwordSHA1;
    }

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDingId() {
        return dingId;
    }

    public void setDingId(String dingId) {
        this.dingId = dingId;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }
}
