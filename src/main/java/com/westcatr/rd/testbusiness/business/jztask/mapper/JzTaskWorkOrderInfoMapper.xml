<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskWorkOrderInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="end_date" property="endDate" />
        <result column="inspection_completion_time" property="inspectionCompletionTime" />
        <result column="sample_id" property="sampleId" />
        <result column="start_date" property="startDate" />
        <result column="task_id" property="taskId" />
        <result column="update_time" property="updateTime" />
        <result column="work_order_number" property="workOrderNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, end_date, id, inspection_completion_time, sample_id, start_date, task_id, update_time, work_order_number
    </sql>

</mapper>
