package com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—实验项目检测仪器信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "标准—实验项目检测仪器信息表VO对象")
public class StandardBasicProjectInstrumentVO extends StandardBasicProjectInstrument {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    // 仪器设备实体
    @JoinSelect(joinClass = InstrumentNewInfo.class, mainId = "instrumentId")
    @TableField(exist = false)
    private InstrumentNewInfo instrumentNewInfo;

    // 实验标准实体
    @JoinSelect(joinClass = StandardBasicInstrumentInfo.class, mainId = "standardBasicInstrumentId")
    @TableField(exist = false)
    private StandardBasicInstrumentInfo standardBasicProjectInstrument;

    // 实验项目参数
    @JoinSelect(joinClass = StandardBasicProjectParam.class, relationId = "standard_basic_project_id")
    @TableField(exist = false)
    private java.util.List<StandardBasicProjectParam> projectParams;
}
