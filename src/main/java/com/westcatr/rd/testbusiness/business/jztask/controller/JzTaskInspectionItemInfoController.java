package com.westcatr.rd.testbusiness.business.jztask.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.Comparator;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInspectionItemInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInspectionItemInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInspectionItemInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * JzTaskInspectionItemInfo 控制器
 * 
 * <AUTHOR>
 * @since 2025-03-20
 */
@Validated
@Tag(name = "荆州—检项列表表接口", description = "荆州—检项列表表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class JzTaskInspectionItemInfoController {

    @Autowired
    private JzTaskInspectionItemInfoService jzTaskInspectionItemInfoService;

    @Operation(summary = "获取荆州—检项列表表分页数据")
    @PostMapping("/jzTaskInspectionItemInfo/page")
    public IResult<IPage<JzTaskInspectionItemInfo>> getJzTaskInspectionItemInfoPage(
            @RequestBody JzTaskInspectionItemInfoQuery query) {
        return IResult.ok(jzTaskInspectionItemInfoService.entityPage(query));
    }

    @Operation(summary = "获取荆州—检项列表表数据")
    @PostMapping("/jzTaskInspectionItemInfo/get")
    public IResult<JzTaskInspectionItemInfo> getJzTaskInspectionItemInfoById(@RequestBody @Id ID id) {
        return IResult.ok(jzTaskInspectionItemInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增荆州—检项列表表数据")
    @PostMapping("/jzTaskInspectionItemInfo/add")
    public IResult addJzTaskInspectionItemInfo(@RequestBody @Validated(Insert.class) JzTaskInspectionItemInfo param) {
        return IResult.auto(jzTaskInspectionItemInfoService.saveEntity(param));
    }

    @Operation(summary = "更新荆州—检项列表表数据")
    @PostMapping("/jzTaskInspectionItemInfo/update")
    public IResult updateJzTaskInspectionItemInfoById(
            @RequestBody @Validated(Update.class) JzTaskInspectionItemInfo param) {
        return IResult.auto(jzTaskInspectionItemInfoService.updateEntity(param));
    }

    @Operation(summary = "删除荆州—检项列表表数据")
    @PostMapping("/jzTaskInspectionItemInfo/delete")
    public IResult deleteJzTaskInspectionItemInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            jzTaskInspectionItemInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取荆州—检项列表表VO分页数据")
    @PostMapping("/jzTaskInspectionItemInfo/voPage")
    public IResult<IPage<JzTaskInspectionItemInfoVO>> getJzTaskInspectionItemInfoVoPage(
            @RequestBody JzTaskInspectionItemInfoQuery query) {
        AssociationQuery<JzTaskInspectionItemInfoVO> associationQuery = new AssociationQuery<>(
                JzTaskInspectionItemInfoVO.class);
        IPage<JzTaskInspectionItemInfoVO> jzTaskInspectionItemInfoVOIPage = associationQuery.voPage(query);
        java.util.List<JzTaskInspectionItemInfoVO> records = jzTaskInspectionItemInfoVOIPage.getRecords();
        java.util.List<JzTaskInspectionItemInfoVO> collect = records.stream()
                .sorted(Comparator.comparing(
                        x -> x.getJzTaskWorkOrderInfo().getEndDate(),
                        Comparator.nullsLast(Comparator.reverseOrder()) // null 值排在最后
                ))
                // .sorted(Comparator.comparing(JzTaskInspectionItemInfoVO::getInspectionItemNumber,
                // Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.toList());
        jzTaskInspectionItemInfoVOIPage.setRecords(collect);
        return IResult.ok(jzTaskInspectionItemInfoVOIPage);
    }

    @Operation(summary = "获取荆州—检项列表表VO数据")
    @PostMapping("/jzTaskInspectionItemInfo/getVo")
    public IResult<JzTaskInspectionItemInfoVO> getJzTaskInspectionItemInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<JzTaskInspectionItemInfoVO> associationQuery = new AssociationQuery<>(
                JzTaskInspectionItemInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
