package com.westcatr.rd.testbusiness.business.jzreport.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 样品基本信息-关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_sample_base_info")
@Schema(description="样品基本信息-关联表")
public class BuSampleBaseInfo extends Model<BuSampleBaseInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "样品名称")
    @Length(max = 255, message = "样品名称长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("name")
    private String name;

    @Schema(description = "样品型号")
    @Length(max = 255, message = "样品型号长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("type")
    private String type;

    @Schema(description = "样品规格")
    @Length(max = 255, message = "样品规格长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("model")
    private String model;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
