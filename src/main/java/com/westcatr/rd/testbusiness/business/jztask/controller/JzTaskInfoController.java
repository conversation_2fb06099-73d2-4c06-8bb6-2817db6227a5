package com.westcatr.rd.testbusiness.business.jztask.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.Date;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * JzTaskInfo 控制器
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Validated
@Tag(name = "荆州—任务列表表接口", description = "荆州—任务列表表接口")
@Slf4j
@RestController
public class JzTaskInfoController {

    @Autowired
    private JzTaskInfoService jzTaskInfoService;

    @Operation(summary = "获取荆州—任务列表表分页数据")
    @PostMapping("/jzTaskInfo/page")
    public IResult<IPage<JzTaskInfo>> getJzTaskInfoPage(@RequestBody JzTaskInfoQuery query) {
        return IResult.ok(jzTaskInfoService.entityPage(query));
    }

    @Operation(summary = "获取荆州—任务列表表数据")
    @PostMapping("/jzTaskInfo/get")
    public IResult<JzTaskInfo> getJzTaskInfoById(@RequestBody @Id ID id) {
        return IResult.ok(jzTaskInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增荆州—任务列表表数据")
    @PostMapping("/jzTaskInfo/add")
    public IResult addJzTaskInfo(@RequestBody @Validated(Insert.class) JzTaskInfo param) {
        return IResult.auto(jzTaskInfoService.saveEntity(param));
    }

    @Operation(summary = "更新荆州—任务列表表数据")
    @PostMapping("/jzTaskInfo/update")
    public IResult updateJzTaskInfoById(@RequestBody @Validated(Update.class) JzTaskInfo param) {
        return IResult.auto(jzTaskInfoService.updateEntity(param));
    }

    @Operation(summary = "删除荆州—任务列表表数据")
    @PostMapping("/jzTaskInfo/delete")
    public IResult deleteJzTaskInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            jzTaskInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取荆州—任务列表表VO分页数据")
    @PostMapping("/jzTaskInfo/voPage")
    public IResult<IPage<JzTaskInfoVO>> getJzTaskInfoVoPage(@RequestBody JzTaskInfoQuery query) {
        return IResult.ok(jzTaskInfoService.myEntityVoPage(query));
    }

    @Operation(summary = "获取荆州—任务列表表VO数据")
    @PostMapping("/jzTaskInfo/getVo")
    public IResult<JzTaskInfoVO> getJzTaskInfoVoById(@RequestBody @Id ID id) {
        return IResult.ok(jzTaskInfoService.myEntityVoById(id.longId()));
    }

    @Operation(summary = "启动任务")
    @PostMapping("/jzTaskInfo/startTask")
    public IResult startTask(@RequestBody @Id ID id) {
        return IResult.auto(jzTaskInfoService.startTask(id.longId()));
    }

    @Operation(summary = "任务检闭")
    @PostMapping("/jzTaskInfo/endTask")
    public IResult endTask(@RequestBody @Id ID id) {
        return IResult.auto(jzTaskInfoService.endTask(id.longId()));
    }

    @Operation(summary = "获取首页统计图数据")
    @GetMapping("/jzTaskInfo/getHomepage")
    public IResult<Map<String, Long>> getTaskInfo() {
        return IResult.ok(jzTaskInfoService.getTaskInfo());
    }

}
