package com.westcatr.rd.testbusiness.business.devicedata.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 设备数据查询请求
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@Schema(description = "设备数据查询请求")
public class DeviceDataRequest {

    @Schema(description = "设备代码", required = true)
    @NotBlank(message = "设备代码不能为空")
    private String deviceCode;

    @Schema(description = "开始时间", required = false)
    private Date startTime;

    @Schema(description = "结束时间", required = false)
    private Date endTime;
}
