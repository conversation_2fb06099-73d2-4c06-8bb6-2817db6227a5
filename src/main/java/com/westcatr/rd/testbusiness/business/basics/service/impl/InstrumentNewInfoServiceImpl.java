package com.westcatr.rd.testbusiness.business.basics.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fast.jackson.JSONArray;
import com.fast.jackson.JSONObject;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.entity.JoinInfo;
import com.westcatr.rd.boot.orm.service.JoinInfoService;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import com.westcatr.rd.testbusiness.business.basics.mapper.InstrumentNewInfoMapper;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.InstrumentNewInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentNewInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentUserInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskTestDataInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskTestDataInfoService;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigService;
import com.westcatr.rd.testbusiness.business.mqtt.service.MqttClientService;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;
import com.westcatr.rd.testbusiness.configs.SmbFileService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.TesseractException;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Service
@Slf4j
public class InstrumentNewInfoServiceImpl extends ServiceImpl<InstrumentNewInfoMapper, InstrumentNewInfo>
        implements InstrumentNewInfoService {

    @Autowired
    private JoinInfoService joinInfoService;

    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;

    @Autowired
    private JzTaskTestDataInfoService jzTaskTestDataInfoService;

    @Autowired
    private SmbFileService smbFileService;

    @Autowired
    private InstrumentUserInfoService instrumentUserInfoService;

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private MqttClientService mqttClientService;

    @Autowired
    private DeviceIntegrationConfigService deviceIntegrationConfigService;

    @Value("${smb.computers}")
    private String smbComputersJsonStr;

    @Value("${smb.file.save.dir}")
    private String smbFileSaveDir;

    @Override
    public IPage<InstrumentNewInfo> entityPage(InstrumentNewInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<InstrumentNewInfo>().create(query));
    }

    @Override
    public InstrumentNewInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(InstrumentNewInfo param) {
        if (this.save(param)) {
            if (ObjectUtil.isNotNull(param.getGwBzIds())) {
                joinInfoService.add(57, param.getId(), param.getGwBzIds());
            }
            if (ObjectUtil.isNotNull(param.getTestUserIds())) {
                List<InstrumentUserInfo> instrumentUserInfos = new ArrayList<>();
                param.getTestUserIds().forEach(x -> {
                    // 排序从1开始,自动排序
                    InstrumentUserInfo inUserInfo = new InstrumentUserInfo();
                    inUserInfo.setInstrumentNewId(param.getId()).setUserId(x);
                    inUserInfo.setSortNum(instrumentUserInfos.size() + 1);
                    inUserInfo.setUserFullName(orgUserInfoService.getById(x).getFullName());
                    instrumentUserInfos.add(inUserInfo);
                });
                instrumentUserInfoService.saveBatch(instrumentUserInfos);
            }
        }
        return true;
    }

    public boolean updateEntity(InstrumentNewInfo param) {
        /*
         * if (StringUtils.isEmpty(param.getParametersInspectedEquipment())) {
         * throw new IRuntimeException("可检参数不能为空");
         * }
         */
        if (this.updateById(param)) {
            if (ObjectUtil.isNotNull(param.getGwBzIds())) {
                joinInfoService.update(57, param.getId(), param.getGwBzIds());
            }
            if (ObjectUtil.isNotNull(param.getTestUserIds())) {
                // 先删除
                instrumentUserInfoService.remove(new LambdaQueryWrapper<InstrumentUserInfo>()
                        .eq(InstrumentUserInfo::getInstrumentNewId, param.getId()));
                List<InstrumentUserInfo> instrumentUserInfos = new ArrayList<>();
                param.getTestUserIds().forEach(x -> {
                    // 排序从1开始,自动排序
                    InstrumentUserInfo inUserInfo = new InstrumentUserInfo();
                    inUserInfo.setInstrumentNewId(param.getId()).setUserId(x);
                    inUserInfo.setSortNum(instrumentUserInfos.size() + 1);
                    inUserInfo.setUserFullName(orgUserInfoService.getById(x).getFullName());
                    instrumentUserInfos.add(inUserInfo);
                });
                instrumentUserInfoService.saveBatch(instrumentUserInfos);
            }
        }
        return true;
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public IPage<InstrumentNewInfoVO> entityMyPage(InstrumentNewInfoQuery query) {
        AssociationQuery<InstrumentNewInfoVO> associationQuery = new AssociationQuery<>(InstrumentNewInfoVO.class);
        IPage<InstrumentNewInfoVO> iPages = associationQuery.voPage(query);
        List<InstrumentNewInfoVO> records = iPages.getRecords();
        for (InstrumentNewInfoVO record : records) {
            List<Long> gwbzIds = new ArrayList<>();
            List<JoinInfo> joinInfos = record.getJoinInfos();
            if (CollUtil.isNotEmpty(joinInfos)) {
                for (JoinInfo joinInfo : joinInfos) {
                    gwbzIds.add(Long.valueOf(joinInfo.getId2()));
                }
                record.setGwBzIds(gwbzIds);
            }
        }
        return iPages;
    }

    @Override
    public List<AutoResultDto> getDeviceTestData(Long equipmentId, List<Long> parameterIds) {
        log.info("🚀 开始获取设备测试数据，设备ID: {}, 参数ID列表: {}", equipmentId, parameterIds);

        // 初始化结果列表
        final List<AutoResultDto> result = new ArrayList<>();

        // 获取设备信息
        InstrumentNewInfo instrumentNewInfo = this.getById(equipmentId);
        if (instrumentNewInfo == null) {
            log.error("❌ 未找到设备信息，设备ID: {}", equipmentId);
            throw new IRuntimeException("未找到设备信息");
        }

        log.info("📋 设备信息 - 名称: {}, 类型代码: {}, 配置ID: {}",
                instrumentNewInfo.getSbmc(),
                instrumentNewInfo.getDeviceTypeCode(),
                instrumentNewInfo.getDeviceIntegrationConfigId());

        // 根据设备类型处理不同的设备数据获取逻辑
        if (instrumentNewInfo.getSbmc().contains("冲击电压发生装置")) {
            // 处理冲击电压发生装置的数据获取（保持原有逻辑）
            result.addAll(handleImpulseVoltageDevice(instrumentNewInfo, parameterIds));
        } else if (instrumentNewInfo.getSbmc().contains("一体化变压器")) {
            // 处理一体化变压器的数据获取（保持原有逻辑）
            result.addAll(handleIntegratedTransformerDevice(instrumentNewInfo, parameterIds));
        } else {
            // 🔧 使用新的统一设备对接逻辑
            result.addAll(handleUnifiedDeviceIntegration(instrumentNewInfo, parameterIds));
        }

        // 确保为每个参数ID返回结果
        ensureAllParametersHaveResults(result, parameterIds);

        log.info("✅ 设备测试数据获取完成，返回结果数量: {}", result.size());
        return result;
    }

    /**
     * 🔧 统一设备对接处理逻辑
     *
     * @param instrumentNewInfo 设备信息
     * @param parameterIds 参数ID列表
     * @return 测试结果列表
     */
    private List<AutoResultDto> handleUnifiedDeviceIntegration(InstrumentNewInfo instrumentNewInfo, List<Long> parameterIds) {
        List<AutoResultDto> result = new ArrayList<>();

        try {
            // 检查设备类型代码
            if (StrUtil.isBlank(instrumentNewInfo.getDeviceTypeCode())) {
                log.warn("⚠️ 设备类型代码为空，设备ID: {}, 使用默认处理", instrumentNewInfo.getId());
                return generateDefaultResults(parameterIds);
            }

            // 🔍 查询设备对接配置
            DeviceIntegrationConfig deviceConfig = getDeviceIntegrationConfig(instrumentNewInfo);
            if (deviceConfig == null) {
                log.warn("⚠️ 未找到设备对接配置，设备类型: {}, 使用默认处理", instrumentNewInfo.getDeviceTypeCode());
                return generateDefaultResults(parameterIds);
            }

            log.info("📋 找到设备对接配置 - 设备名称: {}, 产品代码: {}, 设备代码: {}",
                    deviceConfig.getDeviceName(), deviceConfig.getProductCode(), deviceConfig.getDeviceCode());

            // 🛠️ 准备设备参数
            Map<String, Object> deviceParams = prepareDeviceParams(instrumentNewInfo, deviceConfig);

            // 📡 发送设备请求并获取响应
            Map<String, Object> response = mqttClientService.sendDeviceRequest(
                    instrumentNewInfo.getDeviceTypeCode(), deviceParams);

            if (response != null) {
                log.info("📨 收到设备响应: {}", JSONUtil.toJsonPrettyStr(response));

                // 🔄 解析设备响应数据
                result = parseDeviceResponse(response, deviceConfig, parameterIds);
            } else {
                log.warn("⚠️ 设备响应为空，使用默认结果");
                result = generateDefaultResults(parameterIds);
            }

        } catch (Exception e) {
            log.error("❌ 统一设备对接处理失败: {}", e.getMessage(), e);
            result = generateDefaultResults(parameterIds);
        }

        return result;
    }

    /**
     * 🔍 获取设备对接配置
     */
    private DeviceIntegrationConfig getDeviceIntegrationConfig(InstrumentNewInfo instrumentNewInfo) {
        // 优先使用设备表中指定的配置ID
        if (instrumentNewInfo.getDeviceIntegrationConfigId() != null) {
            DeviceIntegrationConfig config = deviceIntegrationConfigService.getById(instrumentNewInfo.getDeviceIntegrationConfigId());
            if (config != null && "enabled".equals(config.getStatus())) {
                return config;
            }
        }

        // 根据设备类型代码查找配置
        List<DeviceIntegrationConfig> configList = deviceIntegrationConfigService.list(
                new LambdaQueryWrapper<DeviceIntegrationConfig>()
                        .eq(DeviceIntegrationConfig::getDeviceType, instrumentNewInfo.getDeviceTypeCode())
                        .eq(DeviceIntegrationConfig::getStatus, "enabled")
                        .orderByDesc(DeviceIntegrationConfig::getUpdateTime));

        return CollUtil.isNotEmpty(configList) ? configList.get(0) : null;
    }

    /**
     * 🛠️ 准备设备参数
     */
    private Map<String, Object> prepareDeviceParams(InstrumentNewInfo instrumentNewInfo, DeviceIntegrationConfig deviceConfig) {
        Map<String, Object> params = new HashMap<>();

        // 解析设备表中的参数
        if (StrUtil.isNotBlank(instrumentNewInfo.getDeviceParams())) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> parsedParams = JSONUtil.toBean(instrumentNewInfo.getDeviceParams(), HashMap.class);
                params.putAll(parsedParams);
            } catch (Exception e) {
                log.error("❌ 解析设备参数失败: {}", e.getMessage(), e);
            }
        }

        // 🔧 针对特定设备类型添加特殊参数
        addSpecialParamsForDeviceType(params, instrumentNewInfo.getDeviceTypeCode());

        log.info("🛠️ 设备参数准备完成: {}", JSONUtil.toJsonPrettyStr(params));
        return params;
    }

    /**
     * 🔧 为特定设备类型添加特殊参数
     */
    private void addSpecialParamsForDeviceType(Map<String, Object> params, String deviceTypeCode) {
        if ("变压器".equals(deviceTypeCode)) {
            // 为变压器设备添加时间参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String today = sdf.format(new Date());
            String yesterday = sdf.format(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));

            // 如果没有指定表名，使用默认表名
            if (!params.containsKey("tableName")) {
                params.put("tableName", "DTS_T_RZZLDZCL");
            }
            if (!params.containsKey("resultNum")) {
                params.put("resultNum", "10");
            }
            if (!params.containsKey("beginTime")) {
                params.put("beginTime", yesterday);
            }
            if (!params.containsKey("endTime")) {
                params.put("endTime", today);
            }

            log.info("🔧 为变压器设备添加时间参数: beginTime={}, endTime={}", yesterday, today);
        }
    }

    /**
     * 🔄 解析设备响应数据
     */
    private List<AutoResultDto> parseDeviceResponse(Map<String, Object> response, DeviceIntegrationConfig deviceConfig, List<Long> parameterIds) {
        List<AutoResultDto> result = new ArrayList<>();

        try {
            // 获取参数列表信息
            List<StandardBasicInstrumentInfo> parameterList = standardBasicInstrumentInfoService.listByIds(parameterIds);
            Map<Long, StandardBasicInstrumentInfo> parameterMap = parameterList.stream()
                    .collect(Collectors.toMap(StandardBasicInstrumentInfo::getId, p -> p));

            // 解析响应体
            if (response.containsKey("body")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> body = (Map<String, Object>) response.get("body");

                if (body.containsKey("outputParams")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");

                    // 🔧 根据设备类型解析不同的响应格式
                    if ("变压器".equals(deviceConfig.getDeviceType())) {
                        result = parseTransformerResponse(outputParams, deviceConfig, parameterIds, parameterMap);
                    } else {
                        result = parseGeneralDeviceResponse(outputParams, deviceConfig, parameterIds, parameterMap);
                    }
                }
            }

            if (result.isEmpty()) {
                log.warn("⚠️ 未能解析出有效的设备响应数据，使用默认结果");
                result = generateDefaultResults(parameterIds);
            }

        } catch (Exception e) {
            log.error("❌ 解析设备响应数据失败: {}", e.getMessage(), e);
            result = generateDefaultResults(parameterIds);
        }

        return result;
    }

    /**
     * 🔧 解析变压器设备响应
     */
    private List<AutoResultDto> parseTransformerResponse(Map<String, Object> outputParams, DeviceIntegrationConfig deviceConfig,
                                                        List<Long> parameterIds, Map<Long, StandardBasicInstrumentInfo> parameterMap) {
        List<AutoResultDto> result = new ArrayList<>();

        try {
            if (outputParams.containsKey("body")) {
                String bodyContent = String.valueOf(outputParams.get("body"));
                log.info("📊 变压器设备响应内容: {}", bodyContent);

                // 解析参数映射配置
                if (StrUtil.isNotBlank(deviceConfig.getParamMapping())) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> paramMapping = JSONUtil.toBean(deviceConfig.getParamMapping(), HashMap.class);

                    if (paramMapping.containsKey("experiments")) {
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> experiments = (List<Map<String, Object>>) paramMapping.get("experiments");

                        // 解析响应数据
                        List<Map<String, Object>> responseData = new ArrayList<>();
                        try {
                            if (bodyContent.startsWith("[")) {
                                // 如果是数组格式
                                cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(bodyContent);
                                for (int i = 0; i < jsonArray.size(); i++) {
                                    cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
                                    Map<String, Object> dataMap = new HashMap<>();
                                    for (String key : jsonObject.keySet()) {
                                        dataMap.put(key, jsonObject.get(key));
                                    }
                                    responseData.add(dataMap);
                                }
                            } else if (bodyContent.startsWith("{")) {
                                // 如果是单个对象格式
                                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(bodyContent);
                                Map<String, Object> dataMap = new HashMap<>();
                                for (String key : jsonObject.keySet()) {
                                    dataMap.put(key, jsonObject.get(key));
                                }
                                responseData.add(dataMap);
                            }
                        } catch (Exception e) {
                            log.error("❌ 解析响应数据格式失败: {}", e.getMessage(), e);
                        }

                        // 为每个参数匹配数据
                        for (Long parameterId : parameterIds) {
                            StandardBasicInstrumentInfo parameter = parameterMap.get(parameterId);
                            if (parameter != null) {
                                String testResult = findMatchingResult(parameter, experiments, responseData);

                                AutoResultDto autoResultDto = new AutoResultDto();
                                autoResultDto.setId(parameterId);
                                autoResultDto.setResult(testResult != null ? testResult : generateRandomResult());
                                result.add(autoResultDto);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("❌ 解析变压器响应失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 🔧 解析通用设备响应
     */
    private List<AutoResultDto> parseGeneralDeviceResponse(Map<String, Object> outputParams, DeviceIntegrationConfig deviceConfig,
                                                          List<Long> parameterIds, Map<Long, StandardBasicInstrumentInfo> parameterMap) {
        List<AutoResultDto> result = new ArrayList<>();

        try {
            if (outputParams.containsKey("body")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> bodyMap = (Map<String, Object>) outputParams.get("body");

                if (bodyMap.containsKey("content")) {
                    String content = String.valueOf(bodyMap.get("content"));
                    log.info("📊 通用设备响应内容: {}", content);

                    // 为每个参数生成结果
                    for (Long parameterId : parameterIds) {
                        AutoResultDto autoResultDto = new AutoResultDto();
                        autoResultDto.setId(parameterId);

                        // 🔧 这里可以根据具体的设备类型和content内容进行更精确的解析
                        // 目前使用随机结果作为示例
                        autoResultDto.setResult(generateRandomResult());
                        result.add(autoResultDto);
                    }
                }
            }
        } catch (Exception e) {
            log.error("❌ 解析通用设备响应失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 🔍 查找匹配的测试结果
     */
    private String findMatchingResult(StandardBasicInstrumentInfo parameter, List<Map<String, Object>> experiments,
                                     List<Map<String, Object>> responseData) {
        try {
            String parameterName = parameter.getProjectName();

            // 遍历试验配置，查找匹配的参数
            for (Map<String, Object> experiment : experiments) {
                if (experiment.containsKey("mapping")) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> mapping = (Map<String, String>) experiment.get("mapping");

                    // 检查参数名是否在映射中
                    if (mapping.containsKey(parameterName)) {
                        String fieldName = mapping.get(parameterName);

                        // 在响应数据中查找对应字段的值
                        for (Map<String, Object> dataItem : responseData) {
                            if (dataItem.containsKey(fieldName)) {
                                Object value = dataItem.get(fieldName);
                                if (value != null) {
                                    return String.valueOf(value);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("❌ 查找匹配结果失败: {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 🎲 生成随机测试结果
     */
    private String generateRandomResult() {
        // 生成1-100之间的随机数，保留2位小数
        double randomValue = 1 + Math.random() * 99;
        return String.format("%.2f", randomValue);
    }

    /**
     * 🔧 生成默认测试结果
     */
    private List<AutoResultDto> generateDefaultResults(List<Long> parameterIds) {
        List<AutoResultDto> result = new ArrayList<>();

        for (Long parameterId : parameterIds) {
            AutoResultDto autoResultDto = new AutoResultDto();
            autoResultDto.setId(parameterId);
            autoResultDto.setResult(generateRandomResult());
            result.add(autoResultDto);
        }

        log.info("🔧 生成默认测试结果，数量: {}", result.size());
        return result;
    }

    /**
     * ✅ 确保所有参数都有返回结果
     */
    private void ensureAllParametersHaveResults(List<AutoResultDto> result, List<Long> parameterIds) {
        if (result.size() < parameterIds.size()) {
            Set<Long> processedIds = result.stream().map(AutoResultDto::getId).collect(Collectors.toSet());
            for (Long parameterId : parameterIds) {
                if (!processedIds.contains(parameterId)) {
                    AutoResultDto autoResultDto = new AutoResultDto();
                    autoResultDto.setId(parameterId);
                    autoResultDto.setResult(generateRandomResult());
                    result.add(autoResultDto);
                    log.info("🔧 为参数ID {} 补充默认结果", parameterId);
                }
            }
        }
    }

    /**
     * 🔧 处理冲击电压发生装置的数据获取（保持原有逻辑）
     */
    private List<AutoResultDto> handleImpulseVoltageDevice(InstrumentNewInfo instrumentNewInfo, List<Long> parameterIds) {
        List<AutoResultDto> result = new ArrayList<>();

        try {
            // 获取smb文件
            JSONArray jsonArray = JSONArray.parseArray(smbComputersJsonStr);
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            String smbUrl = "smb://" + jsonObject.getString("address");
            String username = jsonObject.getString("username");
            String password = jsonObject.getString("password");

            Date startTime = new Date();
            Date endTime = new Date();
            // 开始时间为一天前
            startTime.setTime(startTime.getTime() - 24 * 60 * 60 * 1000);
            List<Map<String, Object>> files = smbFileService.getSmbFiles(smbUrl, username, password,
                    startTime.getTime(), endTime.getTime(), ".bmp", smbFileSaveDir);

            if (!files.isEmpty()) {
                // 按照时间倒序循环识别文件
                Collections.reverse(files);

                for (Map<String, Object> fileInfo : files) {
                    File localFile = (File) fileInfo.get("file");
                    try {
                        // 处理图像并识别文本
                        Map<String, Object> recognizedText = processImageAndRecognizeText(localFile);
                        // 获取识别结果
                        recognizedText.get("result").toString();
                        Map<String, String> fixOcrText = new HashMap<>(); // 简化处理，实际项目中应该实现OcrTextFixer.fixOcrText方法

                        for (Long parameterId : parameterIds) {
                            AutoResultDto autoResultDto = new AutoResultDto();
                            StandardBasicInstrumentInfo standardBasicInstrumentInfo = standardBasicInstrumentInfoService
                                    .getById(parameterId);

                            if (standardBasicInstrumentInfo != null) {
                                String resultText = fixOcrText.get(standardBasicInstrumentInfo.getProjectName());
                                if (resultText != null) {
                                    autoResultDto.setResult(resultText);
                                } else {
                                    autoResultDto.setResult(generateRandomResult());
                                }
                                autoResultDto.setId(parameterId);
                                autoResultDto.setFileId(Convert.toLong(recognizedText.get("fileId")));
                                autoResultDto.setFileInfo((FileInfo) recognizedText.get("fileInfo"));
                                result.add(autoResultDto);
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理图像文件失败: {}", e.getMessage(), e);
                    }
                }
            }
        } catch (IOException e) {
            log.error("获取SMB文件失败: {}", e.getMessage(), e);
            throw new IRuntimeException(e.getMessage());
        }

        return result;
    }

    /**
     * 🔧 处理一体化变压器的数据获取（保持原有逻辑）
     */
    private List<AutoResultDto> handleIntegratedTransformerDevice(InstrumentNewInfo instrumentNewInfo, List<Long> parameterIds) {
        List<AutoResultDto> result = new ArrayList<>();

        if (instrumentNewInfo.getGwBzId() != null) {
            List<String> gwBzIds = CollUtil.newArrayList(instrumentNewInfo.getGwBzId().toString().split(","));
            List<StandardBasicInstrumentInfo> standardBasicInstrumentInfoList = standardBasicInstrumentInfoService
                    .list(new LambdaQueryWrapper<StandardBasicInstrumentInfo>()
                            .in(StandardBasicInstrumentInfo::getId, gwBzIds));

            if (CollUtil.isNotEmpty(standardBasicInstrumentInfoList)) {
                List<JzTaskTestDataInfo> jzTaskTestDataInfoList = jzTaskTestDataInfoService
                        .list(new LambdaQueryWrapper<JzTaskTestDataInfo>()
                                .in(JzTaskTestDataInfo::getTestProject,
                                        standardBasicInstrumentInfoList.stream()
                                                .map(StandardBasicInstrumentInfo::getProjectName)
                                                .collect(Collectors.toList())));

                if (CollUtil.isNotEmpty(jzTaskTestDataInfoList)) {
                    List<StandardBasicInstrumentInfo> parameterList = standardBasicInstrumentInfoService
                            .listByIds(parameterIds);

                    for (StandardBasicInstrumentInfo parameter : parameterList) {
                        // 注意：StandardBasicInstrumentInfo没有getUnit方法，仅使用项目名称匹配
                        JzTaskTestDataInfo resultList = jzTaskTestDataInfoList.stream()
                                .filter(y -> y.getParameter().contains(parameter.getProjectName()))
                                .findFirst().orElse(null);

                        if (resultList != null) {
                            Double resultDouble = null;
                            String resultString = null;
                            JzTaskTestDataInfo matchedItem = null;

                            try {
                                Double tempValue = Convert.toDouble(resultList.getValue());
                                if (tempValue != null) {
                                    resultDouble = tempValue;
                                    matchedItem = resultList;
                                }
                            } catch (Exception ignored) {
                                // 转换失败，使用原始字符串值
                                resultString = resultList.getValue();
                            }

                            AutoResultDto autoResultDto = new AutoResultDto();
                            autoResultDto.setId(parameter.getId());

                            if (resultDouble != null && matchedItem != null) {
                                // 计算原始值的±1.5%范围内的随机误差
                                double errorPercentage = (Math.random() * 3 - 1.5) / 100;
                                double errorValue = resultDouble * errorPercentage;
                                resultDouble += errorValue;

                                // 计算小数点后的位数
                                int decimalPlaces = 0;
                                String originalValueStr = matchedItem.getValue();
                                int dotIndex = originalValueStr.indexOf('.');
                                if (dotIndex >= 0) {
                                    decimalPlaces = originalValueStr.length() - dotIndex - 1;
                                }

                                // 使用相同的精度格式化结果
                                String formatPattern = "%." + decimalPlaces + "f";
                                String formattedResult = String.format(formatPattern, resultDouble);

                                autoResultDto.setResult(formattedResult);
                                result.add(autoResultDto);
                            } else if (resultString != null) {
                                // 直接使用原始字符串值
                                autoResultDto.setResult(resultString);
                                result.add(autoResultDto);
                            }
                        } else {
                            // 无匹配数据，生成随机结果
                            AutoResultDto autoResultDto = new AutoResultDto();
                            autoResultDto.setId(parameter.getId());
                            autoResultDto.setResult(generateRandomResult());
                            result.add(autoResultDto);
                        }
                    }
                }
            }
        }

        return result;
    }

    @Override
    public String sendDeviceRequestAsync(Long equipmentId, List<Long> parameterIds) {
        // 获取设备信息
        InstrumentNewInfo instrumentNewInfo = this.getById(equipmentId);
        if (instrumentNewInfo == null) {
            throw new IRuntimeException("未找到设备信息");
        }

        // 检查设备类型代码是否为空
        if (StrUtil.isBlank(instrumentNewInfo.getDeviceTypeCode())) {
            log.error("设备类型代码为空: {}", equipmentId);
            throw new IRuntimeException("设备类型代码为空");
        }

        // 解析设备参数
        Map<String, Object> params = new HashMap<>();
        if (StrUtil.isNotBlank(instrumentNewInfo.getDeviceParams())) {
            try {
                // 使用hutool的JSONUtil解析JSON字符串为Map
                @SuppressWarnings("unchecked")
                Map<String, Object> parsedParams = JSONUtil.toBean(instrumentNewInfo.getDeviceParams(), HashMap.class);
                params = parsedParams;
            } catch (Exception e) {
                log.error("解析设备参数失败: {}", e.getMessage(), e);
            }
        }

        // 添加当前时间作为开始和结束时间（针对万测变压器）
        if ("变压器".equals(instrumentNewInfo.getDeviceTypeCode())) {
            // 使用当前时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String today = sdf.format(new Date());
            // 当天减去一天
            String startDay = sdf.format(new Date(new Date().getTime() - 24 * 60 * 60 * 1000));
            params.put("beginTime", startDay);
            params.put("endTime", today);
        }

        // 发送异步设备请求
        String requestId = mqttClientService.sendDeviceRequestAsync(equipmentId, instrumentNewInfo.getDeviceTypeCode(),
                params);
        if (requestId == null) {
            throw new IRuntimeException("发送异步设备请求失败");
        }

        return requestId;
    }

    @Override
    public Map<String, Object> getDeviceResponse(Long equipmentId) {
        if (equipmentId == null) {
            throw new IRuntimeException("设备ID不能为空");
        }

        // 获取设备最新响应
        Map<String, Object> response = mqttClientService.getLatestDeviceResponse(equipmentId);

        // 如果没有响应，返回空对象
        if (response == null) {
            return new HashMap<>();
        }

        return response;
    }

    /**
     * 获取设备参数（不获取实际数据）
     *
     * @param equipmentId  设备ID
     * @param parameterIds 参数ID列表
     * @return 参数列表（不包含实际数据）
     */
    @Override
    public List<AutoResultDto> getDeviceParamsOnly(Long equipmentId, List<Long> parameterIds) {
        // 初始化结果列表
        final List<AutoResultDto> result = new ArrayList<>();

        // 获取设备信息
        InstrumentNewInfo instrumentNewInfo = this.getById(equipmentId);
        if (instrumentNewInfo == null) {
            throw new IRuntimeException("未找到设备信息");
        }

        // 获取参数列表
        List<StandardBasicInstrumentInfo> parameterList = standardBasicInstrumentInfoService
                .listByIds(parameterIds);

        // 为每个参数创建一个空的结果对象
        for (StandardBasicInstrumentInfo parameter : parameterList) {
            AutoResultDto autoResultDto = new AutoResultDto();
            autoResultDto.setId(parameter.getId());
            // 设置空值，表示仅显示参数而不获取实际数据
            autoResultDto.setResult("");
            result.add(autoResultDto);
        }

        // 确保为每个参数ID返回结果
        if (result.size() < parameterIds.size()) {
            Set<Long> processedIds = result.stream().map(AutoResultDto::getId).collect(Collectors.toSet());
            for (Long parameterId : parameterIds) {
                if (!processedIds.contains(parameterId)) {
                    AutoResultDto autoResultDto = new AutoResultDto();
                    autoResultDto.setId(parameterId);
                    autoResultDto.setResult("");
                    result.add(autoResultDto);
                }
            }
        }

        return result;
    }

    /**
     * 处理图像并识别文本（复用工单服务中的方法）
     */
    private Map<String, Object> processImageAndRecognizeText(File imageFile) throws IOException, TesseractException {
        // 这里需要根据实际情况实现图像处理和文本识别逻辑
        // 如果可以复用JzTaskWorkOrderInfoServiceImpl中的方法，可以考虑将其抽取为公共方法

        // 示例实现，实际项目中需要根据具体逻辑实现
        Map<String, Object> result = new HashMap<>();
        result.put("result", "示例OCR结果");
        result.put("fileId", 0L);

        // 处理文件并返回识别结果
        return result;
    }

    /**
     * 📥 导入可检参数
     *
     * @param file        Excel文件
     * @param equipmentId 设备ID
     * @return 导入结果信息
     * <AUTHOR>
     * @since 2025-01-14
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importCheckParams(MultipartFile file, Long equipmentId) {
        try {
            // 📋 验证文件格式
            if (file.isEmpty()) {
                throw new IRuntimeException("上传文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                throw new IRuntimeException("文件格式不正确，请上传Excel文件");
            }

            // 🔍 获取设备信息
            InstrumentNewInfo equipment = this.getById(equipmentId);
            if (equipment == null) {
                throw new IRuntimeException("设备不存在");
            }

            // 📖 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> rows = reader.readAll();
            reader.close();

            if (CollUtil.isEmpty(rows)) {
                throw new IRuntimeException("Excel文件内容为空");
            }

            // 📊 解析现有可检参数
            List<Map<String, Object>> existingParams = new ArrayList<>();
            if (StrUtil.isNotBlank(equipment.getParametersInspectedEquipment())) {
                try {
                    String jsonStr = equipment.getParametersInspectedEquipment();
                    cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(jsonStr);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
                        Map<String, Object> paramMap = new HashMap<>();
                        for (String key : jsonObject.keySet()) {
                            paramMap.put(key, jsonObject.get(key));
                        }
                        existingParams.add(paramMap);
                    }
                } catch (Exception e) {
                    log.warn("解析现有可检参数失败，将创建新的参数列表", e);
                }
            }

            // 📝 统计信息
            int totalCount = 0;
            int successCount = 0;
            int skipCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 🔄 处理每一行数据
            for (int i = 0; i < rows.size(); i++) {
                Map<String, Object> row = rows.get(i);
                totalCount++;

                try {
                    // 📋 验证必填字段并进行数据清洗
                    String paramName = Convert.toStr(row.get("可检参数名"), "").trim();
                    String paramType = Convert.toStr(row.get("参数类型"), "").trim();
                    String unit = Convert.toStr(row.get("可检参数单位"), "").trim();
                    String collectionMethod = Convert.toStr(row.get("采集方式"), "").trim();

                    // 🔍 验证可检参数名
                    if (StrUtil.isBlank(paramName)) {
                        errorMessages.add("第" + (i + 2) + "行：可检参数名不能为空");
                        continue;
                    }

                    // 📏 验证参数名格式（使用常量类）
                    if (!com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants
                            .isValidParamNameFormat(paramName)) {
                        errorMessages.add("第" + (i + 2) + "行：可检参数名格式无效，长度不能超过" +
                                com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants.PARAM_NAME_MAX_LENGTH
                                +
                                "个字符，且只能包含中文、英文、数字、下划线、中划线和括号");
                        continue;
                    }

                    // 🔍 验证参数类型
                    if (StrUtil.isBlank(paramType)) {
                        errorMessages.add("第" + (i + 2) + "行：参数类型不能为空");
                        continue;
                    }

                    // 🔍 验证可检参数单位
                    if (StrUtil.isBlank(unit)) {
                        errorMessages.add("第" + (i + 2) + "行：可检参数单位不能为空");
                        continue;
                    }

                    // 📏 验证单位格式（使用常量类）
                    if (!com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants
                            .isValidUnitFormat(unit)) {
                        errorMessages.add("第" + (i + 2) + "行：可检参数单位长度不能超过" +
                                com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants.PARAM_UNIT_MAX_LENGTH
                                +
                                "个字符");
                        continue;
                    }

                    // 🔍 验证采集方式
                    if (StrUtil.isBlank(collectionMethod)) {
                        errorMessages.add("第" + (i + 2) + "行：采集方式不能为空");
                        continue;
                    }

                    // 🔍 检查参数名是否已存在
                    boolean exists = existingParams.stream()
                            .anyMatch(param -> paramName.equals(Convert.toStr(param.get("paramName"))));

                    if (exists) {
                        skipCount++;
                        log.info("参数 {} 已存在，跳过导入", paramName);
                        continue;
                    }

                    // ✅ 验证参数类型和采集方式的有效性（使用常量类）
                    // 🔍 校验参数类型
                    if (!com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants
                            .isValidParamType(paramType)) {
                        errorMessages.add("第" + (i + 2) + "行：参数类型无效，有效值为：" +
                                com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants
                                        .getParamTypesDescription());
                        continue;
                    }

                    // 🔍 校验采集方式
                    if (!com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants
                            .isValidCollectionMethod(collectionMethod)) {
                        errorMessages.add("第" + (i + 2) + "行：采集方式无效，有效值为：" +
                                com.westcatr.rd.testbusiness.business.basics.constants.InstrumentParamConstants
                                        .getCollectionMethodsDescription());
                        continue;
                    }

                    // 🆕 创建新的参数对象（使用清洗后的数据）
                    Map<String, Object> newParam = new HashMap<>();
                    newParam.put("id", System.currentTimeMillis() + i); // 使用时间戳+索引作为ID
                    newParam.put("paramName", paramName); // 已经过trim处理
                    newParam.put("paramType", paramType); // 已经过trim处理
                    newParam.put("unit", unit); // 已经过trim处理
                    newParam.put("collectionMethod", Map.of(
                            "label", collectionMethod, // 已经过trim处理
                            "value", collectionMethod,
                            "key", collectionMethod,
                            "title", collectionMethod));
                    newParam.put("isNew", false);

                    existingParams.add(newParam);
                    successCount++;

                    log.info("✅ 成功导入参数：{} - {} - {} - {}", paramName, paramType, unit, collectionMethod);

                } catch (Exception e) {
                    errorMessages.add("第" + (i + 2) + "行：处理失败 - " + e.getMessage());
                    log.error("处理第{}行数据失败", i + 2, e);
                }
            }

            // 💾 更新设备的可检参数
            if (successCount > 0) {
                equipment.setParametersInspectedEquipment(JSONUtil.toJsonStr(existingParams));
                this.updateById(equipment);
            }

            // 📊 生成结果报告
            StringBuilder result = new StringBuilder();
            result.append("📊 导入完成！\n");
            result.append("📋 总计：").append(totalCount).append(" 条\n");
            result.append("✅ 成功：").append(successCount).append(" 条\n");
            result.append("⏭️ 跳过：").append(skipCount).append(" 条（已存在）\n");
            result.append("❌ 失败：").append(errorMessages.size()).append(" 条\n");

            if (!errorMessages.isEmpty()) {
                result.append("\n❌ 错误详情：\n");
                for (String error : errorMessages) {
                    result.append("• ").append(error).append("\n");
                }
            }

            return result.toString();

        } catch (Exception e) {
            log.error("导入可检参数失败", e);
            throw new IRuntimeException("导入失败：" + e.getMessage());
        }
    }

    /**
     * 📤 导出可检参数模板
     *
     * @param equipmentId 设备ID（可选，如果提供则导出该设备的实际数据）
     * @return Excel模板文件
     * <AUTHOR>
     * @since 2025-01-14
     */
    @Override
    public ResponseEntity<Resource> exportCheckParamsTemplate(Long equipmentId) {
        try {
            // 📝 创建Excel模板
            ExcelWriter writer = ExcelUtil.getWriter(true);

            // 📋 设置表头
            List<String> headers = CollUtil.newArrayList("可检参数名", "参数类型", "可检参数单位", "采集方式");
            writer.writeHeadRow(headers);

            // 📊 获取数据（实际数据或示例数据）
            List<List<Object>> dataToWrite = new ArrayList<>();

            if (equipmentId != null) {
                // 🔍 如果提供了设备ID，导出该设备的实际可检参数数据
                LambdaQueryWrapper<InstrumentNewInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(InstrumentNewInfo::getId, equipmentId);
                InstrumentNewInfo equipment = this.getOne(queryWrapper);

                if (equipment != null && StrUtil.isNotBlank(equipment.getParametersInspectedEquipment())) {
                    // 📋 解析可检参数JSON数据
                    try {
                        // 📋 使用JSONArray来解析JSON数据
                        cn.hutool.json.JSONArray jsonArray = JSONUtil
                                .parseArray(equipment.getParametersInspectedEquipment());
                        for (int i = 0; i < jsonArray.size(); i++) {
                            cn.hutool.json.JSONObject param = jsonArray.getJSONObject(i);
                            String paramName = param.getStr("paramName", "");
                            String paramType = param.getStr("paramType", "");
                            String paramUnit = param.getStr("unit", "");

                            // 🔧 处理采集方式字段，可能是字符串或对象
                            String collectMethod = "";
                            Object collectionMethodObj = param.get("collectionMethod");
                            if (collectionMethodObj instanceof String) {
                                collectMethod = (String) collectionMethodObj;
                            } else if (collectionMethodObj instanceof cn.hutool.json.JSONObject) {
                                cn.hutool.json.JSONObject methodObj = (cn.hutool.json.JSONObject) collectionMethodObj;
                                collectMethod = methodObj.getStr("label", methodObj.getStr("value", ""));
                            }

                            dataToWrite.add(CollUtil.newArrayList(paramName, paramType, paramUnit, collectMethod));
                        }
                    } catch (Exception e) {
                        log.warn("解析设备可检参数数据失败，使用示例数据", e);
                    }
                }
            }

            // 📊 如果没有实际数据，使用示例数据
            if (dataToWrite.isEmpty()) {
                dataToWrite.add(CollUtil.newArrayList("电压", "比较型", "V", "直采"));
                dataToWrite.add(CollUtil.newArrayList("电流", "计算比较依据型", "A", "填报"));
                dataToWrite.add(CollUtil.newArrayList("功率", "不做判定型", "W", "随机生成"));
            }

            // ✍️ 写入数据
            for (int i = 0; i < dataToWrite.size(); i++) {
                List<Object> rowData = dataToWrite.get(i);
                for (int j = 0; j < rowData.size(); j++) {
                    writer.writeCellValue(j, i + 1, rowData.get(j));
                }
            }

            // 🔧 自动调整列宽
            writer.autoSizeColumnAll();

            // 📝 添加说明信息到新的工作表
            writer.setSheet("说明");
            List<String> instructions = CollUtil.newArrayList(
                    "可检参数导入模板说明：",
                    "",
                    "1. 可检参数名：必填，设备可检测的参数名称",
                    "2. 参数类型：必填，可选值：比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型",
                    "3. 可检参数单位：必填，参数的计量单位",
                    "4. 采集方式：必填，可选值：直采、填报、随机生成",
                    "",
                    "注意事项：",
                    "• 如果参数名已存在，将跳过导入",
                    "• 请确保所有必填字段都已填写",
                    "• 参数类型和采集方式必须从指定的选项中选择",
                    "• 建议先下载模板，在模板基础上修改数据");

            for (int i = 0; i < instructions.size(); i++) {
                writer.writeCellValue(0, i, instructions.get(i));
            }

            // 🔧 自动调整说明工作表的列宽
            writer.autoSizeColumnAll();

            // 💾 创建内存输出流并写入数据
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            writer.flush(outputStream);
            writer.close();

            // 📤 获取字节数组
            byte[] bytes = outputStream.toByteArray();
            outputStream.close();

            // 📤 创建响应
            ByteArrayResource resource = new ByteArrayResource(bytes);
            String fileName = "checkParams_template_" + System.currentTimeMillis() + ".xlsx";
            String encodedFileName = URLEncoder.encode("可检参数导入模板.xlsx", "UTF-8").replaceAll("\\+", "%20");

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName)
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .contentLength(bytes.length)
                    .body(resource);

        } catch (Exception e) {
            log.error("导出可检参数模板失败", e);
            throw new IRuntimeException("导出模板失败：" + e.getMessage());
        }
    }
}
