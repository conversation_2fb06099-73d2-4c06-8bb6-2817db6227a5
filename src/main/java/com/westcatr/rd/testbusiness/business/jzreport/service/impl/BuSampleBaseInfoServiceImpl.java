package com.westcatr.rd.testbusiness.business.jzreport.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInfoService;
import com.westcatr.rd.testbusiness.business.jzreport.entity.BuSampleBaseInfo;
import com.westcatr.rd.testbusiness.business.jzreport.mapper.BuSampleBaseInfoMapper;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.BuSampleBaseInfoQuery;
import com.westcatr.rd.testbusiness.business.jzreport.service.BuSampleBaseInfoService;

import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 样品基本信息-关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Service
public class BuSampleBaseInfoServiceImpl extends ServiceImpl<BuSampleBaseInfoMapper, BuSampleBaseInfo>
        implements BuSampleBaseInfoService {

    @Autowired
    private BuSampleBaseInfoService buSampleBaseInfoService;

    @Autowired
    private StandardBasicInfoService standardBasicInfoService;

    @Override
    public IPage<BuSampleBaseInfo> entityPage(BuSampleBaseInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<BuSampleBaseInfo>().create(query));
    }

    @Override
    public BuSampleBaseInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(BuSampleBaseInfo param) {
        List<BuSampleBaseInfo> baseInfos = buSampleBaseInfoService.list(new LambdaQueryWrapper<BuSampleBaseInfo>()
                .eq(BuSampleBaseInfo::getName, param.getName()).eq(BuSampleBaseInfo::getType, param.getType())
                .eq(BuSampleBaseInfo::getModel, param.getModel()));

        if (CollUtil.isNotEmpty(baseInfos)) {
            throw new IRuntimeException("数据重复，请检查后再重新新增");
        }

        if (this.save(param)) {
            // standardBasicInfoService 判断是否有这个标准，没有就新增
            long count = standardBasicInfoService.count(new LambdaQueryWrapper<StandardBasicInfo>()
                    .eq(StandardBasicInfo::getIntegratedDeviceName, param.getName()));
            if (count == 0) {
                StandardBasicInfo standardBasicInfo = new StandardBasicInfo();
                standardBasicInfo.setIntegratedDeviceName(param.getName());
                standardBasicInfoService.save(standardBasicInfo);
            }
        }
        return true;
    }

    @Override
    public boolean updateEntity(BuSampleBaseInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}
