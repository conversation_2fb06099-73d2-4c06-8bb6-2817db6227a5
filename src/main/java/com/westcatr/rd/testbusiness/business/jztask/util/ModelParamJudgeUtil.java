package com.westcatr.rd.testbusiness.business.jztask.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 模型参数判断工具类
 * 
 * <AUTHOR>
 * @since 2025-04-20
 */
@Slf4j
public class ModelParamJudgeUtil {

    /**
     * 特殊符号映射表
     */
    private static final Map<String, String> SPECIAL_SYMBOL_MAP = new HashMap<>();

    static {
        // 初始化特殊符号映射
        SPECIAL_SYMBOL_MAP.put("×", "*");
        SPECIAL_SYMBOL_MAP.put("÷", "/");
        SPECIAL_SYMBOL_MAP.put("≥", ">=");
        SPECIAL_SYMBOL_MAP.put("≤", "<=");
        SPECIAL_SYMBOL_MAP.put("=", "==");
        SPECIAL_SYMBOL_MAP.put("∪", "|"); // 并集用 | 代替
        SPECIAL_SYMBOL_MAP.put("∩", "&"); // 交集用 & 代替
    }

    /**
     * 判断参数是否合格
     * 
     * @param modelParamList 参数列表
     * @return 处理后的参数列表（已设置合格状态）
     */
    public static List<JzTaskWorkOrderModelParam> judgeQualified(List<JzTaskWorkOrderModelParam> modelParamList) {
        if (modelParamList == null || modelParamList.isEmpty()) {
            return modelParamList;
        }

        // 建立参数键值映射，用于后续公式计算
        Map<String, String> paramValueMap = new HashMap<>();
        for (JzTaskWorkOrderModelParam param : modelParamList) {
            if (param.getParamKey() != null && param.getTestResult() != null) {
                paramValueMap.put(param.getParamKey(), param.getTestResult());
            }
        }

        // 遍历每个参数进行判断
        for (JzTaskWorkOrderModelParam param : modelParamList) {
            try {
                // 设置合格状态
                String qualified = judgeParamQualified(param, paramValueMap);
                param.setTfQualified(qualified);
            } catch (Exception e) {
                log.error("判断参数【{}】是否合格失败: {}", param.getParamKey(), e.getMessage(), e);
                throw new IRuntimeException("判断参数【" + param.getParamKey() + "】是否合格失败: " + e.getMessage());
            }
        }

        return modelParamList;
    }

    /**
     * 判断单个参数是否合格
     * 
     * @param param         参数对象
     * @param paramValueMap 所有参数的值映射
     * @return 合格状态：合格/不合格
     */
    private static String judgeParamQualified(JzTaskWorkOrderModelParam param, Map<String, String> paramValueMap) {
        // 如果没有判定公式，则无法判断
        if (StrUtil.isBlank(param.getJudgeFormula())) {
            return null;
        }

        // 如果没有测试结果，则无法判断
        if (StrUtil.isBlank(param.getTestResult())) {
            return null;
        }

        String judgeType = param.getJudgeType();
        String judgeFormula = param.getJudgeFormula();

        // 人工判定型，直接返回"待判定"
        if ("人工判定型".equals(judgeType)) {
            return null;
        }

        // 不做判定型，直接返回"无需判定"
        if ("不做判定型".equals(judgeType)) {
            return null;
        }

        // 定义要尝试的判断类型顺序
        List<String> judgeTypesToTry = new ArrayList<>();

        // 如果有指定类型，先尝试指定的类型
        if (StrUtil.isNotBlank(judgeType)) {
            judgeTypesToTry.add(judgeType);
        }

        // 添加其他类型作为备选
        if (!"比较型".equals(judgeType))
            judgeTypesToTry.add("比较型");
        if (!"计算比较依据型".equals(judgeType))
            judgeTypesToTry.add("计算比较依据型");
        if (!"计算比较结果型".equals(judgeType))
            judgeTypesToTry.add("计算比较结果型");
        judgeTypesToTry.add("通用公式"); // 最后尝试通用公式

        // 记录所有错误信息
        StringBuilder errorMessages = new StringBuilder();

        // 依次尝试各种判断类型
        for (String typeToTry : judgeTypesToTry) {
            try {
                String result = null;

                if ("比较型".equals(typeToTry)) {
                    result = judgeCompareType(param, judgeFormula);
                } else if ("计算比较依据型".equals(typeToTry)) {
                    result = judgeCalculateBaseType(param, paramValueMap, judgeFormula);
                } else if ("计算比较结果型".equals(typeToTry)) {
                    result = judgeCalculateResultType(param, paramValueMap, judgeFormula);
                } else {
                    // 通用公式
                    result = judgeGenericFormula(param, paramValueMap, judgeFormula);
                }

                // 如果成功获取结果，记录实际使用的判断类型（仅用于日志）
                if (!typeToTry.equals(judgeType)) {
                    log.info("参数【{}】使用了自动匹配的判断类型【{}】代替原类型【{}】",
                            param.getParamKey(), typeToTry, judgeType);
                }

                return result;
            } catch (Exception e) {
                // 记录错误信息，继续尝试下一种类型
                errorMessages.append("尝试【").append(typeToTry).append("】失败: ")
                        .append(e.getMessage()).append("; ");
            }
        }

        // 所有类型都尝试失败，抛出异常
        log.error("判断公式【{}】计算失败，已尝试所有可能的判断类型", judgeFormula);
        throw new IRuntimeException("判断公式【" + judgeFormula + "】计算失败: " + errorMessages.toString());
    }

    /**
     * 判断比较型参数是否合格
     */
    private static String judgeCompareType(JzTaskWorkOrderModelParam param, String judgeFormula) {
        // 示例: x1>200
        String testResult = param.getTestResult();
        if (StrUtil.isBlank(testResult)) {
            return "无法判断";
        }

        // 处理数值
        double testValue;
        try {
            testValue = Double.parseDouble(testResult);
        } catch (NumberFormatException e) {
            throw new IRuntimeException("测试结果【" + testResult + "】不是有效的数值");
        }

        // 解析公式
        Pattern pattern = Pattern.compile("([a-zA-Z0-9_]+)\\s*([><]=?|=|!=|≥|≤)\\s*([0-9.]+)");
        Matcher matcher = pattern.matcher(judgeFormula);

        if (matcher.find()) {
            String operator = matcher.group(2);
            double threshold = Double.parseDouble(matcher.group(3));

            // 转换特殊符号
            if (SPECIAL_SYMBOL_MAP.containsKey(operator)) {
                operator = SPECIAL_SYMBOL_MAP.get(operator);
            }

            switch (operator) {
                case ">":
                    return testValue > threshold ? "合格" : "不合格";
                case ">=":
                    return testValue >= threshold ? "合格" : "不合格";
                case "<":
                    return testValue < threshold ? "合格" : "不合格";
                case "<=":
                    return testValue <= threshold ? "合格" : "不合格";
                case "==":
                    return Math.abs(testValue - threshold) < 0.0001 ? "合格" : "不合格";
                case "!=":
                    return Math.abs(testValue - threshold) >= 0.0001 ? "合格" : "不合格";
                default:
                    throw new IRuntimeException("不支持的操作符: " + operator);
            }
        } else {
            throw new IRuntimeException("公式格式不正确: " + judgeFormula);
        }
    }

    /**
     * 判断计算比较依据型参数是否合格
     */
    private static String judgeCalculateBaseType(JzTaskWorkOrderModelParam param, Map<String, String> paramValueMap,
            String judgeFormula) {
        // 分接位置的平衡率 <1.35%
        String qualifiedStandard = param.getQualifiedStandard();
        if (StrUtil.isBlank(qualifiedStandard)) {
            return "无法判断";
        }

        // 提取阈值和比较符号
        Pattern thresholdPattern = Pattern.compile("([<>]=?|=|!=|≥|≤)\\s*([0-9.]+)%?");
        Matcher thresholdMatcher = thresholdPattern.matcher(qualifiedStandard);

        if (!thresholdMatcher.find()) {
            throw new IRuntimeException("合格标准格式不正确: " + qualifiedStandard);
        }

        String operator = thresholdMatcher.group(1);
        double threshold = Double.parseDouble(thresholdMatcher.group(2));

        // 转换特殊符号
        if (SPECIAL_SYMBOL_MAP.containsKey(operator)) {
            operator = SPECIAL_SYMBOL_MAP.get(operator);
        }

        // 计算结果
        double result = calculateFormulaValue(judgeFormula, paramValueMap);
        // 转换为百分比
        if (qualifiedStandard.contains("%")) {
            result = result * 100;
        }

        // 进行比较
        return compareValues(result, operator, threshold);
    }

    /**
     * 判断计算比较结果型参数是否合格
     */
    private static String judgeCalculateResultType(JzTaskWorkOrderModelParam param, Map<String, String> paramValueMap,
            String judgeFormula) {
        // 类似: (y2-y1)/(y1+y2)+(y3-y2)/(y3+y2)
        // 计算结果
        double result = calculateFormulaValue(judgeFormula, paramValueMap);

        // 从合格标准提取阈值
        String qualifiedStandard = param.getQualifiedStandard();
        if (StrUtil.isBlank(qualifiedStandard)) {
            return "无法判断";
        }

        // 提取阈值和比较符号
        Pattern thresholdPattern = Pattern.compile("([<>]=?|=|!=|≥|≤)\\s*([0-9.]+)%?");
        Matcher thresholdMatcher = thresholdPattern.matcher(qualifiedStandard);

        if (!thresholdMatcher.find()) {
            throw new IRuntimeException("合格标准格式不正确: " + qualifiedStandard);
        }

        String operator = thresholdMatcher.group(1);
        double threshold = Double.parseDouble(thresholdMatcher.group(2));

        // 转换特殊符号
        if (SPECIAL_SYMBOL_MAP.containsKey(operator)) {
            operator = SPECIAL_SYMBOL_MAP.get(operator);
        }

        // 如果含有百分号，需要将结果转换为百分比
        if (qualifiedStandard.contains("%")) {
            result = result * 100;
        }

        // 进行比较
        return compareValues(result, operator, threshold);
    }

    /**
     * 比较两个值
     */
    private static String compareValues(double value, String operator, double threshold) {
        switch (operator) {
            case ">":
                return value > threshold ? "合格" : "不合格";
            case ">=":
                return value >= threshold ? "合格" : "不合格";
            case "<":
                return value < threshold ? "合格" : "不合格";
            case "<=":
                return value <= threshold ? "合格" : "不合格";
            case "==":
                return Math.abs(value - threshold) < 0.0001 ? "合格" : "不合格";
            case "!=":
                return Math.abs(value - threshold) >= 0.0001 ? "合格" : "不合格";
            default:
                throw new IRuntimeException("不支持的操作符: " + operator);
        }
    }

    /**
     * 判断通用公式是否合格
     */
    private static String judgeGenericFormula(JzTaskWorkOrderModelParam param, Map<String, String> paramValueMap,
            String judgeFormula) {
        try {
            // 检查公式中是否包含未替换的变量
            Pattern varPattern = Pattern.compile("\\b[a-zA-Z][a-zA-Z0-9_]*\\b");
            Matcher varMatcher = varPattern.matcher(judgeFormula);
            Set<String> variables = new HashSet<>();

            while (varMatcher.find()) {
                String variable = varMatcher.group();
                // 排除数学函数
                if (!Arrays.asList("sin", "cos", "tan", "abs", "log", "sqrt", "pow", "exp", "max", "min")
                        .contains(variable)) {
                    variables.add(variable);
                }
            }

            // 检查变量是否都有对应的值
            boolean allVariablesHaveValues = true;
            Set<String> missingVariables = new HashSet<>();

            for (String variable : variables) {
                if (!paramValueMap.containsKey(variable)) {
                    allVariablesHaveValues = false;
                    missingVariables.add(variable);
                }
            }

            // 如果有变量没有值，记录日志并返回无法判断
            if (!allVariablesHaveValues) {
                log.warn("公式【{}】中的变量【{}】未找到对应的值，无法进行判断", judgeFormula, String.join(", ", missingVariables));
                return "无法判断";
            }

            // 替换公式中的变量
            String formula = replaceVariablesAndSymbols(judgeFormula, paramValueMap);

            // 尝试作为逻辑表达式计算
            try {
                boolean result = evaluateLogicalExpression(formula);
                return result ? "合格" : "不合格";
            } catch (Exception e) {
                // 如果逻辑表达式计算失败，尝试作为数学等式计算
                if (formula.contains("=")) {
                    return evaluateEquation(formula);
                } else {
                    // 尝试直接计算结果并与0比较
                    try {
                        double value = NumberUtil.calculate(formula);
                        // 非零值视为真，零值视为假
                        return Math.abs(value) > 0.0001 ? "合格" : "不合格";
                    } catch (Exception ex) {
                        throw new IRuntimeException("计算公式【" + formula + "】失败: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            throw new IRuntimeException("计算公式【" + judgeFormula + "】失败: " + e.getMessage());
        }
    }

    /**
     * 评估等式表达式
     */
    private static String evaluateEquation(String equation) {
        // 处理形如 a=b 的等式
        String[] parts = equation.split("=", 2);
        if (parts.length != 2) {
            throw new IRuntimeException("无效的等式: " + equation);
        }

        try {
            double leftValue = NumberUtil.calculate(parts[0].trim());
            double rightValue = NumberUtil.calculate(parts[1].trim());

            // 比较左右两边的值是否相等（考虑浮点数精度）
            return Math.abs(leftValue - rightValue) < 0.0001 ? "合格" : "不合格";
        } catch (Exception e) {
            throw new IRuntimeException("计算等式【" + equation + "】失败: " + e.getMessage());
        }
    }

    /**
     * 评估逻辑表达式
     */
    private static boolean evaluateLogicalExpression(String expression) {
        // 支持多个条件的逻辑表达式
        // 暂时简化处理，只支持单一比较条件
        expression = expression.trim();

        // 检查表达式中是否包含字母（可能是未替换的变量）
        if (expression.matches(".*[a-zA-Z].*")) {
            throw new IRuntimeException("表达式中包含未替换的变量: " + expression);
        }

        Pattern pattern = Pattern.compile("(.*?)\\s*(>=|<=|>|<|==|!=)\\s*(.*)");
        Matcher matcher = pattern.matcher(expression);

        if (matcher.matches()) {
            String leftStr = matcher.group(1).trim();
            String operator = matcher.group(2);
            String rightStr = matcher.group(3).trim();

            try {
                double left = NumberUtil.calculate(leftStr);
                double right = NumberUtil.calculate(rightStr);

                switch (operator) {
                    case ">":
                        return left > right;
                    case ">=":
                        return left >= right;
                    case "<":
                        return left < right;
                    case "<=":
                        return left <= right;
                    case "==":
                        return Math.abs(left - right) < 0.0001;
                    case "!=":
                        return Math.abs(left - right) >= 0.0001;
                    default:
                        throw new IRuntimeException("不支持的操作符: " + operator);
                }
            } catch (Exception e) {
                throw new IRuntimeException("计算表达式【" + expression + "】失败: " + e.getMessage());
            }
        } else {
            throw new IRuntimeException("无法解析逻辑表达式: " + expression);
        }
    }

    /**
     * 计算公式的值
     */
    private static double calculateFormulaValue(String formula, Map<String, String> paramValueMap) {
        // 替换公式中的变量和特殊符号
        String replacedFormula = replaceVariablesAndSymbols(formula, paramValueMap);

        try {
            // 使用hutool的NumberUtil计算表达式
            return NumberUtil.calculate(replacedFormula);
        } catch (Exception e) {
            throw new IRuntimeException("计算公式【" + replacedFormula + "】失败: " + e.getMessage());
        }
    }

    /**
     * 替换公式中的变量和特殊符号
     */
    private static String replaceVariablesAndSymbols(String formula, Map<String, String> paramValueMap) {
        // 先替换特殊符号
        String result = replaceSpecialSymbols(formula);

        // 再替换变量
        return replaceVariables(result, paramValueMap);
    }

    /**
     * 替换特殊符号
     */
    private static String replaceSpecialSymbols(String formula) {
        String result = formula;
        for (Map.Entry<String, String> entry : SPECIAL_SYMBOL_MAP.entrySet()) {
            result = result.replace(entry.getKey(), entry.getValue());
        }
        return result;
    }

    /**
     * 替换公式中的变量
     */
    private static String replaceVariables(String formula, Map<String, String> paramValueMap) {
        String result = formula;

        // 查找公式中的所有变量
        Pattern pattern = Pattern.compile("([a-zA-Z][a-zA-Z0-9_]*)");
        Matcher matcher = pattern.matcher(formula);

        // 数学函数名集合
        Set<String> mathFunctions = new HashSet<>(
                Arrays.asList("sin", "cos", "tan", "abs", "log", "sqrt", "pow", "exp", "max", "min"));

        // 记录未找到值的变量
        Set<String> missingVariables = new HashSet<>();

        while (matcher.find()) {
            String variable = matcher.group();

            // 跳过数学函数名等
            if (mathFunctions.contains(variable)) {
                continue;
            }

            String value = paramValueMap.get(variable);
            if (value == null) {
                // 记录未找到值的变量，但不抛出异常
                missingVariables.add(variable);
                continue;
            }

            try {
                // 检查值是否为数字
                Double.parseDouble(value);
                // 替换变量
                result = result.replaceAll("\\b" + variable + "\\b", value);
            } catch (NumberFormatException e) {
                throw new IRuntimeException("变量【" + variable + "】的值【" + value + "】不是有效的数值");
            }
        }

        // 如果有未找到值的变量，记录日志
        if (!missingVariables.isEmpty()) {
            log.warn("公式【{}】中的变量【{}】未找到对应的值，已忽略这些变量参与的计算", formula, String.join(", ", missingVariables));
        }

        return result;
    }
}
