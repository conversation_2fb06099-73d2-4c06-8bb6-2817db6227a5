package com.westcatr.rd.testbusiness.business.gwstandard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 标准—集成装置设备信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("standard_basic_info")
@Schema(description="标准—集成装置设备信息表")
public class StandardBasicInfo extends Model<StandardBasicInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "集成装置名称")
    @Length(max = 255, message = "集成装置名称长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("integrated_device_name")
    private String integratedDeviceName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
