package com.westcatr.rd.testbusiness.business.basics.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentCameraInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.BuInstrumentCameraInfoVO;

/**
 * <p>
 * 海康威视摄像头管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface BuInstrumentCameraInfoService extends IService<BuInstrumentCameraInfo> {

    IPage<BuInstrumentCameraInfo> entityPage(BuInstrumentCameraInfoQuery query);

    BuInstrumentCameraInfo getEntityById(Long id);

    boolean saveEntity(BuInstrumentCameraInfo param);

    boolean updateEntity(BuInstrumentCameraInfo param);

    boolean removeEntityById(Long id);

    /**
     * 获取摄像头流地址
     * 
     * @param cameraId
     * @return
     */
    String getCameraStreamUrl(Long cameraId);

    List<BuInstrumentCameraInfoVO> getCameraList();

}
