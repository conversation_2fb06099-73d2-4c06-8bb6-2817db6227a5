package com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—集成装置设备信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="标准—集成装置设备信息表VO对象")
public class StandardBasicInfoVO extends StandardBasicInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
