package com.westcatr.rd.testbusiness.business.basics.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.InstrumentNewInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentNewInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.DeviceTestDataRequestDto;
import com.westcatr.rd.testbusiness.business.mqtt.service.UnifiedDeviceIntegrationService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * InstrumentNewInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Validated
@Tag(name = "仪器仪表接口", description = "仪器仪表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class InstrumentNewInfoController {

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private UnifiedDeviceIntegrationService unifiedDeviceIntegrationService;

    @Operation(summary = "获取分页数据")
    @PostMapping("/instrumentNewInfo/page")
    public IResult<IPage<InstrumentNewInfo>> getInstrumentNewInfoPage(@RequestBody InstrumentNewInfoQuery query) {
        return IResult.ok(instrumentNewInfoService.entityPage(query));
    }

    @Operation(summary = "获取数据")
    @PostMapping("/instrumentNewInfo/get")
    public IResult<InstrumentNewInfo> getInstrumentNewInfoById(@RequestBody @Id ID id) {
        return IResult.ok(instrumentNewInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增数据")
    @PostMapping("/instrumentNewInfo/add")
    public IResult<Boolean> addInstrumentNewInfo(@RequestBody @Validated(Insert.class) InstrumentNewInfo param) {
        return IResult.auto(instrumentNewInfoService.saveEntity(param));
    }

    @Operation(summary = "更新数据")
    @PostMapping("/instrumentNewInfo/update")
    public IResult<Boolean> updateInstrumentNewInfoById(@RequestBody @Validated(Update.class) InstrumentNewInfo param) {
        return IResult.auto(instrumentNewInfoService.updateEntity(param));
    }

    @Operation(summary = "删除数据")
    @PostMapping("/instrumentNewInfo/delete")
    public IResult<Void> deleteInstrumentNewInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            instrumentNewInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取VO分页数据")
    @PostMapping("/instrumentNewInfo/voPage")
    public IResult<IPage<InstrumentNewInfoVO>> getInstrumentNewInfoVoPage(@RequestBody InstrumentNewInfoQuery query) {
        return IResult.ok(instrumentNewInfoService.entityMyPage(query));
    }

    @Operation(summary = "获取VO数据")
    @PostMapping("/instrumentNewInfo/getVo")
    public IResult<InstrumentNewInfoVO> getInstrumentNewInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<InstrumentNewInfoVO> associationQuery = new AssociationQuery<>(InstrumentNewInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "获取设备检测数据")
    @PostMapping("/instrumentNewInfo/getDeviceTestData")
    public IResult<List<AutoResultDto>> getDeviceTestData(@RequestBody DeviceTestDataRequestDto request) {
        return IResult
                .ok(instrumentNewInfoService.getDeviceTestData(request.getEquipmentId(), request.getParameterIds()));
    }

    @Operation(summary = "发送异步设备请求")
    @PostMapping("/instrumentNewInfo/sendDeviceRequestAsync")
    public IResult<String> sendDeviceRequestAsync(@RequestBody DeviceTestDataRequestDto request) {
        return IResult.ok(
                instrumentNewInfoService.sendDeviceRequestAsync(request.getEquipmentId(), request.getParameterIds()));
    }

    @Operation(summary = "获取异步设备响应")
    @PostMapping("/instrumentNewInfo/getDeviceResponse")
    public IResult<Map<String, Object>> getDeviceResponse(@RequestBody DeviceTestDataRequestDto request) {
        return IResult.ok(instrumentNewInfoService.getDeviceResponse(request.getEquipmentId()));
    }

    @Operation(summary = "获取设备参数（不获取实际数据）")
    @PostMapping("/instrumentNewInfo/getDeviceParamsOnly")
    public IResult<List<AutoResultDto>> getDeviceParamsOnly(@RequestBody DeviceTestDataRequestDto request) {
        return IResult
                .ok(instrumentNewInfoService.getDeviceParamsOnly(request.getEquipmentId(), request.getParameterIds()));
    }

    @Operation(summary = "🚀 统一设备对接 - 获取设备测试数据")
    @PostMapping("/instrumentNewInfo/getUnifiedDeviceTestData")
    public IResult<List<AutoResultDto>> getUnifiedDeviceTestData(@RequestBody DeviceTestDataRequestDto request) {
        log.info("🚀 统一设备对接请求 - 设备ID: {}, 参数数量: {}",
                request.getEquipmentId(),
                request.getParameterIds() != null ? request.getParameterIds().size() : 0);

        try {
            List<AutoResultDto> results = unifiedDeviceIntegrationService.getDeviceTestData(
                    request.getEquipmentId(), request.getParameterIds());

            log.info("✅ 统一设备对接成功，返回结果数量: {}", results.size());
            return IResult.ok(results);

        } catch (Exception e) {
            log.error("❌ 统一设备对接失败: {}", e.getMessage(), e);
            return IResult.fail("统一设备对接失败: " + e.getMessage());
        }
    }

    /**
     * 📥 导入可检参数
     *
     * <AUTHOR>
     * @since 2025-01-14
     */
    @Operation(summary = "导入可检参数")
    @PostMapping("/instrumentNewInfo/importCheckParams")
    public IResult<String> importCheckParams(@RequestParam("file") MultipartFile file,
            @RequestParam("equipmentId") Long equipmentId) {
        try {
            String result = instrumentNewInfoService.importCheckParams(file, equipmentId);
            return IResult.ok(result);
        } catch (Exception e) {
            log.error("导入可检参数失败", e);
            return IResult.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 📤 导出可检参数模板
     *
     * @param equipmentId 设备ID（可选，如果提供则导出该设备的实际数据）
     * <AUTHOR>
     * @since 2025-01-14
     */
    @Operation(summary = "导出可检参数模板")
    @PostMapping("/instrumentNewInfo/exportCheckParamsTemplate")
    public ResponseEntity<Resource> exportCheckParamsTemplate(
            @RequestParam(value = "equipmentId", required = false) Long equipmentId) {
        try {
            return instrumentNewInfoService.exportCheckParamsTemplate(equipmentId);
        } catch (Exception e) {
            log.error("导出可检参数模板失败", e);
            throw new RuntimeException("导出模板失败：" + e.getMessage());
        }
    }
}
