package com.westcatr.rd.testbusiness.business.org.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @version : V1.0
 * <AUTHOR> yingH
 * @Create : 2023-12-08 9:20
 * @Detail :  重置密码VO
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="重置密码VO")
public class resetPasswordVo {

    @Schema(description = "当前登录用户密码")
    private String oldPassword;

    @Schema(description = "重置用户id")
    private Long userId;

}
