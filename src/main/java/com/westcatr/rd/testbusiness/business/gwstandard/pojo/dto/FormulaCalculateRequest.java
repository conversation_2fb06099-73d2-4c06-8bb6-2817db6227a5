package com.westcatr.rd.testbusiness.business.gwstandard.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 📊 公式计算请求DTO
 * 
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@Schema(description = "公式计算请求")
public class FormulaCalculateRequest {
    
    /**
     * 计算公式
     */
    @Schema(description = "计算公式，例如：avg、(x1+x2+x3)/3、n=(x1+x2+x3)/3*200")
    private String formula;
    
    /**
     * 参数值映射，key为参数名，value为参数值
     */
    @Schema(description = "参数值映射，key为参数名，value为参数值")
    private Map<String, BigDecimal> paramValues;
}
