package com.westcatr.rd.testbusiness.business.datasync.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.westcatr.rd.testbusiness.business.datasync.entity.LogDeviceFunctionCall;
import com.westcatr.rd.testbusiness.business.datasync.entity.LogDeviceReport;
import com.westcatr.rd.testbusiness.business.datasync.mapper.LogDeviceFunctionCallMapper;
import com.westcatr.rd.testbusiness.business.datasync.mapper.postgresql.PostgreSQLLogDeviceReportMapper;
import com.westcatr.rd.testbusiness.business.datasync.service.DataSyncService;
import com.westcatr.rd.testbusiness.configs.DataSyncConfig;
import com.westcatr.rd.testbusiness.configs.IotDao;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据同步服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService {

    @Autowired
    private DataSyncConfig dataSyncConfig;

    @Autowired
    private LogDeviceFunctionCallMapper mysqlMapper;

    @Autowired
    private PostgreSQLLogDeviceReportMapper postgresqlMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IotDao iotDao;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String SYNC_LOCK_KEY = "data:sync:lock";
    private static final String LAST_SYNC_TIME_KEY = "data:sync:last_time";
    private static final String SYNC_STATS_KEY = "data:sync:stats";
    private static final String SYNC_STATUS_KEY = "data:sync:status";

    @Override
    public Map<String, Object> syncData() {
        String lockKey = dataSyncConfig.getRedisKeyPrefix() + SYNC_LOCK_KEY;

        // 尝试获取分布式锁，避免重复同步
        Boolean lockAcquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);

        if (!lockAcquired) {
            log.warn("🔒 数据同步正在进行中，跳过本次同步");
            return createSyncResult(0, 0, 0, "同步正在进行中");
        }

        try {
            Date lastSyncTime = getLastSyncTime();
            // log.warn("🚀 开始执行数据同步，上次同步时间: {}", lastSyncTime != null ?
            // DateUtil.formatDateTime(lastSyncTime) : "首次同步");

            return syncIncrementalData(lastSyncTime);

        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncIncrementalData(Date lastSyncTime) {
        long startTime = System.currentTimeMillis();
        int totalSynced = 0;
        int totalSkipped = 0;
        int batchCount = 0;

        try {
            // 更新同步状态
            updateSyncStatus("RUNNING", "正在同步数据...");

            // 如果没有上次同步时间，使用一个较早的时间进行全量同步
            if (lastSyncTime == null) {
                // 全量同步：从很早的时间开始同步所有数据
                lastSyncTime = DateUtil.offsetDay(new Date(), -365); // 同步最近一年的数据
                log.warn("📅 首次同步或表已清空，开始全量同步，起始时间为: {}", DateUtil.formatDateTime(lastSyncTime));
            }

            Date maxSyncTime = lastSyncTime;
            // 注意：这里不再重复减去天数，避免查询范围过早
            boolean hasMoreData = true;

            while (hasMoreData) {
                // 从PostgreSQL的zzzzz_log_device_report表查询增量数据
                List<LogDeviceReport> reportData = postgresqlMapper.selectIncrementalData(lastSyncTime,
                        dataSyncConfig.getBatchSize());

                if (CollectionUtils.isEmpty(reportData)) {
                    hasMoreData = false;
                    break;
                }

                // log.warn("📊 查询到 {} 条增量数据，开始处理第 {} 批", reportData.size(), ++batchCount);

                // 将LogDeviceReport转换为LogDeviceFunctionCall
                List<LogDeviceFunctionCall> convertedData = convertReportToFunctionCall(reportData);

                // 过滤已存在的数据
                List<LogDeviceFunctionCall> newData = filterExistingData(convertedData);

                if (!CollectionUtils.isEmpty(newData)) {
                    // 批量插入新数据
                    try {
                        // 使用MyBatis-Plus的saveBatch方法
                        for (LogDeviceFunctionCall data : newData) {
                            mysqlMapper.insert(data);
                        }
                        totalSynced += newData.size();
                        log.warn("✅ 成功同步 {} 条新数据", newData.size());
                    } catch (Exception e) {
                        log.error("❌ 批量插入失败: {}", e.getMessage(), e);
                        throw new RuntimeException("批量插入数据失败", e);
                    }
                }

                totalSkipped += (convertedData.size() - newData.size());

                // 更新最大同步时间
                Date batchMaxTime = reportData.stream()
                        .map(LogDeviceReport::getCreateTime)
                        .max(Date::compareTo)
                        .orElse(maxSyncTime);

                if (batchMaxTime.after(maxSyncTime)) {
                    maxSyncTime = batchMaxTime;
                }

                // 更新lastSyncTime为当前批次的最大时间，用于下次查询
                lastSyncTime = maxSyncTime;

                // 如果查询到的数据少于批量大小，说明没有更多数据了
                if (reportData.size() < dataSyncConfig.getBatchSize()) {
                    hasMoreData = false;
                }
            }

            // 更新最后同步时间
            if (maxSyncTime != null) {
                updateLastSyncTime(maxSyncTime);
            }

            long costTime = System.currentTimeMillis() - startTime;
            String message = String.format("同步完成，耗时: %dms", costTime);

            // 更新同步状态和统计
            updateSyncStatus("SUCCESS", message);
            updateSyncStatistics(totalSynced, totalSkipped, costTime);

            // log.warn("🎉 数据同步完成！同步: {} 条，跳过: {} 条，耗时: {}ms",totalSynced, totalSkipped,
            // costTime);

            return createSyncResult(totalSynced, totalSkipped, costTime, message);

        } catch (Exception e) {
            log.error("❌ 数据同步失败: {}", e.getMessage(), e);
            updateSyncStatus("FAILED", "同步失败: " + e.getMessage());
            throw new RuntimeException("数据同步失败", e);
        }
    }

    /**
     * 将LogDeviceReport转换为LogDeviceFunctionCall
     * 字段映射：
     * - id → id
     * - device_id → device_id
     * - product → product
     * - message_body → output_params (字段名转换)
     * - create_time → create_time
     *
     * 过滤规则：
     * - 过滤掉message_body中bodyData为空的数据
     */
    private List<LogDeviceFunctionCall> convertReportToFunctionCall(List<LogDeviceReport> reportData) {
        if (CollectionUtils.isEmpty(reportData)) {
            return new ArrayList<>();
        }

        return reportData.stream()
                .filter(report -> !isBodyDataEmpty(report.getMessageBody())) // 过滤bodyData为空的数据
                .map(report -> {
                    LogDeviceFunctionCall functionCall = new LogDeviceFunctionCall();
                    functionCall.setId(report.getId());
                    functionCall.setDeviceId(report.getDeviceId());
                    functionCall.setProduct(report.getProduct());
                    // 字段名转换：message_body → output_params
                    functionCall.setOutputParams(report.getMessageBody());
                    
                    // 时区处理：根据配置决定是否进行时区转换
                    Date originalTime = report.getCreateTime();
                    if (originalTime != null && dataSyncConfig.getTimezoneConversion().isEnabled()) {
                        // 根据配置进行时区转换
                        java.time.ZoneId postgresqlZone = java.time.ZoneId.of(dataSyncConfig.getPostgresqlTimezone());
                        java.time.ZoneId mysqlZone = java.time.ZoneId.of(dataSyncConfig.getMysqlTimezone());
                        
                        java.time.Instant instant = originalTime.toInstant();
                        java.time.ZonedDateTime postgresqlTime = instant.atZone(postgresqlZone);
                        java.time.ZonedDateTime mysqlTime = postgresqlTime.withZoneSameInstant(mysqlZone);
                        
                        functionCall.setCreateTime(Date.from(mysqlTime.toInstant()));
                        log.debug("🔄 时区转换: {} ({}) -> {} ({})", 
                                originalTime, postgresqlZone, 
                                Date.from(mysqlTime.toInstant()), mysqlZone);
                    } else {
                        // 不进行时区转换，直接使用原始时间
                        functionCall.setCreateTime(originalTime);
                    }
                    
                    // 设置默认值，因为原表没有async_call字段
                    functionCall.setAsyncCall(0);
                    return functionCall;
                })
                .toList();
    }

    /**
     * 检查message_body是否有效（必须有实际返回值才同步）
     *
     * @param messageBody JSON字符串
     * @return true表示无效数据需要跳过，false表示有效数据可以同步
     */
    private boolean isBodyDataEmpty(String messageBody) {
        // 1. 检查message_body是否为空
        if (!StringUtils.hasText(messageBody)) {
            log.debug("📝 message_body为空，跳过同步");
            return true;
        }

        // 2. 检查message_body是否只是空白字符
        if (messageBody.trim().isEmpty()) {
            log.debug("📝 message_body只包含空白字符，跳过同步");
            return true;
        }

        try {
            // 3. 解析JSON
            Map<String, Object> messageMap = objectMapper.readValue(messageBody,
                    new TypeReference<Map<String, Object>>() {
                    });

            // 4. 检查JSON是否为空对象
            if (messageMap == null || messageMap.isEmpty()) {
                log.debug("📝 message_body解析后为空对象，跳过同步");
                return true;
            }

            // 5. 检查是否有message字段
            Object messageObj = messageMap.get("message");
            if (messageObj == null) {
                log.debug("📝 message字段不存在，跳过同步，设备: {}", messageMap.get("deviceId"));
                return true;
            }

            if (messageObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> message = (Map<String, Object>) messageObj;

                // 6. 检查message是否为空对象
                if (message.isEmpty()) {
                    log.debug("📝 message为空对象，跳过同步，设备: {}", messageMap.get("deviceId"));
                    return true;
                }

                // 7. 检查bodyData字段
                Object bodyData = message.get("bodyData");
                if (bodyData == null) {
                    log.debug("📝 bodyData字段不存在，跳过同步，设备: {}", messageMap.get("deviceId"));
                    return true;
                }

                // 8. 检查bodyData是否为空对象
                if (bodyData instanceof Map && ((Map<?, ?>) bodyData).isEmpty()) {
                    log.debug("📝 bodyData为空对象，跳过同步，设备: {}", messageMap.get("deviceId"));
                    return true;
                }

                // 9. 检查bodyData是否为空字符串或只包含空白字符
                if (bodyData instanceof String) {
                    String bodyDataStr = (String) bodyData;
                    if (!StringUtils.hasText(bodyDataStr) || bodyDataStr.trim().isEmpty()) {
                        log.debug("📝 bodyData为空字符串或只包含空白字符，跳过同步，设备: {}", messageMap.get("deviceId"));
                        return true;
                    }
                }

                // 10. 检查bodyData是否为null值的字符串
                if (bodyData instanceof String && "null".equalsIgnoreCase(((String) bodyData).trim())) {
                    log.debug("📝 bodyData为null字符串，跳过同步，设备: {}", messageMap.get("deviceId"));
                    return true;
                }

                // 11. 如果bodyData是数组，检查是否为空数组
                if (bodyData instanceof List && ((List<?>) bodyData).isEmpty()) {
                    log.debug("📝 bodyData为空数组，跳过同步，设备: {}", messageMap.get("deviceId"));
                    return true;
                }
            } else {
                // message字段不是Map类型，认为无效
                log.debug("📝 message字段不是对象类型，跳过同步，设备: {}", messageMap.get("deviceId"));
                return true;
            }

            return false; // 通过所有检查，数据有效

        } catch (Exception e) {
            log.warn("⚠️ 解析message_body失败，跳过同步: {}, 错误: {}", messageBody, e.getMessage());
            return true; // 解析失败也跳过
        }
    }

    /**
     * 过滤已存在的数据
     */
    private List<LogDeviceFunctionCall> filterExistingData(List<LogDeviceFunctionCall> data) {
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }

        // 提取ID列表
        List<Long> ids = data.stream()
                .map(LogDeviceFunctionCall::getId)
                .toList();

        // 查询已存在的ID
        List<Long> existingIds = mysqlMapper.selectExistingIds(ids);

        // 过滤掉已存在的数据
        return data.stream()
                .filter(item -> !existingIds.contains(item.getId()))
                .toList();
    }

    /**
     * 创建同步结果
     */
    private Map<String, Object> createSyncResult(int synced, int skipped, long costTime, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("synced", synced);
        result.put("skipped", skipped);
        result.put("costTime", costTime);
        result.put("message", message);
        result.put("timestamp", new Date());
        return result;
    }

    @Override
    public Date getLastSyncTime() {
        String key = dataSyncConfig.getRedisKeyPrefix() + LAST_SYNC_TIME_KEY;
        Object timeObj = redisTemplate.opsForValue().get(key);

        if (timeObj instanceof Date) {
            return (Date) timeObj;
        } else if (timeObj instanceof String) {
            try {
                return DateUtil.parse((String) timeObj);
            } catch (Exception e) {
                log.warn("⚠️ 解析同步时间失败: {}", timeObj);
            }
        }

        // 如果Redis中没有记录，尝试从MySQL获取最后一条记录的时间
        Date mysqlLastTime = mysqlMapper.getLastSyncTime();
        if (mysqlLastTime != null) {
            updateLastSyncTime(mysqlLastTime);
            return mysqlLastTime;
        }

        return null;
    }

    @Override
    public void updateLastSyncTime(Date syncTime) {
        if (syncTime != null) {
            String key = dataSyncConfig.getRedisKeyPrefix() + LAST_SYNC_TIME_KEY;
            redisTemplate.opsForValue().set(key, syncTime, 7, TimeUnit.DAYS);
            log.debug("📝 更新最后同步时间: {}", DateUtil.formatDateTime(syncTime));
        }
    }

    @Override
    public Map<String, Object> clearSyncCache() {
        Map<String, Object> result = new HashMap<>();
        int clearedCount = 0;
        
        try {
            String prefix = dataSyncConfig.getRedisKeyPrefix();
            
            // 清理同步时间缓存
            String lastSyncTimeKey = prefix + LAST_SYNC_TIME_KEY;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(lastSyncTimeKey))) {
                redisTemplate.delete(lastSyncTimeKey);
                clearedCount++;
                log.info("🗑️ 已清理同步时间缓存: {}", lastSyncTimeKey);
            }
            
            // 清理同步状态缓存
            String syncStatusKey = prefix + SYNC_STATUS_KEY;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(syncStatusKey))) {
                redisTemplate.delete(syncStatusKey);
                clearedCount++;
                log.info("🗑️ 已清理同步状态缓存: {}", syncStatusKey);
            }
            
            // 清理同步统计缓存
            String syncStatsKey = prefix + SYNC_STATS_KEY;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(syncStatsKey))) {
                redisTemplate.delete(syncStatsKey);
                clearedCount++;
                log.info("🗑️ 已清理同步统计缓存: {}", syncStatsKey);
            }
            
            // 清理同步锁（如果存在）
            String lockKey = prefix + SYNC_LOCK_KEY;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(lockKey))) {
                redisTemplate.delete(lockKey);
                clearedCount++;
                log.info("🗑️ 已清理同步锁: {}", lockKey);
            }
            
            result.put("success", true);
            result.put("message", "同步缓存清理完成");
            result.put("clearedCount", clearedCount);
            result.put("clearTime", new Date());
            
            log.info("✅ 同步缓存清理完成，共清理 {} 个缓存项", clearedCount);
            
        } catch (Exception e) {
            log.error("❌ 清理同步缓存失败", e);
            result.put("success", false);
            result.put("message", "清理同步缓存失败: " + e.getMessage());
            result.put("clearedCount", clearedCount);
        }
        
        return result;
    }

    /**
     * 更新同步状态
     */
    private void updateSyncStatus(String status, String message) {
        String key = dataSyncConfig.getRedisKeyPrefix() + SYNC_STATUS_KEY;
        Map<String, Object> statusInfo = new HashMap<>();
        statusInfo.put("status", status);
        statusInfo.put("message", message);
        statusInfo.put("updateTime", new Date());
        redisTemplate.opsForValue().set(key, statusInfo, 1, TimeUnit.HOURS);
    }

    /**
     * 更新同步统计
     */
    private void updateSyncStatistics(int synced, int skipped, long costTime) {
        String key = dataSyncConfig.getRedisKeyPrefix() + SYNC_STATS_KEY;
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalSynced", synced);
        stats.put("totalSkipped", skipped);
        stats.put("lastCostTime", costTime);
        stats.put("lastSyncTime", new Date());
        redisTemplate.opsForValue().set(key, stats, 24, TimeUnit.HOURS);
    }

    @Override
    public Map<String, Object> getSyncStatus() {
        String key = dataSyncConfig.getRedisKeyPrefix() + SYNC_STATUS_KEY;
        Object statusObj = redisTemplate.opsForValue().get(key);

        if (statusObj instanceof Map) {
            return (Map<String, Object>) statusObj;
        }

        // 默认状态
        Map<String, Object> defaultStatus = new HashMap<>();
        defaultStatus.put("status", "UNKNOWN");
        defaultStatus.put("message", "暂无同步状态信息");
        defaultStatus.put("updateTime", new Date());
        return defaultStatus;
    }

    @Override
    public Map<String, Object> getSyncStatistics() {
        String key = dataSyncConfig.getRedisKeyPrefix() + SYNC_STATS_KEY;
        Object statsObj = redisTemplate.opsForValue().get(key);

        if (statsObj instanceof Map) {
            return (Map<String, Object>) statsObj;
        }

        // 默认统计
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("totalSynced", 0);
        defaultStats.put("totalSkipped", 0);
        defaultStats.put("lastCostTime", 0);
        defaultStats.put("lastSyncTime", null);
        return defaultStats;
    }

    @Override
    public void resetSyncStatus() {
        String statusKey = dataSyncConfig.getRedisKeyPrefix() + SYNC_STATUS_KEY;
        String statsKey = dataSyncConfig.getRedisKeyPrefix() + SYNC_STATS_KEY;
        String timeKey = dataSyncConfig.getRedisKeyPrefix() + LAST_SYNC_TIME_KEY;

        redisTemplate.delete(statusKey);
        redisTemplate.delete(statsKey);
        redisTemplate.delete(timeKey);

        log.warn("🔄 已重置数据同步状态");
    }

    @Override
    public Map<String, Boolean> checkDataSourceStatus() {
        Map<String, Boolean> status = new HashMap<>();

        // 检查PostgreSQL连接
        try {
            iotDao.selectOneBySql(Integer.class, "SELECT 1", null);
            status.put("postgresql", true);
            log.debug("✅ PostgreSQL连接正常");
        } catch (Exception e) {
            status.put("postgresql", false);
            log.error("❌ PostgreSQL连接异常: {}", e.getMessage());
        }

        // 检查MySQL连接
        try {
            mysqlMapper.getLastSyncTime();
            status.put("mysql", true);
            log.debug("✅ MySQL连接正常");
        } catch (Exception e) {
            status.put("mysql", false);
            log.error("❌ MySQL连接异常: {}", e.getMessage());
        }

        // 检查Redis连接
        try {
            redisTemplate.opsForValue().get("test");
            status.put("redis", true);
            log.debug("✅ Redis连接正常");
        } catch (Exception e) {
            status.put("redis", false);
            log.error("❌ Redis连接异常: {}", e.getMessage());
        }

        return status;
    }
}
