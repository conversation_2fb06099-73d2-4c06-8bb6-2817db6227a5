package com.westcatr.rd.testbusiness.business.devicedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.westcatr.rd.testbusiness.business.devicedata.entity.DeviceDataConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 设备对接数据管理配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Mapper
public interface DeviceDataConfigMapper extends BaseMapper<DeviceDataConfig> {

    /**
     * 根据设备代码查询配置
     *
     * @param deviceCode 设备代码
     * @return 设备配置
     */
    @Select("SELECT * FROM bu_device_data_config WHERE device_code = #{deviceCode} AND status = 'enabled'")
    DeviceDataConfig selectByDeviceCode(@Param("deviceCode") String deviceCode);
}
