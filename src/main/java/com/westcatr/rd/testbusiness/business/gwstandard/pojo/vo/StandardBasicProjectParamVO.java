package com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实验项目参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "实验项目参数表VO对象")
public class StandardBasicProjectParamVO extends StandardBasicProjectParam {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    // StandardBasicModelParam
    @TableField(exist = false)
    @JoinSelect(joinClass = StandardBasicModelParam.class, relationId = "standard_basic_project_param_id")
    private StandardBasicModelParam standardBasicModelParam;

}
