package com.westcatr.rd.testbusiness.business.basics.controller;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.BackSampleDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.CheckOverDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.CheckingDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.DataInfoDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.DetectAdminOfBottomDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.DetectPersonDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.DetectPersonOfBottomDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.DoneDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.GetSampleDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.InstrumentInfoDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.ProdSchedulerDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.ProdSchedulerOfBottomDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.ReportInfoDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.RolesDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.SampleInfoDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.SampleManagerDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.SampleManagerOfBottomDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.TaskInfoDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.TestingCenterDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.TodayGitDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.UserInfoDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WaitToCheckDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WaittingDto;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jzreport.service.JzReportInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import com.westcatr.rd.testbusiness.business.sample.service.SampleInfoService;
import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import com.westcatr.rd.testbusiness.business.workstation.service.WorkstationHistoryCountService;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作台控制器
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Validated
@Tag(name = "工作台管理接口", description = "工作台管理接口")
@Slf4j
@RestController
public class WorkStationController {

    @Autowired
    private JzTaskInfoService jzTaskInfoService;

    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;

    @Autowired
    private SampleInfoService sampleInfoService;

    @Autowired
    private JzReportInfoService jzReportInfoService;

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private WorkstationHistoryCountService workstationHistoryCountService;

    @Operation(summary = "工作台顶部数据统计")
    @PostMapping("/workStation/topDataCount")
    public IResult<Object> topDataCount(@RequestBody RolesDto rolesDto) {
        List<String> roles = rolesDto.getRoles();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String todayStr = sdf.format(new Date()); // 今日日期字符串

        ProdSchedulerDto prodSchedulerDto = new ProdSchedulerDto();// 生产调度员
        SampleManagerDto sampleManagerDto = new SampleManagerDto();// 样品管理员
        TestingCenterDto testingCenterDto = new TestingCenterDto();// 检查中心主任
        DetectPersonDto detectPersonDto = new DetectPersonDto();// 检查人员

        // 生成调度员
        if (roles.contains("prodScheduler")) {
            /*
             * 生成调度员相关数据
             */
            DataInfoDto dataInfoDto = new DataInfoDto();
            WaitToCheckDto waitToCheckDto = new WaitToCheckDto();
            CheckingDto checkingDto = new CheckingDto();
            CheckOverDto checkOverDto = new CheckOverDto();

            // 赋值all值
            List<JzTaskInfo> taskInfos = jzTaskInfoService.list();

            if (CollUtil.isNotEmpty(taskInfos)) {
                dataInfoDto.setAll(taskInfos.size());

                List<JzTaskInfo> taskInfos1 = taskInfos.stream().filter(x -> "任务待检".equals(x.getTaskStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskInfos1)) {
                    waitToCheckDto.setAll(taskInfos1.size());
                }

                List<JzTaskInfo> taskInfos2 = taskInfos.stream().filter(x -> "任务在检".equals(x.getTaskStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskInfos2)) {
                    checkingDto.setAll(taskInfos2.size());
                }

                List<JzTaskInfo> taskInfos3 = taskInfos.stream().filter(x -> "任务检毕".equals(x.getTaskStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskInfos3)) {
                    checkOverDto.setAll(taskInfos3.size());
                }
            }

            // 赋值today值
            List<WorkstationHistoryCount> todayTasks = workstationHistoryCountService.list().stream()
                    .filter(x -> "任务".equals(x.getType()))
                    .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(todayTasks)) {
                dataInfoDto.setToday(todayTasks.size());

                List<WorkstationHistoryCount> taskInfos1 = todayTasks.stream().filter(x -> "任务待检".equals(x.getStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskInfos1)) {
                    waitToCheckDto.setToday(taskInfos1.size());
                }

                List<WorkstationHistoryCount> taskInfos2 = todayTasks.stream().filter(x -> "任务在检".equals(x.getStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskInfos2)) {
                    checkingDto.setToday(taskInfos2.size());
                }

                List<WorkstationHistoryCount> taskInfos3 = todayTasks.stream().filter(x -> "任务检毕".equals(x.getStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskInfos3)) {
                    checkOverDto.setToday(taskInfos3.size());
                }
            }

            prodSchedulerDto.setDataInfoDto(dataInfoDto);
            prodSchedulerDto.setWaitToCheckDto(waitToCheckDto);
            prodSchedulerDto.setCheckingDto(checkingDto);
            prodSchedulerDto.setCheckOverDto(checkOverDto);

            return IResult.ok(prodSchedulerDto);
        }
        // 检测人员
        else if (roles.contains("detectPerson")) {
            /*
             * 检测人员相关数据
             */
            DataInfoDto dataInfoDto = new DataInfoDto();
            WaitToCheckDto waitToCheckDto = new WaitToCheckDto();
            CheckingDto checkingDto = new CheckingDto();
            CheckOverDto checkOverDto = new CheckOverDto();

            // 赋值all值 （查询与自己相关的）
            List<JzTaskWorkOrderInfo> workOrderInfos = jzTaskWorkOrderInfoService
                    .list(new LambdaQueryWrapper<JzTaskWorkOrderInfo>().eq(JzTaskWorkOrderInfo::getTestUserId,
                            AuthUtil.getUserIdE()));

            if (CollUtil.isNotEmpty(workOrderInfos)) {
                dataInfoDto.setAll(workOrderInfos.size());

                List<JzTaskWorkOrderInfo> workOrderInfos1 = workOrderInfos.stream()
                        .filter(x -> "待检".equals(x.getStatusInfo())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(workOrderInfos1)) {
                    waitToCheckDto.setAll(workOrderInfos1.size());
                }

                List<JzTaskWorkOrderInfo> workOrderInfos2 = workOrderInfos.stream()
                        .filter(x -> "在检".equals(x.getStatusInfo())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(workOrderInfos2)) {
                    checkingDto.setAll(workOrderInfos2.size());
                }

                List<JzTaskWorkOrderInfo> workOrderInfos3 = workOrderInfos.stream()
                        .filter(x -> "检毕".equals(x.getStatusInfo())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(workOrderInfos3)) {
                    checkOverDto.setAll(workOrderInfos3.size());
                }
            }

            // 赋值today值
            List<String> orderNumbers = jzTaskWorkOrderInfoService
                    .list(new LambdaQueryWrapper<JzTaskWorkOrderInfo>().eq(JzTaskWorkOrderInfo::getTestUserId,
                            AuthUtil.getUserIdE()))
                    .stream()
                    .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                    .map(x -> x.getWorkOrderNumber())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(orderNumbers)) {
                List<WorkstationHistoryCount> todayTasks = workstationHistoryCountService.list().stream()
                        .filter(x -> orderNumbers.contains(x.getNumber()))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(todayTasks)) {
                    dataInfoDto.setToday(todayTasks.size());

                    List<WorkstationHistoryCount> workOrderInfos1 = todayTasks.stream()
                            .filter(x -> "待检".equals(x.getStatus())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(workOrderInfos1)) {
                        waitToCheckDto.setToday(workOrderInfos1.size());
                    }

                    List<WorkstationHistoryCount> workOrderInfos2 = todayTasks.stream()
                            .filter(x -> "在检".equals(x.getStatus())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(workOrderInfos2)) {
                        checkingDto.setToday(workOrderInfos2.size());
                    }

                    List<WorkstationHistoryCount> workOrderInfos3 = todayTasks.stream()
                            .filter(x -> "检毕".equals(x.getStatus())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(workOrderInfos3)) {
                        checkOverDto.setToday(workOrderInfos3.size());
                    }
                }
            }

            detectPersonDto.setDataInfoDto(dataInfoDto);
            detectPersonDto.setWaitToCheckDto(waitToCheckDto);
            detectPersonDto.setCheckingDto(checkingDto);
            detectPersonDto.setCheckOverDto(checkOverDto);

            return IResult.ok(detectPersonDto);
        }
        // 样品管理员
        else if (roles.contains("sampleManager")) {
            /*
             * 样品管理人员相关数据
             */
            GetSampleDto getSampleDto = new GetSampleDto();
            BackSampleDto backSampleDto = new BackSampleDto();
            TaskInfoDto taskInfoDto = new TaskInfoDto();

            List<SampleInfo> sampleInfos = sampleInfoService.list();
            List<JzTaskInfo> taskInfos = jzTaskInfoService.list();
            List<WorkstationHistoryCount> workstationHistoryCounts = workstationHistoryCountService.list();

            if (CollUtil.isNotEmpty(sampleInfos)) {
                List<SampleInfo> getSamples = sampleInfos.stream().filter(x -> "收样".equals(x.getStatus()))
                        .collect(Collectors.toList());
                List<SampleInfo> backSamples = sampleInfos.stream().filter(x -> "返样".equals(x.getStatus()))
                        .collect(Collectors.toList());

                // 统计收样信息
                if (CollUtil.isNotEmpty(getSamples)) {
                    getSampleDto.setAll(getSamples.size());
                }

                List<WorkstationHistoryCount> todayGetSamples = workstationHistoryCounts.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .filter(x -> "样品".equals(x.getType()))
                        .filter(x -> "收样".equals(x.getStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayGetSamples)) {
                    getSampleDto.setToday(todayGetSamples.size());
                }

                // 统计返样信息
                if (CollUtil.isNotEmpty(backSamples)) {
                    backSampleDto.setAll(backSamples.size());
                }

                List<WorkstationHistoryCount> todayBackSamples = workstationHistoryCounts.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .filter(x -> "样品".equals(x.getType()))
                        .filter(x -> "返样".equals(x.getStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayBackSamples)) {
                    backSampleDto.setToday(todayBackSamples.size());
                }
            }

            if (CollUtil.isNotEmpty(taskInfos)) {
                taskInfoDto.setAll(taskInfos.size());
            }

            List<WorkstationHistoryCount> todayTaskInfos = workstationHistoryCounts.stream()
                    .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                    .filter(x -> "任务".equals(x.getType()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(todayTaskInfos)) {
                taskInfoDto.setToday(todayTaskInfos.size());
            }

            sampleManagerDto.setGetSampleDto(getSampleDto);
            sampleManagerDto.setBackSampleDto(backSampleDto);
            sampleManagerDto.setTaskInfoDto(taskInfoDto);

            return IResult.ok(sampleManagerDto);
        }
        // 检查中心主任
        else if (roles.contains("detectAdmin")) {
            /*
             * 样检查人员相关数据
             */
            SampleInfoDto sampleInfoDto = new SampleInfoDto();
            TaskInfoDto taskInfoDto = new TaskInfoDto();
            ReportInfoDto reportInfoDto = new ReportInfoDto();

            List<SampleInfo> sampleInfos = sampleInfoService.list();
            List<WorkstationHistoryCount> workstationHistoryCounts = workstationHistoryCountService.list();

            if (CollUtil.isNotEmpty(sampleInfos)) {
                sampleInfoDto.setAll(sampleInfos.size());

                List<WorkstationHistoryCount> todayGetSamples = workstationHistoryCounts.stream()
                        .filter(x -> "样品".equals(x.getType()))
                        .filter(x -> "收样".equals(x.getStatus()))
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayGetSamples)) {
                    sampleInfoDto.setTodayGetSample(todayGetSamples.size());
                }

                List<WorkstationHistoryCount> todayBackSamples = workstationHistoryCounts.stream()
                        .filter(x -> "样品".equals(x.getType()))
                        .filter(x -> "返样".equals(x.getStatus()))
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayBackSamples)) {
                    sampleInfoDto.setTodayBackSample(todayBackSamples.size());
                }
            }

            List<JzTaskInfo> taskInfos = jzTaskInfoService.list();
            if (CollUtil.isNotEmpty(taskInfos)) {
                taskInfoDto.setAll(taskInfos.size());

                // 今日新增待检
                List<WorkstationHistoryCount> todayWaitToCheck = workstationHistoryCounts.stream()
                        .filter(x -> "任务".equals(x.getType()))
                        .filter(x -> "任务待检".equals(x.getStatus()))
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayWaitToCheck)) {
                    taskInfoDto.setTodayWaitToCheck(todayWaitToCheck.size());
                }

                // 今日新增在检
                List<WorkstationHistoryCount> todayChecking = workstationHistoryCounts.stream()
                        .filter(x -> "任务".equals(x.getType()))
                        .filter(x -> "任务在检".equals(x.getStatus()))
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayChecking)) {
                    taskInfoDto.setTodayChecking(todayChecking.size());
                }

                // 今日检毕
                List<WorkstationHistoryCount> todayOverCheck = workstationHistoryCounts.stream()
                        .filter(x -> "任务".equals(x.getType()))
                        .filter(x -> "任务检毕".equals(x.getStatus()))
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayOverCheck)) {
                    taskInfoDto.setTodayOverCheck(todayOverCheck.size());
                }
            }

            List<JzReportInfo> reportInfos = jzReportInfoService.list();
            if (CollUtil.isNotEmpty(reportInfos)) {
                reportInfoDto.setAll(reportInfos.size());

                List<JzReportInfo> todayReports = reportInfos.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayReports)) {
                    reportInfoDto.setToday(todayReports.size());
                }
            }

            testingCenterDto.setSampleInfoDto(sampleInfoDto);
            testingCenterDto.setTaskInfoDto(taskInfoDto);
            testingCenterDto.setReportInfoDto(reportInfoDto);

            return IResult.ok(testingCenterDto);
        } else {
            return IResult.ok(null);
        }
    }

    @Operation(summary = "工作台底部数据统计")
    @PostMapping("/workStation/bottomDataCount")
    public IResult<Object> bottomDataCount(@RequestBody RolesDto rolesDto) {
        SampleManagerOfBottomDto sampleManagerOfBottomDto = new SampleManagerOfBottomDto();
        ProdSchedulerOfBottomDto prodSchedulerOfBottomDto = new ProdSchedulerOfBottomDto();
        DetectPersonOfBottomDto detectPersonOfBottomDto = new DetectPersonOfBottomDto();
        DetectAdminOfBottomDto detectAdminOfBottomDto = new DetectAdminOfBottomDto();

        List<JzTaskInfo> taskInfos = jzTaskInfoService.list();// 任务集合
        List<SampleInfo> sampleInfos = sampleInfoService.list();// 样品集合
        List<JzTaskWorkOrderInfo> workOrderInfos = jzTaskWorkOrderInfoService.list();// 工单集合
        List<InstrumentNewInfo> instrumentNewInfos = instrumentNewInfoService.list();// 设备集合
        List<OrgUserInfo> orgUserInfos = orgUserInfoService.list();// 人员集合

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String todayStr = sdf.format(new Date()); // 今日日期字符串

        List<String> roles = rolesDto.getRoles();

        // 样品管理员
        if (roles.contains("sampleManager")) {
            WaittingDto waittingDto = new WaittingDto();
            DoneDto doneDto = new DoneDto();
            TodayGitDto todayGitDto = new TodayGitDto();

            // ---------------------------------WaittingDto待办---------------------------------
            List<JzTaskInfo> jzTaskInfos = new ArrayList<>();
            if (CollUtil.isNotEmpty(taskInfos)) {
                jzTaskInfos = taskInfos.stream()
                        .filter(x -> "任务检毕".equals(x.getTaskStatus()))
                        .collect(Collectors.toList());
            }

            List<SampleInfo> sampleInfoList = new ArrayList<>();
            if (CollUtil.isNotEmpty(sampleInfos)) {
                sampleInfoList = sampleInfos.stream()
                        .filter(x -> "收样".equals(x.getStatus()))
                        .sorted(Comparator.comparing(SampleInfo::getCreateTime).reversed())
                        .collect(Collectors.toList());
            }

            if (CollUtil.isNotEmpty(sampleInfoList) && CollUtil.isNotEmpty(jzTaskInfos)) {
                Set<Long> sampleIds = jzTaskInfos.stream()
                        .map(JzTaskInfo::getSampleId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                List<String> topNumbers = sampleInfoList.stream()
                        .filter(x -> x.getId() != null && sampleIds.contains(x.getId()))
                        .map(SampleInfo::getSampleNumber)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                waittingDto.setTopNumbers(topNumbers);
            }

            // ---------------------------------DoneDto已办---------------------------------
            if (CollUtil.isNotEmpty(taskInfos)) {
                List<Long> samplesIds = taskInfos.stream()
                        .filter(x -> "任务待检".equals(x.getTaskStatus()))
                        .map(x -> x.getSampleId())
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(samplesIds) && CollUtil.isNotEmpty(sampleInfos)) {
                    List<String> sampleNumbers = sampleInfos.stream()
                            .filter(x -> x.getId() != null && samplesIds.contains(x.getId()))
                            .sorted(Comparator.comparing(SampleInfo::getCreateTime).reversed())
                            .map(SampleInfo::getSampleNumber)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(sampleNumbers)) {
                        doneDto.setTopNumbers(sampleNumbers);
                    }
                }
            }

            if (CollUtil.isNotEmpty(sampleInfos)) {
                List<String> sampleNumbers = sampleInfos.stream()
                        .filter(x -> "返样".equals(x.getStatus()))
                        .sorted(Comparator.comparing(SampleInfo::getCreateTime).reversed())
                        .map(SampleInfo::getSampleNumber)
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(sampleNumbers)) {
                    doneDto.setBottomNumbers(sampleNumbers);
                }
            }

            // ---------------------------------TodayGitDto今日发起---------------------------------
            // 1.3.1 收样
            // 统计当日（今日0点到当前时刻）样品任务状态为“待检”的样品编号，默认为时间最近邻5个样品编号。
            if (CollUtil.isNotEmpty(taskInfos)) {
                Set<Long> sampleIds = taskInfos.stream()
                        .filter(x -> "任务待检".equals(x.getTaskStatus()))
                        .filter(Objects::nonNull)
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .map(x -> x.getSampleId())
                        .collect(Collectors.toSet());

                if (CollUtil.isNotEmpty(sampleIds) && CollUtil.isNotEmpty(sampleInfos)) {
                    List<String> topNumbers = sampleInfos.stream()
                            .filter(x -> sampleIds.contains(x.getId()))
                            .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                            .sorted(Comparator.comparing(SampleInfo::getCreateTime).reversed())
                            .map(SampleInfo::getSampleNumber)
                            .collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(topNumbers)) {
                        todayGitDto.setTopNumbers(topNumbers);
                    }
                }
            }

            // 1.3.2 返样
            // 统计当日（今日0点到当前时刻）样品的样品状态为“返样”的样品编号，默认为时间最近邻5个样品编号。
            if (CollUtil.isNotEmpty(sampleInfos)) {
                List<String> bottomNumbers = sampleInfos.stream()
                        .filter(x -> "返样".equals(x.getStatus()))
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .sorted(Comparator.comparing(SampleInfo::getCreateTime).reversed())
                        .map(SampleInfo::getSampleNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(bottomNumbers)) {
                    todayGitDto.setBottomNumbers(bottomNumbers);
                }

            }

            sampleManagerOfBottomDto.setWaittingDto(waittingDto);
            sampleManagerOfBottomDto.setDoneDto(doneDto);
            sampleManagerOfBottomDto.setTodayGitDto(todayGitDto);

            return IResult.ok(sampleManagerOfBottomDto);
        }
        // 生成调度员
        else if (roles.contains("prodScheduler")) {

            WaittingDto waittingDto = new WaittingDto();
            DoneDto doneDto = new DoneDto();
            TodayGitDto todayGitDto = new TodayGitDto();
            // 3.1 待办
            // 3.1.1 开始任务
            // 统计任务为“待检”的任务编号
            if (CollUtil.isNotEmpty(taskInfos)) {
                List<String> taskNumbers = taskInfos.stream()
                        .filter(x -> "任务待检".equals(x.getTaskStatus()))
                        .sorted(Comparator.comparing(JzTaskInfo::getCreateTime).reversed())
                        .map(JzTaskInfo::getTaskNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskNumbers)) {
                    waittingDto.setTopNumbers(taskNumbers);
                }
            }
            // 3.1.2 检毕任务
            // 统计任务为“在检”的任务编号
            if (CollUtil.isNotEmpty(taskInfos)) {
                List<String> taskNumbers = taskInfos.stream()
                        .filter(x -> "任务在检".equals(x.getTaskStatus()))
                        .sorted(Comparator.comparing(JzTaskInfo::getCreateTime).reversed())
                        .map(JzTaskInfo::getTaskNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(taskNumbers)) {
                    waittingDto.setTopNumbers(taskNumbers);
                }
            }

            // 3.2 已办
            if (CollUtil.isNotEmpty(taskInfos)) {
                // 3.1.1 开始任务
                // 统计任务为“在检”或“检毕”的任务编号，在检的靠前
                List<String> topNumbers = taskInfos.stream()
                        .filter(x -> "任务在检".equals(x.getTaskStatus()) || "任务检毕".equals(x.getTaskStatus()))
                        .sorted(Comparator
                                // 第一排序条件：状态（"任务在检"排在前）
                                .comparing((JzTaskInfo x) -> "任务在检".equals(x.getTaskStatus()) ? 0 : 1)
                                // 第二排序条件：创建时间（倒序）
                                .thenComparing(JzTaskInfo::getCreateTime, Comparator.reverseOrder()))
                        .map(JzTaskInfo::getTaskNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(topNumbers)) {
                    doneDto.setTopNumbers(topNumbers);
                }

                // 3.1.2 检毕任务
                // 统计任务为“检毕”的任务编号
                List<String> bottomNumbers = taskInfos.stream()
                        .filter(x -> "任务检毕".equals(x.getTaskStatus()))
                        .sorted(Comparator.comparing(JzTaskInfo::getCreateTime).reversed())
                        .map(JzTaskInfo::getTaskNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(bottomNumbers)) {
                    doneDto.setTopNumbers(bottomNumbers);
                }
            }

            // 3.3 今日发起
            // 3.3.1 开始任务
            // 统计当日（今日0点到当前时刻）任务为“在检”或“检毕”的任务编号，在检的靠前
            if (CollUtil.isNotEmpty(taskInfos)) {
                List<String> topNumbers = taskInfos.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .filter(x -> "任务在检".equals(x.getTaskStatus()) || "任务检毕".equals(x.getTaskStatus()))
                        .sorted(Comparator
                                // 第一排序条件：状态（"任务在检"排在前）
                                .comparing((JzTaskInfo x) -> "任务在检".equals(x.getTaskStatus()) ? 0 : 1)
                                // 第二排序条件：创建时间（倒序）
                                .thenComparing(JzTaskInfo::getCreateTime, Comparator.reverseOrder()))
                        .map(JzTaskInfo::getTaskNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(topNumbers)) {
                    todayGitDto.setTopNumbers(topNumbers);
                }

                // 3.3.2 检毕任务
                // 统计当日（今日0点到当前时刻）任务为“检毕”的任务编号
                List<String> bottomNumbers = taskInfos.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .filter(x -> "任务检毕".equals(x.getTaskStatus()))
                        .sorted(Comparator.comparing(JzTaskInfo::getCreateTime).reversed())
                        .map(JzTaskInfo::getTaskNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(bottomNumbers)) {
                    todayGitDto.setTopNumbers(bottomNumbers);
                }
            }

            prodSchedulerOfBottomDto.setWaittingDto(waittingDto);
            prodSchedulerOfBottomDto.setDoneDto(doneDto);
            prodSchedulerOfBottomDto.setTodayGitDto(todayGitDto);

            return IResult.ok(prodSchedulerOfBottomDto);
        }
        // 检测人员
        else if (roles.contains("detectPerson")) {

            WaittingDto waittingDto = new WaittingDto();
            DoneDto doneDto = new DoneDto();
            TodayGitDto todayGitDto = new TodayGitDto();
            // 筛选出与自己相关的工单
            workOrderInfos = workOrderInfos.stream()
                    .filter(x -> x.getTestUserId().equals(AuthUtil.getUserIdE()))
                    .collect(Collectors.toList());
            // 2、检测人员
            if (CollUtil.isNotEmpty(workOrderInfos)) {
                // 2.1 待办
                // 2.1.1 开始工单
                // 统计工单为“待检”的工单编号
                List<String> topNumbers = workOrderInfos.stream()
                        .filter(x -> "待检".equals(x.getStatusInfo()))
                        .sorted(Comparator.comparing(JzTaskWorkOrderInfo::getCreateTime).reversed())
                        .map(JzTaskWorkOrderInfo::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(topNumbers)) {
                    waittingDto.setTopNumbers(topNumbers);
                }

                // 2.1.2 检毕工单
                // 统计工单为“在检”的工单编号
                List<String> bottomNumbers = workOrderInfos.stream()
                        .filter(x -> "在检".equals(x.getStatusInfo()))
                        .sorted(Comparator.comparing(JzTaskWorkOrderInfo::getCreateTime).reversed())
                        .map(JzTaskWorkOrderInfo::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(bottomNumbers)) {
                    waittingDto.setBottomNumbers(bottomNumbers);
                }

                // 2.2 已办
                // 2.1.1 开始工单
                // 统计工单为“在检”或“检毕”的工单编号，在检的靠前
                List<String> topOrderNumbers = workOrderInfos.stream()
                        .filter(x -> "在检".equals(x.getStatusInfo()) || "检毕".equals(x.getStatusInfo()))
                        .sorted(Comparator
                                // 第一排序条件：状态（"任务在检"排在前）
                                .comparing((JzTaskWorkOrderInfo x) -> "在检".equals(x.getStatusInfo()) ? 0 : 1)
                                // 第二排序条件：创建时间（倒序）
                                .thenComparing(JzTaskWorkOrderInfo::getCreateTime, Comparator.reverseOrder()))
                        .map(JzTaskWorkOrderInfo::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(topOrderNumbers)) {
                    doneDto.setTopNumbers(topOrderNumbers);
                }

                // 2.1.2 检毕工单
                // 统计工单为“检毕”的工单编号
                List<String> bottomOrderNumbers = workOrderInfos.stream()
                        .filter(x -> "检毕".equals(x.getStatusInfo()))
                        .sorted(Comparator.comparing(JzTaskWorkOrderInfo::getCreateTime).reversed())
                        .map(JzTaskWorkOrderInfo::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(bottomOrderNumbers)) {
                    doneDto.setBottomNumbers(bottomOrderNumbers);
                }

                // 2.3 今日发起
                // 2.3.1 开始工单
                // 统计当日（今日0点到当前时刻）工单为“在检”或“检毕”的工单编号，在检的靠前
                List<String> topTodayNumbers = workOrderInfos.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .filter(x -> "在检".equals(x.getStatusInfo()) || "检毕".equals(x.getStatusInfo()))
                        .sorted(Comparator
                                // 第一排序条件：状态（"任务在检"排在前）
                                .comparing((JzTaskWorkOrderInfo x) -> "在检".equals(x.getStatusInfo()) ? 0 : 1)
                                // 第二排序条件：创建时间（倒序）
                                .thenComparing(JzTaskWorkOrderInfo::getCreateTime, Comparator.reverseOrder()))
                        .map(JzTaskWorkOrderInfo::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(topTodayNumbers)) {
                    todayGitDto.setTopNumbers(topTodayNumbers);
                }

                // 2.3.2 检毕工单
                // 统计当日（今日0点到当前时刻）工单为“检毕”的工单编号
                List<String> todayBottomNumbers = workOrderInfos.stream()
                        .filter(x -> sdf.format(x.getCreateTime()).equals(todayStr))
                        .filter(x -> "检毕".equals(x.getStatusInfo()))
                        .sorted(Comparator.comparing(JzTaskWorkOrderInfo::getCreateTime).reversed())
                        .map(JzTaskWorkOrderInfo::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(todayBottomNumbers)) {
                    todayGitDto.setBottomNumbers(todayBottomNumbers);
                }

            }

            detectPersonOfBottomDto.setWaittingDto(waittingDto);
            detectPersonOfBottomDto.setDoneDto(doneDto);
            detectPersonOfBottomDto.setTodayGitDto(todayGitDto);

            return IResult.ok(detectPersonOfBottomDto);
        }
        // 检查中心主任
        else if (roles.contains("detectAdmin") || roles.contains("superAdmin")) {
            List<InstrumentInfoDto> instrumentInfos = new ArrayList<>();
            List<UserInfoDto> userInfos = new ArrayList();

            if (CollUtil.isNotEmpty(instrumentNewInfos)) {
                for (InstrumentNewInfo instrument : instrumentNewInfos) {
                    InstrumentInfoDto instrumentInfoDto = new InstrumentInfoDto();

                    instrumentInfoDto.setName(instrument.getSbmc());

                    boolean flag = checkInstrumentStatus(instrument.getId(), workOrderInfos);

                    if (flag) {
                        instrumentInfoDto.setStatus("空闲");// 该设备对应的所有工单状态均为检毕
                    } else {
                        instrumentInfoDto.setStatus("实验中");// 该设备对应的所有工单状态并非均为检毕
                    }

                    long day = JudgeNextYearDay();
                    instrumentInfoDto.setNextYearDay(day + "天");

                    instrumentInfos.add(instrumentInfoDto);

                }
            }

            if (CollUtil.isNotEmpty(orgUserInfos)) {
                for (OrgUserInfo orgUserInfo : orgUserInfos) {
                    UserInfoDto userInfoDto = new UserInfoDto();

                    userInfoDto.setName(orgUserInfo.getFullName());

                    boolean flag = checkUserStatus(orgUserInfo.getId(), workOrderInfos);
                    if (flag) {
                        userInfoDto.setStatus("空闲");// 该设备对应的所有工单状态均为检毕
                    } else {
                        userInfoDto.setStatus("实验中");// 该设备对应的所有工单状态并非均为检毕
                    }

                    userInfos.add(userInfoDto);
                }
            }

            detectAdminOfBottomDto.setInstrumentInfoDtos(instrumentInfos);
            detectAdminOfBottomDto.setUserInfoDtos(userInfos);

            return IResult.ok(detectAdminOfBottomDto);
        } else {
            return IResult.ok(null);
        }
    }

    private long JudgeNextYearDay() {
        // 目标日期（2025年1月1日）
        LocalDate targetDate = LocalDate.of(2025, 1, 1);
        // 当前日期
        LocalDate currentDate = LocalDate.now();

        // 计算天数差（currentDate - targetDate，可为负数）
        long daysBetween = ChronoUnit.DAYS.between(targetDate, currentDate);
        return daysBetween;
    }

    private boolean checkUserStatus(Long id, List<JzTaskWorkOrderInfo> workOrderInfos) {
        boolean flag = true;
        if (CollUtil.isNotEmpty(workOrderInfos)) {
            List<String> statusList = workOrderInfos.stream()
                    .filter(x -> id.equals(x.getTestUserId()))
                    .map(JzTaskWorkOrderInfo::getStatusInfo)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(statusList)) {
                if (statusList.contains("待检") || statusList.contains("在检")) {
                    flag = false;
                }
            }
        }

        return flag;
    }

    private boolean checkInstrumentStatus(Long id, List<JzTaskWorkOrderInfo> workOrderInfos) {
        boolean flag = true;
        if (CollUtil.isNotEmpty(workOrderInfos)) {
            List<String> statusList = workOrderInfos.stream()
                    .filter(x -> id.equals(x.getEquipmentId()))
                    .map(JzTaskWorkOrderInfo::getStatusInfo)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(statusList)) {
                if (statusList.contains("待检") || statusList.contains("在检")) {
                    flag = false;
                }
            }
        }
        return flag;
    }
}