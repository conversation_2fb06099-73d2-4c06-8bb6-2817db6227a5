package com.westcatr.rd.testbusiness.business.jztask.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 荆州—任务列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_task_info")
@Schema(description = "荆州—任务列表表")
public class JzTaskInfo extends Model<JzTaskInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "结束时间")
    @TableField("end_date")
    private Date endDate;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "样品id")
    @TableField("sample_id")
    private Long sampleId;

    @Schema(description = "开始时间")
    @TableField("start_date")
    private Date startDate;

    @Schema(description = "任务生成时间")
    @TableField("task_creation_time")
    private Date taskCreationTime;

    @Schema(description = "任务编号")
    @Length(max = 255, message = "任务编号长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("task_number")
    private String taskNumber;

    @Schema(description = "任务状态（任务待检、任务在检、任务检毕）")
    @Length(max = 255, message = "任务状态（任务待检、任务在检、任务检毕）长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("task_status")
    private String taskStatus;

    @Schema(description = "检测级别")
    @Length(max = 255, message = "检测级别长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("test_level")
    private String testLevel;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private List<JzTaskArrangementInfo> jzTaskArrangementInfoList;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
