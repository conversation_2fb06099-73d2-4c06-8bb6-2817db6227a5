<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskInspectionItemInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="inspection_completion_time" property="inspectionCompletionTime" />
        <result column="inspection_item_number" property="inspectionItemNumber" />
        <result column="sample_id" property="sampleId" />
        <result column="task_id" property="taskId" />
        <result column="test_results" property="testResults" />
        <result column="test_standard" property="testStandard" />
        <result column="tf_qualified" property="tfQualified" />
        <result column="update_time" property="updateTime" />
        <result column="user_id" property="userId" />
        <result column="work_order_id" property="workOrderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, id, inspection_completion_time, inspection_item_number, sample_id, task_id, test_results, test_standard, tf_qualified, update_time, user_id, work_order_id
    </sql>

</mapper>
