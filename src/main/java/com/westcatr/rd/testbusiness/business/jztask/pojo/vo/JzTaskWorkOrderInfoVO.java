package com.westcatr.rd.testbusiness.business.jztask.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—工单列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "荆州—工单列表表VO对象")
public class JzTaskWorkOrderInfoVO extends JzTaskWorkOrderInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "设备名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentNewInfo.class, mainId = "equipmentId", field = "sbmc")
    private String equipmentName;

    @Schema(description = "测试人员姓名")
    @TableField(exist = false)
    @JoinSelect(joinClass = OrgUserInfo.class, mainId = "testUserId", field = "full_name")
    private String testUserFullName;

    @Schema(description = "工位名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = BuInstrumentWorkstationInfo.class, mainId = "workstationId", field = "workstation_name")
    private String workstationName;

    @Schema(description = "管理任务信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = JzTaskInfo.class, mainId = "taskId")
    private JzTaskInfo jzTaskInfo;

    @Schema(description = "关联样品信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = SampleInfo.class, mainId = "sampleId")
    private SampleInfo sampleInfo;

    @Schema(description = "关联子项信息")
    @TableField(exist = false)
    // @JoinSelect(joinClass = JzTaskInspectionItemInfo.class, relationId =
    // "work_order_id")
    private List<JzTaskInspectionItemInfoVO> jzTaskInspectionItemInfos;
}
