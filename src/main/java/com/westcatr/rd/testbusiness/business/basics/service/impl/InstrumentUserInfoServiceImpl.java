package com.westcatr.rd.testbusiness.business.basics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.InstrumentUserInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import com.westcatr.rd.testbusiness.business.basics.mapper.InstrumentUserInfoMapper;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentUserInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仪器设备—检测人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Service
public class InstrumentUserInfoServiceImpl extends ServiceImpl<InstrumentUserInfoMapper, InstrumentUserInfo> implements InstrumentUserInfoService {

    @Override
    public IPage<InstrumentUserInfo> entityPage(InstrumentUserInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<InstrumentUserInfo>().create(query));
    }

    @Override
    public InstrumentUserInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(InstrumentUserInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(InstrumentUserInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

