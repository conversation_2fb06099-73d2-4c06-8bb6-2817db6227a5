package com.westcatr.rd.testbusiness.business.sample.pojo.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition.Condition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 样品——基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "样品——基本信息表查询对象")
public class SampleInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition(field = "bu_sample_info.id")
    private Long id;

    @QueryCondition(condition = Condition.IN, field = "bu_sample_info.id")
    private List<Long> ids;

    @Schema(description = "样品编号")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleNumber;

    @Schema(description = "样品名称")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleName;

    @Schema(description = "盲样编号")
    @QueryCondition(condition = Condition.LIKE)
    private String blindSampleNumber;

    @Schema(description = "样品型号")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleModel;

    @Schema(description = "样品所属专业")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleMajor;

    @Schema(description = "样品状态")
    @QueryCondition
    private String sampleStatus;

    @Schema(description = "样品序列号")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleSerialNumber;

    @Schema(description = "样品版本号")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleVersionNumber;

    @Schema(description = "样品类型")
    @QueryCondition(condition = Condition.LIKE)
    private String sampleType;

    @Schema(description = "样品数量")
    @QueryCondition
    private Integer sampleNum;

    @Schema(description = "样品处理状态")
    @QueryCondition
    private String sampleProcessStatus;

    private Long managerId;

    @Schema(description = "关联的任务状态")
    private String taskDeptStatusInfo;

    @Schema(description = "领取部门id")
    private Long deptReceDepId;

    @Schema(description = "样品备注")
    @QueryCondition
    private String sampleRemark;

    @Schema(description = "状态")
    @QueryCondition
    private String statusInfo;

    @Schema(description = "关联委托书id")
    @QueryCondition
    private Long entrustId;

    @Schema(description = "关联委托书id")
    @QueryCondition(condition = Condition.IN, field = "entrust_id")
    private List<Long> entrustIds;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Long taskId;

    @QueryCondition
    private Date updateTime;

    @Schema(description = "查询类型 1 样品列表查询(默认)  2 审核样品详情查询")
    private Integer queryType;

    private Boolean tfentrustIdIsNull;

    @Schema(description = "检测类型")
    @QueryCondition(condition = Condition.AUTO_LIKE)
    private String testType;

    @Schema(description = "小于收样结束时间")
    @QueryCondition(condition = QueryCondition.Condition.LE, field = "sample_receipt_time")
    private Date sampleReceiptTimeEnd;

    @Schema(description = "大于收样开始时间")
    @QueryCondition(condition = QueryCondition.Condition.GE, field = "sample_receipt_time")
    private Date sampleReceiptTimeStart;

    @Schema(description = "样品状态")
    @QueryCondition(condition = Condition.AUTO_LIKE)
    private String status;

    @Schema(description = "返样状态")
    @QueryCondition(condition = Condition.AUTO_LIKE)
    private String backStatus;
}
