package com.westcatr.rd.testbusiness.business.mqtt.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备对接规范表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_device_integration_config")
@Schema(description = "设备对接规范表")
public class DeviceIntegrationConfig extends Model<DeviceIntegrationConfig> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "设备类型")
    @Length(max = 64, message = "设备类型长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("device_type")
    private String deviceType;

    @Schema(description = "设备名称")
    @Length(max = 255, message = "设备名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("device_name")
    private String deviceName;

    @Schema(description = "产品代码")
    @Length(max = 64, message = "产品代码长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("product_code")
    private String productCode;

    @Schema(description = "设备代码")
    @Length(max = 64, message = "设备代码长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("device_code")
    private String deviceCode;

    @Schema(description = "类型代码")
    @Length(max = 64, message = "类型代码长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("type_code")
    private String typeCode;

    @Schema(description = "请求模板")
    @TableField("request_template")
    private String requestTemplate;

    @Schema(description = "响应模板")
    @TableField("response_template")
    private String responseTemplate;

    @Schema(description = "参数映射")
    @TableField("param_mapping")
    private String paramMapping;

    @Schema(description = "状态(enabled/disabled)")
    @Length(max = 32, message = "状态长度不能超过32", groups = { Insert.class, Update.class })
    @TableField("status")
    private String status;

    @Schema(description = "描述")
    @Length(max = 512, message = "描述长度不能超过512", groups = { Insert.class, Update.class })
    @TableField("description")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
