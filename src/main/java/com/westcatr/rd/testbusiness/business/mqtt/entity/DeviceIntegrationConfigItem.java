package com.westcatr.rd.testbusiness.business.mqtt.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 设备对接规范条目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_device_integration_config_item")
@Schema(description="设备对接规范条目表")
public class DeviceIntegrationConfigItem extends Model<DeviceIntegrationConfigItem> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @Schema(description = "试验项目配置ID")
    @TableField("config_id")
    private Long configId;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "描述")
    @Length(max = 512, message = "描述长度不能超过512", groups = {Insert.class, Update.class})
    @TableField("description")
    private String description;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "条目编码")
    @Length(max = 255, message = "条目编码长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("item_code")
    private String itemCode;

    @Schema(description = "条目名称")
    @Length(max = 255, message = "条目名称长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("item_name")
    private String itemName;

    @Schema(description = "试验项目编码")
    @Length(max = 255, message = "试验项目编码长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("project_code")
    private String projectCode;

    @Schema(description = "试验项目名称")
    @Length(max = 255, message = "试验项目名称长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("project_name")
    private String projectName;

    @Schema(description = "状态(enabled/disabled)")
    @Length(max = 32, message = "状态(enabled/disabled)长度不能超过32", groups = {Insert.class, Update.class})
    @TableField("status")
    private String status;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
