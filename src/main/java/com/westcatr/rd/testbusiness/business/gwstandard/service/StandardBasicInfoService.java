package com.westcatr.rd.testbusiness.business.gwstandard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 标准—集成装置设备信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
public interface StandardBasicInfoService extends IService<StandardBasicInfo> {

    IPage<StandardBasicInfo> entityPage(StandardBasicInfoQuery query);

    StandardBasicInfo getEntityById(Long id);

    boolean saveEntity(StandardBasicInfo param);

    boolean updateEntity(StandardBasicInfo param);

    boolean removeEntityById(Long id);
}
