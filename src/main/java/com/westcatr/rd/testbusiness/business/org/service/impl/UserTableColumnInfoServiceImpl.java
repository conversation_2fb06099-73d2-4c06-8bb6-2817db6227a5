package com.westcatr.rd.testbusiness.business.org.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.org.pojo.query.UserTableColumnInfoQuery;
import com.westcatr.rd.testbusiness.business.org.entity.UserTableColumnInfo;
import com.westcatr.rd.testbusiness.business.org.mapper.UserTableColumnInfoMapper;
import com.westcatr.rd.testbusiness.business.org.service.UserTableColumnInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 自定义展示列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
@Service
public class UserTableColumnInfoServiceImpl extends ServiceImpl<UserTableColumnInfoMapper, UserTableColumnInfo> implements UserTableColumnInfoService {

    @Override
    public IPage<UserTableColumnInfo> entityPage(UserTableColumnInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<UserTableColumnInfo>().create(query));
    }

    @Override
    public UserTableColumnInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(UserTableColumnInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(UserTableColumnInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

