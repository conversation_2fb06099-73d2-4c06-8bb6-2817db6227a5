package com.westcatr.rd.testbusiness.business.org.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 自定义展示列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="自定义展示列表查询对象")
public class UserTableColumnInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long id;

    @Schema(description = "列表名")
    @QueryCondition
    private String keyName;

    @Schema(description = "列名详情")
    @QueryCondition
    private String columnsInfo;

    @Schema(description = "员工id")
    @QueryCondition
    private Long userId;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;
}
