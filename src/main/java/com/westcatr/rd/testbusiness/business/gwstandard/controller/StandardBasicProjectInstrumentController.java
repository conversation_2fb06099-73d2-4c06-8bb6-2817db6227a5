package com.westcatr.rd.testbusiness.business.gwstandard.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicProjectInstrumentQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicProjectInstrumentVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectInstrumentService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * StandardBasicProjectInstrument 控制器
 * 
 * <AUTHOR>
 * @since 2025-04-16
 */
@Validated
@Tag(name = "标准—实验项目检测仪器信息表接口", description = "标准—实验项目检测仪器信息表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class StandardBasicProjectInstrumentController {

    @Autowired
    private StandardBasicProjectInstrumentService standardBasicProjectInstrumentService;

    @Operation(summary = "获取标准—实验项目检测仪器信息表分页数据")
    @PostMapping("/standardBasicProjectInstrument/page")
    public IResult<IPage<StandardBasicProjectInstrument>> getStandardBasicProjectInstrumentPage(
            @RequestBody StandardBasicProjectInstrumentQuery query) {
        return IResult.ok(standardBasicProjectInstrumentService.entityPage(query));
    }

    @Operation(summary = "获取标准—实验项目检测仪器信息表数据")
    @PostMapping("/standardBasicProjectInstrument/get")
    public IResult<StandardBasicProjectInstrument> getStandardBasicProjectInstrumentById(@RequestBody @Id ID id) {
        return IResult.ok(standardBasicProjectInstrumentService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增标准—实验项目检测仪器信息表数据")
    @PostMapping("/standardBasicProjectInstrument/add")
    public IResult addStandardBasicProjectInstrument(
            @RequestBody @Validated(Insert.class) StandardBasicProjectInstrument param) {
        return IResult.auto(standardBasicProjectInstrumentService.saveEntity(param));
    }

    @Operation(summary = "更新标准—实验项目检测仪器信息表数据")
    @PostMapping("/standardBasicProjectInstrument/update")
    public IResult updateStandardBasicProjectInstrumentById(
            @RequestBody @Validated(Update.class) StandardBasicProjectInstrument param) {
        return IResult.auto(standardBasicProjectInstrumentService.updateEntity(param));
    }

    @Operation(summary = "删除标准—实验项目检测仪器信息表数据")
    @PostMapping("/standardBasicProjectInstrument/delete")
    public IResult deleteStandardBasicProjectInstrumentById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            standardBasicProjectInstrumentService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取标准—实验项目检测仪器信息表VO分页数据")
    @PostMapping("/standardBasicProjectInstrument/voPage")
    public IResult<IPage<StandardBasicProjectInstrumentVO>> getStandardBasicProjectInstrumentVoPage(
            @RequestBody StandardBasicProjectInstrumentQuery query) {
        AssociationQuery<StandardBasicProjectInstrumentVO> associationQuery = new AssociationQuery<>(
                StandardBasicProjectInstrumentVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取标准—实验项目检测仪器信息表VO数据")
    @PostMapping("/standardBasicProjectInstrument/getVo")
    public IResult<StandardBasicProjectInstrumentVO> getStandardBasicProjectInstrumentVoById(@RequestBody @Id ID id) {
        AssociationQuery<StandardBasicProjectInstrumentVO> associationQuery = new AssociationQuery<>(
                StandardBasicProjectInstrumentVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
