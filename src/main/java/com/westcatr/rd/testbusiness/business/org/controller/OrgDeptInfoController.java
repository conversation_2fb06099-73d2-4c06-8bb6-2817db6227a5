package com.westcatr.rd.testbusiness.business.org.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgDeptInfoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.OrgDeptInfoVO;
import com.westcatr.rd.testbusiness.business.org.service.OrgDeptInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * OrgDeptInfo 控制器
 * 
 * <AUTHOR>
 * @since 2023-06-16
 */
@Validated
@Tag(name = "部门信息表接口", description = "部门信息表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class OrgDeptInfoController {

    @Autowired
    private OrgDeptInfoService orgDeptInfoService;

    @Operation(summary = "获取部门信息表分页数据")
    @PostMapping("/orgDeptInfo/page")
    public IResult<IPage<OrgDeptInfo>> getOrgDeptInfoPage(@RequestBody OrgDeptInfoQuery query) {
        return IResult.ok(orgDeptInfoService.entityPage(query));
    }

    @Operation(summary = "获取部门信息表数据")
    @PostMapping("/orgDeptInfo/get")
    public IResult<OrgDeptInfo> getOrgDeptInfoById(@RequestBody @Id ID id) {
        return IResult.ok(orgDeptInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增部门信息表数据")
    @PostMapping("/orgDeptInfo/add")
    public IResult addOrgDeptInfo(@RequestBody @Validated(Insert.class) OrgDeptInfo param) {
        return IResult.auto(orgDeptInfoService.saveEntity(param));
    }

    @Operation(summary = "更新部门信息表数据")
    @PostMapping("/orgDeptInfo/update")
    public IResult updateOrgDeptInfoById(@RequestBody @Validated(Update.class) OrgDeptInfo param) {
        return IResult.auto(orgDeptInfoService.updateEntity(param));
    }

    @Operation(summary = "删除部门信息表数据")
    @PostMapping("/orgDeptInfo/delete")
    public IResult deleteOrgDeptInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            orgDeptInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取部门信息表VO分页数据")
    @PostMapping("/orgDeptInfo/voPage")
    public IResult<IPage<OrgDeptInfoVO>> getOrgDeptInfoVoPage(@RequestBody OrgDeptInfoQuery query) {
        AssociationQuery<OrgDeptInfoVO> associationQuery = new AssociationQuery<>(OrgDeptInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取部门信息表VO数据")
    @PostMapping("/orgDeptInfo/getVo")
    public IResult<OrgDeptInfoVO> getOrgDeptInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<OrgDeptInfoVO> associationQuery = new AssociationQuery<>(OrgDeptInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "获取部门树型信息表VO数据")
    @GetMapping("/orgDeptInfo/tree")
    public IResult<List<OrgDeptInfo>> tree() {
        return IResult.ok(orgDeptInfoService.tree());
    }
}
