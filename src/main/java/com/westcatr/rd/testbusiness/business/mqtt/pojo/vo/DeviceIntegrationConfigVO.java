package com.westcatr.rd.testbusiness.business.mqtt.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 设备对接规范表VO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "设备对接规范表VO对象")
public class DeviceIntegrationConfigVO extends DeviceIntegrationConfig {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

//    @Schema(description = "关联设备数量")
//    @TableField(exist = false)
//    private Integer deviceCount;

    @Schema(description = "条目列表")
    private List<DeviceIntegrationConfigItem> itemList;
}
