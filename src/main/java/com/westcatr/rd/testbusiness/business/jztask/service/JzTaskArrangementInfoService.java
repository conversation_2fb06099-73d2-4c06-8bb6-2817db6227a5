package com.westcatr.rd.testbusiness.business.jztask.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskArrangementInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskArrangementInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 荆州—任务列表排期表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface JzTaskArrangementInfoService extends IService<JzTaskArrangementInfo> {

    IPage<JzTaskArrangementInfo> entityPage(JzTaskArrangementInfoQuery query);

    JzTaskArrangementInfo getEntityById(Long id);

    boolean saveEntity(JzTaskArrangementInfo param);

    boolean updateEntity(JzTaskArrangementInfo param);

    boolean removeEntityById(Long id);
}
