package com.westcatr.rd.testbusiness.business.org.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("org_dept_info")
@Schema(description = "部门信息表")
public class OrgDeptInfo extends Model<OrgDeptInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("pid")
    private Long pid;

    @Schema(description = "部门名称")
    @Length(max = 255, message = "部门名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("dept_name")
    private String deptName;

    @Length(max = 255, message = "长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("dept_explain")
    private String deptExplain;

    @NotNull(message = "不能为空", groups = { Insert.class, Update.class })
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "部门主任userid")
    @TableField("executive_user_id")
    private Long executiveUserId;

    @Schema(description = "部门id（质量平台）")
    @TableField("sync_id_str")
    private String syncIdStr;

    @Schema(description = "部门编码（质量平台）")
    @TableField("sync_code")
    private String syncCode;

    @TableField(exist = false)
    private List<OrgDeptInfo> child;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
