package com.westcatr.rd.testbusiness.business.org.pojo.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="部门信息表查询对象")
public class OrgDeptInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long id;

    @QueryCondition
    private Long pid;

    @Schema(description = "部门名称")
    @QueryCondition
    private String deptName;

    @QueryCondition
    private String deptExplain;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;

    @Schema(description = "部门主任userid")
    @QueryCondition
    private Long executiveUserId;
}
