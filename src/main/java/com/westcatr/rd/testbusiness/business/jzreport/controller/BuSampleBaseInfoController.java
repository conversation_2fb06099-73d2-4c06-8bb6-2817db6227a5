package com.westcatr.rd.testbusiness.business.jzreport.controller;

import com.westcatr.rd.boot.core.dto.ID;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.BuSampleBaseInfoQuery;
import com.westcatr.rd.testbusiness.business.jzreport.service.BuSampleBaseInfoService;
import com.westcatr.rd.testbusiness.business.jzreport.entity.BuSampleBaseInfo;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.vo.BuSampleBaseInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 *  BuSampleBaseInfo 控制器
 *   <AUTHOR>
 *  @since 2025-04-02
 */
@Validated
@Tag(name = "样品基本信息-关联表接口", description = "样品基本信息-关联表接口", extensions = {@Extension(properties = {@ExtensionProperty(name = "x-order", value = "100")})})
@Slf4j
@RestController
public class BuSampleBaseInfoController {

    @Autowired
    private BuSampleBaseInfoService buSampleBaseInfoService;

    @Operation(summary = "获取样品基本信息-关联表分页数据")
    @PostMapping("/buSampleBaseInfo/page")
    public IResult<IPage<BuSampleBaseInfo>> getBuSampleBaseInfoPage(@RequestBody BuSampleBaseInfoQuery query) {
        return IResult.ok(buSampleBaseInfoService.entityPage(query));
    }

    @Operation(summary = "获取样品基本信息-关联表数据")
    @PostMapping("/buSampleBaseInfo/get")
    public IResult<BuSampleBaseInfo> getBuSampleBaseInfoById(@RequestBody @Id ID id) {
        return IResult.ok(buSampleBaseInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增样品基本信息-关联表数据")
    @PostMapping("/buSampleBaseInfo/add")
    public IResult addBuSampleBaseInfo(@RequestBody @Validated(Insert.class) BuSampleBaseInfo param) {
        return IResult.auto(buSampleBaseInfoService.saveEntity(param));
    }

    @Operation(summary = "更新样品基本信息-关联表数据")
    @PostMapping("/buSampleBaseInfo/update")
    public IResult updateBuSampleBaseInfoById(@RequestBody @Validated(Update.class) BuSampleBaseInfo param) {
        return IResult.auto(buSampleBaseInfoService.updateEntity(param));
    }

    @Operation(summary = "删除样品基本信息-关联表数据")
    @PostMapping("/buSampleBaseInfo/delete")
    public IResult deleteBuSampleBaseInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            buSampleBaseInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取样品基本信息-关联表VO分页数据")
    @PostMapping("/buSampleBaseInfo/voPage")
    public IResult<IPage<BuSampleBaseInfoVO>> getBuSampleBaseInfoVoPage(@RequestBody BuSampleBaseInfoQuery query) {
        AssociationQuery<BuSampleBaseInfoVO> associationQuery = new AssociationQuery<>(BuSampleBaseInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取样品基本信息-关联表VO数据")
    @PostMapping("/buSampleBaseInfo/getVo")
    public IResult<BuSampleBaseInfoVO> getBuSampleBaseInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<BuSampleBaseInfoVO> associationQuery = new AssociationQuery<>(BuSampleBaseInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }


}
