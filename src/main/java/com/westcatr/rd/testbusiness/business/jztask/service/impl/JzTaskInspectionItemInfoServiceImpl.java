package com.westcatr.rd.testbusiness.business.jztask.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInspectionItemInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo;
import com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskInspectionItemInfoMapper;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInspectionItemInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 荆州—检项列表表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Service
public class JzTaskInspectionItemInfoServiceImpl extends ServiceImpl<JzTaskInspectionItemInfoMapper, JzTaskInspectionItemInfo> implements JzTaskInspectionItemInfoService {

    @Override
    public IPage<JzTaskInspectionItemInfo> entityPage(JzTaskInspectionItemInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<JzTaskInspectionItemInfo>().create(query));
    }

    @Override
    public JzTaskInspectionItemInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(JzTaskInspectionItemInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(JzTaskInspectionItemInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

