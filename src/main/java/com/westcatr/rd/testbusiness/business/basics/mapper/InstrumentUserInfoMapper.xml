<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.basics.mapper.InstrumentUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo">
        <id column="id" property="id" />
        <result column="instrument_new_id" property="instrumentNewId" />
        <result column="user_id" property="userId" />
        <result column="user_full_name" property="userFullName" />
        <result column="sort_num" property="sortNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, instrument_new_id, user_id, user_full_name, sort_num
    </sql>

</mapper>
