package com.westcatr.rd.testbusiness.business.mqtt.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.query.DeviceIntegrationConfigItemQuery;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.vo.DeviceIntegrationConfigItemVO;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 *  DeviceIntegrationConfigItem 控制器
 *   <AUTHOR>
 *  @since 2025-06-12
 */
@Validated
@Tag(name = "设备对接规范条目表接口", description = "设备对接规范条目表接口", extensions = {@Extension(properties = {@ExtensionProperty(name = "x-order", value = "100")})})
@Slf4j
@RestController
public class DeviceIntegrationConfigItemController {

    @Autowired
    private DeviceIntegrationConfigItemService deviceIntegrationConfigItemService;

    @Operation(summary = "获取设备对接规范条目表分页数据")
    @PostMapping("/deviceIntegrationConfigItem/page")
    public IResult<IPage<DeviceIntegrationConfigItem>> getDeviceIntegrationConfigItemPage(@RequestBody DeviceIntegrationConfigItemQuery query) {
        return IResult.ok(deviceIntegrationConfigItemService.entityPage(query));
    }

    @Operation(summary = "获取设备对接规范条目表数据")
    @PostMapping("/deviceIntegrationConfigItem/get")
    public IResult<DeviceIntegrationConfigItem> getDeviceIntegrationConfigItemById(@RequestBody @Id ID id) {
        return IResult.ok(deviceIntegrationConfigItemService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增设备对接规范条目表数据")
    @PostMapping("/deviceIntegrationConfigItem/add")
    public IResult addDeviceIntegrationConfigItem(@RequestBody @Validated(Insert.class) DeviceIntegrationConfigItem param) {
        return IResult.auto(deviceIntegrationConfigItemService.saveEntity(param));
    }

    @Operation(summary = "更新设备对接规范条目表数据")
    @PostMapping("/deviceIntegrationConfigItem/update")
    public IResult updateDeviceIntegrationConfigItemById(@RequestBody @Validated(Update.class) DeviceIntegrationConfigItem param) {
        return IResult.auto(deviceIntegrationConfigItemService.updateEntity(param));
    }

    @Operation(summary = "删除设备对接规范条目表数据")
    @PostMapping("/deviceIntegrationConfigItem/delete")
    public IResult deleteDeviceIntegrationConfigItemById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            deviceIntegrationConfigItemService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取设备对接规范条目表VO分页数据")
    @PostMapping("/deviceIntegrationConfigItem/voPage")
    public IResult<IPage<DeviceIntegrationConfigItemVO>> getDeviceIntegrationConfigItemVoPage(@RequestBody DeviceIntegrationConfigItemQuery query) {
        AssociationQuery<DeviceIntegrationConfigItemVO> associationQuery = new AssociationQuery<>(DeviceIntegrationConfigItemVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取设备对接规范条目表VO数据")
    @PostMapping("/deviceIntegrationConfigItem/getVo")
    public IResult<DeviceIntegrationConfigItemVO> getDeviceIntegrationConfigItemVoById(@RequestBody @Id ID id) {
        AssociationQuery<DeviceIntegrationConfigItemVO> associationQuery = new AssociationQuery<>(DeviceIntegrationConfigItemVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }


}
