package com.westcatr.rd.testbusiness.business.basics.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.WeatherInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.WeatherService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 天气信息控制器
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Validated
@Tag(name = "天气信息管理接口", description = "天气信息管理接口")
@Slf4j
@RestController
public class WeatherController {

    @Autowired
    private WeatherService weatherService;

    /**
     * 获取城市天气信息
     *
     * @param cityName 城市名称，例如：荆州
     * @return 天气信息
     */
    @Operation(summary = "获取环境")
    @PostMapping("/environmentInfo/getWeather")
    public IResult getWeather(@RequestParam(defaultValue = "荆州") String cityName) {
        log.info("获取城市[{}]天气信息", cityName);
        WeatherInfoVO weatherInfo = weatherService.getWeatherInfo(cityName);
        return IResult.ok(weatherInfo);
    }
}