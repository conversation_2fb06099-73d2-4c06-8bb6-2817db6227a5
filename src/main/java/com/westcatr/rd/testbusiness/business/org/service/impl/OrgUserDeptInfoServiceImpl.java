package com.westcatr.rd.testbusiness.business.org.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgUserDeptInfoQuery;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.westcatr.rd.testbusiness.business.org.mapper.OrgUserDeptInfoMapper;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserDeptInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户部门信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Service
public class OrgUserDeptInfoServiceImpl extends ServiceImpl<OrgUserDeptInfoMapper, OrgUserDeptInfo> implements OrgUserDeptInfoService {

    @Override
    public IPage<OrgUserDeptInfo> entityPage(OrgUserDeptInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<OrgUserDeptInfo>().create(query));
    }

    @Override
    public OrgUserDeptInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(OrgUserDeptInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(OrgUserDeptInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

