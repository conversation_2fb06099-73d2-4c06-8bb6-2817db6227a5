package com.westcatr.rd.testbusiness.business.org.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgUserDeptInfoQuery;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 用户部门信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
public interface OrgUserDeptInfoService extends IService<OrgUserDeptInfo> {

    IPage<OrgUserDeptInfo> entityPage(OrgUserDeptInfoQuery query);

    OrgUserDeptInfo getEntityById(Long id);

    boolean saveEntity(OrgUserDeptInfo param);

    boolean updateEntity(OrgUserDeptInfo param);

    boolean removeEntityById(Long id);
}
