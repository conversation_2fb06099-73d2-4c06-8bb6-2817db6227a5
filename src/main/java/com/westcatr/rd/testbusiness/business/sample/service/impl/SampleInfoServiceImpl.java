package com.westcatr.rd.testbusiness.business.sample.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import com.westcatr.rd.testbusiness.business.workstation.service.WorkstationHistoryCountService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentNewInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentUserInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInstrumentInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicInstrumentInfoVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicModelParamService;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectParamService;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jzreport.service.JzReportInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskArrangementInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskArrangementInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderModelParamService;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import com.westcatr.rd.testbusiness.business.sample.mapper.SampleInfoMapper;
import com.westcatr.rd.testbusiness.business.sample.pojo.dto.AutoSampleNumberDto;
import com.westcatr.rd.testbusiness.business.sample.pojo.query.SampleInfoQuery;
import com.westcatr.rd.testbusiness.business.sample.pojo.vo.SampleInfoVO;
import com.westcatr.rd.testbusiness.business.sample.service.SampleInfoService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import lombok.extern.log4j.Log4j2;

/**
 * <p>
 * 样品——基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Log4j2
@Service
public class SampleInfoServiceImpl extends ServiceImpl<SampleInfoMapper, SampleInfo> implements SampleInfoService {

    @Autowired
    private StandardBasicModelParamService standardBasicModelParamService;

    @Autowired
    private BaseDao baseDao;

    @Autowired
    private JzTaskWorkOrderModelParamService jzTaskWorkOrderModelParamService;

    @Autowired
    private JzTaskInfoService jzTaskInfoService;

    @Autowired
    private JzTaskArrangementInfoService jzTaskArrangementInfoService;

    @Autowired
    private JzReportInfoService jzReportInfoService;

    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;

    @Autowired
    private InstrumentUserInfoService instrumentUserInfoService;

    @Autowired
    private StandardBasicProjectParamService standardBasicProjectParamService;

    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;

    @Autowired
    private WorkstationHistoryCountService workstationHistoryCountService;

    public static boolean isMoreThanOnce(List<String> list, String str) {
        int frequency = Collections.frequency(list, str);
        return frequency > 1;
    }

    @Override
    public IPage<SampleInfo> entityPage(SampleInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<SampleInfo>().create(query));
    }

    @Override
    public SampleInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(SampleInfo param) {
        param.setStatus("收样");
        boolean flag = this.save(param);

        if (flag) {
            //新增WorkstationHistoryCount（为工作台统计做数据准备）
            WorkstationHistoryCount workstationHistoryCount = new WorkstationHistoryCount();
            workstationHistoryCount.setStatus(param.getStatus());
            workstationHistoryCount.setType("样品");
            workstationHistoryCount.setCreateTime(new Date());
            workstationHistoryCount.setNumber(param.getSampleNumber());

            workstationHistoryCountService.save(workstationHistoryCount);

            // 新增和生成任务逻辑合并
            generationTask(param.getId());
        }

        return flag;
    }

    @Override
    public boolean updateEntity(SampleInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public IPage<SampleInfoVO> entityVoPage(SampleInfoQuery query) {
        // 筛选出任务状态为待分配
        if (ObjectUtil.isNull(query.getQueryType())) {
            query.setQueryType(1);
        }
        /*
         * if (StrUtil.isNotBlank(query.getSampleNumber())) {
         * query.setSampleNumber(query.getSampleNumber().toUpperCase());
         * } else {
         * query.setSampleNumber(null);
         * }
         */
        AssociationQuery<SampleInfoVO> associationQuery = new AssociationQuery<>(SampleInfoVO.class);
        if (StrUtil.isNotBlank(query.getTaskDeptStatusInfo())) {
            String sql = getSql(query);
            List<Long> ids = baseDao.selectListBySql(Long.class, sql, null);
            setIdOrIds(query, ids);
        }
        if (query.getDeptReceDepId() != null) {
            String sql = "select bu_sample_info.id from bu_sample_info LEFT JOIN ( SELECT sample_id, MAX(create_time) as max_create_time  FROM bu_sample_operation_info  GROUP BY sample_id ) latest_operation ON bu_sample_info.id = latest_operation.sample_id LEFT JOIN bu_sample_operation_info b ON bu_sample_info.id = b.sample_id AND latest_operation.max_create_time = b.create_time LEFT JOIN org_dept_info c ON b.user_dept_id = c.id where b.user_dept_id="
                    + query.getDeptReceDepId() + ";";
            List<Long> ids = baseDao.selectListBySql(Long.class, sql, null);
            setIdOrIds(query, ids);
        }
        QueryWrapper<SampleInfo> wrapper = new WrapperFactory<SampleInfo>().create(query);
        wrapper.orderByDesc("bu_sample_info.update_time");
        if (query.getTfentrustIdIsNull() != null && query.getTfentrustIdIsNull()) {
            wrapper.isNull("bu_sample_info.entrust_id");
        }
        if (CollUtil.isNotEmpty(query.getIds())) {
            query.setIds(query.getIds().stream().distinct().toList());
            query.setId(null);
        }
        IPage<SampleInfoVO> iPage = associationQuery.voPage(query, wrapper);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(z -> {
                // jz任务状态
                z.setTaskStatus(z.getJzTaskStatus());

                long reportCount = jzReportInfoService.count(new LambdaQueryWrapper<JzReportInfo>()
                        .eq(JzReportInfo::getSampleId, z.getId()));
                if (reportCount > 0) {
                    z.setJzReportStatus("已生成");
                } else {
                    z.setJzReportStatus("未生成");
                }
            });
        }
        return iPage;
    }

    private void setIdOrIds(SampleInfoQuery query, List<Long> ids) {
        if (CollUtil.isNotEmpty(ids) && CollUtil.isNotEmpty(query.getIds())) {
            List<Long> nowIds = (List<Long>) CollUtil.intersection(query.getIds(), ids);
            if (CollUtil.isNotEmpty(nowIds)) {
                query.setIds(nowIds);
            } else {
                query.setId(-1L);
            }
        } else {
            query.setIds(ids);
        }
    }

    @NotNull
    private static String getSql(SampleInfoQuery query) {
        String sql = "SELECT a.id from bu_sample_info a left join bu_task_info b on a.task_id=b.id where b.task_status='"
                + query.getTaskDeptStatusInfo() + "';";
        return sql;
    }

    @Override
    public List<SampleInfo> importData(MultipartFile file) throws IOException {
        // 使用Hutool的ExcelReader读取数据
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());

        // 读取数据，起始行为0，结束行为最大值（读取所有）
        List<List<Object>> read = reader.read(0, Integer.MAX_VALUE);

        // 固定列数为9
        int fixedColumnCount = 10;

        // 遍历每一行，补足不足9列的情况
        for (List<Object> row : read) {
            // 如果该行的列数少于固定的列数
            while (row.size() < fixedColumnCount) {
                // 补充空值
                row.add("");
            }
        }
        ArrayList<SampleInfo> objects = new ArrayList<>();
        read.remove(0);
        for (List<Object> obj : read) {
            SampleInfo sampleInfo = new SampleInfo();
            String sampleName = String.valueOf(obj.get(0));
            String sampleSerialNumber = String.valueOf(obj.get(1));
            String sampleVersionNumber = String.valueOf(obj.get(2));
            String sampleModel = String.valueOf(obj.get(3));
            Integer sampleNum = Convert.toInt(obj.get(4), 1);
            String sampleStatus = String.valueOf(obj.get(5));
            String sampleProcessStatus = String.valueOf(obj.get(6));
            String productCompany = String.valueOf(obj.get(7));
            String productCompanyAddress = null;
            if (ObjectUtil.isNotNull(obj.get(8))) {
                productCompanyAddress = String.valueOf(obj.get(8));
            }
            String sampleRemark = null;
            if (ObjectUtil.isNotNull(obj.get(9))) {
                sampleRemark = String.valueOf(obj.get(9));
            }
            if (StrUtil.isEmpty(sampleName) || StrUtil.isEmpty(sampleModel) || StrUtil.isEmpty(sampleSerialNumber)
                    || StrUtil.isEmpty(productCompany)) {
                throw new IRuntimeException(
                        "第【" + (read.indexOf(obj) + 2) + "】行中，样品名称、样品序号、样品型号、生产单位为必填项，请检查数据并填写完整后重新导入。");
            }
            if ("null".equals(sampleName)) {
                sampleInfo.setSampleName(null);
            } else {
                sampleInfo.setSampleName(sampleName);
            }

            if ("null".equals(sampleModel)) {
                sampleInfo.setSampleModel(null);
            } else {
                sampleInfo.setSampleModel(sampleModel);
            }

            if ("null".equals(sampleRemark)) {
                sampleInfo.setSampleRemark(null);
            } else {
                sampleInfo.setSampleRemark(sampleRemark);
            }
            objects.add(sampleInfo);
        }
        return objects;
    }

    @Override
    public List<SampleInfo> importExcel(MultipartFile file) throws IOException {
        List<SampleInfo> returnList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), SampleInfo.class, new PageReadListener<SampleInfo>(returnList::addAll))
                .sheet().doRead();
        return returnList;
    }

    @Override
    public void exportTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("样品信息模板", StandardCharsets.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 使用 EasyExcel 导出空数据，只包含标题
            EasyExcel.write(response.getOutputStream(), SampleInfo.class)
                    .sheet("样品信息")
                    .doWrite((Collection<?>) null);

        } catch (IOException e) {
            log.error("导出模板失败", e);
            throw new RuntimeException("导出模板失败", e);
        }
    }

    @Override
    public AutoSampleNumberDto generateSampleNumber() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");

        String format = sdf.format(date);
        String format1 = sdf1.format(date);
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取日
        int day = today.getDayOfMonth();
        String dayStr;

        if (day * 2 < 10) {
            dayStr = "0" + day * 2;
        } else {
            dayStr = String.valueOf(day * 2);
        }

        String secondaryNumber = "JH" + format + "-00" + dayStr + "-";

        List<SampleInfo> sampleInfos = this.list(new LambdaQueryWrapper<SampleInfo>()
                .like(SampleInfo::getCreateTime, format1));

        // 最终生成的样品编号和二次盲样编号
        String sampleNumber = null;

        if (CollUtil.isNotEmpty(sampleInfos)) {
            secondaryNumber = secondaryNumber + String.format("%04d", sampleInfos.size() + 1);
            sampleNumber = "s" + sdf2.format(date) + String.format("%04d", sampleInfos.size() + 1);
        } else {
            secondaryNumber = secondaryNumber + String.format("%04d", 1);
            sampleNumber = "s" + sdf2.format(date) + String.format("%04d", 1);
        }

        // 生成样品编号

        AutoSampleNumberDto autoSampleNumberDto = new AutoSampleNumberDto();
        autoSampleNumberDto.setSampleNumber(sampleNumber);
        autoSampleNumberDto.setSecondaryBlindSampleNumber(secondaryNumber);
        return autoSampleNumberDto;
    }

    @Override
    public boolean backSampleInfo(SampleInfo param) {
        param.setStatus("返样");
        boolean flag = this.updateById(param);
        if (flag){
            //新增WorkstationHistoryCount（为工作台统计做数据准备）
            WorkstationHistoryCount workstationHistoryCount = new WorkstationHistoryCount();
            workstationHistoryCount.setStatus(param.getStatus());
            workstationHistoryCount.setType("样品");
            workstationHistoryCount.setCreateTime(new Date());
            workstationHistoryCount.setNumber(param.getSampleNumber());

            workstationHistoryCountService.save(workstationHistoryCount);
        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean generationTask(Long id) {
        long count = jzTaskInfoService
                .count(new LambdaQueryWrapper<>(JzTaskInfo.class).eq(JzTaskInfo::getSampleId, id));
        if (count > 0) {
            throw new IRuntimeException("该样品已生成任务");
        }
        SampleInfo sampleInfo = this.getById(id);
        /*-------------------------------新增生成任务开始---------------------------------------*/
        JzTaskInfo jzTaskInfo = new JzTaskInfo();
        jzTaskInfo.setSampleId(id);
        jzTaskInfo.setTaskCreationTime(new Date());
        jzTaskInfo.setTestLevel(sampleInfo.getTestType());
        jzTaskInfo.setTaskStatus("任务待检");

        // 任务编号规则
        String taskNumber = null;

        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");

        String format = sdf.format(date);
        String format1 = sdf1.format(date);

        List<JzTaskInfo> jzTaskInfos = jzTaskInfoService.list(new LambdaQueryWrapper<JzTaskInfo>()
                .like(JzTaskInfo::getCreateTime, format1));
        if (CollUtil.isNotEmpty(jzTaskInfos)) {
            taskNumber = "t" + format + String.format("%04d", jzTaskInfos.size() + 1);
        } else {
            taskNumber = "t" + format + String.format("%04d", 1);
        }

        jzTaskInfo.setTaskNumber(taskNumber);
        boolean flag = jzTaskInfoService.save(jzTaskInfo);
        if (flag){
            //新增WorkstationHistoryCount（为工作台统计做数据准备）
            WorkstationHistoryCount workstationHistoryCount = new WorkstationHistoryCount();
            workstationHistoryCount.setStatus(jzTaskInfo.getTaskStatus());
            workstationHistoryCount.setType("任务");
            workstationHistoryCount.setCreateTime(new Date());
            workstationHistoryCount.setNumber(jzTaskInfo.getTaskNumber());

            workstationHistoryCountService.save(workstationHistoryCount);
        }
        /*-------------------------------新增生成任务结束---------------------------------------*/

        List<StandardBasicInstrumentInfo> basicInfoList = standardBasicInstrumentInfoService
                .list(new LambdaQueryWrapper<>(StandardBasicInstrumentInfo.class)
                        .eq(StandardBasicInstrumentInfo::getSampleName, sampleInfo.getSampleName()));
        if (CollUtil.isEmpty(basicInfoList)) {
            throw new IRuntimeException("该样品未关联标准");
        }
        if (CollUtil.isNotEmpty(basicInfoList)) {
            StandardBasicInstrumentInfoQuery query = new StandardBasicInstrumentInfoQuery();
            // query.setIntegratedDeviceIds(
            // basicInfoList.stream().map(StandardBasicInfo::getId).collect(Collectors.toList()));

            // 如果是A ，查询 abc
            /*
             * if ("A".equals(sampleInfo.getTestType())) {
             * query.setTestCapabilityLevels(Arrays.asList("A", "B", "C"));
             * } else if ("B".equals(sampleInfo.getTestType())) {
             * query.setTestCapabilityLevels(Arrays.asList("B", "C"));
             * } else if ("C".equals(sampleInfo.getTestType())) {
             * query.setTestCapabilityLevels(Arrays.asList("C"));
             * }
             */
            query.setTestCapabilityLevel(sampleInfo.getTestType());
            query.setSampleSpecifications(sampleInfo.getSampleModel());
            // 根据样品规格、样品名称去找到对应检测仪器标准instrumentInfoListVO
            List<StandardBasicInstrumentInfoVO> standardBasicInstrumentInfoVOs = new AssociationQuery<>(
                    StandardBasicInstrumentInfoVO.class).voList(query);

            if (CollUtil.isEmpty(standardBasicInstrumentInfoVOs)) {
                throw new IRuntimeException("该样品未能匹配上符合条件的实验标准");
            }
            // 用于存储”任务列表排期“
            List<JzTaskArrangementInfo> saveList = new ArrayList<>();

            // 获取所有在检、待检工单
            /*
             * List<JzTaskWorkOrderInfo> workOrderInfos = jzTaskWorkOrderInfoService
             * .list(new LambdaQueryWrapper<>(JzTaskWorkOrderInfo.class)
             * .in(JzTaskWorkOrderInfo::getStatusInfo, "在检"));
             * // 获取里面的所有设备id
             * 
             * Set<Long> equipmentIds = workOrderInfos.stream()
             * .map(JzTaskWorkOrderInfo::getEquipmentId)
             * .collect(Collectors.toSet());
             * // 获取所有的人员id
             * Set<Long> userIds = workOrderInfos.stream()
             * .map(JzTaskWorkOrderInfo::getTestUserId)
             * .collect(Collectors.toSet());
             */

            standardBasicInstrumentInfoVOs.forEach(y -> {
                List<StandardBasicProjectInstrument> standardBasicProjectInstrumentList = y
                        .getStandardBasicProjectInstrumentList();
                // 按照设备id进行分组
                Map<Long, List<StandardBasicProjectInstrument>> map = standardBasicProjectInstrumentList.stream()
                        .collect(
                                Collectors.groupingBy(StandardBasicProjectInstrument::getInstrumentId));
                // 循环map
                map.forEach((k, v) -> {
                    // 获取 k和 v
                    Long instrumentId = k;
                    // 查询设备vo
                    InstrumentNewInfoVO instrumentNewInfoVO = new AssociationQuery<>(InstrumentNewInfoVO.class)
                            .getVo(instrumentId);
                    List<StandardBasicProjectInstrument> listProInfo = v;
                    // if (equipmentIds.contains(instrumentId)) {
                    // throw new IRuntimeException("仪器【" + instrumentNewInfoVO.getSbmc() +
                    // "】处于非空闲状态，无法生成任务");
                    // }
                    List<InstrumentUserInfo> instrumentUserInfos = instrumentUserInfoService
                            .list(new LambdaQueryWrapper<>(InstrumentUserInfo.class)
                                    .eq(InstrumentUserInfo::getInstrumentNewId, instrumentId)
                                    .orderByAsc(InstrumentUserInfo::getSortNum));
                    // 根据instrumentUserInfos的排序确定本次检测人员、如果都被占用 则提示
                    if (CollUtil.isEmpty(instrumentUserInfos)) {
                        throw new IRuntimeException("设备【" + instrumentNewInfoVO.getSbmc() + "】未添加测试人员");
                    }
                    Long testUserId = null;
                    Random random = new Random();
                    testUserId = instrumentUserInfos.get(random.nextInt(instrumentUserInfos.size())).getUserId();
                    // for (InstrumentUserInfo z : instrumentUserInfos) {
                    // // 排序从1开始,自动排序
                    // if (!userIds.contains(z.getUserId())) {
                    // testUserId = z.getUserId();
                    // // 找到了就退出
                    // break;
                    // }
                    // }
                    // // 如果没有找到合适的用户，提示报错
                    // if (testUserId == null) {
                    // throw new IRuntimeException("设备【" + instrumentNewInfoVO.getSbmc() +
                    // "】无法找到空闲的测试人员，请检查测试人员配置");
                    // }

                    // 去生成排期
                    JzTaskArrangementInfo jzTaskArrangementInfo = new JzTaskArrangementInfo();
                    jzTaskArrangementInfo.setEquipmentId(instrumentId);
                    jzTaskArrangementInfo.setTaskId(jzTaskInfo.getId());
                    jzTaskArrangementInfo.setGwBzName(listProInfo.stream()
                            .map(StandardBasicProjectInstrument::getProjectName).collect(Collectors.joining(",")));
                    // 把 listProInfo 获取id用逗号分开
                    String newIds = listProInfo.stream()
                            .map(StandardBasicProjectInstrument::getId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    List<StandardBasicProjectParam> standardBasicProjectParams = standardBasicProjectParamService
                            .list(new LambdaQueryWrapper<>(StandardBasicProjectParam.class)
                                    .in(StandardBasicProjectParam::getStandardBasicProjectId,
                                            Arrays.stream(newIds.split(",")).map(Long::parseLong)
                                                    .collect(Collectors.toList())));
                    if (CollUtil.isNotEmpty(standardBasicProjectParams)) {
                        jzTaskArrangementInfo.setModelIds(standardBasicProjectParams.stream()
                                .map(StandardBasicProjectParam::getId)
                                .map(String::valueOf)
                                .collect(Collectors.joining(",")));
                    }
                    jzTaskArrangementInfo.setGwBzId(newIds);
                    jzTaskArrangementInfo.setTestUserId(testUserId);
                    jzTaskArrangementInfo.setWorkstationId(instrumentNewInfoVO.getWorkstationId());
                    saveList.add(jzTaskArrangementInfo);
                });

                // 这里把本次任务所有检项添加到对应的表
                List<StandardBasicModelParam> standardBasicModelParams = standardBasicModelParamService
                        .list(new LambdaQueryWrapper<>(StandardBasicModelParam.class)
                                .in(StandardBasicModelParam::getStandardBasicInstrumentId, y.getId()));
                if (CollUtil.isNotEmpty(standardBasicModelParams)) {
                    List<JzTaskWorkOrderModelParam> jzTaskWorkOrderModelParams = new ArrayList<>();
                    for (StandardBasicModelParam x : standardBasicModelParams) {
                        JzTaskWorkOrderModelParam jzTaskWorkOrderModelParam = new JzTaskWorkOrderModelParam();
                        cn.hutool.core.bean.BeanUtil.copyProperties(x, jzTaskWorkOrderModelParam);
                        jzTaskWorkOrderModelParam.setId(null);
                        jzTaskWorkOrderModelParam.setTaskId(jzTaskInfo.getId());
                        jzTaskWorkOrderModelParam.setSourceId(x.getId());
                        jzTaskWorkOrderModelParam.setSourceProjectParamId(x.getStandardBasicProjectParamId());
                        jzTaskWorkOrderModelParam.setCreateTime(new Date());
                        jzTaskWorkOrderModelParam.setUpdateTime(new Date());
                        jzTaskWorkOrderModelParams.add(jzTaskWorkOrderModelParam);
                    }
                    jzTaskWorkOrderModelParamService.saveBatch(jzTaskWorkOrderModelParams);
                }
            });
            // if (saveList.size() == 0) {
            // throw new IRuntimeException("该样品关联的实验无法匹配到空闲的设备");
            // }
            jzTaskArrangementInfoService.saveBatch(saveList);

        }
        return true;
    }

    private Long toGetFreeTestUserId(InstrumentNewInfoVO instrumentNewInfoVO,
            StandardBasicInstrumentInfoVO standardBasicInstrumentInfoVO) {

        Long userId = 0L;

        List<InstrumentUserInfo> instrumentUserInfos = instrumentUserInfoService
                .list(new LambdaQueryWrapper<InstrumentUserInfo>()
                        .eq(InstrumentUserInfo::getInstrumentNewId, instrumentNewInfoVO.getId())
                        .orderByAsc(InstrumentUserInfo::getSortNum));
        if (CollUtil.isNotEmpty(instrumentUserInfos)) {
            // 获取到该空闲设备的所有可以测试的测试人员id集合
            List<Long> userIds = instrumentUserInfos.stream().map(x -> x.getUserId()).collect(Collectors.toList());

            for (Long userIdInfo : userIds) {
                int times = 0;
                List<JzTaskWorkOrderInfo> workOrderInfos = jzTaskWorkOrderInfoService.list().stream()
                        .filter(x1 -> "待检".equals(x1.getStatusInfo()))
                        .filter(x1 -> "在检".equals(x1.getStatusInfo()))
                        .filter(x1 -> userIdInfo.equals(x1.getTestUserId()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(workOrderInfos)) {
                    if (times != userIds.size()) {
                        continue;
                    } else {
                        throw new IRuntimeException(
                                "该样品关联的[" + standardBasicInstrumentInfoVO.getProName() + "]的实验无法匹配到空闲的测试人员");
                    }
                } else {
                    userId = userIdInfo;
                }
            }
        } else {
            throw new IRuntimeException("该设备还未添加可以进行相关操作的测试人员");
        }

        return userId;
    }
}
