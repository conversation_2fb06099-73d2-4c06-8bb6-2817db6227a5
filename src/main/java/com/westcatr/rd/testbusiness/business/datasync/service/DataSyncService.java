package com.westcatr.rd.testbusiness.business.datasync.service;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 数据同步服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
public interface DataSyncService {

    /**
     * 执行数据同步
     * 
     * @return 同步结果统计
     */
    Map<String, Object> syncData();

    /**
     * 执行增量数据同步
     * 
     * @param lastSyncTime 上次同步时间
     * @return 同步结果统计
     */
    Map<String, Object> syncIncrementalData(Date lastSyncTime);

    /**
     * 获取最后同步时间
     * 
     * @return 最后同步时间
     */
    Date getLastSyncTime();

    /**
     * 清理同步缓存
     * 用于重置同步状态，重新开始全量同步
     * 
     * @return 清理结果
     */
    Map<String, Object> clearSyncCache();

    /**
     * 更新同步时间
     * 
     * @param syncTime 同步时间
     */
    void updateLastSyncTime(Date syncTime);

    /**
     * 获取同步状态
     * 
     * @return 同步状态信息
     */
    Map<String, Object> getSyncStatus();

    /**
     * 获取同步统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getSyncStatistics();

    /**
     * 重置同步状态
     */
    void resetSyncStatus();

    /**
     * 检查数据源连接状态
     * 
     * @return 连接状态
     */
    Map<String, Boolean> checkDataSourceStatus();
}
