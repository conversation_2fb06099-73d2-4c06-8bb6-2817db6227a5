package com.westcatr.rd.testbusiness.business.devicedata.service;

import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataRequest;
import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataResponse;
import com.westcatr.rd.testbusiness.business.devicedata.entity.DeviceDataConfig;

import java.util.List;

/**
 * <p>
 * 设备对接数据管理服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
public interface DeviceDataService {

    /**
     * 根据设备代码和时间范围查询设备数据
     *
     * @param request 查询请求
     * @return 设备数据响应列表
     */
    List<DeviceDataResponse> getDeviceData(DeviceDataRequest request);

    /**
     * 根据设备代码查询配置
     *
     * @param deviceCode 设备代码
     * @return 设备配置
     */
    DeviceDataConfig getDeviceConfig(String deviceCode);
}
