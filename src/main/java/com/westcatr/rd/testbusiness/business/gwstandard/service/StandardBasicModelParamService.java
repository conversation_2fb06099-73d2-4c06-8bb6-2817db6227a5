package com.westcatr.rd.testbusiness.business.gwstandard.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.dto.ModelParamCalculateResult;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicModelParamQuery;
/**
 * <p>
 * 标准-模型参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface StandardBasicModelParamService extends IService<StandardBasicModelParam> {

    IPage<StandardBasicModelParam> entityPage(StandardBasicModelParamQuery query);

    StandardBasicModelParam getEntityById(Long id);

    boolean saveEntity(StandardBasicModelParam param);

    boolean updateEntity(StandardBasicModelParam param);

    boolean removeEntityById(Long id);
    
    /**
     * 🧮 验证公式并计算结果
     * <p>
     * 用于验证公式的正确性并计算结果
     * </p>
     *
     * @param formula 公式字符串
     * @param paramValues 参数值映射
     * @return 计算结果
     */
    BigDecimal validateAndCalculateFormula(String formula, Map<String, BigDecimal> paramValues);
    
    /**
     * 🧮 批量计算标准下的所有计算型参数
     * <p>
     * 获取标准下所有计算型参数，并根据其公式计算结果
     * </p>
     *
     * @param standardId 标准ID
     * @return 计算结果列表
     */
    List<ModelParamCalculateResult> batchCalculateFormulasByStandardId(Long standardId);
}
