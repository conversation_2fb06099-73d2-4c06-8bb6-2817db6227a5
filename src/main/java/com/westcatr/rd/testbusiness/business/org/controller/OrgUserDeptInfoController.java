package com.westcatr.rd.testbusiness.business.org.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgUserDeptInfoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.OrgUserDeptInfoVO;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserDeptInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 *  OrgUserDeptInfo 控制器
 *   <AUTHOR>
 *  @since 2023-06-16
 */
@Validated
@Tag(name = "用户部门信息表接口", description = "用户部门信息表接口", extensions = {@Extension(properties = {@ExtensionProperty(name = "x-order", value = "100")})})
@Slf4j
@RestController
public class OrgUserDeptInfoController {

    @Autowired
    private OrgUserDeptInfoService orgUserDeptInfoService;

    @Operation(summary = "获取用户部门信息表分页数据")
    @PostMapping("/orgUserDeptInfo/page")
    public IResult<IPage<OrgUserDeptInfo>> getOrgUserDeptInfoPage(@RequestBody OrgUserDeptInfoQuery query) {
        return IResult.ok(orgUserDeptInfoService.entityPage(query));
    }

    @Operation(summary = "获取用户部门信息表数据")
    @PostMapping("/orgUserDeptInfo/get")
    public IResult<OrgUserDeptInfo> getOrgUserDeptInfoById(@RequestBody @Id ID id) {
        return IResult.ok(orgUserDeptInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增用户部门信息表数据")
    @PostMapping("/orgUserDeptInfo/add")
    public IResult addOrgUserDeptInfo(@RequestBody @Validated(Insert.class) OrgUserDeptInfo param) {
        return IResult.auto(orgUserDeptInfoService.saveEntity(param));
    }

    @Operation(summary = "更新用户部门信息表数据")
    @PostMapping("/orgUserDeptInfo/update")
    public IResult updateOrgUserDeptInfoById(@RequestBody @Validated(Update.class) OrgUserDeptInfo param) {
        return IResult.auto(orgUserDeptInfoService.updateEntity(param));
    }

    @Operation(summary = "删除用户部门信息表数据")
    @PostMapping("/orgUserDeptInfo/delete")
    public IResult deleteOrgUserDeptInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            orgUserDeptInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取用户部门信息表VO分页数据")
    @PostMapping("/orgUserDeptInfo/voPage")
    public IResult<IPage<OrgUserDeptInfoVO>> getOrgUserDeptInfoVoPage(@RequestBody OrgUserDeptInfoQuery query) {
        AssociationQuery<OrgUserDeptInfoVO> associationQuery = new AssociationQuery<>(OrgUserDeptInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取用户部门信息表VO数据")
    @PostMapping("/orgUserDeptInfo/getVo")
    public IResult<OrgUserDeptInfoVO> getOrgUserDeptInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<OrgUserDeptInfoVO> associationQuery = new AssociationQuery<>(OrgUserDeptInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }


}
