package com.westcatr.rd.testbusiness.business.basics.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.mapper.BuInstrumentCameraInfoMapper;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentCameraInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.BuInstrumentCameraInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentCameraInfoService;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentWorkstationInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BuInstrumentCameraInfoServiceImpl extends ServiceImpl<BuInstrumentCameraInfoMapper, BuInstrumentCameraInfo>
        implements BuInstrumentCameraInfoService {

    @Value("${rstptoflv.host}")
    private String rstpToFltHost;

    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;

    @Autowired
    private BuInstrumentCameraInfoService buInstrumentCameraInfoService;

    @Autowired
    private BuInstrumentWorkstationInfoService buInstrumentWorkstationInfoService;

    @Override
    public IPage<BuInstrumentCameraInfo> entityPage(BuInstrumentCameraInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<BuInstrumentCameraInfo>().create(query));
    }

    @Override
    public BuInstrumentCameraInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(BuInstrumentCameraInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(BuInstrumentCameraInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public String getCameraStreamUrl(Long cameraId) {
        BuInstrumentCameraInfo camera = this.getById(cameraId);
        if (camera == null) {
            return null;
        }
        String rtspUrl = String.format("rtsp://%s:%s@%s:%d/Streaming/Channels/101",
                camera.getUsername(), camera.getPassword(), camera.getIpAddress(), camera.getPort());
        rtspUrl = Base62.encode(rtspUrl.getBytes());
        String flvUrl = String.format("%s%s.flv", rstpToFltHost, rtspUrl);
        return flvUrl;
    }

    @Override
    public List<BuInstrumentCameraInfoVO> getCameraList() {

        List<BuInstrumentCameraInfoVO> monitorData = new ArrayList<>();
        List<JzTaskWorkOrderInfo> jzTaskWorkOrderInfos = jzTaskWorkOrderInfoService
                .list(new LambdaQueryWrapper<>(JzTaskWorkOrderInfo.class).eq(JzTaskWorkOrderInfo::getStatusInfo, "在检"));
        if (CollUtil.isNotEmpty(jzTaskWorkOrderInfos)) {
            List<Long> workIds = jzTaskWorkOrderInfos.stream().map(JzTaskWorkOrderInfo::getWorkstationId)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(workIds)) {
                List<BuInstrumentCameraInfoVO> buInstrumentCameraInfos = new AssociationQuery<>(
                        BuInstrumentCameraInfoVO.class)
                        .voList(new BuInstrumentCameraInfoQuery().setWorkstationIds(workIds));
                // 取前4条
                buInstrumentCameraInfos = buInstrumentCameraInfos.stream().limit(4).collect(Collectors.toList());
                buInstrumentCameraInfos.forEach(x -> {
                    x.setFlvUrl(this.getCameraStreamUrl(x.getId()));
                    if (x.getBuInstrumentWorkstationInfo() != null
                            && StrUtil.isNotEmpty(x.getBuInstrumentWorkstationInfo().getWorkstationCode())) {
                        x.setLargeScreenTitle(x.getInstallationLocation() + "("
                                + x.getBuInstrumentWorkstationInfo().getWorkstationCode() + ")");
                    } else {
                        x.setLargeScreenTitle(x.getInstallationLocation());
                    }

                    x.setTfTesting(true);
                    monitorData.add(x);
                });
            }
        }
        // 小于4条去摄像头表查询
        if (CollUtil.isEmpty(monitorData) || monitorData.size() < 4) {
            int neededCount = 4 - monitorData.size();

            // 过滤掉已经存在的摄像头
            List<Long> existingIds = monitorData.stream()
                    .map(BuInstrumentCameraInfoVO::getId)
                    .collect(Collectors.toList());

            List<BuInstrumentCameraInfoVO> buInstrumentCameraInfos = new AssociationQuery<>(
                    BuInstrumentCameraInfoVO.class)
                    .voList(new LambdaQueryWrapper<>(BuInstrumentCameraInfoVO.class)
                            .notIn(BuInstrumentCameraInfoVO::getId, existingIds));
            if (CollUtil.isEmpty(buInstrumentCameraInfos)) {
                return monitorData;
            }
            // 取 neededCount 条
            buInstrumentCameraInfos = buInstrumentCameraInfos.stream().limit(neededCount).collect(Collectors.toList());
            buInstrumentCameraInfos.forEach(x -> {
                x.setFlvUrl(this.getCameraStreamUrl(x.getId()));
                if (x.getBuInstrumentWorkstationInfo() != null
                        && StrUtil.isNotEmpty(x.getBuInstrumentWorkstationInfo().getWorkstationCode())) {
                    x.setLargeScreenTitle(x.getInstallationLocation() + "("
                            + x.getBuInstrumentWorkstationInfo().getWorkstationCode() + ")");
                } else {
                    x.setLargeScreenTitle(x.getInstallationLocation());
                }

                x.setTfTesting(false);
                x.setLargeScreenTitle(x.getInstallationLocation());
                monitorData.add(x);
            });
        }
        return monitorData;
    }
}