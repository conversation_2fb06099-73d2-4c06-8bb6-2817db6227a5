package com.westcatr.rd.testbusiness.business.workstation.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工作台-历史数据统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="工作台-历史数据统计查询对象")
public class WorkstationHistoryCountQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long id;

    @Schema(description = "样品/任务/工单")
    @QueryCondition
    private String type;

    @Schema(description = "对应状态")
    @QueryCondition
    private String status;

    @Schema(description = "对应编号")
    @QueryCondition
    private String number;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;
}
