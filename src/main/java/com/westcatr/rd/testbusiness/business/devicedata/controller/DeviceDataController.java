package com.westcatr.rd.testbusiness.business.devicedata.controller;

import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataRequest;
import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataResponse;
import com.westcatr.rd.testbusiness.business.devicedata.entity.DeviceDataConfig;
import com.westcatr.rd.testbusiness.business.devicedata.service.DeviceDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备对接数据管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@RestController
@RequestMapping("/device-data")
@Tag(name = "设备对接数据管理", description = "设备对接数据管理相关接口")
public class DeviceDataController {

    @Autowired
    private DeviceDataService deviceDataService;

    /**
     * 查询设备数据
     */
    @GetMapping("/query")
    @Operation(summary = "查询设备数据", description = "根据设备代码和时间范围查询设备数据，时间参数可选，不传时查询最新数据")
    public ResponseEntity<Map<String, Object>> queryDeviceData(
            @Parameter(description = "设备代码", required = true)
            @RequestParam String deviceCode,

            @Parameter(description = "开始时间", required = false)
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,

            @Parameter(description = "结束时间", required = false)
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {

        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建请求对象
            DeviceDataRequest request = new DeviceDataRequest();
            request.setDeviceCode(deviceCode);
            request.setStartTime(startTime);
            request.setEndTime(endTime);

            // 查询设备数据
            List<DeviceDataResponse> data = deviceDataService.getDeviceData(request);

            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            result.put("count", data.size());

            log.info("✅ 设备数据查询成功，设备代码: {}, 返回数据量: {}", deviceCode, data.size());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 设备数据查询失败，设备代码: {}, 错误: {}", deviceCode, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 使用POST方式查询设备数据
     */
    @PostMapping("/query")
    @Operation(summary = "查询设备数据(POST)", description = "使用POST方式根据设备代码和时间范围查询设备数据")
    public ResponseEntity<Map<String, Object>> queryDeviceDataPost(@Valid @RequestBody DeviceDataRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询设备数据
            List<DeviceDataResponse> data = deviceDataService.getDeviceData(request);

            result.put("success", true);
            result.put("message", "查询成功");
            result.put("data", data);
            result.put("count", data.size());

            log.info("✅ 设备数据查询成功，设备代码: {}, 返回数据量: {}", request.getDeviceCode(), data.size());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 设备数据查询失败，设备代码: {}, 错误: {}", request.getDeviceCode(), e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 查询设备配置
     */
    @GetMapping("/config/{deviceCode}")
    @Operation(summary = "查询设备配置", description = "根据设备代码查询设备配置信息")
    public ResponseEntity<Map<String, Object>> getDeviceConfig(
            @Parameter(description = "设备代码", required = true)
            @PathVariable String deviceCode) {

        Map<String, Object> result = new HashMap<>();
        
        try {
            DeviceDataConfig config = deviceDataService.getDeviceConfig(deviceCode);

            if (config != null) {
                result.put("success", true);
                result.put("message", "查询成功");
                result.put("data", config);
            } else {
                result.put("success", false);
                result.put("message", "未找到设备配置");
                result.put("data", null);
            }

            log.info("✅ 设备配置查询完成，设备代码: {}, 结果: {}", deviceCode, config != null ? "找到" : "未找到");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ 设备配置查询失败，设备代码: {}, 错误: {}", deviceCode, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
            return ResponseEntity.badRequest().body(result);
        }
    }
}
