package com.westcatr.rd.testbusiness.business.jztask.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskWorkOrderModelParamQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskWorkOrderModelParamVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderModelParamService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * JzTaskWorkOrderModelParam 控制器
 * 
 * <AUTHOR>
 * @since 2025-04-20
 */
@Validated
@Tag(name = "标准-模型参数表接口", description = "标准-模型参数表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class JzTaskWorkOrderModelParamController {

    @Autowired
    private JzTaskWorkOrderModelParamService jzTaskWorkOrderModelParamService;

    @Operation(summary = "获取标准-模型参数表分页数据")
    @PostMapping("/jzTaskWorkOrderModelParam/page")
    public IResult<IPage<JzTaskWorkOrderModelParam>> getJzTaskWorkOrderModelParamPage(
            @RequestBody JzTaskWorkOrderModelParamQuery query) {
        return IResult.ok(jzTaskWorkOrderModelParamService.entityPage(query));
    }

    @Operation(summary = "获取标准-模型参数表数据")
    @PostMapping("/jzTaskWorkOrderModelParam/get")
    public IResult<JzTaskWorkOrderModelParam> getJzTaskWorkOrderModelParamById(@RequestBody @Id ID id) {
        return IResult.ok(jzTaskWorkOrderModelParamService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增标准-模型参数表数据")
    @PostMapping("/jzTaskWorkOrderModelParam/add")
    public IResult addJzTaskWorkOrderModelParam(@RequestBody @Validated(Insert.class) JzTaskWorkOrderModelParam param) {
        return IResult.auto(jzTaskWorkOrderModelParamService.saveEntity(param));
    }

    @Operation(summary = "更新标准-模型参数表数据")
    @PostMapping("/jzTaskWorkOrderModelParam/update")
    public IResult updateJzTaskWorkOrderModelParamById(
            @RequestBody @Validated(Update.class) JzTaskWorkOrderModelParam param) {
        return IResult.auto(jzTaskWorkOrderModelParamService.updateEntity(param));
    }

    @Operation(summary = "删除标准-模型参数表数据")
    @PostMapping("/jzTaskWorkOrderModelParam/delete")
    public IResult deleteJzTaskWorkOrderModelParamById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            jzTaskWorkOrderModelParamService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取标准-模型参数表VO分页数据")
    @PostMapping("/jzTaskWorkOrderModelParam/voPage")
    public IResult<IPage<JzTaskWorkOrderModelParamVO>> getJzTaskWorkOrderModelParamVoPage(
            @RequestBody JzTaskWorkOrderModelParamQuery query) {
        AssociationQuery<JzTaskWorkOrderModelParamVO> associationQuery = new AssociationQuery<>(
                JzTaskWorkOrderModelParamVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取标准-模型参数表VO数据")
    @PostMapping("/jzTaskWorkOrderModelParam/getVo")
    public IResult<JzTaskWorkOrderModelParamVO> getJzTaskWorkOrderModelParamVoById(@RequestBody @Id ID id) {
        AssociationQuery<JzTaskWorkOrderModelParamVO> associationQuery = new AssociationQuery<>(
                JzTaskWorkOrderModelParamVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
