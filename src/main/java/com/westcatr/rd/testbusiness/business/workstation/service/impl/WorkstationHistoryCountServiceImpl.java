package com.westcatr.rd.testbusiness.business.workstation.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import com.westcatr.rd.testbusiness.business.workstation.mapper.WorkstationHistoryCountMapper;
import com.westcatr.rd.testbusiness.business.workstation.pojo.query.WorkstationHistoryCountQuery;
import com.westcatr.rd.testbusiness.business.workstation.service.WorkstationHistoryCountService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工作台-历史数据统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Service
public class WorkstationHistoryCountServiceImpl extends ServiceImpl<WorkstationHistoryCountMapper, WorkstationHistoryCount> implements WorkstationHistoryCountService {

    @Override
    public IPage<WorkstationHistoryCount> entityPage(WorkstationHistoryCountQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<WorkstationHistoryCount>().create(query));
    }

    @Override
    public WorkstationHistoryCount getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(WorkstationHistoryCount param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(WorkstationHistoryCount param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

