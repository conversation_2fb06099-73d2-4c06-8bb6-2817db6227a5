package com.westcatr.rd.testbusiness.business.datasync.controller;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.testbusiness.business.datasync.service.DataSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 数据同步管理控制器
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@RequestMapping("/dataSyncManagement")
@Tag(name = "数据同步管理", description = "数据同步管理相关接口")
public class DataSyncManagementController {

    @Autowired
    private DataSyncService dataSyncService;

    /**
     * 清理同步缓存，重置同步状态
     * 用于在目标表清空后，重新开始全量同步
     */
    @PostMapping("/clearSyncCache")
    @Operation(summary = "清理同步缓存", description = "清理Redis中的同步时间缓存，用于重新开始全量同步")
    public IResult<Map<String, Object>> clearSyncCache() {
        try {
            log.info("🧹 开始清理数据同步缓存...");
            
            Map<String, Object> result = dataSyncService.clearSyncCache();
            
            log.info("✅ 同步缓存清理完成: {}", result);
            return IResult.ok(result);
            
        } catch (Exception e) {
            log.error("❌ 清理同步缓存失败", e);
            return IResult.fail("清理同步缓存失败: " + e.getMessage());
        }
    }

    /**
     * 强制全量同步
     * 清理缓存并立即执行全量同步
     */
    @PostMapping("/forceFullSync")
    @Operation(summary = "强制全量同步", description = "清理缓存并立即执行全量同步")
    public IResult<Map<String, Object>> forceFullSync() {
        try {
            log.info("🚀 开始强制全量同步...");
            
            // 先清理缓存
            dataSyncService.clearSyncCache();
            
            // 执行同步
            Map<String, Object> result = dataSyncService.syncData();
            
            log.info("✅ 强制全量同步完成: {}", result);
            return IResult.ok(result);
            
        } catch (Exception e) {
            log.error("❌ 强制全量同步失败", e);
            return IResult.fail("强制全量同步失败: " + e.getMessage());
        }
    }
}