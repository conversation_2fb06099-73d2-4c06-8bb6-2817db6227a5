package com.westcatr.rd.testbusiness.business.mqtt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.query.DeviceIntegrationConfigItemQuery;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 设备对接规范条目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface DeviceIntegrationConfigItemService extends IService<DeviceIntegrationConfigItem> {

    IPage<DeviceIntegrationConfigItem> entityPage(DeviceIntegrationConfigItemQuery query);

    DeviceIntegrationConfigItem getEntityById(Long id);

    boolean saveEntity(DeviceIntegrationConfigItem param);

    boolean updateEntity(DeviceIntegrationConfigItem param);

    boolean removeEntityById(Long id);
}
