package com.westcatr.rd.testbusiness.business.jztask.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskWorkOrderModelParamQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;
import com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskWorkOrderModelParamMapper;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderModelParamService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标准-模型参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Service
public class JzTaskWorkOrderModelParamServiceImpl extends ServiceImpl<JzTaskWorkOrderModelParamMapper, JzTaskWorkOrderModelParam> implements JzTaskWorkOrderModelParamService {

    @Override
    public IPage<JzTaskWorkOrderModelParam> entityPage(JzTaskWorkOrderModelParamQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<JzTaskWorkOrderModelParam>().create(query));
    }

    @Override
    public JzTaskWorkOrderModelParam getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(JzTaskWorkOrderModelParam param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(JzTaskWorkOrderModelParam param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

