package com.westcatr.rd.testbusiness.business.basics.constants;

import cn.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * 🔧 设备可检参数相关常量
 * 
 * <AUTHOR>
 * @since 2025-01-14
 */
public class InstrumentParamConstants {

    /**
     * 📋 有效的参数类型选项
     */
    public static final List<String> VALID_PARAM_TYPES = CollUtil.newArrayList(
            "比较型",
            "计算比较依据型", 
            "计算比较结果型",
            "不做判定型",
            "人工判定型",
            "计算型",
            "其他型"
    );

    /**
     * 📋 有效的采集方式选项
     */
    public static final List<String> VALID_COLLECTION_METHODS = CollUtil.newArrayList(
            "直采",
            "填报", 
            "随机生成"
    );

    /**
     * 📏 参数名最大长度
     */
    public static final int PARAM_NAME_MAX_LENGTH = 50;

    /**
     * 📏 参数单位最大长度
     */
    public static final int PARAM_UNIT_MAX_LENGTH = 20;

    /**
     * 🔤 参数名允许的字符正则表达式（中文、英文、数字、下划线、中划线、括号）
     */
    public static final String PARAM_NAME_PATTERN = "^[\\u4e00-\\u9fa5a-zA-Z0-9_\\-()（）]+$";

    /**
     * 📝 获取参数类型选项的字符串描述
     * 
     * @return 参数类型选项字符串
     */
    public static String getParamTypesDescription() {
        return String.join("、", VALID_PARAM_TYPES);
    }

    /**
     * 📝 获取采集方式选项的字符串描述
     * 
     * @return 采集方式选项字符串
     */
    public static String getCollectionMethodsDescription() {
        return String.join("、", VALID_COLLECTION_METHODS);
    }

    /**
     * ✅ 验证参数类型是否有效
     * 
     * @param paramType 参数类型
     * @return 是否有效
     */
    public static boolean isValidParamType(String paramType) {
        return paramType != null && VALID_PARAM_TYPES.contains(paramType.trim());
    }

    /**
     * ✅ 验证采集方式是否有效
     * 
     * @param collectionMethod 采集方式
     * @return 是否有效
     */
    public static boolean isValidCollectionMethod(String collectionMethod) {
        return collectionMethod != null && VALID_COLLECTION_METHODS.contains(collectionMethod.trim());
    }

    /**
     * ✅ 验证参数名格式是否有效
     * 
     * @param paramName 参数名
     * @return 是否有效
     */
    public static boolean isValidParamNameFormat(String paramName) {
        return paramName != null && 
               paramName.trim().length() <= PARAM_NAME_MAX_LENGTH && 
               paramName.trim().matches(PARAM_NAME_PATTERN);
    }

    /**
     * ✅ 验证参数单位格式是否有效
     * 
     * @param unit 参数单位
     * @return 是否有效
     */
    public static boolean isValidUnitFormat(String unit) {
        return unit != null && unit.trim().length() <= PARAM_UNIT_MAX_LENGTH;
    }
}
