package com.westcatr.rd.testbusiness.business.datasync.mapper.postgresql;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.westcatr.rd.testbusiness.business.datasync.entity.LogDeviceReport;

/**
 * <p>
 * 设备报告记录表 Mapper 接口 (PostgreSQL)
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@DS("iot")
@Mapper
public interface PostgreSQLLogDeviceReportMapper {

    /**
     * 查询增量数据
     * 
     * @param lastSyncTime 上次同步时间
     * @param limit 限制条数
     * @return 增量数据列表
     */
    @Select("SELECT id, device_id, product, message_body, create_time " +
            "FROM zzzzz_log_device_report " +
            "WHERE create_time > #{lastSyncTime} " +
            "ORDER BY create_time ASC " +
            "LIMIT #{limit}")
    List<LogDeviceReport> selectIncrementalData(@Param("lastSyncTime") Date lastSyncTime, 
                                               @Param("limit") int limit);

    /**
     * 查询指定时间范围的数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 数据列表
     */
    @Select("SELECT id, device_id, product, message_body, create_time " +
            "FROM zzzzz_log_device_report " +
            "WHERE create_time >= #{startTime} AND create_time <= #{endTime} " +
            "ORDER BY create_time ASC " +
            "LIMIT #{limit}")
    List<LogDeviceReport> selectByTimeRange(@Param("startTime") Date startTime, 
                                           @Param("endTime") Date endTime, 
                                           @Param("limit") int limit);

    /**
     * 统计增量数据数量
     * 
     * @param lastSyncTime 上次同步时间
     * @return 数据数量
     */
    @Select("SELECT COUNT(*) FROM zzzzz_log_device_report " +
            "WHERE create_time > #{lastSyncTime}")
    Long countIncrementalData(@Param("lastSyncTime") Date lastSyncTime);

    /**
     * 获取最大创建时间
     * 
     * @return 最大创建时间
     */
    @Select("SELECT MAX(create_time) FROM zzzzz_log_device_report")
    Date getMaxCreateTime();
}
