package com.westcatr.rd.testbusiness.business.jztask.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import com.westcatr.rd.testbusiness.business.workstation.service.WorkstationHistoryCountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentNewInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentWorkstationInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicProjectParamVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectInstrumentService;
import com.westcatr.rd.testbusiness.business.jzreport.service.JzReportInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskArrangementInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;
import com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskInfoMapper;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskArrangementInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskArrangementInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskArrangementInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInspectionItemInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderModelParamService;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.service.OrgDeptInfoService;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserDeptInfoService;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;
import com.westcatr.rd.testbusiness.business.sample.service.SampleInfoService;

import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 荆州—任务列表表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Service
public class JzTaskInfoServiceImpl extends ServiceImpl<JzTaskInfoMapper, JzTaskInfo> implements JzTaskInfoService {

    @Autowired
    private JzTaskArrangementInfoService jzTaskArrangementInfoService;

    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;

    @Autowired
    private JzTaskInspectionItemInfoService jzTaskInspectionItemInfoService;

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private BuInstrumentWorkstationInfoService buInstrumentWorkstationInfoService;

    @Autowired
    private JzTaskWorkOrderModelParamService jzTaskWorkOrderModelParamService;

    @Lazy
    @Autowired
    private SampleInfoService sampleInfoService;

    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;

    @Autowired
    private OrgUserDeptInfoService orgUserDeptInfoService;

    @Autowired
    private OrgDeptInfoService orgDeptInfoService;

    @Autowired
    private JzTaskInfoService jzTaskInfoService;

    @Autowired
    private JzReportInfoService jzReportInfoService;

    @Autowired
    private StandardBasicProjectInstrumentService standardBasicProjectInstrumentService;

    @Autowired
    private WorkstationHistoryCountService workstationHistoryCountService;

    @Override
    public IPage<JzTaskInfo> entityPage(JzTaskInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<JzTaskInfo>().create(query));
    }

    @Override
    public JzTaskInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(JzTaskInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(JzTaskInfo param) {
        if (this.updateById(param)) {
            if (CollUtil.isNotEmpty(param.getJzTaskArrangementInfoList())) {
                jzTaskArrangementInfoService.remove(new LambdaQueryWrapper<>(JzTaskArrangementInfo.class)
                        .eq(JzTaskArrangementInfo::getTaskId, param.getId()));
                param.getJzTaskArrangementInfoList().forEach(x -> {
                    x.setId(null);
                    x.setTaskId(param.getId());
                });
                jzTaskArrangementInfoService.saveBatch(param.getJzTaskArrangementInfoList());
            }
        }
        return true;
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public IPage<JzTaskInfoVO> myEntityVoPage(JzTaskInfoQuery query) {
        AssociationQuery<JzTaskInfoVO> associationQuery = new AssociationQuery<>(JzTaskInfoVO.class);
        IPage<JzTaskInfoVO> iPage = associationQuery.voPage(query);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                List<JzTaskArrangementInfoVO> jzTaskArrangementInfoLists = new AssociationQuery<>(
                        JzTaskArrangementInfoVO.class)
                        .voList(new JzTaskArrangementInfoQuery().setTaskId(x.getId()));
                jzTaskArrangementInfoLists.forEach(y -> {
                    if (y.getGwBzId() != null) {
                        String[] ids = y.getGwBzId().split(",");
                        List<StandardBasicInstrumentInfo> standardBasicInstrumentInfoList = standardBasicInstrumentInfoService
                                .list(new LambdaQueryWrapper<>(StandardBasicInstrumentInfo.class)
                                        .in(StandardBasicInstrumentInfo::getId, ids));
                        if (CollUtil.isNotEmpty(standardBasicInstrumentInfoList)) {
                            y.setGwBzName(standardBasicInstrumentInfoList.stream()
                                    .map(StandardBasicInstrumentInfo::getProjectName)
                                    .distinct()
                                    .collect(Collectors.joining(",")));
                        }
                    }
                });
                x.setJzTaskArrangementInfoLists(jzTaskArrangementInfoLists);
            });
        }
        return iPage;
    }

    @Override
    public JzTaskInfoVO myEntityVoById(Long id) {
        JzTaskInfoVO jzTaskInfoVO = new AssociationQuery<>(JzTaskInfoVO.class).getVo(id);
        List<JzTaskArrangementInfo> jzTaskArrangementInfoList = jzTaskArrangementInfoService
                .list(new LambdaQueryWrapper<>(JzTaskArrangementInfo.class).eq(JzTaskArrangementInfo::getTaskId, id));
        List<Long> userIds = jzTaskArrangementInfoList.stream().map(JzTaskArrangementInfo::getTestUserId).toList();
        if (CollUtil.isNotEmpty(userIds)) {
            jzTaskInfoVO.setOrgUserInfoLists(orgUserInfoService.listByIds(userIds));
        }
        List<Long> equipmentIds = jzTaskArrangementInfoList.stream().map(JzTaskArrangementInfo::getEquipmentId)
                .toList();
        if (CollUtil.isNotEmpty(equipmentIds)) {
            // List<InstrumentNewInfo> instrumentNewInfo =
            // instrumentNewInfoService.listByIds(equipmentIds);
            AssociationQuery<InstrumentNewInfoVO> associationQuery = new AssociationQuery<>(InstrumentNewInfoVO.class);
            List<InstrumentNewInfoVO> instrumentNewInfo = associationQuery.voList(equipmentIds);
            if (CollUtil.isNotEmpty(instrumentNewInfo)) {
                instrumentNewInfo.forEach(x -> {
                    List<JzTaskWorkOrderInfo> list = jzTaskWorkOrderInfoService
                            .list(new LambdaQueryWrapper<>(JzTaskWorkOrderInfo.class)
                                    .eq(JzTaskWorkOrderInfo::getEquipmentId, x.getId())
                                    .eq(JzTaskWorkOrderInfo::getTaskId, id));
                    if (CollUtil.isNotEmpty(list)) {
                        List<StandardBasicProjectInstrument> info = standardBasicProjectInstrumentService
                                .listByIds(list.stream().map(y -> y.getGwBzId()).toList());
                        if (CollUtil.isNotEmpty(info)) {
                            x.setProName(info.stream().map(z -> z.getProjectName()).distinct().toList());
                        }
                    }
                });
                jzTaskInfoVO.setInstrumentNewInfoLists(instrumentNewInfo);
                List<Long> workstationIds = instrumentNewInfo.stream().map(InstrumentNewInfo::getWorkstationId)
                        .toList();
                jzTaskInfoVO.setBuInstrumentWorkstationInfoLists(
                        buInstrumentWorkstationInfoService.listByIds(workstationIds));
            }
        }
        List<JzTaskWorkOrderInfo> jzTaskWorkOrderInfos = jzTaskWorkOrderInfoService
                .list(new LambdaQueryWrapper<>(JzTaskWorkOrderInfo.class).eq(JzTaskWorkOrderInfo::getTaskId, id));
        if (CollUtil.isNotEmpty(jzTaskWorkOrderInfos)) {
            jzTaskInfoVO.setJzTaskWorkOrderInfoLists(jzTaskWorkOrderInfos);
        }

        // 对人员所属组织进行赋值
        List<OrgUserInfo> orgUserInfoLists = jzTaskInfoVO.getOrgUserInfoLists();
        if (CollUtil.isNotEmpty(orgUserInfoLists)) {
            for (OrgUserInfo orgUserInfo : orgUserInfoLists) {
                List<String> deptNames = new ArrayList<>();
                List<OrgUserDeptInfo> userDeptInfos = orgUserDeptInfoService
                        .list(new LambdaQueryWrapper<OrgUserDeptInfo>()
                                .eq(OrgUserDeptInfo::getUserId, orgUserInfo.getId()));
                if (CollUtil.isNotEmpty(userDeptInfos)) {
                    for (OrgUserDeptInfo userDeptInfo : userDeptInfos) {
                        OrgDeptInfo dept = orgDeptInfoService.getOne(new LambdaQueryWrapper<OrgDeptInfo>()
                                .eq(OrgDeptInfo::getId, userDeptInfo.getDeptId()));
                        if (Objects.nonNull(dept)) {
                            deptNames.add(dept.getDeptName());
                        }
                    }
                }

                orgUserInfo.setDeptNames(deptNames);
            }
        }

        // 对设备信息里面的关联实验赋值对应id
        jzTaskInfoVO.getInstrumentNewInfoLists();

        return jzTaskInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startTask(Long id) {
        // todo 校验排期
        JzTaskInfo jzTaskInfo = this.getById(id);
        List<JzTaskArrangementInfoVO> jzTaskArrangementInfoVOList = new AssociationQuery<>(
                JzTaskArrangementInfoVO.class).voList(new JzTaskArrangementInfoQuery().setTaskId(id));

        AtomicInteger counter = new AtomicInteger(1); // 从1开始
        AtomicInteger counter1 = new AtomicInteger(1); // 从1开始
        jzTaskArrangementInfoVOList.forEach(x -> {
            JzTaskWorkOrderInfo jzTaskWorkOrderInfo = new JzTaskWorkOrderInfo();
            jzTaskWorkOrderInfo.setSampleId(jzTaskInfo.getSampleId());
            jzTaskWorkOrderInfo.setTaskId(id);
            jzTaskWorkOrderInfo.setGwBzName(x.getGwBzName());
            jzTaskWorkOrderInfo.setEquipmentId(x.getEquipmentId());
            // todo 还没做完
            jzTaskWorkOrderInfo.setGwBzId(x.getGwBzId());
            jzTaskWorkOrderInfo.setTestUserId(x.getTestUserId());
            jzTaskWorkOrderInfo.setWorkstationId(x.getWorkstationId());
            // todo 工单编号规则
            jzTaskWorkOrderInfo.setWorkOrderNumber(jzTaskInfo.getTaskNumber() + "-" + counter.getAndIncrement());
            jzTaskWorkOrderInfo.setStatusInfo("待检");
            if (jzTaskWorkOrderInfoService.save(jzTaskWorkOrderInfo)) {
                //新增WorkstationHistoryCount（为工作台统计做数据准备）
                WorkstationHistoryCount workstationHistoryCount = new WorkstationHistoryCount();
                workstationHistoryCount.setStatus(jzTaskWorkOrderInfo.getStatusInfo());
                workstationHistoryCount.setType("工单");
                workstationHistoryCount.setCreateTime(new Date());
                workstationHistoryCount.setNumber(jzTaskWorkOrderInfo.getWorkOrderNumber());

                workstationHistoryCountService.save(workstationHistoryCount);

                String modelIds = x.getModelIds();
                List<StandardBasicProjectParamVO> standardBasicProjectParamVOs = new AssociationQuery<>(
                        StandardBasicProjectParamVO.class)
                        .voList(Arrays.stream(modelIds.split(",")).collect(Collectors.toList()));
                if (CollUtil.isNotEmpty(standardBasicProjectParamVOs)) {
                    standardBasicProjectParamVOs.forEach(z -> {
                        StandardBasicModelParam modelParam = z.getStandardBasicModelParam();

                        if (modelParam == null) {
                            throw new IRuntimeException("【" + z.getParamName() + "】未找到对应的模型参数");
                        }

                        JzTaskInspectionItemInfo jzTaskInspectionItemInfo = new JzTaskInspectionItemInfo();
                        jzTaskInspectionItemInfo.setUnit(modelParam.getUnit());
                        jzTaskInspectionItemInfo.setTestStandard(modelParam.getQualifiedStandard());
                        jzTaskInspectionItemInfo.setParamType(modelParam.getJudgeType());
                        jzTaskInspectionItemInfo.setParamFormula(modelParam.getJudgeFormula());
                        jzTaskInspectionItemInfo.setParamDefinitionValue(modelParam.getParamKey());

                        jzTaskInspectionItemInfo.setSampleId(jzTaskInfo.getSampleId());
                        jzTaskInspectionItemInfo.setTaskId(id);
                        jzTaskInspectionItemInfo.setSourceId(z.getId());
                        jzTaskInspectionItemInfo.setWorkOrderId(jzTaskWorkOrderInfo.getId());
                        jzTaskInspectionItemInfo.setExperimentalProject(z.getParamName());
                        jzTaskInspectionItemInfo.setUserId(x.getTestUserId());
                        jzTaskInspectionItemInfo.setTestName(z.getParamName());
                        jzTaskInspectionItemInfo.setDataCollectionMethod(z.getCollectionMethod());

                        jzTaskInspectionItemInfo.setInspectionItemNumber(
                                jzTaskWorkOrderInfo.getWorkOrderNumber() + "-" + counter1.getAndIncrement());
                        jzTaskInspectionItemInfoService.save(jzTaskInspectionItemInfo);

                        jzTaskWorkOrderModelParamService
                                .update(new LambdaUpdateWrapper<>(JzTaskWorkOrderModelParam.class)
                                        .set(JzTaskWorkOrderModelParam::getOrderId, jzTaskWorkOrderInfo.getId())
                                        .eq(JzTaskWorkOrderModelParam::getTaskId, id)
                                        .eq(JzTaskWorkOrderModelParam::getTestItem, z.getParamName())
                                        .eq(JzTaskWorkOrderModelParam::getUnit, modelParam.getUnit()).last("limit 1"));

                    });
                }
            }
        });
        jzTaskInfo.setStartDate(new Date());
        jzTaskInfo.setTaskStatus("任务在检");
        boolean flag = this.updateById(jzTaskInfo);
        if (flag){
            //新增WorkstationHistoryCount（为工作台统计做数据准备）
            WorkstationHistoryCount workstationHistoryCount = new WorkstationHistoryCount();
            workstationHistoryCount.setStatus(jzTaskInfo.getTaskStatus());
            workstationHistoryCount.setType("任务");
            workstationHistoryCount.setCreateTime(new Date());
            workstationHistoryCount.setNumber(jzTaskInfo.getTaskNumber());

            workstationHistoryCountService.save(workstationHistoryCount);
        }
        return true;
    }

    @Override
    public Map<String, Long> getTaskInfo() {
        Map<String, Long> map = new HashMap<>();
        map.put("sampleNumber", sampleInfoService.count());
        map.put("taskNumber", this.count());
        return map;
    }

    @Override
    public boolean endTask(Long id) {
        long countSize = jzTaskWorkOrderInfoService
                .count(new LambdaQueryWrapper<>(JzTaskWorkOrderInfo.class).ne(JzTaskWorkOrderInfo::getStatusInfo, "检毕")
                        .eq(JzTaskWorkOrderInfo::getTaskId, id));
        if (countSize == 0) {
            JzTaskInfo jzTaskInfo = jzTaskInfoService.getById(id);
            if (jzTaskInfo == null) {
                return true;
            }
            jzTaskInfo.setTaskStatus("任务检毕");
            jzTaskInfo.setEndDate(new Date());
            boolean flag = jzTaskInfoService.updateById(jzTaskInfo);
            if (flag){
                //新增WorkstationHistoryCount（为工作台统计做数据准备）
                WorkstationHistoryCount workstationHistoryCount = new WorkstationHistoryCount();
                workstationHistoryCount.setStatus(jzTaskInfo.getTaskStatus());
                workstationHistoryCount.setType("任务");
                workstationHistoryCount.setCreateTime(new Date());
                workstationHistoryCount.setNumber(jzTaskInfo.getTaskNumber());

                workstationHistoryCountService.save(workstationHistoryCount);
            }
            jzReportInfoService.autoReport(id);
        } else {
            throw new IRuntimeException("该任务还存在关联工单未检闭,无法进行”任务检闭“操作");
        }

        return true;
    }
}
