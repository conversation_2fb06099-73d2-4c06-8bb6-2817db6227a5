<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.org.mapper.OrgDeptInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo">
        <id column="id" property="id" />
        <result column="pid" property="pid" />
        <result column="dept_name" property="deptName" />
        <result column="dept_explain" property="deptExplain" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="executive_user_id" property="executiveUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pid, dept_name, dept_explain, create_time, update_time, executive_user_id
    </sql>

</mapper>
