package com.westcatr.rd.testbusiness.business.basics.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 海康威视摄像头管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_instrument_camera_info")
@Schema(description = "海康威视摄像头管理")
public class BuInstrumentCameraInfo extends Model<BuInstrumentCameraInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "摄像头类型(球机/枪机/半球等)")
    @Length(max = 50, message = "摄像头类型(球机/枪机/半球等)长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("camera_type")
    private String cameraType;

    @Schema(description = "通道号")
    @TableField("channel_num")
    private Integer channelNum;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "设备编号")
    @Length(max = 255, message = "设备编号长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("device_code")
    private String deviceCode;

    @Schema(description = "摄像头型号")
    @Length(max = 255, message = "摄像头型号长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("device_model")
    private String deviceModel;

    @Schema(description = "摄像头名称")
    @Length(max = 255, message = "摄像头名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("device_name")
    private String deviceName;

    @Schema(description = "设备状态")
    @Length(max = 255, message = "设备状态长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("device_status")
    private String deviceStatus;

    @Schema(description = "主键")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "安装位置")
    @Length(max = 255, message = "安装位置长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("installation_location")
    private String installationLocation;

    @Schema(description = "IP地址")
    @Length(max = 255, message = "IP地址长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("ip_address")
    private String ipAddress;

    @Schema(description = "登录密码")
    @Length(max = 255, message = "登录密码长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("password")
    private String password;

    @Schema(description = "端口号")
    @TableField("port")
    private Integer port;

    @Schema(description = "RTSP流地址")
    @Length(max = 512, message = "RTSP流地址长度不能超过512", groups = { Insert.class, Update.class })
    @TableField("rtsp_url")
    private String rtspUrl;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "登录用户名")
    @Length(max = 255, message = "登录用户名长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("username")
    private String username;

    @Schema(description = "工位id")
    @TableField("workstation_id")
    private Long workstationId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
