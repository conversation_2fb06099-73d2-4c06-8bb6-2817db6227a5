package com.westcatr.rd.testbusiness.business.basics.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.boot.orm.entity.JoinInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "VO对象")
public class InstrumentNewInfoVO extends InstrumentNewInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "计时信息（测试子项）")
    @TableField(exist = false)
    private Double workTimeByResult;

    @Schema(description = "计时信息（测试子项历史记录）")
    @TableField(exist = false)
    private Double workTimeByResultHistory;

    @JoinSelect(joinClass = BuInstrumentWorkstationInfo.class, mainId = "workstationId")
    @TableField(exist = false)
    @Schema(description = "工位信息")
    private BuInstrumentWorkstationInfo buInstrumentWorkstationInfo;

    @JoinSelect(joinClass = BuInstrumentWorkstationInfo.class, mainId = "workstationId", field = "workstation_name")
    @TableField(exist = false)
    @Schema(description = "工位信息名称")
    private String workstationName;

    @TableField(exist = false)
    @JoinSelect(joinClass = StandardBasicProjectInstrument.class, relationId = "instrument_id", field = "project_name")
    private List<String> proName;

    /*
     * @TableField(exist = false)
     * 
     * @JoinSelect(joinClass = StandardBasicInstrumentInfo.class, joinCode = 57, and
     * = "join_code = 57", field = "project_name")
     * private List<String> proName;
     * 
     * @TableField(exist = false)
     * 
     * @JoinSelect(joinClass = StandardBasicInstrumentInfo.class, joinCode = 57, and
     * = "join_code = 57")
     * private List<StandardBasicInstrumentInfo> standardBasicInstrumentInfo;
     */

    @TableField(exist = false)
    @JoinSelect(joinClass = JoinInfo.class, relationId = "id1")
    private List<JoinInfo> joinInfos;

    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentUserInfo.class, relationId = "instrument_new_id")
    private List<InstrumentUserInfo> testUsers;

    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentUserInfo.class, relationId = "instrument_new_id", field = "user_full_name")
    private List<String> testUserFullNames;

}
