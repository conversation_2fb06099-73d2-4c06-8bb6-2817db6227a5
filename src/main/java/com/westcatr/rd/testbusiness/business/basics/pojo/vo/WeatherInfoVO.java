package com.westcatr.rd.testbusiness.business.basics.pojo.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 天气信息VO
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@Schema(description = "天气信息")
public class WeatherInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "日期")
    private Date dateTime;

    @Schema(description = "湿度")
    private String humidity;

    @Schema(description = "地点")
    private String location;

    @Schema(description = "大气压")
    private String pressure;

    @Schema(description = "温度")
    private String temperature;

    @Schema(description = "天气状况")
    private String weather;

    @Schema(description = "风向")
    private String windDirection;

    @Schema(description = "风力")
    private String windPower;
}