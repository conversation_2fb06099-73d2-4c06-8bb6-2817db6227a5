package com.westcatr.rd.testbusiness.business.gwstandard.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 🧮 公式计算器 - 用于解析和计算自定义公式
 * 
 * <AUTHOR>
 * @since 2025-06-12
 */
@Slf4j
public class FormulaCalculator {

    /**
     * 🔢 计算平均值
     * 
     * @param values 数值列表
     * @return 平均值
     */
    public static BigDecimal calculateAvg(List<BigDecimal> values) {
        if (CollUtil.isEmpty(values)) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal sum = values.stream()
                .filter(v -> v != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
        return NumberUtil.div(sum, new BigDecimal(values.size()), 6);
    }
    
    /**
     * 📊 解析并计算公式
     * 
     * @param formula 计算公式，例如：avg、(x1+x2+x3)/3、n=(x1+x2+x3)/3*200
     * @param paramValues 参数值映射，key为参数名，value为参数值
     * @return 计算结果
     */
    public static BigDecimal calculate(String formula, Map<String, BigDecimal> paramValues) {
        if (StrUtil.isBlank(formula)) {
            return BigDecimal.ZERO;
        }
        
        // 处理avg特殊函数
        if ("avg".equalsIgnoreCase(formula.trim())) {
            return calculateAvg(CollUtil.newArrayList(paramValues.values()));
        }
        
        // 替换公式中的参数变量为实际值
        String expression = replaceVariables(formula, paramValues);
        
        try {
            // 使用hutool的NumberUtil计算表达式
            return NumberUtil.toBigDecimal(NumberUtil.calculate(expression));
        } catch (Exception e) {
            log.error("计算公式出错: {}, 表达式: {}", e.getMessage(), expression);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 🔄 替换公式中的变量为实际值
     * 
     * @param formula 原始公式
     * @param paramValues 参数值映射
     * @return 替换后的表达式
     */
    private static String replaceVariables(String formula, Map<String, BigDecimal> paramValues) {
        String result = formula;
        
        // 匹配公式中的变量名（字母+数字组合）
        Pattern pattern = Pattern.compile("[a-zA-Z][a-zA-Z0-9]*");
        Matcher matcher = pattern.matcher(formula);
        
        while (matcher.find()) {
            String varName = matcher.group();
            BigDecimal value = paramValues.get(varName);
            
            if (value != null) {
                // 替换变量为实际值
                result = result.replace(varName, value.toString());
            }
        }
        
        return result;
    }
}
