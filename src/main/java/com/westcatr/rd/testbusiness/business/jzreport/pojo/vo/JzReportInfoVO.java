package com.westcatr.rd.testbusiness.business.jzreport.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 国王—报告信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "国王—报告信息表VO对象")
public class JzReportInfoVO extends JzReportInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Schema(description = "原始记录文件信息")
    @JoinSelect(joinClass = FileInfo.class, mainId = "originalRecordFileId")
    private FileInfo originalRecordFileInfo;

    @TableField(exist = false)
    @Schema(description = "报告文件")
    @JoinSelect(joinClass = FileInfo.class, mainId = "reportFileId")
    private FileInfo reportFileInfo;

    @TableField(exist = false)
    @Schema(description = "样品名称")
    @JoinSelect(joinClass = SampleInfo.class, mainId = "sampleId", field = "sample_name")
    private String sampleName;

    @TableField(exist = false)
    @Schema(description = "检测级别")
    @JoinSelect(joinClass = SampleInfo.class, mainId = "sampleId", field = "test_type")
    private String testLevel;

    @TableField(exist = false)
    @Schema(description = "任务编号")
    @JoinSelect(joinClass = JzTaskInfo.class, mainId = "taskId", field = "task_number")
    private String taskNumber;
}
