package com.westcatr.rd.testbusiness.business.datasync.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.testbusiness.business.datasync.service.DataSyncService;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.westcatr.rd.testbusiness.business.datasync.service.DataSyncMonitorService;
import com.westcatr.rd.testbusiness.business.datasync.service.DataSyncRetryService;
import com.westcatr.rd.testbusiness.configs.DataSyncConfig;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据同步控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@RestController
@RequestMapping("/datasync")
@Tag(name = "数据同步管理", description = "数据同步功能的监控和管理接口")
public class DataSyncController {

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private DataSyncMonitorService monitorService;

    @Autowired
    private DataSyncRetryService retryService;

    @Autowired
    private DataSyncConfig dataSyncConfig;

    /**
     * 手动触发数据同步
     */
    @PostMapping("/trigger")
    @Operation(summary = "手动触发数据同步", description = "立即执行一次数据同步任务")
    public ResponseEntity<Map<String, Object>> triggerSync() {
        try {
            log.info("🚀 手动触发数据同步");
            Map<String, Object> result = dataSyncService.syncData();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "数据同步执行完成");
            response.put("data", result);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("❌ 手动同步失败: {}", e.getMessage(), e);

            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "数据同步失败: " + e.getMessage());

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取同步状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取同步状态", description = "查询当前数据同步的状态信息")
    public ResponseEntity<Map<String, Object>> getSyncStatus() {
        try {
            Map<String, Object> status = dataSyncService.getSyncStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("❌ 获取同步状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取同步状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取同步统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取同步统计", description = "查询数据同步的统计信息")
    public ResponseEntity<Map<String, Object>> getSyncStatistics() {
        try {
            Map<String, Object> stats = dataSyncService.getSyncStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("❌ 获取同步统计失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取同步统计失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取数据源连接状态
     */
    @GetMapping("/datasource/status")
    @Operation(summary = "检查数据源状态", description = "检查PostgreSQL、MySQL、Redis的连接状态")
    public ResponseEntity<Map<String, Object>> checkDataSourceStatus() {
        try {
            Map<String, Boolean> status = dataSyncService.checkDataSourceStatus();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", status);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("❌ 检查数据源状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "检查数据源状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 重置同步状态
     */
    @PostMapping("/reset")
    @Operation(summary = "重置同步状态", description = "清除同步状态和统计信息，重新开始同步")
    public ResponseEntity<Map<String, Object>> resetSyncStatus() {
        try {
            dataSyncService.resetSyncStatus();
            log.info("🔄 已重置数据同步状态");
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "同步状态已重置");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("❌ 重置同步状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "重置同步状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    /**
     * 获取同步配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取同步配置", description = "查询当前的数据同步配置参数")
    public ResponseEntity<DataSyncConfig> getSyncConfig() {
        return ResponseEntity.ok(dataSyncConfig);
    }

    /**
     * 获取健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取健康状态", description = "查询数据同步的健康状态")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        try {
            Map<String, Object> health = monitorService.getHealthStatus();
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("❌ 获取健康状态失败: {}", e.getMessage(), e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "获取健康状态失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}
