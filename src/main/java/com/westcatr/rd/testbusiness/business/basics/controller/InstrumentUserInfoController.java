package com.westcatr.rd.testbusiness.business.basics.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.InstrumentUserInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentUserInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentUserInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * InstrumentUserInfo 控制器
 * 
 * <AUTHOR>
 * @since 2025-04-17
 */
@Validated
@Tag(name = "仪器设备—检测人员表接口", description = "仪器设备—检测人员表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class InstrumentUserInfoController {

    @Autowired
    private InstrumentUserInfoService instrumentUserInfoService;

    @Operation(summary = "获取仪器设备—检测人员表分页数据")
    @PostMapping("/instrumentUserInfo/page")
    public IResult<IPage<InstrumentUserInfo>> getInstrumentUserInfoPage(@RequestBody InstrumentUserInfoQuery query) {
        return IResult.ok(instrumentUserInfoService.entityPage(query));
    }

    @Operation(summary = "获取仪器设备—检测人员表数据")
    @PostMapping("/instrumentUserInfo/get")
    public IResult<InstrumentUserInfo> getInstrumentUserInfoById(@RequestBody @Id ID id) {
        return IResult.ok(instrumentUserInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增仪器设备—检测人员表数据")
    @PostMapping("/instrumentUserInfo/add")
    public IResult addInstrumentUserInfo(@RequestBody @Validated(Insert.class) InstrumentUserInfo param) {
        return IResult.auto(instrumentUserInfoService.saveEntity(param));
    }

    @Operation(summary = "更新仪器设备—检测人员表数据")
    @PostMapping("/instrumentUserInfo/update")
    public IResult updateInstrumentUserInfoById(@RequestBody @Validated(Update.class) InstrumentUserInfo param) {
        return IResult.auto(instrumentUserInfoService.updateEntity(param));
    }

    @Operation(summary = "删除仪器设备—检测人员表数据")
    @PostMapping("/instrumentUserInfo/delete")
    public IResult deleteInstrumentUserInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            instrumentUserInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取仪器设备—检测人员表VO分页数据")
    @PostMapping("/instrumentUserInfo/voPage")
    public IResult<IPage<InstrumentUserInfoVO>> getInstrumentUserInfoVoPage(
            @RequestBody InstrumentUserInfoQuery query) {
        AssociationQuery<InstrumentUserInfoVO> associationQuery = new AssociationQuery<>(InstrumentUserInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取仪器设备—检测人员表VO数据")
    @PostMapping("/instrumentUserInfo/getVo")
    public IResult<InstrumentUserInfoVO> getInstrumentUserInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<InstrumentUserInfoVO> associationQuery = new AssociationQuery<>(InstrumentUserInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
