package com.westcatr.rd.testbusiness.business.mqtt.util;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import com.westcatr.rd.testbusiness.business.mqtt.entity.MqttDebugResponse;

/**
 * <p>
 * MQTT调试响应生成器工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public class MqttDebugResponseGenerator {

    /**
     * 生成变压器直阻试验调试响应
     */
    public static MqttDebugResponse generateTransformerDirectResistanceResponse(String deviceId) {
        MqttDebugResponse response = new MqttDebugResponse();
        response.setDeviceId(deviceId);
        response.setDeviceType("变压器");
        response.setMessageId("custom_" + System.currentTimeMillis());
        response.setResponseTime(LocalDateTime.now());
        response.setTableName("DTS_T_RZZLDZCL");
        response.setTimestamp(System.currentTimeMillis());
        response.setDefault(true);
        response.setDescription("变压器直阻试验示例响应");
        response.setPriority(100);
        response.setEnabled(true);

        // 构造原始响应数据
        Map<String, Object> originalResponse = new HashMap<>();
        
        // Header
        Map<String, Object> header = new HashMap<>();
        header.put("deviceId", deviceId);
        header.put("product", "变压器");
        header.put("typeCode", "callFunction");
        header.put("messageId", response.getMessageId());
        
        // Body
        Map<String, Object> body = new HashMap<>();
        body.put("functionName", "getDataByTable");
        body.put("functionIdentifier", "getDataByTable");
        body.put("resultCode", 200);
        body.put("resultMessage", "成功");
        
        // OutputParams
        Map<String, Object> outputParams = new HashMap<>();
        outputParams.put("body", "[{\"A相直流电阻\":\"0.123\",\"B相直流电阻\":\"0.124\",\"C相直流电阻\":\"0.125\",\"温度\":\"25.5\"}]");
        outputParams.put("_status", 200);
        outputParams.put("_msg", "获取数据成功");
        outputParams.put("tableName", "DTS_T_RZZLDZCL");
        
        body.put("outputParams", outputParams);
        
        originalResponse.put("header", header);
        originalResponse.put("body", body);
        
        response.setOriginalResponse(originalResponse);
        response.setResponseContent(outputParams.get("body").toString());
        
        return response;
    }

    /**
     * 生成变压器空载试验调试响应
     */
    public static MqttDebugResponse generateTransformerNoLoadResponse(String deviceId) {
        MqttDebugResponse response = new MqttDebugResponse();
        response.setDeviceId(deviceId);
        response.setDeviceType("变压器");
        response.setMessageId("custom_" + System.currentTimeMillis());
        response.setResponseTime(LocalDateTime.now());
        response.setTableName("DTS_T_KZSY");
        response.setTimestamp(System.currentTimeMillis());
        response.setDefault(true);
        response.setDescription("变压器空载试验示例响应");
        response.setPriority(100);
        response.setEnabled(true);

        // 构造原始响应数据
        Map<String, Object> originalResponse = new HashMap<>();
        
        // Header
        Map<String, Object> header = new HashMap<>();
        header.put("deviceId", deviceId);
        header.put("product", "变压器");
        header.put("typeCode", "callFunction");
        header.put("messageId", response.getMessageId());
        
        // Body
        Map<String, Object> body = new HashMap<>();
        body.put("functionName", "getDataByTable");
        body.put("functionIdentifier", "getDataByTable");
        body.put("resultCode", 200);
        body.put("resultMessage", "成功");
        
        // OutputParams
        Map<String, Object> outputParams = new HashMap<>();
        outputParams.put("body", "[{\"空载电流\":\"0.85\",\"空载损耗\":\"125.6\",\"电压\":\"10500\"}]");
        outputParams.put("_status", 200);
        outputParams.put("_msg", "获取数据成功");
        outputParams.put("tableName", "DTS_T_KZSY");
        
        body.put("outputParams", outputParams);
        
        originalResponse.put("header", header);
        originalResponse.put("body", body);
        
        response.setOriginalResponse(originalResponse);
        response.setResponseContent(outputParams.get("body").toString());
        
        return response;
    }

    /**
     * 生成通用设备调试响应
     */
    public static MqttDebugResponse generateGenericDeviceResponse(String deviceId, String deviceType, 
                                                                String tableName, String responseData) {
        MqttDebugResponse response = new MqttDebugResponse();
        response.setDeviceId(deviceId);
        response.setDeviceType(deviceType);
        response.setMessageId("custom_" + System.currentTimeMillis());
        response.setResponseTime(LocalDateTime.now());
        response.setTableName(tableName);
        response.setTimestamp(System.currentTimeMillis());
        response.setDefault(true);
        response.setDescription(deviceType + "设备示例响应");
        response.setPriority(100);
        response.setEnabled(true);

        // 构造原始响应数据
        Map<String, Object> originalResponse = new HashMap<>();
        
        // Header
        Map<String, Object> header = new HashMap<>();
        header.put("deviceId", deviceId);
        header.put("product", deviceType);
        header.put("typeCode", "callFunction");
        header.put("messageId", response.getMessageId());
        
        // Body
        Map<String, Object> body = new HashMap<>();
        body.put("functionName", "getDataByTable");
        body.put("functionIdentifier", "getDataByTable");
        body.put("resultCode", 200);
        body.put("resultMessage", "成功");
        
        // OutputParams
        Map<String, Object> outputParams = new HashMap<>();
        outputParams.put("body", responseData);
        outputParams.put("_status", 200);
        outputParams.put("_msg", "获取数据成功");
        if (tableName != null) {
            outputParams.put("tableName", tableName);
        }
        
        body.put("outputParams", outputParams);
        
        originalResponse.put("header", header);
        originalResponse.put("body", body);
        
        response.setOriginalResponse(originalResponse);
        response.setResponseContent(responseData);
        
        return response;
    }

    /**
     * 生成错误响应
     */
    public static MqttDebugResponse generateErrorResponse(String deviceId, String deviceType, 
                                                        String errorMessage, int errorCode) {
        MqttDebugResponse response = new MqttDebugResponse();
        response.setDeviceId(deviceId);
        response.setDeviceType(deviceType);
        response.setMessageId("custom_error_" + System.currentTimeMillis());
        response.setResponseTime(LocalDateTime.now());
        response.setTimestamp(System.currentTimeMillis());
        response.setDefault(true);
        response.setDescription("设备错误响应示例");
        response.setPriority(50); // 错误响应优先级较低
        response.setEnabled(true);

        // 构造原始响应数据
        Map<String, Object> originalResponse = new HashMap<>();
        
        // Header
        Map<String, Object> header = new HashMap<>();
        header.put("deviceId", deviceId);
        header.put("product", deviceType);
        header.put("typeCode", "callFunction");
        header.put("messageId", response.getMessageId());
        
        // Body
        Map<String, Object> body = new HashMap<>();
        body.put("functionName", "getDataByTable");
        body.put("functionIdentifier", "getDataByTable");
        body.put("resultCode", errorCode);
        body.put("resultMessage", errorMessage);
        
        // OutputParams (错误情况下可能为空或包含错误信息)
        Map<String, Object> outputParams = new HashMap<>();
        outputParams.put("_status", errorCode);
        outputParams.put("_msg", errorMessage);
        
        body.put("outputParams", outputParams);
        
        originalResponse.put("header", header);
        originalResponse.put("body", body);
        
        response.setOriginalResponse(originalResponse);
        response.setResponseContent(null);
        
        return response;
    }
}
