package com.westcatr.rd.testbusiness.business.gwstandard.pojo.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—检测仪器标准信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "标准—检测仪器标准信息表查询对象")
public class StandardBasicInstrumentInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long id;

    @Schema(description = "集成设备id")
    @QueryCondition
    private Long integratedDeviceId;

    @Schema(description = "集成设备id")
    @QueryCondition(condition = QueryCondition.Condition.IN, field = "integrated_device_id")
    private List<Long> integratedDeviceIds;

    @Schema(description = "项目名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String projectName;

    @Schema(description = "检测能力级别（A\\B\\C）")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String testCapabilityLevel;

    @QueryCondition(condition = QueryCondition.Condition.IN, field = "test_capability_level")
    private List<String> testCapabilityLevels;

    @Schema(description = "仪器名称")
    @QueryCondition
    private String instrumentName;

    @Schema(description = "设备关键参数和要求")
    @QueryCondition
    private String keyParamRequirement;

    @Schema(description = "标准要求")
    @QueryCondition
    private String standardRequirement;

    @Schema(description = "型号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String sampleSpecifications;

    // 样品名称
    @Schema(description = "样品名称")
    @QueryCondition
    private String sampleName;

    @Schema(description = "样品模型")
    @QueryCondition
    private String sampleModel;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;
}
