package com.westcatr.rd.testbusiness.business.sample.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 样品——基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_sample_info")
@Schema(description = "样品——基本信息表")
public class SampleInfo extends Model<SampleInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "样品编号")
    @Length(max = 50, message = "样品编号长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("sample_number")
    private String sampleNumber;

    @ExcelProperty("样品名称")
    @Schema(description = "样品名称")
    @Length(max = 100, message = "样品名称长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("sample_name")
    private String sampleName;

    @ExcelProperty("样品规格")
    @Schema(description = "样品规格")
    @Length(max = 100, message = "样品型号长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("sample_model")
    private String sampleModel;

    @ExcelProperty("样品型号")
    @Schema(description = "样品型号")
    @TableField("jz_sample_model")
    private String jzSampleModel;

    @ExcelProperty("样品所属专业")
    @Schema(description = "样品所属专业")
    @TableField("sample_profession")
    private String sampleProfession;

    @ExcelProperty("收样时间")
    @Schema(description = "收样时间")
    @TableField("sample_receipt_time")
    private Date sampleReceiptTime;

    @Schema(description = "样品备注")
    @Length(max = 255, message = "样品备注长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sample_remark")
    private String sampleRemark;

    @Schema(description = "委托类型")
    @TableField(exist = false)
    private Integer entrustType;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private Integer serialNumber;

    @Schema(description = "盲样编号")
    @Length(max = 50, message = "盲样编号长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("blind_sample_number")
    private String blindSampleNumber;

    @Schema(description = "二次盲样编号")
    @Length(max = 50, message = "二次盲样编号长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("secondary_blind_sample_number")
    private String secondaryBlindSampleNumber;

    @Schema(description = "样品所属专业")
    @Length(max = 100, message = "样品所属专业长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("sample_major")
    private String sampleMajor;

    @Schema(description = "检测类型")
    @Length(max = 100, message = "检测类型长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("test_type")
    private String testType;

    @Schema(description = "任务状态")
    @Length(max = 100, message = "任务状态长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("task_status")
    private String taskStatus;

    @Schema(description = "报告状态")
    @Length(max = 100, message = "报告状态长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("report_status")
    private String reportStatus;

    @TableField("nameplate_file_id")
    @Schema(description = "样品铭牌图片id")
    private Long nameplateFileId;

    // sample_pic_id
    @TableField("sample_pic_id")
    @Schema(description = "样品图片id")
    private Long samplePicId;

    @Schema(description = "样品状态")
    @Length(max = 100, message = "样品状态长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("status")
    private String status;

    @Schema(description = "返样状态")
    @Length(max = 100, message = "返样状态长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("back_status")
    private String backStatus;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
