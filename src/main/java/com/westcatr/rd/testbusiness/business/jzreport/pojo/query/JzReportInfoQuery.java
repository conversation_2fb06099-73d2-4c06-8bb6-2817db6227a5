package com.westcatr.rd.testbusiness.business.jzreport.pojo.query;

import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 国王—报告信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="国王—报告信息表查询对象")
public class JzReportInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Long id;

    @Schema(description = "原始记录文件id")
    @QueryCondition
    private Long originalRecordFileId;

    @Schema(description = "报告文件id")
    @QueryCondition
    private Long reportFileId;

    @Schema(description = "报告编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String reportNumber;

    @Schema(description = "样品id")
    @QueryCondition
    private Long sampleId;

    @Schema(description = "任务id")
    @QueryCondition
    private Long taskId;

    @Schema(description = "是否合格")
    @QueryCondition
    private String tfQualified;

    @QueryCondition
    private Date updateTime;

    @Schema(description = "样品名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE,field = "bsi.sample_name")
    @JoinExpression(value = "inner join bu_sample_info bsi on bsi.id=@m.sample_id")
    private String sampleName;

    @Schema(description = "检测级别")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE,field = "bsi.test_type")
    @JoinExpression(value = "inner join bu_sample_info bsi on bsi.id=@m.sample_id")
    private String testLevel;

    @Schema(description = "任务编号")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE,field = "jti.task_number")
    @JoinExpression(value = "inner join jz_task_info jti on jti.id=@m.task_id")
    private String taskNumber;
}
