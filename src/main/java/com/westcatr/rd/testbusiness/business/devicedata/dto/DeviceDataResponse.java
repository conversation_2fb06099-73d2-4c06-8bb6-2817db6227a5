package com.westcatr.rd.testbusiness.business.devicedata.dto;

import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备数据响应
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@Schema(description = "设备数据响应")
public class DeviceDataResponse {

    @Schema(description = "试验名称")
    private String name;

    @Schema(description = "试验代码")
    private String code;

    @Schema(description = "数据创建时间")
    private Date dataTime;

    @Schema(description = "试验结果")
    private List<TestResult> result;

    public DeviceDataResponse() {
    }

    public DeviceDataResponse(String name, String code, Date dataTime, List<TestResult> result) {
        this.name = name;
        this.code = code;
        this.dataTime = dataTime;
        this.result = result;
    }

    @Data
    @Schema(description = "测试结果")
    public static class TestResult {
        @Schema(description = "参数名称")
        private String name;

        @Schema(description = "参数代码")
        private String code;

        @Schema(description = "参数值")
        private String value;

        public TestResult() {
        }

        public TestResult(String name, String code, String value) {
            this.name = name;
            this.code = code;
            this.value = value;
        }
    }
}
