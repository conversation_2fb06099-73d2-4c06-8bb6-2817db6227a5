/**
 * MQTT客户端示例
 * 用于连接物联网系统，实现消息发布和订阅
 *
 * 通信模式：
 * - 下行主题（/jcxt/link/down/edgeGateway/edgeTopic）：只发送指令，不监听
 * - 上行主题（/jcxt/link/up/edgeGateway/edgeTopic）：只监听结果，不发送
 *
 * 适配设备：
 * 1. 思创检测设备
 * 2. 万测变压器
 * 3. 电机设备
 * 4. 10T电子万能试验机
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
package com.westcatr.rd.testbusiness.business.mqtt;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MqttClientDemo {
    static {
        // 设置系统属性，在终端中启用ANSI颜色
        System.setProperty("jansi.passthrough", "true");
    }

    // MQTT服务器地址
    private static final String BROKER_URL = "tcp://48kkod342786.vicp.fun:39266";
    // 客户端ID，需要保证唯一性
    private static final String CLIENT_ID = "jz_westcatr_demo";
    // 可选的用户名和密码
    private static final String USERNAME = null; // 如果需要认证，设置为实际的用户名
    private static final String PASSWORD = null; // 如果需要认证，设置为实际的密码
    // 下行主题（只发送指令，不监听）
    private static final String DOWN_TOPIC = "/jcxt/link/down/edgeGateway/edgeTopic";
    // 上行主题（只监听结果，不发送）
    private static final String UP_TOPIC = "/jcxt/link/up/edgeGateway/edgeTopic";
    // QoS级别，0表示最多发送一次，1表示至少发送一次，2表示只发送一次
    private static final int QOS = 1;

    // 设备和产品相关常量
    private static final String DEVICE_SICHUANG = "sichuang";
    private static final String PRODUCT_SICHUANG = "sichuangDevice";
    private static final String DEVICE_WANCE = "wanceDevice";
    private static final String PRODUCT_ACCESSID = "accessId";

    // 电机设备常量
    private static final String DEVICE_ELE_MACHINE = "eleMachine51";
    private static final String PRODUCT_ELE_MACHINE = "eleMachine";

    // 10T电子万能试验机常量
    private static final String DEVICE_ELECTRONIC_UTM = "electronicUTM10T";
    private static final String PRODUCT_ELECTRONIC_UTM = "electronicUTM";

    // 绝缘子机电破坏试验机常量
    private static final String DEVICE_JYZ_JDPH = "jyaDevice";
    private static final String PRODUCT_JYZ_JDPH = "jyzjdph";

    // 维卡万能试验机常量
    private static final String DEVICE_WK_UTM = "wkDevice";
    private static final String PRODUCT_WK_UTM = "wkProduct";

    private MqttClient mqttClient;
    private MqttConnectOptions options;
    // 存储最近发送的消息ID，用于过滤自己发送的消息
    private String lastSentMessageId = null;

    // 保存最后一次接收到的响应体
    private String lastResponseBody;

    /**
     * 初始化MQTT客户端
     */
    public void init() {
        try {
            log.info("开始初始化MQTT客户端，服务器地址: {}, 客户端ID: {}", BROKER_URL, CLIENT_ID);

            // 创建MQTT客户端，使用内存持久化
            mqttClient = new MqttClient(BROKER_URL, CLIENT_ID, new MemoryPersistence());

            // 设置连接选项
            options = new MqttConnectOptions();
            // 设置是否清除会话
            options.setCleanSession(true);
            // 设置超时时间，单位为秒
            options.setConnectionTimeout(60); // 增加超时时间
            // 设置心跳间隔，单位为秒
            options.setKeepAliveInterval(60); // 增加心跳间隔
            // 设置自动重连
            options.setAutomaticReconnect(true);
            // 设置最大重连间隔，单位为秒
            options.setMaxReconnectDelay(5000);

            // 如果需要认证，设置用户名和密码
            if (USERNAME != null && PASSWORD != null) {
                log.info("设置MQTT连接认证信息");
                options.setUserName(USERNAME);
                options.setPassword(PASSWORD.toCharArray());
            }

            // 设置遗嘱消息（可选）
            // String willTopic = "clients/lastWill";
            // String willPayload = CLIENT_ID + " 异常断开";
            // options.setWill(willTopic, willPayload.getBytes(), 1, false);

            // 设置回调函数
            mqttClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.error("MQTT连接丢失: {}", cause.getMessage());
                    // 尝试重新连接
                    try {
                        log.info("尝试重新连接到MQTT服务器...");
                        mqttClient.connect(options);
                        if (mqttClient.isConnected()) {
                            log.info("重新连接成功");
                            // 重新订阅主题
                            mqttClient.subscribe(UP_TOPIC, QOS);
                            log.info("重新订阅主题: {}", UP_TOPIC);
                        }
                    } catch (MqttException e) {
                        log.error("重新连接失败: {}", e.getMessage(), e);
                    }
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
                    // log.info("接收到来自主题[{}]的消息: {}", topic, payload);

                    // 处理接收到的消息
                    if (UP_TOPIC.equals(topic)) {
                        handleUpMessage(payload);
                    }
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    log.info("消息发送完成");
                }
            });

            log.info("正在连接到MQTT服务器: {}", BROKER_URL);

            try {
                // 连接MQTT服务器
                mqttClient.connect(options);

                if (mqttClient.isConnected()) {
                    log.info("MQTT服务器连接成功");

                    // 订阅主题
                    mqttClient.subscribe(UP_TOPIC, QOS);
                    log.info("已订阅上行主题: {}", UP_TOPIC);

                    log.info("MQTT客户端初始化成功，已连接到: {}", BROKER_URL);
                } else {
                    log.error("MQTT服务器连接失败，但未抛出异常");
                }
            } catch (MqttException e) {
                log.error("MQTT服务器连接失败: {}", e.getMessage(), e);
                throw e; // 重新抛出异常以便上层处理
            }

        } catch (MqttException e) {
            log.error("MQTT客户端初始化失败: {}", e.getMessage(), e);
            throw new RuntimeException("MQTT客户端初始化失败", e);
        }
    }

    /**
     * 处理上行消息（从上行主题接收的设备响应消息）
     *
     * @param message 消息内容
     */
    private void handleUpMessage(String message) {
        try {
            if (StrUtil.isBlank(message)) {
                log.warn("接收到空消息");
                return;
            }

            log.info("✨✨✨✨✨接收到上行消息: {}", message);

            // 解析JSON消息
            @SuppressWarnings("unchecked")
            Map<String, Object> msgMap = JSONUtil.toBean(message, HashMap.class);
            // log.info("解析接收到的消息: {}", msgMap);

            // 获取header和body
            @SuppressWarnings("unchecked")
            Map<String, Object> header = (Map<String, Object>) msgMap.get("header");
            @SuppressWarnings("unchecked")
            Map<String, Object> body = (Map<String, Object>) msgMap.get("body");

            if (header == null || body == null) {
                log.warn("消息格式不正确，缺少header或body");
                return;
            }

            // 获取消息类型
            String deviceId = (String) header.get("deviceId");
            String product = (String) header.get("product");
            // 兼容两种消息类型字段：typeCode 和 messageType
            String typeCode = (String) header.get("typeCode");
            String messageType = (String) header.get("messageType");

            // 如果 typeCode 为空，但 messageType 不为空，则使用 messageType
            if (typeCode == null && messageType != null) {
                typeCode = messageType;
                log.info("使用messageType作为消息类型: {}", typeCode);
            }

            // 修复：messageId可能是Long类型，需要转换为String
            Object messageIdObj = header.get("messageId");
            String messageId = messageIdObj != null ? String.valueOf(messageIdObj) : null;

            // 检查是否是自己发送的消息
            if (messageId != null && messageId.equals(lastSentMessageId)) {
                // 如果消息类型是callFunction，即使是自己发送的消息也需要处理
                if ("callFunction".equals(typeCode)) {
                    log.info("收到自己发送的消息(ID: {})，但因为是callFunction类型，继续处理", messageId);
                } else {
                    log.info("收到自己发送的消息(ID: {})，忽略处理", messageId);
                    return;
                }
            }

            log.info("处理消息 - 设备ID: {}, 产品: {}, 类型: {}, 消息ID: {}",
                    deviceId, product, typeCode, messageId);

            // 根据设备和消息类型处理不同的业务逻辑
            if (PRODUCT_ACCESSID.equals(product)) {
                // 处理万测变压器数据响应
                if ("getAllData".equals(typeCode)) {
                    log.info("接收到万测变压器全部数据响应");
                    // 处理全部数据响应
                    handleWanceAllDataResponse(body);
                } else if ("getDataByTable".equals(typeCode)) {
                    log.info("接收到万测变压器特定表数据响应");
                    // 处理特定表数据响应
                    handleWanceTableDataResponse(body);
                } else if ("callFunction".equals(typeCode)) {
                    log.info("接收到调用函数响应消息");
                    // 处理函数调用响应
                    handleFunctionCallResponse(body);
                }
            } else if (PRODUCT_SICHUANG.equals(product)) {
                // 处理思创设备数据响应
                log.info("接收到思创设备数据响应");
                // 原有的思创设备响应处理逻辑
            } else if (PRODUCT_ELE_MACHINE.equals(product)) {
                // 处理电机设备数据响应
                log.info("接收到电机设备数据响应");
                // 重点打印outputParams部分
                if (body.containsKey("outputParams")) {
                    Object outputParamsObj = body.get("outputParams");
                    log.info("\n======================outputParams开始======================\n");
                    log.info("outputParams: {}", JSONUtil.toJsonPrettyStr(outputParamsObj));

                    // 如果是一个Map对象
                    if (outputParamsObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> outputParams = (Map<String, Object>) outputParamsObj;

                        // 如果有body字段，尝试解析并格式化打印
                        if (outputParams.containsKey("body")) {
                            String bodyStr = String.valueOf(outputParams.get("body"));
                            try {
                                // 尝试解析body字符串为JSON
                                @SuppressWarnings("unchecked")
                                Map<String, Object> bodyMap = JSONUtil.toBean(bodyStr, HashMap.class);
                                log.info("\n======================body内容开始======================\n");
                                log.info("body解析结果: {}", JSONUtil.toJsonPrettyStr(bodyMap));

                                // 如果有content字段，单独打印
                                if (bodyMap.containsKey("content")) {
                                    String content = String.valueOf(bodyMap.get("content"));
                                    log.info("\n======================content内容开始======================\n");
                                    log.info("content:\n{}", content);
                                    log.info("\n======================content内容结束======================\n");
                                }

                                log.info("\n======================body内容结束======================\n");
                            } catch (Exception e) {
                                log.warn("解析body字符串失败: {}", e.getMessage());
                                log.info("body原始内容: {}", bodyStr);
                            }
                        }
                    } else {
                        log.info("outputParams不是Map类型，原始类型: {}",
                                outputParamsObj != null ? outputParamsObj.getClass().getName() : "null");
                    }

                    log.info("\n======================outputParams结束======================\n");
                } else {
                    log.info("消息中没有outputParams字段");
                }
            } else if (PRODUCT_ELECTRONIC_UTM.equals(product)) {
                // 处琇10T电子万能试验机数据响应
                log.info("接收到 10T电子万能试验机 数据响应");
                // 重点打印outputParams部分
                if (body.containsKey("outputParams")) {
                    Object outputParamsObj = body.get("outputParams");
                    log.info("\n======================10T电子万能试验机 outputParams开始======================\n");
                    log.info("outputParams: {}", JSONUtil.toJsonPrettyStr(outputParamsObj));

                    // 如果是一个Map对象
                    if (outputParamsObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> outputParams = (Map<String, Object>) outputParamsObj;

                        // 如果有body字段，尝试解析并格式化打印
                        if (outputParams.containsKey("body")) {
                            String bodyStr = String.valueOf(outputParams.get("body"));
                            try {
                                // 尝试解析body字符串为JSON
                                @SuppressWarnings("unchecked")
                                Map<String, Object> bodyMap = JSONUtil.toBean(bodyStr, HashMap.class);
                                log.info("\n======================10T电子万能试验机 body内容开始======================\n");
                                log.info("body解析结果: {}", JSONUtil.toJsonPrettyStr(bodyMap));

                                // 如果有content字段，单独打印
                                if (bodyMap.containsKey("content")) {
                                    String content = String.valueOf(bodyMap.get("content"));
                                    log.info("\n======================10T电子万能试验机 content内容开始======================\n");
                                    log.info("content:\n{}", content);
                                    log.info("\n======================10T电子万能试验机 content内容结束======================\n");
                                }

                                log.info("\n======================10T电子万能试验机 body内容结束======================\n");
                            } catch (Exception e) {
                                log.warn("解析body字符串失败: {}", e.getMessage());
                                log.info("body原始内容: {}", bodyStr);
                            }
                        }
                    } else {
                        log.info("outputParams不是Map类型，原始类型: {}",
                                outputParamsObj != null ? outputParamsObj.getClass().getName() : "null");
                    }

                    log.info("\n======================10T电子万能试验机 outputParams结束======================\n");
                } else {
                    log.info("消息中没有outputParams字段");
                }
            } else if (PRODUCT_JYZ_JDPH.equals(product)) {
                // 处理绝缘子机电破坏试验机数据响应
                log.info("接收到绝缘子机电破坏试验机数据响应");
                // 打印响应内容
                log.info("\n======================绝缘子机电破坏试验机响应内容======================\n");
                log.info("响应体: {}", JSONUtil.toJsonPrettyStr(body));
                log.info("\n======================绝缘子机电破坏试验机响应结束======================\n");

                // 重点打印outputParams部分
                if (body.containsKey("outputParams")) {
                    Object outputParamsObj = body.get("outputParams");
                    log.info("\n======================绝缘子机电破坏试验机 outputParams开始======================\n");
                    log.info("outputParams: {}", JSONUtil.toJsonPrettyStr(outputParamsObj));

                    // 如果是一个Map对象
                    if (outputParamsObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> outputParams = (Map<String, Object>) outputParamsObj;

                        // 如果有body字段，尝试解析并格式化打印
                        if (outputParams.containsKey("body")) {
                            String bodyStr = String.valueOf(outputParams.get("body"));
                            try {
                                // 尝试解析body字符串为JSON
                                @SuppressWarnings("unchecked")
                                Map<String, Object> bodyMap = JSONUtil.toBean(bodyStr, HashMap.class);
                                log.info("\n======================绝缘子机电破坏试验机 body内容开始======================\n");
                                log.info("body解析结果: {}", JSONUtil.toJsonPrettyStr(bodyMap));

                                // 如果有content字段，单独打印
                                if (bodyMap.containsKey("content")) {
                                    String content = String.valueOf(bodyMap.get("content"));
                                    log.info("\n======================绝缘子机电破坏试验机 content内容开始======================\n");
                                    log.info("content:\n{}", content);
                                    log.info("\n======================绝缘子机电破坏试验机 content内容结束======================\n");
                                }

                                log.info("\n======================绝缘子机电破坏试验机 body内容结束======================\n");
                            } catch (Exception e) {
                                log.warn("解析body字符串失败: {}", e.getMessage());
                                log.info("body原始内容: {}", bodyStr);
                            }
                        }
                    } else {
                        log.info("outputParams不是Map类型，原始类型: {}",
                                outputParamsObj != null ? outputParamsObj.getClass().getName() : "null");
                    }

                    log.info("\n======================绝缘子机电破坏试验机 outputParams结束======================\n");
                } else {
                    log.info("消息中没有outputParams字段");
                }
            } else if (PRODUCT_WK_UTM.equals(product)) {
                // 处理维卡万能试验机数据响应
                log.info("接收到维卡万能试验机数据响应");
                // 打印响应内容
                log.info("\n======================维卡万能试验机响应内容======================\n");
                log.info("响应体: {}", JSONUtil.toJsonPrettyStr(body));
                log.info("\n======================维卡万能试验机响应结束======================\n");

                // 重点打印outputParams部分
                if (body.containsKey("outputParams")) {
                    Object outputParamsObj = body.get("outputParams");
                    log.info("\n======================维卡万能试验机 outputParams开始======================\n");
                    log.info("outputParams: {}", JSONUtil.toJsonPrettyStr(outputParamsObj));

                    // 如果是一个Map对象
                    if (outputParamsObj instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> outputParams = (Map<String, Object>) outputParamsObj;

                        // 如果有body字段，尝试解析并格式化打印
                        if (outputParams.containsKey("body")) {
                            String bodyStr = String.valueOf(outputParams.get("body"));
                            try {
                                // 尝试解析body字符串为JSON
                                @SuppressWarnings("unchecked")
                                Map<String, Object> bodyMap = JSONUtil.toBean(bodyStr, HashMap.class);
                                log.info("\n======================维卡万能试验机 body内容开始======================\n");
                                log.info("body解析结果: {}", JSONUtil.toJsonPrettyStr(bodyMap));

                                // 如果有content字段，单独打印
                                if (bodyMap.containsKey("content")) {
                                    String content = String.valueOf(bodyMap.get("content"));
                                    log.info("\n======================维卡万能试验机 content内容开始======================\n");
                                    log.info("content:\n{}", content);
                                    log.info("\n======================维卡万能试验机 content内容结束======================\n");
                                }

                                log.info("\n======================维卡万能试验机 body内容结束======================\n");
                            } catch (Exception e) {
                                log.warn("解析body字符串失败: {}", e.getMessage());
                                log.info("body原始内容: {}", bodyStr);
                            }
                        }
                    } else {
                        log.info("outputParams不是Map类型，原始类型: {}",
                                outputParamsObj != null ? outputParamsObj.getClass().getName() : "null");
                    }

                    log.info("\n======================维卡万能试验机 outputParams结束======================\n");
                } else {
                    log.info("消息中没有outputParams字段");
                }
            }

            // 构造通用响应消息
            Map<String, Object> responseMap = new HashMap<>();
            Map<String, Object> respHeader = new HashMap<>();
            Map<String, Object> respBody = new HashMap<>();

            // 设置响应header
            respHeader.put("deviceId", deviceId);
            respHeader.put("product", product);
            respHeader.put("typeCode", typeCode + "_response");
            respHeader.put("messageId", String.valueOf(System.currentTimeMillis()));

            // 设置响应body
            respBody.put("status", "success");
            respBody.put("message", "消息已处理");
            respBody.put("timestamp", System.currentTimeMillis());
            respBody.put("originalMessageId", messageId);

            responseMap.put("header", respHeader);
            responseMap.put("body", respBody);

            // 我们不需要对设备响应再发送响应
            // 只记录日志表示已处理
            String responseJson = JSONUtil.toJsonStr(responseMap);
            log.info("已处理设备响应消息: {}", responseJson);

            // 保存最后一次接收到的响应体
            lastResponseBody = JSONUtil.toJsonStr(body);

        } catch (Exception e) {
            log.error("处理上行消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理万测变压器全部数据响应
     *
     * @param body 响应消息体
     */
    private void handleWanceAllDataResponse(Map<String, Object> body) {
        try {
            String type = (String) body.get("type");
            log.info("万测变压器全部数据类型: {}", type);

            // 根据类型进行不同处理，目前示例中type为"1"表示模拟值/固定的
            if ("1".equals(type)) {
                log.info("数据类型为模拟值/固定的");
                // 在这里添加具体的业务处理逻辑
            } else {
                log.info("未知的数据类型: {}", type);
            }
        } catch (Exception e) {
            log.error("处理万测变压器全部数据响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理万测变压器特定表数据响应
     *
     * @param body 响应消息体
     */
    private void handleWanceTableDataResponse(Map<String, Object> body) {
        try {
            String tableName = (String) body.get("tableName");
            String resultNum = String.valueOf(body.get("resultNum"));
            String beginTime = (String) body.get("beginTime");
            String endTime = (String) body.get("endTime");

            log.info("万测变压器表名: {}, 结果数量: {}, 时间范围: {} 至 {}",
                    tableName, resultNum, beginTime, endTime);

            // 在这里添加针对特定表数据的处理逻辑
            // 例如，可以根据不同的表名采取不同的处理措施

            // 示例：保存结果到数据库或其他后续处理
            log.info("表 {} 的数据已处理完成", tableName);
        } catch (Exception e) {
            log.error("处理万测变压器特定表数据响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理函数调用响应消息
     *
     * @param body 响应消息体
     */
    private void handleFunctionCallResponse(Map<String, Object> body) {
        try {
            log.info("开始处理函数调用响应");

            // 提取重要字段
            String functionName = (String) body.get("functionName");
            String functionIdentifier = (String) body.get("functionIdentifier");
            String resultMessage = (String) body.get("resultMessage");
            Object resultCode = body.get("resultCode");

            log.info("函数名称: {}, 函数标识符: {}, 结果代码: {}, 结果消息: {}",
                    functionName, functionIdentifier, resultCode, resultMessage);

            // 处理输入参数
            @SuppressWarnings("unchecked")
            Map<String, Object> inputParams = (Map<String, Object>) body.get("inputParams");
            if (inputParams != null) {
                log.info("输入参数: {}", JSONUtil.toJsonPrettyStr(inputParams));
            }

            // 重点处理输出参数
            Object outputParamsObj = body.get("outputParams");
            if (outputParamsObj != null) {
                log.info("\n======================outputParams开始======================\n");
                log.info("outputParams: {}", JSONUtil.toJsonPrettyStr(outputParamsObj));

                // 如果是一个Map对象
                if (outputParamsObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> outputParams = (Map<String, Object>) outputParamsObj;

                    // 如果有body字段，尝试解析并格式化打印
                    if (outputParams.containsKey("body")) {
                        Object bodyObj = outputParams.get("body");
                        String bodyStr = String.valueOf(bodyObj);
                        log.info("\n======================body内容开始======================\n");

                        try {
                            // 尝试解析body字符串为JSON
                            Object bodyParsed = JSONUtil.parse(bodyStr);
                            String prettyBody = JSONUtil.toJsonPrettyStr(bodyParsed);
                            log.info("body解析结果: {}", prettyBody);

                            // 保存最后一次响应体
                            this.lastResponseBody = bodyStr;
                        } catch (Exception e) {
                            log.warn("解析body字符串失败: {}", e.getMessage());
                            log.info("body原始内容: {}", bodyStr);
                        }

                        log.info("\n======================body内容结束======================\n");
                    }
                } else {
                    log.info("outputParams不是Map类型，原始类型: {}",
                            outputParamsObj != null ? outputParamsObj.getClass().getName() : "null");
                }

                log.info("\n======================outputParams结束======================\n");
            } else {
                log.info("消息中没有outputParams字段");
            }

            log.info("函数调用响应处理完成");

        } catch (Exception e) {
            log.error("处理函数调用响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发布消息
     *
     * @param topic   主题
     * @param payload 消息内容
     */
    public void publishMessage(String topic, String payload) {
        try {
            if (mqttClient == null || !mqttClient.isConnected()) {
                log.error("MQTT客户端未连接");
                return;
            }

            MqttMessage message = new MqttMessage(payload.getBytes(StandardCharsets.UTF_8));
            message.setQos(QOS);

            mqttClient.publish(topic, message);
            log.info("消息已发布到主题: {}", topic);

        } catch (MqttException e) {
            log.error("消息发布失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送思创检测请求示例
     */
    public void sendSichuangTestRequest() {
        try {
            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 使用固定的JSON字符串，确保完全匹配用户提供的格式，但使用动态的消息ID
            String jsonMessage = "{\n" +
                    "  \"header\":{\n" +
                    "    \"deviceId\":\"" + DEVICE_SICHUANG + "\",\n" +
                    "    \"product\": \"" + PRODUCT_SICHUANG + "\",\n" +
                    "    \"typeCode\": \"getData\",\n" +
                    "    \"messageId\": \"" + messageId + "\"\n" +
                    "  },\n" +
                    "  \"body\":{\n" +
                    "    \"encodingcode\":\"20250304163316334536\",\n" +
                    "    \"items\":\"E006,E007\",\n" +
                    "    \"devicename\":\"JP柜\",\n" +
                    "    \"categoryname\":\"JP柜\",\n" +
                    "    \"testcentercode\":\"2019061900049522\",\n" +
                    "    \"workstationName\":\"A1\",\n" +
                    "    \"sichuangSeverUrl\":\"http://192.168.16.101:5000/TaskNR/TaskData\",\n" +
                    "    \"deviceparams\":\"[{\\\"SPCS001\\\":\\\"630\\\"}]\"\n" +
                    "  }\n" +
                    "}";

            log.info("准备发送检测请求到下行主题: {}", jsonMessage);
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            // 向下行主题发送消息
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("检测请求已发送，等待设备采集系统响应...");

        } catch (Exception e) {
            log.error("发送思创检测请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送万测变压器获取所有数据请求
     */
    public void sendWanceDeviceGetAllDataRequest() {
        try {
            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 根据截图构造万测变压器获取所有数据的请求
            String jsonMessage = "{\n" +
                    "  \"header\":{\n" +
                    "    \"deviceId\":\"" + DEVICE_WANCE + "\",\n" +
                    "    \"product\": \"" + PRODUCT_ACCESSID + "\",\n" +
                    "    \"typeCode\": \"getAllData\",\n" +
                    "    \"messageId\": \"" + messageId + "\"\n" +
                    "  },\n" +
                    "  \"body\":{\n" +
                    "    \"type\":\"1\" // 模拟值，固定的\n" +
                    "  }\n" +
                    "}";

            log.info("准备发送万测变压器获取所有数据请求: {}", jsonMessage);
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("万测变压器获取所有数据请求已发送，等待响应...");

        } catch (Exception e) {
            log.error("发送万测变压器获取所有数据请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送万测变压器获取指定表数据请求（不指定时间范围）
     *
     * @param tableName 表名，例如"DTS_T_RZZLDZCL"
     */
    public void sendWanceDeviceGetDataByTableRequest(String tableName) {
        // 默认使用当前日期作为开始和结束时间
        String today = cn.hutool.core.date.DateUtil.today();
        // 将yyyy-MM-dd格式转换为yyyy/MM/dd格式
        String formattedDate = today.replace("-", "/");
        log.info("使用当前日期作为时间范围: {}", formattedDate);
        sendWanceDeviceGetDataByTableRequest(tableName, formattedDate, formattedDate);
    }

    /**
     * 发送万测变压器获取指定表数据请求（带时间范围）
     *
     * @param tableName 表名，例如"DTS_T_RZZLDZCL"
     * @param beginTime 开始时间，格式为"yyyy/MM/dd"，例如"2025/4/21"
     * @param endTime   结束时间，格式为"yyyy/MM/dd"，例如"2025/4/22"
     */
    public void sendWanceDeviceGetDataByTableRequest(String tableName, String beginTime, String endTime) {
        try {
            if (StrUtil.isBlank(tableName)) {
                log.error("表名不能为空");
                return;
            }

            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 根据最新需求构造万测变压器获取指定表数据的请求，增加时间参数
            Map<String, Object> requestMap = new HashMap<>();
            Map<String, Object> header = new HashMap<>();
            Map<String, Object> body = new HashMap<>();

            // 设置header
            header.put("deviceId", "wanceDevice");
            header.put("product", "accessId");
            header.put("typeCode", "getDataByTable");
            header.put("messageId", messageId);

            // 设置body
            body.put("tableName", tableName);
            body.put("resultNum", "1");
            body.put("beginTime", beginTime);
            body.put("endTime", endTime);

            // 组装请求
            requestMap.put("header", header);
            requestMap.put("body", body);

            // 转换为JSON字符串
            String jsonMessage = JSONUtil.toJsonStr(requestMap);

            // 格式化打印JSON，方便查看
            String prettyJson = JSONUtil.toJsonPrettyStr(requestMap);
            log.info("\n======================万测变压器获取表数据请求（格式化）======================\n");
            log.info("\n{}", prettyJson);
            log.info("\n======================请求结束======================\n");

            log.info("准备发送万测变压器获取表[{}]数据请求，时间范围: {} 至 {}",
                    tableName, beginTime, endTime);
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("万测变压器获取表[{}]数据请求已发送，等待响应...", tableName);

        } catch (Exception e) {
            log.error("发送万测变压器获取指定表数据请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送电机设备获取数据请求
     *
     * @param host     主机地址，例如"************"
     * @param userName 用户名，例如"smb"
     * @param pwd      密码，例如"123456"
     * @param share    共享文件夹，例如"data"
     */
    public void sendEleMachineGetDataRequest(String host, String userName, String pwd, String share) {
        try {
            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 构造电机设备获取数据请求
            Map<String, Object> requestMap = new HashMap<>();
            Map<String, Object> header = new HashMap<>();
            Map<String, Object> body = new HashMap<>();

            // 设置header
            header.put("deviceId", DEVICE_ELE_MACHINE);
            header.put("product", PRODUCT_ELE_MACHINE);
            header.put("typeCode", "getData");
            header.put("messageId", messageId);

            // 设置body
            body.put("host", host);
            body.put("userName", userName);
            body.put("pwd", pwd);
            body.put("share", share);

            // 组装请求
            requestMap.put("header", header);
            requestMap.put("body", body);

            // 转换为JSON字符串
            String jsonMessage = JSONUtil.toJsonStr(requestMap);

            // 格式化打印JSON，方便查看
            String prettyJson = JSONUtil.toJsonPrettyStr(requestMap);
            log.info("\n======================电机设备请求（格式化）======================\n");
            log.info("\n{}", prettyJson);
            log.info("\n======================请求结束======================\n");

            log.info("准备发送电机设备获取数据请求");
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("电机设备获取数据请求已发送，等待响应...");

        } catch (Exception e) {
            log.error("发送电机设备获取数据请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送 10T电子万能试验机 获取数据请求
     *
     * @param host     主机地址，例如"************"
     * @param userName 用户名，例如"utm"
     * @param pwd      密码，例如"123456"
     * @param share    共享文件夹，例如"testdata"
     */
    public void sendElectronicUTMGetDataRequest(String host, String userName, String pwd, String share) {
        try {
            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 构造 10T电子万能试验机 获取数据请求
            Map<String, Object> requestMap = new HashMap<>();
            Map<String, Object> header = new HashMap<>();
            Map<String, Object> body = new HashMap<>();

            // 设置header
            header.put("deviceId", DEVICE_ELECTRONIC_UTM);
            header.put("product", PRODUCT_ELECTRONIC_UTM);
            header.put("typeCode", "getData");
            header.put("messageId", messageId);

            // 设置body
            body.put("host", host);
            body.put("userName", userName);
            body.put("pwd", pwd);
            body.put("share", share);

            // 组装请求
            requestMap.put("header", header);
            requestMap.put("body", body);

            // 转换为JSON字符串
            String jsonMessage = JSONUtil.toJsonStr(requestMap);

            // 格式化打印JSON，方便查看
            String prettyJson = JSONUtil.toJsonPrettyStr(requestMap);
            log.info("\n======================10T电子万能试验机请求（格式化）======================\n");
            log.info("\n{}", prettyJson);
            log.info("\n======================请求结束======================\n");

            log.info("准备发送 10T电子万能试验机 获取数据请求");
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("10T电子万能试验机 获取数据请求已发送，等待响应...");

        } catch (Exception e) {
            log.error("发送 10T电子万能试验机 获取数据请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送绝缘子机电破坏试验机获取数据请求
     */
    public void sendJyzJdphGetAllDataRequest() {
        try {
            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 构造绝缘子机电破坏试验机获取数据请求
            Map<String, Object> requestMap = new HashMap<>();
            Map<String, Object> header = new HashMap<>();
            Map<String, Object> body = new HashMap<>();

            // 设置header
            header.put("deviceId", DEVICE_JYZ_JDPH);
            header.put("product", PRODUCT_JYZ_JDPH);
            header.put("typeCode", "getAllData");
            header.put("messageId", messageId);

            // 设置body，根据文档只需要简单的占位值
            body.put("test", "1");

            // 组装请求
            requestMap.put("header", header);
            requestMap.put("body", body);

            // 转换为JSON字符串
            String jsonMessage = JSONUtil.toJsonStr(requestMap);

            // 格式化打印JSON，方便查看
            String prettyJson = JSONUtil.toJsonPrettyStr(requestMap);
            log.info("\n======================绝缘子机电破坏试验机请求（格式化）======================\n");
            log.info("\n{}", prettyJson);
            log.info("\n======================请求结束======================\n");

            log.info("准备发送绝缘子机电破坏试验机获取数据请求");
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("绝缘子机电破坏试验机获取数据请求已发送，等待响应...");

        } catch (Exception e) {
            log.error("发送绝缘子机电破坏试验机获取数据请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送维卡万能试验机获取数据请求
     */
    public void sendWkUtmGetAllDataRequest() {
        try {
            // 生成时间戳作为消息ID
            String messageId = String.valueOf(System.currentTimeMillis());
            // 保存消息ID，用于过滤自己发送的消息
            this.lastSentMessageId = messageId;

            // 构造维卡万能试验机获取数据请求
            Map<String, Object> requestMap = new HashMap<>();
            Map<String, Object> header = new HashMap<>();
            Map<String, Object> body = new HashMap<>();

            // 设置header
            header.put("deviceId", DEVICE_WK_UTM);
            header.put("product", PRODUCT_WK_UTM);
            header.put("typeCode", "getAllData");
            header.put("messageId", messageId);

            // 设置body，根据文档只需要简单的占位值
            body.put("test", "1");

            // 组装请求
            requestMap.put("header", header);
            requestMap.put("body", body);

            // 转换为JSON字符串
            String jsonMessage = JSONUtil.toJsonStr(requestMap);

            // 格式化打印JSON，方便查看
            String prettyJson = JSONUtil.toJsonPrettyStr(requestMap);
            log.info("\n======================维卡万能试验机请求（格式化）======================\n");
            log.info("\n{}", prettyJson);
            log.info("\n======================请求结束======================\n");

            log.info("准备发送维卡万能试验机获取数据请求");
            log.info("消息ID: {} 已保存，用于过滤自己发送的消息", messageId);
            publishMessage(DOWN_TOPIC, jsonMessage);
            log.info("维卡万能试验机获取数据请求已发送，等待响应...");

        } catch (Exception e) {
            log.error("发送维卡万能试验机获取数据请求失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 关闭MQTT客户端
     */
    public void close() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                mqttClient.close();
                log.info("MQTT客户端已关闭");
            }
        } catch (MqttException e) {
            log.error("关闭MQTT客户端失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取最后一次接收到的响应体
     * 
     * @return 响应体JSON字符串
     */
    public String getLastResponseBody() {
        return lastResponseBody;
    }

    /**
     * 主函数示例
     */
    public static void main(String[] args) {

        MqttClientDemo demo = new MqttClientDemo();

        try {
            // 初始化MQTT客户端
            demo.init();

            // 检查连接状态
            if (demo.mqttClient.isConnected()) {
                log.info("MQTT服务器连接状态：已连接");
            } else {
                log.error("MQTT服务器连接状态：未连接");
                return;
            }

            // 测试选项：
            // 1. 发送思创检测请求示例
            // 2. 发送万测变压器获取所有数据请求
            // 3. 发送万测变压器获取指定表数据请求
            // 4. 发送电机设备获取数据请求
            // 5. 发送 10T电子万能试验机 获取数据请求
            // 6. 发送绝缘子机电破坏试验机获取数据请求
            // 7. 发送维卡万能试验机获取数据请求

            int testCase = 3; // 修改此值以测试不同的请求

            switch (testCase) {
                case 1:
                    // 发送思创检测请求示例
                    demo.sendSichuangTestRequest();
                    break;
                case 2:
                    // 发送万测变压器获取所有数据请求
                    demo.sendWanceDeviceGetAllDataRequest();
                    break;
                case 3:
                    // 发送万测变压器获取指定表数据请求
                    demo.sendWanceDeviceGetDataByTableRequest("DTS_T_RZZLDZCL", "2024-4-21", "2025-5-6");
                    break;
                case 4:
                    // 10T电子万能试验机
                    demo.sendEleMachineGetDataRequest("************", "smb", "123456", "data");
                    break;
                case 5:
                    // 发送 10T电子万能试验机 获取数据请求
                    demo.sendElectronicUTMGetDataRequest("************", "utm", "123456", "testdata");
                    break;
                case 6:
                    // 发送绝缘子机电破坏试验机获取数据请求
                    demo.sendJyzJdphGetAllDataRequest();
                    break;
                case 7:
                    // 发送维卡万能试验机获取数据请求
                    demo.sendWkUtmGetAllDataRequest();
                    break;
                default:
                    log.warn("未选择有效的测试选项");
            }

            // 保持程序运行，等待接收消息
            log.info("程序将保持运行15分钟，等待接收消息...");
            Thread.sleep(900000); // 运行15分钟

        } catch (Exception e) {
            log.error("程序运行异常: {}", e.getMessage(), e);
        } finally {
            // 关闭MQTT客户端
            demo.close();
        }
    }
}
