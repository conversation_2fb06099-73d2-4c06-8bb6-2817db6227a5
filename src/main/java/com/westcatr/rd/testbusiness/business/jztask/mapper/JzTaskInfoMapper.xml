<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="end_date" property="endDate" />
        <result column="sample_id" property="sampleId" />
        <result column="start_date" property="startDate" />
        <result column="task_creation_time" property="taskCreationTime" />
        <result column="task_number" property="taskNumber" />
        <result column="task_status" property="taskStatus" />
        <result column="test_level" property="testLevel" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, end_date, id, sample_id, start_date, task_creation_time, task_number, task_status, test_level, update_time
    </sql>

</mapper>
