package com.westcatr.rd.testbusiness.business.basics.pojo.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 海康威视摄像头管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "海康威视摄像头管理查询对象")
public class BuInstrumentCameraInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "摄像头类型(球机/枪机/半球等)")
    @QueryCondition
    private String cameraType;

    @Schema(description = "通道号")
    @QueryCondition
    private Integer channelNum;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "设备编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String deviceCode;

    @Schema(description = "摄像头型号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String deviceModel;

    @Schema(description = "摄像头名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String deviceName;

    @Schema(description = "设备状态")
    @QueryCondition
    private String deviceStatus;

    @Schema(description = "主键")
    @QueryCondition
    private Long id;

    @Schema(description = "安装位置")
    @QueryCondition
    private String installationLocation;

    @Schema(description = "IP地址")
    @QueryCondition
    private String ipAddress;

    @Schema(description = "是否在线(1在线/0离线)")
    @QueryCondition
    private Integer isOnline;

    @Schema(description = "登录密码")
    @QueryCondition
    private String password;

    @Schema(description = "端口号")
    @QueryCondition
    private Integer port;

    @Schema(description = "RTSP流地址")
    @QueryCondition
    private String rtspUrl;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "登录用户名")
    @QueryCondition
    private String username;

    @Schema(description = "工位id")
    @QueryCondition
    private Long workstationId;

    @QueryCondition(condition = QueryCondition.Condition.IN, field = "workstation_id")
    private List<Long> workstationIds;
}
