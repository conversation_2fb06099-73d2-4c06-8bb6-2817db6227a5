package com.westcatr.rd.testbusiness.business.mqtt.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.westcatr.rd.testbusiness.business.mqtt.service.MqttDebugService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * MQTT调试文件清理定时任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Slf4j
@Component
public class MqttDebugCleanupTask {

    @Autowired
    private MqttDebugService mqttDebugService;

    /**
     * 每天凌晨2点执行清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredDebugResponses() {
        try {
            log.info("🧹 开始执行MQTT调试文件清理任务");
            
            int cleanedCount = mqttDebugService.cleanExpiredResponses();
            
            if (cleanedCount > 0) {
                log.info("✅ MQTT调试文件清理完成，共清理 {} 个过期文件", cleanedCount);
            } else {
                log.debug("📂 没有需要清理的过期MQTT调试文件");
            }
            
        } catch (Exception e) {
            log.error("❌ MQTT调试文件清理任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每小时执行一次统计任务（可选）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void logDebugStatistics() {
        try {
            if (mqttDebugService.isDebugEnabled()) {
                var stats = mqttDebugService.getDebugStatistics();
                log.info("📊 MQTT调试统计 - 文件数量: {}, 总大小: {} bytes, 设备类型: {}", 
                    stats.get("totalFiles"), 
                    stats.get("totalSize"), 
                    stats.get("deviceTypes"));
            }
        } catch (Exception e) {
            log.warn("⚠️ 获取MQTT调试统计信息失败: {}", e.getMessage());
        }
    }
}
