package com.westcatr.rd.testbusiness.business.org.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.file.service.FileInfoService;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.security.crud.entity.LoginLog;
import com.westcatr.rd.boot.security.crud.entity.Role;
import com.westcatr.rd.boot.security.crud.entity.UserRole;
import com.westcatr.rd.boot.security.crud.service.LoginLogService;
import com.westcatr.rd.boot.security.crud.service.UserRoleService;
import com.westcatr.rd.boot.security.service.PasswordHandler;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.mapper.OrgUserInfoMapper;
import com.westcatr.rd.testbusiness.business.org.pojo.dto.UserInfoNoPhotoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgUserInfoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.UserDepInfoVO;
import com.westcatr.rd.testbusiness.business.org.service.OrgDeptInfoService;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserDeptInfoService;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p>
 * 人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Service
public class OrgUserInfoServiceImpl extends ServiceImpl<OrgUserInfoMapper, OrgUserInfo> implements OrgUserInfoService {

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private OrgUserDeptInfoService orgUserDeptInfoService;

    @Autowired
    private OrgUserInfoMapper orgUserInfoMapper;

    @Autowired
    private OrgDeptInfoService orgDeptInfoService;

    @Autowired
    private LoginLogService loginLogService;

    @Autowired
    private PasswordHandler passwordHandler;

    @Value("${westcatr.boot.file.upload-folder}")
    private String filePath;

    @Autowired
    private FileInfoService fileInfoService;

    @Autowired
    private BaseDao baseDao;

    @Override
    public IPage<OrgUserInfo> entityPage(OrgUserInfoQuery query) {
        if (query.getDeptNewId() != null) {

            String deptnewid = query.getDeptNewId();
            LambdaQueryWrapper<OrgUserDeptInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(OrgUserDeptInfo::getDeptId, deptnewid);
            List<OrgUserDeptInfo> list = orgUserDeptInfoService.list(lambdaQueryWrapper);
            if (list.size() >= 1) {
                QueryWrapper<OrgUserInfo> wFactory = new WrapperFactory<OrgUserInfo>().create(query);
                if (StrUtil.isNotEmpty(query.getDeptNewId())) {
                    List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService.list(
                            new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getDeptId,
                                    query.getDeptNewId()));
                    if (CollUtil.isNotEmpty(orgUserDeptInfos)) {
                        wFactory.in("id",
                                orgUserDeptInfos.stream().map(OrgUserDeptInfo::getUserId).collect(Collectors.toList()));
                    }
                }
                if (StrUtil.isNotEmpty(query.getRoleNewId())) {
                    List<UserRole> userRoles = userRoleService
                            .list(new LambdaQueryWrapper<UserRole>().eq(UserRole::getRoleId, query.getRoleNewId()));
                    if (CollUtil.isNotEmpty(userRoles)) {
                        wFactory.in("id",
                                userRoles.stream().map(UserRole::getUserId).collect(Collectors.toList()));
                    }
                }
                IPage<OrgUserInfo> orIPage = this.page(PageDTO.page(query), wFactory);
                if (CollUtil.isNotEmpty(orIPage.getRecords())) {
                    orIPage.getRecords().forEach(x -> {
                        x.setPassWord(null);
                        if (x.getTfPathSign() != null && x.getTfPathSign()) {
                            String picPath = filePath + "avatar/" + x.getId() + ".jpg";
                            if (FileUtil.exist(picPath)) {
                                x.setPhotoUrl("avatar/" + x.getId() + ".jpg");
                            }
                        }
                        List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService
                                .list(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getUserId,
                                        x.getId()));
                        List<String> deptNames = orgUserDeptInfos.stream()
                                .map(OrgUserDeptInfo::getDeptId)
                                .distinct()
                                .flatMap(deptId -> orgDeptInfoService.list(
                                        new LambdaQueryWrapper<OrgDeptInfo>().eq(OrgDeptInfo::getId, deptId))
                                        .stream()
                                        .map(OrgDeptInfo::getDeptName))
                                .collect(Collectors.toList());
                        x.setDeptNames(deptNames);
                        List<Role> roles = userRoleService.getByUserId(x.getId());
                        x.setRoleNames(roles.stream().map(Role::getRoleName)
                                .collect(Collectors.toList()));
                    });
                }
                return orIPage;
            } else {
                return null;
            }
        }
        QueryWrapper<OrgUserInfo> wFactory = new WrapperFactory<OrgUserInfo>().create(query);
        if (StrUtil.isNotEmpty(query.getDeptNewId())) {
            List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService.list(
                    new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getDeptId, query.getDeptNewId()));
            if (CollUtil.isNotEmpty(orgUserDeptInfos)) {
                wFactory.in("id",
                        orgUserDeptInfos.stream().map(OrgUserDeptInfo::getUserId).collect(Collectors.toList()));
            }
        }
        if (StrUtil.isNotEmpty(query.getRoleNewId())) {
            List<UserRole> userRoles = userRoleService
                    .list(new LambdaQueryWrapper<UserRole>().eq(UserRole::getRoleId, query.getRoleNewId()));
            if (CollUtil.isNotEmpty(userRoles)) {
                wFactory.in("id",
                        userRoles.stream().map(UserRole::getUserId).collect(Collectors.toList()));
            }
        }
        IPage<OrgUserInfo> orIPage = this.page(PageDTO.page(query), wFactory);
        if (CollUtil.isNotEmpty(orIPage.getRecords())) {
            orIPage.getRecords().forEach(x -> {
                x.setPassWord(null);
                if (x.getTfPathSign() != null && x.getTfPathSign()) {
                    String picPath = filePath + "avatar/" + x.getId() + ".jpg";
                    if (FileUtil.exist(picPath)) {
                        x.setPhotoUrl("avatar/" + x.getId() + ".jpg");
                    }
                }
                List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService
                        .list(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getUserId, x.getId()));
                List<String> deptNames = orgUserDeptInfos.stream()
                        .map(OrgUserDeptInfo::getDeptId)
                        .distinct()
                        .flatMap(deptId -> orgDeptInfoService.list(
                                new LambdaQueryWrapper<OrgDeptInfo>().eq(OrgDeptInfo::getId, deptId))
                                .stream()
                                .map(OrgDeptInfo::getDeptName))
                        .collect(Collectors.toList());
                x.setDeptNames(deptNames);
                List<Role> roles = userRoleService.getByUserId(x.getId());
                x.setRoleNames(roles.stream().map(Role::getRoleName)
                        .collect(Collectors.toList()));
            });
        }
        return orIPage;

    }

    @Override
    public OrgUserInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(OrgUserInfo param) {
        String newPass = passwordHandler.transferPasswordDecryption(param.getPassWord());
        param.setPassWord(passwordHandler.dbPasswordEncryption(newPass));
        String base64 = "";
        if (StrUtil.isNotEmpty(param.getPhotoUrl())) {
            FileInfo fileInfo = fileInfoService.getOne(new LambdaQueryWrapper<FileInfo>()
                    .eq(FileInfo::getFileUrl, param.getPhotoUrl()));
            if (ObjectUtil.isNotNull(fileInfo)) {
                String filePathPhoto = filePath + fileInfo.getFilePath();
                if (FileUtil.exist(filePathPhoto)) {
                    FileUtil.copy(filePathPhoto, filePath + "avatar/" + param.getId() + ".jpg", true);
                    base64 = Base64.encode(FileUtil.readBytes(filePathPhoto));
                }
            }
        }
        if (this.save(param)) {
            List<String> deptNames = param.getDeptNames();
            ArrayList<OrgUserDeptInfo> vos = new ArrayList<>();
            for (String name : deptNames) {
                OrgUserDeptInfo vo = new OrgUserDeptInfo();
                OrgDeptInfo res = orgDeptInfoService
                        .getOne(new LambdaQueryWrapper<OrgDeptInfo>().eq(OrgDeptInfo::getDeptName, name));
                vo.setUserId(param.getId());
                vo.setDeptId(res.getId());
                vos.add(vo);
            }
            // 更新头像
            if (this.updateById(param)) {
                if (StrUtil.isNotBlank(base64)) {
                    String sql = "update org_user_info set user_sign_info = \"" + base64
                            + "\", tf_path_sign =1  where id = "
                            + param.getId() + "";
                    baseDao.execute(sql);
                }
            }
            return orgUserDeptInfoService.saveBatch(vos);
        }
        return false;
    }

    @Override
    public boolean updateEntity(OrgUserInfo param) {
        List<String> deptNames = param.getDeptNames();
        if (CollUtil.isNotEmpty(deptNames)) {
            ArrayList<OrgUserDeptInfo> records = new ArrayList<>();
            List<OrgDeptInfo> list = orgDeptInfoService
                    .list(new LambdaQueryWrapper<OrgDeptInfo>().in(OrgDeptInfo::getDeptName, deptNames));
            if (orgUserDeptInfoService
                    .remove(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getUserId, param.getId()))) {
                for (OrgDeptInfo orgDeptInfo : list) {
                    OrgUserDeptInfo vo = new OrgUserDeptInfo();
                    vo.setUserId(param.getId());
                    vo.setDeptId(orgDeptInfo.getId());
                    records.add(vo);
                }
                orgUserDeptInfoService.saveBatch(records);
            }
        } else {
            orgUserDeptInfoService
                    .remove(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getUserId, param.getId()));
        }
        String base64 = "";
        if (StrUtil.isNotEmpty(param.getPhotoUrl())) {
            FileInfo fileInfo = fileInfoService.getOne(new LambdaQueryWrapper<FileInfo>()
                    .eq(FileInfo::getFileUrl, param.getPhotoUrl()));
            if (ObjectUtil.isNotNull(fileInfo)) {
                String filePathPhoto = filePath + fileInfo.getFilePath();
                if (FileUtil.exist(filePathPhoto)) {
                    FileUtil.copy(filePathPhoto, filePath + "avatar/" + param.getId() + ".jpg", true);
                    base64 = Base64.encode(FileUtil.readBytes(filePathPhoto));
                }
            }
        }
        // 更新头像
        if (this.updateById(param)) {
            if (StrUtil.isNotBlank(base64)) {
                String sql = "update org_user_info set user_sign_info = \"" + base64
                        + "\", tf_path_sign =1  where id = "
                        + param.getId() + "";
                baseDao.execute(sql);
            }
        }
        return true;
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<OrgUserInfo> listByRoleId(Long roleId) {
        List<UserRole> userRoles = userRoleService
                .list(new LambdaQueryWrapper<UserRole>().eq(UserRole::getRoleId, roleId));
        return CollUtil.isNotEmpty(userRoles) ? this.list(new LambdaQueryWrapper<OrgUserInfo>().in(OrgUserInfo::getId,
                userRoles.stream().map(UserRole::getUserId).collect(Collectors.toList()))) : null;
    }

    @Override
    public List<OrgUserInfo> listByDeptId(Long deptId) {
        List<OrgUserDeptInfo> orgUserDeptInfos = orgUserDeptInfoService
                .list(new LambdaQueryWrapper<OrgUserDeptInfo>().eq(OrgUserDeptInfo::getDeptId, deptId));
        return CollUtil.isNotEmpty(orgUserDeptInfos)
                ? this.list(new LambdaQueryWrapper<OrgUserInfo>().in(OrgUserInfo::getId,
                        orgUserDeptInfos.stream().map(OrgUserDeptInfo::getUserId).collect(Collectors.toList())))
                : null;
    }

    @Override
    public List<OrgUserInfo> listByIds(List<Long> ids) {
        return orgUserInfoMapper.selectBatchIds(ids);
    }

    @Override
    public List<OrgUserInfo> userInfoNoPhoto(UserInfoNoPhotoQuery userInfoNoPhotoQuery) {
        LambdaQueryWrapper<OrgUserInfo> queryWrapper = new LambdaQueryWrapper<OrgUserInfo>();
        if (CollUtil.isNotEmpty(userInfoNoPhotoQuery.getDeptIds())) {
            List<OrgUserDeptInfo> oUserDeptInfos = orgUserDeptInfoService.list(new LambdaQueryWrapper<OrgUserDeptInfo>()
                    .in(OrgUserDeptInfo::getDeptId, userInfoNoPhotoQuery.getDeptIds()));
            if (CollUtil.isNotEmpty(oUserDeptInfos)) {
                // java8获取oUserDeptInfos中userId的属性并返回List<Long>
                queryWrapper.in(OrgUserInfo::getId,
                        oUserDeptInfos.stream().map(OrgUserDeptInfo::getUserId).collect(Collectors.toList()));
            }
        }
        if (CollUtil.isNotEmpty(userInfoNoPhotoQuery.getRoleIds())) {
            List<UserRole> userRoles = userRoleService.list(
                    new LambdaQueryWrapper<UserRole>().in(UserRole::getRoleId, userInfoNoPhotoQuery.getRoleIds()));
            if (CollUtil.isNotEmpty(userRoles)) {
                queryWrapper.in(OrgUserInfo::getId,
                        userRoles.stream().map(UserRole::getUserId).collect(Collectors.toList()));

            }
        }
        if (CollUtil.isNotEmpty(userInfoNoPhotoQuery.getUserIds())) {
            queryWrapper.in(OrgUserInfo::getId, userInfoNoPhotoQuery.getUserIds());
        }
        if (StrUtil.isNotEmpty(userInfoNoPhotoQuery.getFullName())) {
            queryWrapper.like(OrgUserInfo::getFullName, userInfoNoPhotoQuery.getFullName());
        }
        if (StrUtil.isNotEmpty(userInfoNoPhotoQuery.getUserName())) {
            queryWrapper.like(OrgUserInfo::getUserName, userInfoNoPhotoQuery.getUserName());
        }
        List<Long> testUserIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(userInfoNoPhotoQuery.getDeptIds())) {
            List<OrgDeptInfo> orgDeptInfos = orgDeptInfoService.list(
                    new LambdaQueryWrapper<OrgDeptInfo>().in(OrgDeptInfo::getId, userInfoNoPhotoQuery.getDeptIds()));
            if (CollUtil.isNotEmpty(orgDeptInfos)) {
                testUserIds = orgDeptInfos.stream().map(OrgDeptInfo::getExecutiveUserId).collect(Collectors.toList());
            }
        }
        queryWrapper.select(OrgUserInfo::getUserName, OrgUserInfo::getId, OrgUserInfo::getFullName,
                OrgUserInfo::getPhone, OrgUserInfo::getEmail);
        List<OrgUserInfo> returnList = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(testUserIds)) {
            for (OrgUserInfo orgUserInfo : returnList) {
                if (CollUtil.contains(testUserIds, orgUserInfo.getId())) {
                    orgUserInfo.setTfTestMain(true);
                }
            }
        }
        return returnList;
    }

    public boolean updatePassword(Long userId, String password, String token) {
        boolean update = update(new LambdaUpdateWrapper<OrgUserInfo>()
                .set(OrgUserInfo::getPassWord, passwordHandler.dbPasswordEncryption(password))
                .eq(OrgUserInfo::getId, userId));
        if (update) {
            if (StrUtil.isBlank(token)) {
                // 让用户下线
                return loginLogService.down(userId, "因修改密码，强制账号下线");
            } else {
                List<LoginLog> list = loginLogService.list(new LambdaQueryWrapper<LoginLog>()
                        .eq(LoginLog::getUserId, userId)
                        .ne(LoginLog::getToken, token)
                        .eq(LoginLog::getOnline, true));
                if (CollUtil.isNotEmpty(list)) {
                    for (LoginLog loginLog : list) {
                        loginLogService.down(loginLog.getToken(), "因修改密码，强制账号下线");
                    }
                }
            }
        }
        return update;
    }

    @Override
    public List<OrgUserInfo> getSameDepUserInfo(Long longId) {

        List<OrgUserDeptInfo> tagetList = orgUserDeptInfoService.list();
        List<Long> depIds = tagetList.stream()
                .filter(x -> Objects.equals(x.getUserId(), longId))
                .map(OrgUserDeptInfo::getDeptId)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(depIds)) {
            List<Long> userIds = tagetList.stream()
                    .filter(x -> depIds.contains(x.getDeptId()))
                    .map(OrgUserDeptInfo::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(userIds)) {
                List<OrgUserInfo> users = this
                        .list(new LambdaQueryWrapper<OrgUserInfo>().in(OrgUserInfo::getId, userIds));
                if (CollUtil.isNotEmpty(users)) {
                    users.stream().forEach(x -> {
                        x.setPassWord(null);
                    });
                    return users;
                }
            }

        }
        return null;
    }

    @Override
    public Map<String, List<UserDepInfoVO>> getUserInfoByRole(Long longId) {
        if (longId == null) {
            longId = 8L;
        }
        List<UserRole> list = userRoleService.list(new LambdaQueryWrapper<UserRole>().eq(UserRole::getRoleId, longId));
        if (CollUtil.isNotEmpty(list)) {
            List<Long> userlist = list.stream().map(UserRole::getUserId).collect(Collectors.toList());
            List<OrgUserDeptInfo> userDeptInfos = orgUserDeptInfoService
                    .list(new LambdaQueryWrapper<OrgUserDeptInfo>().in(OrgUserDeptInfo::getUserId, userlist));
            List<UserDepInfoVO> vos = new ArrayList<>();
            for (OrgUserDeptInfo info : userDeptInfos) {
                UserDepInfoVO vo = new UserDepInfoVO();
                BeanUtils.copyProperties(info, vo);
                OrgUserInfo userInfo = this.getById(info.getUserId());
                OrgUserInfo orgUserInfo = this.getById(info.getUserId());
                if (ObjectUtil.isNull(orgUserInfo)) {
                    continue;
                }
                orgUserInfo.setPassWord(null);
                vo.setUserInfo(orgUserInfo);
                OrgDeptInfo deptInfo = orgDeptInfoService.getById(info.getDeptId());
                if (ObjectUtil.isNull(deptInfo)) {
                    continue;
                }
                vo.setDeptName(deptInfo.getDeptName());
                vos.add(vo);
            }
            Map<String, List<UserDepInfoVO>> collect = vos.stream()
                    .collect(Collectors.groupingBy(UserDepInfoVO::getDeptName));
            for (String key : collect.keySet()) {
                List<UserDepInfoVO> voList = collect.get(key);
                voList = voList.stream().distinct().collect(Collectors.toList());
                collect.put(key, voList);
            }
            return collect;
        }
        return null;
    }

    @Override
    public OrgUserInfo getEntityByEmpId(String empId) {
        return this.getOne(new LambdaQueryWrapper<OrgUserInfo>().eq(OrgUserInfo::getEmpId, empId));
    }

    @Override
    public List<OrgUserInfo> getEntityWithNoEmpId() {
        return this.list(new LambdaQueryWrapper<OrgUserInfo>().isNull(OrgUserInfo::getEmpId));
    }

}
