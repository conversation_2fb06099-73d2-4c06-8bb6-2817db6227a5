package com.westcatr.rd.testbusiness.business.gwstandard.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—实验项目检测仪器信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="标准—实验项目检测仪器信息表查询对象")
public class StandardBasicProjectInstrumentQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "实验项目名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String projectName;

    @Schema(description = "仪器")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE)
    private Long instrumentId;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "修改时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "创建人ID")
    @QueryCondition
    private Long createUserId;

    @Schema(description = "最后操作人ID")
    @QueryCondition
    private Long updateUserId;

    @QueryCondition
    private String createUserFullName;

    @QueryCondition
    private String updateUserFullName;
}
