package com.westcatr.rd.testbusiness.business.devicedata.mapper;

import java.util.Date;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 设备数据查询 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Mapper
public interface DeviceDataQueryMapper {

        /**
         * 根据设备代码和时间范围查询设备功能调用记录的最新有效数据
         * 过滤掉错误响应，检查message.bodyData字段是否存在
         *
         * @param deviceCode 设备代码（对应device_id或product字段）
         * @param startTime  开始时间
         * @param endTime    结束时间
         * @return 查询结果
         */
        @Select("SELECT id, device_id, product, output_params, async_call, create_time " +
                        "FROM zzzzz_log_device_function_call " +
                        "WHERE (device_id = #{deviceCode} OR product = #{deviceCode}) " +
                        "AND create_time >= #{startTime} AND create_time <= #{endTime} " +
                        "AND output_params IS NOT NULL " +
                        "AND JSON_EXTRACT(output_params, '$.message') IS NOT NULL " +
                        "AND JSON_EXTRACT(output_params, '$.message.bodyData') IS NOT NULL " +
                        "ORDER BY create_time DESC LIMIT 1")
        Map<String, Object> selectLatestDataByDeviceCode(@Param("deviceCode") String deviceCode,
                        @Param("startTime") Date startTime,
                        @Param("endTime") Date endTime);

        /**
         * 根据设备代码查询最新一条有效数据（不限时间）
         * 过滤掉错误响应，检查message.bodyData字段是否存在
         *
         * @param deviceCode 设备代码（对应device_id或product字段）
         * @return 查询结果
         */
        @Select("SELECT id, device_id, product, output_params, async_call, create_time " +
                        "FROM zzzzz_log_device_function_call " +
                        "WHERE (device_id = #{deviceCode}) " +
                        "AND output_params IS NOT NULL " +
                        "AND JSON_EXTRACT(output_params, '$.message') IS NOT NULL " +
                        "AND JSON_EXTRACT(output_params, '$.message.bodyData') IS NOT NULL " +
                        "ORDER BY create_time DESC LIMIT 1")
        Map<String, Object> selectLatestDataByDeviceCodeNoTime(@Param("deviceCode") String deviceCode);

        /**
         * 检查设备是否存在有效数据记录
         * 过滤掉错误响应，检查message.bodyData字段是否存在
         *
         * @param deviceCode 设备代码
         * @return 有效记录数量
         */
        @Select("SELECT COUNT(*) FROM zzzzz_log_device_function_call " +
                        "WHERE (device_id = #{deviceCode}) " +
                        "AND output_params IS NOT NULL " +
                        "AND JSON_EXTRACT(output_params, '$.message') IS NOT NULL " +
                        "AND JSON_EXTRACT(output_params, '$.message.bodyData') IS NOT NULL")
        int checkDeviceDataExists(@Param("deviceCode") String deviceCode);
}
