package com.westcatr.rd.testbusiness.business.mqtt.service.impl;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fast.jackson.JSONObject;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigService;
import com.westcatr.rd.testbusiness.business.mqtt.service.MqttClientService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * MQTT客户端服务实现类
 * </p>
 *
 * 通信模式：
 * - 下行主题（/jcxt/link/down/edgeGateway/edgeTopic）：只发送指令，不监听
 * - 上行主题（/jcxt/link/up/edgeGateway/edgeTopic）：只监听结果，不发送
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Slf4j
@Service
public class MqttClientServiceImpl implements MqttClientService {

    // MQTT服务器地址
    @Value("${mqtt.broker-url:tcp://48kkod342786.vicp.fun:39266}")
    private String brokerUrl;

    // 客户端ID前缀
    @Value("${mqtt.client-id-prefix:test_business_}")
    private String clientIdPrefix;

    // 用户名
    @Value("${mqtt.username:}")
    private String username;

    // 密码
    @Value("${mqtt.password:}")
    private String password;

    // 下行主题（只发送指令，不监听）
    @Value("${mqtt.down-topic:/jcxt/link/down/edgeGateway/edgeTopic}")
    private String downTopic;

    // 上行主题（只监听结果，不发送）
    @Value("${mqtt.up-topic:/jcxt/link/up/edgeGateway/edgeTopic}")
    private String upTopic;

    // QoS级别
    @Value("${mqtt.qos:1}")
    private int qos;

    // 请求超时时间（秒）
    @Value("${mqtt.request-timeout:60}")
    private int requestTimeout;

    // 是否启用MQTT客户端
    @Value("${mqtt.enabled:true}")
    private boolean enabled;

    // 断线重连初始间隔（毫秒）
    @Value("${mqtt.reconnect-interval:5000}")
    private long initialReconnectInterval;

    // 最大重连间隔（毫秒）
    @Value("${mqtt.max-reconnect-interval:300000}")
    private long maxReconnectInterval;

    @Autowired
    private DeviceIntegrationConfigService deviceIntegrationConfigService;

    @Autowired
    private com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationService deviceIntegrationService;

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;

    @Autowired
    private com.westcatr.rd.testbusiness.business.mqtt.service.MqttDebugService mqttDebugService;

    private MqttClient mqttClient;
    private MqttConnectOptions options;

    // 存储消息ID和响应的映射关系
    private final Map<String, Map<String, Object>> responseMap = new ConcurrentHashMap<>();
    // 存储消息ID和CountDownLatch的映射关系，用于同步等待响应
    private final Map<String, CountDownLatch> latchMap = new ConcurrentHashMap<>();

    // 存储设备ID和最新响应的映射关系（用于异步获取设备响应）
    private final Map<Long, Map<String, Object>> deviceResponseMap = new ConcurrentHashMap<>();
    // 存储请求ID和设备ID的映射关系（用于异步请求）
    private final Map<String, Long> requestDeviceMap = new ConcurrentHashMap<>();

    // 添加原子布尔值，用于标记是否已初始化
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean connecting = new AtomicBoolean(false);

    // 添加连接丢失计数器，用于控制日志输出频率
    private final AtomicInteger connectionLossCount = new AtomicInteger(0);
    // 添加上次日志时间记录
    private long lastConnectionLossLogTime = 0;
    // 添加重连退避时间（毫秒）
    private long reconnectBackoff;

    @PostConstruct
    public void initOnStartup() {
        if (!enabled) {
            log.info("MQTT客户端已禁用，将不会初始化连接");
            return;
        }

        // 初始化重连退避时间
        reconnectBackoff = initialReconnectInterval;

        // 使用异步线程初始化MQTT客户端，避免阻塞应用启动
        new Thread(() -> {
            try {
                // 等待5秒，让应用其他部分先启动
                Thread.sleep(5000);
                log.info("开始异步初始化MQTT客户端...");
                init();
                if (isConnected()) {
                    log.info("MQTT客户端异步初始化成功并已连接！");
                } else {
                    log.warn("MQTT客户端异步初始化完成但未连接，将由定时任务重试连接");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("MQTT初始化线程被中断");
            } catch (Exception e) {
                log.error("MQTT客户端异步初始化失败: {}", e.getMessage(), e);
            }
        }, "mqtt-init-thread").start();

        log.info("MQTT客户端将在应用启动后异步初始化");
    }

    @Override
    public void init() {
        // 如果MQTT客户端已禁用，则直接返回
        if (!enabled) {
            log.info("MQTT客户端已禁用，跳过初始化");
            return;
        }

        // 如果已经初始化或正在连接中，则直接返回
        if (initialized.get() || !connecting.compareAndSet(false, true)) {
            return;
        }

        try {
            // 生成唯一的客户端ID
            String clientId = clientIdPrefix;
            log.info("开始初始化MQTT客户端，服务器地址: {}, 客户端ID: {}", brokerUrl, clientId);

            // 创建MQTT客户端，使用内存持久化
            mqttClient = new MqttClient(brokerUrl, clientId, new MemoryPersistence());

            // 设置连接选项
            options = new MqttConnectOptions();
            // 设置是否清除会话
            options.setCleanSession(true);
            // 设置超时时间，单位为秒 - 增加超时时间到180秒
            options.setConnectionTimeout(180);
            // 设置心跳间隔，单位为秒 - 增加到120秒
            options.setKeepAliveInterval(120);
            // 设置自动重连
            options.setAutomaticReconnect(true);
            // 设置最大重连间隔，单位为毫秒 - 增加到60秒
            options.setMaxReconnectDelay(60000);

            // 如果需要认证，设置用户名和密码
            if (StrUtil.isNotBlank(username) && StrUtil.isNotBlank(password)) {
                log.info("设置MQTT连接认证信息");
                options.setUserName(username);
                options.setPassword(password.toCharArray());
            }

            // 设置回调函数
            mqttClient.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    // 增加连接丢失计数
                    int count = connectionLossCount.incrementAndGet();
                    long now = System.currentTimeMillis();

                    // 如果距离上次日志超过30秒，或者是第一次断开，则记录日志
                    if (now - lastConnectionLossLogTime > 30000 || count == 1) {
                        log.error("MQTT连接丢失: {}，已断开{}次", cause.getMessage(), count);
                        lastConnectionLossLogTime = now;
                    }

                    initialized.set(false); // 重置初始化状态
                    // 尝试重新连接将由定时任务处理
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
                    log.debug("接收到来自主题[{}]的消息: {}", topic, payload);

                    // 处理接收到的消息
                    if (upTopic.equals(topic)) {
                        handleResponse(payload);
                    }
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    log.debug("消息发送完成");
                }
            });

            log.info("正在连接到MQTT服务器: {}", brokerUrl);

            try {
                // 连接MQTT服务器
                mqttClient.connect(options);

                if (mqttClient.isConnected()) {
                    log.info("MQTT服务器连接成功");
                    // 连接成功，重置计数器和退避时间
                    connectionLossCount.set(0);
                    reconnectBackoff = initialReconnectInterval;

                    // 订阅主题
                    mqttClient.subscribe(upTopic, qos);
                    log.info("已订阅上行主题: {}", upTopic);

                    log.info("MQTT客户端初始化成功，已连接到: {}", brokerUrl);
                    initialized.set(true); // 标记为已初始化
                } else {
                    log.error("MQTT服务器连接失败，但未抛出异常");
                }
            } catch (MqttException e) {
                log.error("MQTT服务器连接失败: {}", e.getMessage(), e);
                // 不抛出异常，允许应用继续运行
            }

        } catch (MqttException e) {
            log.error("MQTT客户端初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，允许应用继续运行
        } finally {
            connecting.set(false); // 重置连接状态
        }
    }

    // 添加定时任务，定期检查MQTT连接状态，使用指数退避策略
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkConnection() {
        // 如果MQTT客户端已禁用，则直接返回
        if (!enabled) {
            return;
        }

        if (!initialized.get() && !connecting.get()) {
            // 计算是否应该尝试重连
            long currentTime = System.currentTimeMillis();
            long timeSinceLastLog = currentTime - lastConnectionLossLogTime;

            if (timeSinceLastLog >= reconnectBackoff) {
                log.info("MQTT客户端未初始化或连接已断开，尝试重新连接...");
                init();

                // 如果连接失败，增加重连间隔时间（指数退避）
                if (!initialized.get()) {
                    reconnectBackoff = Math.min(reconnectBackoff * 2, maxReconnectInterval);
                    log.info("MQTT连接尝试失败，下次尝试将在{}秒后进行", reconnectBackoff / 1000);
                    lastConnectionLossLogTime = currentTime;
                }
            }
        } else if (mqttClient != null && !mqttClient.isConnected() && !connecting.get()) {
            // 计算是否应该尝试重连
            long currentTime = System.currentTimeMillis();
            long timeSinceLastLog = currentTime - lastConnectionLossLogTime;

            if (timeSinceLastLog >= reconnectBackoff) {
                log.info("MQTT客户端已断开连接，尝试重新连接...");
                try {
                    connecting.set(true);
                    mqttClient.connect(options);
                    if (mqttClient.isConnected()) {
                        mqttClient.subscribe(upTopic, qos);
                        log.info("MQTT重新连接成功并订阅主题: {}", upTopic);
                        initialized.set(true);
                        // 重置计数器和退避时间
                        connectionLossCount.set(0);
                        reconnectBackoff = initialReconnectInterval;
                    }
                } catch (MqttException e) {
                    log.error("MQTT重新连接失败: {}", e.getMessage(), e);
                    initialized.set(false);
                    // 增加重连间隔时间（指数退避）
                    reconnectBackoff = Math.min(reconnectBackoff * 2, maxReconnectInterval);
                    log.info("MQTT重连尝试失败，下次尝试将在{}秒后进行", reconnectBackoff / 1000);
                    lastConnectionLossLogTime = currentTime;
                } finally {
                    connecting.set(false);
                }
            }
        }
    }

    @PreDestroy
    @Override
    public void close() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                mqttClient.close();
                log.info("MQTT客户端已关闭");
            }
        } catch (MqttException e) {
            log.error("关闭MQTT客户端失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void publishMessage(String topic, String payload) {
        // 如果MQTT客户端已禁用，则直接返回
        if (!enabled) {
            log.warn("MQTT客户端已禁用，无法发送消息到主题: {}", topic);
            return;
        }

        try {
            if (mqttClient == null || !mqttClient.isConnected()) {
                log.error("MQTT客户端未连接");
                // 尝试初始化并连接
                init();
                if (mqttClient == null || !mqttClient.isConnected()) {
                    log.error("MQTT客户端连接失败，无法发送消息");
                    return;
                }
            }

            MqttMessage message = new MqttMessage(payload.getBytes(StandardCharsets.UTF_8));
            message.setQos(qos);

            mqttClient.publish(topic, message);
            log.info("消息已发布到主题: {}", topic);

        } catch (MqttException e) {
            log.error("消息发布失败: {}", e.getMessage(), e);
            throw new RuntimeException("消息发布失败", e);
        }
    }

    @Override
    public List<AutoResultDto> getDeviceTestData(Long deviceId, List<Long> parameterIds) {
        // 初始化结果列表
        List<AutoResultDto> result = new ArrayList<>();

        try {
            // 获取设备信息
            InstrumentNewInfo deviceInfo = instrumentNewInfoService.getById(deviceId);
            if (deviceInfo == null) {
                log.error("设备不存在: {}", deviceId);
                return result;
            }

            // 获取设备类型
            String deviceType = deviceInfo.getDeviceTypeCode();
            if (StrUtil.isBlank(deviceType)) {
                log.error("设备类型为空: {}", deviceId);
                return result;
            }

            // 解析设备参数
            Map<String, Object> params = new HashMap<>();
            if (StrUtil.isNotBlank(deviceInfo.getDeviceParams())) {
                try {
                    // 使用hutool的JSONUtil解析JSON字符串为Map
                    params = JSONUtil.toBean(deviceInfo.getDeviceParams(), HashMap.class);
                } catch (Exception e) {
                    log.error("解析设备参数失败: {}", e.getMessage(), e);
                }
            }

            // 添加当前时间作为开始和结束时间（针对万测变压器）
            if ("变压器".equals(deviceType)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                String today = sdf.format(new Date());
                params.put("beginTime", today);
                params.put("endTime", today);
            }

            // 发送设备请求
            Map<String, Object> response = sendDeviceRequest(deviceType, params);

            if (response != null && response.containsKey("body")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> body = (Map<String, Object>) response.get("body");

                if (body.containsKey("outputParams")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");

                    if (outputParams.containsKey("body")) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> bodyMap = (Map<String, Object>) outputParams.get("body");

                        if (bodyMap.containsKey("content")) {
                            String content = String.valueOf(bodyMap.get("content"));

                            // 解析content内容，提取测试结果
                            log.info("收到设备响应内容: {}", content);

                            // 尝试使用DeviceIntegrationService解析响应内容
                            try {
                                // 根据设备类型确定表名
                                String tableName = null;
                                if ("变压器".equals(deviceType)) {
                                    // 如果参数中包含tableName，则使用参数中的表名
                                    if (params.containsKey("tableName")) {
                                        tableName = String.valueOf(params.get("tableName"));
                                    } else {
                                        // 默认使用直阻试验表
                                        tableName = "DTS_T_RZZLDZCL";
                                    }
                                }

                                // 使用设备集成服务将设备返回的数据转换为测试项目与值的映射
                                Map<String, String> testNameValueMap = deviceIntegrationService
                                        .convertToTestNameValueMap(deviceType, content, tableName);

                                log.info("设备[{}]表名[{}]的测试项目值映射结果: {}", deviceType, tableName, testNameValueMap);

                                // 获取所有参数对应的StandardBasicInstrumentInfo
                                List<StandardBasicInstrumentInfo> parameterInfoList = standardBasicInstrumentInfoService
                                        .listByIds(parameterIds);

                                // 创建参数ID到测试项名称的映射
                                Map<Long, String> paramIdToNameMap = new HashMap<>();
                                for (StandardBasicInstrumentInfo info : parameterInfoList) {
                                    if (info != null && info.getProjectName() != null) {
                                        paramIdToNameMap.put(info.getId(), info.getProjectName());
                                    }
                                }

                                // 使用映射结果为每个参数ID创建AutoResultDto
                                for (Long parameterId : parameterIds) {
                                    AutoResultDto autoResultDto = new AutoResultDto();
                                    autoResultDto.setId(parameterId);

                                    // 获取参数对应的测试项名称
                                    String testName = paramIdToNameMap.get(parameterId);

                                    if (testName != null && testNameValueMap.containsKey(testName)) {
                                        // 使用映射结果中的值
                                        String resultValue = testNameValueMap.get(testName);
                                        autoResultDto.setResult(resultValue);
                                        // 设置参数原始key，用于前端显示
                                        String paramKey = getParamKeyByTestName(deviceType, testName);
                                        autoResultDto.setParamKey(paramKey);
                                        log.info("为参数ID[{}]测试项[{}]设置实际值: {}, 参数key: {}", parameterId, testName,
                                                resultValue, paramKey);
                                    } else {
                                        // 如果没有找到映射，尝试模糊匹配
                                        boolean matched = false;
                                        if (testName != null) {
                                            for (Map.Entry<String, String> entry : testNameValueMap.entrySet()) {
                                                String mappedName = entry.getKey();
                                                String mappedValue = entry.getValue();

                                                if ((testName.contains(mappedName) || mappedName.contains(testName)) &&
                                                        mappedValue != null && !mappedValue.equals("0")
                                                        && !mappedValue.equals("0E-7")) {
                                                    autoResultDto.setResult(mappedValue);
                                                    // 设置参数原始key，用于前端显示
                                                    String paramKey = getParamKeyByTestName(deviceType, mappedName);
                                                    autoResultDto.setParamKey(paramKey);
                                                    log.info("通过模糊匹配为参数ID[{}]测试项[{}]设置值: {} (映射名: {}, 参数key: {})",
                                                            parameterId, testName, mappedValue, mappedName, paramKey);
                                                    matched = true;
                                                    break;
                                                }
                                            }
                                        }

                                        // 如果仍然没有匹配，使用随机值
                                        if (!matched) {
                                            String randomValue = String.valueOf((int) (Math.random() * 100));
                                            autoResultDto.setResult(randomValue);
                                            // 设置参数名称作为原始key
                                            autoResultDto.setParamKey(testName);
                                            log.info("未找到参数ID[{}]测试项[{}]的映射，使用随机值: {}, 参数key设置为测试项名称",
                                                    parameterId, testName, randomValue);
                                        }
                                    }

                                    result.add(autoResultDto);
                                }
                            } catch (Exception e) {
                                log.error("解析设备响应内容失败: {}", e.getMessage(), e);

                                // 出错时使用随机值
                                for (Long parameterId : parameterIds) {
                                    AutoResultDto autoResultDto = new AutoResultDto();
                                    autoResultDto.setId(parameterId);
                                    autoResultDto.setResult(String.valueOf((int) (Math.random() * 100)));

                                    // 获取参数对应的测试项名称
                                    StandardBasicInstrumentInfo paramInfo = standardBasicInstrumentInfoService
                                            .getById(parameterId);
                                    if (paramInfo != null && paramInfo.getProjectName() != null) {
                                        autoResultDto.setParamKey(paramInfo.getProjectName());
                                    } else {
                                        autoResultDto.setParamKey("param_" + parameterId);
                                    }

                                    result.add(autoResultDto);
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取设备测试数据失败: {}", e.getMessage(), e);
        }

        // 确保为每个参数ID返回结果
        if (result.size() < parameterIds.size()) {
            for (Long parameterId : parameterIds) {
                boolean exists = false;
                for (AutoResultDto dto : result) {
                    if (dto.getId().equals(parameterId)) {
                        exists = true;
                        break;
                    }
                }
                if (!exists) {
                    AutoResultDto dto = new AutoResultDto();
                    dto.setId(parameterId);
                    dto.setResult(String.valueOf((int) (Math.random() * 100)));

                    // 获取参数对应的测试项名称
                    StandardBasicInstrumentInfo paramInfo = standardBasicInstrumentInfoService.getById(parameterId);
                    if (paramInfo != null && paramInfo.getProjectName() != null) {
                        dto.setParamKey(paramInfo.getProjectName());
                    } else {
                        dto.setParamKey("param_" + parameterId);
                    }

                    result.add(dto);
                }
            }
        }

        return result;
    }

    @Override
    public Map<String, Object> sendDeviceRequest(String deviceType, Map<String, Object> params) {
        // 🔧 检查是否启用调试模式
        if (mqttDebugService.isDebugEnabled()) {
            log.info("🔧 MQTT调试模式已启用，从调试文件获取响应");

            // 从参数中提取设备ID和表名
            String deviceId = params != null ? String.valueOf(params.get("deviceId")) : null;
            String tableName = params != null ? String.valueOf(params.get("tableName")) : null;

            // 如果没有设备ID，尝试从其他字段获取
            if (deviceId == null || "null".equals(deviceId)) {
                deviceId = params != null ? String.valueOf(params.get("deviceCode")) : "unknown";
            }

            Map<String, Object> debugResponse = mqttDebugService.getDebugResponse(deviceId, deviceType, tableName);
            if (debugResponse != null) {
                log.info("✅ 使用调试响应，设备[{}]类型[{}]表名[{}]", deviceId, deviceType, tableName);
                return debugResponse;
            } else {
                log.warn("⚠️ 未找到匹配的调试响应，设备[{}]类型[{}]表名[{}]，将继续使用真实MQTT请求",
                    deviceId, deviceType, tableName);
            }
        }

        // 检查MQTT客户端是否已初始化
        if (!initialized.get()) {
            log.warn("MQTT客户端未初始化，尝试初始化...");
            init();
            if (!initialized.get()) {
                log.error("MQTT客户端初始化失败，无法发送请求");
                return null;
            }
        }

        // 查询设备对接规范
        List<DeviceIntegrationConfig> configList = deviceIntegrationConfigService.list(
                new LambdaQueryWrapper<DeviceIntegrationConfig>()
                        .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                        .eq(DeviceIntegrationConfig::getStatus, "enabled"));

        if (CollUtil.isEmpty(configList)) {
            log.error("未找到设备对接规范: {}", deviceType);
            return null;
        }

        // 使用第一个配置
        DeviceIntegrationConfig config = configList.get(0);

        // 生成消息ID（添加环境前缀）
        String messageId = clientIdPrefix + "_" + System.currentTimeMillis();
        messageId = String.valueOf(System.currentTimeMillis());

        // 创建CountDownLatch，用于同步等待响应
        CountDownLatch latch = new CountDownLatch(1);
        latchMap.put(messageId, latch);

        try {
            // 将参数添加到映射中，用于替换模板
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("deviceCode", config.getDeviceCode());
            templateParams.put("productCode", config.getProductCode());
            templateParams.put("typeCode", config.getTypeCode());
            templateParams.put("messageId", messageId);
            if (params != null) {
                templateParams.putAll(params);
            }

            // 替换请求模板中的占位符
            String requestTemplate = config.getRequestTemplate();
            String jsonMessage = replaceTemplateParams(requestTemplate, templateParams);

            // 格式化打印JSON，方便查看
            log.info("\n======================设备请求（{}）======================\n", deviceType);
            log.info("\n{}", jsonMessage);
            log.info("\n======================请求结束======================\n");

            // 发送消息
            publishMessage(downTopic, jsonMessage);

            // 等待响应
            boolean received = latch.await(requestTimeout, TimeUnit.SECONDS);

            // 获取响应
            Map<String, Object> response = responseMap.remove(messageId);

            if (!received || response == null) {
                log.error("请求超时或未收到响应: {}", messageId);
                return null;
            }

            return response;

        } catch (Exception e) {
            log.error("发送设备请求失败: {}", e.getMessage(), e);
            return null;
        } finally {
            // 清理资源
            latchMap.remove(messageId);
        }
    }

    /**
     * 处理响应消息
     *
     * @param message 响应消息
     */
    private void handleResponse(String message) {
        try {
            log.info("\n======================设备响应开始======================\n");
            log.info("\n{}", message);
            log.info("\n======================设备响应结束======================\n");
            if (StrUtil.isBlank(message)) {
                log.warn("接收到空消息");
                return;
            }

            // 解析JSON消息
            @SuppressWarnings("unchecked")
            Map<String, Object> msgMap = JSONUtil.toBean(message, HashMap.class);

            // 获取header和body
            @SuppressWarnings("unchecked")
            Map<String, Object> header = (Map<String, Object>) msgMap.get("header");
            @SuppressWarnings("unchecked")
            Map<String, Object> body = (Map<String, Object>) msgMap.get("body");

            if (header == null) {
                log.warn("消息格式不正确，缺少header");
                return;
            }

            // 获取设备ID
            String deviceIdStr = (String) header.get("deviceId");

            // 兼容两种消息类型字段：typeCode 和 messageType
            String typeCode = (String) header.get("typeCode");
            String messageType = (String) header.get("messageType");

            // 如果 typeCode 为空，但 messageType 不为空，则使用 messageType
            if (typeCode == null && messageType != null) {
                typeCode = messageType;
                log.info("使用messageType作为消息类型: {}", typeCode);
            }

            // 获取消息ID
            Object messageIdObj = header.get("messageId");
            String messageId = messageIdObj != null ? String.valueOf(messageIdObj) : null;

            if (messageId == null) {
                log.warn("消息格式不正确，缺少messageId");
                return;
            }

            // 打印接收到的消息信息
            log.info("✨✨✨ 收到MQTT消息，消息ID: {}, 类型: {}, 设备ID: {}", messageId, typeCode, deviceIdStr);

            // 如果是 callFunction 类型的消息，处理 outputParams
            if (("callFunction".equals(typeCode) || "callFunction_response".equals(typeCode)) && body != null) {
                log.info("🔥🔥🔥 收到函数调用消息，消息ID: {}", messageId);

                // 获取错误消息
                String resultMessage = (String) body.get("resultMessage");

                // 判断是否是命令发送确认消息，如果是则不处理
                if (resultMessage != null && resultMessage.contains("等待设备响应")) {
                    log.info("📡 该消息是命令发送确认消息，不存入响应映射，消息ID: {}", messageId);
                    // 不将这种消息存入responseMap，因为这只是命令发送确认，不是真正的数据响应
                    return;
                }

                // 检查是否没有outputParams，如果没有则不存入响应映射
                if (!body.containsKey("outputParams") || body.get("outputParams") == null) {
                    log.info("⚠️ 该消息没有outputParams，不存入响应映射，消息ID: {}", messageId);
                    // 不将这种消息存入responseMap
                    return;
                }

                // 检查outputParams是否包含body字段
                @SuppressWarnings("unchecked")
                Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");
                if (!outputParams.containsKey("body") || outputParams.get("body") == null) {
                    log.info("⚠️ outputParams中没有body字段，不存入响应映射，消息ID: {}", messageId);
                    // 不将这种消息存入responseMap
                    return;
                }

                log.info("✅ 收到有效的数据响应，消息ID: {}", messageId);

                // 检查响应状态码
                Object resultCodeObj = body.get("resultCode");
                int resultCode = 0;
                if (resultCodeObj != null) {
                    if (resultCodeObj instanceof Number) {
                        resultCode = ((Number) resultCodeObj).intValue();
                    } else {
                        try {
                            resultCode = Integer.parseInt(String.valueOf(resultCodeObj));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析状态码: {}", resultCodeObj);
                        }
                    }
                }

                // 如果状态码是错误，抛出异常
                if (resultCode >= 400) {
                    log.error("设备响应错误，状态码: {}, 错误信息: {}", resultCode, resultMessage);
                    throw new IRuntimeException(resultMessage != null ? resultMessage : "设备响应错误，状态码: " + resultCode);
                }

                // 检查outputParams中的状态
                Object statusObj = outputParams.get("_status");
                int status = 0;
                if (statusObj != null) {
                    if (statusObj instanceof Number) {
                        status = ((Number) statusObj).intValue();
                    } else {
                        try {
                            status = Integer.parseInt(String.valueOf(statusObj));
                        } catch (NumberFormatException e) {
                            log.warn("无法解析outputParams中的状态码: {}", statusObj);
                        }
                    }
                }

                // 如果outputParams中的状态码是错误，抛出异常
                if (status >= 400) {
                    String msg = (String) outputParams.get("_msg");
                    log.error("设备响应outputParams错误，状态码: {}, 错误信息: {}", status, msg);
                    throw new IRuntimeException(msg != null ? msg : "设备响应outputParams错误，状态码: " + status);
                }

                handleFunctionCallResponse(body);

                // 将所有有效的 callFunction 类型的消息存入响应映射
                responseMap.put(messageId, msgMap);

                // 查找可能的原始消息ID
                Object originalMsgIdObj = body.get("originalMessageId");
                String originalMsgId = originalMsgIdObj != null ? String.valueOf(originalMsgIdObj) : null;

                if (originalMsgId != null) {
                    log.info("🔥🔥🔥 原始消息ID: {}", originalMsgId);
                    // 将响应也存入原始消息ID的映射
                    responseMap.put(originalMsgId, msgMap);

                    // 释放等待原始消息ID的线程
                    CountDownLatch originalLatch = latchMap.get(originalMsgId);
                    if (originalLatch != null) {
                        log.info("释放原始消息ID的等待线程，消息ID={}", originalMsgId);
                        originalLatch.countDown();
                    }
                }
            }

            // 存储响应到同步响应映射
            responseMap.put(messageId, msgMap);

            // 如果存在对应的设备ID映射，则存储到设备响应映射
            Long deviceId = requestDeviceMap.get(messageId);
            if (deviceId != null) {
                log.info("收到设备ID={}的异步响应，消息ID={}", deviceId, messageId);
                deviceResponseMap.put(deviceId, msgMap);
            }

            // 📁 自动保存设备响应到调试文件（如果启用了自动保存）
            try {
                if (mqttDebugService != null && deviceIdStr != null) {
                    // 尝试从header中获取设备类型信息
                    String deviceType = (String) header.get("product");
                    if (deviceType == null) {
                        deviceType = typeCode; // 使用消息类型作为设备类型
                    }

                    // 尝试从body中获取表名信息
                    String tableName = null;
                    if (body != null && body.containsKey("outputParams")) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");
                        if (outputParams != null) {
                            tableName = (String) outputParams.get("tableName");
                        }
                    }

                    // 保存响应到调试文件
                    String savedPath = mqttDebugService.saveDeviceResponse(
                        deviceIdStr, deviceType, messageId, msgMap, tableName);

                    if (savedPath != null) {
                        log.debug("📁 设备响应已保存到调试文件: {}", savedPath);
                    }
                }
            } catch (Exception e) {
                log.warn("⚠️ 保存设备响应到调试文件失败: {}", e.getMessage());
            }

            // 释放等待的线程
            CountDownLatch latch = latchMap.get(messageId);
            if (latch != null) {
                log.info("释放等待线程，消息ID={}", messageId);
                latch.countDown();
            }
        } catch (Exception e) {
            log.error("处理响应消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据messageId判断消息来源环境
     * 
     * @param messageId 消息ID
     * @return 环境标识，如"test"、"prod"等
     */
    private String getMessageEnvironment(String messageId) {
        if (messageId == null) {
            return "unknown";
        }

        if (messageId.startsWith("jz_westcatr_test_")) {
            return "test";
        } else if (messageId.startsWith("jz_westcatr_prod_")) {
            return "prod";
        } else if (messageId.startsWith("jz_westcatr_")) {
            // 兼容旧消息格式
            return "legacy";
        } else {
            return "unknown";
        }
    }

    /**
     * 处理函数调用响应消息
     *
     * @param body 响应消息体
     */
    private void handleFunctionCallResponse(Map<String, Object> body) {
        try {
            log.debug("开始处理函数调用响应");

            // 提取重要字段
            String functionName = (String) body.get("functionName");
            String functionIdentifier = (String) body.get("functionIdentifier");
            String resultMessage = (String) body.get("resultMessage");
            Object resultCode = body.get("resultCode");

            log.debug("函数名称: {}, 函数标识符: {}, 结果代码: {}, 结果消息: {}",
                    functionName, functionIdentifier, resultCode, resultMessage);

            // 处理输入参数
            @SuppressWarnings("unchecked")
            Map<String, Object> inputParams = (Map<String, Object>) body.get("inputParams");
            if (inputParams != null) {
                log.debug("输入参数: {}", JSONUtil.toJsonPrettyStr(inputParams));
            }

            // 重点处理输出参数
            Object outputParamsObj = body.get("outputParams");
            if (outputParamsObj != null) {
                log.debug("\n======================outputParams开始======================\n");
                log.debug("outputParams: {}", JSONUtil.toJsonPrettyStr(outputParamsObj));

                // 如果是一个Map对象
                if (outputParamsObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> outputParams = (Map<String, Object>) outputParamsObj;

                    // 如果有body字段，尝试解析并格式化打印
                    if (outputParams.containsKey("body")) {
                        Object bodyObj = outputParams.get("body");
                        log.debug("\n======================body内容开始======================\n");

                        // 处理body可能是字符串或Map的情况
                        if (bodyObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> bodyMap = (Map<String, Object>) bodyObj;
                            log.debug("body (Map类型): {}", JSONUtil.toJsonPrettyStr(bodyMap));

                            // 如果有content字段，尝试解析并格式化打印
                            if (bodyMap.containsKey("content")) {
                                Object contentObj = bodyMap.get("content");
                                log.debug("\n======================content内容开始======================\n");

                                try {
                                    // 尝试解析content为JSON
                                    String contentStr = String.valueOf(contentObj);
                                    if (contentStr.startsWith("[") || contentStr.startsWith("{")) {
                                        Object contentParsed = JSONUtil.parse(contentStr);
                                        log.debug("content解析结果: {}", JSONUtil.toJsonPrettyStr(contentParsed));
                                    } else {
                                        log.debug("content不是JSON格式: {}", contentStr);
                                    }
                                } catch (Exception e) {
                                    log.warn("解析content失败: {}", e.getMessage());
                                    log.debug("content原始内容: {}", contentObj);
                                }

                                log.debug("\n======================content内容结束======================\n");
                            }
                        } else if (bodyObj instanceof String) {
                            String bodyStr = String.valueOf(bodyObj);
                            log.debug("body (字符串类型): {}", bodyStr);

                            try {
                                // 尝试解析body字符串为JSON
                                if (bodyStr.startsWith("[") || bodyStr.startsWith("{")) {
                                    Object bodyParsed = JSONUtil.parse(bodyStr);
                                    log.debug("body解析结果: {}", JSONUtil.toJsonPrettyStr(bodyParsed));
                                }
                            } catch (Exception e) {
                                log.warn("解析body字符串失败: {}", e.getMessage());
                            }
                        } else {
                            log.debug("body类型: {}, 值: {}",
                                    bodyObj != null ? bodyObj.getClass().getName() : "null",
                                    bodyObj);
                        }

                        log.debug("\n======================body内容结束======================\n");
                    }
                } else if (outputParamsObj instanceof String) {
                    String outputParamsStr = (String) outputParamsObj;
                    log.debug("outputParams (字符串类型): {}", outputParamsStr);

                    try {
                        // 尝试解析outputParams字符串为JSON
                        if (outputParamsStr.startsWith("[") || outputParamsStr.startsWith("{")) {
                            Object outputParamsParsed = JSONUtil.parse(outputParamsStr);
                            log.debug("outputParams解析结果: {}", JSONUtil.toJsonPrettyStr(outputParamsParsed));
                        }
                    } catch (Exception e) {
                        log.warn("解析outputParams字符串失败: {}", e.getMessage());
                    }
                } else {
                    log.debug("outputParams类型: {}",
                            outputParamsObj != null ? outputParamsObj.getClass().getName() : "null");
                }

                log.debug("\n======================outputParams结束======================\n");
            } else {
                log.debug("消息中没有outputParams字段");
            }

            log.debug("函数调用响应处理完成");

        } catch (Exception e) {
            log.error("处理函数调用响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 替换模板中的占位符
     *
     * @param template 模板字符串
     * @param params   参数映射
     * @return 替换后的字符串
     */
    private String replaceTemplateParams(String template, Map<String, Object> params) {
        if (StrUtil.isBlank(template) || params == null || params.isEmpty()) {
            return template;
        }

        String result = template;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() != null ? String.valueOf(entry.getValue()) : "";
            result = result.replace("${" + key + "}", value);
        }

        return result;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    @Override
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;

        if (enabled && !initialized.get() && !connecting.get()) {
            // 如果启用了MQTT客户端，则尝试初始化连接
            log.info("MQTT客户端已启用，尝试初始化连接...");
            init();
        } else if (!enabled && mqttClient != null && mqttClient.isConnected()) {
            // 如果禁用了MQTT客户端，则关闭连接
            log.info("MQTT客户端已禁用，关闭连接...");
            try {
                mqttClient.disconnect();
                log.info("MQTT客户端连接已断开");
            } catch (MqttException e) {
                log.error("MQTT客户端断开连接失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 根据设备类型和测试项名称获取参数原始key
     * 
     * @param deviceType 设备类型
     * @param testName   测试项名称
     * @return 参数原始key
     */
    private String getParamKeyByTestName(String deviceType, String testName) {
        try {
            // 获取设备配置
            DeviceIntegrationConfig config = deviceIntegrationConfigService.getOne(
                    new LambdaQueryWrapper<DeviceIntegrationConfig>()
                            .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                            .eq(DeviceIntegrationConfig::getStatus, "enabled")
                            .last("LIMIT 1"));

            if (config != null && StrUtil.isNotBlank(config.getParamMapping())) {
                // 解析参数映射
                JSONObject paramMapping = JSONObject.parseObject(config.getParamMapping());

                // 遍历参数映射查找对应的原始key
                for (Map.Entry<String, Object> entry : paramMapping.entrySet()) {
                    if (testName.equals(entry.getKey())) {
                        return entry.getValue().toString();
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取参数原始key失败: {}", e.getMessage(), e);
        }

        // 默认返回测试项名称
        return testName;
    }

    @Override
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * 获取MQTT错误代码的描述信息
     * 
     * @param reasonCode MQTT错误代码
     * @return 错误描述
     */
    private String getMqttErrorDescription(int reasonCode) {
        switch (reasonCode) {
            case MqttException.REASON_CODE_BROKER_UNAVAILABLE:
                return "代理不可用";
            case MqttException.REASON_CODE_CLIENT_EXCEPTION:
                return "客户端异常";
            case MqttException.REASON_CODE_CONNECT_IN_PROGRESS:
                return "连接正在进行中";
            case MqttException.REASON_CODE_CLIENT_DISCONNECTING:
                return "客户端正在断开连接";
            case MqttException.REASON_CODE_FAILED_AUTHENTICATION:
                return "认证失败 - 检查用户名和密码";
            case MqttException.REASON_CODE_NOT_AUTHORIZED:
                return "未授权 - 客户端无权执行请求的操作";
            case MqttException.REASON_CODE_INVALID_CLIENT_ID:
                return "无效的客户端ID";
            case MqttException.REASON_CODE_INVALID_PROTOCOL_VERSION:
                return "无效的协议版本";
            case MqttException.REASON_CODE_INVALID_MESSAGE:
                return "无效的消息";
            case MqttException.REASON_CODE_SOCKET_FACTORY_MISMATCH:
                return "Socket工厂不匹配";
            case MqttException.REASON_CODE_SSL_CONFIG_ERROR:
                return "SSL配置错误";
            case MqttException.REASON_CODE_CLIENT_TIMEOUT:
                return "客户端超时";
            default:
                return "未知错误代码: " + reasonCode;
        }
    }

    @Override
    public String sendDeviceRequestAsync(Long deviceId, String deviceType, Map<String, Object> params) {
        // 检查MQTT客户端是否已初始化
        if (!initialized.get()) {
            log.warn("MQTT客户端未初始化，尝试初始化...");
            init();
            if (!initialized.get()) {
                log.error("MQTT客户端初始化失败，无法发送异步请求");
                return null;
            }
        }

        // 查询设备对接规范
        List<DeviceIntegrationConfig> configList = deviceIntegrationConfigService.list(
                new LambdaQueryWrapper<DeviceIntegrationConfig>()
                        .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                        .eq(DeviceIntegrationConfig::getStatus, "enabled"));

        if (CollUtil.isEmpty(configList)) {
            log.error("未找到设备对接规范: {}", deviceType);
            return null;
        }

        // 使用第一个配置
        DeviceIntegrationConfig config = configList.get(0);

        // 生成消息ID（添加环境前缀）
        String messageId = clientIdPrefix + "_" + System.currentTimeMillis();
        // messageId = String.valueOf(System.currentTimeMillis());

        try {
            // 将参数添加到映射中，用于替换模板
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("deviceCode", config.getDeviceCode());
            templateParams.put("productCode", config.getProductCode());
            templateParams.put("typeCode", config.getTypeCode());
            templateParams.put("messageId", messageId);
            if (params != null) {
                templateParams.putAll(params);
            }

            // 替换请求模板中的占位符
            String requestTemplate = config.getRequestTemplate();
            String jsonMessage = replaceTemplateParams(requestTemplate, templateParams);

            // 格式化打印JSON，方便查看
            log.info("\n======================异步设备请求（{}）======================\n", deviceType);
            log.info("\n{}", jsonMessage);
            log.info("\n======================请求结束======================\n");

            // 存储设备ID和请求ID的映射关系
            requestDeviceMap.put(messageId, deviceId);

            // 发送消息
            publishMessage(downTopic, jsonMessage);

            log.debug("异步请求已发送，设备ID={}，消息ID={}", deviceId, messageId);

            return messageId;

        } catch (Exception e) {
            log.error("发送异步设备请求失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getAsyncResponse(String requestId) {
        if (requestId == null) {
            return null;
        }
        return responseMap.get(requestId);
    }

    @Override
    public Map<String, Object> getLatestDeviceResponse(Long deviceId) {
        if (deviceId == null) {
            return null;
        }
        return deviceResponseMap.get(deviceId);
    }
}
