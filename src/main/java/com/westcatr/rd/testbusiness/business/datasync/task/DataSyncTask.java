package com.westcatr.rd.testbusiness.business.datasync.task;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.westcatr.rd.testbusiness.business.datasync.service.DataSyncService;
import com.westcatr.rd.testbusiness.configs.DataSyncConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据同步定时任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "data.sync", name = "enabled", havingValue = "true", matchIfMissing = true)
public class DataSyncTask {

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private DataSyncConfig dataSyncConfig;

    /**
     * 执行数据同步任务
     * 根据配置的间隔时间执行同步
     */
    @Scheduled(fixedRateString = "#{${data.sync.interval-seconds:2} * 1000}")
    public void executeDataSync() {
        if (!dataSyncConfig.isEnabled()) {
            log.debug("📴 数据同步功能已禁用，跳过同步任务");
            return;
        }

        try {
            log.debug("🔄 开始执行定时数据同步任务");
            
            Map<String, Object> result = dataSyncService.syncData();
            
            int synced = (Integer) result.getOrDefault("synced", 0);
            int skipped = (Integer) result.getOrDefault("skipped", 0);
            long costTime = (Long) result.getOrDefault("costTime", 0L);
            
            if (synced > 0) {
                log.info("📊 定时同步完成 - 同步: {} 条，跳过: {} 条，耗时: {}ms", 
                        synced, skipped, costTime);
            } else {
                log.debug("📊 定时同步完成 - 无新数据，耗时: {}ms", costTime);
            }
            
        } catch (Exception e) {
            log.error("❌ 定时数据同步任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 数据源连接状态检查任务
     * 每5分钟检查一次数据源连接状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void checkDataSourceStatus() {
        if (!dataSyncConfig.isEnabled()) {
            return;
        }

        try {
            Map<String, Boolean> status = dataSyncService.checkDataSourceStatus();
            
            boolean allHealthy = status.values().stream().allMatch(Boolean::booleanValue);
            
            if (!allHealthy) {
                log.warn("⚠️ 数据源连接状态检查 - PostgreSQL: {}, MySQL: {}, Redis: {}", 
                        status.get("postgresql") ? "✅" : "❌",
                        status.get("mysql") ? "✅" : "❌", 
                        status.get("redis") ? "✅" : "❌");
            } else {
                log.debug("✅ 所有数据源连接正常");
            }
            
        } catch (Exception e) {
            log.error("❌ 数据源状态检查失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 同步统计信息记录任务
     * 每小时记录一次同步统计信息
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时执行
    public void logSyncStatistics() {
        if (!dataSyncConfig.isEnabled()) {
            return;
        }

        try {
            Map<String, Object> stats = dataSyncService.getSyncStatistics();
            Map<String, Object> status = dataSyncService.getSyncStatus();
            
            log.info("📈 数据同步统计报告 - 状态: {}, 累计同步: {} 条, 累计跳过: {} 条, 最后同步耗时: {}ms", 
                    status.get("status"),
                    stats.get("totalSynced"),
                    stats.get("totalSkipped"),
                    stats.get("lastCostTime"));
                    
        } catch (Exception e) {
            log.error("❌ 记录同步统计信息失败: {}", e.getMessage(), e);
        }
    }
}
