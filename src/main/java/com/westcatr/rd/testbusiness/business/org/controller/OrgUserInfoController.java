package com.westcatr.rd.testbusiness.business.org.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;
import java.util.Map;

import com.westcatr.rd.testbusiness.business.org.pojo.vo.UserDepInfoVO;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.resetPasswordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.dto.UserInfoNoPhotoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgUserInfoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.OrgUserInfoVO;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;
import com.westcatr.rd.testbusiness.business.org.service.UserPasswordService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * OrgUserInfo 控制器
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Validated
@Tag(name = "组织架构——人员表接口", description = "人员表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class OrgUserInfoController {

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private UserPasswordService userPasswordService;

    @Operation(summary = "获取人员表分页数据")
    @PostMapping("/orgUserInfo/page")
    public IResult<IPage<OrgUserInfo>> getOrgUserInfoPage(@RequestBody OrgUserInfoQuery query) {
        return IResult.ok(orgUserInfoService.entityPage(query));
    }

    @Operation(summary = "获取人员表数据")
    @PostMapping("/orgUserInfo/get")
    public IResult<OrgUserInfo> getOrgUserInfoById(@RequestBody @Id ID id) {
        return IResult.ok(orgUserInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增人员表数据")
    @PostMapping("/orgUserInfo/add")
    public IResult addOrgUserInfo(@RequestBody @Validated(Insert.class) OrgUserInfo param) {
        return IResult.auto(orgUserInfoService.saveEntity(param));
    }

    @Operation(summary = "更新人员表数据")
    @PostMapping("/orgUserInfo/update")
    public IResult updateOrgUserInfoById(@RequestBody @Validated(Update.class) OrgUserInfo param) {
        return IResult.auto(orgUserInfoService.updateEntity(param));
    }

    @Operation(summary = "删除人员表数据")
    @PostMapping("/orgUserInfo/delete")
    public IResult deleteOrgUserInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            orgUserInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取人员表VO分页数据")
    @PostMapping("/orgUserInfo/voPage")
    public IResult<IPage<OrgUserInfoVO>> getOrgUserInfoVoPage(@RequestBody OrgUserInfoQuery query) {
        AssociationQuery<OrgUserInfoVO> associationQuery = new AssociationQuery<>(OrgUserInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取人员表VO数据")
    @PostMapping("/orgUserInfo/getVo")
    public IResult<OrgUserInfoVO> getOrgUserInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<OrgUserInfoVO> associationQuery = new AssociationQuery<>(OrgUserInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "查询不带用户头像的用户信息")
    @PostMapping("/orgUserInfo/userInfoNoPhoto")
    public IResult<List<OrgUserInfo>> userInfoNoPhoto(@RequestBody UserInfoNoPhotoQuery userInfoNoPhotoQuery) {
        return IResult.ok(orgUserInfoService.userInfoNoPhoto(userInfoNoPhotoQuery));
    }

    @Operation(summary = "查询同部门用户")
    @PostMapping("/orgUserInfo/getSameDepUserInfo")
    public IResult<List<OrgUserInfo>> getSameDepUserInfo(@RequestBody @Id ID id){
        return IResult.ok(orgUserInfoService.getSameDepUserInfo(id.longId()));
    }

    /**
     * 查询同角色用户 & 部门
     * @param id
     * @return
     */
    @Operation(summary = "查询同角色用户 & 部门")
    @PostMapping("/orgUserInfo/getUserInfoByRole")
    public IResult<Map<String, List<UserDepInfoVO>>> getUserInfoByRole(@RequestBody @Id ID id){
        return IResult.ok(orgUserInfoService.getUserInfoByRole(id.longId()));
    }


    @Operation(summary = "同步用户头像")
    @GetMapping("/orgUserInfo/syncAvatar")
    public IResult<String> syncAvatar() throws Exception {
        return IResult.ok("成功", userPasswordService.updateAvatar());
    }

    @Operation(summary = "重置密码")
    @PostMapping("/orgUserInfo/resetPassword")
    public IResult<String> resetPassword(@RequestBody resetPasswordVo resetPasswordVo) throws Exception {
        return IResult.ok("成功", userPasswordService.resetPassword(resetPasswordVo));
    }

}
