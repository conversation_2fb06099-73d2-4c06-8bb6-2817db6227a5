package com.westcatr.rd.testbusiness.business.mqtt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.mapper.DeviceIntegrationConfigMapper;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.query.DeviceIntegrationConfigQuery;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备对接规范表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Service
public class DeviceIntegrationConfigServiceImpl extends ServiceImpl<DeviceIntegrationConfigMapper, DeviceIntegrationConfig> implements DeviceIntegrationConfigService {

    @Override
    public IPage<DeviceIntegrationConfig> entityPage(DeviceIntegrationConfigQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<DeviceIntegrationConfig>().create(query));
    }

    @Override
    public DeviceIntegrationConfig getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(DeviceIntegrationConfig param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(DeviceIntegrationConfig param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public DeviceIntegrationConfig getByDeviceTypeAndProductCode(String deviceType, String productCode) {
        return this.getOne(new LambdaQueryWrapper<DeviceIntegrationConfig>()
                .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                .eq(DeviceIntegrationConfig::getProductCode, productCode)
                .eq(DeviceIntegrationConfig::getStatus, "enabled")
                .last("LIMIT 1"));
    }
}
