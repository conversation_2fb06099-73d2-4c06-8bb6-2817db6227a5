package com.westcatr.rd.testbusiness.business.jztask.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—任务列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="荆州—任务列表表查询对象")
public class JzTaskInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "结束时间")
    @QueryCondition
    private Date endDate;

    @QueryCondition
    private Long id;

    @Schema(description = "样品id")
    @QueryCondition
    private Long sampleId;

    @Schema(description = "开始时间")
    @QueryCondition
    private Date startDate;

    @Schema(description = "任务生成时间")
    @QueryCondition
    private Date taskCreationTime;

    @Schema(description = "任务编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String taskNumber;

    @Schema(description = "任务状态（任务待检、任务在检、任务检毕）")
    @QueryCondition
    private String taskStatus;

    @Schema(description = "检测级别")
    @QueryCondition
    private String testLevel;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;
}
