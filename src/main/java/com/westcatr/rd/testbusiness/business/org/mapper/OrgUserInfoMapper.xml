<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.org.mapper.OrgUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="pass_word" property="passWord" />
        <result column="full_name" property="fullName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name, pass_word, full_name, create_time, update_time, phone, email, enable
    </sql>

</mapper>
