package com.westcatr.rd.testbusiness.business.gwstandard.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicProjectParamQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicProjectParamVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectParamService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * StandardBasicProjectParam 控制器
 * 
 * <AUTHOR>
 * @since 2025-04-16
 */
@Validated
@Tag(name = "实验项目参数表接口", description = "实验项目参数表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class StandardBasicProjectParamController {

    @Autowired
    private StandardBasicProjectParamService standardBasicProjectParamService;

    @Operation(summary = "获取实验项目参数表分页数据")
    @PostMapping("/standardBasicProjectParam/page")
    public IResult<IPage<StandardBasicProjectParam>> getStandardBasicProjectParamPage(
            @RequestBody StandardBasicProjectParamQuery query) {
        return IResult.ok(standardBasicProjectParamService.entityPage(query));
    }

    @Operation(summary = "获取实验项目参数表数据")
    @PostMapping("/standardBasicProjectParam/get")
    public IResult<StandardBasicProjectParam> getStandardBasicProjectParamById(@RequestBody @Id ID id) {
        return IResult.ok(standardBasicProjectParamService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增实验项目参数表数据")
    @PostMapping("/standardBasicProjectParam/add")
    public IResult addStandardBasicProjectParam(@RequestBody @Validated(Insert.class) StandardBasicProjectParam param) {
        return IResult.auto(standardBasicProjectParamService.saveEntity(param));
    }

    @Operation(summary = "更新实验项目参数表数据")
    @PostMapping("/standardBasicProjectParam/update")
    public IResult updateStandardBasicProjectParamById(
            @RequestBody @Validated(Update.class) StandardBasicProjectParam param) {
        return IResult.auto(standardBasicProjectParamService.updateEntity(param));
    }

    @Operation(summary = "删除实验项目参数表数据")
    @PostMapping("/standardBasicProjectParam/delete")
    public IResult deleteStandardBasicProjectParamById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            standardBasicProjectParamService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取实验项目参数表VO分页数据")
    @PostMapping("/standardBasicProjectParam/voPage")
    public IResult<IPage<StandardBasicProjectParamVO>> getStandardBasicProjectParamVoPage(
            @RequestBody StandardBasicProjectParamQuery query) {
        AssociationQuery<StandardBasicProjectParamVO> associationQuery = new AssociationQuery<>(
                StandardBasicProjectParamVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取实验项目参数表VO数据")
    @PostMapping("/standardBasicProjectParam/getVo")
    public IResult<StandardBasicProjectParamVO> getStandardBasicProjectParamVoById(@RequestBody @Id ID id) {
        AssociationQuery<StandardBasicProjectParamVO> associationQuery = new AssociationQuery<>(
                StandardBasicProjectParamVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
