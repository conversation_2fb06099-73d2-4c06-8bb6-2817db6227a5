package com.westcatr.rd.testbusiness.business.jztask.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—检项列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "荆州—检项列表表VO对象")
public class JzTaskInspectionItemInfoVO extends JzTaskInspectionItemInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "管理任务信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = JzTaskInfo.class, mainId = "taskId")
    private JzTaskInfo jzTaskInfo;

    @Schema(description = "关联样品信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = SampleInfo.class, mainId = "sampleId")
    private SampleInfo sampleInfo;

    @Schema(description = "文件信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = FileInfo.class, mainId = "fileId")
    private FileInfo fileInfo;

    @Schema(description = "关联工单信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = JzTaskWorkOrderInfo.class, mainId = "workOrderId", relationId = "id")
    private JzTaskWorkOrderInfo jzTaskWorkOrderInfo;

    // userName
    @TableField(exist = false)
    @JoinSelect(joinClass = OrgUserInfo.class, mainId = "userId", field = "full_name")
    private String userName;
}
