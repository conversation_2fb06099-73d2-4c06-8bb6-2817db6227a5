package com.westcatr.rd.testbusiness.business.jzreport.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.data.Pictures;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.file.service.FileInfoService;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInstrumentInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicInstrumentInfoVO;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jzreport.mapper.JzReportInfoMapper;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.JzReportInfoQuery;
import com.westcatr.rd.testbusiness.business.jzreport.service.JzReportInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInspectionItemInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInspectionItemInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInspectionItemInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInspectionItemInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import com.westcatr.rd.testbusiness.business.sample.pojo.vo.SampleInfoVO;
import com.westcatr.rd.testbusiness.poitl.entity.MeasurementWindingResistanceData;
import com.westcatr.rd.testbusiness.poitl.entity.WindingResistanceRow;
import com.westcatr.rd.testbusiness.utils.TemplateProcessUtils;
import com.westcatr.rd.testbusiness.utils.WindingResistanceTableHelper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.log4j.Log4j2;

/**
 * <p>
 * 国王—报告信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Log4j2
@Service
public class JzReportInfoServiceImpl extends ServiceImpl<JzReportInfoMapper, JzReportInfo>
        implements JzReportInfoService {

    @Autowired
    private FileInfoService fileInfoService;

    @Value("${westcatr.boot.file.upload-folder}")
    private String uploadPath;

    @Value("${gw.template.file.path:/opt/templates/gw}")
    private String gwTemplatePath;

    @Autowired
    private JzTaskInspectionItemInfoService jzTaskInspectionItemInfoService;

    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;

    @Override
    public IPage<JzReportInfo> entityPage(JzReportInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<JzReportInfo>().create(query));
    }

    @Override
    public JzReportInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(JzReportInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(JzReportInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public boolean autoReport(Long taskId) {
        JzTaskInfoVO jzTaskInfo = new AssociationQuery<>(JzTaskInfoVO.class).getVo(taskId);
        JzReportInfo jzReportInfo = new JzReportInfo();
        jzReportInfo.setTaskId(taskId);
        jzReportInfo.setSampleId(jzTaskInfo.getSampleId());
        jzReportInfo.setTfQualified("合格");
        // 报告编号
        jzReportInfo.setReportNumber("B" + jzTaskInfo.getTaskNumber());
        IUser iUser = AuthUtil.getUserE();

        // 通过样品查询关联的标准
        SampleInfo sampleInfo = jzTaskInfo.getSampleInfo();
        if (sampleInfo == null) {
            log.warn("未找到样品信息，样品ID: {}", jzTaskInfo.getSampleId());
            sampleInfo = new AssociationQuery<>(SampleInfo.class).getVo(jzTaskInfo.getSampleId());
            if (sampleInfo == null) {
                log.error("无法获取样品信息，样品ID: {}", jzTaskInfo.getSampleId());
                return false;
            }
        }

        // 查找关联的标准信息
        StandardBasicInstrumentInfoVO standardInfo = null;

        // 根据样品名称和规格查询匹配的标准
        StandardBasicInstrumentInfoQuery query = new StandardBasicInstrumentInfoQuery();
        query.setSampleName(sampleInfo.getSampleName()); // 项目名称对应样品名称
        query.setSampleSpecifications(sampleInfo.getSampleModel()); // 样品规格
        query.setTestCapabilityLevel(sampleInfo.getTestType()); // 检测能力级别

        List<StandardBasicInstrumentInfoVO> standardList = new AssociationQuery<>(StandardBasicInstrumentInfoVO.class)
                .voList(query);

        if (CollUtil.isNotEmpty(standardList)) {
            // 按照更新时间倒序排序，取第一个
            standardInfo = standardList.stream()
                    .sorted(Comparator.comparing(StandardBasicInstrumentInfoVO::getUpdateTime,
                            Comparator.nullsLast(Comparator.reverseOrder())))
                    .findFirst()
                    .orElse(null);

            if (standardInfo != null) {
                log.info("找到关联的标准信息: ID={}, 名称={}", standardInfo.getId(), standardInfo.getProjectName());
            }
        }

        // 如果通过样品信息没有找到标准，尝试通过工单查找
        if (standardInfo == null) {
            log.info("通过样品信息未找到标准，尝试通过工单查找");
            JzTaskWorkOrderInfo workOrderInfo = jzTaskWorkOrderInfoService.getOne(
                    new LambdaQueryWrapper<JzTaskWorkOrderInfo>()
                            .eq(JzTaskWorkOrderInfo::getTaskId, taskId)
                            .orderByDesc(JzTaskWorkOrderInfo::getCreateTime)
                            .last("LIMIT 1"));

            if (workOrderInfo != null && StrUtil.isNotBlank(workOrderInfo.getGwBzId())) {
                String standardId = workOrderInfo.getGwBzId();
                try {
                    // 尝试将标准ID转换为Long类型
                    Long standardIdLong = Long.parseLong(standardId);
                    // 查询标准信息
                    standardInfo = new AssociationQuery<>(StandardBasicInstrumentInfoVO.class).getVo(standardIdLong);
                    log.info("通过工单找到关联的标准信息: ID={}, 名称={}", standardIdLong,
                            standardInfo != null ? standardInfo.getProjectName() : "未找到");
                } catch (NumberFormatException e) {
                    log.warn("标准ID不是有效的数字: {}", standardId);
                }
            }
        }

        jzTaskInfo.setFindStandardBasicInstrumentInfoVO(standardInfo);

        // 处理原始记录模板
        String originalRecordFileName = "物资抽检原始记录(油浸式配电变压器).docx";
        String resourcePath = "国网模板文件/原始记录/" + originalRecordFileName;
        String showOriginalRecordFileName = jzTaskInfo.getSampleName() + jzTaskInfo.getTestLevel() + "原始记录.docx";

        try {
            // 首先尝试使用标准关联的原始记录模板
            InputStream inputStream = null;

            if (standardInfo != null && standardInfo.getOriginalRecordTemplateFileId() != null) {
                // 获取标准关联的原始记录模板文件
                FileInfo templateFile = fileInfoService.getById(standardInfo.getOriginalRecordTemplateFileId());
                if (templateFile != null) {
                    String templateFilePath = uploadPath + templateFile.getFilePath();
                    if (FileUtil.exist(templateFilePath)) {
                        inputStream = new FileInputStream(new File(templateFilePath));
                        originalRecordFileName = templateFile.getFileName();
                        log.info("使用标准关联的原始记录模板文件: {}", templateFilePath);
                    }
                }
            }

            // 如果标准关联的模板不存在，则使用资源目录中的模板
            if (inputStream == null) {
                inputStream = ResourceUtil.getStream(resourcePath);
                log.info("使用资源目录原始记录模板文件: {}", resourcePath);
            }

            if (inputStream == null) {
                log.error("未找到原始记录模板文件");
                return false;
            }

            // 创建原始记录基础数据
            Map<String, Object> originalRecordBaseData = new HashMap<>();
            SampleInfo sampleInfoData = jzTaskInfo.getSampleInfo();

            // 添加基础数据
            originalRecordBaseData.put("sampleName", sampleInfoData.getSampleName());
            originalRecordBaseData.put("sampleModelNumber", sampleInfoData.getSampleModel());
            originalRecordBaseData.put("clientOrganization", "--");
            originalRecordBaseData.put("blindSampleNumber", sampleInfoData.getBlindSampleNumber());
            originalRecordBaseData.put("productionUnit", "--");
            originalRecordBaseData.put("detectionCategory", "送样实验（" + sampleInfoData.getTestType() + "类）");
            originalRecordBaseData.put("reportNumber", "B" + jzTaskInfo.getTaskNumber());
            originalRecordBaseData.put("testDate", DateUtil.format(new Date(), "yyyy年MM月dd日"));
            originalRecordBaseData.put("testDateTwo", DateUtil.format(new Date(), "yyyy.MM.dd"));
            originalRecordBaseData.put("sampleArrivalDate",
                    DateUtil.format(sampleInfoData.getCreateTime(), "yyyy.MM.dd"));
            originalRecordBaseData.put("sampleNumber", sampleInfoData.getSecondaryBlindSampleNumber());
            originalRecordBaseData.put("remark", sampleInfoData.getSampleRemark());

            // 获取当前日期+3个月
            Date threeMonthsLater = DateUtil.offsetMonth(new Date(), 3);
            originalRecordBaseData.put("instrumentValidityDate", DateUtil.format(threeMonthsLater, "yyyy.MM.dd"));

            // 使用通用方法构建完整原始记录数据（自动合并自定义模板值）
            Map<String, Object> originalRecordData = TemplateProcessUtils.buildOriginalRecordTemplateData(
                    originalRecordBaseData, standardInfo);

            // 创建MultipartFile对象
            MultipartFile mockMultipartFile = new MockMultipartFile(
                    originalRecordFileName,
                    showOriginalRecordFileName,
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    inputStream);

            // 上传文件
            FileInfo fileInfo = fileInfoService.upload(
                    mockMultipartFile,
                    originalRecordFileName,
                    3, // 文件类型
                    1, // 业务类型
                    iUser, // 用户信息
                    false // 是否私有
            );

            // 获取上传后的文件路径
            String uploadedFilePath = uploadPath + fileInfo.getFilePath();

            // 使用工具类处理原始记录模板（应用数据替换标签）
            TemplateProcessUtils.processTemplate(
                    uploadedFilePath, // 源文件路径
                    uploadedFilePath, // 目标文件路径（覆盖原文件）
                    originalRecordData, // 数据
                    standardInfo, // 标准信息，用于处理自定义模板值
                    false // 是原始记录模板而非报告模板
            );

            // 将上传后的文件ID赋值给原始记录ID
            jzReportInfo.setOriginalRecordFileId(fileInfo.getId());

        } catch (Exception e) {
            log.error("原始记录处理失败", e);
            return false;
        }

        // 处理报告模板
        String reportFileName = "电网物资抽检试验报告.docx";
        String resourceReportPath = "国网模板文件/报告/" + reportFileName;
        String showReportFileName = jzTaskInfo.getSampleName() + jzTaskInfo.getTestLevel() + "试验报告.docx";

        try {
            // 首先尝试使用标准关联的报告模板
            InputStream inputStream = null;

            if (standardInfo != null && standardInfo.getReportTemplateFileId() != null) {
                // 获取标准关联的报告模板文件
                FileInfo templateFile = fileInfoService.getById(standardInfo.getReportTemplateFileId());
                if (templateFile != null) {
                    String templateFilePath = uploadPath + templateFile.getFilePath();
                    if (FileUtil.exist(templateFilePath)) {
                        inputStream = new FileInputStream(new File(templateFilePath));
                        reportFileName = templateFile.getFileName();
                        log.info("使用标准关联的报告模板文件: {}", templateFilePath);
                    }
                }
            }

            // 如果标准关联的模板不存在，则尝试从外部配置路径获取最新的模板文件
            if (inputStream == null) {
                String externalReportPath = gwTemplatePath;
                File externalReportDir = new File(externalReportPath);

                if (externalReportDir.exists() && externalReportDir.isDirectory()) {
                    // 获取目录下所有.docx文件
                    List<File> docxFiles = FileUtil.loopFiles(externalReportDir,
                            file -> file.getName().toLowerCase().endsWith(".docx"));

                    if (!docxFiles.isEmpty()) {
                        // 按最后修改时间排序，获取最新的文件
                        File latestDocx = docxFiles.stream()
                                .sorted(Comparator.comparing(File::lastModified).reversed())
                                .findFirst()
                                .orElse(null);

                        if (latestDocx != null) {
                            inputStream = new FileInputStream(latestDocx);
                            reportFileName = latestDocx.getName();
                            log.info("使用外部模板文件: {}", latestDocx.getAbsolutePath());
                        }
                    }
                }
            }

            // 如果外部模板不存在，则使用资源目录中的模板
            if (inputStream == null) {
                inputStream = ResourceUtil.getStream(resourceReportPath);
                log.info("使用资源目录模板文件: {}", resourceReportPath);
            }

            if (inputStream == null) {
                log.error("未找到报告模板文件");
                return false;
            }

            // 创建MultipartFile对象
            MultipartFile mockMultipartFile = new MockMultipartFile(
                    reportFileName,
                    showReportFileName,
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    inputStream);

            // 上传文件
            FileInfo fileInfo = fileInfoService.upload(
                    mockMultipartFile,
                    reportFileName, // 修正这里，使用正确的文件名
                    3, // 文件类型
                    1, // 业务类型
                    iUser, // 用户信息
                    false // 是否私有
            );

            // 生成雷冲实验报告
            this.generateLeiChongReport(jzTaskInfo, uploadPath + fileInfo.getFilePath());

            // 将上传后的文件ID赋值给报告文件ID
            jzReportInfo.setReportFileId(fileInfo.getId());
        } catch (Exception e) {
            log.error("上传报告模板失败", e);
            return false;
        }
        // 保存报告信息
        return this.save(jzReportInfo);
    }

    // 生成雷冲实验报告
    public void generateLeiChongReport(JzTaskInfoVO jzTjzTanfokInfo, String fileSavePath) {
        // 创建基础数据Map
        Map<String, Object> baseData = new HashMap<>();
        StandardBasicInstrumentInfo standardInfo = jzTjzTanfokInfo.getFindStandardBasicInstrumentInfoVO();
        SampleInfo sampleInfo = jzTjzTanfokInfo.getSampleInfo();

        // 添加基础数据到Map
        // 样品信息
        baseData.put("sampleName", sampleInfo.getSampleName());
        baseData.put("sampleModelNumber", sampleInfo.getSampleModel());
        baseData.put("clientOrganization", "--");
        baseData.put("blindSampleNumber", sampleInfo.getBlindSampleNumber());
        baseData.put("productionUnit", "--");
        baseData.put("detectionCategory", "送样实验（" + sampleInfo.getTestType() + "类）");

        // 报告编号
        baseData.put("reportNumber", "B" + jzTjzTanfokInfo.getTaskNumber());

        // 检测时间信息
        baseData.put("testDate", DateUtil.format(new Date(), "yyyy年MM月dd日"));
        baseData.put("testDateTwo", DateUtil.format(new Date(), "yyyy.MM.dd"));
        baseData.put("sampleArrivalDate", DateUtil.format(sampleInfo.getCreateTime(), "yyyy.MM.dd"));
        baseData.put("sampleNumber", sampleInfo.getSecondaryBlindSampleNumber());
        baseData.put("remark", sampleInfo.getSampleRemark());

        // 获取当前日期+3个月
        Date threeMonthsLater = DateUtil.offsetMonth(new Date(), 3);
        baseData.put("instrumentValidityDate", DateUtil.format(threeMonthsLater, "yyyy.MM.dd"));

        // 样品图片和铭牌图片
        SampleInfoVO sampleInfoVO = new AssociationQuery<>(SampleInfoVO.class).getVo(sampleInfo.getId());
        if (sampleInfoVO != null) {
            // 样品图
            if (sampleInfoVO.getSamplePicInfo() != null) {
                String filePath = sampleInfoVO.getSamplePicInfo().getFilePath();
                if (FileUtil.exist(uploadPath + filePath)) {
                    int[] imageDimensions = calculateImageDimensions(uploadPath + filePath, 378);
                    baseData.put("samplePicOne", Pictures.ofLocal(uploadPath + filePath)
                            .size(imageDimensions[0], imageDimensions[1]).create());
                }
            }
            // 铭牌图
            if (sampleInfoVO.getNameplateFileInfo() != null) {
                String filePath = sampleInfoVO.getNameplateFileInfo().getFilePath();
                if (FileUtil.exist(uploadPath + filePath)) {
                    int[] imageDimensions = calculateImageDimensions(uploadPath + filePath, 378);
                    baseData.put("samplePicTwo", Pictures.ofLocal(uploadPath + filePath)
                            .size(imageDimensions[0], imageDimensions[1]).create());
                }
            }
        }

        // 测试数据（默认值）
        baseData.put("leiChongOne", "/");
        baseData.put("leiChongTwo", "/");
        baseData.put("leiChongThree", "/");
        baseData.put("leiChongFour", "/");
        baseData.put("leiChongFive", "/");
        baseData.put("maxUnbalanceRateHighVoltage", "/");
        baseData.put("maxUnbalanceRateLowVoltage", "/");
        baseData.put("permissibleValueHighVoltageUnbalanceRate", "/");
        baseData.put("permissibleValueLowVoltageUnbalanceRate", "/");

        // 获取任务检测项目信息
        List<JzTaskInspectionItemInfo> jzTaskInspectionItemInfos = jzTaskInspectionItemInfoService
                .list(new LambdaQueryWrapper<>(JzTaskInspectionItemInfo.class)
                        .eq(JzTaskInspectionItemInfo::getTaskId, jzTjzTanfokInfo.getId()));

        // 如果存在检测项目信息，添加到数据Map
        if (CollUtil.isNotEmpty(jzTaskInspectionItemInfos)) {
            // 这部分逻辑与原来相同，处理各种检测项目数据
            // one
            JzTaskInspectionItemInfo jzTaskInspectionItemInfoOne = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压对低压及地".equals(x.getTestName()) && "R60(MΩ)".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (jzTaskInspectionItemInfoOne != null) {
                baseData.put("leiChongOne",
                        jzTaskInspectionItemInfoOne.getTestResults() + "(MΩ)");
            }

            // two
            JzTaskInspectionItemInfo jzTaskInspectionItemInfoTwo = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "低压对高压及地".equals(x.getTestName()) && "R61(MΩ)".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (jzTaskInspectionItemInfoTwo != null) {
                baseData.put("leiChongTwo",
                        jzTaskInspectionItemInfoTwo.getTestResults() + "(MΩ)");
            }

            // three
            JzTaskInspectionItemInfo jzTaskInspectionItemInfoThree = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压及低压对地".equals(x.getTestName()) && "R62(MΩ)".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (jzTaskInspectionItemInfoThree != null) {
                baseData.put("leiChongThree",
                        jzTaskInspectionItemInfoThree.getTestResults() + "(MΩ)");
            }

            // four
            JzTaskInspectionItemInfo jzTaskInspectionItemInfoFour = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压绕组不平衡率".equals(x.getTestName()) && "%".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (jzTaskInspectionItemInfoFour != null) {
                baseData.put("leiChongFour",
                        jzTaskInspectionItemInfoFour.getTestResults() + "%");
            }

            // five
            JzTaskInspectionItemInfo jzTaskInspectionItemInfoFive = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "低压绕组不平衡率".equals(x.getTestName()) && "%".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (jzTaskInspectionItemInfoFive != null) {
                baseData.put("leiChongFive",
                        jzTaskInspectionItemInfoFive.getTestResults() + "%");
            }

            // 空载损耗和空载电流测量(1-7)
            baseData.put("noloadOne", "/");
            baseData.put("noloadTwo", "/");
            baseData.put("noloadThree", "/");
            baseData.put("noloadFour", "/");
            baseData.put("noloadFive", "/");
            baseData.put("noloadSix", "/");
            baseData.put("noloadSeven", "/");

            // 测量电流(A)
            JzTaskInspectionItemInfo noloadThree = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "测量电流".equals(x.getTestName()) && "A".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (noloadThree != null) {
                baseData.put("noloadThree", noloadThree.getTestResults());
            }

            // 测量损耗(W)
            JzTaskInspectionItemInfo noloadFour = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "测量损耗".equals(x.getTestName()) && "W".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (noloadFour != null) {
                baseData.put("noloadFour", noloadFour.getTestResults());
            }

            // 空载损耗P₀(W)
            JzTaskInspectionItemInfo noloadFive = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "空载损耗".equals(x.getTestName()) && "W".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (noloadFive != null) {
                baseData.put("noloadFive", noloadFive.getTestResults());
            }

            // 空载电流Io(A)
            JzTaskInspectionItemInfo noloadSix = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "空载电流".equals(x.getTestName()) && "A".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (noloadSix != null) {
                baseData.put("noloadSix", noloadSix.getTestResults());
            }

            // 短路阻抗和负载损耗测量(1-11)
            baseData.put("shortOne", "/");
            baseData.put("shortTwo", "/");
            baseData.put("shortThree", "/");
            baseData.put("shortFour", "/");
            baseData.put("shortFive", "/");
            baseData.put("shortSix", "/");
            baseData.put("shortSeven", "/");
            baseData.put("shortEight", "/");
            baseData.put("shortNine", "/");
            baseData.put("shortTen", "/");
            baseData.put("shortEleven", "/");

            // 高压对低压分接位置3施加电流(A)
            JzTaskInspectionItemInfo shortTwo = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压对低压分接位置3施加电流".equals(x.getTestName()) && "A".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (shortTwo != null) {
                baseData.put("shortTwo", shortTwo.getTestResults());
            }

            // 测量电压(V)
            JzTaskInspectionItemInfo shortThree = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压对低压分接位置3测量电压".equals(x.getTestName()) && "V".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (shortThree != null) {
                baseData.put("shortThree", shortThree.getTestResults());
            }

            // 测量损耗(W)
            JzTaskInspectionItemInfo shortFour = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压对低压分接位置3测量损耗".equals(x.getTestName()) && "W".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (shortFour != null) {
                baseData.put("shortFour", shortFour.getTestResults());
            }

            // 负载损耗Pₖ₇₅c(W)
            JzTaskInspectionItemInfo shortFive = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压对低压分接位置3负载损耗".equals(x.getTestName()) && "PK75℃（W）".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (shortFive != null) {
                baseData.put("shortFive", shortFive.getTestResults());
            }

            // 短路阻抗 ZA75℃
            JzTaskInspectionItemInfo shortSix = jzTaskInspectionItemInfos.stream()
                    .filter(x -> "高压对低压分接位置3短路阻抗".equals(x.getTestName()) && "%".equals(x.getUnit()))
                    .findFirst().orElse(null);
            if (shortSix != null) {
                baseData.put("shortSix", shortSix.getTestResults());
            }

            // 获取不平衡允许电阻值
            String permissibleValueHighVoltage = "0.5%"; // 默认值
            String permissibleValueLowVoltage = "1.0%"; // 默认值

            // 高压和低压的最大不平衡率
            String maxUnbalanceRateHighVoltage = "";
            String maxUnbalanceRateLowVoltage = "";

            // 包含 rab、rec、rea
            List<JzTaskInspectionItemInfo> jzTaskInspectionItemInfosSix = jzTaskInspectionItemInfos.stream()
                    .filter(x -> StrUtil.containsAny(x.getTestName(), "Rab", "Rbc", "Rca", "高压绕组不平衡率",
                            "低压绕组不平衡率"))
                    .collect(Collectors.toList());

            // 处理绕组电阻测量数据
            MeasurementWindingResistanceData resistanceData = buildMeasurementWindingResistanceData(
                    jzTaskInspectionItemInfosSix,
                    maxUnbalanceRateHighVoltage,
                    permissibleValueHighVoltage,
                    maxUnbalanceRateLowVoltage,
                    permissibleValueLowVoltage);

            baseData.put("measurementWindingResistanceTable", resistanceData);

            // 获取对应task下面所有的jzTaskInspectionItemInfos
            List<JzTaskInspectionItemInfoVO> allJzTaskInspectionItemInfos = new AssociationQuery<>(
                    JzTaskInspectionItemInfoVO.class)
                    .voList(new JzTaskInspectionItemInfoQuery().setTaskId(jzTjzTanfokInfo.getId()));
            if (allJzTaskInspectionItemInfos != null && !allJzTaskInspectionItemInfos.isEmpty()) {
                // 往map添加，key就是测试项，value就是测试结果
                allJzTaskInspectionItemInfos.forEach(x -> {
                    baseData.put(x.getTestName(), x.getTestResults());
                    // 插入图片
                    if (x.getFileInfo() != null) {
                        String filePath = uploadPath + x.getFileInfo().getFilePath();
                        if (FileUtil.exist(filePath)) {
                            int[] imageDimensions = calculateImageDimensions(filePath, 378);
                            baseData.put(x.getTestName() + "_Pic", Pictures.ofLocal(filePath)
                                    .size(imageDimensions[0], imageDimensions[1]).create());
                        }
                    }
                });
            }
        }

        // 使用TemplateProcessUtils处理数据和模板
        Map<String, Object> completeData = TemplateProcessUtils.buildReportTemplateData(baseData, standardInfo);
        TemplateProcessUtils.processTemplate(fileSavePath, fileSavePath, completeData, standardInfo, true);
    }

    // 创建一个临时生成表格的设备类
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    public static class InstrumentTable {
        // 序号
        private Integer serialNumber;
        // 设备名称
        private String sbmc;
        // 型号/规格
        private String modelNumber;
        // 设备编号
        private String equipmentNumber;
        // 出厂编号
        private String factoryNumber;
        // 不确定度/准确度/最大允许误差
        private String parameter;
        // 检定/校准机构
        private String calibrationAgency;
        // 有效日期
        private String effectiveDate;
    }

    /**
     * 根据测试数据构建绕组电阻测量数据模型
     *
     * @param inspectionItemList          检测项目数据列表
     * @param highVoltageMaxUnbalanceRate 高压最大不平衡率
     * @param highVoltagePermissibleRate  高压允许不平衡率
     * @param lowVoltageMaxUnbalanceRate  低压最大不平衡率
     * @param lowVoltagePermissibleRate   低压允许不平衡率
     * @return 绕组电阻测量数据模型
     */
    private MeasurementWindingResistanceData buildMeasurementWindingResistanceData(
            List<JzTaskInspectionItemInfo> inspectionItemList,
            String highVoltageMaxUnbalanceRate,
            String highVoltagePermissibleRate,
            String lowVoltageMaxUnbalanceRate,
            String lowVoltagePermissibleRate) {
        if (inspectionItemList == null || inspectionItemList.isEmpty()) {
            return WindingResistanceTableHelper.buildExampleData(); // 如果没有数据，返回示例数据
        }

        // 创建高压和低压绕组数据列表
        List<WindingResistanceRow> highVoltageRows = new ArrayList<>();
        List<WindingResistanceRow> lowVoltageRows = new ArrayList<>();

        // 使用传入的参数，如果为空则使用默认值
        if (highVoltageMaxUnbalanceRate == null || highVoltageMaxUnbalanceRate.isEmpty()) {
            highVoltageMaxUnbalanceRate = "0.352";
        }
        if (highVoltagePermissibleRate == null || highVoltagePermissibleRate.isEmpty()) {
            highVoltagePermissibleRate = "≤2";
        }
        if (lowVoltageMaxUnbalanceRate == null || lowVoltageMaxUnbalanceRate.isEmpty()) {
            lowVoltageMaxUnbalanceRate = "1.22";
        }
        if (lowVoltagePermissibleRate == null || lowVoltagePermissibleRate.isEmpty()) {
            lowVoltagePermissibleRate = "≤2";
        }

        // 将测试数据按照分接位置和单位（高压/低压）分组
        List<JzTaskInspectionItemInfo> highVoltageData = new ArrayList<>();
        List<JzTaskInspectionItemInfo> lowVoltageData = new ArrayList<>();

        // 先分离高压和低压数据
        for (JzTaskInspectionItemInfo info : inspectionItemList) {
            // 跳过不相关的数据
            if (info.getTestName() == null || info.getTestResults() == null) {
                continue;
            }

            // 跳过不平衡率和不平衡允许数据
            if (info.getTestName().contains("不平衡率") || info.getTestName().contains("不平衡允许")) {
                continue;
            }

            String unit = info.getUnit();
            boolean isHighVoltage = unit != null && unit.contains("Ω") && !unit.contains("mΩ");
            boolean isLowVoltage = unit != null && unit.contains("mΩ");

            if (isHighVoltage) {
                highVoltageData.add(info);
            } else if (isLowVoltage) {
                lowVoltageData.add(info);
            } else {
                // 如果无法确定，根据testName判断
                if (info.getTestName().contains("高压") ||
                        (info.getTestName().contains("RAB") ||
                                info.getTestName().contains("RBC") ||
                                info.getTestName().contains("RCA"))) {
                    highVoltageData.add(info);
                } else if (info.getTestName().contains("低压") ||
                        (info.getTestName().contains("Rab") ||
                                info.getTestName().contains("Rbc") ||
                                info.getTestName().contains("Rca"))) {
                    lowVoltageData.add(info);
                } else {
                    // 如果还是无法确定，假设为高压
                    highVoltageData.add(info);
                }
            }
        }

        // 先添加表头行
        WindingResistanceRow highHeaderRow = WindingResistanceTableHelper.createHighVoltageRow(
                "—", "RAB", "Rec", "Ra", highVoltageMaxUnbalanceRate, highVoltagePermissibleRate);
        highVoltageRows.add(highHeaderRow);

        WindingResistanceRow lowHeaderRow = WindingResistanceTableHelper.createLowVoltageRow(
                "—", "Rab", "Rbe", "Rea", lowVoltageMaxUnbalanceRate, lowVoltagePermissibleRate);
        lowVoltageRows.add(lowHeaderRow);

        // 如果没有数据，使用默认数据
        if (highVoltageData.isEmpty()) {
            // 使用默认示例数据 - 确保每行都有完整的三个测量值
            // 样本数据1
            highVoltageRows.add(WindingResistanceTableHelper.createHighVoltageRow(
                    "1", "1.6104", "1.6117", "1.6123", highVoltageMaxUnbalanceRate, "0.5%"));
            // 样本数据2
            highVoltageRows.add(WindingResistanceTableHelper.createHighVoltageRow(
                    "2", "1.5614", "1.5643", "1.5651", highVoltageMaxUnbalanceRate, "0.5%"));
            // 样本数据3
            highVoltageRows.add(WindingResistanceTableHelper.createHighVoltageRow(
                    "3", "1.5529", "1.5559", "1.5563", highVoltageMaxUnbalanceRate, "0.5%"));
            // 样本数据4
            highVoltageRows.add(WindingResistanceTableHelper.createHighVoltageRow(
                    "4", "1.5034", "1.5061", "1.5087", highVoltageMaxUnbalanceRate, "0.5%"));
            // 样本数据5
            highVoltageRows.add(WindingResistanceTableHelper.createHighVoltageRow(
                    "5", "1.4613", "1.4639", "1.4647", highVoltageMaxUnbalanceRate, "0.5%"));
        } else {
            // 使用真实数据构建高压绕组行
            // 使用Map按分接位置分组
            Map<String, List<JzTaskInspectionItemInfo>> positionMap = new HashMap<>();

            // 提取分接位置并分组
            for (JzTaskInspectionItemInfo info : highVoltageData) {
                String testName = info.getTestName();
                // 从testName中提取分接位置（如"分接位置1Rab测量电阻值"中的"1"）
                String position = "";
                if (testName.contains("分接位置")) {
                    Pattern pattern = Pattern.compile("分接位置(\\d+)");
                    Matcher matcher = pattern.matcher(testName);
                    if (matcher.find()) {
                        position = matcher.group(1);
                    }
                }

                if (!position.isEmpty()) {
                    positionMap.computeIfAbsent(position, k -> new ArrayList<>()).add(info);
                }
            }

            // 按分接位置处理数据
            for (Map.Entry<String, List<JzTaskInspectionItemInfo>> entry : positionMap.entrySet()) {
                String position = entry.getKey();
                List<JzTaskInspectionItemInfo> positionData = entry.getValue();

                String rabValue = "";
                String recValue = "";
                String raValue = "";

                // 从这个分接位置的数据中提取RAB、RBC/Rec、RCA/Ra值
                for (JzTaskInspectionItemInfo info : positionData) {
                    String testName = info.getTestName();
                    String value = info.getTestResults().replaceAll("[^\\d.]+", ""); // 去掉单位，只保留数值

                    if (testName.contains("RAB") || testName.contains("Rab")) {
                        rabValue = value;
                    } else if (testName.contains("RBC") || testName.contains("Rbc") ||
                            testName.contains("Rec") || testName.contains("Rbe")) {
                        recValue = value;
                    } else if (testName.contains("RCA") || testName.contains("Rca") ||
                            testName.contains("Ra") || testName.contains("Rea")) {
                        raValue = value;
                    }
                }

                // 如果三个值都有，则添加一行数据
                if (!rabValue.isEmpty() && !recValue.isEmpty() && !raValue.isEmpty()) {
                    WindingResistanceRow row = WindingResistanceTableHelper.createHighVoltageRow(
                            position,
                            rabValue,
                            recValue,
                            raValue,
                            highVoltageMaxUnbalanceRate,
                            "0.5%");
                    highVoltageRows.add(row);
                }
            }

            // 如果没有生成任何数据行，使用默认样本数据
            if (highVoltageRows.size() <= 1) { // 只有表头行
                highVoltageRows.add(WindingResistanceTableHelper.createHighVoltageRow(
                        "1", "1.6104", "1.6117", "1.6123", highVoltageMaxUnbalanceRate, "0.5%"));
            }
        }

        // 处理低压数据，逻辑与高压类似
        if (lowVoltageData.isEmpty()) {
            // 使用默认示例数据
            lowVoltageRows.add(WindingResistanceTableHelper.createLowVoltageRow(
                    "1", "2.5406", "2.5201", "2.5066", lowVoltageMaxUnbalanceRate, "1.0%"));
        } else {
            // 使用真实数据构建低压绕组行
            // 使用Map按分接位置分组
            Map<String, List<JzTaskInspectionItemInfo>> positionMap = new HashMap<>();

            // 提取分接位置并分组
            for (JzTaskInspectionItemInfo info : lowVoltageData) {
                String testName = info.getTestName();
                // 从testName中提取分接位置（如"分接位置1Rab测量电阻值"中的"1"）
                String position = "";
                if (testName.contains("分接位置")) {
                    Pattern pattern = Pattern.compile("分接位置(\\d+)");
                    Matcher matcher = pattern.matcher(testName);
                    if (matcher.find()) {
                        position = matcher.group(1);
                    }
                }

                if (!position.isEmpty()) {
                    positionMap.computeIfAbsent(position, k -> new ArrayList<>()).add(info);
                }
            }

            // 按分接位置处理数据
            for (Map.Entry<String, List<JzTaskInspectionItemInfo>> entry : positionMap.entrySet()) {
                String position = entry.getKey();
                List<JzTaskInspectionItemInfo> positionData = entry.getValue();

                String rabValue = "";
                String rbeValue = "";
                String reaValue = "";

                // 从这个分接位置的数据中提取RAB、RBC/Rbe、RCA/Rea值
                for (JzTaskInspectionItemInfo info : positionData) {
                    String testName = info.getTestName();
                    String value = info.getTestResults().replaceAll("[^\\d.]+", ""); // 去掉单位，只保留数值

                    if (testName.contains("RAB") || testName.contains("Rab")) {
                        rabValue = value;
                    } else if (testName.contains("RBC") || testName.contains("Rbc") ||
                            testName.contains("Rec") || testName.contains("Rbe")) {
                        rbeValue = value;
                    } else if (testName.contains("RCA") || testName.contains("Rca") ||
                            testName.contains("Ra") || testName.contains("Rea")) {
                        reaValue = value;
                    }
                }

                // 如果三个值都有，则添加一行数据
                if (!rabValue.isEmpty() && !rbeValue.isEmpty() && !reaValue.isEmpty()) {
                    WindingResistanceRow row = WindingResistanceTableHelper.createLowVoltageRow(
                            position,
                            rabValue,
                            rbeValue,
                            reaValue,
                            lowVoltageMaxUnbalanceRate,
                            "1.0%");
                    lowVoltageRows.add(row);
                }
            }

            // 如果没有生成任何数据行，使用默认样本数据
            if (lowVoltageRows.size() <= 1) { // 只有表头行
                lowVoltageRows.add(WindingResistanceTableHelper.createLowVoltageRow(
                        "1", "2.5406", "2.5201", "2.5066", lowVoltageMaxUnbalanceRate, "1.0%"));
            }
        }

        // 构建并返回数据模型
        return WindingResistanceTableHelper.buildResistanceData(highVoltageRows, lowVoltageRows);
    }

    /**
     * 从字符串中提取分接位置信息
     *
     * @param str 输入字符串
     * @return 分接位置信息
     */
    private String extractPosition(String str) {
        if (str == null || str.isEmpty()) {
            return "";
        }

        // 尝试提取形如"418kVA"的分接位置
        Pattern pattern = Pattern.compile("(\\d+)\\s*kVA");
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            return matcher.group(1) + "kVA";
        }

        // 尝试提取数字后跟单位的形式
        pattern = Pattern.compile("(\\d+)\\s*[a-zA-Z]+");
        matcher = pattern.matcher(str);
        if (matcher.find()) {
            return matcher.group(0);
        }

        // 如果都不匹配，返回空字符串
        return "";
    }

    /**
     * 计算图片插入到Word时的合适尺寸
     *
     * @param imagePath 图片路径
     * @param maxHeight Word中允许的最大高度
     * @return 返回一个包含宽度和高度的数组，索引0为宽度，索引1为高度
     */
    private int[] calculateImageDimensions(String imagePath, int maxHeight) {
        try {
            // 读取图片获取原始尺寸
            BufferedImage image = ImageIO.read(new File(imagePath));
            int originalWidth = image.getWidth();
            int originalHeight = image.getHeight();

            // 初始化返回结果
            int width = originalWidth;
            int height = originalHeight;

            // 如果原始高度超过最大高度，按比例缩放
            if (originalHeight > maxHeight) {
                // 计算缩放比例
                double ratio = (double) maxHeight / originalHeight;
                // 按比例计算新的宽度，保持宽高比
                width = (int) (originalWidth * ratio);
                height = maxHeight;
            }

            // 确保宽高至少为1像素
            width = Math.max(1, width);
            height = Math.max(1, height);

            return new int[] { width, height };
        } catch (Exception e) {
            log.error("计算图片尺寸时出错: {}", e.getMessage());
            // 出错时返回默认尺寸
            return new int[] { 300, 300 };
        }
    }
}
