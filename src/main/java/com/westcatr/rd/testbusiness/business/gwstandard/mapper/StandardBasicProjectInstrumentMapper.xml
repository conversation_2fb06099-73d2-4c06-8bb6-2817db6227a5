<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicProjectInstrumentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument">
        <id column="id" property="id" />
        <result column="project_name" property="projectName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="update_user_id" property="updateUserId" />
        <result column="create_user_full_name" property="createUserFullName" />
        <result column="update_user_full_name" property="updateUserFullName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_name, create_time, update_time, create_user_id, update_user_id, create_user_full_name, update_user_full_name
    </sql>

</mapper>
