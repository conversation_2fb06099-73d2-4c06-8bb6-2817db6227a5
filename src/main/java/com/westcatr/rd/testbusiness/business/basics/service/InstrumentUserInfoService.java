package com.westcatr.rd.testbusiness.business.basics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.InstrumentUserInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 仪器设备—检测人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface InstrumentUserInfoService extends IService<InstrumentUserInfo> {

    IPage<InstrumentUserInfo> entityPage(InstrumentUserInfoQuery query);

    InstrumentUserInfo getEntityById(Long id);

    boolean saveEntity(InstrumentUserInfo param);

    boolean updateEntity(InstrumentUserInfo param);

    boolean removeEntityById(Long id);
}
