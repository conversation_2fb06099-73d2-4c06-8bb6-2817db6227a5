package com.westcatr.rd.testbusiness.business.gwstandard.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicInfoVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * StandardBasicInfo 控制器
 * 
 * <AUTHOR>
 * @since 2025-03-17
 */
@Validated
@Tag(name = "标准—集成装置设备信息表接口", description = "标准—集成装置设备信息表接口")
@Slf4j
@RestController
public class StandardBasicInfoController {

    @Autowired
    private StandardBasicInfoService standardBasicInfoService;

    @Operation(summary = "获取标准—集成装置设备信息表分页数据")
    @PostMapping("/standardBasicInfo/page")
    public IResult<IPage<StandardBasicInfo>> getStandardBasicInfoPage(@RequestBody StandardBasicInfoQuery query) {
        return IResult.ok(standardBasicInfoService.entityPage(query));
    }

    @Operation(summary = "获取标准—集成装置设备信息表数据")
    @PostMapping("/standardBasicInfo/get")
    public IResult<StandardBasicInfo> getStandardBasicInfoById(@RequestBody @Id ID id) {
        return IResult.ok(standardBasicInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增标准—集成装置设备信息表数据")
    @PostMapping("/standardBasicInfo/add")
    public IResult addStandardBasicInfo(@RequestBody @Validated(Insert.class) StandardBasicInfo param) {
        return IResult.auto(standardBasicInfoService.saveEntity(param));
    }

    @Operation(summary = "更新标准—集成装置设备信息表数据")
    @PostMapping("/standardBasicInfo/update")
    public IResult updateStandardBasicInfoById(@RequestBody @Validated(Update.class) StandardBasicInfo param) {
        return IResult.auto(standardBasicInfoService.updateEntity(param));
    }

    @Operation(summary = "删除标准—集成装置设备信息表数据")
    @PostMapping("/standardBasicInfo/delete")
    public IResult deleteStandardBasicInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            standardBasicInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取标准—集成装置设备信息表VO分页数据")
    @PostMapping("/standardBasicInfo/voPage")
    public IResult<IPage<StandardBasicInfoVO>> getStandardBasicInfoVoPage(@RequestBody StandardBasicInfoQuery query) {
        AssociationQuery<StandardBasicInfoVO> associationQuery = new AssociationQuery<>(StandardBasicInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取标准—集成装置设备信息表VO数据")
    @PostMapping("/standardBasicInfo/getVo")
    public IResult<StandardBasicInfoVO> getStandardBasicInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<StandardBasicInfoVO> associationQuery = new AssociationQuery<>(StandardBasicInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
