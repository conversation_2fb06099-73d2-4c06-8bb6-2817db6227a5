package com.westcatr.rd.testbusiness.business.org.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.org.pojo.query.UserTableColumnInfoQuery;
import com.westcatr.rd.testbusiness.business.org.entity.UserTableColumnInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 自定义展示列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
public interface UserTableColumnInfoService extends IService<UserTableColumnInfo> {

    IPage<UserTableColumnInfo> entityPage(UserTableColumnInfoQuery query);

    UserTableColumnInfo getEntityById(Long id);

    boolean saveEntity(UserTableColumnInfo param);

    boolean updateEntity(UserTableColumnInfo param);

    boolean removeEntityById(Long id);
}
