package com.westcatr.rd.testbusiness.business.jztask.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentNewInfoVO;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicInstrumentInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—任务列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "荆州—任务列表表VO对象")
public class JzTaskInfoVO extends JzTaskInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<JzTaskArrangementInfoVO> jzTaskArrangementInfoLists;

    @Schema(description = "关联样品信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = SampleInfo.class, mainId = "sampleId")
    private SampleInfo sampleInfo;

    @Schema(description = "关联样品信息名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = SampleInfo.class, mainId = "sampleId", field = "sample_name")
    private String sampleName;

    @Schema(description = "详情：工位信息")
    @TableField(exist = false)
    private List<BuInstrumentWorkstationInfo> buInstrumentWorkstationInfoLists;

    @Schema(description = "详情：设备信息")
    @TableField(exist = false)
    private List<InstrumentNewInfoVO> instrumentNewInfoLists;

    @Schema(description = "详情：人员信息")
    @TableField(exist = false)
    private List<OrgUserInfo> orgUserInfoLists;

    @Schema(description = "详情：工单详情")
    @TableField(exist = false)
    private List<JzTaskWorkOrderInfo> jzTaskWorkOrderInfoLists;

    @TableField(exist = false)
    private StandardBasicInstrumentInfoVO findStandardBasicInstrumentInfoVO;

}
