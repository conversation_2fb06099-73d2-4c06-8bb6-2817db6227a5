package com.westcatr.rd.testbusiness.business.gwstandard.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.file.service.FileInfoService;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInstrumentInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicInstrumentInfoVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * StandardBasicInstrumentInfo 控制器
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Validated
@Tag(name = "标准—检测仪器标准信息表接口", description = "标准—检测仪器标准信息表接口")
@Slf4j
@RestController
public class StandardBasicInstrumentInfoController {

    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;

    @Autowired
    private FileInfoService fileInfoService;

    @Operation(summary = "获取标准—检测仪器标准信息表分页数据")
    @PostMapping("/standardBasicInstrumentInfo/page")
    public IResult<IPage<StandardBasicInstrumentInfo>> getStandardBasicInstrumentInfoPage(
            @RequestBody StandardBasicInstrumentInfoQuery query) {
        return IResult.ok(standardBasicInstrumentInfoService.entityPage(query));
    }

    @Operation(summary = "获取标准—检测仪器标准信息表数据")
    @PostMapping("/standardBasicInstrumentInfo/get")
    public IResult<StandardBasicInstrumentInfo> getStandardBasicInstrumentInfoById(@RequestBody @Id ID id) {
        return IResult.ok(standardBasicInstrumentInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增标准—检测仪器标准信息表数据")
    @PostMapping("/standardBasicInstrumentInfo/add")
    public IResult<Boolean> addStandardBasicInstrumentInfo(
            @RequestBody @Validated(Insert.class) StandardBasicInstrumentInfo param) {
        return IResult.auto(standardBasicInstrumentInfoService.saveEntity(param));
    }

    @Operation(summary = "更新标准—检测仪器标准信息表数据")
    @PostMapping("/standardBasicInstrumentInfo/update")
    public IResult<Boolean> updateStandardBasicInstrumentInfoById(
            @RequestBody @Validated(Update.class) StandardBasicInstrumentInfo param) {
        return IResult.auto(standardBasicInstrumentInfoService.updateEntity(param));
    }

    @Operation(summary = "删除标准—检测仪器标准信息表数据")
    @PostMapping("/standardBasicInstrumentInfo/delete")
    public IResult<Void> deleteStandardBasicInstrumentInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            standardBasicInstrumentInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取标准—检测仪器标准信息表VO分页数据")
    @PostMapping("/standardBasicInstrumentInfo/voPage")
    public IResult<IPage<StandardBasicInstrumentInfoVO>> getStandardBasicInstrumentInfoVoPage(
            @RequestBody StandardBasicInstrumentInfoQuery query) {
        AssociationQuery<StandardBasicInstrumentInfoVO> associationQuery = new AssociationQuery<>(
                StandardBasicInstrumentInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取标准—检测仪器标准信息表VO数据")
    @PostMapping("/standardBasicInstrumentInfo/getVo")
    public IResult<StandardBasicInstrumentInfoVO> getStandardBasicInstrumentInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<StandardBasicInstrumentInfoVO> associationQuery = new AssociationQuery<>(
                StandardBasicInstrumentInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "上传报告模板文件")
    @PostMapping("/standardBasicInstrumentInfo/uploadReportTemplate")
    public IResult<FileInfo> uploadReportTemplate(MultipartFile file,
            Long standardId) {
        try {
            // 获取当前用户
            IUser iUser = AuthUtil.getUserE();

            // 上传文件
            FileInfo fileInfo = fileInfoService.upload(
                    file,
                    file.getOriginalFilename(),
                    3, // 文件类型
                    1, // 业务类型
                    iUser, // 用户信息
                    false // 是否私有
            );

            // 更新标准信息
            StandardBasicInstrumentInfo standardInfo = standardBasicInstrumentInfoService.getById(standardId);
            if (standardInfo != null) {
                standardInfo.setReportTemplateFileId(fileInfo.getId());
                standardBasicInstrumentInfoService.updateById(standardInfo);
            } else {
                return IResult.fail("未找到对应的标准信息");
            }

            return IResult.ok(fileInfo);
        } catch (Exception e) {
            log.error("上传报告模板文件失败", e);
            return IResult.fail("上传报告模板文件失败: " + e.getMessage());
        }
    }

    @Operation(summary = "上传原始记录模板文件")
    @PostMapping("/standardBasicInstrumentInfo/uploadOriginalRecordTemplate")
    public IResult<FileInfo> uploadOriginalRecordTemplate(MultipartFile file,
            Long standardId) {
        try {
            // 获取当前用户
            IUser iUser = AuthUtil.getUserE();

            // 上传文件
            FileInfo fileInfo = fileInfoService.upload(
                    file,
                    file.getOriginalFilename(),
                    3, // 文件类型
                    1, // 业务类型
                    iUser, // 用户信息
                    false // 是否私有
            );

            // 更新标准信息
            StandardBasicInstrumentInfo standardInfo = standardBasicInstrumentInfoService.getById(standardId);
            if (standardInfo != null) {
                standardInfo.setOriginalRecordTemplateFileId(fileInfo.getId());
                standardBasicInstrumentInfoService.updateById(standardInfo);
            } else {
                return IResult.fail("未找到对应的标准信息");
            }

            return IResult.ok(fileInfo);
        } catch (Exception e) {
            log.error("上传原始记录模板文件失败", e);
            return IResult.fail("上传原始记录模板文件失败: " + e.getMessage());
        }
    }

    @Data
    public static class ProjectAssociateVO {
        private Long standardId;
        private List<Long> projectIds;
    }

}
