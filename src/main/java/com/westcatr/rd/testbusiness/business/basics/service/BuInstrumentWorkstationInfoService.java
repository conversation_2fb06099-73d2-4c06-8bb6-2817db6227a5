package com.westcatr.rd.testbusiness.business.basics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 工位信息管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkCameraDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkEquipmentDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkstationDeviceTreeDTO;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentWorkstationInfoQuery;

import java.util.List;

public interface BuInstrumentWorkstationInfoService extends IService<BuInstrumentWorkstationInfo> {

    IPage<BuInstrumentWorkstationInfo> entityPage(BuInstrumentWorkstationInfoQuery query);

    BuInstrumentWorkstationInfo getEntityById(Long id);

    boolean saveEntity(BuInstrumentWorkstationInfo param);

    boolean updateEntity(BuInstrumentWorkstationInfo param);

    boolean removeEntityById(Long id);

    /**
     * 关联设备
     */
    boolean saveEntityWithDevice(WorkEquipmentDto workEquipmentDto);

    /**
     * 关联摄像头
     */
    boolean saveEntityWithCamera(WorkCameraDto workCameraDto);
    
    /**
     * 获取工位-设备树形结构
     * 
     * @return 工位-设备树形结构列表
     */
    List<WorkstationDeviceTreeDTO> getWorkstationDeviceTree();
}
