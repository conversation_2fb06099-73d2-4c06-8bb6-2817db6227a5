package com.westcatr.rd.testbusiness.business.gwstandard.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实验项目参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("standard_basic_project_param")
@Schema(description = "实验项目参数表")
public class StandardBasicProjectParam extends Model<StandardBasicProjectParam> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "参数名称")
    @NotBlank(message = "参数名称不能为空", groups = { Insert.class, Update.class })
    @Length(max = 255, message = "参数名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("param_name")
    private String paramName;

    @Schema(description = "参数类别")
    @NotBlank(message = "参数类别不能为空", groups = { Insert.class, Update.class })
    @Length(max = 64, message = "参数类别长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("param_type")
    private String paramType;

    @Schema(description = "实验项目ID")
    @TableField("standard_basic_project_id")
    private Long standardBasicProjectId;

    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "单位")
    @TableField("unit")
    private String unit;

    @Schema(description = "采集方式")
    @TableField("collection_method")
    private String collectionMethod;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
