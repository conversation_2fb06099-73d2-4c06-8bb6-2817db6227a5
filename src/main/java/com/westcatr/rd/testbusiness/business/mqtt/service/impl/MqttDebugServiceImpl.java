package com.westcatr.rd.testbusiness.business.mqtt.service.impl;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.westcatr.rd.testbusiness.business.mqtt.entity.MqttDebugResponse;
import com.westcatr.rd.testbusiness.business.mqtt.service.MqttDebugService;
import com.westcatr.rd.testbusiness.configs.MqttDebugConfig;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * MQTT调试服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Slf4j
@Service
public class MqttDebugServiceImpl implements MqttDebugService {

    @Autowired
    private MqttDebugConfig debugConfig;

    @Autowired
    private ObjectMapper objectMapper;

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    @Override
    public String saveDeviceResponse(String deviceId, String deviceType, String messageId, 
                                   Map<String, Object> response, String tableName) {
        if (!debugConfig.isAutoSaveResponses()) {
            log.debug("🔧 自动保存响应已禁用，跳过保存");
            return null;
        }

        try {
            // 创建调试响应对象
            MqttDebugResponse debugResponse = new MqttDebugResponse();
            debugResponse.setDeviceId(deviceId);
            debugResponse.setDeviceType(deviceType);
            debugResponse.setMessageId(messageId);
            debugResponse.setResponseTime(LocalDateTime.now());
            debugResponse.setOriginalResponse(response);
            debugResponse.setTableName(tableName);
            debugResponse.setTimestamp(System.currentTimeMillis());
            debugResponse.setDefault(false);
            debugResponse.setDescription("自动保存的设备响应");

            // 提取响应内容
            String responseContent = extractResponseContent(response);
            debugResponse.setResponseContent(responseContent);

            // 生成文件路径
            String filePath = generateFilePath(deviceId, deviceType, tableName, false);
            debugResponse.setFilePath(filePath);

            // 确保目录存在
            ensureDirectoryExists(filePath);

            // 保存到文件
            String jsonContent = objectMapper.writeValueAsString(debugResponse);
            FileUtil.writeString(jsonContent, filePath, StandardCharsets.UTF_8);

            log.info("📁 保存设备响应到调试文件: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("❌ 保存设备响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getDebugResponse(String deviceId, String deviceType, String tableName) {
        if (!debugConfig.isEnabled()) {
            log.debug("🔧 调试模式未启用");
            return null;
        }

        try {
            // 获取设备的所有调试响应文件
            List<MqttDebugResponse> responses = listDebugResponses(deviceId, deviceType);
            
            if (responses.isEmpty()) {
                log.warn("⚠️ 未找到设备[{}]类型[{}]的调试响应文件", deviceId, deviceType);
                return null;
            }

            // 过滤匹配的响应（考虑表名）
            List<MqttDebugResponse> matchedResponses = responses.stream()
                .filter(r -> r.isEnabled())
                .filter(r -> tableName == null || tableName.equals(r.getTableName()))
                .sorted(Comparator.comparing(MqttDebugResponse::getPriority).reversed()
                    .thenComparing(MqttDebugResponse::getTimestamp).reversed())
                .collect(Collectors.toList());

            if (matchedResponses.isEmpty()) {
                log.warn("⚠️ 未找到匹配的调试响应，设备[{}]类型[{}]表名[{}]", deviceId, deviceType, tableName);
                return null;
            }

            // 返回优先级最高的响应
            MqttDebugResponse selectedResponse = matchedResponses.get(0);
            log.info("🎯 使用调试响应文件: {}", selectedResponse.getFilePath());
            
            return selectedResponse.getOriginalResponse();

        } catch (Exception e) {
            log.error("❌ 获取调试响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getLatestDebugResponse(String deviceId, String deviceType, String tableName) {
        return getDebugResponse(deviceId, deviceType, tableName);
    }

    @Override
    public List<MqttDebugResponse> listDebugResponses(String deviceId, String deviceType) {
        List<MqttDebugResponse> responses = new ArrayList<>();
        
        try {
            // 构建设备目录路径
            String deviceDir = buildDeviceDirectory(deviceType, deviceId);
            Path dirPath = Paths.get(deviceDir);
            
            if (!Files.exists(dirPath)) {
                log.debug("📂 设备目录不存在: {}", deviceDir);
                return responses;
            }

            // 扫描目录中的JSON文件
            File[] files = new File(deviceDir).listFiles((dir, name) -> name.endsWith(".json"));
            if (files == null) {
                return responses;
            }

            // 读取并解析每个文件
            for (File file : files) {
                try {
                    String content = FileUtil.readString(file, StandardCharsets.UTF_8);
                    MqttDebugResponse response = objectMapper.readValue(content, MqttDebugResponse.class);
                    responses.add(response);
                } catch (Exception e) {
                    log.warn("⚠️ 解析调试响应文件失败: {}, 错误: {}", file.getPath(), e.getMessage());
                }
            }

            // 按时间戳倒序排列
            responses.sort(Comparator.comparing(MqttDebugResponse::getTimestamp).reversed());

        } catch (Exception e) {
            log.error("❌ 列出调试响应失败: {}", e.getMessage(), e);
        }

        return responses;
    }

    @Override
    public boolean deleteDebugResponse(String filePath) {
        try {
            if (StrUtil.isBlank(filePath)) {
                return false;
            }

            File file = new File(filePath);
            if (file.exists() && file.delete()) {
                log.info("🗑️ 删除调试响应文件: {}", filePath);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("❌ 删除调试响应文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int cleanExpiredResponses() {
        int cleanedCount = 0;
        
        try {
            Path rootPath = Paths.get(debugConfig.getStoragePath());
            if (!Files.exists(rootPath)) {
                return 0;
            }

            long expireTime = System.currentTimeMillis() - (debugConfig.getRetentionDays() * 24 * 60 * 60 * 1000L);

            // 递归扫描所有JSON文件
            Files.walk(rootPath)
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .forEach(path -> {
                    try {
                        String content = Files.readString(path, StandardCharsets.UTF_8);
                        MqttDebugResponse response = objectMapper.readValue(content, MqttDebugResponse.class);
                        
                        if (response.getTimestamp() < expireTime && !response.isDefault()) {
                            Files.delete(path);
                            log.debug("🗑️ 清理过期调试响应: {}", path);
                        }
                    } catch (Exception e) {
                        log.warn("⚠️ 处理文件失败: {}, 错误: {}", path, e.getMessage());
                    }
                });

        } catch (Exception e) {
            log.error("❌ 清理过期响应失败: {}", e.getMessage(), e);
        }

        log.info("🧹 清理完成，共清理 {} 个过期调试响应文件", cleanedCount);
        return cleanedCount;
    }

    @Override
    public String createCustomDebugResponse(String deviceId, String deviceType, 
                                          Map<String, Object> response, String description, String tableName) {
        try {
            // 创建自定义调试响应对象
            MqttDebugResponse debugResponse = new MqttDebugResponse();
            debugResponse.setDeviceId(deviceId);
            debugResponse.setDeviceType(deviceType);
            debugResponse.setMessageId("custom_" + System.currentTimeMillis());
            debugResponse.setResponseTime(LocalDateTime.now());
            debugResponse.setOriginalResponse(response);
            debugResponse.setTableName(tableName);
            debugResponse.setTimestamp(System.currentTimeMillis());
            debugResponse.setDefault(true);
            debugResponse.setDescription(description);
            debugResponse.setPriority(100); // 自定义响应优先级较高

            // 提取响应内容
            String responseContent = extractResponseContent(response);
            debugResponse.setResponseContent(responseContent);

            // 生成文件路径
            String filePath = generateFilePath(deviceId, deviceType, tableName, true);
            debugResponse.setFilePath(filePath);

            // 确保目录存在
            ensureDirectoryExists(filePath);

            // 保存到文件
            String jsonContent = objectMapper.writeValueAsString(debugResponse);
            FileUtil.writeString(jsonContent, filePath, StandardCharsets.UTF_8);

            log.info("📁 创建自定义调试响应: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("❌ 创建自定义调试响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean isDebugEnabled() {
        return debugConfig.isEnabled();
    }

    @Override
    public void setDebugEnabled(boolean enabled) {
        debugConfig.setEnabled(enabled);
        log.info("🔧 MQTT调试模式已{}: {}", enabled ? "启用" : "禁用", enabled);
    }

    @Override
    public Map<String, Object> getDebugStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Path rootPath = Paths.get(debugConfig.getStoragePath());
            if (!Files.exists(rootPath)) {
                stats.put("totalFiles", 0);
                stats.put("totalSize", 0);
                stats.put("deviceTypes", new ArrayList<>());
                return stats;
            }

            // 统计文件数量和大小
            long[] fileStats = {0, 0}; // [count, size]
            List<String> deviceTypes = new ArrayList<>();

            Files.walk(rootPath)
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".json"))
                .forEach(path -> {
                    try {
                        fileStats[0]++; // 文件数量
                        fileStats[1] += Files.size(path); // 文件大小
                        
                        // 提取设备类型
                        String pathStr = path.toString();
                        String[] parts = pathStr.split(File.separator);
                        if (parts.length >= 2) {
                            String deviceType = parts[parts.length - 2];
                            if (!deviceTypes.contains(deviceType)) {
                                deviceTypes.add(deviceType);
                            }
                        }
                    } catch (IOException e) {
                        log.warn("⚠️ 统计文件失败: {}", path);
                    }
                });

            stats.put("totalFiles", fileStats[0]);
            stats.put("totalSize", fileStats[1]);
            stats.put("deviceTypes", deviceTypes);
            stats.put("debugEnabled", debugConfig.isEnabled());
            stats.put("storagePath", debugConfig.getStoragePath());
            stats.put("retentionDays", debugConfig.getRetentionDays());

        } catch (Exception e) {
            log.error("❌ 获取调试统计信息失败: {}", e.getMessage(), e);
        }

        return stats;
    }

    /**
     * 提取响应内容
     */
    private String extractResponseContent(Map<String, Object> response) {
        if (response == null) {
            return null;
        }

        try {
            // 尝试从标准格式中提取内容
            if (response.containsKey("body")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> body = (Map<String, Object>) response.get("body");
                if (body != null && body.containsKey("outputParams")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");
                    if (outputParams != null && outputParams.containsKey("body")) {
                        return String.valueOf(outputParams.get("body"));
                    }
                }
            }

            // 如果没有找到标准格式，返回整个响应的JSON字符串
            return JSONUtil.toJsonStr(response);

        } catch (Exception e) {
            log.warn("⚠️ 提取响应内容失败: {}", e.getMessage());
            return JSONUtil.toJsonStr(response);
        }
    }

    /**
     * 生成文件路径
     */
    private String generateFilePath(String deviceId, String deviceType, String tableName, boolean isCustom) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        String deviceDir = buildDeviceDirectory(deviceType, deviceId);
        
        StringBuilder fileName = new StringBuilder();
        fileName.append(debugConfig.getFilePrefix());
        fileName.append("_").append(deviceType);
        fileName.append("_").append(deviceId);
        
        if (StrUtil.isNotBlank(tableName)) {
            fileName.append("_").append(tableName);
        }
        
        if (isCustom) {
            fileName.append("_custom");
        }
        
        fileName.append("_").append(timestamp);
        fileName.append(debugConfig.getFileExtension());
        
        return Paths.get(deviceDir, fileName.toString()).toString();
    }

    /**
     * 构建设备目录路径
     */
    private String buildDeviceDirectory(String deviceType, String deviceId) {
        return Paths.get(debugConfig.getStoragePath(), deviceType, deviceId).toString();
    }

    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String filePath) {
        try {
            Path path = Paths.get(filePath);
            Path parentDir = path.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                log.debug("📂 创建目录: {}", parentDir);
            }
        } catch (IOException e) {
            log.error("❌ 创建目录失败: {}", e.getMessage(), e);
        }
    }
}
