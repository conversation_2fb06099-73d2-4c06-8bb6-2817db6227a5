package com.westcatr.rd.testbusiness.business.jzreport.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.jzreport.entity.BuSampleBaseInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 样品基本信息-关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="样品基本信息-关联表VO对象")
public class BuSampleBaseInfoVO extends BuSampleBaseInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
