package com.westcatr.rd.testbusiness.business.jztask.service;

import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInfoVO;

/**
 * <p>
 * 荆州—任务列表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface JzTaskInfoService extends IService<JzTaskInfo> {

    IPage<JzTaskInfo> entityPage(JzTaskInfoQuery query);

    JzTaskInfo getEntityById(Long id);

    boolean saveEntity(JzTaskInfo param);

    boolean updateEntity(JzTaskInfo param);

    boolean removeEntityById(Long id);

    IPage<JzTaskInfoVO> myEntityVoPage(JzTaskInfoQuery query);

    /**
     * 启动任务
     *
     * @param id
     * @return
     */
    boolean startTask(Long id);

    /**
     * 根据id查询vo
     *
     * @param id
     * @return
     */
    JzTaskInfoVO myEntityVoById(Long id);

    /**
     * 获取首页统计图数据
     * 
     * @return
     */
    Map<String, Long> getTaskInfo();

    boolean endTask(Long id);
}
