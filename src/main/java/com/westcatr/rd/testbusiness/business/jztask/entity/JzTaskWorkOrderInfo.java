package com.westcatr.rd.testbusiness.business.jztask.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—工单列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_task_work_order_info")
@Schema(description = "荆州—工单列表表")
public class JzTaskWorkOrderInfo extends Model<JzTaskWorkOrderInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "结束时间")
    @TableField("end_date")
    private Date endDate;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "检毕时间")
    @TableField("inspection_completion_time")
    private Date inspectionCompletionTime;

    @Schema(description = "关联任务")
    @TableField("sample_id")
    private Long sampleId;

    @Schema(description = "开始时间")
    @TableField("start_date")
    private Date startDate;

    @Schema(description = "关联样品")
    @TableField("task_id")
    private Long taskId;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "工单编号")
    @Length(max = 255, message = "工单编号长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("work_order_number")
    private String workOrderNumber;

    @Schema(description = "设备id")
    @TableField("equipment_id")
    private Long equipmentId;

    @Schema(description = "实验标准id")
    @TableField("gw_bz_id")
    private String gwBzId;

    @Schema(description = "测试人员id")
    @TableField("test_user_id")
    private Long testUserId;

    @Schema(description = "工位id")
    @TableField("workstation_id")
    private Long workstationId;

    @Schema(description = "工单状态")
    @TableField("status_info")
    private String statusInfo;

    @Schema(description = "关联数据项更新")
    @TableField(exist = false)
    private List<JzTaskInspectionItemInfo> jzTaskInspectionItemInfosUpdate;

    @Schema(description = "检测标准名称")
    @TableField("gw_bz_name")
    private String gwBzName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
