package com.westcatr.rd.testbusiness.business.gwstandard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInstrumentInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 标准—检测仪器标准信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
public interface StandardBasicInstrumentInfoService extends IService<StandardBasicInstrumentInfo> {

    IPage<StandardBasicInstrumentInfo> entityPage(StandardBasicInstrumentInfoQuery query);

    StandardBasicInstrumentInfo getEntityById(Long id);

    boolean saveEntity(StandardBasicInstrumentInfo param);

    boolean updateEntity(StandardBasicInstrumentInfo param);

    boolean removeEntityById(Long id);
}
