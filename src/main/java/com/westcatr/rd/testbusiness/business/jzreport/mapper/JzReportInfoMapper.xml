<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jzreport.mapper.JzReportInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="original_record_file_id" property="originalRecordFileId" />
        <result column="report_file_id" property="reportFileId" />
        <result column="report_number" property="reportNumber" />
        <result column="sample_id" property="sampleId" />
        <result column="task_id" property="taskId" />
        <result column="tf_qualified" property="tfQualified" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, id, original_record_file_id, report_file_id, report_number, sample_id, task_id, tf_qualified, update_time
    </sql>

</mapper>
