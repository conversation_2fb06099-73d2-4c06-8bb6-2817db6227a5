package com.westcatr.rd.testbusiness.business.mqtt.pojo.query;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 设备对接规范表查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "设备对接规范表查询对象")
public class DeviceIntegrationConfigQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "设备类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String deviceType;

    @Schema(description = "设备名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String deviceName;

    @Schema(description = "产品代码")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String productCode;

    @Schema(description = "设备代码")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String deviceCode;

    @Schema(description = "类型代码")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String typeCode;

    @Schema(description = "状态(enabled/disabled)")
    @QueryCondition
    private String status;
}
