package com.westcatr.rd.testbusiness.business.jztask.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskTestDataInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskTestDataInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 检测任务测试数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
public interface JzTaskTestDataInfoService extends IService<JzTaskTestDataInfo> {

    IPage<JzTaskTestDataInfo> entityPage(JzTaskTestDataInfoQuery query);

    JzTaskTestDataInfo getEntityById(Long id);

    boolean saveEntity(JzTaskTestDataInfo param);

    boolean updateEntity(JzTaskTestDataInfo param);

    boolean removeEntityById(Long id);
}
