package com.westcatr.rd.testbusiness.business.datasync.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备功能调用记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("zzzzz_log_device_function_call")
@Schema(description = "设备功能调用记录表")
public class LogDeviceFunctionCall extends Model<LogDeviceFunctionCall> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "设备id")
    @TableField("device_id")
    private String deviceId;

    @Schema(description = "设备产品")
    @TableField("product")
    private String product;

    @Schema(description = "输出数据")
    @TableField("output_params")
    private String outputParams;

    @Schema(description = "是否为异步调用")
    @TableField("async_call")
    private Integer asyncCall;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
