package com.westcatr.rd.testbusiness.business.basics.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工位信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="工位信息管理查询对象")
public class BuInstrumentWorkstationInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "工位描述")
    @QueryCondition
    private String description;

    @Schema(description = "主键")
    @QueryCondition
    private Long id;

    @Schema(description = "工位位置")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String location;

    @Schema(description = "负责人")
    @QueryCondition
    private String responsiblePerson;

    @Schema(description = "工位状态(空闲/使用中/维护中)")
    @QueryCondition
    private String status;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "工位编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String workstationCode;

    @Schema(description = "工位名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String workstationName;
}
