package com.westcatr.rd.testbusiness.business.basics.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 海康威视摄像头管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "海康威视摄像头管理VO对象")
public class BuInstrumentCameraInfoVO extends BuInstrumentCameraInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @JoinSelect(joinClass = BuInstrumentWorkstationInfo.class, mainId = "workstationId")
    @TableField(exist = false)
    private BuInstrumentWorkstationInfo buInstrumentWorkstationInfo;

    // flvUrl
    @TableField(exist = false)
    private String flvUrl;

    @TableField(exist = false)
    private boolean tfTesting = false;

    @TableField(exist = false)
    private String largeScreenTitle;
    
}
