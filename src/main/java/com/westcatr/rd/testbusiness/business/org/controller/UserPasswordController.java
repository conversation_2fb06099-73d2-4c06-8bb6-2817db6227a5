package com.westcatr.rd.testbusiness.business.org.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.testbusiness.business.org.pojo.dto.UpdatePasswordDTO;
import com.westcatr.rd.testbusiness.business.org.service.UserPasswordService;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */

@Validated
@Tag(name = "用户管理——用户密码相关接口", description = "用户管理——用户密码相关接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "2") }) })
@Slf4j
@RestController
public class UserPasswordController {

    @Autowired
    private UserPasswordService userPasswordService;

    @Operation(summary = "修改系统用户密码接口")
    @PostMapping("/orgUserInfo/update/password")
    public IResult<String> updatePassword(@RequestBody @Valid UpdatePasswordDTO dto) {
        return IResult.ok("操作成功", userPasswordService.updatePassword(dto, AuthUtil.getUserE()));
    }

    @Operation(summary = "用户自己修改密码接口")
    @PostMapping("/orgUserInfo/self/password")
    public IResult selfUpdatePassword(@RequestBody UpdatePasswordDTO dto) {
        if (StrUtil.isBlank(dto.getPassword()) || StrUtil.isBlank(dto.getCheckPassword())) {
            return IResult.fail("密码不能为空");
        }
        return IResult.auto(userPasswordService.selfUpdatePassword(dto, AuthUtil.getTokenE()));
    }

}
