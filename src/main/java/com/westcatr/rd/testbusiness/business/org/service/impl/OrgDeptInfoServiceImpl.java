package com.westcatr.rd.testbusiness.business.org.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.util.TreeUtil;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.mapper.OrgDeptInfoMapper;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgDeptInfoQuery;
import com.westcatr.rd.testbusiness.business.org.service.OrgDeptInfoService;

import cn.hutool.core.collection.CollUtil;

/**
 * <p>
 * 部门信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Service
public class OrgDeptInfoServiceImpl extends ServiceImpl<OrgDeptInfoMapper, OrgDeptInfo> implements OrgDeptInfoService {

    @Override
    public IPage<OrgDeptInfo> entityPage(OrgDeptInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<OrgDeptInfo>().create(query));
    }

    @Override
    public OrgDeptInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(OrgDeptInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(OrgDeptInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public List<OrgDeptInfo> tree() {
        OrgDeptInfoQuery deptInfoQuery = new OrgDeptInfoQuery();
        deptInfoQuery.setPage(1);
        deptInfoQuery.setSize(9999);
        IPage<OrgDeptInfo> iPage = this.entityPage(deptInfoQuery);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            return TreeUtil.longTree(iPage.getRecords());
        }
        List<OrgDeptInfo> nullList = new ArrayList<>();
        return nullList;
    }

    @Override
    public List<OrgDeptInfo> tree(List<Long> ids) {
        List<OrgDeptInfo> iList = this.list(new LambdaQueryWrapper<OrgDeptInfo>().in(OrgDeptInfo::getId, ids));
        if (CollUtil.isNotEmpty(iList)) {
            return TreeUtil.longTree(iList);
        }
        List<OrgDeptInfo> nullList = new ArrayList<>();
        return nullList;
    }
}
