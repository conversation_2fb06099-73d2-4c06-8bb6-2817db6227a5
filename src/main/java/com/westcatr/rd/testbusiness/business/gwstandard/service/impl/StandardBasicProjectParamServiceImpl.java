package com.westcatr.rd.testbusiness.business.gwstandard.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicProjectParamQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicProjectParamMapper;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectParamService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 实验项目参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class StandardBasicProjectParamServiceImpl extends ServiceImpl<StandardBasicProjectParamMapper, StandardBasicProjectParam> implements StandardBasicProjectParamService {

    @Override
    public IPage<StandardBasicProjectParam> entityPage(StandardBasicProjectParamQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<StandardBasicProjectParam>().create(query));
    }

    @Override
    public StandardBasicProjectParam getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(StandardBasicProjectParam param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(StandardBasicProjectParam param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}
