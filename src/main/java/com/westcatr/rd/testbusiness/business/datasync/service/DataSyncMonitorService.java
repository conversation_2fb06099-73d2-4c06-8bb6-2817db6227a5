package com.westcatr.rd.testbusiness.business.datasync.service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.westcatr.rd.testbusiness.business.datasync.mapper.LogDeviceFunctionCallMapper;
import com.westcatr.rd.testbusiness.business.datasync.mapper.postgresql.PostgreSQLLogDeviceFunctionCallMapper;
import com.westcatr.rd.testbusiness.configs.DataSyncConfig;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据同步监控服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@Service
public class DataSyncMonitorService {

    @Autowired
    private DataSyncConfig dataSyncConfig;

    @Autowired
    private PostgreSQLLogDeviceFunctionCallMapper postgresqlMapper;

    @Autowired
    private LogDeviceFunctionCallMapper mysqlMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String MONITOR_KEY_PREFIX = "data:sync:monitor:";

    /**
     * 获取数据同步健康状态
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();

        try {
            // 检查同步延迟
            long syncDelay = getSyncDelay();
            health.put("syncDelay", syncDelay);
            health.put("syncDelayStatus", syncDelay < 60000 ? "HEALTHY" : "WARNING"); // 1分钟内为健康

            // 检查数据一致性
            Map<String, Object> consistency = checkDataConsistency();
            health.put("dataConsistency", consistency);

            // 检查同步频率
            Map<String, Object> frequency = getSyncFrequency();
            health.put("syncFrequency", frequency);

            // 总体健康状态
            boolean isHealthy = syncDelay < 60000 &&
                    (Boolean) consistency.getOrDefault("isConsistent", false);
            health.put("overallStatus", isHealthy ? "HEALTHY" : "UNHEALTHY");
            health.put("checkTime", new Date());

        } catch (Exception e) {
            log.error("❌ 获取健康状态失败: {}", e.getMessage(), e);
            health.put("overallStatus", "ERROR");
            health.put("error", e.getMessage());
        }

        return health;
    }

    /**
     * 获取同步延迟（毫秒）
     */
    public long getSyncDelay() {
        try {
            // 获取PostgreSQL最新数据时间
            Date pgMaxTime = postgresqlMapper.getMaxCreateTime();

            // 获取MySQL最新同步时间
            Date mysqlMaxTime = mysqlMapper.getLastSyncTime();

            if (pgMaxTime != null && mysqlMaxTime != null) {
                return pgMaxTime.getTime() - mysqlMaxTime.getTime();
            }

            return 0;
        } catch (Exception e) {
            log.error("❌ 计算同步延迟失败: {}", e.getMessage(), e);
            return -1;
        }
    }

    /**
     * 检查数据一致性
     */
    public Map<String, Object> checkDataConsistency() {
        Map<String, Object> consistency = new HashMap<>();

        try {
            // 检查最近1小时的数据
            Date endTime = new Date();
            Date startTime = DateUtil.offsetHour(endTime, -1);

            // 统计PostgreSQL数据量
            Long pgCount = postgresqlMapper.countIncrementalData(startTime);

            // 统计MySQL数据量
            Long mysqlCount = mysqlMapper.countByTimeRange(startTime, endTime);

            consistency.put("postgresqlCount", pgCount);
            consistency.put("mysqlCount", mysqlCount);
            consistency.put("isConsistent", pgCount.equals(mysqlCount));
            consistency.put("difference", pgCount - mysqlCount);
            consistency.put("checkPeriod", "最近1小时");

        } catch (Exception e) {
            log.error("❌ 检查数据一致性失败: {}", e.getMessage(), e);
            consistency.put("isConsistent", false);
            consistency.put("error", e.getMessage());
        }

        return consistency;
    }

    /**
     * 获取同步频率统计
     */
    public Map<String, Object> getSyncFrequency() {
        Map<String, Object> frequency = new HashMap<>();

        try {
            String key = MONITOR_KEY_PREFIX + "frequency";

            // 记录本次检查
            long currentTime = System.currentTimeMillis();
            redisTemplate.opsForZSet().add(key, currentTime, currentTime);

            // 清理1小时前的记录
            long oneHourAgo = currentTime - 3600000;
            redisTemplate.opsForZSet().removeRangeByScore(key, 0, oneHourAgo);

            // 统计最近1小时的同步次数
            Long count = redisTemplate.opsForZSet().count(key, oneHourAgo, currentTime);

            frequency.put("syncCountLastHour", count);
            frequency.put("expectedCount", 3600 / dataSyncConfig.getIntervalSeconds()); // 期望次数
            frequency.put("isNormal", count >= (3600 / dataSyncConfig.getIntervalSeconds()) * 0.8); // 80%以上为正常

            // 设置过期时间
            redisTemplate.expire(key, 2, TimeUnit.HOURS);

        } catch (Exception e) {
            log.error("❌ 获取同步频率失败: {}", e.getMessage(), e);
            frequency.put("isNormal", false);
            frequency.put("error", e.getMessage());
        }

        return frequency;
    }

    /**
     * 记录同步事件
     */
    public void recordSyncEvent(String eventType, Map<String, Object> eventData) {
        try {
            String key = MONITOR_KEY_PREFIX + "events:" + eventType;

            Map<String, Object> event = new HashMap<>();
            event.put("timestamp", new Date());
            event.put("data", eventData);

            // 使用列表存储最近的事件
            redisTemplate.opsForList().leftPush(key, event);

            // 只保留最近100个事件
            redisTemplate.opsForList().trim(key, 0, 99);

            // 设置过期时间
            redisTemplate.expire(key, 24, TimeUnit.HOURS);

        } catch (Exception e) {
            log.error("❌ 记录同步事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取同步事件历史
     */
    public Map<String, Object> getSyncEvents(String eventType, int limit) {
        Map<String, Object> result = new HashMap<>();

        try {
            String key = MONITOR_KEY_PREFIX + "events:" + eventType;

            // 获取最近的事件
            var events = redisTemplate.opsForList().range(key, 0, limit - 1);

            result.put("events", events);
            result.put("total", redisTemplate.opsForList().size(key));
            result.put("eventType", eventType);

        } catch (Exception e) {
            log.error("❌ 获取同步事件失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }

        return result;
    }
}
