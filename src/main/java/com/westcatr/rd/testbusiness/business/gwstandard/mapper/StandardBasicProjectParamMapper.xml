<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicProjectParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="param_name" property="paramName" />
        <result column="param_type" property="paramType" />
        <result column="standard_basic_project_id" property="standardBasicProjectId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, id, param_name, param_type, standard_basic_project_id, update_time
    </sql>

</mapper>
