package com.westcatr.rd.testbusiness.business.gwstandard.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 🧮 模型参数计算结果DTO
 * 
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@Schema(description = "模型参数计算结果")
public class ModelParamCalculateResult {
    
    /**
     * 参数ID
     */
    @Schema(description = "参数ID")
    private Long paramId;
    
    /**
     * 参数键
     */
    @Schema(description = "参数键")
    private String paramKey;
    
    /**
     * 测试项目
     */
    @Schema(description = "测试项目")
    private String testItem;
    
    /**
     * 计算公式
     */
    @Schema(description = "计算公式")
    private String formula;
    
    /**
     * 计算结果
     */
    @Schema(description = "计算结果")
    private BigDecimal calculatedValue;
    
    /**
     * 计算是否成功
     */
    @Schema(description = "计算是否成功")
    private Boolean success;
    
    /**
     * 错误信息
     */
    @Schema(description = "错误信息，计算失败时有值")
    private String errorMessage;
}
