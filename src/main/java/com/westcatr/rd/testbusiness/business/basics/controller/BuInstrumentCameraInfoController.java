package com.westcatr.rd.testbusiness.business.basics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentCameraInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.BuInstrumentCameraInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentCameraInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;

/**
 * BuInstrumentCameraInfo 控制器
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Validated
@Tag(name = "海康威视摄像头管理接口", description = "海康威视摄像头管理接口")
@Slf4j
@RestController
public class BuInstrumentCameraInfoController {

    @Autowired
    private BuInstrumentCameraInfoService buInstrumentCameraInfoService;

    @Operation(summary = "获取海康威视摄像头管理分页数据")
    @PostMapping("/buInstrumentCameraInfo/page")
    public IResult<IPage<BuInstrumentCameraInfo>> getBuInstrumentCameraInfoPage(
            @RequestBody BuInstrumentCameraInfoQuery query) {
        return IResult.ok(buInstrumentCameraInfoService.entityPage(query));
    }

    @Operation(summary = "获取海康威视摄像头管理数据")
    @PostMapping("/buInstrumentCameraInfo/get")
    public IResult<BuInstrumentCameraInfo> getBuInstrumentCameraInfoById(@RequestBody @Id ID id) {
        return IResult.ok(buInstrumentCameraInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增海康威视摄像头管理数据")
    @PostMapping("/buInstrumentCameraInfo/add")
    public IResult addBuInstrumentCameraInfo(@RequestBody @Validated(Insert.class) BuInstrumentCameraInfo param) {
        return IResult.auto(buInstrumentCameraInfoService.saveEntity(param));
    }

    @Operation(summary = "更新海康威视摄像头管理数据")
    @PostMapping("/buInstrumentCameraInfo/update")
    public IResult updateBuInstrumentCameraInfoById(
            @RequestBody @Validated(Update.class) BuInstrumentCameraInfo param) {
        return IResult.auto(buInstrumentCameraInfoService.updateEntity(param));
    }

    @Operation(summary = "删除海康威视摄像头管理数据")
    @PostMapping("/buInstrumentCameraInfo/delete")
    public IResult deleteBuInstrumentCameraInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            buInstrumentCameraInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取海康威视摄像头管理VO分页数据")
    @PostMapping("/buInstrumentCameraInfo/voPage")
    public IResult<IPage<BuInstrumentCameraInfoVO>> getBuInstrumentCameraInfoVoPage(
            @RequestBody BuInstrumentCameraInfoQuery query) {
        AssociationQuery<BuInstrumentCameraInfoVO> associationQuery = new AssociationQuery<>(
                BuInstrumentCameraInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取海康威视摄像头管理VO数据")
    @PostMapping("/buInstrumentCameraInfo/getVo")
    public IResult<BuInstrumentCameraInfoVO> getBuInstrumentCameraInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<BuInstrumentCameraInfoVO> associationQuery = new AssociationQuery<>(
                BuInstrumentCameraInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @GetMapping("/buInstrumentCameraInfo/stream")
    public IResult<String> getCameraStream(@RequestParam Long id) {
        return IResult.ok("成功",
                buInstrumentCameraInfoService.getCameraStreamUrl(id));
    }

    @Operation(summary = "获取海康威视摄像头管理列表")
    @GetMapping("/buInstrumentCameraInfo/list")
    public IResult<List<BuInstrumentCameraInfoVO>> getBuInstrumentCameraInfoList() {
        return IResult.ok(buInstrumentCameraInfoService.getCameraList());
    }

}
