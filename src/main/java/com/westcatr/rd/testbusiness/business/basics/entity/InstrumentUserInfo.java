package com.westcatr.rd.testbusiness.business.basics.entity;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仪器设备—检测人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_instrument_user_info")
@Schema(description = "仪器设备—检测人员表")
public class InstrumentUserInfo extends Model<InstrumentUserInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("instrument_new_id")
    private Long instrumentNewId;

    @TableField("user_id")
    private Long userId;

    @Length(max = 255, message = "长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("user_full_name")
    private String userFullName;

    @TableField("sort_num")
    private Integer sortNum;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
