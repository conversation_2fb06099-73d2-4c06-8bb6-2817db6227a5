package com.westcatr.rd.testbusiness.business.datasync.controller;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据同步测试控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@RestController
@RequestMapping("/datasync/test")
@Tag(name = "数据同步测试", description = "数据同步功能的测试接口")
public class DataSyncTestController {

    @Autowired(required = false)
    @Qualifier("postgresqlJdbcTemplate")
    private JdbcTemplate postgresqlJdbcTemplate;

    /**
     * 测试PostgreSQL连接
     */
    @GetMapping("/postgresql")
    @Operation(summary = "测试PostgreSQL连接", description = "测试PostgreSQL数据库连接是否正常")
    public ResponseEntity<Map<String, Object>> testPostgreSQLConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (postgresqlJdbcTemplate == null) {
                result.put("success", false);
                result.put("message", "PostgreSQL JdbcTemplate未初始化");
                return ResponseEntity.badRequest().body(result);
            }

            // 测试连接
            Integer count = postgresqlJdbcTemplate.queryForObject("SELECT 1", Integer.class);

            result.put("success", true);
            result.put("message", "PostgreSQL连接正常");
            result.put("testResult", count);

            log.info("✅ PostgreSQL连接测试成功");
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ PostgreSQL连接测试失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "PostgreSQL连接失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 测试查询PostgreSQL中的表
     */
    @GetMapping("/postgresql/table")
    @Operation(summary = "测试查询PostgreSQL表", description = "测试查询zzzzz_log_device_function_call表")
    public ResponseEntity<Map<String, Object>> testPostgreSQLTable() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (postgresqlJdbcTemplate == null) {
                result.put("success", false);
                result.put("message", "PostgreSQL JdbcTemplate未初始化");
                return ResponseEntity.badRequest().body(result);
            }

            // 测试查询表结构
            String sql = "SELECT COUNT(*) FROM zzzzz_log_device_report";
            Long count = postgresqlJdbcTemplate.queryForObject(sql, Long.class);

            result.put("success", true);
            result.put("message", "PostgreSQL表查询成功");
            result.put("tableCount", count);

            log.info("✅ PostgreSQL表查询成功，记录数: {}", count);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❌ PostgreSQL表查询失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "PostgreSQL表查询失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 获取PostgreSQL配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取配置信息", description = "获取数据同步相关的配置信息")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> result = new HashMap<>();

        result.put("postgresqlJdbcTemplate", postgresqlJdbcTemplate != null ? "已初始化" : "未初始化");
        result.put("message", "配置信息获取成功");

        return ResponseEntity.ok(result);
    }

    /**
     * 时区诊断接口
     */
    @GetMapping("/timezone")
    @Operation(summary = "时区诊断", description = "诊断PostgreSQL和MySQL的时区问题")
    public ResponseEntity<Map<String, Object>> diagnosisTimezone() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取系统时区信息
            ZoneId systemZone = ZoneId.systemDefault();
            TimeZone systemTimeZone = TimeZone.getDefault();

            result.put("systemTimezone", systemZone.toString());
            result.put("systemTimezoneOffset", systemTimeZone.getOffset(System.currentTimeMillis()) / (1000 * 60 * 60));

            // 获取当前时间在不同时区的表示
            ZonedDateTime now = ZonedDateTime.now();
            ZonedDateTime utcNow = now.withZoneSameInstant(ZoneId.of("UTC"));
            ZonedDateTime beijingNow = now.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));

            Map<String, String> timeInfo = new HashMap<>();
            timeInfo.put("system", now.toString());
            timeInfo.put("utc", utcNow.toString());
            timeInfo.put("beijing", beijingNow.toString());
            result.put("currentTime", timeInfo);

            if (postgresqlJdbcTemplate != null) {
                // 查询PostgreSQL的时区设置
                String pgTimezone = postgresqlJdbcTemplate.queryForObject("SHOW timezone", String.class);
                result.put("postgresqlTimezone", pgTimezone);

                // 查询PostgreSQL中的当前时间
                String pgCurrentTime = postgresqlJdbcTemplate.queryForObject("SELECT NOW()::text", String.class);
                result.put("postgresqlCurrentTime", pgCurrentTime);

                // 查询最近的几条数据的create_time
                List<Map<String, Object>> recentData = postgresqlJdbcTemplate.queryForList(
                        "SELECT id, create_time, " +
                                "create_time AT TIME ZONE 'UTC' AS create_time_utc, " +
                                "create_time AT TIME ZONE 'Asia/Shanghai' AS create_time_beijing " +
                                "FROM zzzzz_log_device_report " +
                                "ORDER BY create_time DESC LIMIT 3");

                result.put("recentPostgresqlData", recentData);
            } else {
                result.put("postgresqlInfo", "PostgreSQL连接未初始化");
            }

            result.put("success", true);
            result.put("message", "时区诊断完成");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("❤️ 时区诊断失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "时区诊断失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(result);
        }
    }
}
