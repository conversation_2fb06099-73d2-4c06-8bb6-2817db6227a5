package com.westcatr.rd.testbusiness.business.jztask.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskArrangementInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—任务列表排期表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "荆州—任务列表排期表VO对象")
public class JzTaskArrangementInfoVO extends JzTaskArrangementInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "设备名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentNewInfo.class, mainId = "equipmentId")
    private InstrumentNewInfo instrumentNewInfo;

    @Schema(description = "设备名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentNewInfo.class, mainId = "equipmentId", field = "sbmc")
    private String equipmentName;

    /* @Schema(description = "标准名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = StandardBasicInstrumentInfo.class, mainId = "gwBzId", field = "project_name")
    private String gwBzName; */

    @Schema(description = "测试人员姓名")
    @TableField(exist = false)
    @JoinSelect(joinClass = OrgUserInfo.class, mainId = "testUserId", field = "full_name")
    private String testUserFullName;

    @Schema(description = "工位名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = BuInstrumentWorkstationInfo.class, mainId = "workstationId", field = "workstation_name")
    private String workstationName;
}
