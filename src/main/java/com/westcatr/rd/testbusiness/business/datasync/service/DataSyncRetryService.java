package com.westcatr.rd.testbusiness.business.datasync.service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import org.springframework.stereotype.Service;

import com.westcatr.rd.testbusiness.configs.DataSyncConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 数据同步重试服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@Service
public class DataSyncRetryService {

    @Autowired
    private DataSyncConfig dataSyncConfig;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String RETRY_KEY_PREFIX = "data:sync:retry:";
    private static final String FAILURE_KEY_PREFIX = "data:sync:failure:";

    /**
     * 执行带重试的数据同步操作
     */
    public void executeWithRetry(Runnable syncOperation) throws Exception {
        int maxAttempts = dataSyncConfig.getMaxRetryTimes();
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                syncOperation.run();
                // 成功后清除失败记录
                clearFailureRecord();
                return;
            } catch (Exception e) {
                lastException = e;
                log.warn("🔄 数据同步第 {} 次尝试失败: {}", attempt, e.getMessage());

                if (attempt < maxAttempts) {
                    // 等待后重试
                    try {
                        long delay = dataSyncConfig.getRetryDelaySeconds() * 1000L * attempt;
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                }
            }
        }

        // 所有重试都失败了
        recordFailure(lastException);
        throw new RuntimeException("数据同步重试 " + maxAttempts + " 次后仍然失败", lastException);
    }

    /**
     * 记录同步失败信息
     */
    public void recordFailure(Exception exception) {
        try {
            String key = FAILURE_KEY_PREFIX + "latest";
            
            Map<String, Object> failureInfo = new HashMap<>();
            failureInfo.put("timestamp", new Date());
            failureInfo.put("errorMessage", exception.getMessage());
            failureInfo.put("errorType", exception.getClass().getSimpleName());
            failureInfo.put("stackTrace", getStackTrace(exception));
            
            redisTemplate.opsForValue().set(key, failureInfo, 24, TimeUnit.HOURS);
            
            // 增加失败计数
            String countKey = FAILURE_KEY_PREFIX + "count";
            redisTemplate.opsForValue().increment(countKey);
            redisTemplate.expire(countKey, 24, TimeUnit.HOURS);
            
            log.error("🔥 记录数据同步失败: {}", exception.getMessage());
            
        } catch (Exception e) {
            log.error("❌ 记录失败信息时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 清除失败记录
     */
    public void clearFailureRecord() {
        try {
            String latestKey = FAILURE_KEY_PREFIX + "latest";
            String countKey = FAILURE_KEY_PREFIX + "count";
            
            redisTemplate.delete(latestKey);
            redisTemplate.delete(countKey);
            
            log.debug("🧹 已清除同步失败记录");
            
        } catch (Exception e) {
            log.error("❌ 清除失败记录时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取失败统计信息
     */
    public Map<String, Object> getFailureStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            String latestKey = FAILURE_KEY_PREFIX + "latest";
            String countKey = FAILURE_KEY_PREFIX + "count";
            
            // 获取最新失败信息
            Object latestFailure = redisTemplate.opsForValue().get(latestKey);
            stats.put("latestFailure", latestFailure);
            
            // 获取失败次数
            Object failureCount = redisTemplate.opsForValue().get(countKey);
            stats.put("failureCount", failureCount != null ? failureCount : 0);
            
            // 判断是否有失败
            stats.put("hasFailures", latestFailure != null);
            
        } catch (Exception e) {
            log.error("❌ 获取失败统计失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    /**
     * 检查是否应该跳过同步（基于失败频率）
     */
    public boolean shouldSkipSync() {
        try {
            String countKey = FAILURE_KEY_PREFIX + "count";
            Object countObj = redisTemplate.opsForValue().get(countKey);
            
            if (countObj instanceof Integer) {
                int failureCount = (Integer) countObj;
                // 如果连续失败超过配置的最大重试次数，暂停同步
                return failureCount >= dataSyncConfig.getMaxRetryTimes() * 3;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("❌ 检查是否跳过同步失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 重置失败状态
     */
    public void resetFailureStatus() {
        try {
            String pattern = FAILURE_KEY_PREFIX + "*";
            var keys = redisTemplate.keys(pattern);
            
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("🔄 已重置数据同步失败状态");
            }
            
        } catch (Exception e) {
            log.error("❌ 重置失败状态时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取异常堆栈信息
     */
    private String getStackTrace(Exception exception) {
        if (exception == null) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(exception.toString()).append("\n");
        
        StackTraceElement[] elements = exception.getStackTrace();
        for (int i = 0; i < Math.min(elements.length, 10); i++) { // 只取前10行
            sb.append("\tat ").append(elements[i].toString()).append("\n");
        }
        
        return sb.toString();
    }

    /**
     * 执行同步健康检查
     */
    public Map<String, Object> performHealthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 检查失败状态
            Map<String, Object> failureStats = getFailureStatistics();
            boolean hasRecentFailures = (Boolean) failureStats.getOrDefault("hasFailures", false);
            
            // 检查是否应该跳过同步
            boolean shouldSkip = shouldSkipSync();
            
            health.put("failureStatistics", failureStats);
            health.put("shouldSkipSync", shouldSkip);
            health.put("isHealthy", !hasRecentFailures && !shouldSkip);
            health.put("checkTime", new Date());
            
            if (shouldSkip) {
                health.put("recommendation", "建议检查数据源连接和配置，考虑重置失败状态");
            }
            
        } catch (Exception e) {
            log.error("❌ 执行健康检查失败: {}", e.getMessage(), e);
            health.put("isHealthy", false);
            health.put("error", e.getMessage());
        }
        
        return health;
    }
}
