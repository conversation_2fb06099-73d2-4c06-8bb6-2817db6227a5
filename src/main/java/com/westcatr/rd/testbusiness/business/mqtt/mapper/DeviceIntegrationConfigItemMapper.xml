<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.mqtt.mapper.DeviceIntegrationConfigItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="create_time" property="createTime" />
        <result column="description" property="description" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="param_mapping" property="paramMapping" />
        <result column="project_code" property="projectCode" />
        <result column="project_name" property="projectName" />
        <result column="status" property="status" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        config_id, create_time, description, id, item_code, item_name, param_mapping, project_code, project_name, status, update_time
    </sql>

</mapper>
