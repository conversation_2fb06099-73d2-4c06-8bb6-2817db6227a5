package com.westcatr.rd.testbusiness.business.jztask.entity;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—任务列表排期表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_task_arrangement_info")
@Schema(description = "荆州—任务列表排期表")
public class JzTaskArrangementInfo extends Model<JzTaskArrangementInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "设备id")
    @TableField("equipment_id")
    private Long equipmentId;

    @Schema(description = "实验标准id")
    @TableField("gw_bz_id")
    private String gwBzId;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "测试人员id")
    @TableField("test_user_id")
    private Long testUserId;

    @Schema(description = "工位id")
    @TableField("workstation_id")
    private Long workstationId;

    @Schema(description = "任务id")
    @TableField("task_id")
    private Long taskId;

    // 模型id
    @TableField("model_ids")
    private String modelIds;

    @Schema(description = "实验标准名称")
    @TableField("gw_bz_name")
    private String gwBzName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
