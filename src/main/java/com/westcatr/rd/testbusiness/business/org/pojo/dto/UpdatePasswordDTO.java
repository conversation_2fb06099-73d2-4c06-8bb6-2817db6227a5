package com.westcatr.rd.testbusiness.business.org.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020-06-09 19:59
 */
@Schema(description = "更新密码DTO")
@Data
public class UpdatePasswordDTO {

    @NotNull(message = "用户id不能为空")
    private Long id;

    @NotBlank(message = "用户密码不能为空")
    private String checkPassword;

    private String password;
}
