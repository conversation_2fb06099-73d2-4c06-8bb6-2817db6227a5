package com.westcatr.rd.testbusiness.business.org.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "找回密码DTO")
public class RetrievePasswordDTO {

    @NotNull(message = "请选择找回密码方式")
    @Schema(description = "找回密码类型")
    private Integer type;

    @NotBlank(message = "邮箱或者手机号不能为空")
    @Schema(description = "找回密码依据")
    private String param;
}
