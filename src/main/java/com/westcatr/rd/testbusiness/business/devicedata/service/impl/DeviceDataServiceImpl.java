package com.westcatr.rd.testbusiness.business.devicedata.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataRequest;
import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceDataResponse;
import com.westcatr.rd.testbusiness.business.devicedata.dto.DeviceResponseConfig;
import com.westcatr.rd.testbusiness.business.devicedata.dto.ExperimentConfig;
import com.westcatr.rd.testbusiness.business.devicedata.entity.DeviceDataConfig;
import com.westcatr.rd.testbusiness.business.devicedata.exception.DeviceDataException;
import com.westcatr.rd.testbusiness.business.devicedata.mapper.DeviceDataConfigMapper;
import com.westcatr.rd.testbusiness.business.devicedata.mapper.DeviceDataQueryMapper;
import com.westcatr.rd.testbusiness.business.devicedata.service.DeviceDataService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 设备对接数据管理服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@Service
public class DeviceDataServiceImpl implements DeviceDataService {

    @Autowired
    private DeviceDataConfigMapper deviceDataConfigMapper;

    @Autowired
    private DeviceDataQueryMapper deviceDataQueryMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<DeviceDataResponse> getDeviceData(DeviceDataRequest request) {
        log.info("🔍 开始查询设备数据，设备代码: {}, 时间范围: {} - {}",
                request.getDeviceCode(), request.getStartTime(), request.getEndTime());

        // 参数校验
        if (request == null || !StringUtils.hasText(request.getDeviceCode())) {
            throw new DeviceDataException("设备代码不能为空");
        }

        // 如果时间参数为空，设置默认时间范围（查询最新数据）
        if (request.getStartTime() == null || request.getEndTime() == null) {
            log.info("⏰ 时间参数为空，设置默认时间范围查询最新数据");
            // 设置一个较大的时间范围来查询最新数据
            Calendar cal = Calendar.getInstance();
            request.setEndTime(cal.getTime()); // 当前时间
            cal.add(Calendar.YEAR, -10); // 10年前
            request.setStartTime(cal.getTime());
        } else if (request.getStartTime().after(request.getEndTime())) {
            throw new DeviceDataException("开始时间不能晚于结束时间");
        }

        try {
            // 1. 查询设备配置
            DeviceDataConfig config = getDeviceConfig(request.getDeviceCode());
            if (config == null) {
                log.warn("⚠️ 未找到设备配置，设备代码: {}", request.getDeviceCode());
                throw new DeviceDataException("未找到设备配置，设备代码: " + request.getDeviceCode());
            }

            // 2. 解析配置
            DeviceResponseConfig responseConfig = parseResponseConfig(config.getResponseBody());
            if (responseConfig == null || responseConfig.getExperiments() == null) {
                log.warn("⚠️ 设备配置解析失败，设备代码: {}", request.getDeviceCode());
                return new ArrayList<>();
            }

            // 3. 查询各个试验的数据
            List<DeviceDataResponse> responses = new ArrayList<>();
            for (ExperimentConfig experiment : responseConfig.getExperiments()) {
                DeviceDataResponse response = queryExperimentData(experiment, request);
                if (response != null) {
                    responses.add(response);
                }
            }

            log.info("✅ 设备数据查询完成，设备代码: {}, 返回试验数量: {}",
                    request.getDeviceCode(), responses.size());
            return responses;

        } catch (Exception e) {
            log.error("❌ 设备数据查询失败，设备代码: {}, 错误: {}",
                    request.getDeviceCode(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public DeviceDataConfig getDeviceConfig(String deviceCode) {
        if (!StringUtils.hasText(deviceCode)) {
            log.warn("⚠️ 设备代码为空");
            return null;
        }

        try {
            DeviceDataConfig config = deviceDataConfigMapper.selectByDeviceCode(deviceCode);
            if (config != null) {
                log.debug("✅ 找到设备配置，设备代码: {}, 配置ID: {}", deviceCode, config.getId());
            } else {
                log.warn("⚠️ 未找到设备配置，设备代码: {}", deviceCode);
            }
            return config;
        } catch (Exception e) {
            log.error("❌ 查询设备配置失败，设备代码: {}, 错误: {}", deviceCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析响应配置
     */
    private DeviceResponseConfig parseResponseConfig(String responseBody) {
        if (!StringUtils.hasText(responseBody)) {
            log.warn("⚠️ 响应配置为空");
            return null;
        }

        try {
            return objectMapper.readValue(responseBody, DeviceResponseConfig.class);
        } catch (Exception e) {
            log.error("❌ 解析响应配置失败，错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询试验数据
     */
    private DeviceDataResponse queryExperimentData(ExperimentConfig experiment, DeviceDataRequest request) {
        if (experiment == null || !StringUtils.hasText(experiment.getCode())) {
            log.warn("⚠️ 试验配置无效");
            return null;
        }

        try {
            // 检查设备是否存在数据记录
            int dataExists = deviceDataQueryMapper.checkDeviceDataExists(request.getDeviceCode());
            if (dataExists == 0) {
                log.warn("⚠️ 设备无数据记录: {}", request.getDeviceCode());
                return null;
            }

            // 查询最新数据
            Map<String, Object> data;

            // 判断是否为默认时间范围（查询最新数据）
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.YEAR, -9); // 9年前，用于判断是否为默认时间范围
            boolean isDefaultTimeRange = request.getStartTime().before(cal.getTime());

            if (isDefaultTimeRange) {
                // 查询最新一条数据（不限时间）
                data = deviceDataQueryMapper.selectLatestDataByDeviceCodeNoTime(request.getDeviceCode());
                log.debug("🔍 查询最新数据（不限时间），设备代码: {}", request.getDeviceCode());
            } else {
                // 按时间范围查询
                data = deviceDataQueryMapper.selectLatestDataByDeviceCode(
                        request.getDeviceCode(), request.getStartTime(), request.getEndTime());
                log.debug("🔍 按时间范围查询，设备代码: {}, 时间范围: {} - {}",
                        request.getDeviceCode(), request.getStartTime(), request.getEndTime());
            }

            if (data == null || data.isEmpty()) {
                log.warn("⚠️ 未找到数据，设备代码: {}, 查询方式: {}",
                        request.getDeviceCode(), isDefaultTimeRange ? "最新数据" : "时间范围查询");
                return null;
            }

            // 转换数据格式
            return convertToResponse(experiment, data);

        } catch (Exception e) {
            log.error("❌ 查询试验数据失败，设备代码: {}, 错误: {}",
                    request.getDeviceCode(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换为响应格式
     */
    private DeviceDataResponse convertToResponse(ExperimentConfig experiment, Map<String, Object> data) {
        DeviceDataResponse response = new DeviceDataResponse();
        response.setName(experiment.getName());
        response.setCode(experiment.getCode());

        // 设置数据创建时间
        if (data.get("create_time") != null) {
            Object createTimeObj = data.get("create_time");
            try {
                if (createTimeObj instanceof java.time.LocalDateTime) {
                    // 将 LocalDateTime 转换为 Date
                    java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) createTimeObj;
                    Date date = Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
                    response.setDataTime(date);
                } else if (createTimeObj instanceof Date) {
                    response.setDataTime((Date) createTimeObj);
                } else if (createTimeObj instanceof java.sql.Timestamp) {
                    response.setDataTime(new Date(((java.sql.Timestamp) createTimeObj).getTime()));
                } else {
                    log.warn("⚠️ 未知的时间类型: {}", createTimeObj.getClass().getName());
                }
            } catch (Exception e) {
                log.error("❌ 时间转换失败: {}", e.getMessage(), e);
            }
        }

        List<DeviceDataResponse.TestResult> results = new ArrayList<>();

        try {
            // 从 zzzzz_log_device_function_call 表的数据中解析 output_params
            String outputParamsStr = (String) data.get("output_params");
            // 打印原始 output_params 字符串
            // log.info("原始 output_params: {}", outputParamsStr);
            if (!StringUtils.hasText(outputParamsStr)) {
                log.warn("⚠️ output_params 为空，设备: {}", data.get("device_id"));
                response.setResult(results);
                return response;
            }

            // 解析 output_params JSON
            Map<String, Object> outputParams = objectMapper.readValue(outputParamsStr,
                    new TypeReference<Map<String, Object>>() {
                    });
            log.debug("📊 解析 output_params: {}", outputParams);

            // 检查是否为错误响应
            if (isErrorResponse(outputParams)) {
                log.warn("⚠️ 检测到错误响应，跳过处理，设备: {}, 错误信息: {}",
                        data.get("device_id"), outputParams.get("_msg"));
                response.setResult(results);
                return response;
            }

            // 提取试验数据
            Map<String, Object> testData = extractTestData(outputParams, experiment);

            if (experiment.getMapping() != null && testData != null) {
                for (Map.Entry<String, String> entry : experiment.getMapping().entrySet()) {
                    String displayName = entry.getKey();
                    String fieldName = entry.getValue();

                    Object value = testData.get(fieldName);
                    if (value != null) {
                        DeviceDataResponse.TestResult result = new DeviceDataResponse.TestResult();
                        result.setName(displayName);
                        result.setCode(fieldName);
                        result.setValue(value.toString());
                        results.add(result);
                    }
                }
            }

        } catch (Exception e) {
            log.error("❌ 解析设备数据失败，试验: {}, 错误: {}", experiment.getName(), e.getMessage(), e);
        }

        response.setResult(results);

        log.debug("✅ 转换试验数据完成，试验: {}, 结果数量: {}",
                experiment.getName(), results.size());

        return response;
    }

    /**
     * 检查是否为错误响应
     * 适配新的数据结构：message.bodyData
     */
    private boolean isErrorResponse(Map<String, Object> outputParams) {
        // 检查新格式：message.bodyData 是否存在
        if (outputParams.containsKey("message")) {
            Object messageObj = outputParams.get("message");
            if (messageObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> messageMap = (Map<String, Object>) messageObj;

                // 检查是否有 bodyData 字段
                if (messageMap.containsKey("bodyData")) {
                    Object bodyDataObj = messageMap.get("bodyData");
                    // bodyData 存在且不为空，认为是有效响应
                    if (bodyDataObj != null) {
                        // 对于字符串类型的 bodyData，检查是否有内容
                        if (bodyDataObj instanceof String) {
                            return !StringUtils.hasText((String) bodyDataObj);
                        }
                        // 对于 Map 类型的 bodyData，检查是否为空
                        else if (bodyDataObj instanceof Map) {
                            return ((Map<?, ?>) bodyDataObj).isEmpty();
                        }
                        // 其他类型认为是有效的
                        return false;
                    }
                }
                // 没有 bodyData 字段，认为是错误响应
                return true;
            }
        }

        // 兼容旧格式：检查是否包含错误状态码
        Object status = outputParams.get("_status");
        if (status != null && (status.equals(500) || "500".equals(status.toString()))) {
            return true;
        }

        // 兼容旧格式：检查是否包含错误消息
        Object msg = outputParams.get("_msg");
        if (msg != null && StringUtils.hasText(msg.toString())) {
            String msgStr = msg.toString();
            // 检查常见的错误消息
            if (msgStr.contains("连接器未连接") ||
                    msgStr.contains("连接器未启动") ||
                    msgStr.contains("错误") ||
                    msgStr.contains("失败")) {
                return true;
            }
        }

        // 兼容旧格式：检查是否缺少有效的 body 字段
        Object body = outputParams.get("body");
        if (body == null || !StringUtils.hasText(body.toString())) {
            return true;
        }

        return false;
    }

    /**
     * 从 output_params 中提取试验数据
     * 支持多种数据格式：
     * 1. message.bodyData 数组结构（如万测变压器）
     * 2. message.bodyData 嵌套数组结构（如维卡试验机）
     * 3. 兼容旧格式 body 字段
     *
     * <AUTHOR>
     * @date 2025-01-17
     * @updated 2025-06-17 新增维卡试验机嵌套数组格式支持
     */
    private Map<String, Object> extractTestData(Map<String, Object> outputParams, ExperimentConfig experiment) {
        try {
            // 优先检查新格式：message.bodyData 数组
            if (outputParams.containsKey("message")) {
                Object messageObj = outputParams.get("message");
                if (messageObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> messageMap = (Map<String, Object>) messageObj;

                    if (messageMap.containsKey("bodyData")) {
                        Object bodyDataObj = messageMap.get("bodyData");
                        log.debug("📊 从 message.bodyData 中提取数据: {}", bodyDataObj);

                        // 🔥 新增：处理 bodyData 数组格式（accessId 设备类型）
                        if (bodyDataObj instanceof List) {
                            List<?> bodyDataList = (List<?>) bodyDataObj;
                            log.debug("📋 检测到 bodyData 数组格式，数组长度: {}", bodyDataList.size());

                            // 根据试验配置的 tableName 查找匹配的数据
                            String targetTableName = experiment.getTableName();
                            if (StringUtils.hasText(targetTableName)) {
                                for (Object item : bodyDataList) {
                                    if (item instanceof Map) {
                                        @SuppressWarnings("unchecked")
                                        Map<String, Object> itemMap = (Map<String, Object>) item;

                                        // 检查 tableName 字段是否匹配
                                        Object tableNameObj = itemMap.get("tableName");
                                        if (tableNameObj != null && targetTableName.equals(tableNameObj.toString())) {
                                            // 提取 bodyData 字段中的实际数据
                                            Object actualBodyData = itemMap.get("bodyData");
                                            if (actualBodyData instanceof Map) {
                                                @SuppressWarnings("unchecked")
                                                Map<String, Object> actualDataMap = (Map<String, Object>) actualBodyData;
                                                log.debug("✅ 找到匹配的试验数据，tableName: {}, 数据: {}", targetTableName,
                                                        actualDataMap);
                                                return actualDataMap;
                                            }
                                            // 新增：处理 bodyData 为数组的情况（如维卡试验机格式）
                                            else if (actualBodyData instanceof List) {
                                                List<?> actualBodyDataList = (List<?>) actualBodyData;
                                                if (!actualBodyDataList.isEmpty()
                                                        && actualBodyDataList.get(0) instanceof Map) {
                                                    @SuppressWarnings("unchecked")
                                                    Map<String, Object> firstDataItem = (Map<String, Object>) actualBodyDataList
                                                            .get(0);
                                                    log.debug("✅ 找到匹配的试验数据（数组格式），tableName: {}, 数据: {}",
                                                            targetTableName,
                                                            firstDataItem);
                                                    return firstDataItem;
                                                }
                                            }
                                        }
                                    }
                                }
                                log.warn("⚠️ 未找到匹配的试验数据，目标 tableName: {}", targetTableName);
                            } else {
                                log.warn("⚠️ 试验配置中未设置 tableName，试验: {}", experiment.getName());
                            }
                        }
                        // 兼容原有的 Map 格式处理
                        else if (bodyDataObj instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> bodyDataMap = (Map<String, Object>) bodyDataObj;

                            // 检查是否有 content 字段（如 wanceTransformer 格式）
                            if (bodyDataMap.containsKey("content") && bodyDataMap.get("content") instanceof List) {
                                List<?> contentList = (List<?>) bodyDataMap.get("content");
                                if (!contentList.isEmpty() && contentList.get(0) instanceof Map) {
                                    @SuppressWarnings("unchecked")
                                    Map<String, Object> firstItem = (Map<String, Object>) contentList.get(0);
                                    return firstItem;
                                }
                            }
                            // 检查是否有 data 字段（如 sqlLiteProduct 格式）
                            else if (bodyDataMap.containsKey("data") && bodyDataMap.get("data") instanceof List) {
                                List<?> dataList = (List<?>) bodyDataMap.get("data");
                                if (!dataList.isEmpty() && dataList.get(0) instanceof Map) {
                                    @SuppressWarnings("unchecked")
                                    Map<String, Object> firstItem = (Map<String, Object>) dataList.get(0);
                                    return firstItem;
                                }
                            }
                            // 检查是否有 fileContent 字段（如 dianrong 格式）
                            else if (bodyDataMap.containsKey("fileContent")) {
                                String fileContent = String.valueOf(bodyDataMap.get("fileContent"));
                                // 解析 fileContent 为键值对
                                Map<String, Object> parsedData = parseFileContent(fileContent);
                                return parsedData;
                            }
                            // 直接返回 bodyData
                            else {
                                return bodyDataMap;
                            }
                        } else if (bodyDataObj instanceof String) {
                            // 处理字符串格式的 bodyData（如 eleMachine 格式）
                            String bodyDataStr = (String) bodyDataObj;
                            Map<String, Object> parsedData = parseTextContent(bodyDataStr);
                            return parsedData;
                        }
                    }
                }
            }

            // 兼容旧格式：检查是否有 body 字段
            if (outputParams.containsKey("body")) {
                Object bodyObj = outputParams.get("body");
                log.debug("📊 使用旧格式 body 字段提取数据: {}", bodyObj);

                if (bodyObj instanceof String) {
                    String bodyStr = (String) bodyObj;

                    // 尝试解析 body 字符串为 JSON 数组
                    if (bodyStr.startsWith("[") && bodyStr.endsWith("]")) {
                        List<Map<String, Object>> bodyList = objectMapper.readValue(bodyStr,
                                new TypeReference<List<Map<String, Object>>>() {
                                });
                        if (!bodyList.isEmpty()) {
                            return bodyList.get(0); // 返回第一条数据
                        }
                    }
                    // 尝试解析为单个 JSON 对象
                    else if (bodyStr.startsWith("{") && bodyStr.endsWith("}")) {
                        return objectMapper.readValue(bodyStr,
                                new TypeReference<Map<String, Object>>() {
                                });
                    }
                } else if (bodyObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> bodyMap = (Map<String, Object>) bodyObj;
                    return bodyMap;
                } else if (bodyObj instanceof List) {
                    List<?> bodyList = (List<?>) bodyObj;
                    if (!bodyList.isEmpty() && bodyList.get(0) instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> firstItem = (Map<String, Object>) bodyList.get(0);
                        return firstItem;
                    }
                }
            }

            // 如果没有找到数据，返回空 Map
            log.warn("⚠️ 未找到有效的试验数据，试验: {}", experiment.getName());
            return new HashMap<>();

        } catch (Exception e) {
            log.error("❌ 提取试验数据失败，试验: {}, 错误: {}", experiment.getName(), e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 解析 fileContent 格式的数据
     * 例如："原边电压(V):0;高压电流(mA):0;原边电流(A):0.6872"
     */
    private Map<String, Object> parseFileContent(String fileContent) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(fileContent)) {
            return result;
        }

        try {
            // 按分号分割
            String[] pairs = fileContent.split(";");
            for (String pair : pairs) {
                if (StringUtils.hasText(pair) && pair.contains(":")) {
                    String[] keyValue = pair.split(":", 2);
                    if (keyValue.length == 2) {
                        String key = keyValue[0].trim();
                        String value = keyValue[1].trim();
                        result.put(key, value);
                    }
                }
            }
            log.debug("📊 解析 fileContent 结果: {}", result);
        } catch (Exception e) {
            log.error("❌ 解析 fileContent 失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 解析文本格式的数据
     * 例如：eleMachine 格式的文本数据
     */
    private Map<String, Object> parseTextContent(String textContent) {
        Map<String, Object> result = new HashMap<>();

        if (!StringUtils.hasText(textContent)) {
            return result;
        }

        try {
            // 按行分割
            String[] lines = textContent.split("\n");
            for (String line : lines) {
                if (StringUtils.hasText(line) && line.contains("\t")) {
                    String[] parts = line.split("\t", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();
                        // 过滤掉空值和特殊标记
                        if (StringUtils.hasText(value) && !"/".equals(value) && !value.startsWith("-")) {
                            result.put(key, value);
                        }
                    }
                }
            }
            log.debug("📊 解析文本内容结果: {}", result);
        } catch (Exception e) {
            log.error("❌ 解析文本内容失败: {}", e.getMessage(), e);
        }

        return result;
    }
}
