package com.westcatr.rd.testbusiness.business.mqtt.entity;

import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * MQTT调试响应实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
public class MqttDebugResponse {

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime responseTime;

    /**
     * 原始响应数据
     */
    private Map<String, Object> originalResponse;

    /**
     * 响应内容（解析后的数据）
     */
    private String responseContent;

    /**
     * 是否为默认响应（手动添加的调试响应）
     */
    private boolean isDefault = false;

    /**
     * 响应描述
     */
    private String description;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 创建时间戳
     */
    private long timestamp;

    /**
     * 表名（用于变压器等设备区分不同试验类型）
     */
    private String tableName;

    /**
     * 是否启用（用于标记是否使用此响应）
     */
    private boolean enabled = true;

    /**
     * 优先级（数字越大优先级越高）
     */
    private int priority = 0;
}
