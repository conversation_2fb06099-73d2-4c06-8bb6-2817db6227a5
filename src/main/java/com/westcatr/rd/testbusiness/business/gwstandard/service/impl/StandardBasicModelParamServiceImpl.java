package com.westcatr.rd.testbusiness.business.gwstandard.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicModelParamMapper;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.dto.ModelParamCalculateResult;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicModelParamQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicModelParamService;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectParamService;
import com.westcatr.rd.testbusiness.business.gwstandard.util.FormulaCalculator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 标准-模型参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
@Slf4j
public class StandardBasicModelParamServiceImpl extends ServiceImpl<StandardBasicModelParamMapper, StandardBasicModelParam> implements StandardBasicModelParamService {
    
    @Autowired
    private StandardBasicProjectParamService projectParamService;

    @Override
    public IPage<StandardBasicModelParam> entityPage(StandardBasicModelParamQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<StandardBasicModelParam>().create(query));
    }

    @Override
    public StandardBasicModelParam getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(StandardBasicModelParam param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(StandardBasicModelParam param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
    
    /**
     * 🧮 验证公式并计算结果
     * <p>
     * 用于验证公式的正确性并计算结果
     * </p>
     *
     * @param formula 公式字符串
     * @param paramValues 参数值映射
     * @return 计算结果
     */
    public BigDecimal validateAndCalculateFormula(String formula, Map<String, BigDecimal> paramValues) {
        if (StrUtil.isBlank(formula)) {
            throw new IllegalArgumentException("公式不能为空");
        }
        
        try {
            return FormulaCalculator.calculate(formula, paramValues);
        } catch (Exception e) {
            log.error("公式计算出错: {}, 公式: {}", e.getMessage(), formula);
            throw new IllegalArgumentException("公式格式错误: " + e.getMessage());
        }
    }
    
    /**
     * 🧮 批量计算标准下的所有计算型参数
     * <p>
     * 获取标准下所有计算型参数，并根据其公式计算结果
     * </p>
     *
     * @param standardId 标准ID
     * @return 计算结果列表
     */
    public List<ModelParamCalculateResult> batchCalculateFormulasByStandardId(Long standardId) {
        // 获取标准下的所有模型参数
        List<StandardBasicModelParam> modelParams = this.list(
            new LambdaQueryWrapper<StandardBasicModelParam>()
                .eq(StandardBasicModelParam::getStandardBasicInstrumentId, standardId)
        );
        
        // 构建参数值映射
        Map<String, BigDecimal> paramValues = buildParamValueMap(modelParams);
        
        // 筛选计算型参数并计算结果
        return calculateResults(modelParams, paramValues);
    }
    
    /**
     * 构建参数值映射
     * 
     * @param modelParams 模型参数列表
     * @return 参数值映射
     */
    private Map<String, BigDecimal> buildParamValueMap(List<StandardBasicModelParam> modelParams) {
        Map<String, BigDecimal> paramValues = new HashMap<>();
        for (StandardBasicModelParam param : modelParams) {
            // 使用paramKey作为参数标识
            if (StrUtil.isNotBlank(param.getParamKey()) && StrUtil.isNotBlank(param.getQualifiedStandard())) {
                try {
                    // 尝试将合格标准转换为数值
                    paramValues.put(param.getParamKey(), new BigDecimal(param.getQualifiedStandard().trim()));
                } catch (Exception e) {
                    log.debug("参数{}的值{}无法转换为数值", param.getParamKey(), param.getQualifiedStandard());
                }
            }
        }
        return paramValues;
    }
    
    /**
     * 计算结果
     * 
     * @param modelParams 模型参数列表
     * @param paramValues 参数值映射
     * @return 计算结果列表
     */
    private List<ModelParamCalculateResult> calculateResults(List<StandardBasicModelParam> modelParams, 
                                                          Map<String, BigDecimal> paramValues) {
        List<ModelParamCalculateResult> results = CollUtil.newArrayList();
        
        for (StandardBasicModelParam param : modelParams) {
            // 获取对应的项目参数
            StandardBasicProjectParam projectParam = projectParamService.getById(param.getStandardBasicProjectParamId());
            if (projectParam == null || !"计算型".equals(projectParam.getParamType())) {
                continue;
            }
            
            ModelParamCalculateResult result = new ModelParamCalculateResult();
            result.setParamId(param.getId());
            result.setParamKey(param.getParamKey());
            result.setTestItem(param.getTestItem());
            result.setFormula(param.getJudgeFormula());
            
            try {
                // 计算公式结果
                if (StrUtil.isNotBlank(param.getJudgeFormula())) {
                    BigDecimal calculatedValue = FormulaCalculator.calculate(param.getJudgeFormula(), paramValues);
                    result.setCalculatedValue(calculatedValue);
                    result.setSuccess(true);
                } else {
                    result.setSuccess(false);
                    result.setErrorMessage("公式为空");
                }
            } catch (Exception e) {
                result.setSuccess(false);
                result.setErrorMessage(e.getMessage());
            }
            
            results.add(result);
        }
        
        return results;
    }
}

