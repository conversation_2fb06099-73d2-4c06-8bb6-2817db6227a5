package com.westcatr.rd.testbusiness.business.org.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @version : V1.0
 * <AUTHOR> yingH
 * @Create : 2023-11-02 10:14
 * @Detail : 用户部门信息vo
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("org_user_dept_info")
@Schema(description="用户部门信息表")
public class UserDepInfoVO extends OrgUserDeptInfo {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    /*@Schema(description = "部门信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = OrgDeptInfo.class , mainId = "deptId")
    private OrgDeptInfo orgDeptInfo;*/

    @Schema(description = "部门名称")
    @TableField(exist = false)
    private String deptName;

    @Schema(description = "用户信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = OrgUserInfo.class, mainId = "userId")
    private OrgUserInfo userInfo;

}
