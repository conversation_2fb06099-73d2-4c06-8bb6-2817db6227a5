package com.westcatr.rd.testbusiness.business.basics.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_instrument_new_info")
@Schema(description = "")
public class InstrumentNewInfo extends Model<InstrumentNewInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "专业类型")
    @Length(max = 255, message = "专业类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("professional_type")
    private String professionalType;

    @Schema(description = "设备管理员")
    @Length(max = 255, message = "设备管理员长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("equipment_admin")
    private String equipmentAdmin;

    @Schema(description = "投入使用时间")
    @TableField("commission_date")
    private Date commissionDate;

    @Schema(description = "主键")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "设备编号")
    @Length(max = 255, message = "设备编号长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sbbh")
    private String sbbh;

    @Schema(description = "设备类型")
    @Length(max = 255, message = "设备类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sblb")
    private String sblb;

    @Schema(description = "设备名称")
    @Length(max = 255, message = "设备名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sbmc")
    private String sbmc;

    @Schema(description = "生产厂")
    @Length(max = 255, message = "生产厂长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("scc")
    private String scc;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private String jzyxq = "";

    @TableField(exist = false)
    private String serialNumber;

    @Schema(description = "设备使用状态")
    @TableField("status_equipment_usage")
    private String statusEquipmentUsage;

    @Schema(description = "物联网id")
    @TableField("internet_thing_id")
    private Long internetThingId;

    @Schema(description = "工位id")
    @TableField("workstation_id")
    private Long workstationId;

    // test_user_id
    @TableField("test_user_id")
    @Schema(description = "管理测试人员id")
    private String testUserId;

    @TableField("gw_bz_id")
    @Schema(description = "关联的检测实验id")
    private Long gwBzId;

    @TableField("parameters_inspected_equipment")
    @Schema(description = "设备可检参数（用逗号分开）")
    private String parametersInspectedEquipment;

    @TableField("collection_method")
    @Schema(description = "数据采集方式（直采/填报）")
    private String collectionMethod;

    @TableField(exist = false)
    @Schema(description = "关联的检测实验id")
    private List<Long> gwBzIds;

    // 人员ids
    @TableField(exist = false)
    private List<Long> testUserIds;

    @Schema(description = "设备类型代码")
    @TableField("device_type_code")
    private String deviceTypeCode;

    @Schema(description = "设备参数JSON")
    @TableField("device_params")
    private String deviceParams;

    @Schema(description = "设备对接规范id")
    @TableField("device_integration_config_id")
    private Long deviceIntegrationConfigId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
