package com.westcatr.rd.testbusiness.business.gwstandard.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.dto.FormulaCalculateRequest;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.dto.ModelParamCalculateResult;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicModelParamQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicModelParamVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicModelParamService;
import com.westcatr.rd.testbusiness.business.gwstandard.util.FormulaCalculator;

import cn.hutool.core.text.StrPool;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * StandardBasicModelParam 控制器
 * 
 * <AUTHOR>
 * @since 2025-04-16
 */
@Validated
@Tag(name = "标准-模型参数表接口", description = "标准-模型参数表接口")
@Slf4j
@RestController
@RequestMapping("/standardBasicModelParam")
public class StandardBasicModelParamController {

    @Autowired
    private StandardBasicModelParamService modelParamService;

    // 为了兼容现有代码，保留这个变量
    private StandardBasicModelParamService standardBasicModelParamService;

    @Autowired
    public void setStandardBasicModelParamService(StandardBasicModelParamService service) {
        this.standardBasicModelParamService = service;
        this.modelParamService = service; // 确保两个引用指向同一个服务实例
    }

    @Operation(summary = "获取标准-模型参数表分页数据")
    @PostMapping("/page")
    public IResult<IPage<StandardBasicModelParam>> getStandardBasicModelParamPage(
            @RequestBody StandardBasicModelParamQuery query) {
        return IResult.ok(standardBasicModelParamService.entityPage(query));
    }

    @Operation(summary = "获取标准-模型参数表数据")
    @PostMapping("/get")
    public IResult<StandardBasicModelParam> getStandardBasicModelParamById(@RequestBody @Id ID id) {
        return IResult.ok(standardBasicModelParamService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增标准-模型参数表数据")
    @PostMapping("/add")
    public IResult<Boolean> addStandardBasicModelParam(
            @RequestBody @Validated(Insert.class) StandardBasicModelParam param) {
        return IResult.auto(standardBasicModelParamService.saveEntity(param));
    }

    @Operation(summary = "更新标准-模型参数表数据")
    @PostMapping("/update")
    public IResult<Boolean> updateStandardBasicModelParamById(
            @RequestBody @Validated(Update.class) StandardBasicModelParam param) {
        return IResult.auto(standardBasicModelParamService.updateEntity(param));
    }

    @Operation(summary = "删除标准-模型参数表数据")
    @PostMapping("/delete")
    public IResult<Void> deleteStandardBasicModelParamById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(StrPool.COMMA)) {
            standardBasicModelParamService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取标准-模型参数表VO分页数据")
    @PostMapping("/standardBasicModelParam/voPage")
    public IResult<IPage<StandardBasicModelParamVO>> getStandardBasicModelParamVoPage(
            @RequestBody StandardBasicModelParamQuery query) {
        AssociationQuery<StandardBasicModelParamVO> associationQuery = new AssociationQuery<>(
                StandardBasicModelParamVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取标准-模型参数表VO数据")
    @PostMapping("/standardBasicModelParam/getVo")
    public IResult<StandardBasicModelParamVO> getStandardBasicModelParamVoById(@RequestBody @Id ID id) {
        AssociationQuery<StandardBasicModelParamVO> associationQuery = new AssociationQuery<>(
                StandardBasicModelParamVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    /**
     * 🧮 计算公式并验证结果
     * <p>
     * 用于标准管理页面公式的自动运算与结果校验，支持计算型参数的公式计算
     * </p>
     *
     * @param request 计算公式请求
     * @return 计算结果
     */
    @Operation(summary = "验证公式并计算结果")
    @PostMapping("/calculateFormula")
    public IResult<BigDecimal> calculateFormula(@RequestBody FormulaCalculateRequest request) {
        log.info("计算公式请求: {}", request);
        // 获取公式和参数值
        String formula = request.getFormula();
        Map<String, BigDecimal> paramValues = request.getParamValues();
        // 使用公式计算器计算结果
        BigDecimal result = FormulaCalculator.calculate(formula, paramValues);
        return IResult.ok(result);
    }

    /**
     * 🧮 批量计算标准模型参数的公式
     * <p>
     * 用于标准管理页面批量计算多个计算型参数的值
     * </p>
     *
     * @param standardId 标准ID
     * @return 计算结果列表
     */
    @Operation(summary = "批量计算标准模型参数的公式")
    @PostMapping("/batchCalculate")
    public IResult<List<ModelParamCalculateResult>> batchCalculateFormulas(@RequestParam Long standardId) {
        try {
            log.info("开始批量计算标准ID: {}的公式", standardId);

            // 使用服务层方法批量计算公式
            List<ModelParamCalculateResult> results = modelParamService.batchCalculateFormulasByStandardId(standardId);

            log.info("批量计算完成，共计算{}\u4e2a参数", results.size());
            return IResult.ok(results);
        } catch (Exception e) {
            log.error("批量计算公式出错: {}", e.getMessage());
            return IResult.fail("批量计算公式出错: " + e.getMessage());
        }
    }

}
