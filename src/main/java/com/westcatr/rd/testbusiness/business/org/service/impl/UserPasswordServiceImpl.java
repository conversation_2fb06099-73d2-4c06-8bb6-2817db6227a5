package com.westcatr.rd.testbusiness.business.org.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

import javax.imageio.ImageIO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.security.properties.ISecurityProperties;
import com.westcatr.rd.boot.security.service.PasswordHandler;
import com.westcatr.rd.boot.sso.cache.TokenManageProvider;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.dto.UpdatePasswordDTO;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.OrgUserInfoVO;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.resetPasswordVo;
import com.westcatr.rd.testbusiness.business.org.service.OrgUserInfoService;
import com.westcatr.rd.testbusiness.business.org.service.UserPasswordService;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Service
public class UserPasswordServiceImpl implements UserPasswordService {

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private ISecurityProperties iSecurityProperties;

    @Autowired
    private PasswordHandler passwordHandler;

    @Autowired
    private TokenManageProvider tokenManageProvider;

    @Value("${westcatr.boot.file.upload-folder}")
    private String uploadFolder;

    @Autowired
    private ISecurityProperties securityProperties;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String updatePassword(UpdatePasswordDTO dto, IUser iUser) {
        boolean b = passwordHandler.dbPasswordCheck(passwordHandler.getTransferPassword(dto.getCheckPassword()),
                iUser.getPassword());
        if (!b) {
            throw new IRuntimeException("登录密码错误");
        }
        OrgUserInfo userInfo = orgUserInfoService.getById(dto.getId());
        if (userInfo == null) {
            throw new IRuntimeException("用户不存在");
        }
        String password = iSecurityProperties.getDefaultPassword();
        if (StrUtil.isNotBlank(dto.getPassword())) {
            password = passwordHandler.getTransferPassword(dto.getPassword());
        }
        boolean b1 = orgUserInfoService.updatePassword(userInfo.getId(), password, "");
        if (b1) {
            return password;
        }
        throw new IRuntimeException("修改密码失败");
    }

    @Override
    public boolean selfUpdatePassword(UpdatePasswordDTO dto, String token) {
        IUser user = AuthUtil.getUserE(token);
        boolean b = passwordHandler.dbPasswordCheck(passwordHandler.getTransferPassword(dto.getCheckPassword()),
                user.getPassword());
        if (!b) {
            throw new IRuntimeException("登录密码错误");
        }
        String password = iSecurityProperties.getDefaultPassword();
        if (StrUtil.isNotBlank(dto.getPassword())) {
            password = passwordHandler.getTransferPassword(dto.getPassword());
        }
        boolean b1 = orgUserInfoService.updatePassword(user.getId(), password, token);
        if (b1) {
            // 修改用户信息中的是否需要修改密码标识
            user.setExtend1(null);
            Long expire = tokenManageProvider.getExpire(token);
            tokenManageProvider.setUser(token, user, expire);
            tokenManageProvider.setUserAndPermission(token, user, expire);
        }
        return b1;
    }

    @Override
    public String updateAvatar() throws Exception {
        AssociationQuery<OrgUserInfoVO> associationQuery = new AssociationQuery<>(OrgUserInfoVO.class);
        List<OrgUserInfoVO> list = (associationQuery.voList(associationQuery));
        String path = uploadFolder + "avatar";
        // 判断文件夹是否存在，不存在则创建
        File folder = new File(path);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        Integer count = 0;
        for (OrgUserInfoVO orgUserInfo : list) {
            if (StrUtil.isNotBlank(orgUserInfo.getUserSignInfo())) {
                String filePath = path + "/" + orgUserInfo.getId() + ".jpg";
                if (!FileUtil.exist(filePath)) {
                    // 把用户头像转为图片存储，并手动设置文件名称
                    String result = orgUserInfo.getUserSignInfo()
                            .substring(orgUserInfo.getUserSignInfo().indexOf(",") + 1);
                    BufferedImage bufferedImage = ImgUtil.toImage(result);
                    File output = new File(filePath);
                    // 保存图片
                    ImageIO.write(bufferedImage, "jpg", output);
                    orgUserInfo.setTfPathSign(true);
                    if (orgUserInfoService.updateById(orgUserInfo)) {
                        count++;
                    }
                }
            }
        }
        Console.log("用户头像转换完成");
        return "本次一共转换" + count + "个用户头像";
    }

    @Override
    public String resetPassword(resetPasswordVo vo) {

        // 获取当前登录用户 业务调整
        /*
         * IUser iUser = AuthUtil.getUserE();
         * if (iUser == null) {
         * throw new IRuntimeException("用户未登录");
         * }
         * boolean b =
         * passwordHandler.dbPasswordCheck(passwordHandler.getTransferPassword(vo.
         * getOldPassword()),
         * iUser.getPassword());
         * if (!b) {
         * throw new IRuntimeException("登录密码错误");
         * }
         * OrgUserInfo userInfo = orgUserInfoService.getById(vo.getUserId());
         * if (userInfo == null) {
         * throw new IRuntimeException("用户不存在");
         * }
         */
        boolean b1 = orgUserInfoService.updatePassword(vo.getUserId(), securityProperties.getDefaultPassword(), "");
        if (b1) {
            return "重置密码成功";
        }
        throw new IRuntimeException("重置密码失败");
    }

}
