<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskTestDataInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskTestDataInfo">
        <id column="id" property="id" />
        <result column="sample_name" property="sampleName" />
        <result column="mode" property="mode" />
        <result column="test_project" property="testProject" />
        <result column="parameter" property="parameter" />
        <result column="unit" property="unit" />
        <result column="value" property="value" />
        <result column="back_info" property="backInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sample_name, mode, test_project, parameter, unit, value, back_info
    </sql>

</mapper>
