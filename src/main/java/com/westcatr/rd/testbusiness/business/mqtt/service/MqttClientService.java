package com.westcatr.rd.testbusiness.business.mqtt.service;

import java.util.List;
import java.util.Map;

import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;

/**
 * <p>
 * MQTT客户端服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface MqttClientService {

    /**
     * 初始化MQTT客户端
     */
    void init();

    /**
     * 关闭MQTT客户端
     */
    void close();

    /**
     * 发布消息
     *
     * @param topic   主题
     * @param payload 消息内容
     */
    void publishMessage(String topic, String payload);

    /**
     * 获取设备测试数据
     *
     * @param deviceId     设备ID
     * @param parameterIds 参数ID列表
     * @return 测试数据结果列表
     */
    List<AutoResultDto> getDeviceTestData(Long deviceId, List<Long> parameterIds);

    /**
     * 发送设备请求（同步方法）
     *
     * @param deviceType 设备类型
     * @param params     请求参数
     * @return 响应结果
     */
    Map<String, Object> sendDeviceRequest(String deviceType, Map<String, Object> params);

    /**
     * 发送设备请求（异步方法）
     *
     * @param deviceId   设备ID
     * @param deviceType 设备类型
     * @param params     请求参数
     * @return 请求ID，用于后续获取响应
     */
    String sendDeviceRequestAsync(Long deviceId, String deviceType, Map<String, Object> params);

    /**
     * 获取异步请求的响应结果
     *
     * @param requestId 请求ID
     * @return 响应结果，如果尚未收到响应则返回null
     */
    Map<String, Object> getAsyncResponse(String requestId);

    /**
     * 获取设备最新的响应结果
     *
     * @param deviceId 设备ID
     * @return 最新的响应结果，如果没有则返回null
     */
    Map<String, Object> getLatestDeviceResponse(Long deviceId);

    /**
     * 获取MQTT客户端启用状态
     *
     * @return 是否启用
     */
    boolean isEnabled();

    /**
     * 设置MQTT客户端启用状态
     *
     * @param enabled 是否启用
     */
    void setEnabled(boolean enabled);

    /**
     * 获取MQTT客户端连接状态
     *
     * @return 是否已连接
     */
    boolean isConnected();
}
