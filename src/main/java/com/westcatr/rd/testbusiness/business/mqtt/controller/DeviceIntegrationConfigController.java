package com.westcatr.rd.testbusiness.business.mqtt.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.query.DeviceIntegrationConfigQuery;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.vo.DeviceIntegrationConfigItemVO;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.vo.DeviceIntegrationConfigVO;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigItemService;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.text.StrPool.COMMA;

/**
 * <p>
 * 设备对接规范表 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Validated
@Tag(name = "设备对接规范接口", description = "设备对接规范接口", extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-order", value = "100")})})
@Slf4j
@RestController
public class DeviceIntegrationConfigController {

    @Autowired
    private DeviceIntegrationConfigService deviceIntegrationConfigService;
    @Autowired
    private DeviceIntegrationConfigItemService deviceIntegrationConfigItemService;

    @Operation(summary = "获取设备对接规范分页数据")
    @PostMapping("/deviceIntegrationConfig/page")
    public IResult<IPage<DeviceIntegrationConfig>> getDeviceIntegrationConfigPage(
            @RequestBody DeviceIntegrationConfigQuery query) {
        return IResult.ok(deviceIntegrationConfigService.entityPage(query));
    }

    @Operation(summary = "获取设备对接规范详情")
    @PostMapping("/deviceIntegrationConfig/detail")
    public IResult<DeviceIntegrationConfigVO> getDeviceIntegrationConfigById(@RequestBody @Id ID id) {
        DeviceIntegrationConfigVO vo = new DeviceIntegrationConfigVO();
        DeviceIntegrationConfig deviceIntegrationConfig = deviceIntegrationConfigService.getEntityById(Long.valueOf(id.getId()));
        BeanUtil.copyProperties(deviceIntegrationConfig,vo);
        List<DeviceIntegrationConfigItem> itemList = deviceIntegrationConfigItemService.list(Wrappers.lambdaQuery(DeviceIntegrationConfigItem.class)
                .eq(DeviceIntegrationConfigItem::getConfigId, vo.getId())
        );
        vo.setItemList(itemList);
        return IResult.ok(vo);
    }

    @Operation(summary = "新增设备对接规范")
    @PostMapping("/deviceIntegrationConfig/add")
    public IResult addDeviceIntegrationConfig(@RequestBody @Validated(Insert.class) DeviceIntegrationConfig param) {
        return IResult.auto(deviceIntegrationConfigService.saveEntity(param));
    }

    @Operation(summary = "更新设备对接规范")
    @PostMapping("/deviceIntegrationConfig/update")
    public IResult updateDeviceIntegrationConfigById(
            @RequestBody @Validated(Update.class) DeviceIntegrationConfig param) {
        return IResult.auto(deviceIntegrationConfigService.updateEntity(param));
    }

    @Operation(summary = "删除设备对接规范")
    @PostMapping("/deviceIntegrationConfig/delete")
    public IResult deleteDeviceIntegrationConfigById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            deviceIntegrationConfigService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取设备对接规范VO分页数据")
    @PostMapping("/deviceIntegrationConfig/voPage")
    public IResult<IPage<DeviceIntegrationConfigVO>> getDeviceIntegrationConfigVoPage(
            @RequestBody DeviceIntegrationConfigQuery query) {
        AssociationQuery<DeviceIntegrationConfigVO> associationQuery = new AssociationQuery<>(
                DeviceIntegrationConfigVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }


    /**
     * 获取导入模板
     */
    @Operation(summary = "下载导入模板")
    @PostMapping("/deviceIntegrationConfig/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 1. 设置文件路径（resources目录下的相对路径）
        String filePath = "templates/副本荆州物资数据对接映射关系表.xlsx";

        // 2. 获取资源文件
        ClassPathResource resource = new ClassPathResource(filePath);

        // 3. 检查文件是否存在
        if (resource.getStream() == null) {
            throw new RuntimeException("文件不存在");
        }

        // 4. 获取输入流
        InputStream inputStream = resource.getStream();
        try (inputStream) {
            BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            // 5. 设置响应头
            String filename = URLEncoder.encode("模板文件.xlsx", "UTF-8")
                    .replaceAll("\\+", "%20"); // 处理空格编码问题
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + filename);
            response.setContentLengthLong(resource.getFile().length());

            // 6. 文件拷贝
            byte[] buffer = new byte[1024 * 8];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    @PostMapping("/deviceIntegrationConfig/importExcel")
    @Transactional
    public IResult importExcel(@RequestParam("file") MultipartFile file) throws IOException {
        // 1. 校验文件
        if (file.isEmpty()) {
            return IResult.fail("请选择文件上传");
        }

        // 2. 使用Hutool读取Excel
        ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
        List<String> sheetNames = reader.getSheetNames();

        // 3. 遍历每个Sheet
        for (String sheetName : sheetNames) {
            // 切换到当前Sheet
            reader.setSheet(sheetName);
            System.out.println("\n当前Sheet: " + sheetName);
            DeviceIntegrationConfig deviceIntegrationConfig = new DeviceIntegrationConfig();
            deviceIntegrationConfig.setDeviceType(sheetName);
            deviceIntegrationConfig.setDeviceName(sheetName);
            deviceIntegrationConfig.setDescription(sheetName);
            deviceIntegrationConfigService.saveEntity(deviceIntegrationConfig);

            // 4.读取当前Sheet的所有数据
            List<Map<String, Object>> mapList = reader.readAll();

            List<DeviceIntegrationConfigItem> itemList = new ArrayList<>();
            for (Map<String, Object> row : mapList) {
                System.out.println("行数据: " + row);
                DeviceIntegrationConfigItem item = new DeviceIntegrationConfigItemVO();
                item.setProjectCode(row.get("试验项目映射编码").toString());
                item.setProjectName(row.get("试验项目").toString());
                item.setItemCode(row.get("结果参数映射编码").toString());
                item.setItemName(row.get("结果参数").toString());
                item.setConfigId(deviceIntegrationConfig.getId());
                itemList.add(item);
            }
            deviceIntegrationConfigItemService.saveBatch(itemList);
        }
        // 5. 关闭reader
        reader.close();
        return IResult.ok();
    }
}
