package com.westcatr.rd.testbusiness.business.basics.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工位信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "工位信息管理VO对象")
public class BuInstrumentWorkstationInfoVO extends BuInstrumentWorkstationInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @JoinSelect(joinClass = InstrumentNewInfo.class, relationId = "workstation_id")
    @TableField(exist = false)
    private List<InstrumentNewInfo> instrumentNewInfos;

    @JoinSelect(joinClass = BuInstrumentCameraInfo.class, relationId = "workstation_id")
    @TableField(exist = false)
    private BuInstrumentCameraInfo buInstrumentCameraInfo;

}
