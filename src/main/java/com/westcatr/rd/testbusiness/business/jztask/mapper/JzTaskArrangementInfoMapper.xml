<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskArrangementInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskArrangementInfo">
        <id column="id" property="id" />
        <result column="equipment_id" property="equipmentId" />
        <result column="gw_bz_id" property="gwBzId" />
        <result column="test_user_id" property="testUserId" />
        <result column="workstation_id" property="workstationId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        equipment_id, gw_bz_id, id, test_user_id, workstation_id
    </sql>

</mapper>
