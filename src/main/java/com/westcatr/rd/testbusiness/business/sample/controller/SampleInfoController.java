package com.westcatr.rd.testbusiness.business.sample.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import com.westcatr.rd.testbusiness.business.sample.pojo.dto.AutoSampleNumberDto;
import com.westcatr.rd.testbusiness.business.sample.pojo.query.SampleInfoQuery;
import com.westcatr.rd.testbusiness.business.sample.pojo.vo.SampleInfoVO;
import com.westcatr.rd.testbusiness.business.sample.service.SampleInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * SampleInfo 控制器
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Validated
@Tag(name = "样品——基本信息表接口", description = "样品——基本信息表接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class SampleInfoController {

    @Autowired
    private SampleInfoService sampleInfoService;

    @Operation(summary = "获取样品——基本信息表分页数据")
    @PostMapping("/sampleInfo/page")
    public IResult<IPage<SampleInfo>> getSampleInfoPage(@RequestBody SampleInfoQuery query) {
        return IResult.ok(sampleInfoService.entityPage(query));
    }

    @Operation(summary = "获取样品——基本信息表数据")
    @PostMapping("/sampleInfo/get")
    public IResult<SampleInfo> getSampleInfoById(@RequestBody @Id ID id) {
        return IResult.ok(sampleInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增样品——基本信息表数据")
    @PostMapping("/sampleInfo/add")
    public IResult addSampleInfo(@RequestBody @Validated(Insert.class) SampleInfo param) {
        return IResult.auto(sampleInfoService.saveEntity(param));
    }

    @Operation(summary = "更新样品——基本信息表数据")
    @PostMapping("/sampleInfo/update")
    public IResult updateSampleInfoById(@RequestBody @Validated(Update.class) SampleInfo param) {
        return IResult.auto(sampleInfoService.updateEntity(param));
    }

    @Operation(summary = "删除样品——判断是否可以删除")
    @PostMapping("/sampleInfo/canDelSampleInfo")
    public IResult canDelSampleInfo(@RequestBody @Id ID id) {
        return IResult.ok(sampleInfoService.removeById(id.longId()));
    }

    @Operation(summary = "删除样品——基本信息表数据")
    @PostMapping("/sampleInfo/delete")
    public IResult deleteSampleInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            sampleInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取样品——基本信息表VO分页数据")
    @PostMapping("/sampleInfo/voPage")
    public IResult<IPage<SampleInfoVO>> getSampleInfoVoPage(@RequestBody SampleInfoQuery query) {
        IPage<SampleInfoVO> iPage = sampleInfoService.entityVoPage(query);
        return IResult.ok(iPage);
    }

    @Operation(summary = "获取样品——基本信息表VO数据")
    @PostMapping("/sampleInfo/getVo")
    public IResult<SampleInfoVO> getSampleInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<SampleInfoVO> associationQuery = new AssociationQuery<>(SampleInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "获取样品——批量导入")
    @PostMapping("/sampleInfo/importExcel")
    public IResult importExcel(@RequestParam("file") MultipartFile file) throws IOException {
        List<SampleInfo> sampleInfos = sampleInfoService.importExcel(file);
        return IResult.ok(sampleInfos);
    }

    @Operation(summary = "获取样品——导出模板")
    @GetMapping("/sampleInfo/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        sampleInfoService.exportTemplate(response);
    }

    @Operation(summary = "获取样品——随机生成样品编号和二次盲样编号")
    @GetMapping("/sampleInfo/generateSampleNumber")
    public IResult<AutoSampleNumberDto> generateSampleNumber() {
        return IResult.ok(sampleInfoService.generateSampleNumber());
    }

    @Operation(summary = "返样")
    @PostMapping("/sampleInfo/backSampleInfo")
    public IResult backSampleInfo(@RequestBody @Validated(Update.class) SampleInfo param) {
        return IResult.auto(sampleInfoService.backSampleInfo(param));
    }

//    @Operation(summary = "获取样品——生成任务")
//    @GetMapping("/sampleInfo/generationTask")
//    public IResult generationTask(@RequestParam("id") Long id) {
//        return IResult.auto(sampleInfoService.generationTask(id));
//    }
}
