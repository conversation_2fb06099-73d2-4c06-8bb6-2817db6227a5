<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicInstrumentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo">
        <id column="id" property="id" />
        <result column="integrated_device_id" property="integratedDeviceId" />
        <result column="project_name" property="projectName" />
        <result column="test_capability_level" property="testCapabilityLevel" />
        <result column="instrument_name" property="instrumentName" />
        <result column="key_param_requirement" property="keyParamRequirement" />
        <result column="standard_requirement" property="standardRequirement" />
        <result column="experiment_project_ids" property="experimentProjectIds" />
        <result column="rule_model_id" property="ruleModelId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="sample_specifications" property="sampleSpecifications" />
        <result column="unit" property="unit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, integrated_device_id, project_name, test_capability_level, instrument_name, key_param_requirement, standard_requirement, experiment_project_ids, rule_model_id, create_time, update_time, sample_specifications, unit
    </sql>

</mapper>
