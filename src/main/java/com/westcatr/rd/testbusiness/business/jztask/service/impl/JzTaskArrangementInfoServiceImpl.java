package com.westcatr.rd.testbusiness.business.jztask.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskArrangementInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskArrangementInfo;
import com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskArrangementInfoMapper;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskArrangementInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 荆州—任务列表排期表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Service
public class JzTaskArrangementInfoServiceImpl extends ServiceImpl<JzTaskArrangementInfoMapper, JzTaskArrangementInfo> implements JzTaskArrangementInfoService {

    @Override
    public IPage<JzTaskArrangementInfo> entityPage(JzTaskArrangementInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<JzTaskArrangementInfo>().create(query));
    }

    @Override
    public JzTaskArrangementInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(JzTaskArrangementInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(JzTaskArrangementInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

