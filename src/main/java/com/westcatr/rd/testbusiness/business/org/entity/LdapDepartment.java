package com.westcatr.rd.testbusiness.business.org.entity;

import lombok.Data;

import java.util.List;

@Data
public class LdapDepartment {

    /**
     * 全文目录索引
     */
    private String dn;
    /**
     * 当前目录索引
     */

    private String ou;

    private String parentOu;
    private String description;
    /**
     * 节点类型
     */
    private String[] objectClass;


    /**
     * 钉钉id
     */
    private String dingId;

    /**
     * 钉钉父级ID
     */
    private String dingParentId;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门等级
     */
    private String level;

    /**
     * 路径
     */
    private String path;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private List<LdapDepartment> children;
    public String getDn() {
        return dn;
    }

    public void setDn(String dn) {
        this.dn = dn;
    }

    public String getOu() {
        return ou;
    }

    public void setOu(String ou) {
        this.ou = ou;
    }

    public String[] getObjectClass() {
        return objectClass;
    }

    public void setObjectClass(String[] objectClass) {
        this.objectClass = objectClass;
    }

    public String getDingId() {
        return dingId;
    }

    public void setDingId(String dingId) {
        this.dingId = dingId;
    }

    public String getDingParentId() {
        return dingParentId;
    }

    public void setDingParentId(String dingParentId) {
        this.dingParentId = dingParentId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
