package com.westcatr.rd.testbusiness.business.jztask.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskWorkOrderModelParamQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 标准-模型参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
public interface JzTaskWorkOrderModelParamService extends IService<JzTaskWorkOrderModelParam> {

    IPage<JzTaskWorkOrderModelParam> entityPage(JzTaskWorkOrderModelParamQuery query);

    JzTaskWorkOrderModelParam getEntityById(Long id);

    boolean saveEntity(JzTaskWorkOrderModelParam param);

    boolean updateEntity(JzTaskWorkOrderModelParam param);

    boolean removeEntityById(Long id);
}
