package com.westcatr.rd.testbusiness.business.jzreport.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.JzReportInfoQuery;

/**
 * <p>
 * 国王—报告信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface JzReportInfoService extends IService<JzReportInfo> {

    IPage<JzReportInfo> entityPage(JzReportInfoQuery query);

    JzReportInfo getEntityById(Long id);

    boolean saveEntity(JzReportInfo param);

    boolean updateEntity(JzReportInfo param);

    boolean removeEntityById(Long id);

    /**
     * 自动生成报告
     *
     * @param taskId
     * @return
     */
    boolean autoReport(Long taskId);
}
