package com.westcatr.rd.testbusiness.business.org.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserDeptInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "用户部门信息表VO对象")
public class OrgUserDeptInfoVO extends OrgUserDeptInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
