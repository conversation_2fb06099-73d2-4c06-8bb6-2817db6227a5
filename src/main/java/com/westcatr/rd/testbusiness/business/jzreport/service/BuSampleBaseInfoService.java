package com.westcatr.rd.testbusiness.business.jzreport.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.BuSampleBaseInfoQuery;
import com.westcatr.rd.testbusiness.business.jzreport.entity.BuSampleBaseInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 样品基本信息-关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface BuSampleBaseInfoService extends IService<BuSampleBaseInfo> {

    IPage<BuSampleBaseInfo> entityPage(BuSampleBaseInfoQuery query);

    BuSampleBaseInfo getEntityById(Long id);

    boolean saveEntity(BuSampleBaseInfo param);

    boolean updateEntity(BuSampleBaseInfo param);

    boolean removeEntityById(Long id);
}
