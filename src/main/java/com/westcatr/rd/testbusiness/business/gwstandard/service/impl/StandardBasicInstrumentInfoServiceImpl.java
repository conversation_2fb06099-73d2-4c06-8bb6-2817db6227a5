package com.westcatr.rd.testbusiness.business.gwstandard.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.JoinInfoService;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicInstrumentInfoMapper;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInstrumentInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo.StandardBasicProjectInstrumentVO;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p>
 * 标准—检测仪器标准信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Service
public class StandardBasicInstrumentInfoServiceImpl
        extends ServiceImpl<StandardBasicInstrumentInfoMapper, StandardBasicInstrumentInfo>
        implements StandardBasicInstrumentInfoService {

    @Autowired
    private JoinInfoService joinInfoService;

    @Autowired
    private StandardBasicModelParamServiceImpl standardBasicModelParamService;

    @Override
    public IPage<StandardBasicInstrumentInfo> entityPage(StandardBasicInstrumentInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<StandardBasicInstrumentInfo>().create(query));
    }

    @Override
    public StandardBasicInstrumentInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(StandardBasicInstrumentInfo param) {
        // 校验5列组合的唯一性：样品名称、样品型号、样品规格、检测能力级别、标准要求
        checkUniqueConstraint(param);

        IUser iUser = AuthUtil.getUser();
        param.setCreateUserId(iUser.getId());
        param.setCreateUserFullName(iUser.getFullName());
        param.setUpdateUserId(iUser.getId());
        param.setUpdateUserFullName(iUser.getFullName());
        if (this.save(param)) {
            if (CollUtil.isNotEmpty(param.getProjectIds())) {
                joinInfoService.add(58, param.getId(), param.getProjectIds());
            }
            if (CollUtil.isNotEmpty(param.getStandardBasicModelParams())) {
                List<StandardBasicProjectInstrumentVO> standardBasicProjectInstrumentList = new AssociationQuery<>(
                        StandardBasicProjectInstrumentVO.class)
                        .voList(param.getProjectIds());
                List<StandardBasicProjectParam> projectParams = standardBasicProjectInstrumentList.stream()
                        .map(x -> x.getProjectParams()).flatMap(List::stream).collect(Collectors.toList());
                param.getStandardBasicModelParams().forEach(x -> {
                    x.setStandardBasicInstrumentId(param.getId());
                    if (CollUtil.isNotEmpty(projectParams)) {
                        // 根据名称匹配
                        StandardBasicProjectParam projectParam = projectParams.stream()
                                .filter(y -> y.getParamName().equals(x.getTestItem()))
                                .findFirst().orElse(null);
                        if (ObjectUtil.isNotNull(projectParam)) {
                            /*
                             * if (x.getStandardBasicInstrumentId() != null) {
                             * x.setStandardBasicProjectParamId(projectParam.getId());
                             * }
                             */
                            x.setUnit(projectParam.getUnit());
                            x.setCollectionMethod(projectParam.getCollectionMethod());
                        }
                    }
                });
                standardBasicModelParamService.saveBatch(param.getStandardBasicModelParams());
            }
        }
        return true;
    }

    /**
     * 校验5列组合的唯一性：样品名称、样品型号、样品规格、检测能力级别、标准要求
     *
     * @param param 待保存的实体
     */
    private void checkUniqueConstraint(StandardBasicInstrumentInfo param) {
        // 构建查询条件
        LambdaQueryWrapper<StandardBasicInstrumentInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 只有当所有5个字段都有值时才进行唯一性校验
        if (StrUtil.isNotBlank(param.getSampleName()) &&
                StrUtil.isNotBlank(param.getSampleModel()) &&
                StrUtil.isNotBlank(param.getSampleSpecifications()) &&
                StrUtil.isNotBlank(param.getTestCapabilityLevel()) &&
                StrUtil.isNotBlank(param.getStandardRequirement())) {

            queryWrapper.eq(StandardBasicInstrumentInfo::getSampleName, param.getSampleName())
                    .eq(StandardBasicInstrumentInfo::getSampleModel, param.getSampleModel())
                    .eq(StandardBasicInstrumentInfo::getSampleSpecifications, param.getSampleSpecifications())
                    .eq(StandardBasicInstrumentInfo::getTestCapabilityLevel, param.getTestCapabilityLevel())
                    .eq(StandardBasicInstrumentInfo::getStandardRequirement, param.getStandardRequirement());

            // 如果是更新操作，需要排除自身ID
            if (param.getId() != null) {
                queryWrapper.ne(StandardBasicInstrumentInfo::getId, param.getId());
            }

            // 查询是否存在相同组合的记录
            long count = this.count(queryWrapper);
            if (count > 0) {
                throw new IRuntimeException("已存在相同的记录，样品名称、样品型号、样品规格、检测能力级别和标准要求的组合必须唯一");
            }
        }
    }

    @Override
    public boolean updateEntity(StandardBasicInstrumentInfo param) {
        // 校验5列组合的唯一性：样品名称、样品型号、样品规格、检测能力级别、标准要求
        checkUniqueConstraint(param);

        IUser iUser = AuthUtil.getUser();
        param.setUpdateUserId(iUser.getId());
        param.setUpdateUserFullName(iUser.getFullName());
        if (this.updateById(param)) {
            if (CollUtil.isNotEmpty(param.getProjectIds())) {
                joinInfoService.update(58, param.getId(), param.getProjectIds());
            }
            if (CollUtil.isNotEmpty(param.getStandardBasicModelParams())) {
                List<StandardBasicProjectInstrumentVO> standardBasicProjectInstrumentList = new AssociationQuery<>(
                        StandardBasicProjectInstrumentVO.class)
                        .voList(param.getProjectIds());
                List<StandardBasicProjectParam> projectParams = standardBasicProjectInstrumentList.stream()
                        .map(x -> x.getProjectParams()).flatMap(List::stream).collect(Collectors.toList());
                param.getStandardBasicModelParams().forEach(x -> {
                    x.setStandardBasicInstrumentId(param.getId());
                    if (CollUtil.isNotEmpty(projectParams)) {
                        // 根据名称匹配
                        StandardBasicProjectParam projectParam = projectParams.stream()
                                .filter(y -> y.getParamName().equals(x.getTestItem()))
                                .findFirst().orElse(null);
                        if (ObjectUtil.isNotNull(projectParam)) {
                            /*
                             * if (x.getStandardBasicInstrumentId() != null) {
                             * x.setStandardBasicProjectParamId(projectParam.getId());
                             * }
                             */
                            if (x.getUnit() == null) {
                                x.setUnit(projectParam.getUnit());
                            }
                            x.setCollectionMethod(projectParam.getCollectionMethod());
                        }
                    }
                });
                standardBasicModelParamService.remove(new LambdaQueryWrapper<>(StandardBasicModelParam.class)
                        .eq(StandardBasicModelParam::getStandardBasicInstrumentId, param.getId()));
                standardBasicModelParamService.saveBatch(param.getStandardBasicModelParams());
            }
        }
        return true;
    }

    @Override
    public boolean removeEntityById(Long id) {
        if (this.removeById(id)) {
            standardBasicModelParamService.remove(new LambdaQueryWrapper<>(StandardBasicModelParam.class)
                    .eq(StandardBasicModelParam::getStandardBasicInstrumentId, id));
        }
        return true;
    }
}
