package com.westcatr.rd.testbusiness.business.org.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("org_user_info")
@Schema(description = "人员表")
public class OrgUserInfo extends Model<OrgUserInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空", groups = { Insert.class, Update.class })
    @Length(max = 32, message = "用户名长度不能超过32", groups = { Insert.class, Update.class })
    @TableField("user_name")
    private String userName;

    @Schema(description = "密码")
    @Length(max = 228, message = "密码长度不能超过128", groups = { Insert.class, Update.class })
    @TableField("pass_word")
    private String passWord;

    @Schema(description = "真实姓名")
    @Length(max = 32, message = "真实姓名长度不能超过32", groups = { Insert.class, Update.class })
    @TableField("full_name")
    private String fullName;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "用户电话")
    @Length(max = 11, message = "用户电话长度不能超过11", groups = { Insert.class, Update.class })
    @TableField("phone")
    private String phone;

    @Schema(description = "邮件")
    @Length(max = 255, message = "邮件长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("email")
    private String email;

    // @Schema(description = "是否本地存储头像")
    // @TableField("tf_path_sign")
    // private String tfpathsign;

    @NotNull(message = "不能为空", groups = { Insert.class, Update.class })
    @TableField("enable")
    private Integer enable;

    @TableField("tf_path_sign")
    private Boolean tfPathSign;

    @TableField(exist = false)
    private String photoUrl;

    @TableField(exist = false)
    private Boolean tfTestMain;

    @TableField(exist = false)
    private List<String> deptNames;

    @TableField(exist = false)
    private List<String> roleNames;

    // 所属专业
    @Schema(description = "所属专业")
    @TableField("major")
    private String major;

    private String empId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
