<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.org.mapper.UserTableColumnInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.org.entity.UserTableColumnInfo">
        <id column="id" property="id" />
        <result column="key_name" property="keyName" />
        <result column="columns_info" property="columnsInfo" />
        <result column="user_id" property="userId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, key_name, columns_info, user_id, create_time, update_time
    </sql>

</mapper>
