package com.westcatr.rd.testbusiness.business.mqtt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.query.DeviceIntegrationConfigQuery;

/**
 * <p>
 * 设备对接规范表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
public interface DeviceIntegrationConfigService extends IService<DeviceIntegrationConfig> {

    /**
     * 分页查询设备对接规范
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<DeviceIntegrationConfig> entityPage(DeviceIntegrationConfigQuery query);

    /**
     * 根据ID获取设备对接规范
     * 
     * @param id 主键ID
     * @return 设备对接规范
     */
    DeviceIntegrationConfig getEntityById(Long id);

    /**
     * 保存设备对接规范
     * 
     * @param param 设备对接规范
     * @return 是否成功
     */
    boolean saveEntity(DeviceIntegrationConfig param);

    /**
     * 更新设备对接规范
     * 
     * @param param 设备对接规范
     * @return 是否成功
     */
    boolean updateEntity(DeviceIntegrationConfig param);

    /**
     * 删除设备对接规范
     * 
     * @param id 主键ID
     * @return 是否成功
     */
    boolean removeEntityById(Long id);

    /**
     * 根据设备类型和产品代码获取设备对接规范
     * 
     * @param deviceType 设备类型
     * @param productCode 产品代码
     * @return 设备对接规范
     */
    DeviceIntegrationConfig getByDeviceTypeAndProductCode(String deviceType, String productCode);
}
