package com.westcatr.rd.testbusiness.business.gwstandard.pojo.query;

import com.westcatr.rd.boot.orm.dto.TimeDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 实验项目查询对象
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "实验项目查询对象")
public class ExperimentProjectQuery extends TimeDTO {

    @Schema(description = "实验项目名称")
    private String projectName;

    @Schema(description = "检测仪器ID")
    private Long instrumentId;
}