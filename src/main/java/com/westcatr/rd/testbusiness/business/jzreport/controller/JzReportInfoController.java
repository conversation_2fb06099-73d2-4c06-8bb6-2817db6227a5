package com.westcatr.rd.testbusiness.business.jzreport.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.JzReportInfoQuery;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.vo.JzReportInfoVO;
import com.westcatr.rd.testbusiness.business.jzreport.service.JzReportInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 * JzReportInfo 控制器
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Validated
@Tag(name = "国王—报告信息表接口", description = "国王—报告信息表接口")
@Slf4j
@RestController
public class JzReportInfoController {

    @Autowired
    private JzReportInfoService jzReportInfoService;

    @Operation(summary = "获取国王—报告信息表分页数据")
    @PostMapping("/jzReportInfo/page")
    public IResult<IPage<JzReportInfo>> getJzReportInfoPage(@RequestBody JzReportInfoQuery query) {
        return IResult.ok(jzReportInfoService.entityPage(query));
    }

    @Operation(summary = "获取国王—报告信息表数据")
    @PostMapping("/jzReportInfo/get")
    public IResult<JzReportInfo> getJzReportInfoById(@RequestBody @Id ID id) {
        return IResult.ok(jzReportInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增国王—报告信息表数据")
    @PostMapping("/jzReportInfo/add")
    public IResult addJzReportInfo(@RequestBody @Validated(Insert.class) JzReportInfo param) {
        return IResult.auto(jzReportInfoService.saveEntity(param));
    }

    @Operation(summary = "更新国王—报告信息表数据")
    @PostMapping("/jzReportInfo/update")
    public IResult updateJzReportInfoById(@RequestBody @Validated(Update.class) JzReportInfo param) {
        return IResult.auto(jzReportInfoService.updateEntity(param));
    }

    @Operation(summary = "删除国王—报告信息表数据")
    @PostMapping("/jzReportInfo/delete")
    public IResult deleteJzReportInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            jzReportInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取国王—报告信息表VO分页数据")
    @PostMapping("/jzReportInfo/voPage")
    public IResult<IPage<JzReportInfoVO>> getJzReportInfoVoPage(@RequestBody JzReportInfoQuery query) {
        AssociationQuery<JzReportInfoVO> associationQuery = new AssociationQuery<>(JzReportInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取国王—报告信息表VO数据")
    @PostMapping("/jzReportInfo/getVo")
    public IResult<JzReportInfoVO> getJzReportInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<JzReportInfoVO> associationQuery = new AssociationQuery<>(JzReportInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "自动生成报告")
    @PostMapping("/jzReportInfo/autoReport")
    public IResult autoReport(@RequestBody @Id ID id) {
        return IResult.auto(jzReportInfoService.autoReport(id.longId()));
    }

}
