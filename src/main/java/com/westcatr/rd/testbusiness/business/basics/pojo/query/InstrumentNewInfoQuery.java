package com.westcatr.rd.testbusiness.business.basics.pojo.query;

import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "查询对象")
public class InstrumentNewInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "保管人")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String bgr;

    @Schema(description = "设备出厂编号（序列号）")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String ccbh;

    @Schema(description = "到货日期")
    @QueryCondition
    private Date dhrq;

    @Schema(description = "到货日期，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "dhrq", sort = QueryCondition.Sort.AUTO)
    private Integer dhrqSort;

    @Schema(description = "放置地点")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String fzdd;

    @Schema(description = "供应商")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String gys;

    @Schema(description = "主键")
    @QueryCondition
    private Long id;

    @Schema(description = "计划计量溯源日期")
    @QueryCondition
    private Date jhjlrq;

    @Schema(description = "计划计量溯源日期，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "jhjlrq", sort = QueryCondition.Sort.AUTO)
    private Integer jhjlrqSort;

    @Schema(description = "计量溯源机构")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String jlsyjg;

    @Schema(description = "计量溯源类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String jlsylx;

    @Schema(description = "计量证书编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String jlzsbh;

    @Schema(description = "软件版本")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String rjbb;

    @Schema(description = "设备编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String sbbh;

    @Schema(description = "设备类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String sblb;

    @Schema(description = "设备名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String sbmc;

    @Schema(description = "设备型号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String sbxh;

    @Schema(description = "设备状态")
    @QueryCondition
    private String sbzt;

    @Schema(description = "生产厂")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String scc;

    @Schema(description = "实际计量日期")
    @QueryCondition
    private Date sjjlrq;

    @Schema(description = "到货日期，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "sjjlrq", sort = QueryCondition.Sort.AUTO)
    private Integer sjjlrqSort;

    @Schema(description = "使用部门")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String sybm;

    @Schema(description = "使用人")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String syr;

    @Schema(description = "硬件版本")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String yjbb;

    @Schema(description = "业务状态")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String ywzt;

    @Schema(description = "资产编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String zcbh;

    @Schema(description = "资产类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String zclx;

    @Schema(description = "类型")
    @QueryCondition(condition = QueryCondition.Condition.EQ)
    private String typeInfo;

    @JoinExpression(value = "left join bu_instrument_dept_sys_info idsi on idsi.instrumentation_id = bu_instrument_new_info.id")
    @Schema(description = "所属系统")
    @QueryCondition(condition = QueryCondition.Condition.LIKE, field = "idsi.belong_system")
    private String sysName;

    @JoinExpression(value = "left join bu_instrument_dept_sys_info idsi on idsi.instrumentation_id = bu_instrument_new_info.id")
    @Schema(description = "部门Id")
    @QueryCondition(condition = QueryCondition.Condition.EQ, field = "idsi.dept_id")
    private Long deptId;

    @Schema(description = "领域名称")
    @JoinExpression(value = "left join bu_instrument_dept_sys_info idsitwo on idsitwo.instrumentation_id = bu_instrument_new_info.id")
    @QueryCondition(condition = QueryCondition.Condition.LIKE, field = "idsitwo.field_child_name")
    private String fieldChildName;

    @Schema(description = "专业类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String professionalType;

    @Schema(description = "数据采集方式（直采/填报）")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE)
    private String collectionMethod;

}
