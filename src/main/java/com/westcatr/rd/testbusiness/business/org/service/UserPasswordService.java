package com.westcatr.rd.testbusiness.business.org.service;

import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.testbusiness.business.org.pojo.dto.UpdatePasswordDTO;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.resetPasswordVo;

/**
 * <AUTHOR>
 */
public interface UserPasswordService {

    /**
     * 管理员修改用户密码
     *
     * @param dto   dto
     * @param iUser 我用户
     * @return {@link String } 修改后的密码
     * @since 2.1.0
     */
    String updatePassword(UpdatePasswordDTO dto, IUser iUser);

    /**
     * 用户修改自己的密码
     *
     * @param dto 修改密码DTO
     * @return
     */
    boolean selfUpdatePassword(UpdatePasswordDTO dto, String token);

    String updateAvatar() throws Exception;

    String resetPassword(resetPasswordVo resetPasswordVo);
}
