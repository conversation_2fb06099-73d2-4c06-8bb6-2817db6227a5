<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.basics.mapper.BuInstrumentWorkstationInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="description" property="description" />
        <result column="location" property="location" />
        <result column="responsible_person" property="responsiblePerson" />
        <result column="status" property="status" />
        <result column="update_time" property="updateTime" />
        <result column="workstation_code" property="workstationCode" />
        <result column="workstation_name" property="workstationName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, description, id, location, responsible_person, status, update_time, workstation_code, workstation_name
    </sql>

</mapper>
