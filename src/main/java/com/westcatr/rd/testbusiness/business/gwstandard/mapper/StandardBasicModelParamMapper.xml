<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicModelParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam">
        <id column="id" property="id" />
        <result column="test_item" property="testItem" />
        <result column="param_key" property="paramKey" />
        <result column="unit" property="unit" />
        <result column="judge_type" property="judgeType" />
        <result column="qualified_standard" property="qualifiedStandard" />
        <result column="judge_formula" property="judgeFormula" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="standard_basic_instrument_id" property="standardBasicInstrumentId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, test_item, param_key, unit, judge_type, qualified_standard, judge_formula, create_time, update_time, standard_basic_instrument_id
    </sql>

</mapper>
