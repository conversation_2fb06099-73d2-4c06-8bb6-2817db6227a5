package com.westcatr.rd.testbusiness.business.devicedata.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * 设备对接数据管理配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_device_data_config")
@Schema(description = "设备对接数据管理配置表")
public class DeviceDataConfig extends Model<DeviceDataConfig> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "设备代码")
    @Length(max = 64, message = "设备代码长度不能超过64")
    @TableField("device_code")
    private String deviceCode;

    @Schema(description = "设备名称")
    @Length(max = 255, message = "设备名称长度不能超过255")
    @TableField("device_name")
    private String deviceName;

    @Schema(description = "设备返回体JSON配置")
    @TableField("response_body")
    private String responseBody;

    @Schema(description = "状态(enabled/disabled)")
    @Length(max = 32, message = "状态长度不能超过32")
    @TableField("status")
    private String status;

    @Schema(description = "描述")
    @Length(max = 512, message = "描述长度不能超过512")
    @TableField("description")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public String toString() {
        return "DeviceDataConfig{" +
                "id=" + id +
                ", deviceCode='" + deviceCode + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", responseBody='" + responseBody + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
