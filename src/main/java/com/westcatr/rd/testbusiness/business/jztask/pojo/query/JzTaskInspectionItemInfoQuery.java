package com.westcatr.rd.testbusiness.business.jztask.pojo.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—检项列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="荆州—检项列表表查询对象")
public class JzTaskInspectionItemInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Long id;

    @Schema(description = "检项编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String inspectionItemNumber;

    @Schema(description = "关联样品Id")
    @QueryCondition
    private Long sampleId;

    @Schema(description = "关联任务id")
    @QueryCondition
    private Long taskId;

    @Schema(description = "检测结果")
    @QueryCondition
    private String testResults;

    @Schema(description = "检测标准")
    @QueryCondition
    private String testStandard;

    @Schema(description = "是否合格")
    @QueryCondition
    private String tfQualified;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "检测人员")
    @QueryCondition
    private Long userId;

    @Schema(description = "关联工单id")
    @QueryCondition
    private Long workOrderId;

    @Schema(description = "实验项目")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String experimentalProject;

    @Schema(description = "小于检毕结束时间")
    @QueryCondition(condition = QueryCondition.Condition.LE,field = "bsi.end_date")
    @JoinExpression(value = "inner join jz_task_work_order_info bsi on bsi.id=@m.work_order_id")
    private Date inspectionCompletionTimeEnd;

    @Schema(description = "大于检毕开始时间")
    @QueryCondition(condition = QueryCondition.Condition.GE,field = "bsi.end_date")
    @JoinExpression(value = "inner join jz_task_work_order_info bsi on bsi.id=@m.work_order_id")
    private Date inspectionCompletionTimeStart;

}
