package com.westcatr.rd.testbusiness.business.gwstandard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicProjectInstrumentQuery;

/**
 * <p>
 * 标准—实验项目检测仪器信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface StandardBasicProjectInstrumentService extends IService<StandardBasicProjectInstrument> {

    IPage<StandardBasicProjectInstrument> entityPage(StandardBasicProjectInstrumentQuery query);

    StandardBasicProjectInstrument getEntityById(Long id);

    boolean saveEntity(StandardBasicProjectInstrument param);

    boolean updateEntity(StandardBasicProjectInstrument param);

    boolean removeEntityById(Long id);
}
