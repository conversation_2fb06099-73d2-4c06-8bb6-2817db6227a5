<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.basics.mapper.BuInstrumentCameraInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo">
        <id column="id" property="id" />
        <result column="camera_type" property="cameraType" />
        <result column="channel_num" property="channelNum" />
        <result column="create_time" property="createTime" />
        <result column="device_code" property="deviceCode" />
        <result column="device_model" property="deviceModel" />
        <result column="device_name" property="deviceName" />
        <result column="device_status" property="deviceStatus" />
        <result column="installation_location" property="installationLocation" />
        <result column="ip_address" property="ipAddress" />
        <result column="is_online" property="isOnline" />
        <result column="password" property="password" />
        <result column="port" property="port" />
        <result column="rtsp_url" property="rtspUrl" />
        <result column="update_time" property="updateTime" />
        <result column="username" property="username" />
        <result column="workstation_id" property="workstationId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        camera_type, channel_num, create_time, device_code, device_model, device_name, device_status, id, installation_location, ip_address, is_online, password, port, rtsp_url, update_time, username, workstation_id
    </sql>

</mapper>
