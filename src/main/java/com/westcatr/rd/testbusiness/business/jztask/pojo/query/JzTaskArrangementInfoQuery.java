package com.westcatr.rd.testbusiness.business.jztask.pojo.query;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 荆州—任务列表排期表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "荆州—任务列表排期表查询对象")
public class JzTaskArrangementInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "设备id")
    @QueryCondition
    private Long equipmentId;

    @Schema(description = "实验标准id")
    @QueryCondition
    private Long gwBzId;

    @QueryCondition
    private Long id;

    @Schema(description = "测试人员id")
    @QueryCondition
    private Long testUserId;

    @Schema(description = "工位id")
    @QueryCondition
    private Long workstationId;

    @Schema(description = "任务id")
    @QueryCondition
    private Long taskId;
}
