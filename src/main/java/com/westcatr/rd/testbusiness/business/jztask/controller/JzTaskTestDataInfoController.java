package com.westcatr.rd.testbusiness.business.jztask.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskTestDataInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskTestDataInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskTestDataInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskTestDataInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * JzTaskTestDataInfo 控制器
 * 
 * <AUTHOR>
 * @since 2025-03-27
 */
@Validated
@Tag(name = "检测任务测试数据表接口", description = "检测任务测试数据表接口")
@Slf4j
@RestController
public class JzTaskTestDataInfoController {

    @Autowired
    private JzTaskTestDataInfoService jzTaskTestDataInfoService;

    @Operation(summary = "获取检测任务测试数据表分页数据")
    @PostMapping("/jzTaskTestDataInfo/page")
    public IResult<IPage<JzTaskTestDataInfo>> getJzTaskTestDataInfoPage(@RequestBody JzTaskTestDataInfoQuery query) {
        return IResult.ok(jzTaskTestDataInfoService.entityPage(query));
    }

    @Operation(summary = "获取检测任务测试数据表数据")
    @PostMapping("/jzTaskTestDataInfo/get")
    public IResult<JzTaskTestDataInfo> getJzTaskTestDataInfoById(@RequestBody @Id ID id) {
        return IResult.ok(jzTaskTestDataInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增检测任务测试数据表数据")
    @PostMapping("/jzTaskTestDataInfo/add")
    public IResult addJzTaskTestDataInfo(@RequestBody @Validated(Insert.class) JzTaskTestDataInfo param) {
        return IResult.auto(jzTaskTestDataInfoService.saveEntity(param));
    }

    @Operation(summary = "更新检测任务测试数据表数据")
    @PostMapping("/jzTaskTestDataInfo/update")
    public IResult updateJzTaskTestDataInfoById(@RequestBody @Validated(Update.class) JzTaskTestDataInfo param) {
        return IResult.auto(jzTaskTestDataInfoService.updateEntity(param));
    }

    @Operation(summary = "删除检测任务测试数据表数据")
    @PostMapping("/jzTaskTestDataInfo/delete")
    public IResult deleteJzTaskTestDataInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            jzTaskTestDataInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取检测任务测试数据表VO分页数据")
    @PostMapping("/jzTaskTestDataInfo/voPage")
    public IResult<IPage<JzTaskTestDataInfoVO>> getJzTaskTestDataInfoVoPage(
            @RequestBody JzTaskTestDataInfoQuery query) {
        AssociationQuery<JzTaskTestDataInfoVO> associationQuery = new AssociationQuery<>(JzTaskTestDataInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取检测任务测试数据表VO数据")
    @PostMapping("/jzTaskTestDataInfo/getVo")
    public IResult<JzTaskTestDataInfoVO> getJzTaskTestDataInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<JzTaskTestDataInfoVO> associationQuery = new AssociationQuery<>(JzTaskTestDataInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

}
