package com.westcatr.rd.testbusiness.business.basics.pojo.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 工位-设备树形结构DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@Accessors(chain = true)
@Schema(description = "工位-设备树形结构DTO")
public class WorkstationDeviceTreeDTO {

    @Schema(description = "节点ID")
    private Long id;

    @Schema(description = "节点名称")
    private String label;

    @Schema(description = "节点类型（workstation-工位，device-设备）")
    private String type;

    @Schema(description = "子节点列表")
    private List<WorkstationDeviceTreeDTO> children;
}
