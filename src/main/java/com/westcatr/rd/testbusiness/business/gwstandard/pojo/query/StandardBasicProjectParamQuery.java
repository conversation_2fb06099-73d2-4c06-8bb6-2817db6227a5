package com.westcatr.rd.testbusiness.business.gwstandard.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实验项目参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="实验项目参数表查询对象")
public class StandardBasicProjectParamQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "参数名称")
    @QueryCondition
    private String paramName;

    @Schema(description = "参数类别")
    @QueryCondition
    private String paramType;

    @Schema(description = "实验项目ID")
    @QueryCondition
    private Long standardBasicProjectId;

    // 实验项目IDs
    @QueryCondition(condition = QueryCondition.Condition.IN, field = "standard_basic_project_id")
    private List<Long> standardBasicProjectIds;

    @Schema(description = "修改时间")
    @QueryCondition
    private Date updateTime;
}
