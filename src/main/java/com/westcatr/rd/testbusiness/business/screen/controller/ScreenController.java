package com.westcatr.rd.testbusiness.business.screen.controller;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.file.service.FileInfoService;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentCameraInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.BuInstrumentCameraInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentCameraInfoService;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentWorkstationInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;
import com.westcatr.rd.testbusiness.business.jzreport.entity.JzReportInfo;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.query.JzReportInfoQuery;
import com.westcatr.rd.testbusiness.business.jzreport.pojo.vo.JzReportInfoVO;
import com.westcatr.rd.testbusiness.business.jzreport.service.JzReportInfoService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskWorkOrderInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskWorkOrderInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskInfoService;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;
import com.westcatr.rd.testbusiness.business.sample.pojo.query.SampleInfoQuery;
import com.westcatr.rd.testbusiness.business.sample.pojo.vo.SampleInfoVO;
import com.westcatr.rd.testbusiness.business.sample.service.SampleInfoService;
import com.westcatr.rd.testbusiness.business.screen.entity.MidCenter;
import com.westcatr.rd.testbusiness.business.screen.entity.MidLeft;
import com.westcatr.rd.testbusiness.business.screen.entity.Monitor;
import com.westcatr.rd.testbusiness.business.screen.entity.MonitorData;
import com.westcatr.rd.testbusiness.business.screen.entity.NewScreenData;
import com.westcatr.rd.testbusiness.business.screen.entity.ReportInfo;
import com.westcatr.rd.testbusiness.business.screen.entity.ScreenData;
import com.westcatr.rd.testbusiness.business.screen.entity.TaskDto;
import com.westcatr.rd.testbusiness.business.screen.entity.TaskSummary;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.Columns;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.DataSource;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.ItemStyle;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.ReportData;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.ReportDataSource;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.Sub;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.SubjectArr;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.TaskCountData;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.TaskDataSource;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.TaskListObj;
import com.westcatr.rd.testbusiness.business.screen.pojo.dto.TaskStatusData;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * JzReportInfo 控制器
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Validated
@Tag(name = "大屏接口", description = "大屏接口")
@Slf4j
@RestController
@RequestMapping("/screen")
public class ScreenController {

    @Autowired
    private JzReportInfoService jzReportInfoService;
    @Autowired
    private SampleInfoService sampleInfoService;
    @Autowired
    private FileInfoService fileInfoService;
    @Autowired
    private JzTaskInfoService jzTaskInfoService;
    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;
    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;
    @Value("${rstptoflv.host}")
    private String rstpToFltHost;
    @Autowired
    private BuInstrumentWorkstationInfoService buInstrumentWorkstationInfoService;
    @Autowired
    private BuInstrumentCameraInfoService buInstrumentCameraInfoService;

    @Operation(summary = "大屏—报告汇总")
    @PostMapping("/report")
    public IResult<ScreenData> getJzReportInfoPage() {
        ScreenData screenData = new ScreenData();
        screenData.setTitle("大屏幕");
        List<MonitorData> monitorData = new ArrayList<>();
        List<JzTaskWorkOrderInfo> jzTaskWorkOrderInfos = jzTaskWorkOrderInfoService
                .list(new LambdaQueryWrapper<>(JzTaskWorkOrderInfo.class).eq(JzTaskWorkOrderInfo::getStatusInfo, "在检"));
        if (CollUtil.isNotEmpty(jzTaskWorkOrderInfos)) {
            List<Long> workIds = jzTaskWorkOrderInfos.stream().map(JzTaskWorkOrderInfo::getWorkstationId)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(workIds)) {
                List<BuInstrumentCameraInfoVO> buInstrumentCameraInfos = new AssociationQuery<>(
                        BuInstrumentCameraInfoVO.class)
                        .voList(new BuInstrumentCameraInfoQuery().setWorkstationIds(workIds));
                buInstrumentCameraInfos.forEach(buInstrumentCameraInfo -> {
                    MonitorData monitorData1 = new MonitorData();
                    monitorData1.setId(buInstrumentCameraInfo.getId());
                    monitorData1.setCameraCode(buInstrumentCameraInfo.getDeviceCode());
                    monitorData1.setCameraName(buInstrumentCameraInfo.getDeviceName());
                    if (buInstrumentCameraInfo.getBuInstrumentWorkstationInfo() != null) {
                        monitorData1.setWorkstation(
                                buInstrumentCameraInfo.getBuInstrumentWorkstationInfo().getWorkstationName());
                    }
                    String rtspUrl = String.format("rtsp://%s:%s@%s:%d/Streaming/Channels/101",
                            buInstrumentCameraInfo.getUsername(), buInstrumentCameraInfo.getPassword(),
                            buInstrumentCameraInfo.getIpAddress(), buInstrumentCameraInfo.getPort());
                    rtspUrl = Base62.encode(rtspUrl.getBytes());
                    String flvUrl = String.format("%s%s.flv", rstpToFltHost, rtspUrl);
                    monitorData1.setFlvUrl(flvUrl);
                    monitorData1.setTfTest(true);
                    monitorData.add(monitorData1);
                });
            }
        }
        // 小于4条去摄像头表查询
        if (CollUtil.isEmpty(monitorData) || monitorData.size() < 4) {
            int neededCount = 4 - monitorData.size();

            // 获取所有摄像头数据
            List<BuInstrumentCameraInfoVO> allCameras = new AssociationQuery<>(
                    BuInstrumentCameraInfoVO.class)
                    .voList(new BuInstrumentCameraInfoQuery());

            // 过滤掉已经存在的摄像头
            List<Long> existingIds = monitorData.stream()
                    .map(MonitorData::getId)
                    .collect(Collectors.toList());

            // 从所有摄像头中筛选不重复的，并限制数量
            allCameras.stream()
                    .filter(camera -> !existingIds.contains(camera.getId()))
                    .limit(neededCount)
                    .forEach(buInstrumentCameraInfo -> {
                        MonitorData monitorData1 = new MonitorData();
                        monitorData1.setId(buInstrumentCameraInfo.getId());
                        monitorData1.setCameraCode(buInstrumentCameraInfo.getDeviceCode());
                        monitorData1.setCameraName(buInstrumentCameraInfo.getDeviceName());
                        if (buInstrumentCameraInfo.getBuInstrumentWorkstationInfo() != null) {
                            monitorData1.setWorkstation(
                                    buInstrumentCameraInfo.getBuInstrumentWorkstationInfo().getWorkstationName());
                        }
                        String rtspUrl = String.format("rtsp://%s:%s@%s:%d/Streaming/Channels/101",
                                buInstrumentCameraInfo.getUsername(), buInstrumentCameraInfo.getPassword(),
                                buInstrumentCameraInfo.getIpAddress(), buInstrumentCameraInfo.getPort());
                        rtspUrl = Base62.encode(rtspUrl.getBytes());
                        String flvUrl = String.format("%s%s.flv", rstpToFltHost, rtspUrl);
                        monitorData1.setFlvUrl(flvUrl);
                        monitorData.add(monitorData1);
                    });
        }
        Monitor monitor = new Monitor();
        monitor.setTitle("实时监控");
        monitor.setMonitorData(monitorData);
        screenData.setMonitor(monitor);

        /*------------------------------------------报告汇总-------------------------------------------------*/
        ReportInfo reportInfo = new ReportInfo();
        reportInfo.setTitle("报告汇总");

        List<ReportData> reportData = new ArrayList();
        // 查询当天数据 creatTime
        LocalDate today1 = LocalDate.now();
        LocalDateTime startOfDay = today1.atStartOfDay(); // 当天 00:00:00
        LocalDateTime endOfDay = today1.atTime(LocalTime.MAX); // 当天 23:59:59

        List<JzReportInfo> jzReportInfos = jzReportInfoService.list(new LambdaQueryWrapper<JzReportInfo>()
                .between(JzReportInfo::getCreateTime, startOfDay, endOfDay));
        if (CollUtil.isNotEmpty(jzReportInfos)) {
            // 筛选合格的Î
            List<JzReportInfo> jzReportInfos1 = jzReportInfos.stream().filter(x -> "合格".equals(x.getTfQualified()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(jzReportInfos1)) {
                ItemStyle itemStyle = new ItemStyle();
                itemStyle.setColor("#2BA471");

                ReportData reportData1 = new ReportData();
                reportData1.setName("合格");
                reportData1.setValue(jzReportInfos1.size());
                reportData1.setItemStyle(itemStyle);

                reportData.add(reportData1);
            }

            // 筛选不合格的
            List<JzReportInfo> jzReportInfos2 = jzReportInfos.stream().filter(x -> "不合格".equals(x.getTfQualified()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(jzReportInfos2)) {
                ItemStyle itemStyle = new ItemStyle();
                itemStyle.setColor("#FF4D4F");

                ReportData reportData2 = new ReportData();
                reportData2.setName("不合格");
                reportData2.setValue(jzReportInfos2.size());
                reportData2.setItemStyle(itemStyle);

                reportData.add(reportData2);

            }

            reportInfo.setReportData(reportData);
        }

        List<SubjectArr> subjectArrs = new ArrayList();
        List<SampleInfo> sampleInfos = sampleInfoService.list();

        // 生成4个相关材料数据
        for (int i = 0; i < 4; i++) {
            SubjectArr subjectArr = new SubjectArr();
            subjectArr.setMax(5);
            subjectArr.setValue(0);

            if (i == 0) {
                subjectArr.setName("材料");
                if (CollUtil.isNotEmpty(sampleInfos)) {
                    List<SampleInfo> sampleInfos1 = sampleInfos.stream().filter(x -> "材料".equals(x.getSampleMajor()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(sampleInfos1)) {
                        subjectArr.setValue(sampleInfos1.size());
                    }
                }
                subjectArrs.add(subjectArr);
            } else if (i == 1) {
                subjectArr.setName("变压器");
                if (CollUtil.isNotEmpty(sampleInfos)) {
                    List<SampleInfo> sampleInfos2 = sampleInfos.stream().filter(x -> "变压器".equals(x.getSampleMajor()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(sampleInfos2)) {
                        subjectArr.setValue(sampleInfos2.size());
                    }
                }
                subjectArrs.add(subjectArr);
            } else if (i == 2) {
                subjectArr.setName("线圈/线缆");
                if (CollUtil.isNotEmpty(sampleInfos)) {
                    List<SampleInfo> sampleInfos3 = sampleInfos.stream().filter(x -> "线圈/线缆".equals(x.getSampleMajor()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(sampleInfos3)) {
                        subjectArr.setValue(sampleInfos3.size());
                    }
                }
                subjectArrs.add(subjectArr);
            } else if (i == 3) {
                subjectArr.setName("开关");
                if (CollUtil.isNotEmpty(sampleInfos)) {
                    List<SampleInfo> sampleInfos4 = sampleInfos.stream().filter(x -> "开关".equals(x.getSampleMajor()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(sampleInfos4)) {
                        subjectArr.setValue(sampleInfos4.size());
                    }
                }
                subjectArrs.add(subjectArr);
            }
        }
        reportInfo.setSubjectArr(subjectArrs);

        List<ReportDataSource> reportDataSource = new ArrayList();
        if (CollUtil.isNotEmpty(jzReportInfos)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");

            for (int i = 0; i < jzReportInfos.size(); i++) {
                ReportDataSource dataSource = new ReportDataSource();

                // 获取索引
                dataSource.setIndex(String.valueOf(i + 1));
                JzReportInfo jzReportInfo = jzReportInfos.get(i);
                // 获取检验报告名
                Long reportFileId = jzReportInfo.getReportFileId();
                if (Objects.nonNull(reportFileId)) {
                    FileInfo file = fileInfoService.getById(reportFileId);
                    if (Objects.nonNull(file)) {
                        dataSource.setName(file.getFileName());
                    }
                }

                // 获取对应任务编号
                Long taskId = jzReportInfo.getTaskId();
                if (Objects.nonNull(taskId)) {
                    JzTaskInfo jzTaskInfo = jzTaskInfoService.getById(taskId);
                    if (Objects.nonNull(jzTaskInfo)) {
                        dataSource.setTaskName(jzTaskInfo.getTaskNumber());
                    }
                }

                // 获取生成时间
                Date createTime = jzReportInfo.getCreateTime();
                String format = sdf.format(createTime);
                dataSource.setReportTime(format);

                reportDataSource.add(dataSource);
            }

        }
        reportInfo.setReportDataSource(reportDataSource);

        /*------------------------------------------任务汇总-------------------------------------------------*/
        // 获取今天的日期
        LocalDate today = LocalDate.now();

        TaskSummary taskSummary = new TaskSummary();
        taskSummary.setTitle("任务汇总");

        List<TaskStatusData> taskStatusData = new ArrayList();

        LocalDateTime startOfDayNew = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDayNew = LocalDate.now().atTime(LocalTime.MAX);

        List<JzTaskInfo> jzTaskInfos = jzTaskInfoService.list(new LambdaQueryWrapper<JzTaskInfo>()
                .between(JzTaskInfo::getCreateTime, startOfDayNew, endOfDayNew));

        for (int i = 0; i < 3; i++) {
            TaskStatusData taskStatusData1 = new TaskStatusData();
            ItemStyle itemStyle = new ItemStyle();
            taskStatusData1.setValue(0);
            if (i == 0) {
                itemStyle.setColor("#FADB14");
                taskStatusData1.setName("待检");
                taskStatusData1.setItemStyle(itemStyle);

                if (CollUtil.isNotEmpty(jzTaskInfos)) {
                    List<JzTaskInfo> jzTaskInfos1 = jzTaskInfos.stream().filter(x -> x.getTaskStatus().contains("待检"))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(jzTaskInfos1)) {
                        taskStatusData1.setValue(jzTaskInfos1.size());
                    }
                }

                taskStatusData.add(taskStatusData1);

            } else if (i == 1) {
                itemStyle.setColor("#2BA471");
                taskStatusData1.setName("在检");
                taskStatusData1.setItemStyle(itemStyle);

                if (CollUtil.isNotEmpty(jzTaskInfos)) {
                    List<JzTaskInfo> jzTaskInfos2 = jzTaskInfos.stream().filter(x -> x.getTaskStatus().contains("在检"))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(jzTaskInfos2)) {
                        taskStatusData1.setValue(jzTaskInfos2.size());
                    }
                }

                taskStatusData.add(taskStatusData1);

            } else if (i == 2) {
                itemStyle.setColor("#999999");
                taskStatusData1.setName("当日检毕");
                taskStatusData1.setItemStyle(itemStyle);

                if (CollUtil.isNotEmpty(jzTaskInfos)) {

                    LocalDate today2 = LocalDate.now();
                    LocalDateTime startOfDay1 = today2.atStartOfDay(); // 今天 00:00:00
                    LocalDateTime endOfDay1 = today2.atTime(LocalTime.MAX); // 今天 23:59:59

                    List<JzTaskInfo> jzTaskInfos3 = jzTaskInfos.stream()
                            .filter(x -> x.getTaskStatus() != null && x.getTaskStatus().contains("任务检毕")) // 任务状态筛选
                            .filter(data -> data.getEndDate() != null) // 过滤 createTime 为空的情况
                            .filter(data -> {
                                LocalDateTime createTime = data.getEndDate().toInstant()
                                        .atZone(ZoneId.systemDefault())
                                        .toLocalDateTime();
                                return !createTime.isBefore(startOfDay1) && !createTime.isAfter(endOfDay1);
                            })
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(jzTaskInfos3)) {
                        taskStatusData1.setValue(jzTaskInfos3.size());
                    }
                }

                taskStatusData.add(taskStatusData1);

            }
        }

        taskSummary.setTaskStatusData(taskStatusData);

        List<TaskCountData> taskCountData = new ArrayList();
        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 获取当日以及前 4 天的日期
        List<JzTaskInfo> jzTaskInfoList = jzTaskInfoService.list();
        for (int i = 0; i < 5; i++) {
            TaskCountData taskCountData1 = new TaskCountData();
            LocalDate date = today.minusDays(i); // 获取前 i 天的日期
            String formattedDate = date.format(formatter); // 格式化为 MM-dd

            taskCountData1.setTime(formattedDate);
            taskCountData1.setCount(0);

            List<JzTaskInfo> jzTaskInfos1 = jzTaskInfoList.stream()
                    .filter(x -> "任务检毕".equals(x.getTaskStatus()))
                    .filter(x -> sdf.format(x.getCreateTime()).contains(formattedDate)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(jzTaskInfos1)) {
                taskCountData1.setCount(jzTaskInfos1.size());
            }

            taskCountData.add(taskCountData1);
        }

        taskSummary.setTaskCountData(taskCountData);

        List<TaskDataSource> taskDataSource = new ArrayList();

        int indexa = 0;
        if (Objects.nonNull(jzTaskInfoList)) {
            for (int i = 0; i < jzTaskInfoList.size(); i++) {
                if (!"任务待检".equals(jzTaskInfoList.get(i).getTaskStatus())) {
                    continue;
                }
                TaskDataSource taskDataSource1 = new TaskDataSource();
                // taskDataSource1.setIndex(String.valueOf(indexa + 1));
                JzTaskInfo jzTaskInfo = jzTaskInfoList.get(i);

                Long sampleId = jzTaskInfo.getSampleId();
                if (Objects.nonNull(sampleId)) {
                    SampleInfo sampleInfo = sampleInfoService.getById(sampleId);
                    if (Objects.nonNull(sampleId)) {
                        taskDataSource1.setCheckName(sampleInfo.getSampleName());
                        taskDataSource1.setCheckType(sampleInfo.getTestType());
                        taskDataSource1.setCheckCode(sampleInfo.getBlindSampleNumber());
                        taskDataSource1.setSecondaryCheckCode(sampleInfo.getSecondaryBlindSampleNumber());
                    }
                }

                taskDataSource1.setCheckTime(sdf.format(jzTaskInfo.getCreateTime()));
                taskDataSource1.setCreateTime(sdf1.format(jzTaskInfo.getCreateTime()));

                taskDataSource.add(taskDataSource1);
                indexa++;
            }
            if (CollUtil.isNotEmpty(taskDataSource)) {
                taskDataSource = taskDataSource.stream()
                        .sorted(Comparator.comparing(TaskDataSource::getCreateTime).reversed())
                        .collect(Collectors.toList());

                int i = 1;
                for (TaskDataSource dataSource : taskDataSource) {
                    dataSource.setIndex(i);
                    i++;
                }
            }
        }

        taskSummary.setTaskDataSource(taskDataSource);

        /*-------------------------------------检测任务实时信息----------------------------------*/
        List<String> taskStatus = jzTaskInfoList.stream().map(x -> x.getTaskStatus()).collect(Collectors.toList());
        boolean allTaskFinished = taskStatus.stream()
                .allMatch(status -> "任务检毕".equals(status));
        TaskListObj taskListObj = new TaskListObj();

        if (!allTaskFinished) {
            jzTaskInfoList = jzTaskInfoList.stream().filter(x -> "任务在检".equals(x.getTaskStatus()))
                    .collect(Collectors.toList());

            List<Columns> columns = new ArrayList();
            List<DataSource> dataSource = new ArrayList();
            List<Map<String, Object>> newdataSourceMap = new ArrayList();

            int maxSize = 0;
            for (int i = 0; i < jzTaskInfoList.size(); i++) {
                List<JzTaskWorkOrderInfo> orderInfos = jzTaskWorkOrderInfoService
                        .list(new LambdaQueryWrapper<JzTaskWorkOrderInfo>()
                                .eq(JzTaskWorkOrderInfo::getTaskId, jzTaskInfoList.get(i).getId()));
                // 找最大的那个
                if (CollUtil.isNotEmpty(orderInfos)) {
                    maxSize = Math.max(maxSize, orderInfos.size());
                }
            }

            for (int i = 0; i < maxSize; i++) {
                Columns columns1 = new Columns();
                String title = Convert.toStr(i + 1);
                columns1.setTitle(title);
                columns1.setDataIndex("sub" + (i + 1));
                columns.add(columns1);

            }

            taskListObj.setColumns(columns);

            if (Objects.nonNull(jzTaskInfoList)) {
                try {
                    for (int i = 0; i < jzTaskInfoList.size(); i++) {
                        // 跳过任务待检和任务检毕
                        if ("任务检毕".equals(jzTaskInfoList.get(i).getTaskStatus())) {
                            continue;
                        }
                        if ("任务待检".equals(jzTaskInfoList.get(i).getTaskStatus())) {
                            continue;
                        }
                        DataSource dataSource1 = new DataSource();

                        Map<String, Object> newDataSource1 = new HashMap<>();

                        newDataSource1.put("index", String.valueOf(i + 1));

                        JzTaskInfo jzTaskInfo = jzTaskInfoList.get(i);

                        Long sampleId = jzTaskInfo.getSampleId();

                        List<String> statusList = jzTaskWorkOrderInfoService.list().stream()
                                .filter(x -> jzTaskInfo.getId().equals(x.getTaskId())).map(x -> x.getStatusInfo())
                                .collect(Collectors.toList());
                        boolean allFinished = statusList.stream()
                                .allMatch(status -> "检毕".equals(status));

                        if (Objects.nonNull(sampleId)) {
                            SampleInfo sampleInfo = sampleInfoService.getById(sampleId);
                            if (Objects.nonNull(sampleId) && !allFinished) {
                                dataSource1.setTestName(sampleInfo.getSampleName());
                                newDataSource1.put("testName",
                                        sampleInfo.getSampleName() + " " + sampleInfo.getTestType());
                            }
                        }

                        List<JzTaskWorkOrderInfo> orderInfos = new ArrayList<>();
                        if (!allFinished) {
                            orderInfos = jzTaskWorkOrderInfoService
                                    .list(new LambdaQueryWrapper<JzTaskWorkOrderInfo>()
                                            .eq(JzTaskWorkOrderInfo::getTaskId, jzTaskInfo.getId())
                                            .in(JzTaskWorkOrderInfo::getStatusInfo, "在检", "检毕"));
                        }
                        if (CollUtil.isNotEmpty(orderInfos)) {
                            for (int j = 0; j < orderInfos.size(); j++) {
                                Sub sub1 = new Sub();
                                Long id = orderInfos.get(j).getId();
                                AssociationQuery<JzTaskWorkOrderInfoVO> associationQuery = new AssociationQuery<>(
                                        JzTaskWorkOrderInfoVO.class);
                                JzTaskWorkOrderInfoVO vo = associationQuery.getVo(id);
                                if (Objects.nonNull(vo)) {
                                    String gwBzId = vo.getGwBzId();
                                    // 转换为list<string>
                                    List<String> gwBzIdList = StrUtil.split(gwBzId, ",");
                                    List<StandardBasicInstrumentInfo> standardBasicInstrumentInfos = standardBasicInstrumentInfoService
                                            .listByIds(gwBzIdList);
                                    sub1.setTitle(vo.getWorkOrderNumber());
                                    if (CollUtil.isNotEmpty(standardBasicInstrumentInfos)) {
                                        // 获取所有 projectName ，并去重用逗号分割
                                        List<String> projectNames = standardBasicInstrumentInfos.stream()
                                                .map(StandardBasicInstrumentInfo::getProjectName).distinct()
                                                .collect(Collectors.toList());
                                        String projectName = String.join(",", projectNames);
                                        sub1.setTitle(projectName);
                                    } else {
                                        // todo 修改
                                        if (orderInfos.get(j).getEquipmentId().equals(1L)) {
                                            sub1.setTitle("雷冲实验");
                                        } else {
                                            sub1.setTitle("变压器集成检测多项实验");
                                        }
                                    }
                                    sub1.setTaskId(orderInfos.get(j).getWorkOrderNumber());
                                    sub1.setWorkStation(vo.getWorkstationName());
                                    sub1.setStatus(vo.getStatusInfo());
                                    dataSource1.setSub1(sub1);
                                    newDataSource1.put("sub" + (j + 1), sub1);
                                }
                            }

                        }
                        newdataSourceMap.add(newDataSource1);
                        dataSource.add(dataSource1);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

            taskListObj.setDataSource(newdataSourceMap);
        }

        /*------------------------------------------------合数据-----------------------------------------------------------------------*/
        reportInfo.setReportData(reportData);
        reportInfo.setSubjectArr(subjectArrs);
        reportInfo.setReportDataSource(reportDataSource);

        taskSummary.setTaskStatusData(taskStatusData);
        taskSummary.setTaskCountData(taskCountData);
        taskSummary.setTaskDataSource(taskDataSource);
        taskSummary.setTaskListObj(taskListObj);

        screenData.setReportInfo(reportInfo);
        screenData.setTaskSummary(taskSummary);

        return IResult.ok(screenData);
    }

    @Operation(summary = "大屏—报告汇总")
    @PostMapping("/newScreenDataCount")
    public IResult<NewScreenData> newScreenDataCount() {
        NewScreenData newScreenData = new NewScreenData();

        // Top 最上部分数据统计
        Map<String, Integer> top = new HashMap<>();

        List<SampleInfo> sampleInfos = sampleInfoService.list();
        List<JzTaskInfo> taskInfos = jzTaskInfoService.list().stream().filter( x -> "任务检毕".equals(x.getTaskStatus())).collect(Collectors.toList());
        List<JzReportInfo> reportInfos = jzReportInfoService.list();
        // 获取今日日期（不带时间部分）
        LocalDate today = LocalDate.now();
        // 筛选出今天创建的报告
        List<JzReportInfo> todayReports = jzReportInfoService.list().stream()
                .filter(report -> {
                    // 将Date转换为LocalDate进行比较
                    Instant instant = report.getCreateTime().toInstant();
                    ZoneId zoneId = ZoneId.systemDefault();
                    LocalDate reportDate = instant.atZone(zoneId).toLocalDate();
                    return reportDate.equals(today);
                })
                .collect(Collectors.toList());
        top.put("样品", CollUtil.isNotEmpty(sampleInfos) ? sampleInfos.size() : 0);
        top.put("任务", CollUtil.isNotEmpty(taskInfos) ? taskInfos.size() : 0);
        top.put("今日报告", CollUtil.isNotEmpty(todayReports) ? todayReports.size() : 0);
        top.put("累计报告", CollUtil.isNotEmpty(reportInfos) ? reportInfos.size() : 0);

        // Left 左部分数据统计
        Map<String, MidLeft> left = new HashMap<>();
        for (int i = 1; i <= 5; i++) {
            MidLeft midLeft = new MidLeft();

            SampleInfoQuery query = new SampleInfoQuery();
            String name = chooseSamepleName(i);
            query.setSampleMajor(name);
            AssociationQuery<SampleInfoVO> associationQuery = new AssociationQuery<>(SampleInfoVO.class);
            List<SampleInfoVO> sampleInfoVOS = associationQuery.voList(query);

            if (CollUtil.isNotEmpty(sampleInfoVOS)) {
                // 总体
                midLeft.setAllOfSample(sampleInfoVOS.size());
                // 待检
                List<SampleInfoVO> waitings = sampleInfoVOS.stream()
                        .filter(x -> "任务待检".equals(x.getJzTaskStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(waitings)) {
                    midLeft.setWaitingToCheck(waitings.size());
                } else {
                    midLeft.setWaitingToCheck(0);
                }
                // 在检
                List<SampleInfoVO> checking = sampleInfoVOS.stream()
                        .filter(x -> "任务在检".equals(x.getJzTaskStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(checking)) {
                    midLeft.setChecking(checking.size());
                } else {
                    midLeft.setChecking(0);
                }
                // 检毕
                List<SampleInfoVO> checkOver = sampleInfoVOS.stream()
                        .filter(x -> "任务检毕".equals(x.getJzTaskStatus()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(checkOver)) {
                    midLeft.setCheckOver(checkOver.size());
                } else {
                    midLeft.setCheckOver(0);
                }
            } else {
                midLeft.setAllOfSample(0);
                midLeft.setWaitingToCheck(0);
                midLeft.setChecking(0);
                midLeft.setCheckOver(0);
            }

            left.put(name, midLeft);
        }

        // Center 中间部分数据统计
        SampleInfoQuery query = new SampleInfoQuery();
        AssociationQuery<SampleInfoVO> associationQuery = new AssociationQuery<>(SampleInfoVO.class);
        List<SampleInfoVO> sampleInfoVOS = associationQuery.voList(query);

        Map<String, MidCenter> center = new HashMap<>();
        for (int i = 1; i <= 3; i++) {
            String getWarningName = getWarningName(i);
            MidCenter warningInfo = getgetWarningInfo(sampleInfoVOS, i);
            // // 随机生成1-10的数
            // warningInfo.setThreeBeforeFiveDay(new Random().nextInt(10) + 1);
            // warningInfo.setOverFiveDay(new Random().nextInt(10) + 1);
            center.put(getWarningName, warningInfo);
        }

        // Right 右边部分数据统计
        JzReportInfoQuery query4 = new JzReportInfoQuery();
        query4.setTimeSort(1);
        AssociationQuery<JzReportInfoVO> associationQuery4 = new AssociationQuery<>(JzReportInfoVO.class);
        List<JzReportInfoVO> jzReportInfos = associationQuery4.voList(query4);

        // Bottom 下面部分数据统计
        List<TaskDto> taskDtos = new ArrayList<>();

        JzTaskInfoQuery query1 = new JzTaskInfoQuery();
        query1.setTaskStatus("任务在检");
        AssociationQuery<JzTaskInfoVO> associationQuery1 = new AssociationQuery<>(JzTaskInfoVO.class);
        // List<JzTaskInfoVO> jzTaskInfoVOS = associationQuery1.voList(query1);
        query1.setTimeSort(1);
        List<JzTaskInfoVO> taskInfoVOS = associationQuery1.voList(query1);
        if (CollUtil.isNotEmpty(taskInfoVOS)) {
            List<Long> taskIdList = new ArrayList<>();
            List<Long> taskIds = taskInfoVOS.stream().map(x -> x.getId()).collect(Collectors.toList());
            for (Long taskId : taskIds) {
                List<JzTaskWorkOrderInfo> taskWorkOrderInfos = jzTaskWorkOrderInfoService
                        .list(new LambdaQueryWrapper<JzTaskWorkOrderInfo>()
                                .eq(JzTaskWorkOrderInfo::getTaskId, taskId));
                if (CollUtil.isNotEmpty(taskWorkOrderInfos)) {
                    taskIdList.add(taskId);
                }
            }
            if (CollUtil.isNotEmpty(taskIdList)) {
                taskInfoVOS = taskInfoVOS.stream().filter(x -> taskIdList.contains(x.getId()))
                        .collect(Collectors.toList());
            }
        }
        // List<JzTaskInfoVO> taskInfoVOS = jzTaskInfoVOS.stream()
        // .filter(x -> Objects.nonNull(x.getStartDate()))
        // .sorted(Comparator.comparing(JzTaskInfo::getStartDate).reversed())
        // .limit(6)
        // .collect(Collectors.toList());

        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");

        if (CollUtil.isNotEmpty(taskInfoVOS)) {
            for (JzTaskInfoVO jzTaskInfo : taskInfoVOS) {
                TaskDto taskDto = new TaskDto();

                taskDto.setTaskNumber(jzTaskInfo.getTaskNumber());
                taskDto.setTaskName(jzTaskInfo.getSampleName()+jzTaskInfo.getTestLevel()+"类的实验");
                taskDto.setSecondaryBlindSampleNumber(jzTaskInfo.getSampleInfo().getSecondaryBlindSampleNumber());
                taskDto.setTaskStartTime(outputFormat.format(jzTaskInfo.getStartDate()));

                Long cameralId = 0l;
                String stationNames = "";// 初始化工位名，多个做拼接处理
                String projectNames = "";// 初始化项目名，多个做拼接处理
                String testPersonNames = "";// 初始化测试人员名，多个做拼接处理
                Map<String, Integer> workdOrderStatus = new HashMap<>();// 初始化该任务对应的工单状态数量

                JzTaskWorkOrderInfoQuery query2 = new JzTaskWorkOrderInfoQuery();
                query2.setTaskId(jzTaskInfo.getId());
                query2.setStatusInfo("在检");
                AssociationQuery<JzTaskWorkOrderInfoVO> associationQuery2 = new AssociationQuery<>(
                        JzTaskWorkOrderInfoVO.class);
                List<JzTaskWorkOrderInfoVO> orderInfos = associationQuery2.voList(query2);

                if (CollUtil.isNotEmpty(orderInfos)) {
                    // 赋值工位名称
                    Set<Long> workStationIds = orderInfos.stream()
                            .filter(x -> Objects.nonNull(x.getWorkstationId()))
                            .map(x -> x.getWorkstationId())
                            .collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(workStationIds)) {
                        // 赋值摄像头id
                        List<BuInstrumentCameraInfo> cameraInfos = buInstrumentCameraInfoService
                                .list(new LambdaQueryWrapper<BuInstrumentCameraInfo>()
                                        .in(BuInstrumentCameraInfo::getWorkstationId, workStationIds));
                        if (CollUtil.isNotEmpty(cameraInfos)) {
                            cameralId = cameraInfos.get(0).getId();
                        }

                        List<BuInstrumentWorkstationInfo> workstationInfos = buInstrumentWorkstationInfoService
                                .list(new LambdaQueryWrapper<BuInstrumentWorkstationInfo>()
                                        .in(BuInstrumentWorkstationInfo::getId, workStationIds));
                        stationNames = workstationInfos.stream()
                                .filter(x -> StrUtil.isNotEmpty(x.getWorkstationName()))
                                .map(x -> x.getWorkstationName())
                                .collect(Collectors.joining(","));
                    }

                    // 赋值项目名称
                    projectNames = orderInfos.stream().map(JzTaskWorkOrderInfoVO::getEquipmentName).distinct()
                            .collect(Collectors.joining(","));
                    /*
                     * for (JzTaskWorkOrderInfoVO orderInfo : orderInfos) {
                     * String gwBzId = orderInfo.getGwBzId();
                     * if (com.anji.captcha.util.StringUtils.isNotEmpty(gwBzId)) {
                     * List<Long> ids = convertToIdList(gwBzId);
                     * List<StandardBasicInstrumentInfo> standardBasicInstrumentInfos =
                     * standardBasicInstrumentInfoService
                     * .list(new LambdaQueryWrapper<StandardBasicInstrumentInfo>()
                     * .in(StandardBasicInstrumentInfo::getId, ids));
                     * if (CollUtil.isNotEmpty(standardBasicInstrumentInfos)) {
                     * String gwBzName = standardBasicInstrumentInfos.stream()
                     * .map(StandardBasicInstrumentInfo::getProjectName) // 获取需要拼接的字段
                     * .filter(x -> x != null && !x.isEmpty()) // 过滤空值
                     * .collect(Collectors.joining(","));// 用逗号拼接
                     * 
                     * if (StringUtils.isNotEmpty(gwBzName)) {
                     * projectNames = gwBzName;
                     * }
                     * }
                     * }
                     * }
                     */

                    // 赋值测试人员名称
                    testPersonNames = orderInfos.stream()
                            .filter(x -> StrUtil.isNotEmpty(x.getTestUserFullName()))
                            .map(x -> x.getTestUserFullName())
                            .distinct()
                            .collect(Collectors.joining(","));

                    // 赋值该任务下的工单各个状态的数量
                    JzTaskWorkOrderInfoQuery query3 = new JzTaskWorkOrderInfoQuery();
                    query3.setTaskId(jzTaskInfo.getId());
                    AssociationQuery<JzTaskWorkOrderInfoVO> associationQuery3 = new AssociationQuery<>(
                            JzTaskWorkOrderInfoVO.class);
                    List<JzTaskWorkOrderInfoVO> orderInfoList = associationQuery3.voList(query3);

                    workdOrderStatus = orderInfoList.stream()
                            .collect(Collectors.groupingBy(
                                    JzTaskWorkOrderInfoVO::getStatusInfo,
                                    Collectors.collectingAndThen(
                                            Collectors.counting(),
                                            Long::intValue)));

                }
                taskDto.setWorkStationName(stationNames);
                taskDto.setProjectName(projectNames);
                taskDto.setTestPersonName(testPersonNames);
                taskDto.setWorkdOrderStatus(workdOrderStatus);
                taskDto.setCamera(cameralId);

                taskDtos.add(taskDto);
            }
        }

        // 合并数据
        newScreenData.setTop(top);
        newScreenData.setLeft(left);
        newScreenData.setCenter(center);
        newScreenData.setRight(jzReportInfos);
        newScreenData.setBottom(taskDtos);

        return IResult.ok(newScreenData);
    }

    private MidCenter getgetWarningInfo(List<SampleInfoVO> sampleInfoVOS, int i) {
        MidCenter center = new MidCenter();
        center.setThreeBeforeFiveDay(0);
        center.setOverFiveDay(0);

        Date currentDate = new Date(); // 当前时间
        long threeDaysInMillis = TimeUnit.DAYS.toMillis(3); // 3 天的毫秒数
        long fiveDaysInMillis = TimeUnit.DAYS.toMillis(5); // 5 天的毫秒数

        if (i == 1) {
            // 判断样品超期3天小于5天的数据
            List<SampleInfoVO> sampleInfosOverThreeAndBeforFiveDay = sampleInfoVOS.stream()
                    .filter(x -> !"任务检毕".equals(x.getJzTaskStatus()))
                    .filter(sample -> {
                        Date receiptTime = sample.getSampleReceiptTime();
                        if (receiptTime == null)
                            return false; // 忽略无收样时间的记录

                        long diffInMillis = currentDate.getTime() - receiptTime.getTime();
                        return diffInMillis > threeDaysInMillis && diffInMillis <= fiveDaysInMillis;
                    })
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(sampleInfosOverThreeAndBeforFiveDay)) {
                center.setThreeBeforeFiveDay(sampleInfosOverThreeAndBeforFiveDay.size());
            }

            // 判断样品超期5天的数据
            List<SampleInfoVO> sampleInfosOverFiveDay = sampleInfoVOS.stream()
                    .filter(x -> !"任务检毕".equals(x.getJzTaskStatus()))
                    .filter(sample -> {
                        Date receiptTime = sample.getSampleReceiptTime();
                        if (receiptTime == null)
                            return false; // 忽略无收样时间的记录

                        long diffInMillis = currentDate.getTime() - receiptTime.getTime();
                        return diffInMillis > fiveDaysInMillis;
                    })
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(sampleInfosOverFiveDay)) {
                center.setOverFiveDay(sampleInfosOverFiveDay.size());
            }
        }

        // if (i == 2) {
        // List<JzTaskInfo> jzTaskInfos = jzTaskInfoService.list().stream()
        // .filter(x -> !"任务检毕".equals(x.getTaskStatus())).collect(Collectors.toList());
        // // 判断样品超期3天小于5天的数据
        // if (CollUtil.isNotEmpty(jzTaskInfos)) {
        // List<JzTaskInfo> jzTaskInfosOverThreeAndBeforFiveDay = jzTaskInfos.stream()
        // .filter(x -> {
        // Date taskCreationTime = x.getTaskCreationTime();
        // if (taskCreationTime == null)
        // return false; // 忽略无收样时间的记录
        //
        // long diffInMillis = currentDate.getTime() - taskCreationTime.getTime();
        // return diffInMillis > threeDaysInMillis && diffInMillis <= fiveDaysInMillis;
        // })
        // .collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(jzTaskInfosOverThreeAndBeforFiveDay)) {
        // center.setThreeBeforeFiveDay(jzTaskInfosOverThreeAndBeforFiveDay.size());
        // }
        // }
        //
        // // 判断样品超期5天的数据
        // List<JzTaskInfo> jzTaskInfosOverFiveDay = jzTaskInfos.stream()
        // .filter(x -> {
        // Date taskCreationTime = x.getTaskCreationTime();
        // if (taskCreationTime == null)
        // return false; // 忽略无收样时间的记录
        //
        // long diffInMillis = currentDate.getTime() - taskCreationTime.getTime();
        // return diffInMillis > fiveDaysInMillis;
        // })
        // .collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(jzTaskInfosOverFiveDay)) {
        // center.setOverFiveDay(jzTaskInfosOverFiveDay.size());
        // }
        // }

        return center;
    }

    private String getWarningName(int i) {
        String getWarningName = "";
        if (i == 1) {
            getWarningName = "样品超期";
        }
        if (i == 2) {
            getWarningName = "任务超期";
        }
        if (i == 3) {
            getWarningName = "报告超期";
        }
        return getWarningName;
    }

    private String chooseSamepleName(int i) {
        String name = "";
        if (i == 1) {
            name = "变压器";
        }
        if (i == 2) {
            name = "开关";
        }
        if (i == 3) {
            name = "线圈";
        }
        if (i == 4) {
            name = "材料";
        }
        if (i == 5) {
            name = "台台检";
        }
        return name;
    }

    public static List<Long> convertToIdList(String idString) {
        if (idString == null || idString.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(idString.split(","))
                .map(String::trim)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

}
