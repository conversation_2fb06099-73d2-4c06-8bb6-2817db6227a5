package com.westcatr.rd.testbusiness.business.basics.service;

import java.util.List;
import java.util.Map;

import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.InstrumentNewInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.InstrumentNewInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
public interface InstrumentNewInfoService extends IService<InstrumentNewInfo> {

    IPage<InstrumentNewInfo> entityPage(InstrumentNewInfoQuery query);

    InstrumentNewInfo getEntityById(Long id);

    boolean saveEntity(InstrumentNewInfo param);

    boolean updateEntity(InstrumentNewInfo param);

    boolean removeEntityById(Long id);

    IPage<InstrumentNewInfoVO> entityMyPage(InstrumentNewInfoQuery query);

    /**
     * 获取设备检测数据
     *
     * @param equipmentId  设备ID
     * @param parameterIds 参数ID列表
     * @return 检测数据结果列表
     */
    List<AutoResultDto> getDeviceTestData(Long equipmentId, List<Long> parameterIds);

    /**
     * 获取设备参数（不获取实际数据）
     *
     * @param equipmentId  设备ID
     * @param parameterIds 参数ID列表
     * @return 参数列表（不包含实际数据）
     */
    List<AutoResultDto> getDeviceParamsOnly(Long equipmentId, List<Long> parameterIds);

    /**
     * 发送异步设备请求
     *
     * @param equipmentId  设备ID
     * @param parameterIds 参数ID列表
     * @return 请求ID，用于后续获取响应
     */
    String sendDeviceRequestAsync(Long equipmentId, List<Long> parameterIds);

    /**
     * 获取设备响应
     *
     * @param equipmentId 设备ID
     * @return 设备响应
     */
    Map<String, Object> getDeviceResponse(Long equipmentId);

    /**
     * 📥 导入可检参数
     *
     * @param file        Excel文件
     * @param equipmentId 设备ID
     * @return 导入结果信息
     * <AUTHOR>
     * @since 2025-01-14
     */
    String importCheckParams(MultipartFile file, Long equipmentId);

    /**
     * 📤 导出可检参数模板
     *
     * @param equipmentId 设备ID（可选，如果提供则导出该设备的实际数据）
     * @return Excel模板文件
     * <AUTHOR>
     * @since 2025-01-14
     */
    ResponseEntity<Resource> exportCheckParamsTemplate(Long equipmentId);
}
