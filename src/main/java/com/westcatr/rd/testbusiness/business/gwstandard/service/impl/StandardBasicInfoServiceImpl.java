package com.westcatr.rd.testbusiness.business.gwstandard.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicInfoQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicInfoMapper;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标准—集成装置设备信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Service
public class StandardBasicInfoServiceImpl extends ServiceImpl<StandardBasicInfoMapper, StandardBasicInfo> implements StandardBasicInfoService {

    @Override
    public IPage<StandardBasicInfo> entityPage(StandardBasicInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<StandardBasicInfo>().create(query));
    }

    @Override
    public StandardBasicInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(StandardBasicInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(StandardBasicInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

