package com.westcatr.rd.testbusiness.business.workstation.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.workstation.entity.WorkstationHistoryCount;
import com.westcatr.rd.testbusiness.business.workstation.pojo.query.WorkstationHistoryCountQuery;

/**
 * <p>
 * 工作台-历史数据统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public interface WorkstationHistoryCountService extends IService<WorkstationHistoryCount> {

    IPage<WorkstationHistoryCount> entityPage(WorkstationHistoryCountQuery query);

    WorkstationHistoryCount getEntityById(Long id);

    boolean saveEntity(WorkstationHistoryCount param);

    boolean updateEntity(WorkstationHistoryCount param);

    boolean removeEntityById(Long id);
}
