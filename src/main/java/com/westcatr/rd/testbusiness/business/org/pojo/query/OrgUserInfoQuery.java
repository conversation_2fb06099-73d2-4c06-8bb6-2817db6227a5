package com.westcatr.rd.testbusiness.business.org.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition.Condition;
import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "人员表查询对象")
public class OrgUserInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @QueryCondition
    private Long id;

    @Schema(description = "用户名")
    @QueryCondition(condition = Condition.LIKE)
    private String userName;

    @Schema(description = "密码")
    @QueryCondition
    private String passWord;

    @Schema(description = "真实姓名")
    @QueryCondition(condition = Condition.LIKE)
    private String fullName;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "用户电话")
    @QueryCondition(condition = Condition.LIKE)
    private String phone;

    @Schema(description = "邮件")
    @QueryCondition(condition = Condition.LIKE)
    private String email;

    @JoinExpression(value = "join org_user_dept_info du on du.user_id = org_user_info.id left join org_dept_info d on du.dept_id = d.id")
    @Schema(description = "部门id")
    @QueryCondition(field = "d.id", condition = QueryCondition.Condition.EQ)
    private String deptId;

    @JoinExpression(value = "join sys_auth_user_role ur on ur.user_id = org_user_info.id left join sys_auth_role r on ur.role_id = r.id")
    @Schema(description = "角色id")
    @QueryCondition(field = "r.id", condition = QueryCondition.Condition.EQ)
    private String roleId;

    private String roleNewId;

    private String deptNewId;

    @QueryCondition
    private Integer enable;
}
