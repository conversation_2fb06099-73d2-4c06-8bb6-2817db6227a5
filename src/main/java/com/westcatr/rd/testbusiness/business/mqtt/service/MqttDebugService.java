package com.westcatr.rd.testbusiness.business.mqtt.service;

import java.util.List;
import java.util.Map;

import com.westcatr.rd.testbusiness.business.mqtt.entity.MqttDebugResponse;

/**
 * <p>
 * MQTT调试服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface MqttDebugService {

    /**
     * 保存设备响应到调试文件
     *
     * @param deviceId     设备ID
     * @param deviceType   设备类型
     * @param messageId    消息ID
     * @param response     响应数据
     * @param tableName    表名（可选）
     * @return 保存的文件路径
     */
    String saveDeviceResponse(String deviceId, String deviceType, String messageId, 
                             Map<String, Object> response, String tableName);

    /**
     * 获取设备的调试响应
     *
     * @param deviceId   设备ID
     * @param deviceType 设备类型
     * @param tableName  表名（可选）
     * @return 调试响应数据
     */
    Map<String, Object> getDebugResponse(String deviceId, String deviceType, String tableName);

    /**
     * 获取设备的最新调试响应
     *
     * @param deviceId   设备ID
     * @param deviceType 设备类型
     * @param tableName  表名（可选）
     * @return 最新的调试响应数据
     */
    Map<String, Object> getLatestDebugResponse(String deviceId, String deviceType, String tableName);

    /**
     * 获取设备的所有调试响应文件列表
     *
     * @param deviceId   设备ID
     * @param deviceType 设备类型
     * @return 调试响应文件列表
     */
    List<MqttDebugResponse> listDebugResponses(String deviceId, String deviceType);

    /**
     * 删除调试响应文件
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    boolean deleteDebugResponse(String filePath);

    /**
     * 清理过期的调试响应文件
     *
     * @return 清理的文件数量
     */
    int cleanExpiredResponses();

    /**
     * 创建自定义调试响应
     *
     * @param deviceId     设备ID
     * @param deviceType   设备类型
     * @param response     响应数据
     * @param description  描述
     * @param tableName    表名（可选）
     * @return 保存的文件路径
     */
    String createCustomDebugResponse(String deviceId, String deviceType, 
                                   Map<String, Object> response, String description, String tableName);

    /**
     * 检查是否启用调试模式
     *
     * @return 是否启用调试模式
     */
    boolean isDebugEnabled();

    /**
     * 设置调试模式状态
     *
     * @param enabled 是否启用
     */
    void setDebugEnabled(boolean enabled);

    /**
     * 获取调试统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getDebugStatistics();
}
