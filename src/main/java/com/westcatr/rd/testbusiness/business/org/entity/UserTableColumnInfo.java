package com.westcatr.rd.testbusiness.business.org.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 自定义展示列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user_table_column_info")
@Schema(description="自定义展示列表")
public class UserTableColumnInfo extends Model<UserTableColumnInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "列表名")
    @Length(max = 200, message = "列表名长度不能超过200", groups = {Insert.class, Update.class})
    @TableField("key_name")
    private String keyName;

    @Schema(description = "列名详情")
    @Length(max = 65535, message = "列名详情长度不能超过65535", groups = {Insert.class, Update.class})
    @TableField("columns_info")
    private String columnsInfo;

    @Schema(description = "员工id")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
