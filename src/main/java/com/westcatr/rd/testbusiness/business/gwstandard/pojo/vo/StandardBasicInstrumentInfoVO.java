package com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—检测仪器标准信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "标准—检测仪器标准信息表VO对象")
public class StandardBasicInstrumentInfoVO extends StandardBasicInstrumentInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @JoinSelect(joinClass = StandardBasicInfo.class, mainId = "integratedDeviceId", field = "integrated_device_name")
    private String standardBasicInfoName;

    @Schema(description = "仪器名称")
    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentNewInfo.class, relationId = "gw_bz_id", field = "sbmc")
    private List<String> instrumentNames;

    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentNewInfo.class, joinCode = -57, field = "id")
    private List<Long> instrumentNewIds;

    @TableField(exist = false)
    @JoinSelect(joinClass = InstrumentNewInfo.class, joinCode = -57, middleTable = "sys_common_join_info", and = "join_code = 57", field = "sbmc")
    private List<String> proName;

    @TableField(exist = false)
    @JoinSelect(joinClass = StandardBasicProjectInstrument.class, joinCode = 58, and = "join_code = 58")
    private List<StandardBasicProjectInstrument> standardBasicProjectInstrumentList;

    @TableField(exist = false)
    @JoinSelect(joinClass = StandardBasicModelParam.class, relationId = "standard_basic_instrument_id")
    private List<StandardBasicModelParam> standardBasicModelParamList;

    @TableField(exist = false)
    @Schema(description = "报告模板文件信息")
    @JoinSelect(joinClass = FileInfo.class, mainId = "reportTemplateFileId")
    private FileInfo reportTemplateFileInfo;

    @TableField(exist = false)
    @Schema(description = "原始记录模板文件信息")
    @JoinSelect(joinClass = FileInfo.class, mainId = "originalRecordTemplateFileId")
    private FileInfo originalRecordTemplateFileInfo;
}
