package com.westcatr.rd.testbusiness.business.basics.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkCameraDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkEquipmentDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkstationDeviceTreeDTO;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentWorkstationInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.BuInstrumentWorkstationInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentWorkstationInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * BuInstrumentWorkstationInfo 控制器
 * 
 * <AUTHOR>
 * @since 2025-03-10
 */
@Validated
@Tag(name = "工位信息管理接口", description = "工位信息管理接口")
@Slf4j
@RestController
public class BuInstrumentWorkstationInfoController {

    @Autowired
    private BuInstrumentWorkstationInfoService buInstrumentWorkstationInfoService;

    @Operation(summary = "获取工位信息管理分页数据")
    @PostMapping("/buInstrumentWorkstationInfo/page")
    public IResult<IPage<BuInstrumentWorkstationInfo>> getBuInstrumentWorkstationInfoPage(
            @RequestBody BuInstrumentWorkstationInfoQuery query) {
        return IResult.ok(buInstrumentWorkstationInfoService.entityPage(query));
    }

    @Operation(summary = "获取工位信息管理数据")
    @PostMapping("/buInstrumentWorkstationInfo/get")
    public IResult<BuInstrumentWorkstationInfo> getBuInstrumentWorkstationInfoById(@RequestBody @Id ID id) {
        return IResult.ok(buInstrumentWorkstationInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增工位信息管理数据")
    @PostMapping("/buInstrumentWorkstationInfo/add")
    public IResult addBuInstrumentWorkstationInfo(
            @RequestBody @Validated(Insert.class) BuInstrumentWorkstationInfo param) {
        return IResult.auto(buInstrumentWorkstationInfoService.saveEntity(param));
    }

    @Operation(summary = "更新工位信息管理数据")
    @PostMapping("/buInstrumentWorkstationInfo/update")
    public IResult updateBuInstrumentWorkstationInfoById(
            @RequestBody @Validated(Update.class) BuInstrumentWorkstationInfo param) {
        return IResult.auto(buInstrumentWorkstationInfoService.updateEntity(param));
    }

    @Operation(summary = "删除工位信息管理数据")
    @PostMapping("/buInstrumentWorkstationInfo/delete")
    public IResult deleteBuInstrumentWorkstationInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            buInstrumentWorkstationInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取工位信息管理VO分页数据")
    @PostMapping("/buInstrumentWorkstationInfo/voPage")
    public IResult<IPage<BuInstrumentWorkstationInfoVO>> getBuInstrumentWorkstationInfoVoPage(
            @RequestBody BuInstrumentWorkstationInfoQuery query) {
        AssociationQuery<BuInstrumentWorkstationInfoVO> associationQuery = new AssociationQuery<>(
                BuInstrumentWorkstationInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取工位信息管理VO数据")
    @PostMapping("/buInstrumentWorkstationInfo/getVo")
    public IResult<BuInstrumentWorkstationInfoVO> getBuInstrumentWorkstationInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<BuInstrumentWorkstationInfoVO> associationQuery = new AssociationQuery<>(
                BuInstrumentWorkstationInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "关联设备")
    @PostMapping("/buInstrumentWorkstationInfo/saveEntityWithDevice")
    public IResult saveEntityWithDevice(@RequestBody WorkEquipmentDto workEquipmentDto) {
        return IResult.auto(buInstrumentWorkstationInfoService.saveEntityWithDevice(workEquipmentDto));
    }

    @Operation(summary = "关联摄像头")
    @PostMapping("/buInstrumentWorkstationInfo/saveEntityWithCamera")
    public IResult saveEntityWithCamera(@RequestBody WorkCameraDto workCameraDto) {
        return IResult.auto(buInstrumentWorkstationInfoService.saveEntityWithCamera(workCameraDto));
    }

    @Operation(summary = "获取工位-设备树形结构")
    @PostMapping("/buInstrumentWorkstationInfo/getWorkstationDeviceTree")
    public IResult<List<WorkstationDeviceTreeDTO>> getWorkstationDeviceTree() {
        return IResult.ok(buInstrumentWorkstationInfoService.getWorkstationDeviceTree());
    }
}
