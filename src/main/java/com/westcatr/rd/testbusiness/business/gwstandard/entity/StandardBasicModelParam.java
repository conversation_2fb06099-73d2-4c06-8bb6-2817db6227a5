package com.westcatr.rd.testbusiness.business.gwstandard.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准-模型参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("standard_basic_model_param")
@Schema(description = "标准-模型参数表")
public class StandardBasicModelParam extends Model<StandardBasicModelParam> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "检项")
    @Length(max = 255, message = "检项长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("test_item")
    private String testItem;

    @Schema(description = "检项参数（英文，唯一）")
    @Length(max = 255, message = "检项参数（英文，唯一）长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("param_key")
    private String paramKey;

    @Schema(description = "单位")
    @Length(max = 64, message = "单位长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("unit")
    private String unit;

    @Schema(description = "判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型）")
    @Length(max = 32, message = "判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型）长度不能超过32", groups = { Insert.class,
            Update.class })
    @TableField("judge_type")
    private String judgeType;

    @Schema(description = "合格标准（描述合格的参数）")
    @Length(max = 1024, message = "合格标准（描述合格的参数）长度不能超过1024", groups = { Insert.class, Update.class })
    @TableField("qualified_standard")
    private String qualifiedStandard;

    @Schema(description = "判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =)")
    @Length(max = 1024, message = "判定公式(公式里包含:+ - × ÷ ( ) ∪ ∩ ≥ ≤ < > =)长度不能超过1024", groups = { Insert.class,
            Update.class })
    @TableField("judge_formula")
    private String judgeFormula;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "实验标准id")
    @TableField("standard_basic_instrument_id")
    private Long standardBasicInstrumentId;

    // 实验标准参数id
    @TableField("standard_basic_project_param_id")
    private Long standardBasicProjectParamId;

    @TableField("collection_method")
    private String collectionMethod;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
