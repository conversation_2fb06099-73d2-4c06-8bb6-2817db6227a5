package com.westcatr.rd.testbusiness.business.sample.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.file.entity.FileInfo;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskInfo;
import com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 样品——基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "样品——基本信息表VO对象")
public class SampleInfoVO extends SampleInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Long receivedeptId;

    @TableField(exist = false)
    @Schema(description = "样品铭牌图片信息")
    @JoinSelect(joinClass = FileInfo.class, mainId = "nameplateFileId")
    private FileInfo nameplateFileInfo;

    @TableField(exist = false)
    @Schema(description = "样品铭牌图片信息")
    @JoinSelect(joinClass = FileInfo.class, mainId = "samplePicId")
    private FileInfo samplePicInfo;

    @TableField(exist = false)
    @Schema(description = "检测任务状态")
    @JoinSelect(joinClass = JzTaskInfo.class, relationId = "sample_id", field = "task_status")
    private String jzTaskStatus;

    @TableField(exist = false)
    @Schema(description = "报告状态")
    private String jzReportStatus;

}
