package com.westcatr.rd.testbusiness.business.jztask.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskTestDataInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskTestDataInfo;
import com.westcatr.rd.testbusiness.business.jztask.mapper.JzTaskTestDataInfoMapper;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskTestDataInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 检测任务测试数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Service
public class JzTaskTestDataInfoServiceImpl extends ServiceImpl<JzTaskTestDataInfoMapper, JzTaskTestDataInfo> implements JzTaskTestDataInfoService {

    @Override
    public IPage<JzTaskTestDataInfo> entityPage(JzTaskTestDataInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<JzTaskTestDataInfo>().create(query));
    }

    @Override
    public JzTaskTestDataInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(JzTaskTestDataInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(JzTaskTestDataInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

