package com.westcatr.rd.testbusiness.business.gwstandard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicProjectParamQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 实验项目参数表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface StandardBasicProjectParamService extends IService<StandardBasicProjectParam> {

    IPage<StandardBasicProjectParam> entityPage(StandardBasicProjectParamQuery query);

    StandardBasicProjectParam getEntityById(Long id);

    boolean saveEntity(StandardBasicProjectParam param);

    boolean updateEntity(StandardBasicProjectParam param);

    boolean removeEntityById(Long id);
}
