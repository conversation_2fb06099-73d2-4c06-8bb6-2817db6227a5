package com.westcatr.rd.testbusiness.business.mqtt.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 设备对接规范条目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="设备对接规范条目表VO对象")
public class DeviceIntegrationConfigItemVO extends DeviceIntegrationConfigItem {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
