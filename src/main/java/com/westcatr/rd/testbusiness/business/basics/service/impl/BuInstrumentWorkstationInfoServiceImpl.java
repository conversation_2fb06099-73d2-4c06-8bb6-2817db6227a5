package com.westcatr.rd.testbusiness.business.basics.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentCameraInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.BuInstrumentWorkstationInfo;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.mapper.BuInstrumentWorkstationInfoMapper;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkCameraDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkEquipmentDto;
import com.westcatr.rd.testbusiness.business.basics.pojo.dto.WorkstationDeviceTreeDTO;
import com.westcatr.rd.testbusiness.business.basics.pojo.query.BuInstrumentWorkstationInfoQuery;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentCameraInfoService;
import com.westcatr.rd.testbusiness.business.basics.service.BuInstrumentWorkstationInfoService;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 工位信息管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class BuInstrumentWorkstationInfoServiceImpl
        extends ServiceImpl<BuInstrumentWorkstationInfoMapper, BuInstrumentWorkstationInfo>
        implements BuInstrumentWorkstationInfoService {

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private BuInstrumentCameraInfoService buInstrumentCameraInfoService;

    @Override
    public IPage<BuInstrumentWorkstationInfo> entityPage(BuInstrumentWorkstationInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<BuInstrumentWorkstationInfo>().create(query));
    }

    @Override
    public BuInstrumentWorkstationInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(BuInstrumentWorkstationInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(BuInstrumentWorkstationInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public boolean saveEntityWithDevice(WorkEquipmentDto workEquipmentDto) {
        // 首先解绑当前工位关联的所有设备
        LambdaUpdateWrapper<InstrumentNewInfo> unbindWrapper = new LambdaUpdateWrapper<>();
        unbindWrapper.eq(InstrumentNewInfo::getWorkstationId, workEquipmentDto.getId());
        unbindWrapper.set(InstrumentNewInfo::getWorkstationId, null);
        instrumentNewInfoService.update(unbindWrapper);

        // 如果传入了设备ID，则进行关联操作
        if (workEquipmentDto.getEquipmentIds() != null) {
            // 先检查所有设备是否存在
            for (Long equipmentId : workEquipmentDto.getEquipmentIds()) {
                InstrumentNewInfo instrumentNewInfo = instrumentNewInfoService.getById(equipmentId);
                if (instrumentNewInfo == null) {
                    throw new RuntimeException("设备不存在，设备ID：" + equipmentId);
                }
            }

            // 所有设备都存在，再进行关联操作
            for (Long equipmentId : workEquipmentDto.getEquipmentIds()) {
                InstrumentNewInfo instrumentNewInfo = instrumentNewInfoService.getById(equipmentId);

                // 如果设备已关联其他工位，先解除原关联
                if (instrumentNewInfo.getWorkstationId() != null) {
                    instrumentNewInfo.setWorkstationId(null);
                    instrumentNewInfoService.updateById(instrumentNewInfo);
                }

                // 建立新关联
                instrumentNewInfo.setWorkstationId(workEquipmentDto.getId());
                instrumentNewInfoService.updateById(instrumentNewInfo);
            }
        }
        return true;
    }

    @Override
    public boolean saveEntityWithCamera(WorkCameraDto workCameraDto) {
        // 首先解绑当前工位关联的所有摄像头
        LambdaUpdateWrapper<BuInstrumentCameraInfo> unbindWrapper = new LambdaUpdateWrapper<>();
        unbindWrapper.eq(BuInstrumentCameraInfo::getWorkstationId, workCameraDto.getId());
        unbindWrapper.set(BuInstrumentCameraInfo::getWorkstationId, null);
        buInstrumentCameraInfoService.update(unbindWrapper);

        // 如果传入了摄像头ID，则进行关联操作
        if (workCameraDto.getCameraId() != null) {
            // 获取摄像头信息
            BuInstrumentCameraInfo buInstrumentCameraInfo = buInstrumentCameraInfoService
                    .getById(workCameraDto.getCameraId());
            if (buInstrumentCameraInfo == null) {
                throw new RuntimeException("摄像头不存在");
            }

            // 如果摄像头已关联其他工位，先解除原关联
            if (buInstrumentCameraInfo.getWorkstationId() != null) {
                buInstrumentCameraInfo.setWorkstationId(null);
                buInstrumentCameraInfoService.updateById(buInstrumentCameraInfo);
            }

            // 建立新关联
            buInstrumentCameraInfo.setWorkstationId(workCameraDto.getId());
            buInstrumentCameraInfoService.updateById(buInstrumentCameraInfo);
        }
        return true;
    }

    @Override
    public List<WorkstationDeviceTreeDTO> getWorkstationDeviceTree() {
        // 获取所有工位信息
        List<BuInstrumentWorkstationInfo> workstations = this.list();
        if (workstations == null || workstations.isEmpty()) {
            return new ArrayList<>();
        }

        // 构建工位-设备树形结构
        return workstations.stream().map(workstation -> {
            WorkstationDeviceTreeDTO workstationNode = new WorkstationDeviceTreeDTO();
            workstationNode.setId(workstation.getId());
            workstationNode.setLabel(workstation.getWorkstationName());
            workstationNode.setType("workstation");

            // 获取该工位下的所有设备
            List<InstrumentNewInfo> devices = instrumentNewInfoService.lambdaQuery()
                    .eq(InstrumentNewInfo::getWorkstationId, workstation.getId())
                    .list();

            // 构建设备节点
            if (devices != null && !devices.isEmpty()) {
                List<WorkstationDeviceTreeDTO> deviceNodes = devices.stream().map(device -> {
                    WorkstationDeviceTreeDTO deviceNode = new WorkstationDeviceTreeDTO();
                    deviceNode.setId(device.getId());
                    deviceNode.setLabel(device.getSbmc()); // 设备名称
                    deviceNode.setType("device");
                    deviceNode.setChildren(new ArrayList<>()); // 设备节点没有子节点
                    return deviceNode;
                }).collect(Collectors.toList());
                workstationNode.setChildren(deviceNodes);
            } else {
                workstationNode.setChildren(new ArrayList<>());
            }

            return workstationNode;
        }).collect(Collectors.toList());
    }
}
