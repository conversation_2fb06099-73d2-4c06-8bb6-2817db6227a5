package com.westcatr.rd.testbusiness.business.jztask.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 检测任务测试数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_task_test_data_info")
@Schema(description="检测任务测试数据表")
public class JzTaskTestDataInfo extends Model<JzTaskTestDataInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @Schema(description = "主键 ID")
    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Integer id;

    @Schema(description = "样品名称")
    @Length(max = 32, message = "样品名称长度不能超过32", groups = {Insert.class, Update.class})
    @TableField("sample_name")
    private String sampleName;

    @Schema(description = "模式")
    @Length(max = 256, message = "模式长度不能超过256", groups = {Insert.class, Update.class})
    @TableField("mode")
    private String mode;

    @Schema(description = "测试项目")
    @Length(max = 64, message = "测试项目长度不能超过64", groups = {Insert.class, Update.class})
    @TableField("test_project")
    private String testProject;

    @Schema(description = "参数")
    @Length(max = 64, message = "参数长度不能超过64", groups = {Insert.class, Update.class})
    @TableField("parameter")
    private String parameter;

    @Schema(description = "单位")
    @Length(max = 32, message = "单位长度不能超过32", groups = {Insert.class, Update.class})
    @TableField("unit")
    private String unit;

    @Schema(description = "数值")
    @Length(max = 32, message = "数值长度不能超过32", groups = {Insert.class, Update.class})
    @TableField("value")
    private String value;

    @Schema(description = "反馈信息")
    @Length(max = 64, message = "反馈信息长度不能超过64", groups = {Insert.class, Update.class})
    @TableField("back_info")
    private String backInfo;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
