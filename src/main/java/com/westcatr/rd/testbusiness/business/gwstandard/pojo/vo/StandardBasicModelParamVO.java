package com.westcatr.rd.testbusiness.business.gwstandard.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准-模型参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "标准-模型参数表VO对象")
public class StandardBasicModelParamVO extends StandardBasicModelParam {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
