package com.westcatr.rd.testbusiness.business.mqtt.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.devicedata.entity.DeviceDataConfig;
import com.westcatr.rd.testbusiness.business.devicedata.service.DeviceDataService;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.DeviceTestDataRequestDto;

import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationService;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备集成控制器
 */
@RestController
@RequestMapping("/deviceIntegration")
@Tag(name = "设备集成接口")
@Slf4j
public class DeviceIntegrationController {

    @Autowired
    private DeviceIntegrationService deviceIntegrationService;

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private DeviceDataService deviceDataService;

    /**
     * 获取设备实验数据
     */
    @Operation(summary = "获取设备实验数据")
    @PostMapping("/getExperimentData")
    public IResult<Map<String, Object>> getExperimentData(@RequestBody DeviceTestDataRequestDto request) {
        log.info("获取设备实验数据，设备ID：{}，参数ID列表：{}", request.getEquipmentId(), request.getParameterIds());

        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 根据设备ID查询设备信息
            InstrumentNewInfo deviceInfo = instrumentNewInfoService.getById(request.getEquipmentId());
            if (deviceInfo == null) {
                log.warn("设备不存在，设备ID：{}", request.getEquipmentId());
                return IResult.ok(result);
            }

            // 2. 获取设备类型代码
            String deviceTypeCode = deviceInfo.getDeviceTypeCode();
            if (deviceTypeCode == null || deviceTypeCode.isEmpty()) {
                log.warn("设备类型代码为空，设备ID：{}", request.getEquipmentId());
                return IResult.ok(result);
            }

            // 3. 根据设备类型代码查询bu_device_data_config表
            DeviceDataConfig deviceDataConfig = deviceDataService.getDeviceConfig(deviceTypeCode);
            if (deviceDataConfig == null) {
                log.warn("未找到设备数据配置，设备类型代码：{}", deviceTypeCode);
                return IResult.ok(result);
            }

            // 4. 解析response_body中的JSON配置
            String responseBody = deviceDataConfig.getResponseBody();
            if (responseBody == null || responseBody.isEmpty()) {
                log.warn("设备响应体配置为空，设备代码：{}", deviceTypeCode);
                return IResult.ok(result);
            }

            JSONObject responseBodyObj = JSONUtil.parseObj(responseBody);
            if (!responseBodyObj.containsKey("experiments")) {
                log.warn("设备响应体中不包含experiments配置，设备代码：{}", deviceTypeCode);
                return IResult.ok(result);
            }

            // 5. 返回实验配置数据
            result.put("deviceConfig", deviceDataConfig);
            result.put("experiments", responseBodyObj.get("experiments"));

            log.info("成功获取设备实验数据，设备ID：{}，设备类型：{}，实验数量：{}",
                    request.getEquipmentId(), deviceTypeCode,
                    responseBodyObj.getJSONArray("experiments").size());

            return IResult.ok(result);
        } catch (Exception e) {
            log.error("获取设备实验数据失败", e);
            return IResult.fail("获取设备实验数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备实验测试数据
     */
    @Operation(summary = "获取设备实验测试数据")
    @PostMapping("/getExperimentTestData")
    public IResult<Map<String, Object>> getExperimentTestData(@RequestBody Map<String, Object> request) {
        log.info("获取设备实验测试数据，请求参数：{}", request);

        try {
            Long equipmentId = Long.valueOf(request.get("equipmentId").toString());
            String experimentName = request.get("experimentName").toString();
            // 提取实验名称主体部分，提升健壮性
            if (experimentName != null && experimentName.contains("-")) {
                experimentName = experimentName.split("-")[0];
                experimentName = "JC柜";
            }
            String tableName = request.get("tableName") != null ? request.get("tableName").toString() : null;

            // 调用设备集成服务获取实验数据
            Map<String, String> testData = deviceIntegrationService.getExperimentTestData(equipmentId, experimentName,
                    tableName);

            Map<String, Object> result = new HashMap<>();
            result.put("testData", testData);

            return IResult.ok(result);
        } catch (Exception e) {
            log.error("获取设备实验测试数据失败", e);
            return IResult.fail("获取设备实验测试数据失败：" + e.getMessage());
        }
    }
}