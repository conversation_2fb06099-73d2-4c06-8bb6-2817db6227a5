package com.westcatr.rd.testbusiness.business.org.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.org.entity.OrgUserInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.dto.UserInfoNoPhotoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgUserInfoQuery;
import com.westcatr.rd.testbusiness.business.org.pojo.vo.UserDepInfoVO;

/**
 * <p>
 * 人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-04
 */
public interface OrgUserInfoService extends IService<OrgUserInfo> {

    IPage<OrgUserInfo> entityPage(OrgUserInfoQuery query);

    OrgUserInfo getEntityById(Long id);

    boolean saveEntity(OrgUserInfo param);

    boolean updateEntity(OrgUserInfo param);

    boolean removeEntityById(Long id);

    List<OrgUserInfo> listByRoleId(Long roleId);

    List<OrgUserInfo> listByDeptId(Long deptId);

    List<OrgUserInfo> listByIds(List<Long> ids);

    List<OrgUserInfo> userInfoNoPhoto(UserInfoNoPhotoQuery userInfoNoPhotoQuery);

    boolean updatePassword(Long userId, String password, String token);

    List<OrgUserInfo> getSameDepUserInfo(Long longId);

    Map<String, List<UserDepInfoVO>> getUserInfoByRole(Long longId);

    OrgUserInfo getEntityByEmpId(String empId);

    List<OrgUserInfo> getEntityWithNoEmpId();
}
