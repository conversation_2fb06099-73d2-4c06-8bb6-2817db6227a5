package com.westcatr.rd.testbusiness.business.mqtt.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.testbusiness.business.mqtt.pojo.query.DeviceIntegrationConfigItemQuery;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfigItem;
import com.westcatr.rd.testbusiness.business.mqtt.mapper.DeviceIntegrationConfigItemMapper;
import com.westcatr.rd.testbusiness.business.mqtt.service.DeviceIntegrationConfigItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备对接规范条目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class DeviceIntegrationConfigItemServiceImpl extends ServiceImpl<DeviceIntegrationConfigItemMapper, DeviceIntegrationConfigItem> implements DeviceIntegrationConfigItemService {

    @Override
    public IPage<DeviceIntegrationConfigItem> entityPage(DeviceIntegrationConfigItemQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<DeviceIntegrationConfigItem>().create(query));
    }

    @Override
    public DeviceIntegrationConfigItem getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(DeviceIntegrationConfigItem param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(DeviceIntegrationConfigItem param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

