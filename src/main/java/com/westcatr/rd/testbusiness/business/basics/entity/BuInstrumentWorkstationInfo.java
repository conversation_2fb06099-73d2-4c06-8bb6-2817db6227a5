package com.westcatr.rd.testbusiness.business.basics.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 工位信息管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("bu_instrument_workstation_info")
@Schema(description="工位信息管理")
public class BuInstrumentWorkstationInfo extends Model<BuInstrumentWorkstationInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "工位描述")
    @Length(max = 512, message = "工位描述长度不能超过512", groups = {Insert.class, Update.class})
    @TableField("description")
    private String description;

    @Schema(description = "主键")
    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "工位位置")
    @Length(max = 255, message = "工位位置长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("location")
    private String location;

    @Schema(description = "负责人")
    @Length(max = 255, message = "负责人长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("responsible_person")
    private String responsiblePerson;

    @Schema(description = "工位状态(空闲/使用中/维护中)")
    @Length(max = 50, message = "工位状态(空闲/使用中/维护中)长度不能超过50", groups = {Insert.class, Update.class})
    @TableField("status")
    private String status;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "工位编号")
    @NotBlank(message = "工位编号不能为空", groups = {Insert.class, Update.class})
    @Length(max = 255, message = "工位编号长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("workstation_code")
    private String workstationCode;

    @Schema(description = "工位名称")
    @NotBlank(message = "工位名称不能为空", groups = {Insert.class, Update.class})
    @Length(max = 255, message = "工位名称长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("workstation_name")
    private String workstationName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
