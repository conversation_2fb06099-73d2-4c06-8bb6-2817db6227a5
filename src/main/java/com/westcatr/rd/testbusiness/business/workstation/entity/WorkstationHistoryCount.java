package com.westcatr.rd.testbusiness.business.workstation.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 工作台-历史数据统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("workstation_history_count")
@Schema(description="工作台-历史数据统计")
public class WorkstationHistoryCount extends Model<WorkstationHistoryCount> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "样品/任务/工单")
    @Length(max = 255, message = "任务/工单长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("type")
    private String type;

    @Schema(description = "对应状态")
    @Length(max = 255, message = "对应状态长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("status")
    private String status;

    @Schema(description = "对应编号")
    @Length(max = 255, message = "对应编号长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("number")
    private String number;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
