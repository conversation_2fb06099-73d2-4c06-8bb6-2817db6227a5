package com.westcatr.rd.testbusiness.business.mqtt.service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicInstrumentInfo;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicInstrumentInfoService;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 统一设备对接服务
 * 负责处理所有设备类型的统一对接逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class UnifiedDeviceIntegrationService {

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private DeviceIntegrationConfigService deviceIntegrationConfigService;

    @Autowired
    private StandardBasicInstrumentInfoService standardBasicInstrumentInfoService;

    @Autowired
    private MqttClientService mqttClientService;

    // 设备配置缓存
    private final Map<String, DeviceIntegrationConfig> configCache = new ConcurrentHashMap<>();

    /**
     * 🚀 获取设备测试数据（统一入口）
     * 
     * @param equipmentId 设备ID
     * @param parameterIds 参数ID列表
     * @return 测试结果列表
     */
    public List<AutoResultDto> getDeviceTestData(Long equipmentId, List<Long> parameterIds) {
        log.info("🚀 统一设备对接服务 - 开始获取设备测试数据，设备ID: {}, 参数数量: {}", equipmentId, parameterIds.size());
        
        try {
            // 🔍 获取设备信息
            InstrumentNewInfo deviceInfo = instrumentNewInfoService.getById(equipmentId);
            if (deviceInfo == null) {
                log.error("❌ 设备不存在: {}", equipmentId);
                return generateDefaultResults(parameterIds);
            }

            // 🔧 获取设备对接配置
            DeviceIntegrationConfig config = getDeviceConfig(deviceInfo);
            if (config == null) {
                log.warn("⚠️ 未找到设备对接配置，设备: {} ({})", deviceInfo.getSbmc(), deviceInfo.getDeviceTypeCode());
                return generateDefaultResults(parameterIds);
            }

            log.info("📋 使用设备配置: {} - {}", config.getDeviceName(), config.getDeviceType());

            // 🛠️ 准备设备参数
            Map<String, Object> deviceParams = prepareDeviceParams(deviceInfo, config);

            // 📡 发送设备请求
            Map<String, Object> response = mqttClientService.sendDeviceRequest(
                    deviceInfo.getDeviceTypeCode(), deviceParams);

            if (response != null) {
                log.info("📨 收到设备响应，开始解析数据");
                return parseDeviceResponse(response, config, parameterIds);
            } else {
                log.warn("⚠️ 设备响应为空，返回默认结果");
                return generateDefaultResults(parameterIds);
            }

        } catch (Exception e) {
            log.error("❌ 获取设备测试数据失败: {}", e.getMessage(), e);
            return generateDefaultResults(parameterIds);
        }
    }

    /**
     * 🔍 获取设备对接配置
     */
    private DeviceIntegrationConfig getDeviceConfig(InstrumentNewInfo deviceInfo) {
        String deviceType = deviceInfo.getDeviceTypeCode();
        
        // 优先使用设备指定的配置ID
        if (deviceInfo.getDeviceIntegrationConfigId() != null) {
            DeviceIntegrationConfig config = deviceIntegrationConfigService.getById(
                    deviceInfo.getDeviceIntegrationConfigId());
            if (config != null && "enabled".equals(config.getStatus())) {
                log.info("🎯 使用设备指定的配置ID: {}", config.getId());
                return config;
            }
        }

        // 从缓存中获取配置
        if (configCache.containsKey(deviceType)) {
            return configCache.get(deviceType);
        }

        // 查询数据库获取配置
        List<DeviceIntegrationConfig> configs = deviceIntegrationConfigService.list(
                new LambdaQueryWrapper<DeviceIntegrationConfig>()
                        .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                        .eq(DeviceIntegrationConfig::getStatus, "enabled")
                        .orderByDesc(DeviceIntegrationConfig::getUpdateTime));

        if (CollUtil.isNotEmpty(configs)) {
            DeviceIntegrationConfig config = configs.get(0);
            configCache.put(deviceType, config);
            log.info("📋 缓存设备配置: {} -> {}", deviceType, config.getDeviceName());
            return config;
        }

        return null;
    }

    /**
     * 🛠️ 准备设备参数
     */
    private Map<String, Object> prepareDeviceParams(InstrumentNewInfo deviceInfo, DeviceIntegrationConfig config) {
        Map<String, Object> params = new HashMap<>();

        // 解析设备表中的自定义参数
        if (StrUtil.isNotBlank(deviceInfo.getDeviceParams())) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> customParams = JSONUtil.toBean(deviceInfo.getDeviceParams(), HashMap.class);
                params.putAll(customParams);
                log.info("🔧 加载设备自定义参数: {}", customParams.keySet());
            } catch (Exception e) {
                log.error("❌ 解析设备自定义参数失败: {}", e.getMessage(), e);
            }
        }

        // 根据设备类型添加特殊参数
        addDeviceTypeSpecificParams(params, deviceInfo.getDeviceTypeCode());

        log.info("🛠️ 设备参数准备完成，参数数量: {}", params.size());
        return params;
    }

    /**
     * 🔧 根据设备类型添加特殊参数
     */
    private void addDeviceTypeSpecificParams(Map<String, Object> params, String deviceType) {
        if ("变压器".equals(deviceType)) {
            // 为变压器设备添加时间参数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String today = sdf.format(new Date());
            String yesterday = sdf.format(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
            
            if (!params.containsKey("tableName")) {
                params.put("tableName", "DTS_T_RZZLDZCL");
            }
            if (!params.containsKey("resultNum")) {
                params.put("resultNum", "10");
            }
            if (!params.containsKey("beginTime")) {
                params.put("beginTime", yesterday);
            }
            if (!params.containsKey("endTime")) {
                params.put("endTime", today);
            }
            
            log.info("🔧 变压器设备添加时间参数: {} ~ {}", yesterday, today);
        }
    }

    /**
     * 🔄 解析设备响应数据
     */
    private List<AutoResultDto> parseDeviceResponse(Map<String, Object> response, DeviceIntegrationConfig config, 
                                                   List<Long> parameterIds) {
        List<AutoResultDto> results = new ArrayList<>();
        
        try {
            // 获取参数信息
            List<StandardBasicInstrumentInfo> parameters = standardBasicInstrumentInfoService.listByIds(parameterIds);
            Map<Long, StandardBasicInstrumentInfo> parameterMap = parameters.stream()
                    .collect(Collectors.toMap(StandardBasicInstrumentInfo::getId, p -> p));

            // 解析响应数据
            if (response.containsKey("body")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> body = (Map<String, Object>) response.get("body");
                
                if (body.containsKey("outputParams")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");
                    
                    if (outputParams.containsKey("body")) {
                        String responseData = String.valueOf(outputParams.get("body"));
                        log.info("📊 设备响应数据: {}", responseData);
                        
                        // 根据设备类型选择解析策略
                        if ("变压器".equals(config.getDeviceType())) {
                            results = parseTransformerData(responseData, config, parameterIds, parameterMap);
                        } else {
                            results = parseGeneralDeviceData(responseData, config, parameterIds, parameterMap);
                        }
                    }
                }
            }
            
            if (results.isEmpty()) {
                log.warn("⚠️ 未解析出有效数据，生成默认结果");
                results = generateDefaultResults(parameterIds);
            }
            
        } catch (Exception e) {
            log.error("❌ 解析设备响应失败: {}", e.getMessage(), e);
            results = generateDefaultResults(parameterIds);
        }
        
        return results;
    }

    /**
     * 🔧 解析变压器数据
     */
    private List<AutoResultDto> parseTransformerData(String responseData, DeviceIntegrationConfig config,
                                                    List<Long> parameterIds, Map<Long, StandardBasicInstrumentInfo> parameterMap) {
        List<AutoResultDto> results = new ArrayList<>();
        
        try {
            // 解析参数映射配置
            if (StrUtil.isNotBlank(config.getParamMapping())) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramMapping = JSONUtil.toBean(config.getParamMapping(), HashMap.class);
                
                if (paramMapping.containsKey("experiments")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> experiments = (List<Map<String, Object>>) paramMapping.get("experiments");
                    
                    // 解析响应数据
                    List<Map<String, Object>> dataList = parseResponseDataToList(responseData);
                    
                    // 为每个参数匹配数据
                    for (Long parameterId : parameterIds) {
                        StandardBasicInstrumentInfo parameter = parameterMap.get(parameterId);
                        if (parameter != null) {
                            String value = findParameterValue(parameter.getProjectName(), experiments, dataList);
                            
                            AutoResultDto result = new AutoResultDto();
                            result.setId(parameterId);
                            result.setResult(value != null ? value : generateRandomValue());
                            results.add(result);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("❌ 解析变压器数据失败: {}", e.getMessage(), e);
        }
        
        return results;
    }

    /**
     * 🔧 解析通用设备数据
     */
    private List<AutoResultDto> parseGeneralDeviceData(String responseData, DeviceIntegrationConfig config,
                                                      List<Long> parameterIds, Map<Long, StandardBasicInstrumentInfo> parameterMap) {
        List<AutoResultDto> results = new ArrayList<>();
        
        // 为每个参数生成结果
        for (Long parameterId : parameterIds) {
            AutoResultDto result = new AutoResultDto();
            result.setId(parameterId);
            result.setResult(generateRandomValue());
            results.add(result);
        }
        
        log.info("🔧 通用设备数据解析完成，生成 {} 个结果", results.size());
        return results;
    }

    /**
     * 🔍 查找参数值
     */
    private String findParameterValue(String parameterName, List<Map<String, Object>> experiments, 
                                     List<Map<String, Object>> dataList) {
        try {
            for (Map<String, Object> experiment : experiments) {
                if (experiment.containsKey("mapping")) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> mapping = (Map<String, String>) experiment.get("mapping");
                    
                    if (mapping.containsKey(parameterName)) {
                        String fieldName = mapping.get(parameterName);
                        
                        for (Map<String, Object> dataItem : dataList) {
                            if (dataItem.containsKey(fieldName)) {
                                Object value = dataItem.get(fieldName);
                                if (value != null) {
                                    return String.valueOf(value);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("❌ 查找参数值失败: {}", e.getMessage(), e);
        }
        
        return null;
    }

    /**
     * 🔄 解析响应数据为列表
     */
    private List<Map<String, Object>> parseResponseDataToList(String responseData) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        try {
            if (responseData.startsWith("[")) {
                // 数组格式
                cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(responseData);
                for (int i = 0; i < jsonArray.size(); i++) {
                    cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
                    Map<String, Object> dataMap = new HashMap<>();
                    for (String key : jsonObject.keySet()) {
                        dataMap.put(key, jsonObject.get(key));
                    }
                    dataList.add(dataMap);
                }
            } else if (responseData.startsWith("{")) {
                // 对象格式
                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(responseData);
                Map<String, Object> dataMap = new HashMap<>();
                for (String key : jsonObject.keySet()) {
                    dataMap.put(key, jsonObject.get(key));
                }
                dataList.add(dataMap);
            }
        } catch (Exception e) {
            log.error("❌ 解析响应数据格式失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }

    /**
     * 🎲 生成随机值
     */
    private String generateRandomValue() {
        double value = 1 + Math.random() * 99;
        return String.format("%.2f", value);
    }

    /**
     * 🔧 生成默认结果
     */
    private List<AutoResultDto> generateDefaultResults(List<Long> parameterIds) {
        List<AutoResultDto> results = new ArrayList<>();
        
        for (Long parameterId : parameterIds) {
            AutoResultDto result = new AutoResultDto();
            result.setId(parameterId);
            result.setResult(generateRandomValue());
            results.add(result);
        }
        
        log.info("🔧 生成默认结果，数量: {}", results.size());
        return results;
    }

    /**
     * 🔄 刷新配置缓存
     */
    public void refreshConfigCache(String deviceType) {
        if (StrUtil.isNotBlank(deviceType)) {
            configCache.remove(deviceType);
            log.info("🔄 已清除设备类型 {} 的配置缓存", deviceType);
        } else {
            configCache.clear();
            log.info("🔄 已清除所有设备配置缓存");
        }
    }
}
