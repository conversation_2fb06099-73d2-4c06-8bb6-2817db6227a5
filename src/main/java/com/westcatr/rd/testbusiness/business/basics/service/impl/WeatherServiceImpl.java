package com.westcatr.rd.testbusiness.business.basics.service.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.westcatr.rd.testbusiness.business.basics.pojo.vo.WeatherInfoVO;
import com.westcatr.rd.testbusiness.business.basics.service.WeatherService;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 天气服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Slf4j
@Service
public class WeatherServiceImpl implements WeatherService {

    @Value("${weather.amap.url}")
    private String weatherApiUrl;

    @Value("${weather.amap.key}")
    private String apiKey;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public WeatherInfoVO getWeatherInfo(String cityName) {
        WeatherInfoVO weatherInfo = new WeatherInfoVO();
        weatherInfo.setDateTime(new Date());
        weatherInfo.setLocation(cityName);

        try {
            // 使用Hutool的HttpUtil构建请求URL并发送请求
            String requestUrl = StrUtil.format("{}?city={}&key={}&extensions=base", weatherApiUrl, cityName, apiKey);
            String responseContent = HttpUtil.get(requestUrl);
            log.info("天气API响应: {}", responseContent);

            // 使用Jackson解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(responseContent);

            if ("1".equals(jsonNode.path("status").asText()) && jsonNode.has("lives")
                    && jsonNode.path("lives").isArray() && jsonNode.path("lives").size() > 0) {
                JsonNode weatherData = jsonNode.path("lives").get(0);

                weatherInfo.setTemperature(weatherData.path("temperature").asText() + "°C");
                weatherInfo.setHumidity(weatherData.path("humidity").asText() + "%");
                weatherInfo.setWeather(weatherData.path("weather").asText());
                weatherInfo.setWindDirection(weatherData.path("winddirection").asText());
                weatherInfo.setWindPower(weatherData.path("windpower").asText() + "级");
                // 由于高德API没有提供大气压数据，设置为未知
                weatherInfo.setPressure("未知");
            } else {
                log.error("获取天气数据失败: {}", responseContent);
                weatherInfo.setTemperature("获取失败");
                weatherInfo.setHumidity("获取失败");
                weatherInfo.setWeather("未知");
                weatherInfo.setPressure("未知");
                weatherInfo.setWindDirection("未知");
                weatherInfo.setWindPower("未知");
            }
        } catch (Exception e) {
            log.error("调用天气API异常", e);
            weatherInfo.setTemperature("获取失败");
            weatherInfo.setHumidity("获取失败");
            weatherInfo.setWeather("未知");
            weatherInfo.setPressure("未知");
            weatherInfo.setWindDirection("未知");
            weatherInfo.setWindPower("未知");
        }

        return weatherInfo;
    }
}