package com.westcatr.rd.testbusiness.business.org.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.org.entity.OrgDeptInfo;
import com.westcatr.rd.testbusiness.business.org.pojo.query.OrgDeptInfoQuery;

/**
 * <p>
 * 部门信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
public interface OrgDeptInfoService extends IService<OrgDeptInfo> {

    IPage<OrgDeptInfo> entityPage(OrgDeptInfoQuery query);

    OrgDeptInfo getEntityById(Long id);

    boolean saveEntity(OrgDeptInfo param);

    boolean updateEntity(OrgDeptInfo param);

    boolean removeEntityById(Long id);

    List<OrgDeptInfo> tree();

    List<OrgDeptInfo> tree(List<Long> ids);
}
