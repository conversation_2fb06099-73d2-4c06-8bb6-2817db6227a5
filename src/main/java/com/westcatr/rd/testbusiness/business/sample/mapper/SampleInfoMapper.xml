<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.sample.mapper.SampleInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.sample.entity.SampleInfo">
        <id column="id" property="id" />
        <result column="sample_number" property="sampleNumber" />
        <result column="task_code" property="taskCode" />
        <result column="sample_name" property="sampleName" />
        <result column="sample_model" property="sampleModel" />
        <result column="sample_status" property="sampleStatus" />
        <result column="sample_serial_number" property="sampleSerialNumber" />
        <result column="sample_version_number" property="sampleVersionNumber" />
        <result column="sample_type" property="sampleType" />
        <result column="sample_num" property="sampleNum" />
        <result column="sample_process_status" property="sampleProcessStatus" />
        <result column="sample_remark" property="sampleRemark" />
        <result column="status_info" property="statusInfo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sample_number, task_code, sample_name, sample_model, sample_status, sample_serial_number, sample_version_number, sample_type, sample_num, sample_process_status, sample_remark, status_info, create_time, update_time
    </sql>

</mapper>
