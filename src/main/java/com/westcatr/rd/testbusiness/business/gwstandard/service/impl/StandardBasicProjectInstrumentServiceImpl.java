package com.westcatr.rd.testbusiness.business.gwstandard.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.JoinInfoService;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicModelParam;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectInstrument;
import com.westcatr.rd.testbusiness.business.gwstandard.entity.StandardBasicProjectParam;
import com.westcatr.rd.testbusiness.business.gwstandard.mapper.StandardBasicProjectInstrumentMapper;
import com.westcatr.rd.testbusiness.business.gwstandard.pojo.query.StandardBasicProjectInstrumentQuery;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicModelParamService;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectInstrumentService;
import com.westcatr.rd.testbusiness.business.gwstandard.service.StandardBasicProjectParamService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 标准—实验项目检测仪器信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class StandardBasicProjectInstrumentServiceImpl
        extends ServiceImpl<StandardBasicProjectInstrumentMapper, StandardBasicProjectInstrument>
        implements StandardBasicProjectInstrumentService {

    @Autowired
    private StandardBasicProjectParamService standardBasicProjectParamService;

    // 设备服务
    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private JoinInfoService joinInfoService;

    @Autowired
    private StandardBasicModelParamService standardBasicModelParamService;

    @Override
    public IPage<StandardBasicProjectInstrument> entityPage(StandardBasicProjectInstrumentQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<StandardBasicProjectInstrument>().create(query));
    }

    @Override
    public StandardBasicProjectInstrument getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEntity(StandardBasicProjectInstrument param) {
        // 实验项目不重名校验
        List<StandardBasicProjectInstrument> collect = this.list().stream()
                .filter(x -> param.getProjectName().equals(x.getProjectName())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            throw new IRuntimeException("此实验项目已存在");
        }
        IUser iUser = AuthUtil.getUser();
        param.setCreateUserId(iUser.getId());
        param.setCreateUserFullName(iUser.getFullName());
        param.setUpdateUserId(iUser.getId());
        param.setUpdateUserFullName(iUser.getFullName());
        if (this.save(param)) {
            // 获取设备
            InstrumentNewInfo instrumentNewInfo = instrumentNewInfoService.getById(param.getInstrumentId());
            if (ObjectUtil.isNotNull(instrumentNewInfo)) {
                joinInfoService.add(57, param.getInstrumentId(), param.getId());
            }
            if (CollUtil.isNotEmpty(param.getProjectParams())) {
                param.getProjectParams().forEach(x -> {
                    x.setStandardBasicProjectId(param.getId());
                });
                standardBasicProjectParamService.saveBatch(param.getProjectParams());
            }
        }
        return true;
    }

    @Override
    public boolean updateEntity(StandardBasicProjectInstrument param) {
        IUser iUser = AuthUtil.getUser();
        param.setUpdateUserId(iUser.getId());
        param.setUpdateUserFullName(iUser.getFullName());
        if (this.updateById(param)) {
            if (param.getInstrumentId() != null) {
                joinInfoService.update(57, param.getInstrumentId(), param.getId());
            }
            if (CollUtil.isNotEmpty(param.getProjectParams())) {
                param.getProjectParams().forEach(x -> {
                    x.setStandardBasicProjectId(param.getId());
                });
                // 查询所有子数据 ，如果id没有的就删除
                List<Long> collect = standardBasicProjectParamService.list(new LambdaQueryWrapper<>(
                        StandardBasicProjectParam.class).eq(StandardBasicProjectParam::getStandardBasicProjectId,
                                param.getId()))
                        .stream().map(StandardBasicProjectParam::getId)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    collect.removeAll(param.getProjectParams().stream().map(StandardBasicProjectParam::getId)
                            .collect(Collectors.toList()));
                    if (standardBasicProjectParamService.removeByIds(collect)) {
                        standardBasicModelParamService.remove(new LambdaQueryWrapper<>(StandardBasicModelParam.class)
                                .in(StandardBasicModelParam::getStandardBasicProjectParamId, collect));
                    }
                }
                standardBasicProjectParamService.saveOrUpdateBatch(param.getProjectParams());
            }else {
                //如果编辑时候清空了可检参数
                // 查询所有子数据
                List<Long> collect = standardBasicProjectParamService.list(new LambdaQueryWrapper<>(
                                StandardBasicProjectParam.class).eq(StandardBasicProjectParam::getStandardBasicProjectId,
                                param.getId()))
                        .stream().map(StandardBasicProjectParam::getId)
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(collect)) {
                    if (standardBasicProjectParamService.removeByIds(collect)) {
                        standardBasicModelParamService.remove(new LambdaQueryWrapper<>(StandardBasicModelParam.class)
                                .in(StandardBasicModelParam::getStandardBasicProjectParamId, collect));
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean removeEntityById(Long id) {
        if (this.removeById(id)) {
            standardBasicProjectParamService.remove(new LambdaQueryWrapper<>(StandardBasicProjectParam.class)
                    .eq(StandardBasicProjectParam::getStandardBasicProjectId, id));
        }
        return true;
    }
}
