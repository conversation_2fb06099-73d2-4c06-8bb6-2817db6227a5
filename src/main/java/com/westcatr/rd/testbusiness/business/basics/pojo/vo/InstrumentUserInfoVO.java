package com.westcatr.rd.testbusiness.business.basics.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentUserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 仪器设备—检测人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="仪器设备—检测人员表VO对象")
public class InstrumentUserInfoVO extends InstrumentUserInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
