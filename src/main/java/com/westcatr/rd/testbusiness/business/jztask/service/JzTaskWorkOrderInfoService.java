package com.westcatr.rd.testbusiness.business.jztask.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskWorkOrderInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskWorkOrderInfoVO;

/**
 * <p>
 * 荆州—工单列表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface JzTaskWorkOrderInfoService extends IService<JzTaskWorkOrderInfo> {

    IPage<JzTaskWorkOrderInfo> entityPage(JzTaskWorkOrderInfoQuery query);

    JzTaskWorkOrderInfo getEntityById(Long id);

    boolean saveEntity(JzTaskWorkOrderInfo param);

    boolean updateEntity(JzTaskWorkOrderInfo param);

    boolean removeEntityById(Long id);

    /**
     * 启动工单
     *
     * @param id
     * @return
     */
    boolean startWorkOrder(Long id);

    /**
     * 结束工单
     *
     * @param
     * @return
     */
    boolean endWorkOrder(JzTaskWorkOrderInfo param);

    /**
     * 从仪器仪表获取多个检测项目的结果
     *
     * @param ids
     * @return
     */
    List<AutoResultDto> autoResult(List<Long> ids);

    List<AutoResultDto> autoResultNew(List<Long> ids);

    /**
     * 获取荆州—工单列表表VO分页数据
     * 
     * @param query
     * @return
     */
    IPage<JzTaskWorkOrderInfoVO> myVoPage(JzTaskWorkOrderInfoQuery query);
}
