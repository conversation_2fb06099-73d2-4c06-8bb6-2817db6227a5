package com.westcatr.rd.testbusiness.business.gwstandard.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.file.entity.FileInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准—检测仪器标准信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("standard_basic_instrument_info")
@Schema(description = "标准—检测仪器标准信息表")
public class StandardBasicInstrumentInfo extends Model<StandardBasicInstrumentInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "集成设备id")
    @TableField("integrated_device_id")
    private Long integratedDeviceId;

    @Schema(description = "项目名称")
    @Length(max = 255, message = "项目名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("project_name")
    private String projectName;

    @Schema(description = "检测能力级别（A\\B\\C）")
    @Length(max = 255, message = "检测能力级别（A\\B\\C）长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("test_capability_level")
    private String testCapabilityLevel;

    @Schema(description = "设备关键参数和要求")
    @Length(max = 65535, message = "设备关键参数和要求长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("key_param_requirement")
    private String keyParamRequirement;

    @Schema(description = "标准要求")
    @Length(max = 65535, message = "标准要求长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("standard_requirement")
    private String standardRequirement;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    // sample_specifications
    @Schema(description = "样品规格")
    @Length(max = 255, message = "样品规格长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sample_specifications")
    private String sampleSpecifications;

    @Schema(description = "单位")
    @Length(max = 50, message = "单位长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("unit")
    private String unit;

    // 实验项目ids
    @TableField(exist = false)
    private List<Long> projectIds;

    // 样品名称
    @TableField(value = "sample_name")
    private String sampleName;

    // 样品型号
    @TableField(value = "sample_model")
    private String sampleModel;

    // 创建人的id
    @TableField("create_user_id")
    private Long createUserId;

    // 创建人的userFullName
    @TableField("create_user_full_name")
    private String createUserFullName;

    // 最后更新人id
    @TableField("update_user_id")
    private Long updateUserId;

    // 最后更新人的userFullName
    @TableField("update_user_full_name")
    private String updateUserFullName;

    // 规则模型
    @TableField(exist = false)
    private List<StandardBasicModelParam> standardBasicModelParams;

    // 报告模板文件ID
    @Schema(description = "报告模板文件ID")
    @TableField("report_template_file_id")
    private Long reportTemplateFileId;

    // 原始记录模板文件ID
    @Schema(description = "原始记录模板文件ID")
    @TableField("original_record_template_file_id")
    private Long originalRecordTemplateFileId;

    // 报告模板文件信息（非数据库字段）
    @TableField(exist = false)
    private FileInfo reportTemplateFileInfo;

    // 原始记录模板文件信息（非数据库字段）
    @TableField(exist = false)
    private FileInfo originalRecordTemplateFileInfo;

    // 报告模板自定义值
    @Schema(description = "报告模板自定义值")
    @TableField("report_template_values")
    private String reportTemplateValues;

    // 原始记录模板自定义值
    @Schema(description = "原始记录模板自定义值")
    @TableField("original_record_template_values")
    private String originalRecordTemplateValues;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
