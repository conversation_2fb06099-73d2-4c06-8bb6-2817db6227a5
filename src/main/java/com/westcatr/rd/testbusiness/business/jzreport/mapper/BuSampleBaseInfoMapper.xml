<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.jzreport.mapper.BuSampleBaseInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.jzreport.entity.BuSampleBaseInfo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="model" property="model" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, model, create_time, update_time
    </sql>

</mapper>
