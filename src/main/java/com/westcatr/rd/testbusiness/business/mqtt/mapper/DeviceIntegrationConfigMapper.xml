<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.testbusiness.business.mqtt.mapper.DeviceIntegrationConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig">
        <id column="id" property="id" />
        <result column="device_type" property="deviceType" />
        <result column="device_name" property="deviceName" />
        <result column="product_code" property="productCode" />
        <result column="device_code" property="deviceCode" />
        <result column="type_code" property="typeCode" />
        <result column="request_template" property="requestTemplate" />
        <result column="response_template" property="responseTemplate" />
        <result column="param_mapping" property="paramMapping" />
        <result column="status" property="status" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_type, device_name, product_code, device_code, type_code, request_template, response_template, param_mapping, status, description, create_time, update_time
    </sql>

</mapper>
