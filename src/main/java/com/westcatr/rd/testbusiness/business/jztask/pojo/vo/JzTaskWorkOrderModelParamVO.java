package com.westcatr.rd.testbusiness.business.jztask.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderModelParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 标准-模型参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="标准-模型参数表VO对象")
public class JzTaskWorkOrderModelParamVO extends JzTaskWorkOrderModelParam {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
