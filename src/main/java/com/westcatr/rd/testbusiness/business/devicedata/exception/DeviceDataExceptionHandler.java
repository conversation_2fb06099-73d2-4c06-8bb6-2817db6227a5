package com.westcatr.rd.testbusiness.business.devicedata.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备数据异常处理器
 *
 * <AUTHOR>
 * @since 2025-06-14
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.westcatr.rd.testbusiness.business.devicedata")
public class DeviceDataExceptionHandler {

    /**
     * 处理设备数据异常
     */
    @ExceptionHandler(DeviceDataException.class)
    public ResponseEntity<Map<String, Object>> handleDeviceDataException(DeviceDataException e) {
        log.error("❌ 设备数据异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", e.getMessage());
        result.put("data", null);
        
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<Map<String, Object>> handleValidationException(Exception e) {
        log.error("❌ 参数校验异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "参数校验失败: " + e.getMessage());
        result.put("data", null);
        
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Map<String, Object>> handleConstraintViolationException(ConstraintViolationException e) {
        log.error("❌ 约束违反异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "参数约束违反: " + e.getMessage());
        result.put("data", null);
        
        return ResponseEntity.badRequest().body(result);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        log.error("❌ 系统异常: {}", e.getMessage(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "系统异常: " + e.getMessage());
        result.put("data", null);
        
        return ResponseEntity.internalServerError().body(result);
    }
}
