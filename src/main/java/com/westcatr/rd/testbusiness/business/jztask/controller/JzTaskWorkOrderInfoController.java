package com.westcatr.rd.testbusiness.business.jztask.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskWorkOrderInfo;
import com.westcatr.rd.testbusiness.business.jztask.pojo.dto.AutoResultDto;
import com.westcatr.rd.testbusiness.business.jztask.pojo.query.JzTaskWorkOrderInfoQuery;
import com.westcatr.rd.testbusiness.business.jztask.pojo.vo.JzTaskWorkOrderInfoVO;
import com.westcatr.rd.testbusiness.business.jztask.service.JzTaskWorkOrderInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * JzTaskWorkOrderInfo 控制器
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Validated
@Tag(name = "荆州—工单列表表接口", description = "荆州—工单列表表接口")
@Slf4j
@RestController
public class JzTaskWorkOrderInfoController {

    @Autowired
    private JzTaskWorkOrderInfoService jzTaskWorkOrderInfoService;

    @Operation(summary = "获取荆州—工单列表表分页数据")
    @PostMapping("/jzTaskWorkOrderInfo/page")
    public IResult<IPage<JzTaskWorkOrderInfo>> getJzTaskWorkOrderInfoPage(@RequestBody JzTaskWorkOrderInfoQuery query) {
        return IResult.ok(jzTaskWorkOrderInfoService.entityPage(query));
    }

    @Operation(summary = "获取荆州—工单列表表数据")
    @PostMapping("/jzTaskWorkOrderInfo/get")
    public IResult<JzTaskWorkOrderInfo> getJzTaskWorkOrderInfoById(@RequestBody @Id ID id) {
        return IResult.ok(jzTaskWorkOrderInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增荆州—工单列表表数据")
    @PostMapping("/jzTaskWorkOrderInfo/add")
    public IResult addJzTaskWorkOrderInfo(@RequestBody @Validated(Insert.class) JzTaskWorkOrderInfo param) {
        return IResult.auto(jzTaskWorkOrderInfoService.saveEntity(param));
    }

    @Operation(summary = "更新荆州—工单列表表数据")
    @PostMapping("/jzTaskWorkOrderInfo/update")
    public IResult updateJzTaskWorkOrderInfoById(@RequestBody @Validated(Update.class) JzTaskWorkOrderInfo param) {
        return IResult.auto(jzTaskWorkOrderInfoService.updateEntity(param));
    }

    @Operation(summary = "删除荆州—工单列表表数据")
    @PostMapping("/jzTaskWorkOrderInfo/delete")
    public IResult deleteJzTaskWorkOrderInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            jzTaskWorkOrderInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取荆州—工单列表表VO分页数据")
    @PostMapping("/jzTaskWorkOrderInfo/voPage")
    public IResult<IPage<JzTaskWorkOrderInfoVO>> getJzTaskWorkOrderInfoVoPage(
            @RequestBody JzTaskWorkOrderInfoQuery query) {
        return IResult.ok(jzTaskWorkOrderInfoService.myVoPage(query));
    }

    @Operation(summary = "获取荆州—工单列表表VO数据")
    @PostMapping("/jzTaskWorkOrderInfo/getVo")
    public IResult<JzTaskWorkOrderInfoVO> getJzTaskWorkOrderInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<JzTaskWorkOrderInfoVO> associationQuery = new AssociationQuery<>(JzTaskWorkOrderInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "启动任务")
    @PostMapping("/jzTaskWorkOrderInfo/startWorkOrder")
    public IResult startWorkOrder(@RequestBody @Id ID id) {
        return IResult.auto(jzTaskWorkOrderInfoService.startWorkOrder(id.longId()));
    }

    @Operation(summary = "结束任务")
    @PostMapping("/jzTaskWorkOrderInfo/endWorkOrder")
    public IResult endWorkOrder(@RequestBody JzTaskWorkOrderInfo param) {
        return IResult.auto(jzTaskWorkOrderInfoService.endWorkOrder(param));
    }

    @Operation(summary = "获取设备检测数据")
    @PostMapping("/jzTaskWorkOrderInfo/autoResult")
    public IResult<List<AutoResultDto>> autoResult(@RequestBody List<Long> ids) {
        return IResult.ok(jzTaskWorkOrderInfoService.autoResultNew(ids));
    }
}
