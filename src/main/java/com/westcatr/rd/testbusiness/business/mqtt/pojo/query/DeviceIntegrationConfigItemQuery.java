package com.westcatr.rd.testbusiness.business.mqtt.pojo.query;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 设备对接规范条目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="设备对接规范条目表查询对象")
public class DeviceIntegrationConfigItemQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "试验项目配置ID")
    @QueryCondition
    private Long configId;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "描述")
    @QueryCondition
    private String description;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "条目编码")
    @QueryCondition
    private String itemCode;

    @Schema(description = "条目名称")
    @QueryCondition
    private String itemName;

    @Schema(description = "试验项目编码")
    @QueryCondition
    private String projectCode;

    @Schema(description = "试验项目名称")
    @QueryCondition
    private String projectName;

    @Schema(description = "状态(enabled/disabled)")
    @QueryCondition
    private String status;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;
}
