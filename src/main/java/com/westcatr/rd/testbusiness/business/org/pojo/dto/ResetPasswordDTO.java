package com.westcatr.rd.testbusiness.business.org.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020-06-09 19:59
 */
@Schema(description = "重置密码DTO")
@Data
public class ResetPasswordDTO extends RetrievePasswordDTO {

    @NotNull(message = "验证码不能为空")
    private String code;

    @NotBlank(message = "密码不能为空")
    private String password;
}
