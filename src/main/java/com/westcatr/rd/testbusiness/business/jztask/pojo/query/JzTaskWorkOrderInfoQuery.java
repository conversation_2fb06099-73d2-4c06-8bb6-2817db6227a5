package com.westcatr.rd.testbusiness.business.jztask.pojo.query;

import java.io.Serializable;
import java.util.Date;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—工单列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "荆州—工单列表表查询对象")
public class JzTaskWorkOrderInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "结束时间")
    @QueryCondition
    private Date endDate;

    @QueryCondition
    private Long id;

    @Schema(description = "检毕时间")
    @QueryCondition
    private Date inspectionCompletionTime;

    @Schema(description = "关联任务")
    @QueryCondition
    private Long sampleId;

    @Schema(description = "开始时间")
    @QueryCondition
    private Date startDate;

    @Schema(description = "关联样品")
    @QueryCondition
    private Long taskId;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "工单编号")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String workOrderNumber;

    @Schema(description = "实验项目")
    private String gwBzName;

    @Schema(description = "工单状态")
    @QueryCondition(condition = QueryCondition.Condition.AUTO_LIKE)
    private String statusInfo;

    @Schema(description = "测试人员id")
    @QueryCondition
    private Long testUserId;
}
