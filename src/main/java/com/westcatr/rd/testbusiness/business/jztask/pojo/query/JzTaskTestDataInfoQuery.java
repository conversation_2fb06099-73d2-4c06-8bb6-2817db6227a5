package com.westcatr.rd.testbusiness.business.jztask.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 检测任务测试数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="检测任务测试数据表查询对象")
public class JzTaskTestDataInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键 ID")
    @QueryCondition
    private Integer id;

    @Schema(description = "样品名称")
    @QueryCondition
    private String sampleName;

    @Schema(description = "模式")
    @QueryCondition
    private String mode;

    @Schema(description = "测试项目")
    @QueryCondition
    private String testProject;

    @Schema(description = "参数")
    @QueryCondition
    private String parameter;

    @Schema(description = "单位")
    @QueryCondition
    private String unit;

    @Schema(description = "数值")
    @QueryCondition
    private String value;

    @Schema(description = "反馈信息")
    @QueryCondition
    private String backInfo;
}
