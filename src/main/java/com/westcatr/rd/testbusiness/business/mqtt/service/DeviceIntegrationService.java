package com.westcatr.rd.testbusiness.business.mqtt.service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fast.jackson.JSONArray;
import com.fast.jackson.JSONObject;
import com.westcatr.rd.testbusiness.business.basics.entity.InstrumentNewInfo;
import com.westcatr.rd.testbusiness.business.basics.service.InstrumentNewInfoService;
import com.westcatr.rd.testbusiness.business.mqtt.entity.DeviceIntegrationConfig;
import com.westcatr.rd.testbusiness.business.mqtt.mapper.DeviceIntegrationConfigMapper;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备集成服务类
 * 用于处理设备字段映射和数据转换
 */
@Slf4j
@Service
public class DeviceIntegrationService {

    @Autowired
    private DeviceIntegrationConfigMapper deviceIntegrationConfigMapper;

    @Autowired
    private InstrumentNewInfoService instrumentNewInfoService;

    @Autowired
    private DeviceIntegrationConfigService deviceIntegrationConfigService;

    @Autowired
    private MqttClientService mqttClientService;

    /**
     * 根据设备ID获取设备配置
     *
     * @param equipmentId 设备ID
     * @return 设备配置
     */
    public DeviceIntegrationConfig getDeviceConfigById(Long equipmentId) {
        // 获取设备信息
        InstrumentNewInfo deviceInfo = instrumentNewInfoService.getById(equipmentId);
        if (deviceInfo == null) {
            log.error("设备不存在: {}", equipmentId);
            return null;
        }

        // 获取设备类型
        /*
         * String deviceType = deviceInfo.getDeviceTypeCode();
         * if (deviceType == null || deviceType.isEmpty()) {
         * log.error("设备类型为空: {}", equipmentId);
         * return null;
         * }
         */

        // 获取设备配置列表
        DeviceIntegrationConfig config = deviceIntegrationConfigService
                .getById(deviceInfo.getDeviceIntegrationConfigId());
        if (config == null) {
            log.error("没有找到设备类型[{}]的对接配置", deviceInfo.getSbmc());
            return null;
        }

        return config;
    }

    /**
     * 获取实验测试数据
     *
     * @param equipmentId    设备ID
     * @param experimentName 实验名称
     * @param tableName      表名
     * @return 测试数据
     */
    public Map<String, String> getExperimentTestData(Long equipmentId, String experimentName, String tableName) {
        // 获取设备信息
        InstrumentNewInfo deviceInfo = instrumentNewInfoService.getById(equipmentId);
        if (deviceInfo == null) {
            log.error("设备不存在: {}", equipmentId);
            return new HashMap<>();
        }

        // 获取设备类型
        String deviceType = deviceInfo.getDeviceTypeCode();
        if (deviceType == null || deviceType.isEmpty()) {
            log.error("设备类型为空: {}", equipmentId);
            return new HashMap<>();
        }

        // 解析设备参数
        Map<String, Object> params = new HashMap<>();
        if (deviceInfo.getDeviceParams() != null && !deviceInfo.getDeviceParams().isEmpty()) {
            try {
                params = JSONUtil.toBean(deviceInfo.getDeviceParams(), HashMap.class);
            } catch (Exception e) {
                log.error("解析设备参数失败: {}", e.getMessage(), e);
            }
        }

        // 添加当前时间作为开始和结束时间（针对万测变压器）
        if ("变压器".equals(deviceType)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String today = sdf.format(new Date());
            params.put("beginTime", today);
            params.put("endTime", today);

            // 时间默认往前推3年
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -365 * 3);
            String beginTime = sdf.format(calendar.getTime());
            params.put("beginTime", beginTime);

            // "resultNum": "1",
            params.put("resultNum", "1");
        }

        // 确定表名
        if (tableName == null || tableName.isEmpty()) {
            tableName = getTableNameByExperimentName(experimentName);
        }
        params.put("tableName", tableName);

        // 发送设备请求并获取响应
        Map<String, Object> response = mqttClientService.sendDeviceRequest(deviceType, params);
        if (response == null) {
            log.error("设备请求失败或响应为空");
            return new HashMap<>();
        }

        // 提取响应内容
        String content = null;

        // 由于在 MqttClientServiceImpl 中已经过滤了消息，这里只需要处理有效的数据响应
        if (response.containsKey("body") && response.get("body") instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> body = (Map<String, Object>) response.get("body");

            // 从body中获取outputParams
            if (body.containsKey("outputParams") && body.get("outputParams") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> outputParams = (Map<String, Object>) body.get("outputParams");

                if (outputParams.containsKey("body")) {
                    content = outputParams.get("body").toString();
                    log.info("✅ 从outputParams.body中提取到内容");
                }
            }
        } else if (response.containsKey("content")) {
            // 兼容旧格式，直接从content字段获取内容
            content = response.get("content").toString();
            log.info("✅ 从content字段直接提取到内容");
        }

        if (content == null || content.isEmpty()) {
            log.error("❌ 无法从设备响应中提取内容，原始响应: {}", response);
            return new HashMap<>();
        }

        // 转换为测试名称与值的映射
        return convertToTestNameValueMap(deviceType, content, tableName);
    }

    /**
     * 根据实验名称确定表名
     *
     * @param experimentName 实验名称
     * @return 表名
     */
    private String getTableNameByExperimentName(String experimentName) {
        if (experimentName == null || experimentName.isEmpty()) {
            return "DTS_T_RZZLDZCL"; // 默认返回直阻试验表
        }

        switch (experimentName) {
            case "直阻试验":
                return "DTS_T_RZZLDZCL";
            case "变比试验":
                return "DTS_T_RZDYBCLHLJZBHJD";
            case "空载试验":
                return "DTS_T_KZDLHKZSHCL2";
            case "负载试验":
                return "DTS_T_DLZKHFZSHCL";
            default:
                return "DTS_T_RZZLDZCL"; // 默认返回直阻试验表
        }
    }

    /**
     * 根据设备类型和测试项目名称获取对应的设备字段名
     *
     * @param deviceType 设备类型
     * @param testName   测试项目名称
     * @return 设备字段名
     */
    public String getDeviceFieldName(String deviceType, String testName) {
        DeviceIntegrationConfig config = getDeviceConfig(deviceType);
        if (config == null) {
            log.warn("未找到设备类型[{}]的配置", deviceType);
            return null;
        }

        try {
            JSONObject paramMapping = JSONObject.parseObject(config.getParamMapping());
            return paramMapping.getString(testName);
        } catch (Exception e) {
            log.error("解析设备[{}]的参数映射失败: {}", deviceType, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从设备返回数据中提取指定字段的值
     *
     * @param deviceType   设备类型
     * @param testName     测试项目名称
     * @param responseData 设备返回的数据
     * @return 字段值
     */
    public String extractValueFromResponse(String deviceType, String testName, String responseData) {
        if (responseData == null || responseData.isEmpty()) {
            return null;
        }

        String fieldName = getDeviceFieldName(deviceType, testName);
        if (fieldName == null) {
            return null;
        }

        try {
            JSONArray jsonArray = JSONArray.parseArray(responseData);
            if (jsonArray != null && !jsonArray.isEmpty()) {
                JSONObject dataObject = jsonArray.getJSONObject(0);
                if (dataObject.containsKey(fieldName)) {
                    return dataObject.getString(fieldName);
                }
            }
        } catch (Exception e) {
            log.error("从设备[{}]返回数据中提取[{}]字段值失败: {}", deviceType, fieldName, e.getMessage(), e);
        }

        return null;
    }

    /**
     * 将设备返回的数据转换为测试项目名称与值的映射
     *
     * @param deviceType   设备类型
     * @param responseData 设备返回的数据
     * @return 测试项目名称与值的映射
     */
    public Map<String, String> convertToTestNameValueMap(String deviceType, String responseData) {
        return convertToTestNameValueMap(deviceType, responseData, null);
    }

    /**
     * 将设备返回的数据转换为测试项目名称与值的映射
     *
     * @param deviceType   设备类型
     * @param responseData 设备返回的数据
     * @param tableName    表名，用于区分不同试验类型
     * @return 测试项目名称与值的映射
     */
    public Map<String, String> convertToTestNameValueMap(String deviceType, String responseData, String tableName) {
        Map<String, String> resultMap = new HashMap<>();
        if (responseData == null || responseData.isEmpty()) {
            log.warn("设备[{}]返回的响应数据为空", deviceType);
            // 即使没有响应数据，也需要返回参数映射信息
            DeviceIntegrationConfig config = null;
            if (tableName != null && !tableName.isEmpty()) {
                config = getDeviceConfigByTableName(deviceType, tableName);
            }

            if (config == null) {
                config = getDeviceConfig(deviceType);
            }

            if (config != null && config.getParamMapping() != null) {
                try {
                    JSONObject paramMapping = JSONObject.parseObject(config.getParamMapping());
                    for (Map.Entry<String, Object> entry : paramMapping.entrySet()) {
                        String testName = entry.getKey();
                        String fieldName = entry.getValue().toString();
                        // 添加参数信息，即使没有值
                        resultMap.put(testName, "");
                        log.debug("添加空值的参数映射: {} -> {}", fieldName, testName);
                    }
                } catch (Exception e) {
                    log.error("解析空响应时的参数映射失败: {}", e.getMessage(), e);
                }
            }
            return resultMap;
        }

        DeviceIntegrationConfig config = null;

        // 如果指定了表名，根据表名查找配置
        if (tableName != null && !tableName.isEmpty()) {
            log.debug("正在根据表名[{}]查找设备[{}]的配置", tableName, deviceType);
            config = getDeviceConfigByTableName(deviceType, tableName);
        }

        // 如果没有找到配置，则使用设备类型查找
        if (config == null) {
            log.debug("未根据表名找到配置，尝试仅使用设备类型[{}]查找", deviceType);
            config = getDeviceConfig(deviceType);
        }

        if (config == null) {
            log.warn("未找到设备类型[{}]表名[{}]的配置，将尝试动态映射", deviceType, tableName);
            // 即使没有配置，也尝试动态映射
            if ("变压器".equals(deviceType)) {
                log.info("设备类型为变压器，尝试直接使用动态映射");
                return performDynamicMapping(responseData);
            }
            return resultMap;
        }

        log.info("✅ 找到设备[{}]表名[{}]的配置: ID={}, typeCode={}", deviceType, tableName, config.getId(), config.getTypeCode());

        try {
            // 输出原始参数映射字符串，检查是否有格式问题
            log.debug("原始参数映射字符串: [{}]", config.getParamMapping());

            // 尝试使用标准的JSON解析
            JSONObject paramMapping = JSONObject.parseObject(config.getParamMapping());
            log.debug("参数映射配置(键值数量: {}): {}", paramMapping.size(), paramMapping);

            // 首先将所有映射参数添加到结果集，即使没有值也添加
            for (Map.Entry<String, Object> entry : paramMapping.entrySet()) {
                String testName = entry.getKey();
                String fieldName = entry.getValue().toString();
                // 默认添加空值
                resultMap.put(testName, "");
                log.debug("添加默认空值参数映射: {} -> {}", fieldName, testName);
            }

            // 同时尝试手动解析JSON映射，以应对可能的JSON解析问题
            Map<String, String> manualMapping = manuallyParseJsonMapping(config.getParamMapping());
            log.debug("手动解析的参数映射(键值数量: {}): {}", manualMapping.size());

            JSONArray jsonArray = JSONArray.parseArray(responseData);

            if (jsonArray != null && !jsonArray.isEmpty()) {
                JSONObject dataObject = jsonArray.getJSONObject(0);
                log.debug("设备返回的第一条数据包含字段数量: {}", dataObject.size());

                // 记录所有设备返回的字段，以供参考
                StringBuilder fieldNames = new StringBuilder();
                for (String field : dataObject.keySet()) {
                    fieldNames.append(field).append("=").append(dataObject.get(field)).append(", ");
                }
                log.debug("设备返回的所有字段: {}", fieldNames.toString());

                // 先使用标准JSON解析的结果
                for (Map.Entry<String, Object> entry : paramMapping.entrySet()) {
                    String testName = entry.getKey();
                    String fieldName = entry.getValue().toString();

                    log.debug("检查映射: 测试项[{}] -> 设备字段[{}]", testName, fieldName);

                    if (dataObject.containsKey(fieldName)) {
                        String fieldValue = dataObject.getString(fieldName);
                        resultMap.put(testName, fieldValue);
                        log.debug("成功映射字段: {} -> {}, 值: {}", fieldName, testName, fieldValue);
                    } else {
                        // 字段不存在时保留默认空值
                        log.debug("设备返回数据中不包含字段: {}, 保留默认空值", fieldName);
                    }
                }

                // 检查是否需要使用动态映射（当配置的映射都是空值时）
                boolean needDynamicMapping = "变压器".equals(deviceType);
                if (needDynamicMapping) {
                    // 检查现有结果是否都是空值
                    boolean allEmpty = resultMap.values().stream().allMatch(v -> v == null || v.trim().isEmpty());
                    if (allEmpty) {
                        log.info("检测到参数映射配置存在但值都为空，启用动态映射");
                    } else {
                        needDynamicMapping = false;
                        log.debug("参数映射配置有效，跳过动态映射");
                    }
                }
                
                if (needDynamicMapping) {
                    log.info("启用动态映射，清空之前的空值结果");
                    
                    // 清空现有的空值结果，准备重新填充
                    resultMap.clear();

                    // 动态构建映射关系：通过responseData中的code->name映射来进行反向查找
                    Map<String, String> codeToNameMap = buildCodeToNameMapping(responseData);

                    if (!codeToNameMap.isEmpty()) {
                        log.info("构建了{}个code到name的映射关系", codeToNameMap.size());

                        // 使用构建的映射关系进行数据映射
                        // 注意：这里的dataObject是外层对象，需要从result数组中提取实际的测试数据
                        log.info("开始从数据中提取值进行映射，数据对象的键: {}", dataObject.keySet());
                        
                        // 遍历所有试验类型，从result中提取数据
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject testItem = jsonArray.getJSONObject(i);
                            JSONArray resultArray = testItem.getJSONArray("result");
                            String testName = testItem.getString("name");
                            
                            if (resultArray != null) {
                                log.info("正在处理试验[{}]的{}个测试项数据", testName, resultArray.size());
                                
                                for (int j = 0; j < resultArray.size(); j++) {
                                    JSONObject resultItem = resultArray.getJSONObject(j);
                                    String code = resultItem.getString("code");
                                    String value = resultItem.getString("value");
                                    
                                    if (code != null && value != null && !value.equals("0") && !value.equals("0E-7")) {
                                        // 优先使用映射表中的name
                                        String mappedName = codeToNameMap.get(code);
                                        if (mappedName != null) {
                                            resultMap.put(mappedName, value);
                                            log.info("✅ 动态映射: {} -> {}, 值: {}", code, mappedName, value);
                                        } else {
                                            // 如果是中文字段名，直接使用
                                            if (code.contains("高压") || code.contains("低压") || code.equals("试验温度")) {
                                                resultMap.put(code, value);
                                                log.info("✅ 直接映射中文字段: {} -> {}, 值: {}", code, code, value);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        log.info("动态映射完成，成功映射{}个字段", resultMap.size());
                    } else {
                        log.warn("无法构建code到name的映射关系，使用原始数据");
                        // 降级处理：直接映射中文字段名
                        for (String key : dataObject.keySet()) {
                            String value = dataObject.getString(key);
                            if (value != null && !value.equals("0") && !value.equals("0E-7")) {
                                if (key.contains("高压") || key.contains("低压") || key.equals("试验温度")) {
                                    resultMap.put(key, value);
                                    log.info("降级映射中文字段: {} -> {}, 值: {}", key, key, value);
                                }
                            }
                        }
                    }
                }
            } else {
                log.warn("设备[{}]返回的JSON数组为空", deviceType);
            }
        } catch (Exception e) {
            log.error("转换设备[{}]返回数据失败: {}", deviceType, e.getMessage(), e);
        }

        log.debug("设备[{}]表名[{}]的测试项目值映射结果: {}", deviceType, tableName, resultMap);
        return resultMap;
    }

    /**
     * 获取设备配置
     *
     * @param deviceType 设备类型
     * @return 设备配置
     */
    private DeviceIntegrationConfig getDeviceConfig(String deviceType) {
        return deviceIntegrationConfigMapper.selectOne(
                new LambdaQueryWrapper<DeviceIntegrationConfig>()
                        .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                        .eq(DeviceIntegrationConfig::getStatus, "enabled")
                        .last("LIMIT 1"));
    }

    /**
     * 根据设备类型和表名获取设备配置
     *
     * @param deviceType 设备类型
     * @param tableName  表名
     * @return 设备配置
     */
    private DeviceIntegrationConfig getDeviceConfigByTableName(String deviceType, String tableName) {
        // 查找表名对应的配置
        List<DeviceIntegrationConfig> configs = deviceIntegrationConfigMapper.selectList(
                new LambdaQueryWrapper<DeviceIntegrationConfig>()
                        .eq(DeviceIntegrationConfig::getDeviceType, deviceType)
                        .eq(DeviceIntegrationConfig::getStatus, "enabled"));

        if (configs == null || configs.isEmpty()) {
            log.warn("未找到设备类型[{}]的任何配置", deviceType);
            return null;
        }

        // 打印所有找到的配置信息
        log.debug("找到设备类型[{}]的配置数量: {}", deviceType, configs.size());
        for (int i = 0; i < configs.size(); i++) {
            DeviceIntegrationConfig c = configs.get(i);
            log.debug("配置[{}]: ID={}, typeCode={}, 请求模板长度={}, 参数映射长度={}",
                    i, c.getId(), c.getTypeCode(),
                    (c.getRequestTemplate() != null ? c.getRequestTemplate().length() : 0),
                    (c.getParamMapping() != null ? c.getParamMapping().length() : 0));
        }

        // 首先尝试通过typeCode精确匹配
        for (DeviceIntegrationConfig config : configs) {
            String typeCode = config.getTypeCode();
            if (typeCode != null && typeCode.equals(tableName)) {
                log.debug("通过typeCode[{}]精确匹配到配置ID: {}", tableName, config.getId());
                return config;
            }
        }

        // 如果没有精确匹配，尝试从请求模板中找到包含该表名的配置
        for (DeviceIntegrationConfig config : configs) {
            String requestTemplate = config.getRequestTemplate();
            if (requestTemplate != null && requestTemplate.contains(tableName)) {
                log.debug("在请求模板中找到表名[{}]的配置ID: {}", tableName, config.getId());
                return config;
            }
        }

        // 如果仍然没有找到匹配的配置，根据表名选择特定的配置类型
        if (tableName != null) {
            if (tableName.equals("DTS_T_RZZLDZCL")) {
                // 直阻试验表
                for (DeviceIntegrationConfig config : configs) {
                    if (config.getTypeCode().equals("getDataByTable")) {
                        log.debug("根据表名[{}]选择直阻试验配置ID: {}", tableName, config.getId());
                        return config;
                    }
                }
            } else if (tableName.equals("DTS_T_RZDYBCLHLJZBHJD")) {
                // 变比试验表
                for (DeviceIntegrationConfig config : configs) {
                    if (config.getTypeCode().equals("getDataByTable_ratioBias")) {
                        log.debug("根据表名[{}]选择变比试验配置ID: {}", tableName, config.getId());
                        return config;
                    }
                }
            } else if (tableName.equals("DTS_T_KZDLHKZSHCL2")) {
                // 空载试验表
                for (DeviceIntegrationConfig config : configs) {
                    if (config.getTypeCode().equals("getDataByTable_noload")) {
                        log.debug("根据表名[{}]选择空载试验配置ID: {}", tableName, config.getId());
                        return config;
                    }
                }
            } else if (tableName.equals("DTS_T_DLZKHFZSHCL")) {
                // 负载试验表
                for (DeviceIntegrationConfig config : configs) {
                    if (config.getTypeCode().equals("getDataByTable_load")) {
                        log.debug("根据表名[{}]选择负载试验配置ID: {}", tableName, config.getId());
                        return config;
                    }
                }
            }
        }

        // 如果以上方法都无法找到匹配的配置，记录警告并返回第一个可用配置
        log.warn("无法根据表名[{}]找到精确匹配的配置，使用默认配置", tableName);
        return configs.get(0);
    }

    /**
     * 备用方法：手动从JSON字符串中提取键值对，避免可能的JSON解析问题
     *
     * @param jsonStr JSON字符串
     * @return 手动解析的键值对映射
     */
    /**
     * 动态构建code到name的映射关系
     * 从设备返回的数据中提取出code->name的映射
     */
    private Map<String, String> buildCodeToNameMapping(String responseData) {
        Map<String, String> codeToNameMap = new HashMap<>();

        try {
            log.info("开始构建动态映射，原始数据: {}", responseData);
            JSONArray dataArray = JSONArray.parseArray(responseData);
            if (dataArray != null && !dataArray.isEmpty()) {
                log.info("解析JSON数组成功，数组大小: {}", dataArray.size());
                JSONObject firstData = dataArray.getJSONObject(0);
                log.info("第一个数据项的键: {}", firstData.keySet());

                // 检查是否是设备数据查询接口的格式 (包含name, code, result字段)
                if (firstData.containsKey("result") && firstData.containsKey("name") && firstData.containsKey("code")) {
                    log.info("检测到设备数据查询接口格式，提取映射关系");

                    // 遍历所有数据项
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject dataItem = dataArray.getJSONObject(i);
                        String testName = dataItem.getString("name");
                        JSONArray resultArray = dataItem.getJSONArray("result");

                        if (resultArray != null) {
                            log.info("处理试验[{}]，包含{}个测试项", testName, resultArray.size());
                            for (int j = 0; j < resultArray.size(); j++) {
                                JSONObject resultItem = resultArray.getJSONObject(j);
                                String code = resultItem.getString("code");
                                String name = resultItem.getString("name");

                                if (code != null && name != null) {
                                    codeToNameMap.put(code, name);
                                    log.info("添加映射关系: {} -> {}", code, name);
                                }
                            }
                        }
                    }
                } else {
                    log.warn("数据格式不是设备查询接口格式，无法构建映射关系。第一个对象包含的键: {}", firstData.keySet());
                }
            } else {
                log.warn("JSON数组为空或null");
            }
        } catch (Exception e) {
            log.error("构建code到name映射关系失败: {}", e.getMessage(), e);
        }

        log.info("构建映射关系完成，共{}个映射", codeToNameMap.size());
        return codeToNameMap;
    }

    /**
     * 执行动态映射的独立方法
     */
    private Map<String, String> performDynamicMapping(String responseData) {
        Map<String, String> resultMap = new HashMap<>();
        
        try {
            log.info("=== 执行独立动态映射 ===");
            
            // 动态构建映射关系
            Map<String, String> codeToNameMap = buildCodeToNameMapping(responseData);
            
            if (!codeToNameMap.isEmpty()) {
                log.info("动态映射: 构建了{}个code到name的映射关系", codeToNameMap.size());
                
                // 解析响应数据
                JSONArray jsonArray = JSONArray.parseArray(responseData);
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    // 遍历所有试验类型，从result中提取数据
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject testItem = jsonArray.getJSONObject(i);
                        JSONArray resultArray = testItem.getJSONArray("result");
                        String testName = testItem.getString("name");
                        
                        if (resultArray != null) {
                            log.info("独立映射处理试验[{}]的{}个测试项数据", testName, resultArray.size());
                            
                            for (int j = 0; j < resultArray.size(); j++) {
                                JSONObject resultItem = resultArray.getJSONObject(j);
                                String code = resultItem.getString("code");
                                String value = resultItem.getString("value");
                                
                                if (code != null && value != null && !value.equals("0") && !value.equals("0E-7")) {
                                    // 优先使用映射表中的name
                                    String mappedName = codeToNameMap.get(code);
                                    if (mappedName != null) {
                                        resultMap.put(mappedName, value);
                                        log.info("✅ 独立动态映射: {} -> {}, 值: {}", code, mappedName, value);
                                    } else {
                                        // 如果是中文字段名，直接使用
                                        if (code.contains("高压") || code.contains("低压") || code.equals("试验温度")) {
                                            resultMap.put(code, value);
                                            log.info("✅ 独立直接映射中文字段: {} -> {}, 值: {}", code, code, value);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                log.warn("独立动态映射: 无法构建映射关系");
            }
        } catch (Exception e) {
            log.error("独立动态映射失败: {}", e.getMessage(), e);
        }
        
        log.info("独立动态映射完成，共映射{}个字段", resultMap.size());
        return resultMap;
    }

    private Map<String, String> manuallyParseJsonMapping(String jsonStr) {
        Map<String, String> result = new HashMap<>();
        if (jsonStr == null || jsonStr.isEmpty()) {
            return result;
        }

        try {
            // 移除开头的 { 和结尾的 }
            String content = jsonStr.trim();
            if (content.startsWith("{")) {
                content = content.substring(1);
            }
            if (content.endsWith("}")) {
                content = content.substring(0, content.length() - 1);
            }

            // 按逗号分割各项
            String[] pairs = content.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    // 清理键和值中的引号和空格
                    String key = keyValue[0].trim();
                    if (key.startsWith("\"") && key.endsWith("\"")) {
                        key = key.substring(1, key.length() - 1);
                    }

                    String value = keyValue[1].trim();
                    if (value.startsWith("\"") && value.endsWith("\"")) {
                        value = value.substring(1, value.length() - 1);
                    }

                    result.put(key, value);
                    log.debug("手动解析映射: [{}] -> [{}]", key, value);
                }
            }
        } catch (Exception e) {
            log.error("手动解析JSON映射失败: {}", e.getMessage());
        }

        return result;
    }
}
