package com.westcatr.rd.testbusiness.business.jzreport.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * <p>
 * 国王—报告信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_report_info")
@Schema(description="国王—报告信息表")
public class JzReportInfo extends Model<JzReportInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID=1L;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
        private Long id;

    @Schema(description = "原始记录文件id")
    @TableField("original_record_file_id")
    private Long originalRecordFileId;

    @Schema(description = "报告文件id")
    @TableField("report_file_id")
    private Long reportFileId;

    @Schema(description = "报告编号")
    @Length(max = 255, message = "报告编号长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("report_number")
    private String reportNumber;

    @Schema(description = "样品id")
    @TableField("sample_id")
    private Long sampleId;

    @Schema(description = "任务id")
    @TableField("task_id")
    private Long taskId;

    @Schema(description = "是否合格")
    @Length(max = 255, message = "是否合格长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("tf_qualified")
    private String tfQualified;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
