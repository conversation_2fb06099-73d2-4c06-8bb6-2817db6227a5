package com.westcatr.rd.testbusiness.business.jztask.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 荆州—检项列表表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("jz_task_inspection_item_info")
@Schema(description = "荆州—检项列表表")
public class JzTaskInspectionItemInfo extends Model<JzTaskInspectionItemInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "检毕时间")
    @TableField("inspection_completion_time")
    private Date inspectionCompletionTime;

    @Schema(description = "检项编号")
    @Length(max = 255, message = "检项编号长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("inspection_item_number")
    private String inspectionItemNumber;

    @Schema(description = "关联样品Id")
    @TableField("sample_id")
    private Long sampleId;

    @Schema(description = "关联任务id")
    @TableField("task_id")
    private Long taskId;

    @Schema(description = "检测结果")
    @Length(max = 255, message = "检测结果长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("test_results")
    private String testResults;

    @Schema(description = "检测标准")
    @Length(max = 255, message = "检测标准长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("test_standard")
    private String testStandard;

    @Schema(description = "是否合格")
    @Length(max = 255, message = "是否合格长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("tf_qualified")
    private String tfQualified;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "检测人员")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "关联工单id")
    @TableField("work_order_id")
    private Long workOrderId;

    @Schema(description = "检测项目名称")
    @TableField("test_name")
    private String testName;

    @Schema(description = "数据采集方式")
    @TableField("data_collection_method")
    private String dataCollectionMethod;

    // 参数类型
    @Schema(description = "判别类型（比较型、计算比较依据型、计算比较结果型、不做判定型、人工判定型、其他型）")
    @TableField("param_type")
    private String paramType;

    // 参数公式
    @TableField("param_formula")
    @Schema(description = "参数公式")
    private String paramFormula;

    // 参数定义值
    @TableField("param_definition_value")
    @Schema(description = "参数定义值，比如x1")
    private String paramDefinitionValue;

    @Schema(description = "单位")
    @TableField("unit")
    private String unit;

    @Schema(description = "实验项目")
    @TableField("experimental_project")
    private String experimentalProject;

    @TableField("file_id")
    private Long fileId;

    // souce_id
    @TableField("source_id")
    private Long sourceId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
