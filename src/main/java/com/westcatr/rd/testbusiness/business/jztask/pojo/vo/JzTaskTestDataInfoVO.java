package com.westcatr.rd.testbusiness.business.jztask.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.testbusiness.business.jztask.entity.JzTaskTestDataInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 检测任务测试数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="检测任务测试数据表VO对象")
public class JzTaskTestDataInfoVO extends JzTaskTestDataInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
