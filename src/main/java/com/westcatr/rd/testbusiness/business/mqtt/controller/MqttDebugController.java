package com.westcatr.rd.testbusiness.business.mqtt.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.testbusiness.business.mqtt.entity.MqttDebugResponse;
import com.westcatr.rd.testbusiness.business.mqtt.service.MqttDebugService;
import com.westcatr.rd.testbusiness.business.mqtt.util.MqttDebugResponseGenerator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * MQTT调试功能控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Slf4j
@RestController
@RequestMapping("/api/mqtt/debug")
@Tag(name = "MQTT调试功能")
public class MqttDebugController {

    @Autowired
    private MqttDebugService mqttDebugService;

    @Operation(summary = "获取调试模式状态")
    @GetMapping("/status")
    public IResult<Map<String, Object>> getDebugStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", mqttDebugService.isDebugEnabled());
        status.put("statistics", mqttDebugService.getDebugStatistics());
        return IResult.ok(status);
    }

    @Operation(summary = "设置调试模式状态")
    @PutMapping("/status")
    public IResult<String> setDebugStatus(@RequestParam boolean enabled) {
        mqttDebugService.setDebugEnabled(enabled);
        String message = enabled ? "🔧 MQTT调试模式已启用" : "🔧 MQTT调试模式已禁用";
        log.info(message);
        return IResult.ok(message);
    }

    @Operation(summary = "获取设备调试响应列表")
    @GetMapping("/responses")
    public IResult<List<MqttDebugResponse>> listDebugResponses(
            @Parameter(description = "设备ID") @RequestParam String deviceId,
            @Parameter(description = "设备类型") @RequestParam String deviceType) {

        List<MqttDebugResponse> responses = mqttDebugService.listDebugResponses(deviceId, deviceType);
        return IResult.ok(responses);
    }

    @Operation(summary = "获取设备调试响应")
    @GetMapping("/response")
    public IResult<Map<String, Object>> getDebugResponse(
            @Parameter(description = "设备ID") @RequestParam String deviceId,
            @Parameter(description = "设备类型") @RequestParam String deviceType,
            @Parameter(description = "表名") @RequestParam(required = false) String tableName) {

        Map<String, Object> response = mqttDebugService.getDebugResponse(deviceId, deviceType, tableName);
        if (response == null) {
            return IResult.fail("未找到匹配的调试响应");
        }
        return IResult.ok(response);
    }

    @Operation(summary = "创建自定义调试响应")
    @PostMapping("/response")
    public IResult<String> createCustomResponse(
            @Parameter(description = "设备ID") @RequestParam String deviceId,
            @Parameter(description = "设备类型") @RequestParam String deviceType,
            @Parameter(description = "描述") @RequestParam String description,
            @Parameter(description = "表名") @RequestParam(required = false) String tableName,
            @Parameter(description = "响应数据") @RequestBody Map<String, Object> responseData) {

        String filePath = mqttDebugService.createCustomDebugResponse(
            deviceId, deviceType, responseData, description, tableName);

        if (filePath == null) {
            return IResult.fail("创建自定义调试响应失败");
        }

        return IResult.ok("📁 自定义调试响应创建成功: " + filePath);
    }

    @Operation(summary = "删除调试响应文件")
    @DeleteMapping("/response")
    public IResult<String> deleteDebugResponse(@RequestParam String filePath) {
        boolean success = mqttDebugService.deleteDebugResponse(filePath);
        if (success) {
            return IResult.ok("🗑️ 调试响应文件删除成功");
        } else {
            return IResult.fail("删除调试响应文件失败");
        }
    }

    @Operation(summary = "清理过期调试响应")
    @PostMapping("/cleanup")
    public IResult<String> cleanupExpiredResponses() {
        int cleanedCount = mqttDebugService.cleanExpiredResponses();
        return IResult.ok("🧹 清理完成，共清理 " + cleanedCount + " 个过期文件");
    }

    @Operation(summary = "获取调试统计信息")
    @GetMapping("/statistics")
    public IResult<Map<String, Object>> getStatistics() {
        Map<String, Object> stats = mqttDebugService.getDebugStatistics();
        return IResult.ok(stats);
    }

    @Operation(summary = "测试调试响应")
    @PostMapping("/test")
    public IResult<Map<String, Object>> testDebugResponse(
            @Parameter(description = "设备ID") @RequestParam String deviceId,
            @Parameter(description = "设备类型") @RequestParam String deviceType,
            @Parameter(description = "表名") @RequestParam(required = false) String tableName) {

        if (!mqttDebugService.isDebugEnabled()) {
            return IResult.fail("⚠️ 调试模式未启用，请先启用调试模式");
        }

        Map<String, Object> response = mqttDebugService.getDebugResponse(deviceId, deviceType, tableName);
        if (response == null) {
            return IResult.fail("❌ 未找到匹配的调试响应文件");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("debugResponse", response);
        result.put("message", "✅ 调试响应测试成功");
        result.put("deviceId", deviceId);
        result.put("deviceType", deviceType);
        result.put("tableName", tableName);

        return IResult.ok(result);
    }

    @Operation(summary = "批量导入调试响应")
    @PostMapping("/import")
    public IResult<String> importDebugResponses(@RequestBody List<Map<String, Object>> responseList) {
        int successCount = 0;
        int failCount = 0;
        
        for (Map<String, Object> item : responseList) {
            try {
                String deviceId = (String) item.get("deviceId");
                String deviceType = (String) item.get("deviceType");
                String description = (String) item.get("description");
                String tableName = (String) item.get("tableName");
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = (Map<String, Object>) item.get("responseData");
                
                String filePath = mqttDebugService.createCustomDebugResponse(
                    deviceId, deviceType, responseData, description, tableName);
                
                if (filePath != null) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("❌ 导入调试响应失败: {}", e.getMessage(), e);
                failCount++;
            }
        }

        return IResult.ok(String.format("📦 批量导入完成，成功: %d，失败: %d", successCount, failCount));
    }

    @Operation(summary = "生成示例调试响应")
    @PostMapping("/generate-example")
    public IResult<String> generateExampleResponse(
            @Parameter(description = "设备ID") @RequestParam String deviceId,
            @Parameter(description = "设备类型") @RequestParam String deviceType,
            @Parameter(description = "示例类型") @RequestParam(defaultValue = "normal") String exampleType) {

        try {
            MqttDebugResponse debugResponse;

            switch (exampleType.toLowerCase()) {
                case "transformer_direct_resistance":
                    debugResponse = MqttDebugResponseGenerator.generateTransformerDirectResistanceResponse(deviceId);
                    break;
                case "transformer_no_load":
                    debugResponse = MqttDebugResponseGenerator.generateTransformerNoLoadResponse(deviceId);
                    break;
                case "error":
                    debugResponse = MqttDebugResponseGenerator.generateErrorResponse(deviceId, deviceType, "设备连接超时", 500);
                    break;
                default:
                    debugResponse = MqttDebugResponseGenerator.generateGenericDeviceResponse(
                        deviceId, deviceType, null, "[{\"测试项目\":\"示例值\",\"状态\":\"正常\"}]");
                    break;
            }

            String filePath = mqttDebugService.createCustomDebugResponse(
                deviceId, deviceType, debugResponse.getOriginalResponse(),
                debugResponse.getDescription(), debugResponse.getTableName());

            if (filePath != null) {
                return IResult.ok("✅ 示例调试响应生成成功: " + filePath);
            } else {
                return IResult.fail("❌ 生成示例调试响应失败");
            }

        } catch (Exception e) {
            log.error("❌ 生成示例调试响应失败: {}", e.getMessage(), e);
            return IResult.fail("生成示例调试响应失败: " + e.getMessage());
        }
    }
}
