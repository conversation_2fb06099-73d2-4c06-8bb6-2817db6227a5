server.port=7321
# 激活的Spring配置文件环境 (正式环境)
spring.profiles.active=dev
# 允许Spring Bean之间的循环引用
spring.main.allow-circular-references=true
# 应用的上下文路径 (访问根路径)
server.servlet.context-path=/test
# Westcatr安全配置：用于查询用户认证信息的SQL语句
westcatr.boot.security.select-user-sql=select id,user_name as username,pass_word as password,full_name,enable,tf_path_sign as extend1 from org_user_info
# Westcatr安全配置：查询用户SQL语句中的WHERE条件字段 (按用户名查询)
westcatr.boot.security.select-user-sql-where=user_name
# Westcatr安全配置：启用安全功能
westcatr.boot.security.enable=true
# Spring Boot应用的名称
spring.application.name=jz_gw_test_back
# Westcatr用户配置：用户表名
westcatr.boot.user.user-table=org_user_info
# Westcatr用户配置：部门表名
westcatr.boot.user.dept-table=org_dept_info
# Westcatr用户配置：用户部门关联表名
westcatr.boot.user.user-dept-table=org_user_dept_info
# Westcatr安全配置：自动同步API权限信息 (0不 TONG BU，1只 TONG BU 本服务，3 TONG BU 全部微服务)
# 自动同步接口权限信息 （0不同步，1自己，3全部(微服务场景下)）
westcatr.boot.security.auto-sync-api=1
# Westcatr API日志配置：启用API请求日志记录
westcatr.boot.apiLog.enable=true
# Westcatr API日志配置：默认是否保存API日志
westcatr.boot.api-log.default-save=false
# Westcatr安全配置：不需要进行安全检查的精确URL路径列表 (逗号分隔)
westcatr.boot.security.no-check-url=/test/v3/api-docs/apis,/test/v3/api-docs/swagger-config,/test/doc.html,/test/swagger-ui.html,/test/csrf,/test/error,/test/login,/test/captcha/get,/test/captcha/check,/test/oa/getToken,/test/login/publicKey,/test/systemConfig/front,/test/login/self,/test/office/saveBack,/test/zlmediakit/callback
# Westcatr安全配置：不需要进行安全检查的URL路径模式匹配列表 (逗号分隔)
westcatr.boot.security.no-check-url-match=/test/file/down/**,/test/actuator/**,/test/webjars/**,/test/swagger-resources/**,/test/doc.html,/test/anzhi/**
# 忽略请求的记录的日志api接口地址
# Westcatr API日志配置：忽略记录日志的API接口地址列表 (逗号分隔)
westcatr.boot.api-log.ignorePath=/test/error,/test/applications,/test/callback,/test/flowInfo/flowImg,/test/arrivalInspectionBaseInfo/download,/test/templateInfo/downTemplate,/test/flowTaskInfo/diagram,/test/reportSendInfo/fileDownLoad,/test/sampleInfo/exportTemplate,/test/taskDeptResultInfo/exportData,/test/reportInfo/update,/test/taskDeptRecordInfo/uploadRecordFileInfo,/test/entrustInfo/update,/test/changeEntrustToRecordAndReport,/test/taskDeptResultInfo/add,/test/reportInfo/add
# Westcatr安全配置：是否启用登录密码加密功能
westcatr.boot.security.encryption=true
# Westcatr安全配置：是否启用登录滑动验证码功能
westcatr.boot.security.slide-captcha=true
# Logback日志配置：滚动日志文件的最大保留天数
logging.logback.rollingpolicy.max-history=60
# Logback日志配置：单个日志文件的最大大小 (超过则滚动)
logging.logback.rollingpolicy.max-file-size=200MB
# Westcatr Swagger配置：API文档页面标题
# 测试业务系统后台接口文档
westcatr.boot.web.swagger.title=测试业务系统后台接口文档
# Westcatr ORM配置：默认的数据源服务Bean名称
westcatr.boot.orm.defaultDataSourceService=mysqlDbService
# Westcatr ORM配置：数据源名称映射 (master -> jz_supplie_db)
westcatr.boot.orm.dataSourceDbNameMap.master=jz_supplie_db
# 允许后定义的Bean覆盖先前同名的Bean定义
spring.main.allow-bean-definition-overriding=true
# Spring MVC配置：启用文件上传功能
spring.servlet.multipart.enabled=true
# Spring MVC配置：单个上传文件的最大大小限制
spring.servlet.multipart.max-file-size=3GB
# Spring MVC配置：单次请求中所有上传文件的总大小限制
spring.servlet.multipart.max-request-size=3GB
# Web服务器配置：HTTP Session会话超时时间 (秒)
server.servlet.session.timeout=240s
# 日志级别配置：设置 com.deepoove.poi 包的日志级别为 ERROR
logging.level.com.deepoove.poi=error
# Westcatr API日志配置：启用API请求日志记录 (重复设置)
westcatr.boot.api-log.enable=true
# Westcatr Web配置：禁用XSS跨站脚本攻击过滤器
westcatr.boot.web.xss-filter.enabled=false
# Westcatr缓存配置：启用方法级别的缓存
westcatr.boot.cache.method-cache=true
# Spring MVC配置：异步请求的超时时间 (秒)
spring.mvc.async.request-timeout=600s
# Westcatr Web配置：XSS过滤器排除的路径列表 (即使启用XSS过滤器，这些路径也不过滤)
westcatr.boot.web.xss-filter.exclude-paths=/test/actuator,/test/actuator/health,/test

# 设置控制台编码
# Spring Boot配置：强制设置应用的文件编码为UTF-8
spring.mandatory-file-encoding=UTF-8

# Flyway配置：启用Flyway数据库迁移功能
spring.flyway.enabled=false
# Flyway配置：当迁移时如果不存在基线则自动创建基线
spring.flyway.baseline-on-migrate=true
# Flyway配置：指定数据库迁移脚本的位置 (类路径下)
spring.flyway.locations=classpath:db/migration
# Flyway配置：指定Flyway管理的数据库Schema名称
spring.flyway.schemas=jz_supplie_db

# Tesseract OCR配置：Tesseract安装路径 (可能用于读取依赖库)
tesseract.install.readdes=/opt/homebrew/Cellar/tesseract/5.5.0_1/lib

# Tesseract OCR配置：Tesseract语言数据包(tessdata)路径
tesseract.language.path=/opt/homebrew/share/tessdata

# SMB访问电脑配置:地址、账号、密码 jsonarray # SMB/CIFS 访问配置说明
#smb.computers=[{"address":"***********/xxx/","username":"testuser","password":"123456"}] # SMB/CIFS 访问示例 (已注释)

# SMB/CIFS配置：需要访问的网络共享计算机列表 (JSON格式)
smb.computers=[{"address":"*************/test_jz/","username":"testuser","password":"123456"}]
# smbfilesave # SMB 文件保存目录说明
# SMB文件操作配置：本地文件保存目录 (可能与SMB操作相关, 引用了应用名)
smb.file.save.dir=/Users/<USER>/work/upload/${spring.application.name}/smbfile

# Westcatr缓存配置：启用Redis作为缓存实现
westcatr.boot.cache.enable-redis=true

# rstp转flv配置
rstptoflv.host=http://************:8081/live/

# Westcatr默认密码
westcatr.boot.security.default-password=GwTest2025@312.

# 禁用Spring Cloud Config
spring.cloud.config.enabled=false

# MQTT配置
mqtt.broker-url=tcp://48kkod342786.vicp.fun:39266
mqtt.client-id-prefix=jz_westcatr_test
mqtt.username=
mqtt.password=
mqtt.down-topic=/jcxt/link/down/edgeGateway/edgeTopic
mqtt.up-topic=/jcxt/link/up/edgeGateway/edgeTopic
mqtt.qos=1
# 增加请求超时时间到60秒，减少超时概率
mqtt.request-timeout=60
# 是否启用MQTT客户端，默认为true，设置为false可以禁用MQTT连接
mqtt.enabled=false
# 断线重连初始间隔（毫秒）
mqtt.reconnect-interval=5000
# 最大重连间隔（毫秒）
mqtt.max-reconnect-interval=300000

# 设备数据查询配置
# 是否启用数据库查询模式（默认开启，设置为false可以禁用新的数据库查询方式）
device.data.query.enabled=true

# MQTT调试配置
mqtt.debug.enabled=false
mqtt.debug.storage-path=/data/mqtt_debug/responses/
mqtt.debug.retention-days=30
mqtt.debug.auto-save-responses=true

# 高德地图天气API配置
weather.amap.key=22a385b66db23caace45979620efe3d5
weather.amap.url=https://restapi.amap.com/v3/weather/weatherInfo

# PostgreSQL数据源配置（用于数据同步）
postgresql.datasource.url=**********************************************************
postgresql.datasource.username=jiance
postgresql.datasource.password=jiance.*123

# 数据同步配置
data.sync.enabled=true
data.sync.batch-size=1000
data.sync.interval-seconds=2
data.sync.max-retry-times=3
data.sync.retry-delay-seconds=5
# 时区转换配置：是否需要进行时区转换
data.sync.timezone-conversion.enabled=false
# PostgreSQL的时区设置（如果PostgreSQL使用UTC，设置为UTC；如果使用系统时区，设置为Asia/Shanghai）
data.sync.postgresql.timezone=Asia/Shanghai
# MySQL的时区设置
data.sync.mysql.timezone=Asia/Shanghai

# MyBatis-Plus 配置
# 禁用 Lambda 缓存，解决多数据源环境下的缓存问题
mybatis-plus.global-config.enable-sql-runner=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true
mybatis-plus.configuration.jdbc-type-for-null=null

# 国网模板文件配置（如果外部路径不存在，将使用资源目录中的模板）
gw.template.file.path=/Users/<USER>/Desktop/muban

# 彩色输出
spring.output.ansi.enabled=always

westcatr.boot.orm.default-data-source-service=mysqlDbService
westcatr.boot.orm.data-source-db-name-map.master=jz_supplie_db