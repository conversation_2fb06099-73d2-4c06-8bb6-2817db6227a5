-- 创建设备对接数据管理配置表
CREATE TABLE IF NOT EXISTS `bu_device_data_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_code` varchar(64) NOT NULL COMMENT '设备代码',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `response_body` text NOT NULL COMMENT '设备返回体JSON配置',
  `status` varchar(32) DEFAULT 'enabled' COMMENT '状态(enabled/disabled)',
  `description` varchar(512) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_code` (`device_code`),
  KEY `idx_device_code` (`device_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备对接数据管理配置表';
