ALTER TABLE `org_user_info`
ADD COLUMN `major` VARCHAR(255) DEFAULT NULL COMMENT '所属专业';

CREATE TABLE `jz_task_test_data_info` (
   `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
   `sample_name` VARCHAR(32) DEFAULT NULL COMMENT '样品名称',
   `mode` VARCHAR(256) DEFAULT NULL COMMENT '模式',
   `test_project` VARCHAR(64) DEFAULT NULL COMMENT '测试项目',
   `parameter` VARCHAR(64) DEFAULT NULL COMMENT '参数',
   `unit` VARCHAR(32) DEFAULT NULL COMMENT '单位',
   `value` VARCHAR(32) DEFAULT NULL COMMENT '数值',
   `back_info` VARCHAR(64) DEFAULT NULL COMMENT '反馈信息',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检测任务测试数据表';