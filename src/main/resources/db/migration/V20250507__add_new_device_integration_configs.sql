-- 添加绝缘子机电破坏试验机和维卡万能试验机的设备对接配置
INSERT INTO `bu_device_integration_config` (`id`, `device_type`, `device_name`, `product_code`, `device_code`, `type_code`, `request_template`, `response_template`, `param_mapping`, `status`, `description`, `create_time`, `update_time`)
VALUES
(5, '绝缘子机电破坏试验机', '绝缘子机电破坏试验机', 'jyzjdph', 'jyaDevice', 'getAllData', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "test": "1"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '绝缘子机电破坏试验机设备对接规范', NOW(), NOW()),

(6, '维卡万能试验机', '维卡万能试验机', 'wkProduct', 'wkDevice', 'getAllData', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "test": "1"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '维卡万能试验机设备对接规范', NOW(), NOW());
