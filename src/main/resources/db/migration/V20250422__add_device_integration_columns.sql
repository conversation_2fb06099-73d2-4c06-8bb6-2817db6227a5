-- 添加设备类型代码和设备参数字段
ALTER TABLE `bu_instrument_new_info` 
ADD COLUMN IF NOT EXISTS `device_type_code` VARCHAR(64) COMMENT '设备类型代码',
ADD COLUMN IF NOT EXISTS `device_params` TEXT COMMENT '设备参数JSON';

-- 创建设备对接规范表
CREATE TABLE IF NOT EXISTS `bu_device_integration_config` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `device_type` varchar(64) NOT NULL COMMENT '设备类型',
  `device_name` varchar(255) NOT NULL COMMENT '设备名称',
  `product_code` varchar(64) NOT NULL COMMENT '产品代码',
  `device_code` varchar(64) NOT NULL COMMENT '设备代码',
  `type_code` varchar(64) NOT NULL COMMENT '类型代码',
  `request_template` text COMMENT '请求模板',
  `response_template` text COMMENT '响应模板',
  `param_mapping` text COMMENT '参数映射',
  `status` varchar(32) DEFAULT 'enabled' COMMENT '状态(enabled/disabled)',
  `description` varchar(512) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_type_product_code` (`device_type`,`product_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备对接规范表';

-- 插入设备对接规范数据
INSERT INTO `bu_device_integration_config` (`id`, `device_type`, `device_name`, `product_code`, `device_code`, `type_code`, `request_template`, `response_template`, `param_mapping`, `status`, `description`, `create_time`, `update_time`)
VALUES
-- 10T电子万能试验机
(1, '电子万能试验机', '10T电子万能试验机', 'electronicUTM', 'electronicUTM10T', 'getData', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "host": "${host}",
    "userName": "${userName}",
    "pwd": "${pwd}",
    "share": "${share}"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '10T电子万能试验机设备对接规范', NOW(), NOW()),

-- 电机设备
(2, '电机设备', '电机设备', 'eleMachine', 'eleMachine51', 'getData', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "host": "${host}",
    "userName": "${userName}",
    "pwd": "${pwd}",
    "share": "${share}"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '电机设备对接规范', NOW(), NOW()),

-- 万测变压器
(3, '变压器', '万测变压器', 'accessId', 'wanceDevice', 'getDataByTable', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "tableName": "${tableName}",
    "resultNum": "1",
    "beginTime": "${beginTime}",
    "endTime": "${endTime}"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '万测变压器设备对接规范', NOW(), NOW()),

-- 思创设备
(4, '思创设备', '思创检测设备', 'sichuangDevice', 'sichuang', 'test', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "testData": "${testData}"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '思创检测设备对接规范', NOW(), NOW());

-- 插入设备数据
INSERT INTO `bu_instrument_new_info` 
(`id`, `sbbh`, `sblb`, `sbmc`, `scc`, `create_time`, `update_time`, `device_type_code`, `device_params`, `collection_method`) 
VALUES 
(1001, 'UTM-10T-001', '电子万能试验机', '10T电子万能试验机', '某制造商', NOW(), NOW(), '电子万能试验机', 
'{"host":"************","userName":"utm","pwd":"123456","share":"testdata"}', '直采')
ON DUPLICATE KEY UPDATE 
`device_type_code` = '电子万能试验机', 
`device_params` = '{"host":"************","userName":"utm","pwd":"123456","share":"testdata"}';

INSERT INTO `bu_instrument_new_info` 
(`id`, `sbbh`, `sblb`, `sbmc`, `scc`, `create_time`, `update_time`, `device_type_code`, `device_params`, `collection_method`) 
VALUES 
(1002, 'EM-51-001', '电机设备', '电机设备', '某制造商', NOW(), NOW(), '电机设备', 
'{"host":"************","userName":"smb","pwd":"123456","share":"data"}', '直采')
ON DUPLICATE KEY UPDATE 
`device_type_code` = '电机设备', 
`device_params` = '{"host":"************","userName":"smb","pwd":"123456","share":"data"}';

INSERT INTO `bu_instrument_new_info` 
(`id`, `sbbh`, `sblb`, `sbmc`, `scc`, `create_time`, `update_time`, `device_type_code`, `device_params`, `collection_method`) 
VALUES 
(1003, 'WC-TR-001', '变压器', '万测变压器', '万测科技', NOW(), NOW(), '变压器', 
'{"tableName":"DTS_T_RZZLDZCL"}', '直采')
ON DUPLICATE KEY UPDATE 
`device_type_code` = '变压器', 
`device_params` = '{"tableName":"DTS_T_RZZLDZCL"}';

INSERT INTO `bu_instrument_new_info` 
(`id`, `sbbh`, `sblb`, `sbmc`, `scc`, `create_time`, `update_time`, `device_type_code`, `device_params`, `collection_method`) 
VALUES 
(1004, 'SC-TEST-001', '思创设备', '思创检测设备', '思创科技', NOW(), NOW(), '思创设备', 
'{"testData":"sample_data"}', '直采')
ON DUPLICATE KEY UPDATE 
`device_type_code` = '思创设备', 
`device_params` = '{"testData":"sample_data"}';
