-- 初始化其他设备类型的配置数据

-- 电子万能试验机配置
INSERT INTO `bu_device_data_config` (`device_code`, `device_name`, `response_body`, `status`, `description`, `create_time`, `update_time`)
VALUES ('电子万能试验机', '10T电子万能试验机', '{
  "experiments": [
    {
      "name": "拉伸试验",
      "code": "tensile_test",
      "tableName": "utm_tensile_data",
      "mapping": {
        "最大拉力": "max_force",
        "断裂强度": "break_strength",
        "屈服强度": "yield_strength",
        "弹性模量": "elastic_modulus",
        "延伸率": "elongation",
        "试验温度": "temperature"
      }
    },
    {
      "name": "压缩试验",
      "code": "compression_test",
      "tableName": "utm_compression_data",
      "mapping": {
        "最大压力": "max_force",
        "压缩强度": "compression_strength",
        "压缩模量": "compression_modulus",
        "试验温度": "temperature"
      }
    }
  ]
}', 'enabled', '电子万能试验机设备对接数据管理配置', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`response_body` = VALUES(`response_body`),
`update_time` = NOW();

-- 电机设备配置
INSERT INTO `bu_device_data_config` (`device_code`, `device_name`, `response_body`, `status`, `description`, `create_time`, `update_time`)
VALUES ('电机设备', '电机检测设备', '{
  "experiments": [
    {
      "name": "电机性能试验",
      "code": "motor_performance_test",
      "tableName": "motor_performance_data",
      "mapping": {
        "额定功率": "rated_power",
        "额定电压": "rated_voltage",
        "额定电流": "rated_current",
        "转速": "speed",
        "效率": "efficiency",
        "功率因数": "power_factor",
        "温升": "temperature_rise",
        "试验温度": "ambient_temperature"
      }
    },
    {
      "name": "绝缘电阻试验",
      "code": "insulation_resistance_test",
      "tableName": "motor_insulation_data",
      "mapping": {
        "绝缘电阻": "insulation_resistance",
        "试验电压": "test_voltage",
        "试验时间": "test_duration",
        "环境温度": "ambient_temperature",
        "相对湿度": "relative_humidity"
      }
    }
  ]
}', 'enabled', '电机设备对接数据管理配置', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`response_body` = VALUES(`response_body`),
`update_time` = NOW();

-- 思创设备配置
INSERT INTO `bu_device_data_config` (`device_code`, `device_name`, `response_body`, `status`, `description`, `create_time`, `update_time`)
VALUES ('思创设备', '思创检测设备', '{
  "experiments": [
    {
      "name": "综合检测试验",
      "code": "comprehensive_test",
      "tableName": "sichuang_test_data",
      "mapping": {
        "检测项目": "test_item",
        "检测结果": "test_result",
        "检测值": "test_value",
        "标准值": "standard_value",
        "偏差": "deviation",
        "试验温度": "temperature",
        "试验湿度": "humidity"
      }
    }
  ]
}', 'enabled', '思创设备对接数据管理配置', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`response_body` = VALUES(`response_body`),
`update_time` = NOW();
