-- 初始化维卡试验机设备数据配置
-- 支持新的嵌套数组格式：message.bodyData[].bodyData[]

INSERT INTO `bu_device_data_config` (`device_code`, `device_name`, `response_body`, `status`, `description`, `create_time`, `update_time`)
VALUES ('accessDeviceList106', '维卡试验机', '{
  "experiments": [
    {
      "code": "accessDeviceList106",
      "name": "维卡试验机",
      "tableName": "RstTab",
      "mapping": {
        "AllRoutNo": "AllRoutNo",
        "SmpRstNot": "SmpRstNot", 
        "TestPreRst": "TestPreRst",
        "InTestOrNot": "InTestOrNot",
        "TestLoadRst": "TestLoadRst",
        "TestTimeRst": "TestTimeRst"
      }
    }
  ]
}', 'enabled', '维卡试验机设备对接数据管理配置，支持嵌套数组格式', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`response_body` = VALUES(`response_body`),
`update_time` = NOW();
