CREATE TABLE `workstation_history_count` (
                                             `id` bigint NOT NULL,
                                             `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '样品/任务/工单',
                                             `status` varchar(255) DEFAULT NULL COMMENT '对应状态',
                                             `number` varchar(255) DEFAULT NULL COMMENT '对应编号',
                                             `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作台-历史数据统计';