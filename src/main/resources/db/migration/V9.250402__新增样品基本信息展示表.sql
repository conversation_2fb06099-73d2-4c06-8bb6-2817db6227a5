CREATE TABLE `bu_sample_base_info` (
                                       `id` bigint NOT NULL,
                                       `name` varchar(255) DEFAULT NULL COMMENT '样品名称',
                                       `type` varchar(255) DEFAULT NULL COMMENT '样品型号',
                                       `model` varchar(255) DEFAULT NULL COMMENT '样品规格',
                                       `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                       `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='样品基本信息-关联表';