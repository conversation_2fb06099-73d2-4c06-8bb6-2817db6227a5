-- 创建设备功能调用记录表
-- 作者: liusheng
-- 日期: 2025-06-14

CREATE TABLE IF NOT EXISTS `zzzzz_log_device_function_call` (
  `id` bigint NOT NULL COMMENT '主键',
  `device_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备id',
  `product` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备产品',
  `output_params` json DEFAULT NULL COMMENT '输出数据',
  `async_call` smallint DEFAULT '0' COMMENT '是否为异步调用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_product` (`product`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备功能调用记录';

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS `idx_device_product` ON `zzzzz_log_device_function_call` (`device_id`, `product`);
CREATE INDEX IF NOT EXISTS `idx_time_device` ON `zzzzz_log_device_function_call` (`create_time`, `device_id`);
