ALTER TABLE `bu_instrument_new_info` 
MODIFY COLUMN `parameters_inspected_equipment` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '设备可检参数' AFTER `test_user_id`;

INSERT INTO `sys_common_join_main` (`id`, `join_code`, `middle_table`, `left_id_name`, `left_id_type`, `right_id_name`, `right_id_type`, `create_time`, `data_source_name`, `join_explain`) VALUES (57, 57, NULL, 'id1', 1, 'id2', 1, '2025-03-19 23:35:49', '设备-标准', NULL);