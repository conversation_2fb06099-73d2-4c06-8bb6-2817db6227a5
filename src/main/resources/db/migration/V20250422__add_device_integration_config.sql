-- 创建设备对接规范表
CREATE TABLE IF NOT EXISTS `bu_device_integration_config` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `device_type` varchar(64) NOT NULL COMMENT '设备类型',
  `device_name` varchar(255) NOT NULL COMMENT '设备名称',
  `product_code` varchar(64) NOT NULL COMMENT '产品代码',
  `device_code` varchar(64) NOT NULL COMMENT '设备代码',
  `type_code` varchar(64) NOT NULL COMMENT '类型代码',
  `request_template` text COMMENT '请求模板',
  `response_template` text COMMENT '响应模板',
  `param_mapping` text COMMENT '参数映射',
  `status` varchar(32) DEFAULT 'enabled' COMMENT '状态(enabled/disabled)',
  `description` varchar(512) DEFAULT NULL COMMENT '描述',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_type_product_code` (`device_type`,`product_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备对接规范表';

-- 插入初始数据
INSERT INTO `bu_device_integration_config` (`id`, `device_type`, `device_name`, `product_code`, `device_code`, `type_code`, `request_template`, `response_template`, `param_mapping`, `status`, `description`, `create_time`, `update_time`)
VALUES
(1, '电子万能试验机', '10T电子万能试验机', 'electronicUTM', 'electronicUTM10T', 'getData', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "host": "${host}",
    "userName": "${userName}",
    "pwd": "${pwd}",
    "share": "${share}"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '10T电子万能试验机设备对接规范', NOW(), NOW()),

(2, '电机设备', '电机设备', 'eleMachine', 'eleMachine51', 'getData', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "host": "${host}",
    "userName": "${userName}",
    "pwd": "${pwd}",
    "share": "${share}"
  }
}', '{
  "header": {
    "deviceId": "${deviceCode}",
    "product": "${productCode}",
    "typeCode": "${typeCode}",
    "messageId": "${messageId}"
  },
  "body": {
    "outputParams": {
      "body": {
        "content": "${content}"
      }
    }
  }
}', '{
  "testItem": "testItem",
  "testResult": "testResult"
}', 'enabled', '电机设备对接规范', NOW(), NOW());
