# MQTT调试功能使用说明

## 📋 功能概述

MQTT调试功能允许在没有真实设备的情况下进行功能测试，通过预先保存的响应文件来模拟设备响应。

## ⚙️ 配置说明

在 `application.properties` 中添加以下配置：

```properties
# MQTT调试配置
mqtt.debug.enabled=false                    # 是否启用调试模式
mqtt.debug.storage-path=/data/mqtt_debug/responses/  # 响应文件存储路径
mqtt.debug.retention-days=30                # 响应文件保留天数
mqtt.debug.auto-save-responses=true         # 是否自动保存设备响应
```

## 🔧 使用方法

### 1. 启用调试模式

```bash
# 通过API启用调试模式
curl -X PUT "http://localhost:8080/api/mqtt/debug/status?enabled=true"
```

### 2. 查看调试状态

```bash
# 获取调试模式状态和统计信息
curl -X GET "http://localhost:8080/api/mqtt/debug/status"
```

### 3. 创建自定义调试响应

```bash
# 创建自定义调试响应
curl -X POST "http://localhost:8080/api/mqtt/debug/response" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "DEVICE001",
    "deviceType": "变压器", 
    "description": "变压器直阻试验示例",
    "tableName": "DTS_T_RZZLDZCL",
    "responseData": {
      "header": {
        "deviceId": "DEVICE001",
        "product": "变压器",
        "typeCode": "callFunction",
        "messageId": "1737456789000"
      },
      "body": {
        "functionName": "getDataByTable",
        "resultCode": 200,
        "resultMessage": "成功",
        "outputParams": {
          "body": "[{\"A相直流电阻\":\"0.123\",\"B相直流电阻\":\"0.124\"}]",
          "_status": 200,
          "_msg": "获取数据成功"
        }
      }
    }
  }'
```

### 4. 测试调试响应

```bash
# 测试调试响应
curl -X POST "http://localhost:8080/api/mqtt/debug/test?deviceId=DEVICE001&deviceType=变压器&tableName=DTS_T_RZZLDZCL"
```

### 5. 查看设备的调试响应列表

```bash
# 获取设备的所有调试响应文件
curl -X GET "http://localhost:8080/api/mqtt/debug/responses?deviceId=DEVICE001&deviceType=变压器"
```

## 📂 文件结构

调试响应文件按以下结构存储：

```
/data/mqtt_debug/responses/
├── 变压器/
│   ├── DEVICE001/
│   │   ├── mqtt_response_变压器_DEVICE001_DTS_T_RZZLDZCL_20250121_103000.json
│   │   └── mqtt_response_变压器_DEVICE001_custom_20250121_104500.json
│   └── DEVICE002/
└── 其他设备类型/
```

## 🔍 调试模式工作原理

1. **启用调试模式**：设置 `mqtt.debug.enabled=true`
2. **请求拦截**：当发送设备请求时，系统首先检查是否启用调试模式
3. **文件查找**：根据设备ID、设备类型、表名查找匹配的调试响应文件
4. **优先级排序**：按优先级和时间戳排序，返回最合适的响应
5. **自动保存**：真实的设备响应会自动保存为调试文件（如果启用）

## 📝 注意事项

- 调试模式下不会发送真实的MQTT请求
- 调试响应文件格式必须与真实MQTT响应完全一致
- 生产环境中请确保调试模式默认关闭
- 定期清理过期的调试响应文件

## 🛠️ 高级功能

### 批量导入调试响应

```bash
curl -X POST "http://localhost:8080/api/mqtt/debug/import" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "deviceId": "DEVICE001",
      "deviceType": "变压器",
      "description": "正常响应",
      "tableName": "DTS_T_RZZLDZCL",
      "responseData": {...}
    },
    {
      "deviceId": "DEVICE001", 
      "deviceType": "变压器",
      "description": "异常响应",
      "tableName": "DTS_T_RZZLDZCL",
      "responseData": {...}
    }
  ]'
```

### 清理过期文件

```bash
# 清理过期的调试响应文件
curl -X POST "http://localhost:8080/api/mqtt/debug/cleanup"
```

### 获取统计信息

```bash
# 获取调试统计信息
curl -X GET "http://localhost:8080/api/mqtt/debug/statistics"
```
