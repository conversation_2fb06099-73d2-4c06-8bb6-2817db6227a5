#!/bin/bash

# MQTT调试功能测试脚本
# 作者: liusheng
# 日期: 2025-01-21

BASE_URL="http://localhost:7321/test/api/mqtt/debug"

echo "🔧 MQTT调试功能测试开始"
echo "================================"

# 1. 检查调试模式状态
echo "📊 1. 检查调试模式状态"
curl -s -X GET "$BASE_URL/status" | jq '.'
echo ""

# 2. 启用调试模式
echo "🔧 2. 启用调试模式"
curl -s -X PUT "$BASE_URL/status?enabled=true" | jq '.'
echo ""

# 3. 生成变压器直阻试验示例响应
echo "📁 3. 生成变压器直阻试验示例响应"
curl -s -X POST "$BASE_URL/generate-example?deviceId=DEVICE001&deviceType=变压器&exampleType=transformer_direct_resistance" | jq '.'
echo ""

# 4. 生成变压器空载试验示例响应
echo "📁 4. 生成变压器空载试验示例响应"
curl -s -X POST "$BASE_URL/generate-example?deviceId=DEVICE001&deviceType=变压器&exampleType=transformer_no_load" | jq '.'
echo ""

# 5. 生成错误响应示例
echo "❌ 5. 生成错误响应示例"
curl -s -X POST "$BASE_URL/generate-example?deviceId=DEVICE001&deviceType=变压器&exampleType=error" | jq '.'
echo ""

# 6. 查看设备的调试响应列表
echo "📋 6. 查看设备的调试响应列表"
curl -s -X GET "$BASE_URL/responses?deviceId=DEVICE001&deviceType=变压器" | jq '.'
echo ""

# 7. 测试获取调试响应
echo "🧪 7. 测试获取调试响应（直阻试验）"
curl -s -X POST "$BASE_URL/test?deviceId=DEVICE001&deviceType=变压器&tableName=DTS_T_RZZLDZCL" | jq '.'
echo ""

# 8. 测试获取调试响应（空载试验）
echo "🧪 8. 测试获取调试响应（空载试验）"
curl -s -X POST "$BASE_URL/test?deviceId=DEVICE001&deviceType=变压器&tableName=DTS_T_KZSY" | jq '.'
echo ""

# 9. 获取调试统计信息
echo "📊 9. 获取调试统计信息"
curl -s -X GET "$BASE_URL/statistics" | jq '.'
echo ""

# 10. 创建自定义调试响应
echo "🛠️ 10. 创建自定义调试响应"
curl -s -X POST "$BASE_URL/response" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "DEVICE002",
    "deviceType": "变压器",
    "description": "自定义变压器响应",
    "tableName": "DTS_T_CUSTOM",
    "responseData": {
      "header": {
        "deviceId": "DEVICE002",
        "product": "变压器",
        "typeCode": "callFunction",
        "messageId": "custom_test_123"
      },
      "body": {
        "functionName": "getDataByTable",
        "resultCode": 200,
        "resultMessage": "成功",
        "outputParams": {
          "body": "[{\"自定义参数1\":\"100.5\",\"自定义参数2\":\"200.3\"}]",
          "_status": 200,
          "_msg": "获取数据成功"
        }
      }
    }
  }' | jq '.'
echo ""

# 11. 测试自定义响应
echo "🧪 11. 测试自定义响应"
curl -s -X POST "$BASE_URL/test?deviceId=DEVICE002&deviceType=变压器&tableName=DTS_T_CUSTOM" | jq '.'
echo ""

# 12. 禁用调试模式
echo "🔧 12. 禁用调试模式"
curl -s -X PUT "$BASE_URL/status?enabled=false" | jq '.'
echo ""

echo "================================"
echo "✅ MQTT调试功能测试完成"
echo ""
echo "📝 测试说明："
echo "   - 如果看到 'command not found: jq' 错误，请安装 jq 工具"
echo "   - 如果连接失败，请确保应用已启动且端口正确"
echo "   - 调试文件保存在 /data/mqtt_debug/responses/ 目录下"
echo ""
