#!/bin/bash

# 简单的MQTT调试功能测试脚本
# 作者: liusheng
# 日期: 2025-01-21

BASE_URL="http://localhost:7321/test/api/mqtt/debug"

echo "🔧 MQTT调试功能快速测试"
echo "========================"

# 1. 启用调试模式
echo "1. 启用调试模式..."
curl -s -X PUT "$BASE_URL/status?enabled=true"
echo " ✅"

# 2. 生成示例响应
echo "2. 生成变压器示例响应..."
curl -s -X POST "$BASE_URL/generate-example?deviceId=TEST001&deviceType=变压器&exampleType=transformer_direct_resistance"
echo " ✅"

# 3. 测试调试响应
echo "3. 测试调试响应..."
curl -s -X POST "$BASE_URL/test?deviceId=TEST001&deviceType=变压器&tableName=DTS_T_RZZLDZCL"
echo " ✅"

# 4. 获取统计信息
echo "4. 获取统计信息..."
curl -s -X GET "$BASE_URL/statistics"
echo " ✅"

echo "========================"
echo "✅ 快速测试完成！"
echo ""
echo "💡 提示："
echo "   - 调试模式已启用"
echo "   - 已生成示例响应文件"
echo "   - 可以通过 /api/mqtt/debug/status 查看详细状态"
echo ""
