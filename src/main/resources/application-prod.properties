spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration

logging.level.com.deepoove.poi=error
westcatr.boot.security.encryption=true
management.health.mail.enabled=false

streaming.zlmediakit-host=************
streaming.zlmediakit-http-port=8082
streaming.secret=eW81fN9Mj0PL7ltg2Ahvk1V6q0DSnPwb

westcatr.boot.security.no-check-url=/test/v3/api-docs/apis,/test/v3/api-docs/swagger-config,/test/doc.html,/test/swagger-ui.html,/test/csrf,/test/error,/test/login,/test/captcha/get,/test/captcha/check,/test/oa/getToken,/test/login/publicKey,/test/systemConfig/front,/test/login/self,/test/office/saveBack,/test/zlmediakit/callback

server.port=7325
# 日志文件存放的路径 (使用了应用名称变量)
logging.file.path=/home/<USER>/apps/logs/${spring.application.name}/
# Westcatr 文件配置：文件上传后保存的文件夹路径 (使用了应用名称变量)
westcatr.boot.file.upload-folder=/home/<USER>/apps/upload/${spring.application.name}/
# Spring 数据源配置：JDBC 驱动程序类名 (MySQL 8+)
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# Spring 数据源配置：数据库连接 JDBC URL (包含允许多语句执行的参数)
spring.datasource.url=*******************************************************************
# Spring 数据源配置：数据库连接用户名
spring.datasource.username=root
# Spring 数据源配置：数据库连接密码
spring.datasource.password=Jz@gjdw321.

# Spring Redis 配置：使用的 Redis 数据库索引 (默认为0)
spring.redis.database=1
# Spring Redis 配置：Redis 服务器主机地址
spring.redis.host=************
# Spring Redis 配置：Redis 服务器连接密码
spring.redis.password=Jz@gjdw321.
# Spring Redis 配置：Redis 服务器端口号
spring.redis.port=6379
# Spring Redis 配置：连接超时时间 (毫秒)
spring.redis.timeout=5000
# (已注释) MyBatis-Plus 配置：将 SQL 日志输出到标准输出
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Flyway配置：指定Flyway管理的数据库Schema名称
spring.flyway.schemas=jz_supplie_db

# tesseract安装路径
# Tesseract OCR配置：Tesseract安装路径 (Linux路径)
tesseract.install.readdes=/usr/local/lib

# tesseract语言包路径
# Tesseract OCR配置：Tesseract语言数据包(tessdata)路径 (Linux路径)
tesseract.language.path=/usr/local/share/tessdata

# SMB文件配置 
smb.computers=[{"address":"*************/test_jz/","username":"testuser","password":"123456"}]

# SMB文件保存目录
smb.file.save.dir=/home/<USER>/apps/upload/${spring.application.name}/smbfile

# MQTT配置（生产环境）
mqtt.client-id-prefix=jz_westcatr_prod
mqtt.enabled=true