CREATE TABLE `standard_basic_project_param` (
  `id` BIGINT(20) NOT NULL COMMENT '主键ID',
  `param_name` VARCHAR(255) NOT NULL COMMENT '参数名称',
  `param_type` VARCHAR(64) NOT NULL COMMENT '参数类别',
  `standard_basic_project_id` BIGINT(20) NOT NULL COMMENT '实验项目ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实验项目参数表';