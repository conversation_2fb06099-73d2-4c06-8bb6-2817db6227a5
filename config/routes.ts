import { pathToRegexp } from 'path-to-regexp';

/** ============ 路由配置 ============ */
const routes: RouteData[] = [
  {
    path: '/user',
    component: '@/pages/user/layout/index',
    layout: false,
    routes: [
      { name: '登录', path: '/user/login', component: '@/pages/user/login' },
      { name: '找回密码', path: '/user/forget', component: '@/pages/user/forget' },
    ],
  },
  {
    path: '/mobile',
    component: '@/pages/mobile/index',
    layout: false,
  },
  {
    name: '工作台',
    path: '/home',
    icon: 'home',
    component: '@/pages/home',
    apiList: ['post:/test/environmentInfo/getWeather'],
  },
  {
    // 大屏项目
    path: '/bigScreen',
    component: '@/pages/bigScreen/index',
    actionList: [
      {
        name: '大屏',
        auth: 'bigScreen',
        apiList: ['post:/test/bigScreen/voPage', 'post:/test/screen/report'],
      },
    ],
    layout: false,
  },
  {
    // 大屏项目
    name: '监控中心',
    path: '/bigScreenTwo',
    component: '@/pages/bigScreenTwo/index',
    actionList: [
      {
        name: '监控中心大屏',
        auth: 'bigScreenTwo',
        apiList: ['post:/test/screen/newScreenDataCount'],
      },
    ],
    layout: false,
  },
  {
    name: '样品管理',
    path: '/taskManage/sample',
    component: '@/pages/task/sample',
    crud: true,
    crudKey: 'sampleInfo',
    actionList: [
      {
        name: '返样',
        auth: 'backSample',
        apiList: ['admin:/taskManage/sample@backSample'],
      },
    ],
  },
  {
    name: '任务管理',
    path: '/task',
    icon: 'profile',
    routes: [
      {
        name: '任务列表',
        path: '/task/listData',
        component: '@/pages/task/listData',
        crud: true,
        crudKey: 'jzTaskInfo',
        apiList: ['get:/test/jzTaskInfo/getHomepage'],
        actionList: [
          {
            name: '开始任务',
            auth: 'startTask',
            apiList: ['admin:/task/listData@startTask'],
          },
          {
            name: '任务检毕',
            auth: 'endTask',
            apiList: ['admin:/task/listData@endTask'],
          },
        ],
      },
      {
        name: '工单列表',
        path: '/task/order',
        component: '@/pages/task/order',
        crud: true,
        crudKey: 'jzTaskWorkOrderInfo',
        actionList: [
          {
            name: '开始',
            auth: 'start',
            apiList: ['admin:/task/order@start'],
          },
          {
            name: '工单检毕',
            auth: 'end',
            apiList: ['admin:/task/order@end'],
          },
        ],
      },
      {
        name: '检项列表',
        path: '/task/checkItem',
        component: '@/pages/task/checkItem',
        crud: true,
        crudKey: 'jzTaskInspectionItemInfo',
      },
    ],
  },
  {
    name: '报告管理',
    path: '/report',
    icon: 'fileSearch',
    component: '@/pages/reportNew',
    crud: true,
    crudKey: 'jzReportInfo',
  },
  {
    name: '标准管理',
    path: '/standardManage',
    routes: [
      {
        name: '实验项目管理',
        path: '/standardManage/project',
        component: '@/pages/config/project',
        crud: true,
        crudKey: 'standardProject',
      },
      {
        name: '实验标准管理',
        path: '/standardManage/standard',
        component: '@/pages/config/checkstandard',
        crud: true,
        crudKey: 'standardBasicInstrumentInfo',
      },
    ],
  },
  {
    name: '设备管理',
    path: '/device',
    icon: 'control',
    routes: [
      {
        name: '检测设备管理',
        path: '/device/newInstrument',
        component: '@/pages/config/newInstrument',
        crud: true,
        crudKey: 'instrumentNewInfo',
        actionList: [
          {
            name: '编辑可检参数',
            auth: 'editCheckItem',
            apiList: ['admin:/device/newInstrument@editCheckItem'],
          },
        ],
      },
      // ⼯位管理
      {
        name: '⼯位管理',
        path: '/device/workstation',
        component: '@/pages/config/workstation',
        crud: true, 
        crudKey: 'buInstrumentWorkstationInfo',
        actionList: [
          {
            name: '关联设备',
            auth: 'linkDevice',
            apiList: ['admin:/device/workstation@linkDevice'],
          },
          {
            name: '关联监控设备',
            auth: 'monitorDevice',
            apiList: ['admin:/device/workstation@monitorDevice'],
          },
        ],
      },
      // 监控设备管理
      { 
        name: '监控设备管理',
        path: '/device/monitor',
        component: '@/pages/config/monitor',
        crud: true,
        crudKey: 'buInstrumentCameraInfo',
        actionList: [
          {
            name: '播放',
            auth: 'play',
            apiList: ['admin:/device/monitor@play'],
          },
        ],
      },
    ],
  },
  {
    name: '人员管理',
    path: '/userManage/user',
    component: '@/pages/system/user',
  },
  {
    name: '系统管理',
    path: '/system',
    icon: 'setting',
    access: 'routeAccess',
    routes: [
      {
        name: '物资类别管理',
        path: '/system/sampleType',
        component: '@/pages/system/sampleType',
        crud: true,
        crudKey: 'buSampleBaseInfo',
      },
      {
        name: '角色管理',
        path: '/system/role',
        component: '@/pages/system/role',
      },
      {
        name: '部门管理',
        path: '/system/dept',
        component: '@/pages/system/dept',
      },
      {
        name: '权限管理',
        path: '/system/auth',
        routes: [
          {
            name: '菜单管理',
            path: '/system/auth/menu',
            component: '@/pages/system/auth/menu',
          },
          {
            name: '接口权限管理',
            path: '/system/auth/api',
            component: '@/pages/system/auth/api',
          },
          {
            name: '数据权限模板管理',
            path: '/system/auth/data-template',
            component: '@/pages/system/auth/dataTemplate',
          },
        ],
      },
      {
        name: '字典管理',
        path: '/system/dic',
        component: '@/pages/system/dic',
      },
      {
        name: '系统配置',
        path: '/system/cfg',
        component: '@/pages/system/cfg',
      },
      {
        name: '文件管理',
        path: '/system/file',
        component: '@/pages/system/file',
      },
      {
        name: '系统日志',
        path: '/system/log',
        routes: [
          {
            name: '接口日志',
            path: '/system/log/api',
            component: '@/pages/system/log/api',
          },
          {
            name: '登录日志',
            path: '/system/log/login',
            component: '@/pages/system/log/login',
          },
        ],
      },
    ],
  },

  {
    path: '/i',
    routes: [
      { name: '个人中心', path: '/i/center', component: '@/pages/i/center' },
      { name: '修改密码', path: '/i/password', component: '@/pages/i/password' },
    ],
  },
  { path: '*', layout: false, component: '@/pages/404' },
];
/** ============ end 路由配置 ============ */

/** 后台权限标识 */
export const adminPrefix: string = 'admin';

/** 免登陆页面 */
export const whitePath: RegExp[] = [/^\*$/].concat(
  [
    '/umi/plugin/openapi', // 接口文档地址
    '/~docs', // 项目文档地址
    '/user',
    '/mobile',
  ].map((path) => pathToRegexp(path, [], { end: false })),
);

/**
 * 生成access权限标识
 * 1. 手动设置: 则取设置的值
 * 2. 未设置: admin:页面路径
 */
function genAuthKey(route: RouteData): RouteData['auth'] {
  if ('auth' in route) {
    return route.auth;
  }

  if (!route.path || whitePath.some((v) => v.test(route.path!))) {
    return route.auth;
  }

  return `${adminPrefix}:${route.path}`;
}

/**
 * 格式化权限
 * 1. 自动生成权限
 */
function formatter(routes: RouteData[]): RouteData[] {
  if (!routes) {
    return routes;
  }

  return routes.map((route) => {
    const auth = genAuthKey(route);

    const item = {
      ...route,
      ...(auth && {
        auth,
        access: 'routeAccess',
      }),
      routes: route.routes ? formatter(route.routes) : undefined,
    };

    return item;
  });
}

/** 把路由树变成一维数组 */
function getFlatRoutes(routes: RouteData[]): RouteData[] {
  let flatRoutes: RouteData[] = [];

  routes.forEach((route) => {
    flatRoutes.push({
      ...route,
    });

    if (route.routes) {
      flatRoutes = flatRoutes.concat(getFlatRoutes(route.routes));
    }
  });

  return flatRoutes;
}

const formatRoutes = formatter(routes);
export const flatRoutes = getFlatRoutes(formatRoutes);

export default formatRoutes;
