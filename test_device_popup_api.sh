#!/bin/bash

# 测试设备数据弹窗API功能
# 验证修改后的功能是否正常工作

echo "🚀 开始测试设备数据弹窗API功能"
echo "=================================="

# 设置API基础URL
BASE_URL="http://localhost:7321/test"

# 测试用例1: 测试变压器设备 (ID: 1003)
echo ""
echo "📋 测试用例1: 变压器设备 (ID: 1003)"
echo "-----------------------------------"

curl -X POST "${BASE_URL}/deviceIntegration/getExperimentData" \
  -H "Content-Type: application/json" \
  -d '{
    "equipmentId": 1003,
    "parameterIds": [1, 2, 3]
  }' | jq '.'

echo ""
echo "✅ 变压器设备测试完成"

# 测试用例2: 测试电子万能试验机 (ID: 1001)
echo ""
echo "📋 测试用例2: 电子万能试验机 (ID: 1001)"
echo "-----------------------------------"

curl -X POST "${BASE_URL}/deviceIntegration/getExperimentData" \
  -H "Content-Type: application/json" \
  -d '{
    "equipmentId": 1001,
    "parameterIds": [1, 2, 3]
  }' | jq '.'

echo ""
echo "✅ 电子万能试验机测试完成"

# 测试用例3: 测试电机设备 (ID: 1002)
echo ""
echo "📋 测试用例3: 电机设备 (ID: 1002)"
echo "-----------------------------------"

curl -X POST "${BASE_URL}/deviceIntegration/getExperimentData" \
  -H "Content-Type: application/json" \
  -d '{
    "equipmentId": 1002,
    "parameterIds": [1, 2, 3]
  }' | jq '.'

echo ""
echo "✅ 电机设备测试完成"

# 测试用例4: 测试思创设备 (ID: 1004)
echo ""
echo "📋 测试用例4: 思创设备 (ID: 1004)"
echo "-----------------------------------"

curl -X POST "${BASE_URL}/deviceIntegration/getExperimentData" \
  -H "Content-Type: application/json" \
  -d '{
    "equipmentId": 1004,
    "parameterIds": [1, 2, 3]
  }' | jq '.'

echo ""
echo "✅ 思创设备测试完成"

# 测试用例5: 测试不存在的设备 (ID: 9999)
echo ""
echo "📋 测试用例5: 不存在的设备 (ID: 9999)"
echo "-----------------------------------"

curl -X POST "${BASE_URL}/deviceIntegration/getExperimentData" \
  -H "Content-Type: application/json" \
  -d '{
    "equipmentId": 9999,
    "parameterIds": [1, 2, 3]
  }' | jq '.'

echo ""
echo "✅ 不存在设备测试完成"

echo ""
echo "🎉 所有测试用例执行完成！"
echo "=================================="
echo ""
echo "📊 测试结果说明："
echo "- success: true 表示接口调用成功"
echo "- data.experiments 包含实验配置数组"
echo "- 每个实验包含 name, code, tableName, mapping 字段"
echo "- mapping 字段包含参数名称到数据库字段的映射关系"
echo ""
echo "🔍 如果测试失败，请检查："
echo "1. 应用程序是否正常启动 (端口7321)"
echo "2. 数据库连接是否正常"
echo "3. 数据库迁移是否执行成功"
echo "4. 设备数据配置是否正确插入"
